package io.agora.rtc.ss.permissions;

import static android.app.Activity.RESULT_OK;

import android.annotation.TargetApi;
import android.content.Intent;
import android.media.projection.MediaProjectionManager;
import android.os.Build;
import android.util.Log;

import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;

/**
 * Created by 陈丰尧 on 2021/5/25
 * 不可见的Fragment, 用来申请权限使用的
 * 只能在5.0以上的手机使用
 */
@TargetApi(Build.VERSION_CODES.LOLLIPOP)
public class InvisibleFragment extends Fragment {
    private static final String TAG = "InvisibleFragment";
    private static final int REQ_CODE = 1000;
    private MediaProjectionManager manager;
    private RequestResult onRequestResult;

    public void requestNow(RequestResult onRequestResult) {
        this.onRequestResult = onRequestResult;
        if (manager == null) {
            manager = ContextCompat.getSystemService(requireContext(), MediaProjectionManager.class);
        }
        if (manager != null) {
            Intent intent = manager.createScreenCaptureIntent();
            startActivityForResult(intent, REQ_CODE);
        } else {
            Log.w(TAG, "不支持屏幕录制");
            onRequestResult.onReject();
        }

    }


    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK && requestCode == REQ_CODE) {
            // 权限允许
            Log.d(TAG,"用户同意");
            if (onRequestResult != null) {
                onRequestResult.onAllow(data);
            }
        } else {
            Log.d(TAG,"用户拒绝");
            if (onRequestResult != null) {
                onRequestResult.onReject();
            }
        }
    }

    public interface RequestResult {
        /**
         * 用户同意录制屏幕
         * @param data 系统回调的录制屏幕的intent
         */
        void onAllow(Intent data);

        /**
         * 用户拒绝录制屏幕
         */
        void onReject();
    }
}
