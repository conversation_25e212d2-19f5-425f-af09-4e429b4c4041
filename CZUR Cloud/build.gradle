// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext.kotlin_version = '1.8.0'

    repositories {
        maven { url 'https://jitpack.io' }
        mavenCentral()
        google()
        maven { url 'https://mvn.mob.com/android' }
        maven { url 'https://repo1.maven.org/maven2/' }
        maven { url 'https://developer.huawei.com/repo/' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public' }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.5.0'
        classpath 'io.realm:realm-gradle-plugin:10.18.0'
        classpath 'com.mob.sdk:MobSDK:2018.0319.1724'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.github.dcendents:android-maven-gradle-plugin:2.1'
        classpath 'androidx.navigation:navigation-safe-args-gradle-plugin:2.5.3'
        classpath "com.kanyun.kace:kace-gradle-plugin:1.8.0-1.0.4"

        //TODO OVERSEAS 注意，海外版需要注释掉华为推送包
        classpath 'com.huawei.agconnect:agcp:1.9.1.301'

        //TODO OVERSEAS 国内版需要注释掉goole服务包
//        classpath 'com.google.gms:google-services:4.3.10'

    }
}

allprojects {
    repositories {
        mavenCentral()
        maven { url 'https://jitpack.io' }
        google()
        maven { url 'https://developer.huawei.com/repo/' }
        maven { url "https://s01.oss.sonatype.org/content/repositories/snapshots" }
        maven { url 'https://repo1.maven.org/maven2/' }

        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public' }

        maven {
            url 'https://nexus.czur.com/repository/czur_android_host'
            credentials {
                username = 'appgroup'
                password = 'yswkNP4M43YqeUcc'
            }
        }

    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

project.ext {

    android = [
            compileSdkVersionExt       : 36,
            buildToolsVersionExt       : '36.0.0',
            minSdkVersionExt           : 26,
            targetSdkVersionExt        : 36,

            versionName             : '3.4.0',
            versionCode             : 418,

            versionNameOverSea      : '3.3.0',
            versionCodeOverSea      : 417,

            flavorDimensions        : "1",

            //TODO OVERSEAS 需要注意，每次固件、软件一起升级时，需要修改该值
            //TODO 国内版
            versionMessageApi       : 4,
            //TODO 海外版
            versionMessageApiOversea: 2,
    ]

    deps = [
            // ------------- Android -------------

            // jpush
            jpush           : 'cn.jiguang.sdk:jpush:3.6.0',    // 3.6.4
            jcore           : 'cn.jiguang.sdk:jcore:2.3.4',    // 2.3.6
            // vivoPush
            vivoPush        : 'src/domestic/libs/vpush_clientSDK_v4.0.6.0_506.aar',
            // huaweiPush
            huaweiPush      : 'com.huawei.hms:push:6.11.0.300',
            // oppoPush
            oppoPush        : 'src/domestic/libs/oppo_pushSDK_V3.5.3.aar',
            // xiaomiPush
            xiaomiPush      : 'src/domestic/libs/MiPush_SDK_Client_6_0_1-C_3rd.aar',
            // meizuPush
            meizuPushVersion: '3.8.6'
    ]

    config = [
            domestic: [
                    appName    : '成者CZUR',
                    release    : [
                            // region release
                            checkForceUpdate      : '"https://internal.czur.cc/v1/app/version.do"',
                            checkOCRUrl           : '"https://changer-resource.oss-cn-beijing.aliyuncs.com/static/CZUR-App/HandwritingOCRServer.json"',
                            checkAuraMateUpdateUrl: '"https://changer-resource.oss-cn-beijing.aliyuncs.com/static/AuraMate/AuraMateFWUpdate.json"',
                            checkMirrorUpdateUrl  : '"https://internal.czur.cc/v1/mirror/half/version.do"',
                            versionUpdateUrl      : '"http://osscdn.czur.com/resource/software/android/czur/czur.version.json"',
                            baseUrl               : '"https://cn.czur.cc/api/"',
                            baseStarryUrl         : '"https://starry-api2.czur.cc/api/"',
                            shareStarryUrl        : '"https://meeting.czur.cc/m/"',
                            passportUrl           : '"https://api-passport.czur.cc/api/"',
                            feedbackUrl           : '"https://internal.czur.cc/"',
                            ossEndpoint           : '"oss-cn-beijing.aliyuncs.com"',
                            ossBucketMirror       : '"czur-mirror"',
                            agoraStarryAppId      : '"********************************"',
                            agoraStarryAppCert    : '"********************************"',
                            privacyUrl            : '"https://privacy.czur.cc/privacy"',
                            termsUrl              : '"https://privacy.czur.cc/terms"',
                            // endregion
                    ],
                    debug      : [
                            // region debug
                            checkForceUpdate      : '"https://inner-dubbo.czur.cc/v1/app/version.do"',
                            checkOCRUrl           : '"https://changer-resource.oss-cn-beijing.aliyuncs.com/static/CZUR-App/HandwritingOCRServerTest.json"',
                            checkAuraMateUpdateUrl: '"https://changer-resource.oss-cn-beijing.aliyuncs.com/static/AuraMate/AuraMateFWUpdateTest.json"',
                            checkMirrorUpdateUrl  : '"https://inner-dubbo.czur.cc/v1/mirror/half/version.do"',
                            versionUpdateUrl      : '"http://osscdn.czur.com/resource/software/android/czur/czur_debug.version.json"',
                            baseUrl               : '"https://test.czur.cc/api/"',
                            baseStarryUrl         : '"https://test0927-starry.czur.cc/api/"',
                            shareStarryUrl        : '"https://meeting-test.czur.cc/m/"',
                            passportUrl           : '"https://test-passport.czur.cc/api/"',
                            feedbackUrl           : '"https://inner-dubbo.czur.cc/"',
                            ossEndpoint           : '"oss-cn-beijing.aliyuncs.com"',
                            ossBucketMirror       : '"czur-mirror"',
                            agoraStarryAppId      : '"********************************"',
                            agoraStarryAppCert    : '"********************************"',
                            privacyUrl            : '"https://privacy.czur.cc/privacy"',
                            termsUrl              : '"https://privacy.czur.cc/terms"',
                            // endregion
                    ],
                    umengAppKey: '"5b46cdbf8f4a9d754400010a"',
                    ossBucket  : '"czur-aura"',
                    agoraAppId : '"********************************"',
            ],
            overseas: [
                    appName    : 'CZUR',
                    release    : [
                            // region release
                            checkForceUpdate      : '"https://internal.czur.cc/v1/app/version.do"',
                            checkOCRUrl           : '"https://changer-static.oss-us-west-1.aliyuncs.com/static/CZUR-App/HandwritingOCRServer.json"',
                            checkAuraMateUpdateUrl: '"https://changer-static.oss-us-west-1.aliyuncs.com/static/AuraMate/AuraMateFWUpdate.json"',
                            checkMirrorUpdateUrl  : '"https://internal.czur.cc/v1/mirror/half/version.do"',
                            versionUpdateUrl      : '"https://static.czur.cc/resource/software/android/czur/czur.version.json"',
                            baseUrl               : '"https://global.czur.cc/api/"',
                            baseStarryUrl         : '"https://starry-global.czur.cc/api/"',
                            shareStarryUrl        : '"https://meeting.czur.cc/m/"',
                            passportUrl           : '"https://api-passport.czur.cc/api/"',
                            feedbackUrl           : '"https://internal.czur.cc/"',
                            ossEndpoint           : '"oss-us-west-1.aliyuncs.com"',
                            ossBucketMirror       : '"czur-mirror-f"',
                            agoraStarryAppId      : '"********************************"',
                            agoraStarryAppCert    : '"********************************"',
                            privacyUrl            : '"https://privacy.czur.cc/privacy_en"',
                            termsUrl              : '"https://privacy.czur.cc/terms_en"',
                            // endregion
                    ],
                    debug      : [
                            // region debug
                            checkForceUpdate      : '"https://inner-dubbo.czur.cc/v1/app/version.do"',
                            checkOCRUrl           : '"https://changer-resource.oss-cn-beijing.aliyuncs.com/static/CZUR-App/HandwritingOCRServerTest.json"',
                            checkAuraMateUpdateUrl: '"https://changer-static.oss-us-west-1.aliyuncs.com/static/AuraMate/AuraMateFWUpdateTest.json"',
                            checkMirrorUpdateUrl  : '"https://inner-dubbo.czur.cc/v1/mirror/half/version.do"',
                            versionUpdateUrl      : '"https://static.czur.cc/resource/software/android/czur/czur_debug.version.json"',
                            baseUrl               : '"https://test.czur.cc/api/"',
                            baseStarryUrl         : '"https://testna-starry.czur.cc/api/"',
                            shareStarryUrl        : '"https://meeting-test.czur.cc/m/"',
                            passportUrl           : '"https://test-passport.czur.cc/api/"',
                            feedbackUrl           : '"https://inner-dubbo.czur.cc/"',
//                            ossEndpoint: '"oss-cn-beijing.aliyuncs.com"',
                            ossEndpoint           : '"oss-us-west-1.aliyuncs.com"',
                            ossBucketMirror       : '"czur-mirror"',
                            agoraStarryAppId      : '"********************************"',
                            agoraStarryAppCert    : '"********************************"',
                            privacyUrl            : '"https://privacy.czur.cc/privacy_en"',
                            termsUrl              : '"https://privacy.czur.cc/terms_en"',
                            // endregion
                    ],
                    umengAppKey: '"5b76324fb27b0a7b34000039"',
                    ossBucket  : '"czur-aura-f"',
                    agoraAppId : '"********************************"'
            ]
    ]
}