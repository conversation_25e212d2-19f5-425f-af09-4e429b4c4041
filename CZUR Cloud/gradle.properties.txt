## For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
#
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx1024m -XX:MaxPermSize=256m
# org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
#
# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
#Wed Apr 08 13:51:31 CST 2020
#android.enableR8=false

# Windows
#org.gradle.java.home=F\:/android studio/jre
org.gradle.java.home=E\:/AndroidStudio/jre
# Mac
#org.gradle.java.home=/Applications/Android Studio 4.2 Preview.app/Contents/jre/Contents/Home

# 调整jvm内存大小
org.gradle.jvmargs=-Xmx8192M -XX:MaxPermSize=8192M -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
#org.gradle.jvmargs=-Xmx4096M -XX:MaxPermSize=1024M -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
#org.gradle.jvmargs=-Xmx3096M -Dkotlin.daemon.jvm.options\="-Xmx2048M"
#开启gradle缓存
org.gradle.caching=true
#开启并行编译线程数
org.gradle.parallel.threads=15
#设置gradle编译线程为10个
org.gradle.workers.max=15
#开启守护进程
org.gradle.daemon=true
org.gradle.configureondemand=true
#开启gradle并行编译，
org.gradle.parallel=true
#开启kotlin增量编译
kotlin.increlental=true
#开启kotlin缓存
kotlin.caching.enable=true
#开启kotlin的增量和并行编译
kotlin.incremental=true
kotlin.incremental.java=true
kotlin.incremental.js=true
kotlin.caching.enabled=true
#开启kotlin并行编译
kotlin.parallel.tasks.in.project=true
#启用新一代aapt编译
android.enableAapt2=true
kapt.use.worker.api=true
#允许D8编译
android.enableD8=true
android.enableR8=true
android.injected.testOnly=false
#是否使用AndroidX库
android.useAndroidX=true
#是否迁移第三方库
android.enableJetifier=true
# 配置MobSDK
MobSDK.spEdition=FP

#优化kapt//增量编译 kapt1.3.30版本以上支持
kapt.incremental.apt=true
#//kapt avoiding 如果用kapt依赖的内容没有变化，会完全重用编译内容
kapt.include.compile.classpath=false

# TODO 国内海外区分
isOverSea=false
