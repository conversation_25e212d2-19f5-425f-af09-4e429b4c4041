# For more information about using CMake with Android Studio, read the
# documentation: https://d.android.com/studio/projects/add-native-code.html

# Sets the minimum version of CMake required to build the native library.

cmake_minimum_required(VERSION 3.4.1)

set(CMAKE_VERBOSE_MAKEFILE on)
set(OPENCV_LIBS D:/OpenCV-android-sdk/sdk/native/libs)
#set(OPENCV_LIBS /Users/<USER>/develop/OpenCV-android-sdk/sdk/native/libs)

#配置加载native依赖
include_directories(D:/OpenCV-android-sdk/sdk/native/jni/include)
#include_directories(/Users/<USER>/develop/OpenCV-android-sdk/sdk/native/jni/include)
#动态方式加载
add_library(libopencv_android SHARED IMPORTED )
#引入libopencv_java3.so文件
set_target_properties(libopencv_android PROPERTIES
                      IMPORTED_LOCATION "${OPENCV_LIBS}/${ANDROID_ABI}/libopencv_java3.so")

# Creates and names a library, sets it as either STATIC
# or SHARED, and provides the relative paths to its source code.
# You can define multiple libraries, and CMake builds them for you.
# Gradle automatically packages shared libraries with your APK.

add_library( # Sets the name of the library.
             czur_cloud_native

             # Sets the library as a shared library.
             SHARED

             # Provides a relative path to your source file(s).
             src/main/cpp/czur_cloud_native.cpp
             src/main/cpp/alg_kernel.cpp
             src/main/cpp/alg_kernel.hpp
             src/main/cpp/andriod_ios_alg_api.cpp
             src/main/cpp/andriod_ios_alg_api.hpp
             src/main/cpp/point_line_utils.cpp
             src/main/cpp/point_line_utils.hpp)



# Searches for a specified prebuilt library and stores the path as a
# variable. Because CMake includes system libraries in the search path by
# default, you only need to specify the name of the public NDK library
# you want to add. CMake verifies that the library exists before
# completing its build.

find_library( # Sets the name of the path variable.
              log-lib
              android
              # Specifies the name of the NDK library that
              # you want CMake to locate.
              log )

FIND_PACKAGE( OpenMP REQUIRED)
if(OPENMP_FOUND)
message("OPENMP FOUND")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} ${OpenMP_C_FLAGS}")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${OpenMP_CXX_FLAGS}")
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} ${OpenMP_EXE_LINKER_FLAGS}")
endif()

# Specifies libraries CMake should link to your target library. You
# can link multiple libraries, such as libraries you define in this
# build script, prebuilt third-party libraries, or system libraries.

target_link_libraries( # Specifies the target library.
                       czur_cloud_native
                       android
                       log
                       # Links the target library to the log library
                       # included in the NDK.
                       ${log-lib}

                       #Bitmap
                       jnigraphics

                       libopencv_android)