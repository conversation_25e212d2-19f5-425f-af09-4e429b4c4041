# Android 16 API迁移指南

## 概述
本文档说明了当Android 16正式发布后，如何将当前的降级实现迁移到正式的Android 16 API。

## 当前实现状态
目前所有Android 16新特性都使用降级实现，通过`Android16ApiCompat.kt`统一管理。这确保了：
1. 代码不会因为反射限制而崩溃
2. 功能在当前环境下正常工作
3. 便于后续迁移到正式API

## 迁移步骤

### 1. 版本相关API

#### 当前实现（降级）
```kotlin
// Android16ApiCompat.kt
fun getSdkIntFull(): Int {
    return Build.VERSION.SDK_INT
}

fun getMinorSdkVersion(majorVersion: Int): Int {
    return 0
}
```

#### 迁移到正式API
```kotlin
// 当Android 16正式发布时，替换为：
fun getSdkIntFull(): Int {
    return Build.VERSION.SDK_INT_FULL
}

fun getMinorSdkVersion(majorVersion: Int): Int {
    return Build.getMinorSdkVersion(majorVersion)
}
```

### 2. 高级保护模式API

#### 当前实现（降级）
```kotlin
// Android16ApiCompat.kt
fun isAdvancedProtectionEnabled(context: Context): Boolean {
    return false
}
```

#### 迁移到正式API
```kotlin
// 当Android 16正式发布时，替换为：
fun isAdvancedProtectionEnabled(context: Context): Boolean {
    val manager = context.getSystemService(Context.ADVANCED_PROTECTION_SERVICE) as? AdvancedProtectionManager
    return manager?.isAdvancedProtectionEnabled() ?: false
}
```

### 3. 动态刷新率API

#### 当前实现（降级）
```kotlin
// Android16ApiCompat.kt
fun hasAdaptiveRefreshRateSupport(context: Context): Boolean {
    // 检查是否支持多种刷新率作为降级方案
    val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as? WindowManager
    val display = windowManager?.defaultDisplay
    return display?.supportedRefreshRates?.size ?: 0 > 1
}

fun getSuggestedFrameRate(context: Context, category: Int): Float {
    // 返回最高支持的刷新率作为降级方案
    val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as? WindowManager
    val display = windowManager?.defaultDisplay
    return display?.supportedRefreshRates?.maxOrNull() ?: 60.0f
}
```

#### 迁移到正式API
```kotlin
// 当Android 16正式发布时，替换为：
fun hasAdaptiveRefreshRateSupport(context: Context): Boolean {
    val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    val display = windowManager.defaultDisplay
    return display.hasArrSupport()
}

fun getSuggestedFrameRate(context: Context, category: Int): Float {
    val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    val display = windowManager.defaultDisplay
    return display.getSuggestedFrameRate(category)
}
```

### 4. JobScheduler增强API

#### 当前实现（降级）
```kotlin
// Android16ApiCompat.kt
fun getPendingJobReasons(context: Context, jobId: Int): IntArray {
    val jobScheduler = context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as? JobScheduler
    if (jobScheduler != null) {
        val pendingJob = jobScheduler.getPendingJob(jobId)
        return if (pendingJob != null) intArrayOf() else intArrayOf(14)
    }
    return intArrayOf()
}
```

#### 迁移到正式API
```kotlin
// 当Android 16正式发布时，替换为：
fun getPendingJobReasons(context: Context, jobId: Int): IntArray {
    val jobScheduler = context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler
    return jobScheduler.getPendingJobReasons(jobId)
}
```

### 5. 进度通知API

#### 当前实现（降级）
```kotlin
// Android16ApiCompat.kt
fun createProgressStyleNotification(...): Notification? {
    // 创建标准进度通知作为降级方案
    val builder = Notification.Builder(context, channelId)
        .setContentTitle(title)
        .setSmallIcon(trackerIconRes ?: android.R.drawable.ic_dialog_info)
        .setProgress(maxProgress, progress, false)
        .setOngoing(true)
    
    val progressText = if (segments != null || points != null) {
        "进度: $progress/$maxProgress (增强显示)"
    } else {
        "进度: $progress/$maxProgress"
    }
    builder.setContentText(progressText)
    
    return builder.build()
}
```

#### 迁移到正式API
```kotlin
// 当Android 16正式发布时，替换为：
fun createProgressStyleNotification(...): Notification? {
    val progressStyle = Notification.ProgressStyle()
        .setProgress(progress)
        .setStyledByProgress(false)
    
    trackerIconRes?.let { iconRes ->
        val icon = Icon.createWithResource(context, iconRes)
        progressStyle.setProgressTrackerIcon(icon)
    }
    
    segments?.let { segmentList ->
        val segmentObjects = segmentList.map { (length, color) ->
            Notification.ProgressStyle.Segment(length).setColor(color)
        }
        progressStyle.setProgressSegments(segmentObjects)
    }
    
    points?.let { pointList ->
        val pointObjects = pointList.map { (position, color) ->
            Notification.ProgressStyle.Point(position).setColor(color)
        }
        progressStyle.setProgressPoints(pointObjects)
    }
    
    return Notification.Builder(context, channelId)
        .setContentTitle(title)
        .setSmallIcon(trackerIconRes ?: android.R.drawable.ic_dialog_info)
        .setStyle(progressStyle)
        .build()
}
```

## 迁移检查清单

### 准备工作
- [ ] 确认Android 16正式版已发布
- [ ] 更新Android SDK到最新版本
- [ ] 确认所有新API已在官方文档中公开

### 代码迁移
- [ ] 更新`Android16ApiCompat.kt`中的所有方法实现
- [ ] 移除所有TODO注释
- [ ] 更新相关的导入语句
- [ ] 添加必要的`@RequiresApi(36)`注解

### 测试验证
- [ ] 在Android 16设备上测试所有新特性
- [ ] 验证降级方案在低版本设备上仍然工作
- [ ] 运行`Android16AdaptationTest`确保所有功能正常
- [ ] 进行完整的回归测试

### 文档更新
- [ ] 更新`Android16_Adaptation_Summary.md`
- [ ] 更新代码注释
- [ ] 记录迁移过程中遇到的问题

## 注意事项

1. **渐进式迁移**: 建议逐个API进行迁移，而不是一次性全部更新
2. **向后兼容**: 确保迁移后的代码在低版本Android上仍然正常工作
3. **错误处理**: 保持现有的try-catch结构，以防新API有意外行为
4. **测试覆盖**: 在多个Android版本上进行充分测试
5. **性能监控**: 迁移后监控应用性能，确保新API没有性能问题

## 联系信息
如果在迁移过程中遇到问题，请参考：
- Android官方文档
- Android 16迁移指南
- 项目内部技术文档

---
*本文档将在Android 16正式发布后进行更新*
