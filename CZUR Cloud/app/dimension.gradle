apply plugin: 'com.mob.sdk'
apply plugin: 'com.huawei.agconnect' // TODO OVERSEAS 海外需要注掉

// TODO 国内渠道版本
android {
    buildTypes {
        release {
//            ndk { abiFilters 'arm64-v8a'}//TODO OVERSEAS 注意，海外版 发版 打开
            ndk { abiFilters 'arm64-v8a' , 'armeabi-v7a' } //TODO OVERSEAS 注意，海外版 注掉
        }
        debug {
            //设置debug版本包含x86的so文件
            ndk { abiFilters 'arm64-v8a' , 'armeabi-v7a' }
        }
    }
}
dependencies {
    //友盟统计
    implementation  'com.umeng.umsdk:common:9.7.9'// 必选
    implementation  'com.umeng.umsdk:asms:1.8.5'// 必选
}

// region MobSDK
MobSDK {
    appKey "24bb157418b10"
    appSecret "4903815e6c56d6c6433a974923eab671"
    ShareSDK {
        loopShare true
        //平台配置信息
        devInfo {
            Wechat {
                id "1"
                sortId "1"
                appId "wxfdaf39a42bd1152c"
                appSecret "b253a4ec06db0156e6635f6e1bfabb20"
                shareByAppClient true
                enable true
            }
            WechatMoments {
                id "2"
                sortId "2"
                appId "wxfdaf39a42bd1152c"
                appSecret "b253a4ec06db0156e6635f6e1bfabb20"
                enable true
            }
            QQ {
                id "3"
                sortId "3"
                appId "1105466927"
                appKey "bUttf3gjJXP9q3lz"
                shareByAppClient true
                enable true
            }
            QZone {
                id "4"
                sortId "4"
                appId "1105466927"
                appKey "bUttf3gjJXP9q3lz"
                shareByAppClient true
                enable true
            }
            SinaWeibo {
                id "5"
                sortId "5"
                appKey "3027241250"
                appSecret "2d19a8e82a3bb17ac8e5985ba4373d4f"
                callbackUri "https://www.sina.com"
                shareByAppClient true
                enable true
            }
        }
    }
}
// endregion
