package com.czur.cloud.common;

import android.content.Context;
import android.graphics.Bitmap;
import android.net.Uri;
import android.provider.MediaStore;

import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.blankj.utilcode.util.UriUtils;
import com.czur.cloud.entity.OcrGoogleEntity;
import com.czur.cloud.util.PackageManagerUtils;
import com.google.api.client.extensions.android.http.AndroidHttp;
import com.google.api.client.http.HttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.vision.v1.Vision;
import com.google.api.services.vision.v1.VisionRequest;
import com.google.api.services.vision.v1.VisionRequestInitializer;
import com.google.api.services.vision.v1.model.AnnotateImageRequest;
import com.google.api.services.vision.v1.model.BatchAnnotateImagesRequest;
import com.google.api.services.vision.v1.model.BatchAnnotateImagesResponse;
import com.google.api.services.vision.v1.model.Feature;
import com.google.api.services.vision.v1.model.Image;
import com.google.api.services.vision.v1.model.ImageContext;
import com.google.gson.Gson;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class HandwritingRecognitionTask {

    public interface HandwritingRecognitionCallback {
        void failedToast();

        void hideProgressDialog1();

        void success(String json);
    }

    public static void handwritingRecognition(Context context, String filePath, int ocrLanguageId, HandwritingRecognitionCallback callback) {
        Uri uri = UriUtils.file2Uri(new File(filePath));
        Bitmap bitmap = null;
        try {
            bitmap = MediaStore.Images.Media.getBitmap(context.getContentResolver(), uri);
            Bitmap rotate = ImageUtils.rotate(bitmap, 90, 0, 0);
            if (bitmap != null) {
                bitmap.recycle();
                bitmap = null;
            }
            callCloudVision(context, rotate, ocrLanguageId, callback);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static void callCloudVision(Context context, final Bitmap bitmap, int ocrLanguageId, HandwritingRecognitionCallback callback) {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<String>() {
            @Override
            public String doInBackground() {
                try {
                    BatchAnnotateImagesResponse response = prepareAnnotationRequest(context, bitmap, ocrLanguageId).execute();
                    OcrGoogleEntity responseEntity = new Gson().fromJson(new Gson().toJson(response),
                            OcrGoogleEntity.class);
                    return responseEntity.getResponses().get(0).getTextAnnotations().get(0).getDescription();
                } catch (IOException e) {
                    callback.failedToast();
                    e.printStackTrace();
                }
                return "global_ocr_error";
            }

            @Override
            public void onSuccess(String result) {
                if (result.equals("global_ocr_error")) {
                    callback.failedToast();
                } else {
                    callback.hideProgressDialog1();
                    callback.success(result);
                }
            }

            @Override
            public void onFail(Throwable t) {
                callback.failedToast();
                super.onFail(t);
            }
        });
    }

    private static Vision.Images.Annotate prepareAnnotationRequest(Context context, final Bitmap bitmap, int ocrLanguageId) throws IOException {
        HttpTransport httpTransport = AndroidHttp.newCompatibleTransport();
        JsonFactory jsonFactory = GsonFactory.getDefaultInstance();

        VisionRequestInitializer requestInitializer =
                new VisionRequestInitializer(CZURConstants.CLOUD_VISION_API_KEY) {
                    /**
                     * We override this so we can inject important identifying fields into the HTTP
                     * headers. This enables use of a restricted cloud platform API key.
                     */
                    @Override
                    protected void initializeVisionRequest(VisionRequest<?> visionRequest)
                            throws IOException {
                        super.initializeVisionRequest(visionRequest);

                        String packageName = context.getPackageName();
                        visionRequest.getRequestHeaders().set(CZURConstants.ANDROID_PACKAGE_HEADER, packageName);

                        String sig = PackageManagerUtils.getSignature(context.getPackageManager(), packageName);

                        visionRequest.getRequestHeaders().set(CZURConstants.ANDROID_CERT_HEADER, sig);
                    }
                };

        Vision.Builder builder = new Vision.Builder(httpTransport, jsonFactory, null);
        builder.setVisionRequestInitializer(requestInitializer);

        Vision vision = builder.build();

        BatchAnnotateImagesRequest batchAnnotateImagesRequest =
                new BatchAnnotateImagesRequest();
        batchAnnotateImagesRequest.setRequests(new ArrayList<AnnotateImageRequest>() {{
            AnnotateImageRequest annotateImageRequest = new AnnotateImageRequest();

            // Add the image
            Image base64EncodedImage = new Image();
            // Convert the bitmap to a JPEG
            // Just in case it's a format that Android understands but Cloud Vision
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            bitmap.compress(Bitmap.CompressFormat.JPEG, 90, byteArrayOutputStream);
            byte[] imageBytes = byteArrayOutputStream.toByteArray();

            // Base64 encode the JPEG
            base64EncodedImage.encodeContent(imageBytes);
            annotateImageRequest.setImage(base64EncodedImage);

            // add the features we want
            annotateImageRequest.setFeatures(new ArrayList<Feature>() {{
                Feature labelDetection = new Feature();
                labelDetection.setType("DOCUMENT_TEXT_DETECTION");
                add(labelDetection);
            }});
            List<String> languageHint = new ArrayList<>();
            languageHint.add(CZURConstants.GOOGLE_LANGUAGE[ocrLanguageId] + CZURConstants.GOOGLE_OCR);
            annotateImageRequest.setImageContext(new ImageContext().setLanguageHints(languageHint));

            // Add the list of one thing to the request
            add(annotateImageRequest);
        }});

        Vision.Images.Annotate annotateRequest =
                vision.images().annotate(batchAnnotateImagesRequest);
        // Due to a bug: requests to Vision API containing large images fail when GZipped.
        annotateRequest.setDisableGZipContent(true);

        return annotateRequest;
    }
}
