<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <application>
        <service
            android:name="com.czur.cloud.vendorPush.MyFirebaseMessagingService"
            android:directBootAware="true"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <!--谷歌推送设置channel和图标-->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="aura_mate_device" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@mipmap/small_icon" />
    </application>

    <!--TODO 海外无需安装权限 -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"
        tools:node="remove"
        tools:ignore="ManifestOrder" />

    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:node="remove"
        tools:ignore="QueryAllPackagesPermission" />

</manifest>
