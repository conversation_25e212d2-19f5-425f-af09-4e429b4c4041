<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 小米 begin -->
    <permission
        android:name="com.czur.cloud.permission.MIPUSH_RECEIVE"
        android:protectionLevel="signature" />
    <uses-permission android:name="com.czur.cloud.permission.MIPUSH_RECEIVE" />
    <!-- 小米 end -->

    <!-- oppo begin -->
    <uses-permission android:name="com.coloros.mcs.permission.RECIEVE_MCS_MESSAGE" />
    <uses-permission android:name="com.heytap.mcs.permission.RECIEVE_MCS_MESSAGE" />
    <!-- oppo end -->

    <!-- meizu begin -->
    <!-- 兼容 Flyme5 以下版本，魅族内部接入 PushSDK 必填，不然无法收到消息-->
    <uses-permission android:name="com.meizu.flyme.push.permission.RECEIVE" />
    <permission
        android:name="com.czur.cloud.push.permission.MESSAGE"
        android:protectionLevel="signature" />
    <uses-permission android:name="com.czur.cloud.push.permission.MESSAGE" />
    <!-- 兼容 Flyme3 配置权限-->
    <uses-permission android:name="com.meizu.c2dm.permission.RECEIVE" />
    <permission
        android:name="com.czur.cloud.permission.C2D_MESSAGE"
        android:protectionLevel="signature" />
    <uses-permission android:name="com.czur.cloud.permission.C2D_MESSAGE" />
    <!-- meizu end -->

    <application>
        <!-- vivo push begin -->
        <service
            android:name="com.vivo.push.sdk.service.CommandClientService"
            android:permission="com.push.permission.UPSTAGESERVICE"
            android:exported="true" />

        <meta-data
            android:name="api_key"
            android:value="c2b386c5-2cc3-4682-8b8f-d29ca3eaf981" />
        <meta-data
            android:name="app_id"
            android:value="19056" />
        <!--Vivo Push SDK的版本信息-->
        <meta-data
            android:name="sdk_version_vivo"
            android:value="506"/>

        <receiver android:name="com.czur.cloud.vendorPush.vivo.VivoPushMessageReceiverImpl"
            android:exported="false">
            <intent-filter>
                <action android:name="com.vivo.pushclient.action.RECEIVE" />
            </intent-filter>
        </receiver>
        <!-- vivo push end -->

        <!-- oppo push begin -->
        <service android:name="com.heytap.msp.push.service.CompatibleDataMessageCallbackService"
            android:permission="com.coloros.mcs.permission.SEND_MCS_MESSAGE"
            android:exported="true">
            <intent-filter>
                <action android:name="com.coloros.mcs.action.RECEIVE_MCS_MESSAGE" />
            </intent-filter>
        </service>
<!--        （兼容Q以下版本，继承CompatibleDataMessageCallbackService）-->
        <service android:name="com.heytap.msp.push.service.DataMessageCallbackService"
            android:permission="com.heytap.mcs.permission.SEND_PUSH_MESSAGE"
            android:exported="true">
            <intent-filter>
                <action android:name="com.heytap.mcs.action.RECEIVE_MCS_MESSAGE" />
                <action android:name="com.heytap.msp.push.RECEIVE_MCS_MESSAGE" />
            </intent-filter>
        </service>
<!--        （兼容Q版本，继承DataMessageCallbackService）-->
        <!-- oppo push end -->

        <!-- xiaomi push begin -->
        <activity
            android:name="com.xiaomi.mipush.sdk.NotificationClickedActivity"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:launchMode="singleInstance"
            android:exported="true"
            android:enabled="true">
        </activity>

        <service
            android:name="com.xiaomi.push.service.XMPushService"
            android:enabled="true"
            android:process=":pushservice" />

        <service
            android:name="com.xiaomi.push.service.XMJobService"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":pushservice" />

        <service
            android:name="com.xiaomi.mipush.sdk.PushMessageHandler"
            android:enabled="true"
            android:exported="true" />

        <service
            android:name="com.xiaomi.mipush.sdk.MessageHandleService"
            android:enabled="true" />

        <receiver
            android:name="com.xiaomi.push.service.receivers.NetworkStatusReceiver"
            android:exported="true">

            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.xiaomi.push.service.receivers.PingReceiver"
            android:exported="false"
            android:process=":pushservice">

            <intent-filter>
                <action android:name="com.xiaomi.push.PING_TIMER" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.czur.cloud.vendorPush.xiaomi.XiaoMiMessageReceiver"
            android:exported="true">
            <!--这里com.xiaomi.mipushdemo.DemoMessageRreceiver改成app中定义的完整类名-->
            <intent-filter>
                <action android:name="com.xiaomi.mipush.RECEIVE_MESSAGE" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.mipush.MESSAGE_ARRIVED" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.mipush.ERROR" />
            </intent-filter>
        </receiver>
        <!-- xiaomi push end -->

        <!-- 华为 begin -->
        <service
            android:name="com.czur.cloud.vendorPush.huawei.HuaWeiHmsMessageService"
            android:exported="false">

            <intent-filter>
                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <!-- 华为 end -->

        <!-- meizu begin -->
        <!-- push 应用定义消息 receiver 声明 -->
        <receiver android:name="com.czur.cloud.vendorPush.meizu.MeizuPushMsgReceiver"
            android:exported="true">
            <intent-filter>
                <!-- 接收 push 消息 -->
                <action android:name="com.meizu.flyme.push.intent.MESSAGE" />
                <!-- 接收 register 消息 -->
                <action android:name="com.meizu.flyme.push.intent.REGISTER.FEEDBACK" />
                <!-- 接收 unregister 消息-->
                <action android:name="com.meizu.flyme.push.intent.UNREGISTER.FEEDBACK" />
                <!-- 兼容低版本 Flyme3 推送服务配置 -->
                <action android:name="com.meizu.c2dm.intent.REGISTRATION" />
                <action android:name="com.meizu.c2dm.intent.RECEIVE" />

                <category android:name="com.czur.cloud" />
            </intent-filter>
        </receiver>
        <!-- meizu end -->

        <!-- jpush begin -->
        <!-- 这个是自定义Service，要继承极光JCommonService，可以在更多手机平台上使得推送通道保持的更稳定 -->
        <service
            android:name="com.czur.cloud.vendorPush.jpush.PushService"
            android:enabled="true"
            android:exported="false"
            android:process=":pushcore">
            <intent-filter>
                <action android:name="cn.jiguang.user.service.action" />
            </intent-filter>
        </service>
        <!-- 可配置android:process参数将PushService放在其他进程中 -->
        <service
            android:name="cn.jpush.android.service.PushService"
            android:process=":pushcore"
            android:exported="false">
            <intent-filter>
                <action android:name="cn.jpush.android.intent.REGISTER" />
                <action android:name="cn.jpush.android.intent.REPORT" />
                <action android:name="cn.jpush.android.intent.PushService" />
                <action android:name="cn.jpush.android.intent.PUSH_TIME" />
            </intent-filter>
        </service>

        <!-- since 3.0.9 Required SDK 核心功能-->
        <provider
            android:name="cn.jpush.android.service.DataProvider"
            android:authorities="com.czur.cloud.DataProvider"
            android:exported="false"
            android:process=":pushcore" />

        <!-- since 1.8.0 option 可选项。用于同一设备中不同应用的JPush服务相互拉起的功能。 -->
        <!-- 若不启用该功能可删除该组件，或把 enabled 设置成 false ；App 不会被其他 App 拉起，但会拉起其他的 App。 -->
        <service
            android:name="cn.jpush.android.service.DaemonService"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="cn.jpush.android.intent.DaemonService" />
                <category android:name="com.czur.cloud" />
            </intent-filter>

        </service>

        <!-- 可选，如果使用静态Activity方式拉起，该组件必须声明 -->
        <activity
            android:name="cn.jpush.android.service.DActivity"
            android:enabled="true"
            android:exported="true"
            android:taskAffinity="jpush.custom"
            android:theme="@android:style/Theme.Translucent.NoTitleBar">
            <intent-filter>
                <action android:name="cn.jpush.android.intent.DActivity" />
                <category android:name="com.czur.cloud" />
            </intent-filter>
        </activity>

        <!-- since 3.1.0 Required SDK 核心功能-->
        <provider
            android:name="cn.jpush.android.service.DownloadProvider"
            android:authorities="com.czur.cloud.DownloadProvider"
            android:exported="true" />

        <!-- Required SDK核心功能-->
        <receiver
            android:name="cn.jpush.android.service.PushReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="cn.jpush.android.intent.NOTIFICATION_RECEIVED_PROXY" />   <!--Required  显示通知栏 -->
                <category android:name="com.czur.cloud" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.USER_PRESENT" />
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
            <!-- Optional -->
            <intent-filter>
                <action android:name="android.intent.action.PACKAGE_ADDED" />
                <action android:name="android.intent.action.PACKAGE_REMOVED" />

                <data android:scheme="package" />
            </intent-filter>
        </receiver>

        <!-- Required SDK核心功能-->
        <receiver
            android:name="cn.jpush.android.service.AlarmReceiver"
            android:exported="false" />

        <!-- 3.5.0新增，用于定时展示功能 -->
        <receiver
            android:name="cn.jpush.android.service.SchedulerReceiver"
            android:exported="false" />

        <!-- User defined. 用户自定义的广播接收器-->
        <receiver
            android:name="com.czur.cloud.vendorPush.jpush.PushMessageReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="cn.jpush.android.intent.REGISTRATION" />
                <action android:name="cn.jpush.android.intent.RECEIVE_MESSAGE" />

                <category android:name="com.czur.cloud" />
            </intent-filter>
        </receiver>
        <!--since 3.3.0 Required SDK核心功能-->
        <activity
            android:name="cn.jpush.android.service.JNotifyActivity"
            android:exported="false"
            android:taskAffinity="jpush.custom"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            tools:replace="android:exported">
            <intent-filter>
                <action android:name="cn.jpush.android.intent.JNotifyActivity" />
                <category android:name="com.czur.cloud" />
            </intent-filter>
        </activity>
<!--        <activity-->
<!--            android:name="com.czur.cloud.ui.et.wifi.EtWifiHistoryActivity"-->
<!--            android:configChanges="orientation|keyboardHidden|screenSize"-->
<!--            android:screenOrientation="portrait"-->
<!--            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />-->
        <!-- since 3.5.6 新增华硕通道  -->
        <receiver android:name="cn.jpush.android.asus.AsusPushMessageReceiver" />
        <!-- Required  . Enable it you can get statistics data with channel -->
        <meta-data
            android:name="JPUSH_CHANNEL"
            android:value="developer-default" />
        <meta-data
            android:name="JPUSH_APPKEY"
            android:value="0794d20b5c12c8225704d29b" /> <!--  </>值来自开发者平台取得的AppKey-->
        <!-- jpush end -->
    </application>

</manifest>
