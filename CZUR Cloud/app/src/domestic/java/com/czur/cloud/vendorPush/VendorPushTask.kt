package com.czur.cloud.vendorPush

import android.app.*
import android.content.Context
import android.os.Build
import android.os.Process
import android.text.TextUtils
import android.util.Log
import cn.jpush.android.api.JPushInterface
import com.czur.cloud.event.vendorPush.NewVendorPushRegIDEvent
import com.czur.cloud.event.vendorPush.VendorPushRegIDEvent
import com.czur.cloud.netty.observer.NetChangeObserver
import com.czur.cloud.netty.observer.NetStateReceiver
import com.czur.cloud.netty.observer.NetUtils
import com.czur.cloud.preferences.UserPreferences
import com.czur.cloud.preferences.VendorPushPreferences
import com.czur.cloud.util.VendorUtils
import com.heytap.msp.push.HeytapPushManager
import com.heytap.msp.push.callback.ICallBackResultService
import com.huawei.agconnect.config.AGConnectServicesConfig
import com.huawei.hms.aaid.HmsInstanceId
import com.huawei.hms.common.ApiException
import com.huawei.hms.push.HmsMessaging
import com.meizu.cloud.pushsdk.PushManager
import com.vivo.push.PushClient
import com.vivo.push.PushConfig
import com.xiaomi.mipush.sdk.MiPushClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import java.util.concurrent.atomic.AtomicBoolean

object VendorPushTask {

    const val TAG = "VendorPushTask"

    // oppo
    private const val OPPO_APP_KEY = "9eb51d4b3b4145db9144922149241162"
    private const val OPPO_APP_SECRET = "7d56ff12156f4ca290256bc7d8fc763f"

    // xiaomi
    private const val XIAO_MI_APP_ID = "2882303761518317292"
    private const val XIAO_MI_APP_KEY = "5351831721292"

    // meizu
    private const val MEIZU_APP_ID = "128042"
    private const val MEIZU_APP_KEY = "55a9c39fea6b4c0a944d26ecdcb711aa"

    // Aura Mate 通知channel
    private const val CHANNEL_ID_AURA_MATE = "aura_mate_device"
    private const val CHANNEL_NAME_AURA_MATE = "AuraMate设备通知"
    private const val CHANNEL_DESCRIPTION_AURA_MATE = "AuraMate设备所有功能的通知"

    private lateinit var app: Application
    private var isRegIDTaskRunning = AtomicBoolean(false)

    fun init(app: Application) {
        this.app = app

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = app.getSystemService(Activity.NOTIFICATION_SERVICE) as NotificationManager
            if (notificationManager.getNotificationChannel(CHANNEL_ID_AURA_MATE) == null) {
                val channel = NotificationChannel(CHANNEL_ID_AURA_MATE, CHANNEL_NAME_AURA_MATE, NotificationManager.IMPORTANCE_HIGH)
                channel.description = CHANNEL_DESCRIPTION_AURA_MATE
                notificationManager.createNotificationChannel(channel)
            }
        }

        if (VendorPushPreferences.phoneRom == null) {
            VendorPushPreferences.phoneRom = VendorUtils.check()
        }

        NetStateReceiver.registerObserver(object : NetChangeObserver {
            override fun onWifiDisabling() {
            }

            override fun onWifiEnabled() {
            }

            override fun onNetDisConnect() {
            }

            override fun onNetConnected(type: NetUtils.NetType?) {
                if (!UserPreferences.getInstance(app).isUserLogin) {
                    return
                }
                on()
            }
        })
    }

    fun on() {
        if (!UserPreferences.getInstance(app).isUserLogin) {
            return
        }
        GlobalScope.launch(Dispatchers.IO) {
            if (isRegIDTaskRunning.get()) {
                return@launch
            }
            isRegIDTaskRunning.set(true)
            when (VendorPushPreferences.phoneRom) {
                VendorUtils.RomEnum.VIVO -> vivoOn()
                VendorUtils.RomEnum.OPPO -> oppoOn()
                VendorUtils.RomEnum.REALME -> oppoOn()
                VendorUtils.RomEnum.ONEPLUS -> oppoOn()
                VendorUtils.RomEnum.XIAOMI -> xiaomiOn()
                VendorUtils.RomEnum.HUAWEI -> huaweiOn()
                VendorUtils.RomEnum.MEIZU -> meizuOn()
                else -> {
                    jiguangOn()
                }
            }
        }
    }

    fun off() {
        if (!UserPreferences.getInstance(app).isUserLogin) {
            return
        }
        GlobalScope.launch(Dispatchers.IO) {
            when (VendorPushPreferences.phoneRom) {
                VendorUtils.RomEnum.VIVO -> vivoOff()
                VendorUtils.RomEnum.OPPO -> oppoOff()
                VendorUtils.RomEnum.REALME -> oppoOff()
                VendorUtils.RomEnum.ONEPLUS -> oppoOff()
                VendorUtils.RomEnum.XIAOMI -> xiaomiOff()
                VendorUtils.RomEnum.HUAWEI -> huaweiOff()
                VendorUtils.RomEnum.MEIZU -> meizuOff()
                else -> {
                    jiguangOff()
                }
            }
        }
    }

    private fun vivoOn() {
        val config = PushConfig.Builder()
                .agreePrivacyStatement(true)
                .build();
        PushClient.getInstance(app).initialize(config);
        PushClient.getInstance(app).turnOnPush {
            Log.d(TAG, "${VendorPushPreferences.phoneRom?.rom} 初始化 开关打开状态 $it")
            isRegIDTaskRunning.set(false)
        }
    }

    private fun vivoOff() {
        PushClient.getInstance(app).turnOffPush {}
    }

    private fun oppoOn() {
        // oppo
        HeytapPushManager.init(app, false)
        if (HeytapPushManager.isSupportPush(app)) {
            HeytapPushManager.register(app, OPPO_APP_KEY, OPPO_APP_SECRET, object : ICallBackResultService {
                override fun onGetPushStatus(p0: Int, p1: Int) {
                }

                override fun onRegister(code: Int, regID: String?, p2: String?, p3: String?) {
                    isRegIDTaskRunning.set(false)
                    if (code == 0 && !TextUtils.isEmpty(regID)) {
                        Log.d(TAG, "${VendorPushPreferences.phoneRom?.rom} 注册成功 registerId:$regID")
                        if (TextUtils.isEmpty(UserPreferences.getInstance().regid)) {
                            UserPreferences.getInstance().regid = regID
                            EventBus.getDefault().postSticky(VendorPushRegIDEvent())
                        } else if (UserPreferences.getInstance().regid != regID) {
                            UserPreferences.getInstance().regid = regID
                            EventBus.getDefault().postSticky(NewVendorPushRegIDEvent())
                        }
                    } else {
                        Log.d(TAG, "${VendorPushPreferences.phoneRom?.rom} 注册失败 code=$code,msg=$regID")
                    }
                }

                override fun onUnRegister(p0: Int, p1: String?, p2: String?) {
                }

                override fun onSetPushTime(p0: Int, p1: String?) {
                }

                override fun onGetNotificationStatus(p0: Int, p1: Int) {
                }

                override fun onError(p0: Int, p1: String?, p2: String?, p3: String?) {
                }
            })
            HeytapPushManager.requestNotificationPermission()
        }
    }

    private fun oppoOff() {
        HeytapPushManager.unRegister()
    }

    private fun xiaomiOn() {
        if (xiaomiShouldInit()) {
            //小米的必须每次启动调用
            MiPushClient.registerPush(app, XIAO_MI_APP_ID, XIAO_MI_APP_KEY)
        }
        isRegIDTaskRunning.set(false)
    }

    private fun xiaomiShouldInit(): Boolean {
        val am = app.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val processInfos = am.runningAppProcesses
        val mainProcessName = app.packageName
        val myPid = Process.myPid()
        for (info in processInfos) {
            if (info.pid == myPid && mainProcessName == info.processName) {
                return true
            }
        }
        return false
    }

    private fun xiaomiOff() {
        //关闭MiPush推送服务，当用户希望不再使用MiPush推送服务的时候调用，调用成功之后，app将不会接收到任何MiPush服务推送的数据，直到下一次调用registerPush ()
        MiPushClient.unregisterPush(app)
    }

    private fun huaweiOn() {
        try {
            // EMUI 10 以上这里获得token，以下Service onNewToken()中获得token
            val appId = AGConnectServicesConfig.fromContext(app).getString("client/app_id")
            val token = HmsInstanceId.getInstance(app).getToken(appId, "HCM")
            Log.d(TAG, "huawei token: $token")
            if (!TextUtils.isEmpty(token)) {
                if (TextUtils.isEmpty(UserPreferences.getInstance().regid)) {
                    UserPreferences.getInstance().regid = token
                    EventBus.getDefault().postSticky(VendorPushRegIDEvent())
                } else if (UserPreferences.getInstance().regid != token) {
                    UserPreferences.getInstance().regid = token
                    EventBus.getDefault().postSticky(NewVendorPushRegIDEvent())
                }
            } else {
                Log.d(TAG, "华为token为空")
            }
        } catch (e: ApiException) {
            e.printStackTrace()
        }
        HmsMessaging.getInstance(app).turnOnPush()
        isRegIDTaskRunning.set(false)
    }

    private fun huaweiOff() {
        HmsMessaging.getInstance(app).turnOffPush()
    }

    private fun meizuOn() {
        PushManager.register(app, MEIZU_APP_ID, MEIZU_APP_KEY)
    }

    private fun meizuOff() {
        PushManager.unRegister(app, MEIZU_APP_ID, MEIZU_APP_KEY)
    }

    private fun jiguangOn() {
        JPushInterface.setDebugMode(false) // 设置开启日志,发布时请关闭日志
        JPushInterface.init(app)
        JPushInterface.resumePush(app) //必须要加
        if (!TextUtils.isEmpty(JPushInterface.getRegistrationID(app))) {
            if (TextUtils.isEmpty(UserPreferences.getInstance().regid)) {
                UserPreferences.getInstance().regid = JPushInterface.getRegistrationID(app)
                EventBus.getDefault().postSticky(VendorPushRegIDEvent())
            } else if (UserPreferences.getInstance().regid != JPushInterface.getRegistrationID(app)) {
                UserPreferences.getInstance().regid = JPushInterface.getRegistrationID(app)
                EventBus.getDefault().postSticky(NewVendorPushRegIDEvent())
            }
        }
    }

    private fun jiguangOff() {
        JPushInterface.stopPush(app)
    }

    fun initGooglePush(activity: Activity) {
    }

    fun oppoRequestNotificationPermission() {
        HeytapPushManager.requestNotificationPermission()
    }
}