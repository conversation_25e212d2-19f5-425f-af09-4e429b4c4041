package com.czur.cloud.ui.component.popup;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.ui.home.WelcomeActivity;
import com.czur.cloud.ui.market.WebViewActivity;
import com.czur.cloud.util.validator.StringUtils;

import org.jetbrains.annotations.NotNull;

import java.util.Calendar;

import kotlin.jvm.internal.Intrinsics;


/**
 * Created by Yz on 2018/3/20
 * Email：<EMAIL>
 */

public class PrivacyPopup extends Dialog {

    private static final String USER_PRIVACY_USER = "userPrivacyUser";
    private static final String USER_PRIVACY_PRIVACY = "userPrivacyPrivacy";
    private static final String USER_PRIVACY_INFO = "userPrivacyInfo";
    private static final String USER_PRIVACY_SHARE = "userPrivacyShare";
    public static final float DIMMED_OPACITY = 0.2f;

    public PrivacyPopup(Context context) {
        super(context);
    }

    public PrivacyPopup(Context context, int theme) {
        super(context, theme);
    }

    public static class Builder {
        private Context context;
        private CloudCommonPopupConstants constants;
        private boolean isError = false;
        private boolean isFirstFinish = true;
        private String title;
        private View contentsView;

        private OnClickListener positiveListener;
        private OnClickListener onNegativeListener;

        public Builder(Context context, CloudCommonPopupConstants constants) {
            this.context = context;
            this.constants = constants;
        }

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setContentsView(View contentsView) {
            this.contentsView = contentsView;
            return this;
        }

        public Builder setContentsView(int resource) {
            this.contentsView = LayoutInflater.from(context).inflate(resource, null);
            return this;
        }

        public Builder setOnPositiveListener(OnClickListener positiveListener) {
            this.positiveListener = positiveListener;
            return this;
        }

        public Builder setOnNegativeListener(OnClickListener onNegativeListener) {
            this.onNegativeListener = onNegativeListener;
            return this;
        }

        public PrivacyPopup create() {
            DisplayMetrics dm = new DisplayMetrics();
            WindowManager m = ActivityUtils.getTopActivity().getWindowManager();
            m.getDefaultDisplay().getMetrics(dm);
            LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            final PrivacyPopup dialog = new PrivacyPopup(context, R.style.TransparentProgressDialog);
            View layout = commonCustomPopLayout(inflater, dialog);
            dialog.setContentView(layout);
            dialog.setCanceledOnTouchOutside(false);
            dialog.setCancelable(false);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            params.dimAmount = DIMMED_OPACITY;
            params.width = (int) (dm.widthPixels * 0.74);
            params.height = (int) (dm.heightPixels * 0.6);
            dialog.getWindow().setAttributes(params);
            BarUtils.setStatusBarColor(dialog.getWindow(), Color.TRANSPARENT);
            BarUtils.setNavBarColor(dialog.getWindow(), Color.TRANSPARENT);
            BarUtils.setStatusBarLightMode(dialog.getWindow(), true);

//            dialog.window?.setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE)
            dialog.getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
            return dialog;
        }

        private View commonCustomPopLayout(LayoutInflater inflater, final PrivacyPopup dialog) {
            WindowManager.LayoutParams lp = dialog.getWindow().getAttributes();
            lp.dimAmount = 0.8f;
            dialog.getWindow().setAttributes(lp);
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

            View layout = inflater.inflate(R.layout.dialog_privacy_text_common, null, false);
            ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);

            dialog.addContentView(layout, params);

            TextView title = (TextView) layout.findViewById(R.id.title);
            ImageView backBtn = (ImageView) layout.findViewById(R.id.add_tag_back_btn);
            TextView positiveBtn = (TextView) layout.findViewById(R.id.positive_button);
            TextView negativeBtn = (TextView) layout.findViewById(R.id.negative_button);
            FrameLayout webContainer = (FrameLayout) layout.findViewById(R.id.web_frame);
            RelativeLayout reloadWebviewRl = (RelativeLayout) layout.findViewById(R.id.reload_webview_rl);
            TextView reloadBtn = (TextView) layout.findViewById(R.id.reload_btn);

            TextView textView = new TextView(context);
            String userPrivacyExplain = context.getString(R.string.user_first_in_privacy_explain);
            String userPrivacyUser = context.getString(R.string.user_privacy_user_punctuation);
            String userPrivacyPrivacy = context.getString(R.string.user_privacy_privacy_punctuation);
            String userPrivacyInfo = context.getString(R.string.user_privacy_info_punctuation);
            String userPrivacyShare = context.getString(R.string.user_privacy_share_punctuation);
            SpannableStringBuilder spannableBuilder = new SpannableStringBuilder((CharSequence) userPrivacyExplain);
            setTextViewColorAndClick("userPrivacyUser", spannableBuilder, userPrivacyExplain.indexOf(userPrivacyUser), userPrivacyExplain.indexOf(userPrivacyUser) + userPrivacyUser.length());
            setTextViewColorAndClick("userPrivacyPrivacy", spannableBuilder, userPrivacyExplain.indexOf(userPrivacyPrivacy), userPrivacyExplain.indexOf(userPrivacyPrivacy) + userPrivacyPrivacy.length());
            setTextViewColorAndClick("userPrivacyInfo", spannableBuilder, userPrivacyExplain.indexOf(userPrivacyInfo), userPrivacyExplain.indexOf(userPrivacyInfo) + userPrivacyInfo.length());
            setTextViewColorAndClick("userPrivacyShare", spannableBuilder, userPrivacyExplain.indexOf(userPrivacyShare), userPrivacyExplain.indexOf(userPrivacyShare) + userPrivacyShare.length());

            textView.setTextSize(12f);
            textView.setMovementMethod(LinkMovementMethod.getInstance());
            textView.setText((CharSequence) spannableBuilder);
            textView.setHighlightColor(Color.parseColor("#00000000"));
            webContainer.addView(textView);

            if (contentsView == null) {

                if (StringUtils.isNotEmpty(this.title)) {
                    title.setText(this.title + StringUtils.EMPTY);
                } else if (constants.getTitle() > 0) {
                    title.setText(context.getResources().getString(constants.getTitle()));
                }
            } else {
                title.setVisibility(View.GONE);
            }
            if (constants.getPositiveBtn() > 0) {
                positiveBtn.setText(context.getResources().getString(constants.getPositiveBtn()));
            } else {
                positiveBtn.setVisibility(View.GONE);
            }

            if (positiveListener != null) {
                positiveBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        positiveListener.onClick(dialog, constants.getPositiveBtn());
                    }
                });
            } else {
                positiveBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialog.dismiss();
                    }
                });
            }
            if (negativeBtn != null) {
                negativeBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onNegativeListener.onClick(dialog, constants.getPositiveBtn());
                    }
                });
            } else {
                negativeBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialog.dismiss();
                        ActivityUtils.finishActivity(WelcomeActivity.class);
                    }
                });
            }

            if (onNegativeListener != null) {
                backBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onNegativeListener.onClick(dialog, constants.getNegativeBtn());
                    }
                });
            } else {
                backBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialog.dismiss();
                    }
                });
            }
            return layout;
        }

        private void setTextViewColorAndClick(String type, SpannableStringBuilder spannableBuilder, int clickTextPositionStart, int clickTextPositionEnd) {

            ForegroundColorSpan colorSpan = new ForegroundColorSpan(Color.parseColor("#FF2c86e4"));
            spannableBuilder.setSpan(colorSpan, clickTextPositionStart, clickTextPositionEnd, 33);
            final String pkgName = "com.czur.cloud";
            ClickableSpan clickableSpanTwo = (ClickableSpan) (new ClickableSpan() {
                public void onClick(@NotNull View view) {
                    switch (type) {
                        case USER_PRIVACY_USER:
                            Intent intent = new Intent(context, WebViewActivity.class);
                            intent.putExtra("title", context.getString(R.string.user_privacy_user));
                            long cTime = Calendar.getInstance().getTimeInMillis();
//                            intent.putExtra("url", CZURConstants.PRIVACY_AGREEMENT1 + cTime + "");
                            intent.putExtra("url", BuildConfig.TERMS_URL);
                            ActivityUtils.startActivity(intent);
                            break;
                        case USER_PRIVACY_PRIVACY:
                            Intent intent1 = new Intent(context, WebViewActivity.class);
                            intent1.putExtra("title", context.getString(R.string.user_privacy_privacy));
                            long cTime1 = Calendar.getInstance().getTimeInMillis();
//                            intent1.putExtra("url", CZURConstants.PRIVACY_AGREEMENT2 + cTime1 + "");
                            intent1.putExtra("url", BuildConfig.PRIVACY_AGREEMENT);
                            ActivityUtils.startActivity(intent1);
                            break;
                        case USER_PRIVACY_INFO:
                            Intent intent2 = new Intent(context, WebViewActivity.class);
                            intent2.putExtra("title", context.getString(R.string.user_privacy_info));
                            long cTime2 = Calendar.getInstance().getTimeInMillis();
                            intent2.putExtra("url", CZURConstants.PRIVACY_AGREEMENT3 + cTime2 + "");
                            ActivityUtils.startActivity(intent2);

                            break;
                        case USER_PRIVACY_SHARE:
                            Intent intent3 = new Intent(context, WebViewActivity.class);
                            intent3.putExtra("title", context.getString(R.string.user_privacy_share));
                            long cTime3 = Calendar.getInstance().getTimeInMillis();
                            intent3.putExtra("url", CZURConstants.PRIVACY_AGREEMENT4 + cTime3 + "");
                            ActivityUtils.startActivity(intent3);
                            break;
                    }
                }

                public void updateDrawState(@NotNull TextPaint paint) {
                    Intrinsics.checkNotNullParameter(paint, "paint");
                    paint.setColor(Color.parseColor("#3072F6"));
                    paint.setUnderlineText(false);
                }
            });
            spannableBuilder.setSpan(clickableSpanTwo, clickTextPositionStart, clickTextPositionEnd, 33);
        }
    }
}
