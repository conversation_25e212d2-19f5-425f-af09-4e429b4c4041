package com.czur.cloud.vendorPush.vivo

import android.content.Context
import android.text.TextUtils
import android.util.Log
import com.czur.cloud.event.vendorPush.NewVendorPushRegIDEvent
import com.czur.cloud.event.vendorPush.VendorPushRegIDEvent
import com.czur.cloud.preferences.UserPreferences
import com.czur.cloud.preferences.VendorPushPreferences
import com.czur.cloud.vendorPush.VendorPushTask
import com.vivo.push.model.UPSNotificationMessage
import com.vivo.push.sdk.OpenClientPushMessageReceiver
import org.greenrobot.eventbus.EventBus

class VivoPushMessageReceiverImpl : OpenClientPushMessageReceiver() {
    override fun onNotificationMessageClicked(p0: Context?, p1: UPSNotificationMessage?) {
    }

    /***
     * 当首次turnOnPush成功或regId发生改变时，回调此方法
     * 如需获取regId，请使用PushClient.getInstance(context).getRegId()
     * @param context 应用上下文
     * @param regId 注册id
     */
    override fun onReceiveRegId(p0: Context?, regID: String?) {
        Log.d(VendorPushTask.TAG, "${VendorPushPreferences.phoneRom?.rom} VivoPushMessageReceiverImpl RegId = $regID")
        if (!TextUtils.isEmpty(regID)) {
            if (TextUtils.isEmpty(UserPreferences.getInstance().regid)) {
                UserPreferences.getInstance().regid = regID
                EventBus.getDefault().postSticky(VendorPushRegIDEvent())
            } else if (UserPreferences.getInstance().regid != regID) {
                UserPreferences.getInstance().regid = regID
                EventBus.getDefault().postSticky(NewVendorPushRegIDEvent())
            }
        }
    }
}