package com.czur.cloud.vendorPush.xiaomi

import android.content.Context
import android.text.TextUtils
import android.util.Log
import com.czur.cloud.R
import com.czur.cloud.event.vendorPush.NewVendorPushRegIDEvent
import com.czur.cloud.event.vendorPush.VendorPushRegIDEvent
import com.czur.cloud.preferences.UserPreferences
import com.czur.cloud.vendorPush.VendorPushTask
import com.xiaomi.mipush.sdk.*
import org.greenrobot.eventbus.EventBus

class XiaoMiMessageReceiver : PushMessageReceiver() {

    private var mResultCode = -1L
    private var mRegId: String? = null
    private var mReason: String? = null
    private var mCommand: String? = null
    private var mMessage: String? = null
    private var mTopic: String? = null
    private var mAlias: String? = null
    private var mUserAccount: String? = null
    private var mStartTime: String? = null
    private var mEndTime: String? = null
    private var mAccount: String? = null

    override fun onReceivePassThroughMessage(context: Context?, message: MiPushMessage?) {
        Log.v(VendorPushTask.TAG, "onReceivePassThroughMessage is called. " + message.toString())
        val log = "Receive a passthrough message. Content is ${message!!.content}"

        if (!TextUtils.isEmpty(message.topic)) {
            mTopic = message.topic
        } else if (!TextUtils.isEmpty(message.alias)) {
            mAlias = message.alias
        }

        Log.v(VendorPushTask.TAG, log)
    }

    override fun onNotificationMessageClicked(context: Context?, message: MiPushMessage?) {
        Log.v(VendorPushTask.TAG, "onNotificationMessageClicked is called. " + message.toString())
        val log = "Clicked a notification message. Content is ${message!!.content}"

        if (!TextUtils.isEmpty(message.topic)) {
            mTopic = message.topic
        } else if (!TextUtils.isEmpty(message.alias)) {
            mAlias = message.alias
        }

        Log.v(VendorPushTask.TAG, log)
    }

    override fun onNotificationMessageArrived(context: Context?, message: MiPushMessage?) {
        Log.v(VendorPushTask.TAG, "onNotificationMessageArrived is called. " + message.toString())
        val log = "Arrived a notification message. Content is ${message!!}.content"

        if (!TextUtils.isEmpty(message.topic)) {
            mTopic = message.topic
        } else if (!TextUtils.isEmpty(message.alias)) {
            mAlias = message.alias
        }

        Log.v(VendorPushTask.TAG, log)
    }

    override fun onCommandResult(context: Context?, message: MiPushCommandMessage?) {
        context!!
        Log.v(VendorPushTask.TAG, "onCommandResult is called. " + message.toString())
        val command: String = message!!.command
        val arguments: List<String> = message.commandArguments
        val cmdArg1 = if (arguments.isNotEmpty()) arguments[0] else null
        val cmdArg2 = if (arguments.size > 1) arguments[1] else null
        val log: String
        if (MiPushClient.COMMAND_REGISTER == command) {
            if (message.resultCode == ErrorCode.SUCCESS.toLong()) {
                mRegId = cmdArg1
                Log.w(VendorPushTask.TAG, "注册成功，regID是：$mRegId")
                log = context.getString(R.string.register_success)
            } else {
                log = "Register push fail."
            }
        } else if (MiPushClient.COMMAND_SET_ALIAS == command) {
            if (message.resultCode == ErrorCode.SUCCESS.toLong()) {
                mAlias = cmdArg1
                log = "Set alias $mAlias success."
            } else {
                log = "Set alias fail for ${message.reason}"
            }
        } else if (MiPushClient.COMMAND_UNSET_ALIAS == command) {
            if (message.resultCode == ErrorCode.SUCCESS.toLong()) {
                mAlias = cmdArg1
                log = "Unset alias $mAlias success."
            } else {
                log = "Unset alias fail for ${message.reason}"
            }
        } else if (MiPushClient.COMMAND_SET_ACCOUNT == command) {
            if (message.resultCode == ErrorCode.SUCCESS.toLong()) {
                mAccount = cmdArg1
                log = "Set account $mAccount success."
            } else {
                log = "Set account fail for ${message.reason}"
            }
        } else if (MiPushClient.COMMAND_UNSET_ACCOUNT == command) {
            if (message.resultCode == ErrorCode.SUCCESS.toLong()) {
                mAccount = cmdArg1
                log = "Unset account $mAccount success."
            } else {
                log = "Unset account fail for ${message.reason}"
            }
        } else if (MiPushClient.COMMAND_SUBSCRIBE_TOPIC == command) {
            if (message.resultCode == ErrorCode.SUCCESS.toLong()) {
                mTopic = cmdArg1
                log = "Subscribe topic $mTopic success."
            } else {
                log = "Subscribe topic fail for ${message.reason}"
            }
        } else if (MiPushClient.COMMAND_UNSUBSCRIBE_TOPIC == command) {
            if (message.resultCode == ErrorCode.SUCCESS.toLong()) {
                mTopic = cmdArg1
                log = "Unsubscribe topic $mTopic success."
            } else {
                log = "Unsubscribe topic fail for ${message.reason}"
            }
        } else if (MiPushClient.COMMAND_SET_ACCEPT_TIME == command) {
            if (message.resultCode == ErrorCode.SUCCESS.toLong()) {
                mStartTime = cmdArg1
                mEndTime = cmdArg2
                log = "Set accept time $mStartTime - $mEndTime success."
            } else {
                log = "Set accept time fail for ${message.reason}"
            }
        } else {
            log = message.reason
        }

        Log.v(VendorPushTask.TAG, log)
    }

    override fun onReceiveRegisterResult(context: Context?, message: MiPushCommandMessage?) {
        context!!
        Log.v(VendorPushTask.TAG, "onReceiveRegisterResult is called. " + message!!.toString())
        val command: String = message.command
        val arguments: List<String> = message.commandArguments
        val cmdArg1 = if (arguments.isNotEmpty()) arguments[0] else null
        val log: String
        if (MiPushClient.COMMAND_REGISTER == command) {
            if (message.resultCode == ErrorCode.SUCCESS.toLong()) {
                mRegId = cmdArg1
                log = context.getString(R.string.register_success)
                if (!TextUtils.isEmpty(mRegId)) {
                    if (TextUtils.isEmpty(UserPreferences.getInstance().regid)) {
                        UserPreferences.getInstance().regid = mRegId
                        EventBus.getDefault().postSticky(VendorPushRegIDEvent())
                    } else if (UserPreferences.getInstance().regid != mRegId) {
                        UserPreferences.getInstance().regid = mRegId
                        EventBus.getDefault().postSticky(NewVendorPushRegIDEvent())
                    }
                }
            } else {
                log = "Register push fail."
            }
        } else {
            log = message.reason
        }

        Log.v(VendorPushTask.TAG, log+"mRegId:"+mRegId)
    }
}