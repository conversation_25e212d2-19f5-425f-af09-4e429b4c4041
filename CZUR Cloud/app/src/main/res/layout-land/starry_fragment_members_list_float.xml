<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/starry_land_chat_width"
    android:layout_height="match_parent"
    android:layout_gravity="end"
    android:layout_marginEnd="@dimen/dp_0"
    android:clipChildren="false"
    android:clipToPadding="false"
    app:bl_corners_topLeftRadius="@dimen/starry_meeting_member_bg_corners_radius"
    app:bl_corners_bottomLeftRadius="@dimen/starry_meeting_member_bg_corners_radius"
    android:elevation="10dp"
    app:bl_solid_color="@color/white"
    app:layout_constraintRight_toRightOf="parent"
    app:layout_constraintTop_toTopOf="parent"
    >

    <!--  成员列表页面  -->
    <com.noober.background.view.BLConstraintLayout
        android:id="@+id/memberListContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:elevation="10dp"
        app:bl_corners_topLeftRadius="@dimen/starry_meeting_member_bg_corners_radius"
        app:bl_corners_bottomLeftRadius="@dimen/starry_meeting_member_bg_corners_radius"
        app:bl_solid_color="@color/white"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guidelineTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_begin="60dp" />

        <ImageView
            android:id="@+id/btnCloseIv"
            style="@style/starry_fragment_float_close_style"
            app:layout_constraintBottom_toBottomOf="@id/guidelineTitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/memberTitleTv"
            style="@style/starry_fragment_float_title_style"
            android:text="@string/member_list_title1"
            app:layout_constraintBottom_toBottomOf="@id/guidelineTitle"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:layout_width="0dp"
            android:layout_height="0.5dp"
            android:background="@color/starry_comm_gray_bg"
            app:layout_constraintBottom_toBottomOf="@+id/guidelineTitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/adminControlGroup"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="user_bottom_bar_mic_off_ll,user_bottom_bar_mic_on_ll,user_bottom_bar_lock_ll,user_bottom_bar_add_ll" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/itemMemberRv"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="@+id/guidelineBottom"
            app:layout_constraintTop_toTopOf="@+id/guidelineTitle"
            app:layout_constraintVertical_bias="0.0"
            tools:layout_editor_absoluteX="0dp" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guidelineBottom"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_end="100dp" />

        <LinearLayout
            android:id="@+id/user_bottom_bar_add_ll_company"
            style="@style/member_nav_btn_ll"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/guidelineBottom">

            <ImageView
                android:id="@+id/user_bottom_bar_add_iv_company"
                android:layout_width="@dimen/starry_meeting_member_btn_width"
                android:layout_height="@dimen/starry_meeting_member_btn_height"
                android:padding="0dp"
                android:src="@mipmap/starry_user_all_add" />

            <TextView
                android:id="@+id/user_bottom_bar_add_company"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/starry_user_add_user"
                android:maxLines="1"
                android:textColor="@color/starry_common_text_color"
                android:textSize="@dimen/starryMemberBtnSize" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/user_bottom_bar_mic_off_ll"
            style="@style/member_nav_btn_ll"
            app:layout_constraintEnd_toStartOf="@+id/user_bottom_bar_mic_on_ll"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintHorizontal_chainStyle="spread"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/guidelineBottom">

            <ImageView
                android:id="@+id/user_bottom_bar_mic_off_iv"
                android:layout_width="@dimen/starry_meeting_member_btn_width"
                android:layout_height="@dimen/starry_meeting_member_btn_height"
                android:background="@drawable/circle_with_gray_f2"
                android:padding="12dp"
                android:src="@mipmap/starry_audio_icon" />

            <TextView
                android:id="@+id/user_bottom_bar_mic_off_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:maxLines="1"
                android:text="@string/starry_user_all_audio_off"
                android:textColor="@color/starry_common_text_color"
                android:textSize="@dimen/starryMemberBtnSize" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/user_bottom_bar_mic_on_ll"
            style="@style/member_nav_btn_ll"
            app:layout_constraintEnd_toStartOf="@+id/user_bottom_bar_lock_ll"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/user_bottom_bar_mic_off_ll"
            app:layout_constraintTop_toBottomOf="@+id/guidelineBottom">

            <ImageView
                android:id="@+id/user_bottom_bar_mic_on_iv"
                android:layout_width="@dimen/starry_meeting_member_btn_width"
                android:layout_height="@dimen/starry_meeting_member_btn_height"
                android:background="@drawable/circle_with_gray_f2"
                android:padding="12dp"
                android:src="@mipmap/starry_meeting_member_icon_btn_mic_gray" />

            <TextView
                android:id="@+id/user_bottom_bar_mic_on_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:maxLines="1"
                android:text="@string/starry_user_all_audio_on"
                android:textColor="@color/starry_common_text_color"
                android:textSize="@dimen/starryMemberBtnSize" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/user_bottom_bar_lock_ll"
            style="@style/member_nav_btn_ll"
            app:layout_constraintEnd_toStartOf="@+id/user_bottom_bar_add_ll"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/user_bottom_bar_mic_on_ll"
            app:layout_constraintTop_toBottomOf="@+id/guidelineBottom">

            <ImageView
                android:id="@+id/user_bottom_bar_lock_iv"
                android:layout_width="@dimen/starry_meeting_member_btn_width"
                android:layout_height="@dimen/starry_meeting_member_btn_height"
                android:padding="0dp"
                android:src="@drawable/starry_sel_user_all_lock" />

            <TextView
                android:id="@+id/user_bottom_bar_lock"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:maxLines="1"
                android:text="@string/starry_user_all_lock_off"
                android:textColor="@color/starry_common_text_color"
                android:textSize="@dimen/starryMemberBtnSize" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/user_bottom_bar_add_ll"
            style="@style/member_nav_btn_ll"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/user_bottom_bar_lock_ll"
            app:layout_constraintTop_toBottomOf="@+id/guidelineBottom">

            <ImageView
                android:id="@+id/user_bottom_bar_add_iv"
                android:layout_width="@dimen/starry_meeting_member_btn_width"
                android:layout_height="@dimen/starry_meeting_member_btn_height"
                android:padding="0dp"
                android:src="@mipmap/starry_user_all_add" />

            <TextView
                android:id="@+id/user_bottom_bar_add"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:maxLines="1"
                android:text="@string/starry_user_add_user"
                android:textColor="@color/starry_common_text_color"
                android:textSize="@dimen/starryMemberBtnSize" />
        </LinearLayout>

    </com.noober.background.view.BLConstraintLayout>

    <com.noober.background.view.BLFrameLayout
        android:id="@+id/chooseMemberContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="10dp"
        android:elevation="10dp"
        android:visibility="gone"
        app:bl_corners_radius="10dp"
        app:bl_solid_color="@color/white"
        app:layout_constraintLeft_toRightOf="@id/memberListContainer"
        app:layout_constraintTop_toTopOf="@id/memberListContainer" />

    <com.czur.cloud.ui.starry.meeting.baselib.widget.WordIndexView
        android:id="@+id/memberListIndexBar"
        android:layout_width="90dp"
        android:layout_height="match_parent"
        android:layout_marginLeft="10dp"
        android:elevation="10dp"
        android:visibility="gone"
        app:baselib_bg_color="#E5E5E5"
        app:baselib_tv_color="#5E5E5E"
        app:bl_corners_radius="10dp"
        app:bl_solid_color="@color/white"
        app:layout_constraintLeft_toRightOf="@id/chooseMemberContainer"
        app:layout_constraintTop_toTopOf="@id/chooseMemberContainer" />

</androidx.constraintlayout.widget.ConstraintLayout>