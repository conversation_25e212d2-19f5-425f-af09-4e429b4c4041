<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/starry_land_chat_width"
    android:layout_height="match_parent"
    android:layout_weight="0.5"
    android:weightSum="1"
    android:stretchColumns="1"
    android:animateLayoutChanges="true"
    android:background="@color/white"
    android:orientation="vertical"
    android:layout_gravity="end"
    android:elevation="10dp"
    app:bl_solid_color="@color/white"
    app:layout_constraintRight_toRightOf="parent"
    app:layout_constraintTop_toTopOf="parent"
    app:layout_constraintBottom_toBottomOf="parent"
    app:bl_corners_topLeftRadius="@dimen/starry_meeting_member_bg_corners_radius"
    app:bl_corners_bottomLeftRadius="@dimen/starry_meeting_member_bg_corners_radius"
    >

    <include
        android:id="@+id/starry_top_bar_rl"
        layout="@layout/starry_layout_top_bar_land" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"
            android:background="@color/starry_white_bg"
            android:orientation="vertical">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view_company"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="@dimen/starryMargin10"
                android:background="@color/starry_white_bg"
                android:overScrollMode="never"
                android:layout_weight="1"
                android:visibility="visible" />

            <RelativeLayout
                android:id="@+id/starry_company_list_contacts_rl"
                android:layout_width="match_parent"
                android:layout_height="@dimen/starryMargin50"
                android:clickable="false"
                android:alpha="0.4"
                android:layout_marginStart="@dimen/starryMargin10"
                >

                <ImageView
                    android:id="@+id/starry_home_contacts_iv"
                    android:layout_width="@dimen/starryMargin20"
                    android:layout_height="@dimen/starryMargin20"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/starryMargin20"
                    android:src="@mipmap/starry_home_contacts_icon" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/starryMargin15"
                    android:layout_toEndOf="@+id/starry_home_contacts_iv"
                    android:text="@string/starry_company_list_contacts"
                    android:textColor="@color/title_black_color"
                    android:textSize="@dimen/starryCompanySubTitle" />

                <TextView
                    android:id="@+id/starry_item_company_expried_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="@dimen/starryMargin20"
                    android:text="@string/starry_company_list_contacts_expried"
                    android:textColor="@color/starry_delete_red" />

            </RelativeLayout>


            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginStart="18dp"
                android:background="@color/starry_list_liner_color" />

            <RelativeLayout
                android:id="@+id/starry_company_password_rl"
                android:layout_width="match_parent"
                android:layout_height="@dimen/starryMargin50"
                android:layout_below="@+id/starry_company_list_contacts_rl"
                android:layout_marginStart="@dimen/starryMargin10"
                android:layout_marginTop="@dimen/dp_5"
                >

                <ImageView
                    android:id="@+id/starry_comapny_pwd_iv"
                    android:layout_width="@dimen/starryMargin20"
                    android:layout_height="@dimen/starryMargin20"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/starryMargin20"
                    android:src="@mipmap/starry_company_pwd" />

                <TextView
                    android:id="@+id/starry_comapny_pwd_title"
                    android:layout_width="@dimen/starryMeetPwdWidth"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/starryMargin15"
                    android:layout_toEndOf="@+id/starry_comapny_pwd_iv"
                    android:text="@string/starry_company_pwd_title"
                    android:textColor="@color/title_black_color"
                    android:textSize="@dimen/starryCompanySubTitle" />

                <ImageView
                    android:id="@+id/starry_company_refresh"
                    style="@style/starry_meeting_refresh_style"
                    android:layout_toEndOf="@+id/starry_comapny_pwd_title"
                    android:src="@mipmap/starry_company_refresh" />

                <ImageView
                    android:id="@+id/starry_company_pwd_show"
                    style="@style/starry_meeting_refresh_style"
                    android:layout_toEndOf="@+id/starry_company_refresh"
                    android:src="@drawable/starry_msg_sel" />

                <ImageView
                    android:id="@+id/starry_company_right_arrow"
                    android:layout_width="@dimen/starryMargin10"
                    android:layout_height="@dimen/starryMargin15"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="@dimen/starryMargin20"
                    android:background="@mipmap/starry_right_arrow"
                    android:padding="@dimen/starryMargin10" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/starry_meeting_code_pwd_rl"
                android:layout_width="match_parent"
                android:layout_height="@dimen/starryMargin50"
                android:layout_marginStart="@dimen/starryMargin10"
                android:layout_marginTop="@dimen/dp_5"
                android:visibility="visible">

                <ImageView
                    android:id="@+id/starry_meeting_code_pwd_iv"
                    android:layout_width="@dimen/starryMargin20"
                    android:layout_height="@dimen/starryMargin20"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/starryMargin20"
                    android:src="@mipmap/starry_company_pwd" />

                <TextView
                    android:id="@+id/starry_meeting_code_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/starryMargin15"
                    android:layout_toEndOf="@+id/starry_meeting_code_pwd_iv"
                    android:text="@string/starry_meeting_code_title"
                    android:textColor="@color/title_black_color"
                    android:textSize="@dimen/starryCompanySubTitle" />

                <ImageView
                    android:id="@+id/starry_meeting_code_copy"
                    style="@style/starry_meeting_refresh_style"
                    android:layout_toEndOf="@+id/starry_meeting_code_title"
                    android:src="@mipmap/starry_meetingcode_copy"
                    android:visibility="gone" />

                <ImageView
                    android:id="@+id/starry_meeting_share"
                    android:layout_width="@dimen/starryMargin30"
                    android:layout_height="@dimen/starryMargin30"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="@dimen/starryMargin20"
                    android:padding="@dimen/starryMargin5"
                    android:src="@mipmap/starry_meeting_share" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/starry_meeting_code_pwd_rl2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/starryMargin10"
                android:layout_marginTop="-12dp"
                android:visibility="visible">

                <ImageView
                    android:id="@+id/starry_meeting_pwd_iv"
                    android:layout_width="@dimen/starryMargin20"
                    android:layout_height="@dimen/starryMargin20"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/starryMargin20"
                    android:src="@mipmap/starry_company_pwd"
                    android:visibility="invisible"/>

                <TextView
                    android:id="@+id/starry_meeting_pwd_title"
                    android:layout_width="@dimen/starryMeetPwdWidth"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/starryMargin15"
                    android:layout_toEndOf="@+id/starry_meeting_pwd_iv"
                    android:text="@string/starry_company_pwd_title"
                    android:textColor="@color/title_black_color"
                    android:textSize="@dimen/starryCompanySubTitle" />

                <ImageView
                    android:id="@+id/starry_meeting_pwd_refresh"
                    style="@style/starry_meeting_refresh_style"
                    android:layout_toEndOf="@+id/starry_meeting_pwd_title"
                    android:src="@mipmap/starry_company_refresh"
                    android:visibility="gone"/>

                <ImageView
                    android:id="@+id/starry_meeting_code_pwd_show"
                    style="@style/starry_meeting_refresh_style"
                    android:layout_toEndOf="@+id/starry_meeting_pwd_refresh"
                    android:src="@drawable/starry_show_pwd_sel"
                    android:visibility="invisible"/>

            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/starryMargin15"
                android:layout_marginStart="@dimen/starryMargin20"
                android:background="@color/transparent"
                app:bl_corners_bottomLeftRadius="@dimen/starry_meeting_member_bg_corners_radius"
                />

        </LinearLayout>

</LinearLayout>
