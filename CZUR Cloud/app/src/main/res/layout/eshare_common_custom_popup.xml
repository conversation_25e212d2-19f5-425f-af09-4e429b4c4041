<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="275dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:background="@drawable/btn_rec_5_bg_with_white"
        android:gravity="center_horizontal"
        android:minHeight="175dp"
        android:orientation="vertical">

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="29dp"
            android:layout_marginTop="18dp"
            android:layout_marginRight="29dp"
            android:gravity="center"
            android:text=""
            android:textColor="@color/black_22"
            android:textSize="16sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!--        <androidx.constraintlayout.widget.ConstraintLayout-->
        <!--            android:id="@+id/content_ll"-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:layout_marginStart="22.5dp"-->
        <!--            android:layout_marginTop="7dp"-->
        <!--            android:layout_marginEnd="22.5dp"-->
        <!--            android:layout_marginBottom="33dp"-->
        <!--            app:layout_constraintBottom_toTopOf="@id/bottom_btn_cl"-->
        <!--            app:layout_constraintEnd_toEndOf="parent"-->
        <!--            app:layout_constraintStart_toStartOf="parent"-->
        <!--            app:layout_constraintTop_toBottomOf="@id/title">-->

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="15dp"
            android:gravity="center"
            android:paddingHorizontal="16dp"
            android:text=""
            android:textColor="@color/black_22"
            android:textSize="12sp"
            app:layout_constraintBottom_toTopOf="@id/edt"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title" />

        <EditText
            android:id="@+id/edt"
            android:layout_width="230dp"
            android:layout_height="30dp"
            android:layout_below="@+id/message"
            android:layout_marginBottom="33dp"
            android:background="@drawable/eshare_circle_color_gray_ed"
            android:gravity="center"
            android:inputType="text"
            android:maxLength="20"
            android:paddingStart="3dp"
            android:singleLine="true"
            android:textColor="@color/black_22"
            android:textColorHint="@color/black_22"
            android:textCursorDrawable="@drawable/edittext_cursor_blue"
            android:textSize="12sp"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/no_remind_cl"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/message" />
        <!--        </androidx.constraintlayout.widget.ConstraintLayout>-->


        <TextView
            android:id="@+id/error_code_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/eshare_transmit_file_dialog_code_wrong"
            android:textColor="@color/red_d54146"
            android:textSize="9sp"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="@+id/edt"
            app:layout_constraintTop_toBottomOf="@id/edt" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/no_remind_cl"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:layout_constraintBottom_toTopOf="@id/bottom_btn_cl"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <com.czur.cloud.ui.eshare.widget.ESCheckBox
                android:id="@+id/no_remind_cb"
                android:layout_width="15dp"
                android:layout_height="15dp"
                app:defaultCheckStatus="false"
                app:checkedImage="@mipmap/ic_checkbox_check"
                app:uncheckedImage="@drawable/eshare_circle_color_grey_8dp"
                app:layout_constraintBottom_toBottomOf="@id/no_remind_tv"
                app:layout_constraintEnd_toStartOf="@id/no_remind_tv"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/no_remind_tv" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/no_remind_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/transmit_file_nolonger_remind"
                android:textColor="@color/black_22"
                android:textSize="12sp"
                android:layout_marginStart="6dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/no_remind_cb"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>


        <!-- button -->

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/bottom_btn_cl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="33dp"
            android:layout_marginBottom="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <TextView
                android:id="@+id/negative_button"
                android:layout_width="105dp"
                android:layout_height="30dp"
                android:background="@drawable/eshare_circle_color_gray_ed"
                android:gravity="center"
                android:text="@string/starry_common_dialog_no2"
                android:textColor="@color/title_black_color"
                android:textSize="10sp"
                app:layout_constraintEnd_toStartOf="@+id/positive_button"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="取消" />


            <TextView
                android:id="@+id/positive_button"
                android:layout_width="105dp"
                android:layout_height="30dp"
                android:background="@drawable/eshare_circle_color_blue_5dp"
                android:gravity="center"
                android:text="@string/starry_common_dialog_ok"
                android:textColor="@color/white"
                android:textSize="10sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/negative_button"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="确定" />


        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>
</RelativeLayout>