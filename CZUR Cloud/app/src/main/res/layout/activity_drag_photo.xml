<?xml version="1.0" encoding="UTF-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:orientation="vertical">
    <com.czur.cloud.ui.books.drag.FixMultiViewPager
        android:id="@+id/viewpager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/page_preview_bottom_ll"
        android:layout_below="@+id/page_preview_top_bar" />


    <LinearLayout

        android:id="@+id/page_preview_bottom_ll"
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:layout_alignParentBottom="true"
        android:background="@color/black_2a">

        <RelativeLayout
            android:id="@+id/page_preview_save_album_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/page_preview_save_album_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@mipmap/book_download" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/page_preview_save_album_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/save_to_album"
                    android:textColor="@color/white"
                    android:textSize="9sp"
                    android:textStyle="bold" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/page_preview_handwriting_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/page_preview_handwriting_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@mipmap/handwriting_icon" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/page_preview_handwriting_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/handwriting"
                    android:textColor="@color/white"
                    android:textSize="9sp"
                    android:textStyle="bold" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout

            android:id="@+id/page_preview_star_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/page_preview_star_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_book_page_preview_star" />

                <TextView
                    android:id="@+id/page_preview_star_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/page_preview_star_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/star_page"
                    android:textColor="@color/white"
                    android:textSize="9sp"
                    android:textStyle="bold" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout

            android:id="@+id/page_preview_share_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/page_preview_share_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@mipmap/book_page_share_icon" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/page_preview_share_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/share"
                    android:textColor="@color/white"
                    android:textSize="9sp"
                    android:textStyle="bold" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/page_preview_delete_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/page_preview_delete_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@mipmap/book_delete" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/page_preview_delete_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/delete"
                    android:textColor="@color/white"
                    android:textSize="9sp"
                    android:textStyle="bold" />
            </RelativeLayout>

        </RelativeLayout>

    </LinearLayout>

    <include
        android:id="@+id/page_preview_top_bar"
        layout="@layout/layout_preview_delete_top_bar" />


    <LinearLayout
        android:visibility="gone"
        android:layout_alignParentBottom="true"
        android:id="@+id/handwriting_result_ll"
        android:layout_width="match_parent"
        android:layout_height="280dp"
        android:background="@color/black"
        android:orientation="vertical">

        <!--<LinearLayout-->
        <!--android:layout_width="match_parent"-->
        <!--android:layout_height="0dp"-->
        <!--android:layout_weight="1"></LinearLayout>-->

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"

            android:background="@color/black_2a"
            android:orientation="vertical">

            <RelativeLayout
                android:id="@+id/result_top_bar"
                android:layout_width="match_parent"
                android:layout_height="44dp">


                <TextView
                    android:id="@+id/preview_camera_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/recognition_result"
                    android:textColor="@color/gary_c4"
                    android:textSize="18sp"
                    android:textStyle="bold" />


            </RelativeLayout>

            <com.czur.cloud.ui.component.scrollview.ObservableScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/result_bottom_ll"
                android:layout_below="@+id/result_top_bar">

                <TextView
                    android:id="@+id/handwriting_result_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginLeft="29.5dp"
                    android:layout_marginTop="15dp"
                    android:layout_marginRight="29.5dp"
                    android:textColor="@color/gary_c4"
                    android:textIsSelectable="true"
                    android:textSize="15sp"
                    android:textStyle="bold" />


            </com.czur.cloud.ui.component.scrollview.ObservableScrollView>

            <LinearLayout
                android:id="@+id/result_bottom_ll"
                android:layout_width="match_parent"
                android:layout_height="49dp"
                android:layout_alignParentBottom="true"
                android:background="@color/black_2a">


                <RelativeLayout
                    android:id="@+id/handwriting_result_copy_rl"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1">

                    <RelativeLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true">

                        <ImageView
                            android:id="@+id/handwriting_result_copy_img"
                            android:layout_width="22.5dp"
                            android:layout_height="22.5dp"
                            android:layout_centerHorizontal="true"
                            android:background="@mipmap/handwriting_copy_icon" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/handwriting_result_copy_img"
                            android:layout_centerHorizontal="true"
                            android:layout_marginTop="6.5dp"
                            android:text="@string/copy"
                            android:textColor="@color/white"
                            android:textSize="9sp"
                            android:textStyle="bold" />
                    </RelativeLayout>

                </RelativeLayout>

            </LinearLayout>

        </RelativeLayout>

    </LinearLayout>

</RelativeLayout>




