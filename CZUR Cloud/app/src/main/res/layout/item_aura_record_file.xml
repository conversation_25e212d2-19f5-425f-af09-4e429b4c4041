<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/aura_home_popup_rl"
    android:layout_width="match_parent"
    android:layout_height="60dp">

    <CheckBox
        android:id="@+id/file_cb"
        style="@style/CheckBoxBlue"
        android:layout_width="20dp"
        android:layout_height="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginStart="20dp"
        android:clickable="false"
        android:enabled="false"/>

    <ImageView
        android:id="@+id/movie_iv"
        android:layout_width="24dp"
        android:layout_height="20dp"
        android:layout_marginStart="20dp"
        android:src="@mipmap/ic_aura_files_movie_blue"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/file_cb"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/file_name_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:singleLine="true"
        android:text="timutim1231123123123123123453453453453453u"
        android:textColor="@color/grey_989898"
        android:textSize="16sp"
        android:gravity="left"
        android:textStyle="bold"
        android:layout_marginStart="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/during_time_tv"
        app:layout_constraintStart_toEndOf="@id/movie_iv"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/during_time_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="10dp"
        android:text="11:10"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/arrow_iv"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/arrow_iv"
        android:layout_width="12dp"
        android:layout_height="8dp"
        android:layout_marginEnd="10dp"
        android:src="@mipmap/gray_arrow"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/grey_989898" />
</androidx.constraintlayout.widget.ConstraintLayout>



