<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/starry_item_company_rl"
    android:layout_width="match_parent"
    android:background="@color/white"
    android:layout_height="@dimen/starryMargin70">

    <TextView
        android:id="@+id/starry_item_company_icon"
        android:layout_width="@dimen/starryMargin35"
        android:layout_height="@dimen/starryMargin35"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/starryMargin20"
        android:background="@drawable/starry_circle_with_company_item"
        android:gravity="center"
        android:text=""
        android:textColor="@color/white"
        android:textSize="@dimen/starryFontSize20" />

    <TextView
        android:id="@+id/starry_item_company_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/starryMargin10"
        android:layout_marginEnd="@dimen/starryMargin10"
        android:layout_toStartOf="@+id/starry_item_company_join_ll"
        android:layout_toEndOf="@+id/starry_item_company_icon"
        android:ellipsize="end"
        android:maxLines="1"
        android:text=""
        android:textColor="@color/title_black_color"
        android:textSize="@dimen/starryCompanySubTitle"
        android:visibility="visible" />

    <LinearLayout
        android:id="@+id/starry_item_company_join_ll"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/starryMargin20"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/starry_item_company_expried_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/starry_delete_red"
            android:visibility="gone"
            android:text="@string/starry_company_list_expried" />

        <ImageView
            android:id="@+id/starry_item_company_arrow"
            android:layout_width="@dimen/starryMargin10"
            android:layout_height="@dimen/starryMargin15"
            android:layout_gravity="center"
            android:background="@mipmap/starry_right_arrow"
            android:padding="@dimen/starryMargin10" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/starry_list_liner_color"
        android:layout_marginStart="18dp"
        android:layout_alignParentBottom="true"/>
</RelativeLayout>