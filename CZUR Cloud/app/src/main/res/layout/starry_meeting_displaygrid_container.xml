<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/display2CL"
    android:padding="@dimen/dp_5"
    >

    <include
        android:id="@+id/displayItem1"
        layout="@layout/starry_meeting_item_display_new"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/gv50"
        app:layout_constraintBottom_toTopOf="@+id/gh50"
        android:layout_marginBottom="2.5dp"
        android:layout_marginEnd="2.5dp"
        />

    <include
        android:id="@+id/displayItem2"
        layout="@layout/starry_meeting_item_display_new"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="@+id/gv50"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/gh50"
        android:layout_marginBottom="2.5dp"
        android:layout_marginStart="2.5dp"
        />

    <include
        android:id="@+id/displayItem3"
        layout="@layout/starry_meeting_item_display_new"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="@+id/gh50"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/gv50"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginTop="2.5dp"
        android:layout_marginEnd="2.5dp"
        />

    <include
        android:id="@+id/displayItem4"
        layout="@layout/starry_meeting_item_display_new"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="@+id/gh50"
        app:layout_constraintStart_toStartOf="@+id/gv50"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginTop="2.5dp"
        android:layout_marginStart="2.5dp"
        />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gv50"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gh50"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.5" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gv25"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.25" />
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gv75"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.75" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gh25"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.25" />
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gh75"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.75" />

</androidx.constraintlayout.widget.ConstraintLayout>