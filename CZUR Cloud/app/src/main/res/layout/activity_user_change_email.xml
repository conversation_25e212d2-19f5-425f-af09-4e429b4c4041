<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:fitsSystemWindows="true"
    android:background="@color/gary_f9">


    <LinearLayout
        android:id="@+id/change_email_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/gary_ff"
        android:orientation="vertical">

        <include layout="@layout/layout_user_top_bar" />

        <TextView
            android:id="@+id/user_change_email_info_tv"
            android:layout_width="298.5dp"
            android:lineSpacingExtra="5dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="31dp"
            android:layout_marginTop="45.5dp"
            android:text="@string/user_change_email_first_text"
            android:textColor="@color/black_272f44"
            android:textSize="15sp"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="47dp"
            android:background="@color/gary_ff"
            android:orientation="vertical">

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginLeft="17dp"
                android:background="@color/gary_ef" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="46dp"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/user_change_email_code_edt"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="17dp"
                    android:background="@null"
                    android:digits="@string/password_digits"
                    android:hint="@string/identifying_code"
                    android:inputType="number"
                    android:maxLength="6"
                    android:textColor="@color/normal_blue"
                    android:textColorHint="@color/gary_c4"
                    android:textSize="15sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/user_change_email_send_code_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="17.5dp"
                    android:text="@string/user_send_identifying_code"
                    android:textColor="@color/blue_29b0d7"
                    android:textStyle="bold" />

            </RelativeLayout>


            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/gary_ef" />
        </LinearLayout>


    </LinearLayout>

    <com.czur.cloud.ui.component.ProgressButton
        android:id="@+id/user_change_email_btn"
        android:layout_width="250dp"
        android:layout_height="40dp"
        android:layout_below="@+id/change_email_ll"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="23dp"
        android:layout_marginBottom="30dp"
        android:background="@drawable/selector_register_btn"
        app:progress_btn_color="#ffffff"
        app:progress_btn_tv="@string/next_step" />


</RelativeLayout>