<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/gary_f9">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"

        android:gravity="center_horizontal"
        android:orientation="vertical">

        <include layout="@layout/layout_user_top_bar" />

        <RelativeLayout
            android:id="@+id/et_menu_add_et_rl"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/gary_ff">


            <ImageView
                android:id="@+id/book_menu_add_book_img"
                android:layout_width="16.5dp"
                android:layout_height="16.5dp"
                android:layout_centerVertical="true"
                android:layout_marginStart="16dp"
                android:background="@mipmap/add_book" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="17.5dp"
                android:layout_toEndOf="@+id/book_menu_add_book_img"
                android:text="@string/add_new_et"
                android:textColor="@color/blue_29b0d7"
                android:textSize="15sp" />


            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:layout_marginLeft="17dp"

                android:background="@color/gray_e5" />
        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/book_menu_my_pdf_rl"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/gary_ff">


            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="17.5dp"
                android:text="@string/my_pdf"
                android:textColor="@color/normal_blue"
                android:textSize="15sp" />

            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="17dp"
                android:background="@mipmap/user_right_gray_arrow"
                android:padding="10dp" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/gray_e5" />

        <RelativeLayout
            android:id="@+id/et_menu_advice_rl"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_marginTop="13.5dp"
            android:background="@color/gary_ff">

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/gray_e5" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="17.5dp"
                android:text="@string/advice"
                android:textColor="@color/normal_blue"
                android:textSize="15sp" />


            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="17dp"
                android:background="@mipmap/user_right_gray_arrow"
                android:padding="10dp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:layout_marginStart="17dp"
                android:background="@color/gray_e5" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/et_menu_question_rl"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/gary_ff">


            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="17.5dp"
                android:text="@string/question"
                android:textColor="@color/normal_blue"
                android:textSize="15sp" />


            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="17dp"
                android:background="@mipmap/user_right_gray_arrow"
                android:padding="10dp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:background="@color/gray_e5" />
        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/book_menu_delete_rl"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_marginTop="13.5dp"
            android:background="@color/gary_ff">

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentTop="true"
                android:background="@color/gray_e5" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="17.5dp"
                android:text="@string/delete_from_app"
                android:textColor="@color/red_e75252"
                android:textSize="15sp" />


            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"

                android:background="@color/gray_e5" />
        </RelativeLayout>


    </LinearLayout>


</RelativeLayout>

