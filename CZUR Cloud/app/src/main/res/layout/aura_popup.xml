<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="243dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:background="@drawable/btn_rec_5_bg_with_white"
        android:gravity="center_horizontal"
        android:orientation="vertical">


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:layout_marginLeft="29dp"
            android:layout_marginRight="29dp">

            <TextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text=""
                android:textColor="@color/black_22"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/message"
                android:layout_marginTop="12.5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/title"
                android:gravity="left"
                android:text=""
                android:textColor="@color/black_22"
                android:textSize="12sp"
                android:textStyle="bold" />
            <TextView
                android:id="@+id/message1"
                android:layout_marginTop="1dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/message"
                android:gravity="left"
                android:text=""
                android:textColor="@color/black_22"
                android:textSize="12sp"
                android:textStyle="bold" />
            <TextView
                android:id="@+id/message2"
                android:layout_marginTop="1dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/message1"
                android:gravity="left"
                android:text=""
                android:textColor="@color/black_22"
                android:textSize="12sp"
                android:textStyle="bold" />

        </RelativeLayout>

        <EditText
            android:paddingLeft="3dp"
            android:id="@+id/edt"
            android:maxLength="20"
            android:singleLine="true"
            android:layout_width="216.5dp"
            android:textColor="@color/gary_c4"
            android:textSize="12sp"
            android:textStyle="bold"
            android:background="@drawable/dialog_rec_1_bg_with_white_with_stroke"
            android:layout_marginBottom="10dp"
            android:layout_height="22dp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/gray_e5"></View>
        <!-- button -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/negative_button"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:textStyle="bold"
                android:textColor="@color/black_22"
                android:textSize="14sp" />

            <View
                android:layout_width="0.5dp"
                android:layout_height="match_parent"
                android:background="@color/gray_e5"></View>

            <TextView
                android:id="@+id/positive_button"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text=""
                android:textColor="@color/blue_positive_button"
                android:textSize="14sp" />
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>