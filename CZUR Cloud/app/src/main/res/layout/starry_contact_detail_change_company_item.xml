<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:layout_marginStart="@dimen/starryMargin20"
    android:layout_marginEnd="@dimen/starryMargin20"
    android:id="@+id/starry_contact_detail_company_change_cl"
    android:gravity="center_vertical"
    >

    <TextView
        android:id="@+id/title_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:textSize="16sp"
        android:layout_marginStart="@dimen/starryMargin10"
        android:layout_marginEnd="@dimen/starryMargin20"
        android:layout_centerHorizontal="true"
        tools:text="TextViewTextView"
        android:textColor="@color/starry_text_title_color_black"/>

    <ImageView
        android:id="@+id/select_iv"
        android:layout_width="@dimen/dp_15"
        android:layout_height="@dimen/dp_15"
        android:layout_marginTop="3dp"
        android:layout_alignParentEnd="true"
        android:src="@mipmap/starry_contact_detail_change_company_select" />

</RelativeLayout>