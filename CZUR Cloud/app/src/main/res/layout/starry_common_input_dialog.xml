<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="260dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/btn_rec_5_bg_with_white"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/btnCloseIv"
            android:layout_width="@dimen/starryMargin50"
            android:layout_height="@dimen/starryMargin50"
            android:layout_gravity="end"
            android:padding="@dimen/starryMargin10"
            android:src="@mipmap/starry_btn_close"
            android:visibility="invisible" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="-40dp"
            android:layout_marginRight="30dp">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_5"
                android:layout_marginTop="@dimen/dp_5"
                android:layout_marginEnd="@dimen/dp_5"
                android:layout_marginBottom="@dimen/dp_5"
                android:gravity="center"
                android:text=""
                android:maxLines="1"
                android:ellipsize="middle"
                android:textColor="@color/black_22"
                android:textSize="16sp"
                tools:text="提示" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/title"
                android:layout_marginTop="@dimen/starryMargin10"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/meet_red_point"
                    android:layout_width="@dimen/starryMargin10"
                    android:layout_height="@dimen/starryMargin10"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="@dimen/dp_5"
                    android:src="@drawable/starry_btn_rec_5_bg_with_red"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/message"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text=""
                    android:textColor="@color/black_22"
                    android:textSize="16sp"
                    tools:text="内容" />

            </LinearLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/starry_meet_input_rl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:background="@color/starry_white_bg"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/starryMargin20"
            android:paddingTop="@dimen/starryMargin10"
            android:paddingEnd="@dimen/starryMargin20"
            android:paddingBottom="@dimen/starryMargin10">

            <RelativeLayout
                android:id="@+id/starry_meet_input_rl11"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_toStartOf="@+id/starry_pwd_show"
                android:gravity="center_vertical">

                <EditText
                    android:id="@+id/starry_meet_edit_et"
                    android:layout_width="match_parent"
                    android:layout_height="36dp"
                    android:layout_marginTop="@dimen/dp_5"
                    android:layout_marginBottom="@dimen/dp_5"
                    android:background="@drawable/starry_rec_5_bg_with_gray"
                    android:digits="1234567890 "
                    android:inputType="numberDecimal"
                    android:paddingStart="@dimen/dp10"
                    android:paddingEnd="@dimen/dp10"
                    android:singleLine="true"
                    android:textAlignment="center"
                    android:textColor="@color/black_22"
                    android:textColorHint="@color/starry_title_gray"
                    android:textCursorDrawable="@drawable/edittext_cursor_blue"
                    android:textSize="14sp"
                    android:visibility="visible" />

                <ImageView
                    android:id="@+id/starry_meet_del_iv"
                    android:layout_width="@dimen/starryMargin30"
                    android:layout_height="@dimen/starryMargin30"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:padding="8dp"
                    android:scaleType="fitXY"
                    android:src="@mipmap/tag_delete_icon"
                    android:visibility="gone" />

            </RelativeLayout>

            <ImageView
                android:id="@+id/starry_pwd_show"
                android:layout_width="@dimen/starryMargin30"
                android:layout_height="36dp"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="@dimen/dp_5"
                android:layout_marginBottom="@dimen/dp_5"
                android:paddingStart="@dimen/dp10"
                android:paddingTop="13dp"
                android:paddingEnd="1dp"
                android:paddingBottom="13dp"
                android:scaleType="fitXY"
                android:src="@drawable/selector_starry_pwd"
                android:textSize="@dimen/starryTopBtnSize"
                android:visibility="gone" />

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/gray_e5" />
        <!-- button -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:orientation="horizontal">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/negative_button"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:textColor="@color/title_black_color"
                android:textSize="14sp"
                tools:text="取消" />

            <View
                android:layout_width="0.5dp"
                android:layout_height="match_parent"
                android:background="@color/gray_e5" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/positive_button"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text=""
                android:textColor="@color/blue_positive_button"
                android:textSize="14sp"
                tools:text="确定" />
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>