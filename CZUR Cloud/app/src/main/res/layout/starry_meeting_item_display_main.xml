<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="false"
    >
    <!--  会议画面的容器  -->
    <com.czur.cloud.ui.starry.meeting.widget.container.VideoContainer
        android:id="@+id/displayItemContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/starry_meeting_small_video_bg"
        />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/smallGroup"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible"
        >

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/smallVideoButtomLayer"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/starryMargin24"
            android:padding="3dp"
            android:background="@color/starry_meeting_small_video_bottom_bar_bg"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginBottom="@dimen/starryMargin10">

            <com.czur.cloud.ui.starry.meeting.widget.VolumeView
                android:id="@+id/volumeView"
                android:layout_width="@dimen/starry_meeting_big_video_volume_width"
                android:layout_height="@dimen/starry_meeting_big_video_volume_width"
                android:layout_marginStart="2dp"
                android:visibility="gone"
                android:src="@mipmap/starry_meeting_mic_on"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toEndOf="@+id/volumeView"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_default="wrap"
                android:id="@+id/itemNickNameTvll">
                <TextView
                    android:id="@+id/itemNickNameTv"
                    android:layout_width="wrap_content"
                    android:layout_height="20dp"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:lines="1"
                    android:textColor="@color/white"
                    android:textSize="@dimen/starry_meeting_big_video_fontsize"
                    tools:text="Main Bill" />
            </LinearLayout>

            <com.czur.cloud.ui.starry.meeting.widget.NetworkQualityView
                android:id="@+id/itemNetworkQualityIV"
                android:layout_width="@dimen/starry_meeting_big_video_volume_width"
                android:layout_height="@dimen/starry_meeting_big_video_volume_width"
                android:layout_marginStart="@dimen/dp_5"
                android:layout_marginEnd="@dimen/dp_5"
                android:gravity="center_vertical"
                android:src="@null"
                app:layout_constraintLeft_toRightOf="@id/itemNickNameTvll"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.czur.cloud.ui.starry.meeting.widget.HeadImageView
            android:id="@+id/smallHeadIv"
            android:layout_width="@dimen/starry_meeting_grid_video_width"
            android:layout_height="@dimen/starry_meeting_grid_video_width"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:roundPercent="1"
            android:visibility="gone"
            />

        <TextView
            android:id="@+id/itemNickNameTv2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5"
            android:paddingStart="@dimen/starryMargin20"
            android:paddingEnd="@dimen/starryMargin20"
            android:textColor="@color/white"
            android:textSize="@dimen/starry_meeting_big_video_name_fontsize"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintTop_toBottomOf="@id/smallHeadIv"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            tools:text="艾华" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>