<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/floatOutSide"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:focusable="true">

    <View
        android:id="@+id/floatDarkBg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/float_out_color_dark"
        android:clickable="false"
        android:focusable="false"
        android:visibility="gone"/>

    <View
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal" />
</FrameLayout>