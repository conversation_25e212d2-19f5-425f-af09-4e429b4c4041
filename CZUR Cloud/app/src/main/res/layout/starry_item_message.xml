<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/starry_item_message_rl"
    android:layout_width="match_parent"
    android:background="@color/white"
    android:gravity="center_vertical"
    android:layout_height="@dimen/starryMargin60">

    <View
        android:id="@+id/msg_view"
        android:layout_width="@dimen/starryMargin25"
        android:layout_height="@dimen/starryMargin25"
        android:layout_centerVertical="true"
        android:layout_gravity="center"
        android:layout_marginStart="@dimen/starryMargin15"
        android:padding="@dimen/starryMargin8"
        android:visibility="invisible" />

    <ImageView
        android:id="@+id/msg_red_point"
        android:layout_width="@dimen/starryMargin25"
        android:layout_height="@dimen/starryMargin25"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/starryMargin15"
        android:padding="@dimen/starryMargin8"
        android:layout_gravity="center"
        android:src="@drawable/circle_red"
        android:visibility="gone"/>

    <CheckBox
        android:id="@+id/starry_msg_check"
        style="@style/StarryMsgCheckBox"
        android:layout_width="@dimen/starryMargin20"
        android:layout_height="@dimen/starryMargin20"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/starryMargin15"
        android:layout_gravity="center"
        android:padding="@dimen/starryMargin10"
        android:visibility="visible"/>

    <TextView
        android:id="@+id/starry_item_msg_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_gravity="center"
        android:layout_marginStart="@dimen/starryMargin10"
        android:layout_marginEnd="@dimen/starryMargin10"
        android:layout_toStartOf="@+id/starry_item_msg_date"
        android:layout_toEndOf="@+id/msg_view"
        android:ellipsize="end"
        android:maxLines="1"
        android:text=""
        android:textColor="@color/title_black_color"
        android:textSize="@dimen/starryCompanySubTitle"
        tools:text="成者科技" />

    <TextView
        android:id="@+id/starry_item_msg_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="@dimen/starryMargin15"
        android:text=""
        android:maxLines="1"
        android:layout_gravity="center"
        android:textColor="@color/starry_home_text_gray_color"
        android:textSize="@dimen/starryAccountNo" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/starry_list_liner_color"
        android:layout_marginStart="@dimen/starryMargin50"
        android:layout_alignParentBottom="true"/>
</RelativeLayout>