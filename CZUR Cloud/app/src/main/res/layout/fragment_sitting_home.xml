<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gary_f9">

    <com.lsjwzh.widget.recyclerviewpager.RecyclerViewPager
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:descendantFocusability="afterDescendants"
        android:overScrollMode="never"
        app:fastScrollEnabled="false"
        app:rvp_flingFactor="1"
        app:rvp_singlePageFling="true"
        app:rvp_triggerOffset="1" />

    <include
        layout="@layout/layout_no_network"
        android:visibility="gone" />

</RelativeLayout>