<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="45dp"
    android:background="@color/gary_f9"
    android:orientation="vertical">


    <ImageView
        android:id="@+id/item_et_move_folder_img"
        android:layout_width="18dp"
        android:layout_height="16dp"
        android:layout_marginLeft="15dp"
        android:src="@mipmap/et_move_folder_icon"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/item_et_move_folder_name_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:textColor="@color/black_24"
        android:textSize="15sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/item_et_move_folder_img"
        app:layout_constraintTop_toTopOf="parent" />
<View
    android:layout_width="match_parent"
    android:layout_height="0.5dp"
    app:layout_constraintBottom_toBottomOf="parent"
    android:background="@color/gray_e5"/>

</androidx.constraintlayout.widget.ConstraintLayout>

