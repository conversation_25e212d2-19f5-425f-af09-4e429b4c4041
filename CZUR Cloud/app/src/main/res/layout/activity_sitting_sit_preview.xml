<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:fitsSystemWindows="true"
    android:layout_height="match_parent">

    <View
        android:id="@+id/aura_home_preview_top_bar_bg"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/black_2a"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/aura_home_preview_back_btn"
        android:layout_width="29.5dp"
        android:layout_height="0dp"
        android:layout_marginStart="9dp"
        android:padding="10dp"
        android:src="@mipmap/white_back_icon"
        app:layout_constraintBottom_toBottomOf="@+id/aura_home_preview_top_bar_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/aura_home_preview_top_bar_bg" />

    <TextView
        android:id="@+id/aura_home_preview_title_1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="15sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/aura_home_preview_title_2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/aura_home_preview_title_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/aura_home_preview_top_bar_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/aura_home_preview_title_1" />

    <ImageView
        android:id="@+id/aura_home_preview_more_btn"
        android:layout_width="40dp"
        android:layout_height="0dp"
        android:layout_marginEnd="9dp"
        android:padding="10dp"
        android:src="@mipmap/et_white_more"
        app:layout_constraintBottom_toBottomOf="@+id/aura_home_preview_top_bar_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/aura_home_preview_top_bar_bg" />


    <View
        android:id="@+id/aura_home_preview_bottom_bar_bg"
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:background="@color/black_2a"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <ImageView
        android:id="@+id/aura_home_preview_save_img"
        android:layout_width="22.5dp"
        android:layout_height="22.5dp"
        android:src="@mipmap/book_download"
        app:layout_constraintBottom_toTopOf="@+id/aura_home_preview_save_tv"
        app:layout_constraintLeft_toLeftOf="@+id/guideline2"
        app:layout_constraintRight_toRightOf="@+id/guideline3"
        app:layout_constraintTop_toTopOf="@+id/aura_home_preview_bottom_bar_bg"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/aura_home_preview_save_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6.5dp"
        android:text="@string/save_to_album"
        android:textColor="@color/white"
        android:textSize="9sp"
        app:layout_constraintBottom_toBottomOf="@+id/aura_home_preview_bottom_bar_bg"
        app:layout_constraintLeft_toLeftOf="@+id/aura_home_preview_save_img"
        app:layout_constraintRight_toRightOf="@+id/aura_home_preview_save_img"
        app:layout_constraintTop_toBottomOf="@+id/aura_home_preview_save_img" />

    <View
        android:id="@+id/aura_home_preview_save_btn"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@+id/guideline2"
        app:layout_constraintRight_toRightOf="@+id/guideline3"
        app:layout_constraintTop_toTopOf="@+id/aura_home_preview_bottom_bar_bg" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/aura_home_preview_viewpager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/black"
        app:layout_constraintBottom_toTopOf="@+id/aura_home_preview_bottom_bar_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/aura_home_preview_top_bar_bg" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.2" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.4" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.6" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline4"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.8" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/aura_original_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="aura_home_preview_original_tv" />

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/aura_home_preview_original_tv"
        android:layout_width="wrap_content"
        android:layout_height="25dp"
        android:background="@drawable/btn_rec_5_bg_with_stroke_5e"
        android:textColor="@color/white"
        android:textSize="11sp"
        android:gravity="center"
        android:minWidth="110dp"
        android:paddingLeft="5dp"
        android:paddingRight="5dp"
        android:singleLine="true"
        android:layout_marginBottom="30dp"
        app:layout_constraintBottom_toTopOf="@+id/aura_home_preview_bottom_bar_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        android:visibility="gone"/>


</androidx.constraintlayout.widget.ConstraintLayout>

