<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="250dp"
    android:layout_height="wrap_content"
    android:background="@drawable/btn_rec_10_bg_with_white"
    android:orientation="vertical">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="70dp">

        <ImageView
            android:id="@+id/img"
            android:layout_width="43dp"
            android:layout_height="43dp"
            android:layout_alignParentRight="true"
            android:layout_centerHorizontal="true"
            android:padding="15dp"
            android:src="@mipmap/aura_home_close_icon" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/img"
            android:layout_alignParentTop="true"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="20dp"
            android:text="@string/choose_user"
            android:textColor="@color/black_22"
            android:textSize="20sp" />


    </RelativeLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/aura_home_recyclerView"
        android:layout_width="match_parent"
        android:layout_height="250dp"
        android:layout_marginBottom="42dp"
        android:background="@color/white"
        android:overScrollMode="never"/>

</LinearLayout>
