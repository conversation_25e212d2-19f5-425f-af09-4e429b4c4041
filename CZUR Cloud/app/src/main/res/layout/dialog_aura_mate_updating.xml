<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="MissingPrefix">

    <LinearLayout
        android:layout_width="243dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:background="@drawable/btn_rec_8_bg_with_white"
        android:gravity="center_horizontal"
        android:orientation="vertical">


        <LinearLayout
            android:layout_marginTop="15dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <ImageView
                android:layout_width="55dp"
                android:layout_height="55dp"
                android:layout_gravity="center_horizontal"
                android:src="@mipmap/update_state_working" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:gravity="center"
                android:lineSpacingExtra="3dp"
                android:text="@string/at_updating"
                android:textColor="@color/black_22"
                android:textSize="15sp" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:gravity="center"
                android:lineSpacingExtra="3dp"
                android:text="@string/at_updating_prompt"
                android:textColor="@color/gray_bb"
                android:textSize="13sp" />

        </LinearLayout>


        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/btn_i_konw"
            android:layout_width="161dp"
            android:layout_height="36dp"
            android:layout_marginTop="40dp"
            android:layout_marginBottom="15dp"
            android:background="@drawable/btn_rec_8_bg_with_blue_aura_home"
            android:gravity="center"
            android:text="@string/handwriting_confirm_text"
            android:textColor="@color/white"
            android:textSize="15sp" />
    </LinearLayout>
</RelativeLayout>