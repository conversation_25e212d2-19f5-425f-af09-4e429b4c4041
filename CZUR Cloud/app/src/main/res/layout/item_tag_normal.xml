<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    tools:ignore="MissingPrefix">
    <RelativeLayout
        android:background="@drawable/btn_rect_6_bg_gray_ec"
        android:layout_centerInParent="true"
        android:layout_marginBottom="13dp"
        android:layout_marginRight="7.5dp"
        android:layout_marginLeft="7.5dp"
        android:id="@+id/tag_inner_item"
        android:layout_gravity="center"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="40dp">
        <ImageView
            android:layout_marginLeft="9dp"
            android:layout_width="10dp"
            android:src="@mipmap/red_tag_icon"
            android:layout_height="30dp" />

        <TextView
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:id="@+id/tag_item_name"
            android:textColor="@color/black_22"
            android:layout_centerInParent="true"
            android:textStyle="bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />



    </RelativeLayout>
    <ImageView
        android:layout_marginRight="3dp"
        android:layout_marginTop="3dp"
        android:id="@+id/tag_delete_btn"
        android:layout_alignParentRight="true"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:paddingLeft="15dp"
        android:paddingBottom="15dp"
        android:src="@mipmap/tag_delete_icon"/>



</RelativeLayout>