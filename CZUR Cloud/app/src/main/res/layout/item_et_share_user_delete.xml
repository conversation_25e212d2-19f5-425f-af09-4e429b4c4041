<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:layout_width="40.5dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginBottom="12.5dp"
        android:layout_marginTop="8.5dp"
        android:orientation="vertical">
        <ImageView
            android:layout_width="40.5dp"
            android:src="@mipmap/share_user_delete"
            android:layout_height="40.5dp"/>

        <TextView
            android:text="123"
            android:layout_gravity="center_horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="7.5dp"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:textStyle="bold" />
    </LinearLayout>



</RelativeLayout>