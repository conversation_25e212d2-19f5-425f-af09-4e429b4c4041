<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/black">

    <RelativeLayout
        android:id="@+id/layout_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="45dp"
        android:background="@color/black_2a">

        <ImageView
            android:id="@+id/img_back"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_centerVertical="true"
            android:contentDescription="@null"
            android:paddingBottom="10dp"
            android:paddingEnd="15dp"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:paddingStart="15dp"
            android:paddingTop="10dp"
            android:src="@mipmap/white_back_icon" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginRight="10dp"
            android:text="@string/crop_image_ocr"
            android:textColor="@color/white"
            android:textSize="15sp" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/et_ocr_choose_btn"
            android:layout_width="110dp"
            android:layout_height="40dp"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp"
            android:gravity="end|center_vertical"
            android:paddingEnd="10dp"
            android:paddingLeft="0dp"
            android:paddingRight="10dp"
            android:paddingStart="0dp"
            android:text="@string/crop_image_choose_language"
            android:textColor="@color/white"
            android:textSize="15sp" />
    </RelativeLayout>


    <!-- Image Cropper fill the remaining available height -->
    <com.czur.cloud.ui.component.cropper.CropImageView
        android:layout_marginBottom="108dp"
        android:id="@+id/cropImageView"
        android:layout_marginTop="65dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/layout_title"
        android:background="@color/black"
        app:isCrop="true"
        app:cropBorderCornerColor="@color/transparent"
        app:cropBorderLineColor="@color/gray_e5"
        app:cropBorderLineThickness="0dp"
        app:cropTouchRadius="100dp"
        app:cropGuidelines="on" />

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/layout_title"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="25dp"
        android:text="@string/et_ocr_prompt"
        android:textColor="@color/white"
        android:textSize="15sp" />

    <com.czur.cloud.ui.component.ProgressButton
        android:id="@+id/et_ocr_btn"
        android:layout_width="250dp"
        android:layout_height="40dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="30dp"
        android:background="@drawable/btn_rec_5_bg_with_code_blue"
        android:text="@string/ocr"
        app:progress_btn_tv="@string/ocr" />



</RelativeLayout>