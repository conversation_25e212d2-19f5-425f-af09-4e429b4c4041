<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/white">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/white">

        <ImageView
            android:id="@+id/img_back"
            android:layout_width="29.5dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="10dp"
            android:padding="10dp"
            android:src="@mipmap/login_back_icon" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:orientation="vertical"
        android:layout_centerInParent="true"
        android:layout_height="wrap_content">
        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_gravity="center_horizontal"
            android:src="@mipmap/wifi_error"/>
        <com.czur.cloud.ui.component.MediumBoldTextView
            android:layout_width="wrap_content"
            android:layout_marginTop="20dp"
            android:textSize="13sp"
            android:layout_gravity="center_horizontal"
            android:text="@string/wifi_connect_fail"
            android:layout_height="wrap_content"/>

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:layout_width="wrap_content"
            android:layout_marginTop="25dp"
            android:textSize="13sp"
            android:gravity="center_horizontal"
            android:layout_gravity="center_horizontal"
            android:text="@string/wifi_connect_fail_reason"
            android:lineSpacingExtra="3dp"
            android:layout_height="wrap_content"/>

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/tv_dial"
            android:layout_width="wrap_content"
            android:layout_marginTop="25dp"
            android:textSize="13sp"
            android:textColor="@color/blue_29b0d7"
            android:gravity="center_horizontal"
            android:layout_gravity="center_horizontal"
            android:text="@string/wifi_dial"
            android:lineSpacingExtra="3dp"
            android:layout_height="wrap_content"/>
    </LinearLayout>
    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/tv_reconnect"
        android:layout_width="250dp"
        android:layout_height="40dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="20dp"
        android:background="@drawable/btn_rec_5_bg_with_code_blue"
        android:gravity="center"
        android:foreground="?selectableItemBackground"
        android:text="@string/wifi_reconnect"
        android:textColor="@color/white" />
</RelativeLayout>