<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:fitsSystemWindows="true"
    android:layout_height="match_parent">

    <LinearLayout
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:background="@drawable/btn_rec_5_bg_with_white"
        android:gravity="center_horizontal"
        android:orientation="vertical">


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:layout_marginLeft="29dp"
            android:layout_marginRight="29dp">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/add_tag"
                android:textColor="@color/black_22"
                android:textSize="15sp" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/message"
                android:layout_marginTop="12.5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/title"
                android:gravity="center"
                android:text=""
                android:textColor="@color/black_22"
                android:textSize="12sp" />


        </RelativeLayout>

        <com.czur.cloud.ui.component.NoHintEditText
            android:id="@+id/edt"
            android:gravity="center"
            android:singleLine="true"
            android:layout_width="wrap_content"
            android:textColor="@color/black_22"
            android:textColorHint="@color/gary_c4"
            android:textSize="14sp"
            android:paddingLeft="5dp"
            android:paddingRight="5dp"
            android:minWidth="210dp"
            android:maxLines="1"
            android:hint="@string/add_tag_hint"
            android:background="@drawable/btn_rec_5_bg_with_gray_ef"
            android:layout_marginBottom="40dp"
            android:layout_height="40dp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/gray_e5" />
        <!-- button -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:orientation="horizontal">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/negative_button"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:textColor="@color/black_22"
                android:textSize="14sp" />

            <View
                android:layout_width="0.5dp"
                android:layout_height="match_parent"
                android:background="@color/gray_e5"/>

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/positive_button"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text=""
                android:textColor="@color/blue_positive_button"
                android:textSize="14sp" />
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>