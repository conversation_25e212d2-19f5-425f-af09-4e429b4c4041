<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:background="@color/white">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true">

        <TextView
            android:id="@+id/contact_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginEnd="@dimen/starryMargin24"
            android:ellipsize="end"
            android:maxLines="1"
            android:text=""
            android:textColor="@color/starry_text_title_color_black"
            android:textSize="@dimen/starryCompanySubTitle"
            tools:text="item内容" />

        <TextView
            android:id="@+id/second_line_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/contact_name"
            android:layout_gravity="center"
            android:layout_marginEnd="@dimen/starryMargin24"
            android:ellipsize="end"
            android:maxLines="1"
            android:text=""
            android:textColor="@color/starry_text_title_color_black"
            android:textSize="@dimen/starryAccountNo"
            tools:text="item内容" />
    </RelativeLayout>


    <CheckBox
        android:id="@+id/starry_contacts_check"
        style="@style/StarryMsgCheckBox"
        android:layout_width="@dimen/starryMargin20"
        android:layout_height="@dimen/starryMargin20"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:minHeight="@dimen/jingMargin20"
        android:padding="@dimen/starryMargin8"
        android:visibility="visible" />

    <TextView
        android:id="@+id/starry_contacts_delete"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:text="@string/starry_account_delete"
        android:textColor="@color/starry_delete_red"
        android:textSize="@dimen/starryCompanySubTitle"
        android:visibility="gone"
        />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_alignParentBottom="true"
        android:background="@color/starry_list_liner_color" />

</RelativeLayout>
