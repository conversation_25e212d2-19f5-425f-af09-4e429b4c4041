<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:fitsSystemWindows="true"
    android:background="@color/starry_comm_gray_bg">

    <include
        android:id="@+id/starry_layout_top_bar_rl"
        layout="@layout/starry_layout_top_bar" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/starry_layout_top_bar_rl"
        android:gravity="center_horizontal"
        android:id="@+id/detailView"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/meeting_title_ll"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin120"
            android:background="@color/starry_white_bg"
            android:paddingStart="@dimen/starryMargin20"
            android:orientation="horizontal"
            android:paddingEnd="@dimen/starryMargin20">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:gravity="center"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/meeting_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:text=""
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:textColor="@color/starry_title_value"
                    android:textSize="@dimen/starryCompanySubTitle"
                    tools:text="会议标题" />

                <RelativeLayout
                    android:id="@+id/starry_meeting_info_rl"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/starryMargin8"
                    >

                    <TextView
                        android:id="@+id/meeting_longtime"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/starryMargin30"
                        android:alpha="0.9"
                        android:gravity="center_vertical"
                        android:text=""
                        android:textColor="@color/starry_title_value"
                        android:textSize="@dimen/starryCompanyBtnTitle"
                        tools:text="5人 00:28:10" />

                    <TextView
                        android:id="@+id/starry_meeting_code_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/meeting_longtime"
                        android:layout_marginTop="@dimen/dp_5"
                        android:text="@string/starry_meeting_code_title"
                        android:textColor="@color/title_black_color"
                        android:textSize="@dimen/starryCompanyBtnTitle" />

                    <ImageView
                        android:id="@+id/starry_meetingcode_copy"
                        android:layout_width="@dimen/starryMargin40"
                        android:layout_height="@dimen/starryMargin40"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="@dimen/starryMargin5"
                        android:layout_toEndOf="@+id/starry_meeting_code_title"
                        android:padding="@dimen/starryMargin8"
                        android:src="@mipmap/starry_meetingcode_copy"
                        android:visibility="invisible"/>

                </RelativeLayout>

                <TextView
                    android:id="@+id/meeting_doing"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_5"
                    android:gravity="center_vertical"
                    android:text="@string/starry_meeting_msg_doing"
                    android:textColor="@color/starry_text_color_red"
                    android:textSize="@dimen/starryCompanyBtnTitle" />
            </LinearLayout>

            <TextView
                android:id="@+id/meeting_starttime"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical|end"
                android:maxLines="2"
                android:text=""
                android:layout_marginStart="@dimen/dp_5"
                android:textColor="@color/starry_text_color_gray"
                android:textSize="@dimen/starryCompanySubValue"

                tools:text="10:08\r\n今天" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginStart="@dimen/starryMargin15"
            android:background="@color/starry_list_liner_color" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_member_list"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="@color/starry_white_bg"
            android:visibility="visible" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin30" />

        <com.czur.cloud.ui.starry.component.DrawableCenterOneLineTextView
            android:id="@+id/meeting_join_btn"
            style="@style/starry_contact_detail_btn_style"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/starryMargin20"
            android:layout_marginEnd="@dimen/starryMargin20"
            android:drawableStart="@mipmap/starry_call_btn_logo"
            android:drawablePadding="@dimen/dp10"
            android:text="@string/starry_recently_join_btn"
            android:visibility="visible"
            android:lines="1"/>

        <com.czur.cloud.ui.starry.component.DrawableCenterOneLineTextView
            android:id="@+id/meeting_restart_btn"
            style="@style/starry_contact_detail_btn_style"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/starryMargin20"
            android:layout_marginEnd="@dimen/starryMargin20"
            android:drawableStart="@mipmap/starry_call_btn_logo"
            android:drawablePadding="@dimen/dp10"
            android:text="@string/starry_new_call_btn"
            android:visibility="visible"
            android:lines="1" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin30" />

    </LinearLayout>


</RelativeLayout>

