<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="45dp">

    <TextView
        android:id="@+id/tv_item"
        android:layout_width="wrap_content"
        android:textSize="15sp"
        android:layout_marginLeft="25dp"
        android:layout_centerVertical="true"
        android:textColor="@drawable/selector_dialog_item_text"
        android:layout_height="wrap_content"/>

    <ImageView
        android:id="@+id/img_check"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginLeft="15dp"
        android:layout_toRightOf="@+id/tv_item"
        android:layout_centerVertical="true"
        android:visibility="gone"
        android:src="@mipmap/aura_home_big_right"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginLeft="25dp"
        android:layout_marginRight="25dp"
        android:background="@color/gray_f7"
        android:layout_alignParentBottom="true"/>

</RelativeLayout>