<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="243dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:background="@drawable/btn_rec_5_bg_with_white"
        android:gravity="center_horizontal"
        android:orientation="vertical">


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            android:layout_marginLeft="29dp"
            android:layout_marginRight="29dp"
            android:layout_marginTop="32dp">

            <TextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text=""
                android:textColor="@color/black_22"
                android:textSize="16sp"
                android:textStyle="bold" />


        </RelativeLayout>


        <com.czur.cloud.ui.component.progressbar.RoundedRectProgressBar
            android:id="@+id/progress"
            android:layout_marginBottom="32dp"
            android:layout_marginLeft="18.5dp"
            android:layout_marginRight="18.5dp"
            android:layout_width="match_parent"
            android:layout_height="2dp"
            app:backColor="@color/gary_ef"
            app:barColor="@color/black_22" />
    </LinearLayout>
</RelativeLayout>