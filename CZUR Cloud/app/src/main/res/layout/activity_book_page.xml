<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/white">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/bookshelf_inside_top_bar"
            android:background="@color/gary_f9">


            <RelativeLayout
                android:id="@+id/book_page_empty_rl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/gary_f9"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="120dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:adjustViewBounds="true"
                        android:scaleType="fitXY"
                        android:src="@mipmap/book_page_no_page_icon" />

                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="15.5dp"
                        android:text="@string/none_file_book_page"
                        android:textColor="@color/gary_c4"
                        android:textSize="15sp" />
                </LinearLayout>


            </RelativeLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/book_page_recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginLeft="7.5dp"
                android:layout_marginRight="7.5dp"
                android:overScrollMode="never"
                android:visibility="gone">

            </androidx.recyclerview.widget.RecyclerView>

            <RelativeLayout
                android:id="@+id/book_page_add_rl"
                android:layout_width="79.5dp"
                android:layout_height="79.5dp"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                android:layout_marginRight="8dp"
                android:layout_marginBottom="20.5dp">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@mipmap/book_page_add_shadow" />

                <ImageView
                    android:id="@+id/book_page_add_btn"
                    android:layout_width="59.5dp"
                    android:layout_height="59.5dp"
                    android:layout_centerInParent="true"
                    android:background="@mipmap/book_page_add_icon" />


            </RelativeLayout>

        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/bookshelf_inside_top_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/gary_f9"
            android:minHeight="45dp">

            <LinearLayout
                android:id="@+id/book_page_unselected_left_top_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/book_page_back_btn"
                    android:layout_width="29.5dp"
                    android:layout_height="36dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="9dp"
                    android:padding="10dp"
                    android:src="@mipmap/login_back_icon" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/book_page_folder_name_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="13dp"
                    android:layout_marginRight="30dp"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:textColor="@color/account_title"
                    android:textSize="18sp" />

            </LinearLayout>


            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/books_page_select_all_btn"
                android:layout_width="110dp"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:paddingLeft="18.5dp"
                android:layout_centerVertical="true"
                android:textColor="@color/black_22"
                android:textSize="16sp"
                android:visibility="gone" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/book_page_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:textColor="@color/account_title"
                android:textSize="18sp" />


            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/book_page_cancel_btn"
                android:layout_width="110dp"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:gravity="center_vertical|end"
                android:layout_centerVertical="true"
                android:paddingRight="18.5dp"
                android:textColor="@color/black_22"
                android:textSize="16sp"
                android:visibility="gone" />

            <RelativeLayout
                android:id="@+id/books_page_unselected_right_top_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true">

                <ImageView
                    android:id="@+id/book_page_title_img"
                    android:layout_width="53dp"
                    android:layout_height="44dp"
                    android:src="@mipmap/title_end" />

                <RelativeLayout
                    android:id="@+id/bookshelf_sort_btn"
                    android:layout_width="46dp"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_toRightOf="@+id/book_page_multi_select_btn"
                    android:background="@color/gary_f9">

                    <ImageView
                        android:layout_width="16.5dp"
                        android:layout_height="20dp"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="18.5dp"
                        android:background="@mipmap/book_page_sort_icon" />

                </RelativeLayout>


                <RelativeLayout
                    android:id="@+id/book_page_multi_select_btn"
                    android:layout_width="59.5dp"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_toRightOf="@+id/book_page_title_img"
                    android:background="@color/gary_f9">

                    <ImageView
                        android:layout_width="22.5dp"
                        android:layout_height="19.5dp"
                        android:layout_alignParentRight="true"
                        android:layout_centerInParent="true"
                        android:layout_marginRight="10dp"
                        android:background="@mipmap/multi_select_icon" />

                </RelativeLayout>

            </RelativeLayout>
        </RelativeLayout>


        <ImageView
            android:id="@+id/bookshelf_page_guide_img"
            android:layout_width="12dp"
            android:layout_height="9dp"
            android:layout_alignParentRight="true"
            android:layout_marginTop="33dp"
            android:layout_marginRight="60dp"
            android:src="@drawable/vector_drawable_guide_triangle" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/bookshelf_page_guide_tv"
            android:layout_width="130dp"
            android:layout_height="wrap_content"
            android:layout_below="@+id/bookshelf_page_guide_img"
            android:layout_alignParentRight="true"
            android:layout_marginRight="50dp"
            android:background="@drawable/btn_rec_5_bg_with_code_blue_without_bottom"
            android:lineSpacingExtra="4dp"
            android:paddingLeft="12dp"
            android:paddingTop="10dp"
            android:paddingRight="12dp"
            android:paddingBottom="26dp"
            android:text="@string/page_prompt"
            android:textColor="@color/white"
            android:textSize="12sp" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/bookshelf_page_hide_tv"
            android:layout_width="130dp"
            android:layout_height="wrap_content"
            android:layout_below="@+id/bookshelf_page_guide_tv"
            android:layout_alignParentRight="true"
            android:layout_marginRight="50dp"
            android:background="@drawable/btn_rec_5_bg_with_code_blue_without_top"
            android:gravity="center"
            android:paddingBottom="5dp"
            android:text="@string/not_show"
            android:textColor="@color/blue_86e2fd"
            android:textSize="12sp" />

        <!--<View-->
        <!--android:id="@+id/line"-->
        <!--android:layout_width="match_parent"-->
        <!--android:layout_height="0.5dp"-->
        <!--android:layout_above="@+id/book_page_bottom_ll"-->
        <!--android:background="@color/gary_e1" />-->

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="49dp"
            android:layout_alignParentBottom="true"
            android:visibility="gone">

            <RelativeLayout
                android:id="@+id/book_page_all_page_rl"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@color/gary_f9">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true">

                    <ImageView
                        android:id="@+id/book_page_all_page_img"
                        android:layout_width="22.5dp"
                        android:layout_height="22.5dp"
                        android:layout_centerHorizontal="true"
                        android:background="@drawable/selector_book_page_note" />

                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/book_page_all_page_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/book_page_all_page_img"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="6.5dp"
                        android:text="@string/book_page"
                        android:textSize="9sp" />
                </RelativeLayout>

            </RelativeLayout>


            <RelativeLayout
                android:id="@+id/book_page_star_rl"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@color/gary_f9">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true">

                    <ImageView
                        android:id="@+id/book_page_star_img"
                        android:layout_width="22.5dp"
                        android:layout_height="22.5dp"
                        android:layout_centerHorizontal="true"
                        android:background="@drawable/selector_book_page_star" />

                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/book_page_star_page_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/book_page_star_img"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="6.5dp"
                        android:text="@string/star"
                        android:textSize="9sp" />
                </RelativeLayout>

            </RelativeLayout>


        </LinearLayout>

    </RelativeLayout>


    <LinearLayout
        android:id="@+id/book_page_hide_bottom_ll"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_alignParentBottom="true"
        android:background="@color/black"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:visibility="gone">

        <RelativeLayout
            android:id="@+id/book_page_save_album_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/book_page_save_album_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_save_icon" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/book_page_save_album_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/book_page_save_album_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="3dp"
                    android:gravity="center"
                    android:text="@string/save_to_album"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/book_page_pdf_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/book_page_pdf_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_pdf_icon" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/book_page_pdf_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/book_page_pdf_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="5dp"
                    android:gravity="center"
                    android:text="@string/PDF"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/book_page_file_star_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:visibility="gone">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/page_preview_star_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_star_icon" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/page_preview_star_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/page_preview_star_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/star_page"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/book_page_file_tag_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/page_preview_tag_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_book_tag_icon" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/page_preview_tag_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/page_preview_tag_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="5dp"
                    android:gravity="center"
                    android:text="@string/edit_tag"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/book_page_delete_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/book_page_delete_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_delete_icon" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/book_page_delete_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/book_page_delete_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="5dp"
                    android:gravity="center"
                    android:text="@string/delete"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>


    </LinearLayout>
</RelativeLayout>

