<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/white">

        <ImageView
            android:id="@+id/img_back"
            android:layout_width="29.5dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="10dp"
            android:padding="10dp"
            android:src="@mipmap/login_back_icon" />
    </RelativeLayout>

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="60dp"
        android:layout_marginTop="20dp"
        android:text="@string/my_net"
        android:textColor="@color/black_22"
        android:textSize="22sp" />

    <com.czur.cloud.ui.component.recyclerview.MaxHeightRecyclerView
        android:id="@+id/rv_wifi"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="60dp"
        android:layout_marginTop="25dp"
        android:layout_marginRight="60dp"
        android:background="@drawable/rv_rec_5_gray"
        app:maxHeight="300dp" />

    <RelativeLayout
        android:id="@+id/rl_input_by_hand"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_marginLeft="60dp"
        android:layout_marginTop="15dp"
        android:layout_marginRight="60dp"
        android:background="@drawable/btn_rec_5_gray_bg">

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="14dp"
            android:text="@string/input_by_hand"
            android:textColor="@color/black_22"
            android:textSize="15sp" />

        <ImageView
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="14sp"
            android:src="@mipmap/wifi_add" />
    </RelativeLayout>


</LinearLayout>