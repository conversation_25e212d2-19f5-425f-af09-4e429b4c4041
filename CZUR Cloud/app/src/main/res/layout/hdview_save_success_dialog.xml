<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="205dp"
        android:layout_height="205dp"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:background="@drawable/btn_rec_5_bg_with_white"
        android:gravity="center"
        android:orientation="vertical">
        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <ImageView
                android:id="@+id/img"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_centerHorizontal="true"
                android:scaleType="fitCenter"
                android:src="@mipmap/save_right" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/tv1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/img"
                android:layout_marginTop="14dp"
                android:gravity="center"
                android:text="@string/save_success1"
                android:textColor="@color/black_22"
                android:textSize="15sp" />
            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/tv2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv1"
                android:layout_centerInParent="true"
                android:layout_marginTop="5dp"
                android:gravity="center"
                android:text="@string/save_success2"
                android:textColor="@color/gray_bb"
                android:textSize="12sp" />
        </RelativeLayout>
    </LinearLayout>
</RelativeLayout>