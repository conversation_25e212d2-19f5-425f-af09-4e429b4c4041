<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/blue_29b0d7">

    <RelativeLayout
        android:id="@+id/handwriting_guide_top_bar"
        android:layout_width="match_parent"
        android:layout_height="44dp">


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/how_to_get_handwriting_code"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

    </RelativeLayout>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/handwriting_guide_top_bar"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_centerHorizontal="true"
            android:id="@+id/handwriting_guide_rl"
            android:layout_width="250dp"
            android:layout_height="280dp"
            android:layout_marginTop="15.5dp"
            android:background="@color/blue_29b0d7">

            <ImageView
                android:layout_centerInParent="true"
                android:id="@+id/handwriting_unused_icon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@mipmap/handwriting_cover_icon" />


        </RelativeLayout>
        <TextView
            android:layout_below="@+id/handwriting_guide_rl"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="27.5dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="78.5dp"
            android:gravity="center"
            android:text="@string/handwriting_guide_text"
            android:textColor="@color/white"
            android:textSize="15sp"
            android:textStyle="bold" />

        <TextView
            android:layout_alignParentBottom="true"
            android:id="@+id/handwriting_confirm_btn"
            android:layout_centerHorizontal="true"
            android:layout_width="250dp"
            android:layout_height="40dp"
            android:layout_marginBottom="78.5dp"
            android:background="@drawable/btn_rec_5_bg_with_white"
            android:gravity="center"
            android:text="@string/handwriting_confirm_text"
            android:textColor="@color/blue_29b0d7"
            android:textSize="15sp"
            android:textStyle="bold" />
    </RelativeLayout>


</RelativeLayout>