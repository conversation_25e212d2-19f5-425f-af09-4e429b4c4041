<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gary_f9"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <include
        android:id="@+id/wifi_retry_top_bar"
        layout="@layout/layout_normal_top_bar" />
    <pl.droidsonroids.gif.GifImageView
        android:id="@+id/wifi_reset_gif"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:src="@drawable/wifi_reset"
        app:layout_constraintDimensionRatio="h,1388:1160"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/wifi_retry_top_bar" />

    <TextView
        android:id="@+id/retry_text_1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="50dp"
        android:text="@string/reset_prompt1"
        android:textColor="@color/black_22"
        android:textSize="15sp"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/wifi_reset_gif"
        app:layout_constraintVertical_bias="0"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/retry_text_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="7dp"
        android:text="@string/reset_prompt2"
        android:textColor="@color/black_22"
        android:textSize="15sp"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/retry_text_1"
        app:layout_constraintVertical_bias="0"
        app:layout_constraintVertical_chainStyle="packed" />


    <TextView
        android:id="@+id/wifi_reset_next_step_btn"
        android:layout_width="250dp"
        android:layout_height="40dp"
        android:background="@drawable/btn_rec_5_bg_with_blue"
        android:gravity="center"
        android:text="@string/next_step"
        android:textColor="@color/white"
        android:textStyle="bold"
        android:layout_marginBottom="30dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>

