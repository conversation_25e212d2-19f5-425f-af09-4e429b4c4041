<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:fitsSystemWindows="true"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/book_bottom_bar"
        android:orientation="vertical">


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/bookshelf_recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="30dp"
            android:overScrollMode="never">


        </androidx.recyclerview.widget.RecyclerView>


        <include
            android:id="@+id/bookshelf_top_bar"
            layout="@layout/layout_multi_select_top_bar" />

        <include
            android:id="@+id/tag_top_bar"
            layout="@layout/layout_tag_top_bar" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/tag_recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/tag_top_bar"
            android:overScrollMode="never" />

    </RelativeLayout>

    <ImageView
        android:id="@+id/bookshelf_guide_img"
        android:layout_width="12dp"
        android:layout_height="9dp"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="33dp"
        android:layout_marginEnd="20dp"
        android:src="@drawable/vector_drawable_guide_triangle" />

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/bookshelf_guide_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/bookshelf_guide_img"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="9dp"
        android:background="@drawable/btn_rec_5_bg_with_code_blue"
        android:gravity="center"
        android:padding="10dp"
        android:text="@string/add_handwriting"
        android:textColor="@color/white"
        android:textSize="12sp" />

    <ImageView
        android:id="@+id/bookshelf_folder_guide_img"
        android:layout_width="12dp"
        android:layout_height="9dp"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="33dp"
        android:layout_marginEnd="60dp"
        android:src="@drawable/vector_drawable_guide_triangle" />

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/bookshelf_folder_guide_tv"
        android:layout_width="130dp"
        android:layout_height="wrap_content"
        android:layout_below="@+id/bookshelf_folder_guide_img"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="50dp"
        android:background="@drawable/btn_rec_5_bg_with_code_blue_without_bottom"
        android:lineSpacingExtra="4dp"
        android:paddingLeft="12dp"
        android:paddingTop="10dp"
        android:paddingRight="12dp"
        android:paddingBottom="26dp"
        android:text="@string/shelf_prompt"
        android:textColor="@color/white"
        android:textSize="12sp" />

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/bookshelf_folder_guide_hide_tv"
        android:layout_width="130dp"
        android:layout_height="wrap_content"
        android:layout_below="@+id/bookshelf_folder_guide_tv"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="50dp"
        android:background="@drawable/btn_rec_5_bg_with_code_blue_without_top"
        android:gravity="center"
        android:paddingBottom="5dp"
        android:text="@string/not_show"
        android:textColor="@color/blue_86e2fd"
        android:textSize="12sp" />

    <LinearLayout
        android:id="@+id/book_bottom_bar"
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:layout_alignParentBottom="true"
        android:layout_centerInParent="true"
        android:background="@color/gary_fa"
        android:orientation="horizontal">

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <LinearLayout
                android:id="@+id/book_tab_ll"
                style="@style/index_radio_button"
                android:layout_width="46dp"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                android:layout_marginRight="128dp"
                android:checked="true"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/book_tab_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:src="@drawable/selector_book_icon" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/book_tab_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="6dp"
                    android:text="@string/smart_office"
                    android:textColor="@drawable/selector_index_text_color"
                    android:textSize="9sp" />

            </LinearLayout>
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <RelativeLayout
                android:id="@+id/tag_tab_ll"
                style="@style/index_radio_button"
                android:layout_width="46dp"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_gravity="center">

                <ImageView
                    android:id="@+id/tag_tab_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:src="@drawable/selector_tag_icon" />


                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/tag_tab_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tag_tab_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6dp"
                    android:text="@string/tag"
                    android:textColor="@drawable/selector_index_text_color"
                    android:textSize="9sp" />

            </RelativeLayout>
        </RelativeLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/bookshelf_bottom_ll"
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:layout_alignParentBottom="true"
        android:visibility="gone">

        <RelativeLayout
            android:id="@+id/bookshelf_rename_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/black">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/bookshelf_rename_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_rename_icon" />

                <TextView
                    android:id="@+id/bookshelf_rename_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/bookshelf_rename_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/rename"
                    android:textColor="@color/white"
                    android:textSize="9sp"
                    android:textStyle="bold" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/bookshelf_delete_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/black">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/bookshelf_delete_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_delete_icon" />

                <TextView
                    android:id="@+id/bookshelf_delete_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/bookshelf_delete_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/delete"
                    android:textColor="@color/white"
                    android:textSize="9sp"
                    android:textStyle="bold" />
            </RelativeLayout>

        </RelativeLayout>

    </LinearLayout>


</RelativeLayout>

