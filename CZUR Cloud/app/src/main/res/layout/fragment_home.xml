<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gary_f9">

    <RelativeLayout
        android:id="@+id/rl_default"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:id="@+id/has_equipment_top_bar_rl"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_alignParentTop="true">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/has_equipment_title"
                android:textColor="@color/black_22"
                android:textSize="18sp" />

            <ImageView
                android:id="@+id/has_equipment_add_btn"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="6dp"
                android:padding="12dp"
                android:src="@mipmap/add_equipment_right_icon" />
        </RelativeLayout>


        <com.scwang.smartrefresh.layout.SmartRefreshLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/has_equipment_top_bar_rl"
            app:srlEnableAutoLoadMore="false"
            app:srlEnableLoadMore="false"
            app:srlEnableOverScrollBounce="true"
            app:srlEnableOverScrollDrag="true"
            app:srlEnableRefresh="false">

            <com.czur.cloud.ui.component.damping.VerticalRecyclerView
                android:id="@+id/home_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:overScrollMode="never"
                android:scrollbars="none" />
        </com.scwang.smartrefresh.layout.SmartRefreshLayout>


    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/index_no_equipment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/no_equipment_bg_img"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <LinearLayout
            android:id="@+id/no_equipment_bg_ll"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">


            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_alignParentTop="true">

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/has_equipment_title"
                    android:textColor="@color/white"
                    android:textSize="18sp" />

                <ImageView
                    android:id="@+id/no_equipment_title_add_btn"
                    android:layout_width="44dp"
                    android:layout_height="44dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="6dp"
                    android:padding="12dp"
                    android:src="@mipmap/home_add_equipment_icon" />
            </RelativeLayout>

            <com.scwang.smartrefresh.layout.SmartRefreshLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/has_equipment_top_bar_rl"
                app:srlEnableAutoLoadMore="false"
                app:srlEnableLoadMore="false"
                app:srlEnableOverScrollBounce="true"
                app:srlEnableOverScrollDrag="true"
                app:srlEnableRefresh="false">


                <com.czur.cloud.ui.component.damping.VerticalScrollView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:overScrollMode="never">

                    <RelativeLayout
                        android:id="@+id/no_equipment_add_btn"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="7.5dp"
                        android:layout_marginRight="7.5dp">


                        <ImageView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:src="@mipmap/shadow_icon" />

                        <RelativeLayout
                            android:id="@+id/no_equipment_add_rl"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:layout_margin="10dp">


                            <ImageView
                                android:id="@+id/no_equipment_add_top_img"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_alignParentStart="true"
                                android:layout_alignParentTop="true"
                                android:src="@mipmap/index_no_equipment_img" />

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerHorizontal="true"
                                android:layout_marginTop="22dp"
                                android:orientation="horizontal">


                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="12dp"
                                    android:layout_marginRight="12dp"
                                    android:text="@string/new_home_title"
                                    android:textColor="@color/no_equipment_bg_text"
                                    android:textSize="24sp" />


                            </LinearLayout>


                            <RelativeLayout
                                android:id="@+id/index_no_equipment_bottom_rl"
                                android:layout_width="match_parent"
                                android:layout_height="48dp"
                                android:layout_below="@+id/no_equipment_add_top_img"
                                android:background="@drawable/rec_index_bg">

                                <RelativeLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_centerInParent="true">

                                    <ImageView
                                        android:id="@+id/no_equipment_bg_mini_icon"
                                        android:layout_width="18dp"
                                        android:layout_height="18dp"
                                        android:src="@mipmap/home_equipment_icon" />

                                    <com.czur.cloud.ui.component.MediumBoldTextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_centerVertical="true"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginLeft="8.5dp"
                                        android:layout_toRightOf="@+id/no_equipment_bg_mini_icon"
                                        android:text="@string/add_equipment_to_begin"
                                        android:textColor="@color/gray_b6bbc8"
                                        android:textSize="15sp" />

                                </RelativeLayout>


                            </RelativeLayout>
                        </RelativeLayout>

                    </RelativeLayout>
                </com.czur.cloud.ui.component.damping.VerticalScrollView>
            </com.scwang.smartrefresh.layout.SmartRefreshLayout>


        </LinearLayout>

    </RelativeLayout>
</RelativeLayout>