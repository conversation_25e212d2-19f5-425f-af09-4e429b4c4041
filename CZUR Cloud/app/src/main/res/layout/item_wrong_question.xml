<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:fresco="http://schemas.android.com/apk/res-auto"
    android:id="@+id/wrong_question_item"
    android:layout_width="match_parent"
    android:layout_height="152dp"
    android:background="@color/gary_f9">

    <RelativeLayout
        android:layout_width="62dp"
        android:layout_height="35dp"
        android:layout_centerVertical="true">

        <CheckBox
            android:id="@+id/check"
            style="@style/BookCheckBox"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_centerInParent="true"
            android:minHeight="20dp" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/img_rl"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:background="@mipmap/shadow_icon" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:layout_margin="10dp">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/btn_rec_5_bg_with_white" />

            <com.facebook.drawee.view.SimpleDraweeView
                android:id="@+id/wrong_question_img"
                android:layout_margin="5dp"
                fresco:failureImageScaleType="focusCrop"
                fresco:placeholderImageScaleType="focusCrop"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </RelativeLayout>


    </RelativeLayout>


</RelativeLayout>