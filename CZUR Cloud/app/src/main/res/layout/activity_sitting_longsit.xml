<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <include layout="@layout/layout_sitting_top_bar_withspace" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingStart="@dimen/jingMargin20"
        android:paddingTop="@dimen/jingMargin10"
        android:paddingEnd="@dimen/jingMargin20">

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/jingMargin20"
            android:singleLine="true"
            app:autoSizeTextType="uniform"
            app:autoSizeMinTextSize="19sp"
            app:autoSizeMaxTextSize="22sp"
            app:autoSizeStepGranularity="1sp"
            android:text="@string/sitting_home_text"
            android:textColor="@color/black_22"
            android:textSize="22sp"
            android:visibility="gone"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="visible" />

    </LinearLayout>

    <View
        android:id="@+id/space_view2"
        android:layout_width="match_parent"
        android:background="@color/gray_f2"
        android:layout_height="13.5dp"/>

    <RelativeLayout
        android:id="@+id/jing_main_sitting_rl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:background="@color/white"
        android:paddingTop="@dimen/jingMargin10"
        android:paddingBottom="@dimen/jingMargin10"
        android:paddingStart="@dimen/jingMargin10"
        android:paddingEnd="@dimen/jingMargin10">

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/jing_main_setting_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/jingTitleMargin"
            android:maxLines="1"
            android:text="@string/sitting_longsit_define_sit"
            android:textColor="@color/black_6"
            android:textSize="@dimen/jingMainNameSize" />

        <ImageView
            android:id="@+id/jing_main_setting_statue_img"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_toStartOf="@+id/jing_main_setting_value_tv"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/jingArrowSpaceText"
            android:layout_gravity="center_vertical"
            android:scaleType="fitCenter"
            android:src="@mipmap/sitting_ok"
            android:visibility="gone"/>

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/jing_main_setting_value_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text=""
            android:textColor="@color/gray_bb"
            android:layout_marginStart="@dimen/jingArrowSpaceText"
            android:textSize="@dimen/jingMainValueSize"
            android:layout_toStartOf="@+id/jing_main_setting_img" />

        <ImageView
            android:id="@+id/jing_main_setting_img"
            android:layout_width="@dimen/jingArrowWidth"
            android:layout_height="@dimen/jingArrowHeight"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/jingArrowSpaceText"
            android:layout_marginEnd="@dimen/jingTitleMargin"
            android:src="@mipmap/gray_arrow" />

    </RelativeLayout>

</LinearLayout>