<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/popup_window_dialog_cl">
<!--    android:background="@drawable/popup_window_all_bg"
-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/dialog_cl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@drawable/popup_window_all_bg"
        >

        <LinearLayout
            android:id="@+id/bottom_menu_title_ll"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@drawable/popup_window_bg"
            android:orientation="vertical"
            android:layout_marginBottom="5dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            >

            <TextView
                android:id="@+id/bottom_menu_title"
                style="@style/starry_bottom_menu_text_style_nopadding"
                android:layout_height="match_parent"
                android:textSize="14sp"
                android:paddingStart="12dp"
                android:paddingEnd="12dp"
                android:textStyle="normal"
                tools:text="菜单栏标题"
                />



        </LinearLayout>

        <View
            style="@style/starry_list_gray_line"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/bottom_menu_title_ll"
            app:layout_constraintBottom_toTopOf="@+id/recycleList" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycleList"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/popup_window_bg"
            android:orientation="vertical"
            android:layout_marginTop="1dp"
            android:layout_marginBottom="5dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/bottom_menu_title_ll"
            app:layout_constraintBottom_toTopOf="@+id/bottom_menu_cancel_cl"
            />

        <LinearLayout
            android:id="@+id/bottom_menu_cancel_cl"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_marginTop="5dp"
            android:background="@drawable/popup_window_bg"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            >

            <TextView
                android:id="@+id/bottom_menu_cancel"
                style="@style/starry_bottom_menu_text_style"
                android:text="@string/starry_member_bottom_menu_cancel"
                />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>