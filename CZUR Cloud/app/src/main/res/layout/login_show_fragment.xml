<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/loginshow_background3x"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        />

    <ImageView
        android:layout_width="170dp"
        android:layout_height="30dp"
        android:src="@mipmap/loginshow_logo"
        android:scaleType="fitCenter"
        android:layout_marginTop="50dp"
        android:layout_marginStart="20dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="visible"
        />

    <com.czur.cloud.ui.starry.component.DrawableCenterOneLineTextView
        android:id="@+id/loginshow_btn_eshare"
        android:layout_width="250dp"
        android:layout_height="55dp"
        android:gravity="center"
        android:drawableStart="@mipmap/loginshow_eshare"
        android:drawablePadding="@dimen/dp10"
        android:background="@drawable/loginshow_btn_rec_6_bg_with_blue"
        android:text="@string/loginshow_btn_eshare"
        android:textColor="@color/white"
        android:textSize="@dimen/login_btn_eshare"
        app:layout_constraintBottom_toTopOf="@+id/loginshow_btn_login"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="20dp"
        />

    <TextView
        android:id="@+id/loginshow_btn_login"
        android:layout_width="250dp"
        android:layout_height="55dp"
        android:text="@string/loginshow_btn_login"
        android:textColor="@color/white"
        android:gravity="center"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="45dp"
        android:background="@drawable/loginshow_btn_rec_6_bg_with_black"
        />


</androidx.constraintlayout.widget.ConstraintLayout>