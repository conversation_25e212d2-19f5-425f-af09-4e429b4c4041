<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:orientation="vertical">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="45dp"
        android:background="@color/gary_ff">

        <LinearLayout
            android:id="@+id/btn_back_ll"
            android:layout_centerVertical="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/btn_back"
                android:layout_width="29.5dp"
                android:layout_height="36dp"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="9dp"
                android:padding="10dp"
                android:src="@mipmap/login_back_icon" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:text="@string/album"
                android:textColor="@color/account_title"
                android:textSize="16sp" />
        </LinearLayout>


        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/tv_des"
            android:layout_width="130dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_centerInParent="true"
            android:textColor="@color/account_title"
            android:textSize="18sp" />

        <RelativeLayout
            android:id="@+id/album_top_bar_cancel"
            android:layout_width="100dp"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="17dp"
                android:text="@string/cancel"
                android:textColor="@color/account_title"
                android:textSize="17sp" />

        </RelativeLayout>


    </RelativeLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler"
        android:overScrollMode="never"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/gary_ff"
        android:clipToPadding="false"
        android:paddingLeft="12dp"
        android:paddingRight="12dp" />
</LinearLayout>
