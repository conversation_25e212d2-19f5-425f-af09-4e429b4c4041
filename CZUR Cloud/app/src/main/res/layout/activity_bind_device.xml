<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="194.5dp"
        android:orientation="vertical">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_gravity="center_horizontal"
            android:background="@mipmap/bind_icon" />

        <TextView
            android:id="@+id/bind_tv"
            android:layout_marginTop="46dp"
            android:layout_gravity="center_horizontal"
            android:textColor="@color/blue_29b0d7"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textStyle="bold"
            android:gravity="center"
            android:textSize="18sp"
            android:text="@string/add_equipment_success"
            />
    </LinearLayout>


    <TextView
        android:id="@+id/bind_btn"
        android:layout_width="250dp"
        android:layout_height="40dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="30dp"
        android:background="@drawable/btn_rec_5_bg_with_code_blue"
        android:gravity="center"
        android:text="@string/bind_now"
        android:textColor="@color/white"
        android:textStyle="bold" />
</RelativeLayout>