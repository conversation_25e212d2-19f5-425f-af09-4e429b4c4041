<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/starry_tab_meeting"
    android:background="@color/starry_comm_bg">

    <RelativeLayout
        android:id="@+id/starry_home_user"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/starry_user_bg">

        <RelativeLayout
            android:id="@+id/starry_home_user1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.czur.cloud.ui.starry.meeting.widget.HeadImageView
                android:id="@+id/starry_home_user_iv"
                android:layout_width="@dimen/starryMargin50"
                android:layout_height="@dimen/starryMargin50"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/starryMargin20"
                android:layout_marginTop="@dimen/starryMargin20"
                android:layout_marginEnd="@dimen/starryMargin10"
                android:layout_marginBottom="@dimen/starryMargin20"
                android:visibility="visible"
                app:roundPercent="1" />

            <RelativeLayout
                android:id="@+id/home_user_title_rl"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/starryMargin15"
                android:layout_marginEnd="@dimen/starryMargin15"
                android:layout_toEndOf="@+id/starry_home_user_iv">

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/home_user_account_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:ellipsize="end"
                    android:maxEms="8"
                    android:singleLine="true"
                    android:textColor="@color/account_color"
                    android:textSize="@dimen/starryAccountTitle"
                    tools:text="@string/starry_home_user_title" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/home_user_account_no"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/home_user_account_title"
                    android:layout_alignParentStart="true"
                    android:layout_marginTop="@dimen/starryMargin5"
                    android:textColor="@color/account_color"
                    android:textSize="@dimen/starryAccountNo"
                    tools:text="***********" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/starry_home_message_rl"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:visibility="visible">

                <ImageView
                    android:id="@+id/starry_home_message_btn"
                    android:layout_width="@dimen/starryMargin35"
                    android:layout_height="@dimen/starryMargin35"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="@dimen/starryMargin20"
                    android:background="@drawable/starry_circle_with_message"
                    android:padding="@dimen/starryMargin8"
                    android:src="@mipmap/starry_home_message" />


                <View
                    android:id="@+id/starry_home_top_red_point"
                    android:layout_width="@dimen/starryMargin8"
                    android:layout_height="@dimen/starryMargin8"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginTop="2dp"
                    android:layout_marginEnd="@dimen/starryMargin20"
                    android:background="@drawable/circle_red"
                    android:visibility="gone" />

            </RelativeLayout>

        </RelativeLayout>

        <LinearLayout
            android:id="@+id/starry_home_btn_rl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/starry_home_user1"
            android:layout_marginStart="@dimen/starryMargin10"
            android:layout_marginTop="@dimen/starryMargin10"
            android:layout_marginEnd="@dimen/starryMargin10"
            android:layout_marginBottom="@dimen/starryMargin20"
            android:orientation="horizontal"
            android:visibility="visible">

            <LinearLayout
                android:id="@+id/home_join_btn_ll"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/home_join_iv"
                    android:layout_width="@dimen/starryMargin35"
                    android:layout_height="@dimen/starryMargin35"
                    android:src="@mipmap/starry_home_btn_join_meet" />

                <TextView
                    android:id="@+id/home_join_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_5"
                    android:gravity="center"
                    android:text="@string/starry_home_btn_join"
                    android:textColor="@color/white"
                    android:textSize="@dimen/starry_meeting_home_title_size" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/home_start_btn_ll"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="visible">

                <ImageView
                    android:id="@+id/home_start_iv"
                    android:layout_width="@dimen/starryMargin35"
                    android:layout_height="@dimen/starryMargin35"
                    android:src="@mipmap/starry_home_btn_start_meet" />

                <TextView
                    android:id="@+id/home_start_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_5"
                    android:gravity="center"
                    android:text="@string/starry_home_btn_start"
                    android:textColor="@color/white"
                    android:textSize="@dimen/starry_meeting_home_title_size" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/home_eshare_btn_ll"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/home_eshare_iv"
                    android:layout_width="@dimen/starryMargin35"
                    android:layout_height="@dimen/starryMargin35"
                    android:src="@mipmap/starry_home_btn_eshare" />

                <TextView
                    android:id="@+id/home_eshare_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_5"
                    android:gravity="center"
                    android:text="@string/starry_home_btn_eshare"
                    android:textColor="@color/white"
                    android:textSize="@dimen/starry_meeting_home_title_size" />

            </LinearLayout>


        </LinearLayout>

    </RelativeLayout>
    <RelativeLayout
        android:id="@+id/show_permission_status_rl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/starry_home_user"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:paddingTop="5dp"
        android:paddingBottom="5dp"
        android:background="@color/starry_comm_bg"
        android:visibility="gone">

        <TextView
            android:id="@+id/show_permission_status_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="当前未开启锁屏显示权限、悬浮窗权限、后台弹出界面权限，将无法正常接收会议提醒。去设置"
            android:textColor="@color/starry_home_text_gray_color"
            android:textSize="@dimen/starryCompanyBtnTitle" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/starry_home_company_rl"
        android:layout_width="match_parent"
        android:layout_height="@dimen/starryMargin70"
        android:layout_below="@+id/show_permission_status_rl"
        android:background="@color/starry_white_bg">

        <ImageView
            android:id="@+id/starry_home_company_iv"
            android:layout_width="@dimen/starryMargin20"
            android:layout_height="@dimen/starryMargin20"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/starryMargin20"
            android:src="@mipmap/starry_home_comapny_icon" />

        <TextView
            android:id="@+id/starry_home_company_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/starryMargin15"
            android:layout_toEndOf="@+id/starry_home_company_iv"
            android:text="@string/starry_home_company_title"
            android:textColor="@color/title_black_color"
            android:textSize="@dimen/starryCompanySubTitle" />
    </RelativeLayout>

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/starry_home_company_rl">

        <com.scwang.smartrefresh.layout.header.ClassicsHeader
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin60"
            app:srlDrawableMarginRight="-10dp"
            app:srlDrawableProgress="@mipmap/loading"
            app:srlTextFailed="@string/starry_reflash_fail"
            app:srlTextFinish="@string/starry_reflash_success"
            app:srlTextLoading=""
            app:srlTextNothing=""
            app:srlTextPulling=""
            app:srlTextRefreshing=""
            app:srlTextRelease=""
            app:srlTextUpdate="" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"
            android:background="@color/starry_white_bg"
            android:orientation="vertical">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view_company"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:overScrollMode="never"
                android:visibility="visible" />

            <RelativeLayout
                android:id="@+id/starry_home_company_none"
                android:layout_width="match_parent"
                android:layout_height="@dimen/starryMargin70"
                android:visibility="visible">

                <TextView
                    android:id="@+id/starry_item_company_icon"
                    android:layout_width="@dimen/starryMargin15"
                    android:layout_height="@dimen/starryMargin3"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/starryMargin40"
                    android:background="@drawable/starry_rectangle_with_title"
                    android:gravity="center"
                    android:text=""
                    android:textSize="@dimen/starryFontSize20" />

                <TextView
                    android:id="@+id/starry_item_company_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/starryMargin20"
                    android:layout_toEndOf="@+id/starry_item_company_icon"
                    android:padding="@dimen/dp_5"
                    android:text="@string/starry_home_none"
                    android:textColor="@color/title_black_color"
                    android:textSize="@dimen/starryCompanySubTitle" />

                <TextView
                    android:id="@+id/starry_item_company_desc"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_toEndOf="@+id/starry_item_company_title"
                    android:padding="@dimen/dp_5"
                    android:text="@string/starry_home_none_desc"
                    android:textColor="@color/starry_home_text_gray_color"
                    android:textSize="@dimen/starryCompanySubTitleDesc" />

            </RelativeLayout>


            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/starryMargin20"
                android:background="@color/starry_comm_bg" />

            <RelativeLayout
                android:id="@+id/starry_home_contacts_rl"
                android:layout_width="match_parent"
                android:layout_height="@dimen/starryMargin70">

                <ImageView
                    android:id="@+id/starry_home_contacts_iv"
                    android:layout_width="@dimen/starryMargin20"
                    android:layout_height="@dimen/starryMargin20"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/starryMargin20"
                    android:src="@mipmap/starry_home_contacts_icon" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/starryMargin15"
                    android:layout_toEndOf="@+id/starry_home_contacts_iv"
                    android:text="@string/starry_home_contacts_title"
                    android:textColor="@color/title_black_color"
                    android:textSize="@dimen/starryCompanySubTitle" />

                <ImageView
                    android:layout_width="@dimen/starryMargin8"
                    android:layout_height="@dimen/starryMargin15"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="@dimen/starryMargin20"
                    android:background="@mipmap/starry_right_arrow"
                    android:padding="@dimen/starryMargin10" />

                <View
                    android:id="@+id/company_list_line"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_alignParentBottom="true"
                    android:background="@color/starry_comm_bg" />

            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/starryMargin70"
                android:background="@color/starry_comm_bg"
                android:visibility="gone" />

        </LinearLayout>

    </com.scwang.smartrefresh.layout.SmartRefreshLayout>

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_marginEnd="@dimen/starryMargin20"
        android:layout_marginBottom="@dimen/starryMargin20">

        <ImageView
            android:id="@+id/newStartMeeting"
            android:layout_width="@dimen/starryMargin70"
            android:layout_height="@dimen/starryMargin70"
            android:gravity="center"
            android:src="@mipmap/starry_start_meeting_btn"
            android:visibility="gone" />

    </RelativeLayout>

    <include
        layout="@layout/starry_no_network_layout"
        android:visibility="gone" />

</RelativeLayout>