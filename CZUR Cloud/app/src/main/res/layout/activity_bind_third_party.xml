<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:fitsSystemWindows="true"
    android:background="@color/gary_f9">

    <include
        android:id="@+id/bind_third_party_bar"
        layout="@layout/layout_account_top_bar" />


    <LinearLayout
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:layout_width="250dp"
        android:layout_height="wrap_content"
        android:layout_below="@+id/bind_third_party_bar"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="47dp"
        android:orientation="vertical">

        <com.czur.cloud.ui.component.NoHintEditText
            android:id="@+id/bind_third_party_account_edt"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:background="@drawable/btn_rec_5_bg_with_gray"
            android:gravity="center"
            android:singleLine="true"
            android:digits="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_@."
            android:hint="@string/login_hint"
            android:textColor="@color/login_btn_text"
            android:textColorHint="@color/login_edt_hint_color"
            android:textSize="15sp"
            android:textStyle="bold" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="11dp">

            <com.czur.cloud.ui.component.NoHintEditText
                android:id="@+id/bind_third_party_code_edt"
                android:layout_width="156dp"
                android:layout_height="match_parent"
                android:background="@drawable/btn_rec_5_bg_with_gray"
                android:gravity="center"
                android:inputType="number"
                android:maxLength="6"
                android:hint="@string/identifying_code"
                android:textColor="@color/login_btn_text"
                android:textColorHint="@color/login_edt_hint_color"
                android:textSize="15sp"
                android:textStyle="bold" />

            <TextView
                android:gravity="center"
                android:id="@+id/get_code_btn"
                android:layout_width="83dp"
                android:layout_height="match_parent"
                android:layout_alignParentRight="true"
                android:background="@drawable/selector_get_code_btn_white_grey"
                android:text="@string/gain"
                android:textColor="@color/black_272f44"
                android:textSize="14sp" />

        </RelativeLayout>


        <com.czur.cloud.ui.component.ProgressButton
            android:gravity="center"
            android:id="@+id/next_step_btn"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="23dp"
            android:background="@drawable/selector_register_btn"
            app:progress_btn_tv="@string/next_step"
            android:clickable="false"
         />


    </LinearLayout>


</RelativeLayout>