<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:scrollbars="vertical"
    android:fadingEdge="vertical"
    android:background="@color/white"
    android:id="@+id/fragment_day_sv"
    >

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:srlEnableLoadMore="false"
        app:srlEnableOverScrollBounce="true"
        app:srlEnableOverScrollDrag="true"
        app:srlEnableRefresh="true">

        <com.scwang.smartrefresh.layout.header.ClassicsHeader
            android:layout_width="match_parent"
            android:layout_height="60dp"
            app:srlDrawableMarginRight="-10dp"
            app:srlTextFailed=""
            app:srlTextFinish=""
            app:srlTextLoading=""
            app:srlTextPulling=""
            app:srlTextRefreshing=""
            app:srlTextRelease=""
            app:srlTextUpdate="" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <RelativeLayout
            android:id="@+id/data_rl"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="visible">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/jingMargin10"
                    android:layout_marginStart="2dp"
                    android:layout_marginEnd="@dimen/reportTextMargin"
                    android:layout_gravity="center_vertical">

                    <ImageView
                        android:id="@+id/sitting_report_chart_title"
                        android:layout_width="@dimen/sitting_rectangle_width"
                        android:layout_height="@dimen/sitting_rectangle_height"
                        android:src="@drawable/rectangle_with_title"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="@dimen/sitting_rectangle_width"
                        android:layout_alignParentStart="true"
                        />

                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="@dimen/dp_5"
                        android:layout_toEndOf="@+id/sitting_report_chart_title"
                        android:text="@string/sitting_home_sitting_tend"
                        android:textColor="@color/black_22"
                        android:textSize="@dimen/reportNameSize" />

                        <com.czur.cloud.ui.component.MediumBoldTextView
                            android:id="@+id/report_right_name2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/gray_ac"
                            android:textSize="@dimen/reportSubTitleSize"
                            android:text="@string/sitting_home_sitting_error"
                            android:layout_centerVertical="true"
                            android:layout_alignParentEnd="true"/>
                        <ImageView
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:src="@drawable/circle_red"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="@dimen/dp_5"
                            android:layout_toStartOf="@+id/report_right_name2" />
                        <com.czur.cloud.ui.component.MediumBoldTextView
                            android:id="@+id/report_right_name1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/gray_ac"
                            android:textSize="@dimen/reportSubTitleSize"
                            android:text="@string/sitting_home_sitting_right"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="30dp"
                            android:layout_toStartOf="@+id/report_right_name2"
                            />
                        <ImageView
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:src="@drawable/circle_with_right"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="@dimen/dp_5"
                            android:layout_toStartOf="@+id/report_right_name1" />

                    </RelativeLayout>

                <com.github.mikephil.charting.charts.LineChart
                    android:id="@+id/chart1"
                    android:layout_width="match_parent"
                    android:layout_height="270dp"
                    android:layout_marginStart="6dp"
                    android:layout_marginEnd="-2dp"
                    android:paddingBottom="20dp"/>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="14dp"
                        android:background="@color/gary_f9"
                        android:gravity="center_vertical" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">
                        <include
                            android:id="@+id/sitting_status"
                            layout="@layout/item_sitting_report_horizontal"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:visibility="visible" />
                        <include
                            android:id="@+id/sitting_status2"
                            layout="@layout/item_sitting_report_horizontal"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:visibility="gone" />
                    </LinearLayout>

                    <View
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:gravity="center_vertical" />

            </LinearLayout>

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/empty_rl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="70dp"
                        android:layout_height="wrap_content"
                        android:adjustViewBounds="true"
                        android:layout_marginTop="250dp"
                        android:layout_gravity="center_horizontal"
                        android:scaleType="fitXY"
                        android:src="@mipmap/report_no_data_icon" />

                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="15.5dp"
                        android:layout_marginBottom="150dp"
                        android:text="@string/empty_report"
                        android:textColor="@color/gary_c4"
                        android:textSize="15sp" />
                </LinearLayout>

            </RelativeLayout>
        </RelativeLayout>

    </com.scwang.smartrefresh.layout.SmartRefreshLayout>

</LinearLayout>
</ScrollView>