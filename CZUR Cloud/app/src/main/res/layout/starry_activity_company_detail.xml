<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fitsSystemWindows="true"
    xmlns:tools="http://schemas.android.com/tools">

    <include
        android:id="@+id/starry_layout_top_bar_rl"
        layout="@layout/starry_layout_top_bar" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:layout_below="@+id/starry_layout_top_bar_rl"
        android:orientation="vertical"
        android:background="@color/white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin100"
            android:orientation="horizontal"
            android:gravity="center"
            android:background="@color/starry_white_bg"
            >

            <ImageView
                android:layout_width="@dimen/starryMargin15"
                android:layout_height="@dimen/starryMargin15"
                android:layout_marginStart="@dimen/starryMargin20"
                android:src="@mipmap/starry_home_comapny_icon"/>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/starryMargin20"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/company_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:text="企业名称"
                    android:gravity="center_vertical"
                    android:textSize="@dimen/starryCompanySubTitle"
                    android:textColor="@color/starry_title_value"
                    android:padding="@dimen/starryMargin10"
                    />

                <TextView
                    android:id="@+id/company_name_old"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/starry_company_old"
                    android:gravity="center_vertical"
                    android:textSize="@dimen/starryCompanyBtnTitle"
                    android:textColor="@color/starry_delete_red"
                    android:layout_marginStart="@dimen/starryMargin10"
                    android:layout_below="@+id/company_name"
                    android:visibility="gone"
                    />

            </RelativeLayout>
        </LinearLayout>

        <View
            style="@style/starry_list_space_line_style"
            android:layout_marginStart="@dimen/starryMargin15"/>

        <RelativeLayout
            style="@style/starry_contact_cell_rl_style"
            >

            <TextView
                android:id="@+id/company_count_title"
                style="@style/starry_contact_cell_title_style"
                android:text="@string/starry_company_count" />

            <TextView
                android:id="@+id/company_count"
                style="@style/starry_contact_cell_value_style"
                android:layout_toEndOf="@+id/company_count_title" />

        </RelativeLayout>

        <View
            style="@style/starry_list_space_line_style"
            android:layout_marginStart="@dimen/starryMargin15"/>

        <RelativeLayout
            android:id="@+id/contact_detail_company_title_rl"
            style="@style/starry_contact_cell_rl_style"
            >

            <TextView
                android:id="@+id/company_admin_name_title"
                style="@style/starry_contact_cell_title_style"
                android:text="@string/starry_company_admin_name" />

            <TextView
                android:id="@+id/company_admin_name"
                style="@style/starry_contact_cell_value_style"
                android:layout_toEndOf="@+id/company_admin_name_title" />

        </RelativeLayout>

        <View
            style="@style/starry_list_space_line_style"
            android:layout_marginStart="@dimen/starryMargin15"/>

        <RelativeLayout
            style="@style/starry_contact_cell_rl_style"
            >

            <TextView
                android:id="@+id/company_admin_account_title"
                style="@style/starry_contact_cell_title_style"
                android:text="@string/starry_company_admin_account" />

            <TextView
                android:id="@+id/company_admin_account"
                style="@style/starry_contact_cell_value_style"
                android:layout_toEndOf="@+id/company_admin_account_title" />

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin30"
            android:background="@color/starry_comm_bg"/>

        <LinearLayout
            android:id="@+id/company_exit_btn_ll"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@color/starry_comm_bg">
            <TextView
                android:id="@+id/company_exit_btn"
                style="@style/starry_contact_detail_btn_red_style"
                android:layout_margin="@dimen/starryMargin20"
                android:text="@string/starry_exit_btn"
            />
        </LinearLayout>

    <LinearLayout
        android:id="@+id/company_jion_btn_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/starry_comm_bg"
        android:visibility="visible">

        <TextView
            android:id="@+id/company_join_btn"
            style="@style/starry_contact_detail_btn_style"
            android:layout_marginStart="@dimen/starryMargin20"
            android:layout_marginEnd="@dimen/starryMargin20"
            android:text="@string/starry_join_btn"
            />

        <TextView
            android:id="@+id/company_reject_btn"
            style="@style/starry_contact_detail_btn_red_style"
            android:layout_marginTop="@dimen/starryMargin10"
            android:layout_marginStart="@dimen/starryMargin20"
            android:layout_marginEnd="@dimen/starryMargin20"
            android:text="@string/starry_reject_btn"
            />

    </LinearLayout>

    </LinearLayout>


</RelativeLayout>

