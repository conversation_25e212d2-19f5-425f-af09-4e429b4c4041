<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="50dp"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_centerVertical="true"
        android:gravity="center_vertical">
        <TextView
            android:id="@+id/contact_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/starryCompanySubTitle"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/starry_text_title_color_black"
            android:text=""
            tools:text="item内容"
            android:layout_gravity="center"
            />

        <TextView
            android:id="@+id/second_line"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/contact_name"
            android:layout_gravity="center"
            android:ellipsize="end"
            android:maxLines="1"
            android:text=""
            android:textColor="@color/starry_text_title_color_black"
            android:textSize="@dimen/starryAccountNo"
            tools:text="item内容" />

    </RelativeLayout>


    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_alignParentBottom="true"
        android:background="@color/starry_list_liner_color" />

</RelativeLayout>
