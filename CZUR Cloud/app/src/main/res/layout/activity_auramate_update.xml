<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gary_f9"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/white">

        <ImageView
            android:id="@+id/img_back"
            android:layout_width="29.5dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="10dp"
            android:padding="10dp"
            android:src="@mipmap/login_back_icon" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/normal_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/fw_update"
            android:textColor="@color/account_title"
            android:textSize="18sp" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:paddingTop="18dp"
        android:paddingBottom="18dp">

        <ImageView
            android:id="@+id/img_state"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="24dp"
            android:src="@mipmap/update_state_before" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/img_state"
            android:orientation="vertical">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/black_22"
                android:textSize="15sp" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal">

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/tv_warn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/update_warn"
                    android:textColor="@color/red_e44e4e"
                    android:visibility="gone"
                    android:textSize="12sp" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/tv_tip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:lineSpacingExtra="3dp"
                    android:textColor="@color/red_e44e4e"
                    android:textSize="12sp" />
            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>


    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/tv_update"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="13dp"
        android:background="@color/white"
        android:gravity="center"
        android:foreground="?selectableItemBackground"
        android:text="@string/update_now"
        android:textColor="@drawable/selector_blue_gray_text"
        android:textSize="16sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="13dp"
        android:paddingStart="24dp"
        android:paddingEnd="24dp"
        android:background="@color/white"
        android:orientation="vertical">

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="@string/update_log"
            android:textColor="@color/black_22"
            android:textSize="16sp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_update_log"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="12dp" />
    </LinearLayout>


</LinearLayout>