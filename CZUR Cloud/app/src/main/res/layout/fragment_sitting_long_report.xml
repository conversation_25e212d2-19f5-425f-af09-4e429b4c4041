<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:scrollbars="vertical"
    android:fadingEdge="vertical"
    android:background="@color/white"
    android:id="@+id/fragment_day_sv">

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:srlEnableLoadMore="false"
        app:srlEnableOverScrollBounce="true"
        app:srlEnableOverScrollDrag="true"
        app:srlEnableRefresh="true">

        <com.scwang.smartrefresh.layout.header.ClassicsHeader
            android:layout_width="match_parent"
            android:layout_height="60dp"
            app:srlDrawableMarginRight="-10dp"
            app:srlTextFailed="@string/sitting_reflash_fail"
            app:srlTextFinish="@string/sitting_reflash_success"
            app:srlTextLoading=""
            app:srlTextPulling=""
            app:srlTextRefreshing=""
            app:srlTextRelease=""
            app:srlTextUpdate="" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <RelativeLayout
            android:id="@+id/data_rl"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="visible">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/jingMargin30"
                        android:layout_marginBottom="@dimen/jingMargin30"
                        android:orientation="horizontal">

                        <com.czur.cloud.ui.component.MediumBoldTextView
                            android:id="@+id/tv_report_alert_value"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:singleLine="true"
                            android:textColor="@color/black"
                            android:layout_centerInParent="true"
                            android:textSize="80sp"
                            android:text="18"/>
                        <TextView
                            android:id="@+id/tv_report_alert_han"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:singleLine="true"
                            android:textColor="@color/black"
                            android:textSize="20sp"
                            android:layout_toEndOf="@+id/tv_report_alert_value"
                            android:layout_alignBaseline="@+id/tv_report_alert_value"
                            android:layout_gravity="bottom"
                            android:paddingStart="10dp"
                            android:text="@string/sitting_long_sit_unit"/>
                        <TextView
                            android:id="@+id/tv_report_alert_per"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:singleLine="true"
                            android:textColor="@color/black"
                            android:textSize="20sp"
                            android:layout_toEndOf="@+id/tv_report_alert_han"
                            android:layout_alignBaseline="@+id/tv_report_alert_value"
                            android:paddingStart="5dp"
                            android:text="@string/sitting_long_sit_per"
                            android:visibility="invisible"/>
                    </RelativeLayout>

                    <View
                    android:layout_width="match_parent"
                    android:layout_height="14dp"
                    android:background="@color/gary_f9"
                    android:gravity="center_vertical" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/reportTextMargin"
                    android:layout_marginStart="@dimen/reportTextMargin15"
                    android:textColor="@color/black_22"
                    android:textSize="@dimen/reportNameSize"
                    android:text="@string/sitting_home_sitting_longtime"
                    />
                <com.github.mikephil.charting.charts.BarChart
                    android:id="@+id/chart1"
                    android:layout_width="match_parent"
                    android:layout_height="270dp"
                    android:layout_marginStart="6dp"
                    android:layout_marginEnd="-15dp"
                    android:paddingBottom="20dp"/>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/gary_f9"
                        android:gravity="center_vertical" >
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/gary_f9"
                            android:layout_margin="@dimen/reportTextMargin15"
                            android:text="@string/sitting_longtime_note"
                            android:textSize="@dimen/sitting_report_note"
                            />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="120dp"
                        android:gravity="center_vertical" />

            </LinearLayout>

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/empty_rl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="70dp"
                        android:layout_height="wrap_content"
                        android:adjustViewBounds="true"
                        android:layout_marginTop="250dp"
                        android:layout_gravity="center_horizontal"
                        android:scaleType="fitXY"
                        android:src="@mipmap/report_no_data_icon" />

                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="15.5dp"
                        android:layout_marginBottom="150dp"
                        android:text="@string/empty_report"
                        android:textColor="@color/gary_c4"
                        android:textSize="15sp" />
                </LinearLayout>

            </RelativeLayout>
        </RelativeLayout>

    </com.scwang.smartrefresh.layout.SmartRefreshLayout>

</LinearLayout>
</ScrollView>