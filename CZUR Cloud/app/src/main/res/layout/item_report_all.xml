<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/gary_f9">

    <LinearLayout
        android:id="@+id/report_main_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="30dp">

            <TextView
                android:id="@+id/report_date_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="15dp"
                android:textColor="@color/gary_ba"
                android:textSize="12sp" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="5dp"
            android:clipToPadding="false"
            android:paddingBottom="10dp"
            android:background="@drawable/btn_rec_10_bg_with_code_white"
            android:visibility="visible">

            <LinearLayout
                android:id="@+id/report_progress_rl"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:gravity="center"
                android:orientation="vertical">


                <com.github.mikephil.charting.charts.PieChart
                    android:id="@+id/pie_chart"
                    android:layout_width="90dp"
                    android:layout_height="90dp" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginTop="5dp"
                    android:layout_height="wrap_content">
                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/tv_right"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableStart="@drawable/green_dot"
                        android:drawablePadding="5dp"
                        android:gravity="center_vertical|start"
                        android:textColor="@color/gary_ba"
                        android:textSize="12sp" />
                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/tv_micro_error"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:drawableStart="@drawable/yellow_dot"
                        android:drawablePadding="5dp"
                        android:gravity="center_vertical"
                        android:textColor="@color/gary_ba"
                        android:textSize="12sp" />
                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/tv_error"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:drawableStart="@drawable/orange_dot"
                        android:drawablePadding="5dp"
                        android:gravity="center_vertical"
                        android:textColor="@color/gary_ba"
                        android:textSize="12sp" />
                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/tv_serious_error"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:drawableStart="@drawable/red_dot"
                        android:drawablePadding="5dp"
                        android:textColor="@color/gary_ba"
                        android:textSize="12sp" />
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginEnd="10dp"
                android:layout_marginStart="10dp"
                android:layout_toEndOf="@+id/report_progress_rl"
                android:orientation="vertical">

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/report_use_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/use_time"
                    android:textColor="@color/gary_ba"
                    android:textSize="12sp" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/report_use_time_main"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:text="@string/use_time_main"
                    android:textColor="@color/black_22"
                    android:textSize="13sp" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/report_use_time_sub"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:lineSpacingExtra="5dp"
                    android:text="@string/use_time_sub"
                    android:textColor="@color/black_22"
                    android:textSize="12sp" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/report_prompt"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:lineSpacingExtra="2dp"
                    android:text="@string/report_final"
                    android:textColor="@color/gary_ba"
                    android:textSize="12sp" />
            </LinearLayout>

            <RelativeLayout
                android:id="@+id/new_rl"
                android:layout_width="35dp"
                android:layout_marginTop="-10dp"
                android:layout_height="35dp"
                android:layout_alignParentEnd="true">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:src="@mipmap/report_new_triangle" />

                <TextView
                    android:id="@+id/new_report_tv"
                    android:layout_width="18dp"
                    android:gravity="center"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="2dp"
                    android:layout_marginTop="5dp"
                    android:text="@string/new_text"
                    android:maxLines="1"
                    app:autoSizeMaxTextSize="12sp"
                    app:autoSizeMinTextSize="7sp"
                    app:autoSizeStepGranularity="1sp"
                    app:autoSizeTextType="uniform"
                    android:includeFontPadding="false"
                    android:textColor="@color/white"
                    android:textSize="12sp" />
            </RelativeLayout>
        </RelativeLayout>

    </LinearLayout>

    <RelativeLayout
        android:id="@+id/report_other_rl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipToPadding="false"
        android:background="@drawable/btn_rec_10_bg_with_code_white">

        <LinearLayout
            android:id="@+id/report_ll_space"
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:background="@color/gary_f9"
            android:orientation="horizontal" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_below="@+id/report_ll_space"
            android:layout_marginEnd="10dp"
            android:layout_marginStart="10dp">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_gravity="center_vertical"
                android:padding="10dp"
                android:layout_weight="4">

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/report_use_time_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="日报告"
                    android:textColor="@color/black_22"
                    android:textSize="16sp" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:text="已生成"
                    android:textColor="@color/gary_ba"
                    android:textSize="12sp" />

            </LinearLayout>

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_gravity="center_vertical"
                android:textAlignment="textEnd"
                android:text="立即查看"
                android:padding="10dp"
                android:textColor="@color/blue_29b0d7"
                android:textSize="16sp" />

        </LinearLayout>

        <RelativeLayout
            android:id="@+id/new_rl_other"
            android:layout_width="35dp"
            android:layout_marginTop="10dp"
            android:layout_height="35dp"
            android:layout_alignParentEnd="true">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@mipmap/report_new_triangle" />

            <TextView
                android:layout_width="18dp"
                android:gravity="center"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="2dp"
                android:layout_marginTop="5dp"
                android:text="@string/new_text"
                android:maxLines="1"
                app:autoSizeMaxTextSize="12sp"
                app:autoSizeMinTextSize="7sp"
                app:autoSizeStepGranularity="1sp"
                app:autoSizeTextType="uniform"
                android:includeFontPadding="false"
                android:textColor="@color/white"
                android:textSize="12sp" />
        </RelativeLayout>
    </RelativeLayout>

</RelativeLayout>