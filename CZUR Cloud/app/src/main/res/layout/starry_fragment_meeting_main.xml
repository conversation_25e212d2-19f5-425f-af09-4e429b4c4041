<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/meetMainContainer"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/meeting_main_bg_black">

    <FrameLayout
        android:id="@+id/displayLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <FrameLayout
        android:id="@+id/controlTopBarLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/starry_meeting_main_topbar_height"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />

    <RelativeLayout
        android:id="@+id/recording_rl"
        android:layout_width="wrap_content"
        android:layout_height="25dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="60dp"
        android:paddingStart="5dp"
        android:paddingEnd="5dp"
        android:background="@drawable/starry_main_timelong_bg_with_gray"
        android:visibility="invisible"
        >

        <ImageView
            android:id="@+id/recording_iv"
            android:background="@mipmap/starry_recording_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"/>

        <TextView
            android:layout_marginLeft="5dp"
            android:layout_toRightOf="@id/recording_iv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/starry_recording"
            android:textColor="@color/white"
            android:layout_centerVertical="true"/>


    </RelativeLayout>
    <FrameLayout
        android:id="@+id/controlBarLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/starry_meeting_bottombar_bg_height"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>