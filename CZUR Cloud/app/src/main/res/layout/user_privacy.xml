<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/gary_f9">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <include layout="@layout/layout_user_top_bar" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="13.5dp"
            android:visibility="visible"/>

        <RelativeLayout
            android:id="@+id/user_menu_privacy_rl"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin50"
            android:background="@color/gary_ff">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/starryMargin20"
                android:text="@string/user_privacy_user"
                android:textColor="@color/starry_text_title_color_black"
                android:textSize="@dimen/starryCompanySubTitle" />


            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/starryMargin20"
                android:background="@mipmap/starry_right_arrow"
                android:padding="10dp" />

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="1.5dp"
            android:visibility="visible"/>

        <RelativeLayout
            android:id="@+id/user_menu_privacy_rl_privacy"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin50"
            android:background="@color/gary_ff">


            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/starryMargin20"
                android:text="@string/user_privacy_privacy"
                android:textColor="@color/starry_text_title_color_black"
                android:textSize="@dimen/starryCompanySubTitle"
                android:layout_centerVertical="true"/>

            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/starryMargin20"
                android:background="@mipmap/starry_right_arrow"
                android:padding="10dp" />

        </RelativeLayout>


        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="1.5dp"
            android:visibility="visible"/>

        <RelativeLayout
            android:id="@+id/user_menu_privacy_rl_info"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin50"
            android:background="@color/gary_ff">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/starryMargin20"
                android:text="@string/user_privacy_info"
                android:textColor="@color/starry_text_title_color_black"
                android:textSize="@dimen/starryCompanySubTitle" />


            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/starryMargin20"
                android:background="@mipmap/starry_right_arrow"
                android:padding="10dp" />

        </RelativeLayout>


        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="1.5dp"
            android:visibility="visible"/>

        <RelativeLayout
            android:id="@+id/user_menu_privacy_rl_share"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin50"
            android:background="@color/gary_ff">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/starryMargin20"
                android:text="@string/user_privacy_share"
                android:textColor="@color/starry_text_title_color_black"
                android:textSize="@dimen/starryCompanySubTitle" />


            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/starryMargin20"
                android:background="@mipmap/starry_right_arrow"
                android:padding="10dp" />

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="13.5dp" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/user_logout_btn"
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:background="@color/gary_ff"
            android:gravity="center"
            android:text="@string/user_remove_account"
            android:textColor="@color/red_update"
            android:textStyle="bold"
            android:textSize="15sp" />
    </LinearLayout>


</RelativeLayout>

