<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/language_rl"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@color/gary_ff">

        <ImageView
            android:id="@+id/select_img"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="17dp"
            android:src="@mipmap/aura_home_big_right" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="17.5dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/self_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:textColor="@color/normal_blue"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/chinese_tv"
                android:layout_marginTop="3dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/normal_blue"
                android:textSize="12sp" />
        </LinearLayout>


    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginStart="17.5dp"
        android:background="@color/gray_e5" />
</LinearLayout>
