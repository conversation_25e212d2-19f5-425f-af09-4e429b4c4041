<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <RelativeLayout
        android:layout_gravity="center_horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <RelativeLayout
            android:layout_centerInParent="true"
            android:layout_width="240dp"
            android:layout_height="wrap_content">

            <com.czur.cloud.ui.component.slider.Slider
                android:layout_centerInParent="true"
                android:id="@+id/sv_slider"
                android:layout_width="240dp"
                android:layout_height="60dp"
                android:layout_centerVertical="true"
                android:background="@mipmap/slide_bg"
                android:clickable="false"
                android:max="100"
                android:progressDrawable="@android:color/transparent"
                android:splitTrack="false"
                android:thumb="@drawable/sv_thumb"
                android:thumbOffset="16dp"
                tools:ignore="UnusedAttribute" />

        </RelativeLayout>


        <TextView
            android:id="@+id/sv_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textAppearance="@style/TextAppearance.AppCompat.Medium"
            android:textColor="@android:color/white"
            android:textStyle="bold"
            tools:text="@string/slide_video" />

    </RelativeLayout>


</merge>