<?xml version="1.0" encoding="utf-8"?>
<com.lihang.ShadowLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:hl_cornerRadius="5dp"
    app:hl_shadowColor="@color/blackTransparent95"
    app:hl_layoutBackground="@color/white"
    app:hl_shadowLimit="8dp">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:layout_width="15dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginTop="10dp"
            android:adjustViewBounds="true"
            android:scaleType="fitXY"
            android:src="@mipmap/et_folder" />

        <RelativeLayout
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:layout_alignParentEnd="true">

            <CheckBox
                android:id="@+id/check"
                style="@style/BookCheckBox"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_centerInParent="true"
                android:minHeight="20dp" />

        </RelativeLayout>

        <TextView
            android:id="@+id/item_name_tv"
            android:layout_width="67dp"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:ellipsize="end"
            android:gravity="center"
            android:lines="2"
            android:text=""
            android:textColor="@color/black_22"
            android:textSize="17sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/item_size_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="7.5dp"
            android:layout_marginBottom="11dp"
            android:textColor="@color/gary_c4"
            android:textSize="10sp" />


    </RelativeLayout>

</com.lihang.ShadowLayout>




