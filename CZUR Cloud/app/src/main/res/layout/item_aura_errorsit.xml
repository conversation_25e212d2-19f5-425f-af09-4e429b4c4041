<?xml version="1.0" encoding="utf-8"?>
<com.lihang.ShadowLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:hl_cornerRadius="5dp"
    app:hl_shadowColor="@color/blackTransparent95"
    app:hl_layoutBackground="@color/white"
    app:hl_shadowLimit="8dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.facebook.drawee.view.SimpleDraweeView
            android:id="@+id/et_files_img"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="5dp"
            app:actualImageScaleType="fitCenter" />

        <RelativeLayout
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentEnd="true">

            <CheckBox
                android:id="@+id/check"
                style="@style/BookCheckBox"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_centerInParent="true"
                android:minHeight="20dp" />

        </RelativeLayout>
    </RelativeLayout>

</com.lihang.ShadowLayout>

