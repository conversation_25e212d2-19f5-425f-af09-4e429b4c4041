<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:background="@color/transparent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/copy_top_rl"
        android:layout_width="match_parent"
        android:layout_height="44dp">

        <ImageView
            android:id="@+id/back_btn"
            android:layout_width="50dp"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:padding="10dp" />


    </RelativeLayout>

    <View
        android:id="@+id/copy_empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/handwriting_result_ll"
        android:layout_below="@+id/copy_top_rl">

    </View>

    <LinearLayout
        android:id="@+id/handwriting_result_ll"
        android:layout_width="match_parent"
        android:layout_height="280dp"
        android:layout_alignParentBottom="true"
        android:background="@color/black"
        android:orientation="vertical">


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"

            android:background="@color/black_2a"
            android:orientation="vertical">

            <RelativeLayout
                android:id="@+id/result_top_bar"
                android:layout_width="match_parent"
                android:layout_height="44dp">


                <TextView
                    android:id="@+id/preview_camera_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/recognition_result"
                    android:textColor="@color/gary_c4"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/dialog_dismiss_btn"
                    android:layout_width="45dp"
                    android:layout_height="45dp"
                    android:layout_alignParentRight="true"
                    android:padding="15dp"
                    android:src="@mipmap/copy_close_icon" />


            </RelativeLayout>

            <com.czur.cloud.ui.component.scrollview.ObservableScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/result_bottom_ll"
                android:layout_below="@+id/result_top_bar"
                android:overScrollMode="never">

                <TextView
                    android:id="@+id/handwriting_result_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginLeft="29.5dp"
                    android:layout_marginTop="15dp"
                    android:layout_marginRight="29.5dp"
                    android:textColor="@color/gary_c4"
                    android:textIsSelectable="true"
                    android:textSize="15sp"
                    android:textStyle="bold" />


            </com.czur.cloud.ui.component.scrollview.ObservableScrollView>

            <LinearLayout
                android:id="@+id/result_bottom_ll"
                android:layout_width="match_parent"
                android:layout_height="49dp"
                android:layout_alignParentBottom="true"
                android:background="@color/black_2a">


                <RelativeLayout
                    android:id="@+id/handwriting_result_copy_rl"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1">

                    <RelativeLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true">

                        <ImageView
                            android:id="@+id/handwriting_result_copy_img"
                            android:layout_width="22.5dp"
                            android:layout_height="22.5dp"
                            android:layout_centerHorizontal="true"
                            android:background="@mipmap/handwriting_copy_icon" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/handwriting_result_copy_img"
                            android:layout_centerHorizontal="true"
                            android:layout_marginTop="6.5dp"
                            android:text="@string/copy"
                            android:textColor="@color/white"
                            android:textSize="9sp"
                            android:textStyle="bold" />
                    </RelativeLayout>

                </RelativeLayout>

            </LinearLayout>

        </RelativeLayout>

    </LinearLayout>
</RelativeLayout>