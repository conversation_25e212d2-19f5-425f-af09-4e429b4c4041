<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/index_bg_rl"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <LinearLayout
        android:id="@+id/index_bottom_bar"
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:layout_alignParentBottom="true"
        android:background="@color/gary_fa"
        android:baselineAligned="false"
        android:orientation="horizontal"
        android:weightSum="2">
        <LinearLayout
            android:id="@+id/home_tab_ll"
            style="@style/index_radio_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:checked="true"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/home_tab_img"
                android:layout_width="26dp"
                android:layout_height="22.5dp"
                android:src="@drawable/selector_index_equipment" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/home_tab_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="5dp"
                android:text="@string/home_tab"
                android:textColor="@drawable/selector_index_text_color"
                android:textSize="10sp" />

        </LinearLayout>
        <LinearLayout
            android:id="@+id/mall_tab_ll"
            style="@style/index_radio_button"
            android:layout_width="46dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginRight="96dp"
            android:orientation="vertical"
            android:visibility="gone">

            <ImageView
                android:id="@+id/mall_tab_img"
                android:layout_width="26dp"
                android:layout_height="22.5dp"
                android:src="@drawable/selector_index_mall" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/mall_tab_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="6dp"
                android:text="@string/mall_tab"
                android:textColor="@drawable/selector_index_text_color"
                android:textSize="10sp" />

        </LinearLayout>
        <RelativeLayout
            android:id="@+id/mine_tab_ll"
            style="@style/index_radio_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="1">

            <ImageView
                android:id="@+id/user_tab_img"
                android:layout_width="26dp"
                android:layout_height="22.5dp"
                android:layout_centerHorizontal="true"
                android:src="@drawable/selector_index_user" />

            <ImageView
                android:id="@+id/mine_tab_red_point"
                android:layout_width="26dp"
                android:layout_height="22.5dp"
                android:layout_centerHorizontal="true"
                android:src="@mipmap/index_red_point"
                android:visibility="gone" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/user_tab_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/user_tab_img"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="5dp"
                android:text="@string/mine_tab"
                android:textColor="@drawable/selector_index_text_color"
                android:textSize="10sp" />

        </RelativeLayout>
    </LinearLayout>
    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_above="@+id/index_bottom_bar"
        android:background="@color/gary_e7" />

    <FrameLayout
        android:id="@+id/index_frameLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/line" />


</RelativeLayout>

