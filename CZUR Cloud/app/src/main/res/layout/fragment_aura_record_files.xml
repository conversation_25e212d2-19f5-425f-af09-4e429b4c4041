<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gary_f9">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="44dp">


                    <RelativeLayout
                        android:id="@+id/aura_home_current_item_rl"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_alignParentLeft="true"
                        android:layout_marginEnd="@dimen/dp_15"
                        android:paddingEnd="@dimen/dp_5"
                        android:layout_toLeftOf="@+id/aura_home_unselected_top_bar_rl">


                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/aura_home_unselected_top_bar_rl"
                        android:layout_width="52dp"
                        android:layout_height="match_parent"
                        android:layout_alignParentRight="true">


                        <RelativeLayout
                            android:id="@+id/aura_home_multi_select_btn"
                            android:layout_width="52dp"
                            android:layout_height="match_parent"
                            android:layout_alignParentRight="true">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="21dp"
                                android:layout_alignParentRight="true"
                                android:layout_centerVertical="true"
                                android:layout_marginRight="17.5dp"
                                android:background="@mipmap/et_multi_select" />

                        </RelativeLayout>


                    </RelativeLayout>

                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/aura_home_select_all_btn"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:paddingLeft="18.5dp"
                        android:textColor="@color/black_22"
                        android:textSize="16sp"
                        android:visibility="gone" />


                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/aura_home_cancel_btn"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_alignParentRight="true"
                        android:gravity="center_vertical"
                        android:paddingRight="18.5dp"
                        android:textColor="@color/black_22"
                        android:textSize="16sp"
                        android:visibility="gone" />

                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/aura_files_title_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:textColor="@color/black_22"
                        android:textSize="18sp"
                        android:visibility="gone" />
                </RelativeLayout>


            </RelativeLayout>


            <com.scwang.smartrefresh.layout.SmartRefreshLayout
                android:id="@+id/refresh_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <com.scwang.smartrefresh.layout.header.ClassicsHeader
                    android:layout_width="match_parent"
                    android:layout_height="60dp"
                    app:srlDrawableMarginRight="-10dp"
                    app:srlTextFailed=""
                    app:srlTextFinish=""
                    app:srlTextLoading=""
                    app:srlTextPulling=""
                    app:srlTextRefreshing=""
                    app:srlTextRelease=""
                    app:srlTextUpdate="" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <LinearLayout
                        android:id="@+id/aura_mate_empty_rl"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:layout_gravity="center"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <ImageView
                            android:layout_width="120dp"
                            android:layout_height="wrap_content"
                            android:adjustViewBounds="true"
                            android:scaleType="fitXY"
                            android:layout_gravity="center_horizontal"
                            android:src="@mipmap/book_page_no_page_icon" />

                        <com.czur.cloud.ui.component.MediumBoldTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginTop="15.5dp"
                            android:text="@string/none_file_book_page"
                            android:textColor="@color/gary_c4"
                            android:textSize="15sp" />
                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/aura_home_recyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="8dp"
                        android:layout_marginEnd="8dp"
                        android:overScrollMode="never" />
                </RelativeLayout>
                <com.scwang.smartrefresh.layout.footer.ClassicsFooter
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    app:srlTextFailed=""
                    app:srlTextFinish=""
                    app:srlTextLoading="@string/czur_footer_loading"
                    app:srlTextNothing="@string/czur_footer_nothing"
                    app:srlTextPulling="@string/czur_footer_pulling"
                    app:srlTextRefreshing=""
                    app:srlTextRelease="" />
            </com.scwang.smartrefresh.layout.SmartRefreshLayout>
        </LinearLayout>



    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/loading_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <include
        layout="@layout/layout_no_network"
        android:visibility="gone" />

</RelativeLayout>