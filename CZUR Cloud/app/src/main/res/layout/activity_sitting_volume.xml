<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <include layout="@layout/layout_sitting_top_bar_withspace" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingLeft="20dp"
        android:paddingTop="@dimen/jingMargin30"
        android:paddingRight="20dp">

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:singleLine="true"
            app:autoSizeTextType="uniform"
            app:autoSizeMinTextSize="19sp"
            app:autoSizeMaxTextSize="22sp"
            app:autoSizeStepGranularity="1sp"
            android:text="@string/sitting_home_volume"
            android:textColor="@color/black_22"
            android:textSize="22sp"
            android:visibility="gone"/>

        <RelativeLayout
            android:layout_width="match_parent"
            android:padding="@dimen/dp_5"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/sitting_home_minus_btn"
                android:layout_width="@dimen/jingVolCircle"
                android:layout_height="@dimen/jingVolCircle"
                android:layout_centerVertical="true"
                android:layout_alignParentStart="true"
                android:padding="5dp"
                android:src="@mipmap/vol_minus_icon" />

            <ImageView
                android:id="@+id/sitting_home_plus_btn"
                android:layout_width="@dimen/jingVolCircle"
                android:layout_height="@dimen/jingVolCircle"
                android:layout_centerVertical="true"
                android:layout_alignParentEnd="true"
                android:padding="5dp"
                android:src="@mipmap/vol_plus_icon" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/sitting_home_vol_level_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:gravity="center"
                android:text="3"
                android:textColor="@color/jing_main_bg_color"
                android:textSize="24sp" />
        </RelativeLayout>

        <com.czur.cloud.ui.component.seekbar.BubbleSeekBarSitting
            android:id="@+id/seekBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:paddingLeft="2dp"
            android:paddingTop="10dp"
            android:paddingRight="2dp"
            app:bsb_always_show_bubble="false"
            app:bsb_anim_duration="0"
            app:bsb_auto_adjust_section_mark="true"
            app:bsb_bubble_color="@color/normal_blue"
            app:bsb_hide_bubble="true"
            app:bsb_max="5"
            app:bsb_min="0"
            app:bsb_progress="3"
            app:bsb_section_count="5"
            app:bsb_second_track_color="@color/jing_main_bg_color"
            app:bsb_section_first_color="@color/white"
            app:bsb_thumb_fill_color="@color/jing_main_bg_color"
            app:bsb_track_color="@color/gray_d5"
            app:bsb_show_progress_in_float="false"
            app:bsb_show_section_mark="true"
            app:bsb_show_section_text="false"
            app:bsb_show_thumb_text="false"
            app:bsb_touch_to_seek="true"
            app:bsb_seek_step_section="true"
             />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/sitting_volume_note_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:layout_marginTop="@dimen/reportTextMargin"
            android:textColor="@color/jing_alert_red_color"
            android:textSize="12sp"
            android:text="@string/sitting_valume_msg"
            android:visibility="gone" />

    </LinearLayout>

</LinearLayout>