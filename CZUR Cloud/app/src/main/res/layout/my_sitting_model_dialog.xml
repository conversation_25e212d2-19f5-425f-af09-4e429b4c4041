<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:background="@drawable/sitting_dialog_style"
    android:orientation="vertical">

    <TextView
        android:id="@+id/dialog_title"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:gravity="center"
        android:paddingTop="10dp"
        android:text="@string/sitting_model_dialog_title"
        android:textStyle="bold"
        android:textColor="@color/black_2a"
        android:textSize="20sp"
        android:visibility="visible"/>

    <TextView
        android:id="@+id/dialog_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/sitting_standar_experience30_alert"
        android:textSize="18sp"
        android:gravity="center_horizontal"
        android:padding="@dimen/jingMargin30"
        android:textColor="@color/gray_5e"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#CFCFCF" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:padding="@dimen/jingMargin10"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/sitting_model_dialog_no"
            android:textStyle="bold"
            android:textColor="@color/blue_22a3c8"
            android:textSize="20sp"
            android:padding="@dimen/jingMargin10"
            android:visibility="visible"/>

        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="#CFCFCF"
            android:visibility="visible"/>

        <TextView
            android:id="@+id/confirm"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/sitting_model_dialog_yes"
            android:textStyle="bold"
            android:padding="@dimen/jingMargin10"
            android:textColor="@color/blue_22a3c8"
            android:textSize="20sp" />
    </LinearLayout>

</LinearLayout>
