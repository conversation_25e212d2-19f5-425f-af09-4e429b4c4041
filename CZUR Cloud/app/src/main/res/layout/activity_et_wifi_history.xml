<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/gary_f9"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/white">

        <ImageView
            android:id="@+id/img_back"
            android:layout_width="29.5dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="10dp"
            android:padding="10dp"
            android:src="@mipmap/login_back_icon" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingLeft="60dp"
        android:paddingRight="60dp">

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginTop="20dp"
            android:text="@string/et_connect_wifi_to"
            android:gravity="center_vertical"
            android:textColor="@color/black_22"
            app:autoSizeTextType="uniform"
            app:autoSizeMinTextSize="19sp"
            app:autoSizeMaxTextSize="22sp"
            app:autoSizeStepGranularity="1sp"
            android:textSize="22sp" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/only_2.4G"
            android:layout_marginTop="5dp"
            android:textColor="@color/gray_ac"
            android:textSize="12sp" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="30dp"
            android:gravity="center_vertical"
            app:autoSizeTextType="uniform"
            app:autoSizeMinTextSize="14sp"
            app:autoSizeMaxTextSize="18sp"
            app:autoSizeStepGranularity="1sp"
            android:text="@string/current_network"
            android:textSize="18sp" />

        <RelativeLayout
            android:id="@+id/rl_current_net"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/btn_rec_5_gray_bg"
            android:visibility="gone">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/tv_current_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="14dp"
                android:singleLine="true"
                android:layout_marginEnd="15dp"
                android:ellipsize="end"
                android:layout_toStartOf="@+id/img_lock"
                android:textColor="@color/white"
                android:textSize="15sp" />

            <ImageView
                android:id="@+id/img_lock"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_marginEnd="14dp"
                android:visibility="gone"
                android:layout_centerVertical="true"
                android:layout_toStartOf="@+id/image"
                android:src="@mipmap/wifi_locked_white"/>

            <ImageView
                android:id="@+id/image"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="14dp"
                android:src="@mipmap/wifi_small_icon_white" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_current_no_net"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="10dp"
            android:visibility="gone"
            android:background="@drawable/btn_rec_5_gray_bg">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="110dp"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="14dp"
                android:gravity="center_vertical"
                android:text="@string/no_network_tip"
                android:textColor="@color/gary_c4"
                app:autoSizeTextType="uniform"
                app:autoSizeMinTextSize="11sp"
                app:autoSizeMaxTextSize="15sp"
                app:autoSizeStepGranularity="2sp"
                android:textSize="15sp" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="14dp"
                android:gravity="end"
                android:text="@string/net_config"
                android:textColor="@color/blue_29b0d7"
                android:textSize="15sp"
                app:autoSizeMinTextSize="11sp"
                app:autoSizeMaxTextSize="15sp"
                app:autoSizeStepGranularity="2sp"/>
        </RelativeLayout>

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:text="@string/new_network"
            android:textSize="18sp" />

        <RelativeLayout
            android:id="@+id/rl_search_new_net"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/btn_rec_5_bg">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="14dp"
                android:text="@string/search_new_network"
                android:textColor="@color/white"
                android:textSize="15sp" />

            <ImageView
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="14sp"
                android:src="@mipmap/search_wifi" />
        </RelativeLayout>

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/tv_history_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:text="@string/history_network"
            android:textSize="18sp" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_history_net"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="10dp" />
    </LinearLayout>


</LinearLayout>