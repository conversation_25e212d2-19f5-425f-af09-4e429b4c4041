<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/dialog_common_pick_bg">

    <ImageView
        android:id="@+id/image_close"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_marginEnd="10dp"
        android:padding="17dp"
        android:layout_gravity="end"
        android:scaleType="centerInside"
        android:src="@mipmap/aura_home_close_icon" />

    <com.czur.cloud.ui.component.recyclerview.MaxHeightRecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:maxHeight="260dp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/gray_ca" />
    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/btn_ensure"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_marginBottom="0dp"
        android:background="@color/gary_f9"
        android:text="@string/confirm"
        android:gravity="center"
        android:foreground="?selectableItemBackground"
        android:textColor="@color/blue_29b0d7"
        android:textSize="16sp" />
</LinearLayout>