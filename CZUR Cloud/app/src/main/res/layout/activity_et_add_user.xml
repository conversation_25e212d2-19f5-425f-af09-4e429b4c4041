<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gary_f9"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <include layout="@layout/layout_normal_white_top_bar" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="103.5dp"
        android:background="@color/white">

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="53dp"
            android:layout_marginStart="18.5dp"
            android:layout_marginTop="50.5dp"
            android:layout_toStartOf="@+id/et_add_user_search_btn">

            <ImageView
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_centerVertical="true"
                android:src="@mipmap/search_icon" />

            <EditText
                android:id="@+id/et_add_user_edt"
                android:layout_width="match_parent"
                android:layout_height="53dp"
                android:background="@null"
                android:hint="@string/et_search_hint"
                android:paddingStart="24dp"
                android:textColor="@color/black_22"
                android:textColorHint="@color/gary_c4"
                android:textSize="15sp"
                android:textStyle="bold" />
        </RelativeLayout>


        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/et_add_user_search_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:padding="17sp"
            android:text="@string/et_search"
            android:textColor="@color/blue_29b0d7"
            android:textSize="15sp" />
    </RelativeLayout>


    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/gary_c4" />


    <View
        android:id="@+id/et_search_divider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="19dp"
        android:background="@color/gary_c4"
        android:visibility="gone" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/et_add_recyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/gary_f9"
        android:overScrollMode="never" />


</LinearLayout>

