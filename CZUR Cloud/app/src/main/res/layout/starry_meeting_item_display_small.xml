<?xml version="1.0" encoding="utf-8"?>
    <com.czur.cloud.ui.starry.meeting.fragment.newmain.dragshadowlayout.DragShadowLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/shadowLayout"
        android:layout_width="@dimen/starry_main_small_width"
        android:layout_height="@dimen/starry_main_small_heigh"
        android:animateLayoutChanges="false"
        app:hl_shadowColor="#B3000000"
        app:hl_shadowLimit="16dp"
        app:hl_startColor="#B3000000"
        app:hl_endColor="#33000000">


    <!--  会议画面的容器  -->
    <com.czur.cloud.ui.starry.meeting.widget.container.VideoContainer
        android:id="@+id/displayItemContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/starry_meeting_small_bg"
        />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/smallGroup"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible"
        >

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin24"
            android:background="@color/starry_meeting_small_video_bottom_bar_bg"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            />

        <LinearLayout
            android:id="@+id/smallVideoButtomLayer"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/starryMargin24"
            android:orientation="horizontal"
            android:padding="3dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:gravity="center_vertical"
            >

            <com.czur.cloud.ui.starry.meeting.widget.VolumeView
                android:id="@+id/volumeView"
                android:layout_width="@dimen/starry_meeting_small_video_volume_width"
                android:layout_height="@dimen/starry_meeting_small_video_volume_width"
                android:layout_marginStart="2dp"
                android:visibility="gone"
                android:src="@mipmap/starry_meeting_mic_on"
                android:gravity="center_vertical"
                />

            <TextView
                android:id="@+id/itemNickNameTv"
                android:layout_width="0dp"
                android:layout_height="20dp"
                android:layout_marginStart="2dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="1"
                android:textColor="@color/white"
                android:textSize="@dimen/starry_meeting_small_video_fontsize"
                tools:text="Small Bill" />

            <com.czur.cloud.ui.starry.meeting.widget.NetworkQualityView
                android:id="@+id/itemNetworkQualityIV"
                android:layout_width="@dimen/starry_meeting_small_video_volume_width"
                android:layout_height="@dimen/starry_meeting_small_video_volume_width"
                android:layout_marginStart="2dp"
                android:layout_marginEnd="2dp"
                android:gravity="center_vertical"
                android:src="@color/transparent"
                />

        </LinearLayout>

        <com.czur.cloud.ui.starry.meeting.widget.HeadImageView
            android:id="@+id/smallHeadIv"
            android:layout_width="@dimen/starry_meeting_grid_video_width"
            android:layout_height="@dimen/starry_meeting_grid_video_width"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:roundPercent="1"
            android:visibility="gone"
            />

        <TextView
            android:id="@+id/itemNickNameTv2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5"
            android:paddingStart="@dimen/starryMargin15"
            android:paddingEnd="@dimen/dp_5"            android:textColor="@color/white"
            android:textSize="@dimen/starry_meeting_small_video_name_fontsize"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintTop_toBottomOf="@id/smallHeadIv"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            tools:text="艾华" />

    </androidx.constraintlayout.widget.ConstraintLayout>
    </com.czur.cloud.ui.starry.meeting.fragment.newmain.dragshadowlayout.DragShadowLayout>
