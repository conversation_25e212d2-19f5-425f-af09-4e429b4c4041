<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="@color/white"
    android:paddingBottom="16dp">

    <com.czur.cloud.ui.starry.meeting.widget.HeadImageView
        android:id="@+id/chatSendHeadIv"
        style="@style/chat_head_iv"
        android:src="@mipmap/starry_user_photo_default"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:roundPercent="1"/>

    <TextView
        android:id="@+id/chatSendContentTv"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toLeftOf="@id/chatSendHeadIv"
        android:layout_marginEnd="10dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="2dp"
        android:textColor="@color/white"
        app:bl_corners_radius="4dp"
        app:bl_solid_color="@color/global_accent"
        tools:text="HelloWorldHelloWorldHelloWorldHelloWorld"
        style="@style/chat_content_tv" />

    <TextView
        android:id="@+id/sendMsgErrorTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/starry_message_send_fail"
        android:textColor="@color/starry_text_color_red"
        android:textSize="12sp"
        android:layout_marginEnd="6dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/chatSendContentTv"
        app:layout_constraintRight_toLeftOf="@id/chatSendContentTv" />

</androidx.constraintlayout.widget.ConstraintLayout>