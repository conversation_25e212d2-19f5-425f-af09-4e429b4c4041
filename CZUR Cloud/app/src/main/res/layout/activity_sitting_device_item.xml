<?xml version="1.0" encoding="utf-8"?>
<com.scwang.smartrefresh.layout.SmartRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:descendantFocusability="beforeDescendants"
    app:srlEnableAutoLoadMore="false"
    app:srlEnableLoadMore="false"
    app:srlEnableOverScrollBounce="true"
    app:srlEnableOverScrollDrag="true"
    app:srlEnableRefresh="true"
    android:fitsSystemWindows="true"
    android:id="@+id/refresh_layout"
    >

    <com.scwang.smartrefresh.layout.header.ClassicsHeader
        android:id="@+id/refresh_header"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        app:srlTextFailed="@string/sitting_reflash_fail"
        app:srlTextFinish="@string/sitting_reflash_success"
        app:srlTextLoading=""
        app:srlTextPulling=""
        app:srlTextRefreshing=""
        app:srlTextRelease=""
        app:srlDrawableMarginRight="-10dp"
        app:srlDrawableArrow="@mipmap/reflash_header"
        app:srlDrawableProgress="@mipmap/reflash_header"
        app:srlFinishDuration="10000"
        app:srlTextUpdate="" />

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/refresh_header_status"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:gravity="center"
        android:background="#fff"
        android:text=""
        android:textSize="20sp"
        android:visibility="gone"
        />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scroll"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/jingNestedScrollViewMargin"
        android:layout_marginRight="@dimen/jingNestedScrollViewMargin"
        android:overScrollMode="never"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/btn_rec_10_bg_with_red"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center|center_vertical"
                android:layout_marginTop="@dimen/jingMargin10"
                android:layout_marginBottom="@dimen/jingMargin10"
                android:padding="@dimen/jingMargin10"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/show_message_im"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginEnd="@dimen/dp_5"
                    android:src="@mipmap/hd_failed_icon" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toEndOf="@+id/show_message_im"
                    android:layout_centerHorizontal="true"
                    android:textSize="16sp"
                    android:textColor="@color/white"
                    android:text="@string/sitting_ble_not_connect"/>

            </RelativeLayout>


            <RelativeLayout
                android:id="@+id/sitting_home_device_rl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/btn_rec_10_bg_with_code_green">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_above="@+id/sitting_home_device_setting"
                    android:layout_below="@+id/sitting_home_title_ll"
                    android:layout_marginTop="@dimen/jingMargin15"
                    android:layout_marginBottom="@dimen/jingMargin15"
                    android:orientation="vertical"
                    android:visibility="visible"
                    tools:ignore="NotSibling">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:orientation="vertical">

                        <RelativeLayout
                            android:id="@+id/sitting_home_circle_rl"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_horizontal">

                            <com.czur.cloud.ui.mirror.component.RoundProgress
                                android:id="@+id/socProgress"
                                android:layout_width="180dp"
                                android:layout_height="180dp"
                                android:layout_centerHorizontal="true"
                                android:layout_gravity="center"
                                android:layout_marginBottom="2dp"
                                app:maxProgress="100"
                                app:roundWidth="8dp"
                                app:textIsDisplayable="true"
                                app:textSize1="68sp" />

                            <com.czur.cloud.ui.component.MediumBoldTextView
                                android:id="@+id/socProgress_name"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentBottom="true"
                                android:layout_centerHorizontal="true"
                                android:gravity="center"
                                android:paddingBottom="@dimen/jingMargin20"
                                android:text="@string/sitting_home_sitting_right"
                                android:textColor="@color/white"
                                android:textSize="@dimen/jingMainSmallCycleTitleSize" />

                        </RelativeLayout>

                        <com.czur.cloud.ui.component.MediumBoldTextView
                            android:id="@+id/sitting_home_time"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginTop="@dimen/jingLineSpaceMargin"
                            android:text="@string/sitting_home_sitting_time"
                            android:textColor="@color/white"
                            android:textSize="@dimen/jingMainTitleSize" />

                    </LinearLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/jingLineSpaceMargin"
                        android:paddingTop="@dimen/jingMargin10">

                        <include
                            android:id="@+id/jing_circle_small_left"
                            layout="@layout/sitting_item_device_show_circle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:layout_marginStart="@dimen/jingTitleMargin"
                            android:layout_marginTop="@dimen/dp_5" />

                        <include
                            android:id="@+id/jing_circle_small_right"
                            layout="@layout/sitting_item_device_show_circle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_marginTop="@dimen/dp_5"
                            android:layout_marginEnd="@dimen/jingTitleMargin" />

                    </RelativeLayout>

                </LinearLayout>

                <RelativeLayout
                    android:id="@+id/sitting_home_title_ll"
                    android:layout_width="match_parent"
                    android:layout_height="46dp"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/sitting_home_state_img"
                        android:layout_width="6dp"
                        android:layout_height="6dp"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="10dp"
                        android:src="@drawable/jing_main_dot_with_gray" />

                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/sitting_home_state_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="5dp"
                        android:layout_toEndOf="@+id/sitting_home_state_img"
                        android:ellipsize="end"
                        android:maxWidth="100dp"
                        android:singleLine="true"
                        android:text="@string/sitting_main_status_NO"
                        android:textColor="@color/jing_main_dot_color_gray"
                        android:textSize="12sp" />

                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/sitting_home_device_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:ellipsize="end"
                        android:maxWidth="160dp"
                        android:paddingStart="@dimen/dp_5"
                        android:singleLine="true"
                        android:text="@string/Mirror"
                        android:textColor="@color/white"
                        android:textSize="16sp" />

                    <LinearLayout
                        android:id="@+id/sitting_home_share_ll"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="30dp"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_vertical"
                            android:layout_marginEnd="10dp"
                            android:src="@mipmap/jing_share" />

                    </LinearLayout>

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/loading_rl"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/btn_rec_10_bg_with_code_green"
                    android:clickable="true"
                    android:focusable="true"
                    android:visibility="gone">

                    <ImageView
                        android:id="@+id/loading_img"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_centerInParent="true"
                        android:src="@mipmap/main_loading" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/btn_rec_10_bg_with_red"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center|center_vertical"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/jingMargin10"
                    android:layout_marginStart="@dimen/jingMargin10"
                    android:layout_marginEnd="@dimen/jingMargin10"
                    android:padding="@dimen/jingMargin10"
                    android:visibility="gone">

                    <ImageView
                        android:id="@+id/show_message_im2"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_marginEnd="@dimen/dp_5"
                        android:src="@mipmap/hd_failed_icon" />

                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_toEndOf="@+id/show_message_im2"
                        android:layout_centerHorizontal="true"
                        android:textSize="16sp"
                        android:textColor="@color/white"
                        android:text="@string/sitting_ble_not_connect"/>

                </RelativeLayout>

            </RelativeLayout>

            <include
                android:id="@+id/sitting_home_device_sensitivity"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/jingMargin10"
                layout="@layout/sitting_item_device_sensitivity_panel"
                android:visibility="visible"/>

            <include
                android:id="@+id/sitting_home_device_setting"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/jingMargin10"
                layout="@layout/sitting_item_device_setting_line"
                android:visibility="visible"/>

            <RelativeLayout
                android:id="@+id/sitting_home_unbind_rl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/jingNestedScrollViewMarginTop"
                android:layout_marginBottom="@dimen/jingNestedScrollViewMarginTop"
                android:background="@drawable/btn_rec_10_bg_with_code_white">

                <TextView
                    android:id="@+id/setting_home_unbind_tv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:padding="15dp"
                    android:text="@string/sitting_home_unbind"
                    android:textAlignment="center"
                    android:textColor="@color/red_e44e4e"
                    android:textSize="@dimen/jingMainTitleSize" />
            </RelativeLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>
</com.scwang.smartrefresh.layout.SmartRefreshLayout>


