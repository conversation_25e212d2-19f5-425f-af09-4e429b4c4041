<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="horizontal"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:padding="10dp"
    android:fitsSystemWindows="true"
    android:background="@color/black_20"
    >

    <RelativeLayout
        android:id="@+id/save_loading_rl"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/blackTransparent50"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/save_loading_img"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:src="@mipmap/video_loading" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="25dp"
                android:text="@string/saving"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </LinearLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/hdview_ablum_rl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible">

        <GridView
            android:id="@+id/gridView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:columnWidth="150dp"
            android:numColumns="4"
            android:verticalSpacing="10dp"
            android:horizontalSpacing="10dp"
            android:stretchMode="columnWidth"
            android:scrollbars="none"
            android:fadeScrollbars="true"
            android:fastScrollEnabled="true"
            android:fadingEdge="none"
            android:fadingEdgeLength="10dp"
            android:transcriptMode="alwaysScroll"
            android:drawSelectorOnTop="false"
            android:layout_marginStart="@dimen/reportTextMargin"
            android:gravity="center"
            android:layout_marginEnd="80dp"/>

        <RelativeLayout
            android:layout_width="80dp"
            android:layout_height="match_parent"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true">

            <ImageView
                android:id="@+id/hdview_btn_back"
                android:layout_width="@dimen/hdview_img_size"
                android:layout_height="@dimen/hdview_img_size"
                android:layout_marginTop="25dp"
                android:layout_marginEnd="15dp"
                android:layout_centerHorizontal="true"
                android:padding="18dp"
                android:src="@mipmap/hd_close_icon"/>

        </RelativeLayout>

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/hdview_picture_rl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible">

        <com.davemorrissey.labs.subscaleview.SubsamplingScaleImageView
            android:id="@+id/save_show_img"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginEnd="80dp"
            android:background="@color/black_22"
            android:layout_marginStart="@dimen/reportTextMargin"
            android:visibility="visible" />

        <RelativeLayout
            android:layout_width="80dp"
            android:layout_height="match_parent"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true">

            <ImageView
                android:id="@+id/hdview_btn_close"
                android:layout_width="@dimen/hdview_img_size"
                android:layout_height="@dimen/hdview_img_size"
                android:layout_marginTop="25dp"
                android:layout_marginEnd="15dp"
                android:layout_centerHorizontal="true"
                android:padding="18dp"
                android:src="@mipmap/hd_close_icon"/>


            <LinearLayout
                android:id="@+id/hdview_btn_fav_ll"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_above="@+id/hdview_btn_ablum_ll"
                android:layout_marginEnd="15dp"
                android:layout_centerHorizontal="true"
                >
                <ImageView
                    android:layout_width="@dimen/hdview_img_size"
                    android:layout_height="@dimen/hdview_img_size"
                    android:padding="@dimen/hdview_img_padding"
                    android:src="@mipmap/hdview_btn_fav"
                    android:layout_gravity="center"
                    />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/hdview_fav_tv"
                    android:layout_gravity="center"
                    android:layout_marginTop="-5dp"
                    android:layout_marginBottom="5dp"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:textAlignment="center"/>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/hdview_btn_ablum_ll"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginEnd="15dp"
                android:layout_marginBottom="5dp"
                android:layout_centerHorizontal="true"
                android:layout_alignParentBottom="true"
                >
                <ImageView
                    android:layout_width="@dimen/hdview_img_size"
                    android:layout_height="@dimen/hdview_img_size"
                    android:padding="@dimen/hdview_img_padding"
                    android:src="@mipmap/hdview_btn_ablum"
                    android:layout_gravity="center"
                    />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/hdview_ablum_tv"
                    android:layout_gravity="center"
                    android:layout_marginTop="-5dp"
                    android:layout_marginBottom="5dp"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:textAlignment="center"/>
            </LinearLayout>
        </RelativeLayout>
    </RelativeLayout>

</RelativeLayout>