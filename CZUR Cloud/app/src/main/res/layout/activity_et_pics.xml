<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/gary_f9">


    <RelativeLayout
        android:id="@+id/rl_top_bar"
        android:layout_width="match_parent"
        android:minHeight="45dp"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/et_files_back_btn"
            android:layout_width="29.5dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="6dp"
            android:padding="10dp"
            android:src="@mipmap/login_back_icon" />


        <RelativeLayout
            android:id="@+id/et_pick_date_rl"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true">

            <ImageView
                android:layout_width="11dp"
                android:layout_height="7dp"
                android:layout_centerVertical="true"
                android:layout_marginStart="8dp"
                android:layout_toEndOf="@+id/et_date_title_tv"
                android:src="@mipmap/et_arrow_down" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/et_date_title_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:textColor="@color/black_22"
                android:textSize="16sp" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/et_files_unselected_top_bar_rl"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true">

            <ImageView
                android:id="@+id/et_files_title_img"
                android:layout_width="53dp"
                android:layout_height="44dp"
                android:src="@mipmap/title_end" />


            <RelativeLayout
                android:id="@+id/et_files_multi_select_btn"
                android:layout_width="59.5dp"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toRightOf="@+id/et_files_title_img"
                android:background="@color/gary_f9">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="21dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="18.5dp"
                    android:background="@mipmap/black_multi_icon" />

            </RelativeLayout>


        </RelativeLayout>


        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/et_files_title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="@color/black_22"
            android:textSize="18sp"
            android:visibility="gone" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/et_files_cancel_btn"
            android:layout_width="110dp"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:gravity="end"
            android:paddingEnd="18.5dp"
            android:layout_centerVertical="true"
            android:textColor="@color/black_22"
            android:textSize="16sp"
            android:visibility="gone" />


        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/et_files_select_all_btn"
            android:layout_width="110dp"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:layout_centerVertical="true"
            android:paddingStart="18.5dp"
            android:textColor="@color/black_22"
            android:textSize="16sp"
            android:visibility="gone" />


    </RelativeLayout>

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/et_folder_bottom_ll"
        android:layout_below="@+id/rl_top_bar">

        <com.scwang.smartrefresh.layout.header.ClassicsHeader
            android:layout_width="match_parent"
            android:layout_height="60dp"
            app:srlTextFailed=""
            app:srlTextFinish=""
            app:srlTextLoading=""
            app:srlDrawableMarginRight="-10dp"
            app:srlTextPulling=""
            app:srlTextRefreshing=""
            app:srlTextRelease=""
            app:srlTextUpdate="" />


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <RelativeLayout
                android:id="@+id/et_files_empty_rl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/gary_f9"
                android:visibility="gone">


                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="120dp"
                        android:adjustViewBounds="true"
                        android:layout_gravity="center_horizontal"
                        android:scaleType="fitXY"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/book_page_no_page_icon" />

                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="15.5dp"
                        android:text="@string/none_file_book_page"
                        android:textColor="@color/gary_c4"
                        android:textSize="15sp" />
                </LinearLayout>


            </RelativeLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/et_files_recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="@color/gary_f9"
                android:overScrollMode="never" />
        </RelativeLayout>

        <com.scwang.smartrefresh.layout.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="50dp"
            app:srlTextFailed=""
            app:srlTextFinish=""
            app:srlTextLoading="@string/czur_footer_loading"
            app:srlTextNothing="@string/czur_footer_nothing"
            app:srlTextPulling="@string/czur_footer_pulling"
            app:srlTextRefreshing=""
            app:srlTextRelease="" />

    </com.scwang.smartrefresh.layout.SmartRefreshLayout>


    <LinearLayout
        android:id="@+id/et_folder_bottom_ll"
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:layout_alignParentBottom="true"
        android:baselineAligned="false"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone">


        <RelativeLayout
            android:id="@+id/et_folder_pdf_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/black">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/et_folder_pdf_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="3dp"
                    android:background="@drawable/selector_pdf_icon" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/et_folder_pdf_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/et_folder_pdf_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="5dp"
                    android:text="@string/PDF"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/et_folder_delete_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/black">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/et_folder_delete_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="3dp"
                    android:background="@drawable/selector_delete_icon" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/et_folder_delete_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/et_folder_delete_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="5dp"
                    android:text="@string/delete"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>
    </LinearLayout>


</RelativeLayout>




