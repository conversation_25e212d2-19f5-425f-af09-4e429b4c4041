<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="44dp">

    <ImageView
        android:id="@+id/normal_back_btn"
        android:layout_width="29.5dp"
        android:layout_height="36dp"
        android:layout_centerVertical="true"
        android:layout_marginLeft="9dp"
        android:padding="10dp"
        android:src="@mipmap/login_back_icon" />


    <TextView
        android:id="@+id/normal_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:textColor="@color/account_title"
        android:textSize="18sp"
        android:textStyle="bold" />



    <TextView
        android:visibility="gone"
        android:id="@+id/et_wifi_next_step_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="10dp"
        android:layout_marginRight="10dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:text="@string/next_step"
        android:textColor="@color/blue_29b0d7"
        android:textSize="15sp"
        android:textStyle="bold" />

</RelativeLayout>


