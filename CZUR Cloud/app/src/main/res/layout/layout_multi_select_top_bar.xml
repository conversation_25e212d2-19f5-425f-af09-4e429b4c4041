<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/bookshelf_inside_top_bar"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="45dp"
    android:background="@color/gary_f9">

    <ImageView
        android:id="@+id/bookshelf_back_btn"
        android:layout_width="29.5dp"
        android:layout_height="36dp"
        android:layout_centerVertical="true"
        android:layout_marginStart="9dp"
        android:padding="10dp"
        android:src="@mipmap/login_back_icon" />

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/bookshelf_select_all_btn"
        android:layout_width="110dp"
        android:layout_height="wrap_content"
        android:paddingStart="18.5dp"
        android:textColor="@color/black_22"
        android:textSize="16sp"
        android:layout_centerVertical="true"
        android:visibility="gone" />

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/bookshelf_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:textColor="@color/account_title"
        android:textSize="18sp" />

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/bookshelf_cancel_btn"
        android:layout_width="110dp"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:gravity="end|center_vertical"
        android:layout_centerVertical="true"
        android:paddingEnd="18.5dp"
        android:textColor="@color/black_22"
        android:textSize="16sp"
        android:visibility="gone" />

    <RelativeLayout
        android:id="@+id/bookshelf_unselected_top_bar_rl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_alignParentEnd="true">

        <RelativeLayout
            android:id="@+id/bookshelf_more_btn"
            android:layout_width="44dp"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@+id/bookshelf_multi_select_btn">

            <ImageView
                android:layout_width="18dp"
                android:layout_height="17.5dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="18.5dp"
                android:background="@mipmap/book_more_icon" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/bookshelf_multi_select_btn"
            android:layout_width="44dp"
            android:layout_centerVertical="true"
            android:layout_height="wrap_content">
            <ImageView
                android:layout_width="22.5dp"
                android:layout_height="19.5dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="9dp"
                android:background="@mipmap/multi_select_icon" />

        </RelativeLayout>


    </RelativeLayout>
</RelativeLayout>





