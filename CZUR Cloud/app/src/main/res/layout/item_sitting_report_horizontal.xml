<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="2dp"
        android:layout_marginTop="@dimen/reportTextMargin"
        >

        <ImageView
            android:id="@+id/sitting_report_horizontal_chart_title"
            android:layout_width="@dimen/sitting_rectangle_width"
            android:layout_height="@dimen/sitting_rectangle_height"
            android:src="@drawable/rectangle_with_title"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/sitting_rectangle_width"
            android:layout_alignParentStart="true"
            />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/report_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black_22"
            android:textSize="@dimen/reportNameSize"
            android:text="@string/sitting_report_title_day"
            android:layout_toEndOf="@+id/sitting_report_horizontal_chart_title"
        />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/report_progress_rl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/reportTextMargin"
        android:layout_marginEnd="@dimen/reportTextMargin"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <com.github.mikephil.charting.charts.PieChart
            android:id="@+id/pie_chart"
            android:layout_width="100dp"
            android:layout_height="100dp"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:layout_margin="10dp"
            android:gravity="center"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_weight="1">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">
                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/tv_right"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawablePadding="5dp"
                        android:gravity="center"
                        android:textColor="@color/sitting_report_right"
                        android:textSize="@dimen/reportPerFontSize"
                        android:text="25"
                        />
                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/per_right"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawablePadding="5dp"
                        android:gravity="center"
                        android:textColor="@color/sitting_report_right"
                        android:textSize="@dimen/reportPerFontSize3"
                        android:text=" %"
                        />
                </LinearLayout>
                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:drawableTop="@drawable/report_dot_right"
                    android:layout_marginTop="@dimen/reportDrawablePadding"
                    android:textColor="@color/sitting_report_right"
                    android:textSize="@dimen/reportPerFontSize3"
                    android:drawablePadding="@dimen/reportDrawablePadding"
                    android:text="@string/sitting_report_per_right"
                    />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_weight="1">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">
                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/tv_micro_error"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawablePadding="5dp"
                        android:gravity="center"
                        android:textColor="@color/sitting_report_mild"
                        android:textSize="@dimen/reportPerFontSize"
                        android:text="25"
                    />
                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/per_micro_error"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawablePadding="5dp"
                        android:gravity="center"
                        android:textColor="@color/sitting_report_mild"
                        android:textSize="@dimen/reportPerFontSize3"
                        android:text=" %"
                        />
                </LinearLayout>
                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:drawableTop="@drawable/report_dot_mild"
                    android:layout_marginTop="@dimen/reportDrawablePadding"
                    android:textColor="@color/sitting_report_mild"
                    android:textSize="@dimen/reportPerFontSize3"
                    android:drawablePadding="@dimen/reportDrawablePadding"
                    android:text="@string/sitting_report_per_mild"
                    />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_weight="1">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">
                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/tv_error"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawablePadding="5dp"
                        android:gravity="center"
                        android:textColor="@color/sitting_report_moderate"
                        android:textSize="@dimen/reportPerFontSize"
                        android:text="25"
                        />
                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/per_error"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawablePadding="5dp"
                        android:gravity="center"
                        android:textColor="@color/sitting_report_moderate"
                        android:textSize="@dimen/reportPerFontSize3"
                        android:text=" %"
                        />
                </LinearLayout>
                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:drawableTop="@drawable/report_dot_moderate"
                    android:layout_marginTop="@dimen/reportDrawablePadding"
                    android:textColor="@color/sitting_report_moderate"
                    android:drawablePadding="@dimen/reportDrawablePadding"
                    android:textSize="@dimen/reportPerFontSize3"
                    android:text="@string/sitting_report_per_moderate"
                    />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_weight="1">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">
                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/tv_serious_error"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawablePadding="5dp"
                        android:gravity="center"
                        android:textColor="@color/sitting_report_serious"
                        android:textSize="@dimen/reportPerFontSize"
                        android:text="25"
                        />
                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/per_serious_error"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawablePadding="5dp"
                        android:gravity="center"
                        android:textColor="@color/sitting_report_serious"
                        android:textSize="@dimen/reportPerFontSize3"
                        android:text=" %"
                        />
                </LinearLayout>
                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:drawableTop="@drawable/report_dot_serious"
                    android:layout_marginTop="@dimen/reportDrawablePadding"
                    android:textColor="@color/sitting_report_serious"
                    android:drawablePadding="@dimen/reportDrawablePadding"
                    android:textSize="@dimen/reportPerFontSize3"
                    android:text="@string/sitting_report_per_serious"
                    />
            </LinearLayout>
        </LinearLayout>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="14dp"
        android:background="@color/gary_f9"
        android:gravity="center_vertical" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/reportLineHeight"
        android:background="@color/gary_ff">

        <TextView
            android:id="@+id/tv_report_total_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/reportTextMargin"
            android:text="@string/sitting_report_day_total"
            android:textColor="@color/black_6"
            android:textSize="@dimen/reportTitleSize" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/tv_report_total_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            android:layout_marginLeft="@dimen/reportTextMargin"
            android:singleLine="true"
            android:gravity="right"
            android:layout_marginRight="@dimen/reportTextMargin"
            android:textColor="@color/gray_bb"
            android:textSize="@dimen/reportSubTitleSize"
            android:text=""/>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="@dimen/reportTextMargin"
            android:background="@color/gray_e5" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/reportLineHeight"
        android:background="@color/gary_ff">

        <TextView
            android:id="@+id/tv_report_alert_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/reportTextMargin"
            android:text="@string/sitting_report_day_alert"
            android:textColor="@color/black_6"
            android:textSize="@dimen/reportTitleSize" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/tv_report_alert_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            android:layout_marginLeft="@dimen/reportTextMargin"
            android:singleLine="true"
            android:gravity="right"
            android:layout_marginRight="@dimen/reportTextMargin"
            android:textColor="@color/gray_bb"
            android:textSize="@dimen/reportSubTitleSize"
            android:text=""/>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="@dimen/reportTextMargin"
            android:background="@color/gray_e5" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/sitting_report_longsit_rl"
        android:layout_width="match_parent"
        android:layout_height="@dimen/reportLineHeight"
        android:background="@color/gary_ff"
        android:visibility="gone">

        <TextView
            android:id="@+id/tv_report_longsit_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/reportTextMargin"
            android:text="@string/sitting_report_day_alert"
            android:textColor="@color/black_6"
            android:textSize="@dimen/reportTitleSize" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/tv_report_longsit_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            android:layout_marginLeft="@dimen/reportTextMargin"
            android:singleLine="true"
            android:gravity="right"
            android:layout_marginRight="@dimen/reportTextMargin"
            android:textColor="@color/gray_bb"
            android:textSize="@dimen/reportSubTitleSize"
            android:text=""/>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="@dimen/reportTextMargin"
            android:background="@color/gray_e5" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/tv_report_error_time_rl"
        android:layout_width="match_parent"
        android:layout_height="@dimen/reportLineHeight"
        android:background="@color/gary_ff"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/reportTextMargin"
            android:text="@string/sitting_report_error_time"
            android:textColor="@color/black_6"
            android:textSize="@dimen/reportTitleSize" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            android:layout_centerHorizontal="true"
            android:padding="@dimen/dp_5"
            android:paddingStart="@dimen/reportTextMargin15"
            >
            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/tv_report_error_time_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:textColor="@color/gray_bb"
                android:textSize="@dimen/reportSubTitleSize"
                android:text=""/>

            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_marginStart="@dimen/dp_5"
                android:layout_marginEnd="0dp"
                android:background="@mipmap/user_right_gray_arrow"
                android:layout_gravity="center_vertical"
                android:visibility="visible"
                />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:layout_marginEnd="@dimen/reportTextMargin"
                android:textColor="@color/gray_bb"
                android:textSize="@dimen/reportSubTitleSize"
                android:text=""/>

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="@dimen/reportTextMargin"
            android:background="@color/gray_e5" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/tv_report_happy_time_rl"
        android:layout_width="match_parent"
        android:layout_height="@dimen/reportLineHeight"
        android:background="@color/gary_ff"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/reportTextMargin"
            android:text="@string/sitting_report_happy_time"
            android:textColor="@color/black_6"
            android:textSize="@dimen/reportTitleSize" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            android:layout_centerHorizontal="true"
            android:padding="@dimen/dp_5"
            android:paddingStart="@dimen/reportTextMargin15"
            >
            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/tv_report_happy_time_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:textColor="@color/gray_bb"
                android:textSize="@dimen/reportSubTitleSize"
                android:text=""/>

            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_marginStart="@dimen/dp_5"
                android:layout_marginEnd="0dp"
                android:background="@mipmap/user_right_gray_arrow"
                android:layout_gravity="center_vertical"
                android:visibility="visible"
                />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:layout_marginEnd="@dimen/reportTextMargin"
                android:textColor="@color/gray_bb"
                android:textSize="@dimen/reportSubTitleSize"
                android:text=""/>

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="@dimen/reportTextMargin"
            android:background="@color/gray_e5" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/tv_report_happy_index_rl"
        android:layout_width="match_parent"
        android:layout_height="@dimen/reportLineHeight"
        android:background="@color/gary_ff"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/reportTextMargin"
            android:text="@string/sitting_home_sitting_happy_title"
            android:textColor="@color/black_6"
            android:textSize="@dimen/reportTitleSize" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/tv_report_happy_index_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            android:layout_marginLeft="@dimen/reportTextMargin"
            android:singleLine="true"
            android:gravity="right"
            android:layout_marginRight="@dimen/reportTextMargin"
            android:textColor="@color/gray_bb"
            android:textSize="@dimen/reportSubTitleSize"
            android:text=""/>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="@dimen/reportTextMargin"
            android:background="@color/gray_e5" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/reportLineHeight"
        android:background="@color/gary_ff">

        <TextView
            android:id="@+id/tv_report_sit_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/reportTextMargin"
            android:text="@string/sitting_report_day_sit"
            android:textColor="@color/black_6"
            android:textSize="@dimen/reportTitleSize" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/tv_report_sit_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            android:layout_marginLeft="@dimen/reportTextMargin"
            android:singleLine="true"
            android:layout_marginRight="@dimen/reportTextMargin"
            android:textColor="@color/gray_bb"
            android:textSize="@dimen/reportSubTitleSize"
            android:text=""/>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="@dimen/reportTextMargin"
            android:background="@color/gray_e5" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/sitting_report_trend_rl"
        android:layout_width="match_parent"
        android:layout_height="@dimen/reportLineHeight"
        android:background="@color/gary_ff"
        android:visibility="visible">

        <TextView
            android:id="@+id/tv_report_trend_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/reportTextMargin"
            android:text="@string/sitting_report_day_trend"
            android:textColor="@color/black_6"
            android:textSize="@dimen/reportTitleSize" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            android:layout_marginLeft="@dimen/reportTextMargin"
            android:layout_marginRight="@dimen/reportTextMargin"
            >

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:textColor="@color/gray_bb"
                android:textSize="@dimen/reportSubTitleSize"
                android:text=""/>

            <ImageView
                android:id="@+id/sitting_report_trend_iv"
                android:layout_width="8dp"
                android:layout_height="12dp"
                android:layout_marginStart="@dimen/dp_5"
                android:layout_marginEnd="@dimen/dp_5"
                android:layout_gravity="center_vertical"
                android:visibility="visible"
                />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/tv_report_trend_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:textColor="@color/gray_bb"
                android:textSize="@dimen/reportSubTitleSize"
                android:text=""/>

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="@dimen/reportTextMargin"
            android:background="@color/gray_e5" />
    </RelativeLayout>

</LinearLayout>