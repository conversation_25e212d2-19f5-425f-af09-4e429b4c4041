<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gary_f9"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <include
        android:id="@+id/user_bind_email_top_bar"
        layout="@layout/layout_user_top_bar" />

    <LinearLayout
        android:id="@+id/user_bind_email_ll"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:layout_below="@+id/user_bind_email_top_bar"
        android:background="@color/gary_ff"
        android:orientation="vertical">

        <EditText
            android:id="@+id/user_bind_email_edt"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_marginLeft="17dp"
            android:background="@null"
            android:digits="@string/email_digits"
            android:hint="@string/user_input_email"
            android:singleLine="true"
            android:textColor="@color/normal_blue"
            android:textColorHint="@color/gary_c4"
            android:textSize="15sp"
            android:textStyle="bold" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginLeft="17dp"
            android:background="@color/gary_ef" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:layout_below="@+id/user_bind_email_ll"
        android:background="@color/gary_ff"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/user_bind_email_code_edt"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginLeft="17dp"
                android:background="@null"
                android:digits="@string/password_digits"
                android:hint="@string/identifying_code"
                android:inputType="number"
                android:maxLength="6"
                android:textColor="@color/normal_blue"
                android:textColorHint="@color/gary_c4"
                android:textSize="15sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/user_bind_email_send_code_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="17.5dp"
                android:text="@string/user_send_identifying_code"
                android:textColor="@color/blue_29b0d7"
                android:textStyle="bold" />

        </RelativeLayout>


        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/gary_ef" />
    </LinearLayout>

    <com.czur.cloud.ui.component.ProgressButton
        android:id="@+id/user_bind_email_btn"
        android:layout_width="250dp"
        android:layout_height="40dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="30dp"
        android:layout_marginTop="23dp"
        android:background="@drawable/selector_register_btn"
        app:progress_btn_tv="@string/confirm_text" />

</LinearLayout>