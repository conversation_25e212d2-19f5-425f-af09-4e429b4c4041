<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="25dp"
            android:padding="5dp"
            android:text="欢迎使用成者CZUR"
            android:textColor="#282828"
            android:textSize="20sp"
            android:textStyle="bold" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="300dp"
            android:paddingTop="20dp"
            android:paddingBottom="20dp">

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp"
                android:lineSpacingMultiplier="1.2"
                android:textColor="#282828"
                android:textSize="16sp" />
        </ScrollView>

        <View
            android:id="@+id/view_1"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#E7E7E7" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:padding="12dp"
                android:text="不同意"
                android:textColor="#282828"
                android:textStyle="bold" />

            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="#E7E7E7" />

            <TextView
                android:id="@+id/tv_agree"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_below="@+id/view_1"
                android:layout_toRightOf="@+id/view_2"
                android:layout_weight="1"
                android:gravity="center"
                android:padding="12dp"
                android:text="同意"
                android:textColor="#282828"
                android:textStyle="bold" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
