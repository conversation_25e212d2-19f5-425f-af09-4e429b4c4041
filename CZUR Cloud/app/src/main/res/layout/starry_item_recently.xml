<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/starry_item_company_rl"
    android:layout_width="match_parent"
    android:background="@color/white"
    android:gravity="center_vertical"
    android:layout_height="@dimen/starryMargin60">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_toStartOf="@+id/starry_item_recently_time_ll"
        android:layout_centerVertical="true"
        android:gravity="center_vertical"
        android:orientation="horizontal">

    <com.czur.cloud.ui.starry.meeting.widget.BreathView
        android:id="@+id/starry_item_recently_red_point"
        android:layout_width="@dimen/starryMargin10"
        android:layout_height="@dimen/starryMargin10"
        android:layout_marginStart="@dimen/starryMargin20"
        android:background="@drawable/circle_red"
        android:visibility="visible" />

    <TextView
        android:id="@+id/starry_item_recently_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/starryMargin10"
        android:ellipsize="end"
        android:singleLine="true"
        android:layout_weight="1"
        android:textColor="@color/starry_text_title_color_black"
        android:textSize="@dimen/starryFontSize16"
        tools:text="成者科技" />

        <TextView
            android:id="@+id/starry_item_recently_nojoin"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/starryMargin5"
            android:layout_marginEnd="@dimen/starryMargin5"
            android:text="@string/starry_recently_nojoin"
            android:textColor="@color/starry_text_color_gray"
            android:textSize="@dimen/starryCompanySubValue"
            android:visibility="visible" />

        <TextView
            android:id="@+id/starry_item_recently_status_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/starryMargin10"
            android:layout_marginEnd="@dimen/starryMargin20"
            android:text="@string/starry_recently_join"
            android:textColor="@color/starry_text_color_white"
            android:textSize="@dimen/starryCompanyBtnTitle"
            android:background="@drawable/starry_btn_rec_5_bg_with_red"
            android:padding="@dimen/starryMargin10"
            android:visibility="gone"/>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/starry_item_recently_time_ll"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/starryMargin10"
        android:layout_marginEnd="@dimen/starryMargin20"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/starry_item_recently_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="2021/12/31"
            android:textColor="@color/starry_text_color_gray"
            android:textSize="@dimen/starryCompanySubValue"
            android:layout_marginStart="@dimen/dp_5"
            android:layout_marginEnd="@dimen/dp_5"
            android:visibility="visible" />
        <ImageView
            android:id="@+id/starry_item_recently_arrow"
            android:layout_width="@dimen/starryMargin8"
            android:layout_height="@dimen/starryMargin15"
            android:background="@mipmap/starry_right_arrow"
            android:padding="@dimen/starryMargin10"
            android:visibility="visible"/>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="@dimen/starryMargin40"
        android:background="@color/starry_list_liner_color" />
</RelativeLayout>