<?xml version="1.0" encoding="utf-8"?>
<com.scwang.smartrefresh.layout.SmartRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/refresh_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:srlEnableLoadMore="false"
    app:srlEnableOverScrollBounce="true"
    app:srlEnableOverScrollDrag="true"
    app:srlEnableRefresh="true">

    <com.scwang.smartrefresh.layout.header.ClassicsHeader
        android:layout_width="match_parent"
        android:layout_height="60dp"
        app:srlDrawableMarginRight="-10dp"
        app:srlTextFailed="@string/sitting_reflash_fail"
        app:srlTextFinish="@string/sitting_reflash_success"
        app:srlTextLoading=""
        app:srlTextPulling=""
        app:srlTextRefreshing=""
        app:srlTextRelease=""
        app:srlTextUpdate="" />

    <ScrollView
        android:id="@+id/fragment_day_sv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:fadingEdge="vertical"
        android:scrollbars="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <RelativeLayout
                    android:id="@+id/data_rl"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="visible">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="visible">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/jingMargin30"
                            android:layout_marginBottom="@dimen/jingMargin30"
                            android:orientation="horizontal">

                            <com.czur.cloud.ui.component.MediumBoldTextView
                                android:id="@+id/tv_report_right_value"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true"
                                android:singleLine="true"
                                android:text="18"
                                android:textColor="@color/black"
                                android:textSize="80sp" />

                            <TextView
                                android:id="@+id/tv_report_alert_han"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignBaseline="@+id/tv_report_right_value"
                                android:layout_gravity="bottom"
                                android:layout_toEndOf="@+id/tv_report_right_value"
                                android:paddingStart="10dp"
                                android:singleLine="true"
                                android:text="%"
                                android:textColor="@color/black"
                                android:textSize="@dimen/reportHappyHand" />

                            <TextView
                                android:id="@+id/tv_report_alert_per"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignBaseline="@+id/tv_report_right_value"
                                android:layout_toEndOf="@+id/tv_report_alert_han"
                                android:paddingStart="5dp"
                                android:singleLine="true"
                                android:text="@string/sitting_long_sit_per"
                                android:textColor="@color/black"
                                android:textSize="@dimen/reportHappyHand"
                                android:visibility="invisible" />
                        </RelativeLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="14dp"
                            android:background="@color/gary_f9"
                            android:gravity="center_vertical" />

                        <com.czur.cloud.ui.component.MediumBoldTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/reportTextMargin15"
                            android:layout_marginTop="@dimen/reportTextMargin"
                            android:text="@string/sitting_home_sitting_happy_title"
                            android:textColor="@color/black_22"
                            android:textSize="@dimen/reportNameSize" />

                        <com.github.mikephil.charting.charts.BarChart
                            android:id="@+id/chart1"
                            android:layout_width="match_parent"
                            android:layout_height="270dp"
                            android:layout_marginStart="6dp"
                            android:layout_marginEnd="-15dp"
                            android:paddingBottom="20dp" />

                        <LinearLayout
                            android:id="@+id/happy_time_picture_list"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:visibility="gone">

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="14dp"
                                android:background="@color/gary_f9"
                                android:gravity="center_vertical" />
                            <View
                                android:layout_width="match_parent"
                                android:layout_height="14dp"
                                android:gravity="center_vertical" />

                            <RelativeLayout
                                android:id="@+id/rl_tool_bar_switch"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <com.czur.cloud.ui.component.MediumBoldTextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_centerVertical="true"
                                    android:layout_marginStart="@dimen/reportTextMargin15"
                                    android:layout_marginTop="@dimen/reportTextMargin"
                                    android:text="@string/sitting_home_sitting_happy_title_picture"
                                    android:textColor="@color/black_22"
                                    android:textSize="@dimen/reportNameSize" />

                                <com.github.iielse.switchbutton.SwitchView
                                    android:id="@+id/sitting_happy_time_switch"
                                    android:layout_width="@dimen/jingMargin50"
                                    android:layout_alignParentEnd="true"
                                    android:layout_centerVertical="true"
                                    android:layout_marginEnd="@dimen/reportTextMargin15"
                                    app:isOpened="true"
                                    app:primaryColor="@color/jing_main_bg_color"
                                    app:primaryColorDark="@color/jing_main_bg_color"
                                    app:ratioAspect="0.6"
                                    android:layout_height="30dp"/>

                            </RelativeLayout>

                            <RelativeLayout
                                android:id="@+id/rl_tool_bar"
                                android:layout_width="match_parent"
                                android:layout_marginBottom="@dimen/dp_5"
                                android:padding="@dimen/jingMargin2"
                                android:layout_height="wrap_content">

                                <com.czur.cloud.ui.component.MediumBoldTextView
                                    android:id="@+id/et_note_title_tv"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentStart="true"
                                    android:layout_centerInParent="true"
                                    android:layout_marginStart="@dimen/reportTextMargin"
                                    android:layout_marginEnd="@dimen/reportTextMargin"
                                    android:text="@string/sitting_report_happy_time_msg1"
                                    android:textColor="@color/gray_99"
                                    android:textSize="@dimen/reportSubTitleSizeSmall"
                                    android:visibility="visible" />

                                <com.czur.cloud.ui.component.MediumBoldTextView
                                    android:id="@+id/et_alert_title_tv"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentStart="true"
                                    android:layout_centerInParent="true"
                                    android:layout_marginStart="@dimen/reportTextMargin"
                                    android:layout_marginEnd="@dimen/reportTextMargin"
                                    android:text="@string/sitting_report_happy_time_close"
                                    android:gravity="center_vertical"
                                    android:textColor="@color/gray_99"
                                    android:textSize="@dimen/reportSubTitleSizeSmall"
                                    android:visibility="gone" />

                                <RelativeLayout
                                    android:layout_width="59.5dp"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentEnd="true"
                                    android:layout_toEndOf="@+id/et_note_title_tv"
                                    android:background="@color/gary_f9"
                                    android:visibility="gone">

                                    <ImageView
                                        android:layout_width="24dp"
                                        android:layout_height="21dp"
                                        android:layout_alignParentEnd="true"
                                        android:layout_centerVertical="true"
                                        android:layout_marginEnd="18.5dp"
                                        android:background="@mipmap/black_multi_icon" />
                                </RelativeLayout>

                            </RelativeLayout>

                            <RelativeLayout
                                android:id="@+id/sitting_happy_select_bar_rl"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/jingMargin10"
                                android:layout_marginBottom="@dimen/jingMargin10"
                                android:visibility="visible"
                                >

                                <com.czur.cloud.ui.component.MediumBoldTextView
                                    android:id="@+id/et_files_select_all_btn"
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:gravity="center_vertical"
                                    android:paddingLeft="@dimen/reportTextMargin15"
                                    android:textColor="@color/jing_standar_red_color"
                                    android:textSize="@dimen/reportNameSize"
                                    android:visibility="gone" />

                                <com.czur.cloud.ui.component.MediumBoldTextView
                                    android:id="@+id/et_files_no_title_tv"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_centerVertical="true"
                                    android:layout_marginLeft="@dimen/jingMargin60"
                                    android:layout_marginRight="@dimen/jingMargin60"
                                    android:ellipsize="marquee"
                                    android:focusable="true"
                                    android:focusableInTouchMode="true"
                                    android:gravity="center"
                                    android:marqueeRepeatLimit="marquee_forever"
                                    android:scrollHorizontally="true"
                                    android:singleLine="true"
                                    android:textColor="@color/black_22"
                                    android:textSize="@dimen/reportNameSize" />

                                <com.czur.cloud.ui.component.MediumBoldTextView
                                    android:id="@+id/et_files_title_tv"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_centerInParent="true"
                                    android:textColor="@color/black_22"
                                    android:textSize="@dimen/reportNameSize"
                                    android:visibility="visible" />

                                <com.czur.cloud.ui.component.MediumBoldTextView
                                    android:id="@+id/et_files_cancel_btn"
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_alignParentEnd="true"
                                    android:gravity="center_vertical"
                                    android:paddingEnd="@dimen/reportTextMargin15"
                                    android:textSize="@dimen/reportNameSize"
                                    android:textColor="@color/black_22"
                                    android:visibility="gone" />

                                <RelativeLayout
                                    android:id="@+id/et_files_unselected_top_bar_rl"
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_alignParentEnd="true">

                                    <RelativeLayout
                                        android:id="@+id/et_files_multi_select_btn"
                                        android:layout_width="@dimen/jingMargin70"
                                        android:layout_height="match_parent">

                                        <com.czur.cloud.ui.component.MediumBoldTextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_alignParentEnd="true"
                                            android:layout_centerInParent="true"
                                            android:layout_marginEnd="@dimen/reportTextMargin15"
                                            android:text="@string/error_sit_btn_select"
                                            android:textColor="@color/jing_main_bg_color"
                                            android:textSize="@dimen/reportNameSize"
                                            android:visibility="visible" />

                                    </RelativeLayout>
                                </RelativeLayout>
                            </RelativeLayout>

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginTop="@dimen/dp_5">

                                <LinearLayout
                                    android:id="@+id/ll_empty"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_centerInParent="true"
                                    android:orientation="vertical"
                                    android:visibility="visible">

                                    <ImageView
                                        android:layout_width="70dp"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_horizontal"
                                        android:layout_marginTop="@dimen/jingMargin30"
                                        android:adjustViewBounds="true"
                                        android:scaleType="fitXY"
                                        android:src="@mipmap/error_sit_no_picture" />

                                    <com.czur.cloud.ui.component.MediumBoldTextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_horizontal"
                                        android:layout_marginTop="15.5dp"
                                        android:text="@string/sitting_happy_no_picture"
                                        android:textColor="@color/gary_c4"
                                        android:textSize="@dimen/reportSubTitleSize" />
                                </LinearLayout>

                                <androidx.recyclerview.widget.RecyclerView
                                    android:id="@+id/recycler_view"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_marginStart="8dp"
                                    android:layout_marginEnd="8dp"
                                    android:overScrollMode="never" />
                            </RelativeLayout>

                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:gravity="center_vertical" />

                    </LinearLayout>

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/empty_rl"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="70dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginTop="250dp"
                            android:adjustViewBounds="true"
                            android:scaleType="fitXY"
                            android:src="@mipmap/report_no_data_icon" />

                        <com.czur.cloud.ui.component.MediumBoldTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginTop="15.5dp"
                            android:layout_marginBottom="150dp"
                            android:text="@string/empty_report"
                            android:textColor="@color/gary_c4"
                            android:textSize="15sp" />
                    </LinearLayout>

                </RelativeLayout>
            </RelativeLayout>


        </LinearLayout>

    </ScrollView>

    <com.scwang.smartrefresh.layout.footer.ClassicsFooter
        android:layout_width="match_parent"
        android:layout_height="50dp"
        app:srlTextFailed=""
        app:srlTextFinish=""
        app:srlTextLoading="@string/czur_footer_loading"
        app:srlTextNothing="@string/czur_footer_nothing"
        app:srlTextPulling="@string/czur_footer_pulling"
        app:srlTextRefreshing=""
        app:srlTextRelease="" />

</com.scwang.smartrefresh.layout.SmartRefreshLayout>
