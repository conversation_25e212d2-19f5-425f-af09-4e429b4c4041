<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <include
        android:id="@+id/sitting_standar_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        layout="@layout/layout_sitting_top_bar"
         />

    <ScrollView
        android:id="@+id/sitting_standar_sv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="80dp"
        android:layout_below="@+id/sitting_standar_bar">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/jingMargin10">

        <View
            android:id="@+id/sitting_standar_spaceview1"
            android:layout_width="match_parent"
            android:layout_height="@dimen/jingMargin20"/>

        <ImageView
            android:id="@+id/sitting_standar_bigimg"
            android:layout_width="wrap_content"
            android:layout_height="300dp"
            android:scaleType="fitCenter"
            android:layout_centerHorizontal="true"
            android:layout_below="@+id/sitting_standar_spaceview1"
            android:padding="@dimen/dp_5"
            android:src="@mipmap/sitting_wrong"
            />

        <View
            android:id="@+id/sitting_standar_spaceview2"
            android:layout_width="match_parent"
            android:layout_below="@+id/sitting_standar_bigimg"
            android:layout_height="@dimen/jingMargin20"/>

        <RelativeLayout
            android:id="@+id/sitting_standar_note_tile_rl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/sitting_standar_spaceview2"
            android:padding="@dimen/jingMargin20"
            >
            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/sitting_standar_note_tile"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/sitting_standar_note"
                android:layout_centerHorizontal="true"
                android:textColor="@color/jing_main_bg_color"
                android:textSize="20sp"/>

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/sitting_standar_note"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/sitting_standar_note_tile"
                android:gravity="left"
                android:padding="@dimen/jingMargin20"
                android:text="@string/sitting_standar_sitting_note"
                android:textColor="@color/gray_99"
                android:textSize="16sp" />

        </RelativeLayout>


        </RelativeLayout>

    </ScrollView>

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/confirm"
        android:layout_width="250dp"
        android:layout_height="40dp"
        android:background="@drawable/btn_rec_5_bg_with_code_green"
        android:gravity="center"
        android:text="@string/sitting_standar_sitting_note_dialog_ok"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:visibility="visible"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/jingMargin10"
        android:layout_marginBottom="@dimen/jingMargin30"
        />

</RelativeLayout>
