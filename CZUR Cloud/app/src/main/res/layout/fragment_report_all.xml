<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:srlEnableLoadMore="false"
        app:srlEnableOverScrollBounce="true"
        app:srlEnableOverScrollDrag="true"
        app:srlEnableRefresh="true">

        <com.scwang.smartrefresh.layout.header.ClassicsHeader
            android:layout_width="match_parent"
            android:layout_height="60dp"
            app:srlDrawableMarginRight="-10dp"
            app:srlTextFailed=""
            app:srlTextFinish=""
            app:srlTextLoading=""
            app:srlTextPulling=""
            app:srlTextRefreshing=""
            app:srlTextRelease=""
            app:srlTextUpdate="" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/report_recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:overScrollMode="never"
                android:scrollbars="none">

            </androidx.recyclerview.widget.RecyclerView>

            <RelativeLayout
                android:id="@+id/empty_rl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="70dp"
                        android:layout_height="wrap_content"
                        android:adjustViewBounds="true"
                        android:scaleType="fitXY"
                        android:layout_gravity="center_horizontal"
                        android:src="@mipmap/report_no_data_icon" />

                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="15.5dp"
                        android:text="@string/empty_report"
                        android:textColor="@color/gary_c4"
                        android:textSize="15sp" />
                </LinearLayout>

            </RelativeLayout>
        </RelativeLayout>

    </com.scwang.smartrefresh.layout.SmartRefreshLayout>

</LinearLayout>