<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/user_top_bar"
    android:layout_width="match_parent"
    android:layout_height="@dimen/starry_top_bar_height"
    android:background="@color/gary_ff">

    <TextView
        android:id="@+id/msg_top_select_all"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/starryMargin10"
        android:padding="@dimen/starryMargin10"
        android:text="@string/starry_msg_select_all"
        android:textColor="@color/black"
        android:textSize="@dimen/starryTopBtnSize"
        android:visibility="visible"/>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center_vertical"
        >

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/msg_top_select_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/account_title"
            android:text=""
            android:maxLines="1"
            android:ellipsize="end"
            android:maxEms="10"
            android:textSize="@dimen/starryTopTitleSize" />
    </LinearLayout>

    <TextView
        android:id="@+id/msg_top_select_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/starryMargin10"
        android:padding="@dimen/starryMargin10"
        android:text="@string/starry_msg_select_cancel"
        android:textColor="@color/black"
        android:textSize="@dimen/starryTopBtnSize"
        android:visibility="visible"/>

</RelativeLayout>

