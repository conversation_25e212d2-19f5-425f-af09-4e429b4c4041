<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_alignParentBottom="true"
    android:layout_gravity="bottom"
    android:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="37.5dp"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="36.5dp"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="@string/et_ocr_language"
                android:textColor="@color/black_22"
                android:textSize="12sp"
                android:textStyle="bold" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_gravity="bottom"
                android:background="@color/gary_ef" />


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="39dp"
            android:layout_centerInParent="true"
            android:layout_marginTop="22dp"
            android:orientation="horizontal">


            <RelativeLayout
                android:id="@+id/ocr_chinese_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/ocr_chinese_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/et_ocr_chinese"
                    android:gravity="center"
                    android:textColor="@color/blue_29b0d7"
                    android:textSize="17sp" />


                <ImageView
                    android:id="@+id/ocr_chinese_img"
                    android:layout_width="14dp"
                    android:layout_height="10dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9.5dp"
                    android:layout_toRightOf="@+id/ocr_chinese_tv"
                    android:src="@mipmap/blue_right_icon" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ocr_chinese_taiwan_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/ocr_chinese_taiwan_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/et_ocr_chinese_taiwan"
                    android:gravity="center"
                    android:textColor="@color/black_22" />


                <ImageView
                    android:id="@+id/ocr_chinese_taiwan_img"
                    android:layout_width="14dp"
                    android:layout_height="10dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9.5dp"
                    android:layout_toRightOf="@+id/ocr_chinese_taiwan_tv"
                    android:src="@mipmap/blue_right_icon"
                    android:visibility="gone" />

            </RelativeLayout>


            <RelativeLayout
                android:id="@+id/ocr_english_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/ocr_english_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/et_ocr_english"
                    android:textColor="@color/black_22"
                    android:textSize="17sp" />


                <ImageView
                    android:id="@+id/ocr_english_img"
                    android:layout_width="14dp"
                    android:layout_height="10dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9.5dp"
                    android:layout_toRightOf="@+id/ocr_english_tv"
                    android:src="@mipmap/blue_right_icon"
                    android:visibility="gone" />

            </RelativeLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="39dp"
            android:layout_centerInParent="true"
            android:orientation="horizontal">


            <RelativeLayout
                android:id="@+id/ocr_french_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/ocr_french_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/et_ocr_french"
                    android:textColor="@color/black_22"
                    android:textSize="17sp" />


                <ImageView
                    android:id="@+id/ocr_french_img"
                    android:layout_width="14dp"
                    android:layout_height="10dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9.5dp"
                    android:layout_toRightOf="@+id/ocr_french_tv"
                    android:src="@mipmap/blue_right_icon"
                    android:visibility="gone" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ocr_italian_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/ocr_italian_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/et_ocr_italian"
                    android:textColor="@color/black_22"
                    android:textSize="17sp" />


                <ImageView
                    android:id="@+id/ocr_italian_img"
                    android:layout_width="14dp"
                    android:layout_height="10dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9.5dp"
                    android:layout_toRightOf="@+id/ocr_italian_tv"
                    android:src="@mipmap/blue_right_icon"
                    android:visibility="gone" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ocr_spanish_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/ocr_spanish_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/et_ocr_spanish"
                    android:textColor="@color/black_22"
                    android:textSize="17sp" />


                <ImageView
                    android:id="@+id/ocr_spanish_img"
                    android:layout_width="14dp"
                    android:layout_height="10dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9.5dp"
                    android:layout_toRightOf="@+id/ocr_spanish_tv"
                    android:src="@mipmap/blue_right_icon"
                    android:visibility="gone" />

            </RelativeLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="39dp"
            android:layout_centerInParent="true"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/ocr_portuguese_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/ocr_portuguese_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/et_ocr_portuguese"
                    android:textColor="@color/black_22"
                    android:textSize="17sp" />


                <ImageView
                    android:id="@+id/ocr_portuguese_img"
                    android:layout_width="14dp"
                    android:layout_height="10dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9.5dp"
                    android:layout_toRightOf="@+id/ocr_portuguese_tv"
                    android:src="@mipmap/blue_right_icon"
                    android:visibility="gone" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ocr_swedish_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/ocr_swedish_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/et_ocr_swedish"
                    android:textColor="@color/black_22"
                    android:textSize="17sp" />


                <ImageView
                    android:id="@+id/ocr_swedish_img"
                    android:layout_width="14dp"
                    android:layout_height="10dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9.5dp"
                    android:layout_toRightOf="@+id/ocr_swedish_tv"
                    android:src="@mipmap/blue_right_icon"
                    android:visibility="gone" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ocr_danish_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/ocr_danish_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/et_ocr_danish"
                    android:textColor="@color/black_22"
                    android:textSize="17sp" />


                <ImageView
                    android:id="@+id/ocr_danish_img"
                    android:layout_width="14dp"
                    android:layout_height="10dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9.5dp"
                    android:layout_toRightOf="@+id/ocr_danish_tv"
                    android:src="@mipmap/blue_right_icon"
                    android:visibility="gone" />

            </RelativeLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="39dp"
            android:layout_centerInParent="true"
            android:layout_marginBottom="39dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/ocr_russian_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/ocr_russian_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/et_ocr_russian"
                    android:textColor="@color/black_22"
                    android:textSize="17sp" />


                <ImageView
                    android:id="@+id/ocr_russian_img"
                    android:layout_width="14dp"
                    android:layout_height="10dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9.5dp"
                    android:layout_toRightOf="@+id/ocr_russian_tv"
                    android:src="@mipmap/blue_right_icon"
                    android:visibility="gone" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ocr_japaneses_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/ocr_japaneses_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/et_ocr_japaneses"
                    android:textColor="@color/black_22"
                    android:textSize="17sp" />


                <ImageView
                    android:id="@+id/ocr_japaneses_img"
                    android:layout_width="14dp"
                    android:layout_height="10dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9.5dp"
                    android:layout_toRightOf="@+id/ocr_japaneses_tv"
                    android:src="@mipmap/blue_right_icon"
                    android:visibility="gone" />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">


            </RelativeLayout>
        </LinearLayout>
        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/gray_e5"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="49dp"
            android:layout_centerInParent="true"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/ocr_cancel_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/cancel"
                    android:textColor="@color/black_22"
                    android:textSize="17sp" />


            </RelativeLayout>
            <View
                android:layout_width="0.5dp"
                android:layout_height="match_parent"
                android:background="@color/gray_e5"/>
            <RelativeLayout
                android:id="@+id/ocr_confirm_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/confirm_text"
                    android:textColor="@color/blue_29b0d7"
                    android:textSize="17sp" />




            </RelativeLayout>


        </LinearLayout>

    </LinearLayout>


</RelativeLayout>
