<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/starry_item_company_rl"
    android:layout_width="match_parent"
    android:background="@color/white"
    android:layout_height="@dimen/starryMargin70">

    <TextView
        android:id="@+id/starry_item_company_icon"
        android:layout_width="@dimen/starryMargin35"
        android:layout_height="@dimen/starryMargin35"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/starryMargin30"
        android:background="@drawable/starry_circle_with_company_item"
        android:gravity="center"
        android:text=""
        android:textColor="@color/white"
        android:textSize="@dimen/starryFontSize20" />

    <TextView
        android:id="@+id/starry_item_company_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/starryMargin10"
        android:layout_marginEnd="@dimen/starryMargin10"
        android:layout_toStartOf="@+id/starry_item_company_join_ll"
        android:layout_toEndOf="@+id/starry_item_company_icon"
        android:ellipsize="end"
        android:maxLines="1"
        android:text=""
        android:textColor="@color/title_black_color"
        android:textSize="@dimen/starryCompanySubTitle"
        android:visibility="visible" />

    <LinearLayout
        android:id="@+id/starry_item_company_join_ll"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/starry_item_company_btn_ll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/starryMargin20"
            android:orientation="horizontal"
            android:visibility="visible">

            <TextView
                android:id="@+id/starry_item_company_reject_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/starryMargin15"
                android:paddingTop="@dimen/dp_5"
                android:paddingEnd="@dimen/starryMargin15"
                android:paddingBottom="@dimen/dp_5"
                android:text="@string/starry_company_reject_btn"
                android:textColor="@color/starry_text_color_red"
                android:textSize="@dimen/starryCompanySubTitle" />

            <TextView
                android:id="@+id/starry_item_company_join_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/starryMargin15"
                android:paddingTop="@dimen/dp_5"
                android:paddingEnd="@dimen/starryMargin15"
                android:paddingBottom="@dimen/dp_5"
                android:text="@string/starry_company_join_btn"
                android:textColor="@color/starry_btn_blue"
                android:textSize="@dimen/starryCompanySubTitle" />
        </LinearLayout>

        <TextView
            android:id="@+id/starry_item_company_expried_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginEnd="@dimen/starryMargin20"
            android:text="@string/starry_company_list_expried"
            android:textColor="@color/starry_delete_red"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/starry_item_company_arrow"
            android:layout_width="@dimen/starryMargin8"
            android:layout_height="@dimen/starryMargin15"
            android:layout_gravity="center"
            android:layout_marginEnd="@dimen/starryMargin20"
            android:background="@mipmap/starry_right_arrow"
            android:padding="@dimen/starryMargin10" />

    </LinearLayout>

    <View
        android:id="@+id/company_list_line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="@dimen/starryMargin30"
        android:background="@color/starry_list_liner_color" />
</RelativeLayout>