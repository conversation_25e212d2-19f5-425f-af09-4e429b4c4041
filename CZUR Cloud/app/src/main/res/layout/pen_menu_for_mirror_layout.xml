<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/pen_layout"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:gravity="center"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/select_layout"
        android:background="@drawable/eshare_circle_color_gray"
        android:layout_marginRight="@dimen/starryMargin10">
    <ImageView
        android:id="@+id/pen_iv"
        android:layout_gravity="center"
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:background="@mipmap/icon_pen_def"  />
    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/select_layout"
        android:visibility="gone"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:gravity="center"
        android:layout_marginTop="@dimen/starryMargin10"
        app:layout_constraintRight_toRightOf="@id/pen_layout"
        app:layout_constraintTop_toBottomOf="@id/pen_layout"
        android:background="@drawable/eshare_circle_color_gray">
        <ImageView
            android:layout_gravity="center"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:background="@mipmap/icon_pen_select"  />
    </androidx.appcompat.widget.LinearLayoutCompat>

    <RadioGroup
        android:id="@+id/color_layout"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:background="@drawable/btn_rec_10_bg_with_code_gray"
        android:gravity="center"
        android:orientation="horizontal"
        android:visibility="gone"
        android:layout_marginBottom="@dimen/starryMargin10"
        app:layout_constraintBottom_toTopOf="@+id/pen_layout"
        app:layout_constraintRight_toRightOf="@+id/pen_layout">
        <RadioButton
            android:id="@+id/black"
            android:button="@null"
            style="@style/paint_color"
            android:background="@drawable/eshare_selector_color_black"/>
        <RadioButton
            android:id="@+id/white"
            android:button="@null"
            style="@style/paint_color"
            android:background="@drawable/eshare_selector_color_white"/>
        <RadioButton
            android:id="@+id/red"
            style="@style/paint_color"
            android:button="@null"
            android:background="@drawable/eshare_selector_color_red"/>
        <RadioButton
            android:id="@+id/green"
            style="@style/paint_color"
            android:button="@null"
            android:background="@drawable/eshare_selector_color_green" />
        <RadioButton
            android:id="@+id/yello"
            style="@style/paint_color"
            android:button="@null"
            android:background="@drawable/eshare_selector_color_yello" />
        <RadioButton
            android:id="@+id/cblue"
            style="@style/paint_color"
            android:button="@null"
            android:background="@drawable/eshare_selector_color_cblue" />

        <RadioButton
            android:id="@+id/blue"
            style="@style/paint_color"
            android:button="@null"
            android:background="@drawable/eshare_selector_color_blue" />

    </RadioGroup>
</androidx.constraintlayout.widget.ConstraintLayout>