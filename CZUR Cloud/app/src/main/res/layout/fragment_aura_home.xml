<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gary_f9">

    <com.lsjwzh.widget.recyclerviewpager.RecyclerViewPager
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:descendantFocusability="afterDescendants"
        android:overScrollMode="never"
        app:fastScrollEnabled="false"
        app:rvp_flingFactor="1"
        app:rvp_singlePageFling="true"
        app:rvp_triggerOffset="1" />

    <RelativeLayout
        android:id="@+id/slide_rl"
        android:layout_width="105dp"
        android:layout_height="25dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="5dp"
        android:background="@drawable/btn_with_50_red"
        android:visibility="gone">

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:maxLines="2"
            android:gravity="center"
            android:text="@string/slide_to_read_report"
            android:textColor="@color/white"
            app:autoSizeMaxTextSize="11sp"
            app:autoSizeMinTextSize="9sp"
            app:autoSizeStepGranularity="1sp"
            app:autoSizeTextType="uniform"
            android:textSize="11sp" />
    </RelativeLayout>

    <include
        layout="@layout/layout_no_network"
        android:visibility="gone" />
</RelativeLayout>