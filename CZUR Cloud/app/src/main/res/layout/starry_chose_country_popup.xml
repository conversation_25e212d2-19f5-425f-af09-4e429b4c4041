<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/background_rl"
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <LinearLayout
            android:layout_width="260dp"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:background="@drawable/btn_rec_5_bg_with_white"
            android:gravity="center_horizontal"
            android:orientation="vertical">


            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                android:layout_marginLeft="30dp"
                android:layout_marginRight="30dp">

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text=""
                    android:textColor="@color/black_22"
                    android:textSize="14sp"
                    tools:text="提示" />

                <TextView
                    android:id="@+id/message"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/title"
                    android:layout_marginTop="16dp"
                    android:gravity="center"
                    android:text=""
                    android:textColor="@color/black_22"
                    android:textSize="13sp"
                    tools:text="内容" />


            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/chose_country_Rl"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/btn_rec_5_bg_with_gray">


                <TextView
                    android:id="@+id/chosen_country_tv"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_centerInParent="true"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:lines="1"
                    android:paddingStart="25dp"
                    android:paddingEnd="25dp"
                    android:text=""
                    android:textColor="@color/black"
                    android:textSize="15sp" />

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="6dp"
                    android:padding="5dp"
                    android:rotation="90"
                    android:src="@mipmap/starry_right_arrow"
                    app:tint="#bbbbbb" />
            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/gray_e5" />
            <!-- button -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:orientation="horizontal">


                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/positive_button"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text=""
                    android:textColor="@color/blue_positive_button"
                    android:textSize="14sp"
                    tools:text="确定" />
            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>