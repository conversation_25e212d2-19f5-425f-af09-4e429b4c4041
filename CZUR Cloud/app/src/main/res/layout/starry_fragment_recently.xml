<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/starry_tab_recently"
    android:background="@color/white">

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.scwang.smartrefresh.layout.header.ClassicsHeader
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin60"
            app:srlDrawableMarginRight="-10dp"
            app:srlDrawableProgress="@mipmap/loading"
            app:srlTextFailed="@string/starry_reflash_fail"
            app:srlTextFinish="@string/starry_reflash_success"
            app:srlTextLoading=""
            app:srlTextNothing=""
            app:srlTextPulling=""
            app:srlTextRefreshing=""
            app:srlTextRelease=""
            app:srlTextUpdate="" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingTop="@dimen/dp10">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view_recently"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:overScrollMode="never"
                android:visibility="visible" />

            <TextView
                android:id="@+id/recently_bottom_msg_coount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/starryMargin20"
                android:gravity="center"
                android:text=""
                android:textColor="@color/starry_recently_bottom_text_color"
                android:textSize="@dimen/starryCallInNo" />

        </LinearLayout>

        <com.scwang.smartrefresh.layout.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin60"
            app:srlDrawableProgress="@mipmap/loading"
            app:srlTextFailed=""
            app:srlTextFinish=""
            app:srlTextLoading="@string/starry_reflash_loading"
            app:srlTextNothing=""
            app:srlTextPulling="@string/starry_reflash_pulling"
            app:srlTextRefreshing=""
            app:srlTextRelease="" />

    </com.scwang.smartrefresh.layout.SmartRefreshLayout>

    <include
        android:id="@+id/starry_msg_nodata"
        layout="@layout/starry_layout_no_meeting"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        />

    <include
        layout="@layout/starry_no_network_layout"
        android:visibility="gone" />

</RelativeLayout>