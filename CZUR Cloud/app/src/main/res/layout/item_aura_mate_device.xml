<?xml version="1.0" encoding="utf-8"?>
<com.scwang.smartrefresh.layout.SmartRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="290dp"
    android:layout_height="match_parent"
    android:descendantFocusability="beforeDescendants"
    app:srlEnableAutoLoadMore="false"
    app:srlEnableLoadMore="false"
    app:srlEnableOverScrollBounce="true"
    app:srlEnableOverScrollDrag="true"
    app:srlEnableRefresh="false">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scroll"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="7dp"
        android:layout_marginRight="7dp"
        android:overScrollMode="never"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <RelativeLayout
                android:id="@+id/aura_home_device_rl"
                android:layout_width="match_parent"
                android:layout_height="530dp"
                android:layout_marginTop="15dp"
                android:background="@drawable/btn_rec_10_bg_with_code_blue">

                <LinearLayout
                    android:id="@+id/aura_home_online_ll"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_above="@+id/aura_home_sitting_position_rl"
                    android:layout_below="@+id/aura_home_title_ll"
                    android:orientation="vertical"
                    android:visibility="visible">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:layout_marginLeft="28dp"
                            android:layout_marginRight="28dp">

                            <ImageView
                                android:id="@+id/light_img"
                                android:layout_width="15dp"
                                android:layout_height="15dp"
                                android:layout_below="@+id/aura_home_off_btn"
                                android:layout_centerHorizontal="true"
                                android:layout_marginTop="35dp"
                                android:src="@mipmap/aura_light_icon" />

                            <RelativeLayout
                                android:id="@+id/control_light_rl"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_below="@+id/light_img"
                                android:layout_centerHorizontal="true">

                                <ImageView
                                    android:id="@+id/aura_home_minus_btn"
                                    android:layout_width="54dp"
                                    android:layout_height="43dp"
                                    android:layout_centerVertical="true"
                                    android:layout_marginEnd="25dp"
                                    android:layout_toStartOf="@+id/aura_home_light_level_tv"
                                    android:padding="20dp"
                                    android:src="@mipmap/aura_home_minus_icon" />

                                <ImageView
                                    android:id="@+id/aura_home_plus_btn"
                                    android:layout_width="52dp"
                                    android:layout_height="52dp"
                                    android:layout_centerVertical="true"
                                    android:layout_marginStart="25dp"
                                    android:layout_toEndOf="@+id/aura_home_light_level_tv"
                                    android:padding="20dp"
                                    android:src="@mipmap/aura_home_plus_icon" />

                                <com.czur.cloud.ui.component.MediumBoldTextView
                                    android:id="@+id/aura_home_light_level_tv"
                                    android:layout_width="70dp"
                                    android:layout_height="wrap_content"
                                    android:layout_centerInParent="true"
                                    android:gravity="center"
                                    android:text="50"
                                    android:textColor="@color/white"
                                    android:textSize="22sp" />
                            </RelativeLayout>

                            <RelativeLayout
                                android:id="@+id/aura_home_off_btn"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerHorizontal="true"
                                android:layout_marginTop="20dp">

                                <com.czur.cloud.ui.component.MediumBoldTextView
                                    android:layout_width="80dp"
                                    android:layout_height="80dp"
                                    android:layout_centerInParent="true"
                                    android:background="@drawable/circle_with_code_blue_22a3c8"
                                    android:gravity="center"
                                    android:text="@string/off"
                                    android:textColor="@color/white"
                                    android:textSize="14sp" />
                            </RelativeLayout>


                            <com.czur.cloud.ui.component.seekbar.BubbleSeekBar
                                android:id="@+id/seekBar"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_below="@+id/control_light_rl"
                                android:layout_centerHorizontal="true"
                                android:layout_marginTop="10dp"
                                android:paddingLeft="2dp"
                                android:paddingTop="10dp"
                                android:paddingRight="2dp"
                                app:bsb_always_show_bubble="false"
                                app:bsb_anim_duration="0"
                                app:bsb_auto_adjust_section_mark="true"
                                app:bsb_bubble_color="@color/normal_blue"
                                app:bsb_hide_bubble="true"
                                app:bsb_max="60"
                                app:bsb_min="0"
                                app:bsb_progress="10"
                                app:bsb_second_track_color="@color/white"
                                app:bsb_section_count="6"
                                app:bsb_show_progress_in_float="false"
                                app:bsb_show_section_mark="true"
                                app:bsb_show_section_text="false"
                                app:bsb_show_thumb_text="false"
                                app:bsb_touch_to_seek="true"
                                app:bsb_track_color="@color/blue_22a3c8" />
                        </RelativeLayout>


                    </RelativeLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="9dp"
                        android:layout_marginTop="20dp"
                        android:layout_marginRight="10dp">

                        <RelativeLayout
                            android:id="@+id/aura_home_eye_rl"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_centerHorizontal="true"
                                android:orientation="vertical">

                                <ImageView
                                    android:id="@+id/aura_home_eye_img"
                                    android:layout_width="40dp"
                                    android:layout_height="40dp"
                                    android:layout_gravity="center_horizontal"
                                    android:src="@drawable/selector_eye" />

                                <TextView
                                    android:id="@+id/aura_home_eye_tv"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_horizontal"
                                    android:layout_marginStart="2dp"
                                    android:layout_marginTop="5dp"
                                    android:layout_marginEnd="2dp"
                                    android:gravity="center"
                                    android:maxLines="1"
                                    android:text="@string/eye"
                                    android:textColor="@color/white"
                                    android:textSize="12sp"
                                    app:autoSizeMaxTextSize="12sp"
                                    app:autoSizeMinTextSize="8sp"
                                    app:autoSizeStepGranularity="1sp"
                                    app:autoSizeTextType="uniform" />
                            </LinearLayout>

                        </RelativeLayout>

                        <RelativeLayout
                            android:id="@+id/aura_home_light_rl"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_centerHorizontal="true"
                                android:orientation="vertical">

                                <ImageView
                                    android:id="@+id/aura_home_light_img"
                                    android:layout_width="40dp"
                                    android:layout_height="40dp"
                                    android:layout_gravity="center_horizontal"
                                    android:src="@drawable/selector_light" />

                                <TextView
                                    android:id="@+id/aura_home_light_tv"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_horizontal"
                                    android:layout_marginStart="2dp"
                                    android:layout_marginTop="5dp"
                                    android:layout_marginEnd="2dp"
                                    android:gravity="center"
                                    android:maxLines="1"
                                    android:text="@string/light"
                                    android:textColor="@color/white"
                                    android:textSize="12sp"
                                    app:autoSizeMaxTextSize="12sp"
                                    app:autoSizeMinTextSize="8sp"
                                    app:autoSizeStepGranularity="1sp"
                                    app:autoSizeTextType="uniform" />
                            </LinearLayout>

                        </RelativeLayout>

                        <RelativeLayout
                            android:id="@+id/aura_home_read_rl"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_centerHorizontal="true"
                                android:orientation="vertical">

                                <ImageView
                                    android:id="@+id/aura_home_read_img"
                                    android:layout_width="40dp"
                                    android:layout_height="40dp"
                                    android:layout_gravity="center_horizontal"
                                    android:src="@drawable/selector_read" />

                                <TextView
                                    android:id="@+id/aura_home_read_tv"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_horizontal"
                                    android:layout_marginStart="2dp"
                                    android:layout_marginTop="5dp"
                                    android:layout_marginEnd="2dp"
                                    android:gravity="center"
                                    android:maxLines="1"
                                    android:text="@string/read"
                                    android:textColor="@color/white"
                                    android:textSize="12sp"
                                    app:autoSizeMaxTextSize="12sp"
                                    app:autoSizeMinTextSize="8sp"
                                    app:autoSizeStepGranularity="1sp"
                                    app:autoSizeTextType="uniform" />
                            </LinearLayout>

                        </RelativeLayout>

                        <RelativeLayout
                            android:id="@+id/aura_home_computer_rl"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_centerHorizontal="true"
                                android:orientation="vertical">

                                <ImageView
                                    android:id="@+id/aura_home_computer_img"
                                    android:layout_width="40dp"
                                    android:layout_height="40dp"
                                    android:layout_gravity="center_horizontal"
                                    android:src="@drawable/selector_computer" />

                                <TextView
                                    android:id="@+id/aura_home_computer_tv"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_horizontal"
                                    android:layout_marginStart="2dp"
                                    android:layout_marginTop="5dp"
                                    android:layout_marginEnd="2dp"
                                    android:gravity="center"
                                    android:maxLines="1"
                                    android:text="@string/computer"
                                    android:textColor="@color/white"
                                    android:textSize="12sp"
                                    app:autoSizeMaxTextSize="12sp"
                                    app:autoSizeMinTextSize="8sp"
                                    app:autoSizeStepGranularity="1sp"
                                    app:autoSizeTextType="uniform" />
                            </LinearLayout>
                        </RelativeLayout>
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/aura_home_off_ll"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_above="@+id/aura_home_sitting_position_rl"
                    android:layout_below="@+id/aura_home_title_ll"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:visibility="gone">


                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/aura_home_on_btn"
                        android:layout_width="120dp"
                        android:layout_height="120dp"
                        android:background="@drawable/btn_on_bg"
                        android:gravity="center"
                        android:text="@string/on"
                        android:textColor="@color/white"
                        android:textSize="18sp" />

                </LinearLayout>


                <RelativeLayout
                    android:id="@+id/aura_home_sitting_position_rl"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_above="@+id/aura_home_long_sitting_rl">

                    <RelativeLayout
                        android:layout_width="200dp"
                        android:layout_height="match_parent"
                        android:layout_alignParentEnd="true">

                        <ImageView
                            android:id="@+id/sitting_position_img"
                            android:layout_width="8dp"
                            android:layout_height="14dp"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="7dp"
                            android:layout_marginEnd="14dp"
                            android:src="@mipmap/white_arrow" />

                        <com.czur.cloud.ui.component.MediumBoldTextView
                            android:id="@+id/sitting_switch_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_toStartOf="@+id/sitting_position_img"
                            android:text="@string/open"
                            android:textColor="@color/white"
                            android:textSize="16sp" />

                    </RelativeLayout>


                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginLeft="14dp"
                        android:layout_marginRight="14dp"
                        android:background="@color/whiteOpaque20" />

                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/position_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="14dp"
                        android:maxLines="1"
                        android:text="@string/aura_home_sitting_remind"
                        android:textColor="@color/white"
                        android:textSize="16sp" />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/aura_home_long_sitting_rl"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_above="@+id/aura_home_smart_rl">

                    <RelativeLayout
                        android:layout_width="200dp"
                        android:layout_height="match_parent"
                        android:layout_alignParentEnd="true">

                        <ImageView
                            android:id="@+id/sitting_long_img"
                            android:layout_width="8dp"
                            android:layout_height="14dp"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="7dp"
                            android:layout_marginEnd="14dp"
                            android:src="@mipmap/white_arrow" />

                        <com.czur.cloud.ui.component.MediumBoldTextView
                            android:id="@+id/sitting_long_switch_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_toStartOf="@+id/sitting_long_img"
                            android:text="@string/open"
                            android:textColor="@color/white"
                            android:textSize="16sp" />

                    </RelativeLayout>


                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginLeft="14dp"
                        android:layout_marginRight="14dp"
                        android:background="@color/whiteOpaque20" />

                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/sitting_long_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="14dp"
                        android:maxLines="1"
                        android:text="@string/aura_home_sedentary_remind"
                        android:textColor="@color/white"
                        android:textSize="16sp" />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/aura_home_smart_rl"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_alignParentBottom="true">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginLeft="14dp"
                        android:layout_marginRight="14dp"
                        android:background="@color/whiteOpaque20" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentStart="true"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="14dp"
                        android:layout_toStartOf="@+id/smart_power_switch"
                        android:orientation="vertical">

                        <com.czur.cloud.ui.component.MediumBoldTextView
                            android:id="@+id/smart_power_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:maxLines="1"
                            android:text="@string/aura_home_smart_power"
                            android:textColor="@color/white"
                            android:textSize="16sp" />

                        <TextView
                            android:id="@+id/smart_power_detail_tv"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:layout_marginEnd="5dp"
                            android:includeFontPadding="false"
                            android:text="@string/aura_home_smart_power_detail"
                            android:textColor="@color/white"
                            android:textSize="10sp"
                            app:autoSizeMaxTextSize="10sp"
                            app:autoSizeMinTextSize="8sp"
                            app:autoSizeStepGranularity="1sp"
                            app:autoSizeTextType="uniform" />
                    </LinearLayout>

                    <com.github.iielse.switchbutton.SwitchView
                        android:id="@+id/smart_power_switch"
                        android:layout_width="50dp"
                        android:layout_height="30dp"
                        android:layout_alignParentEnd="true"
                        android:layout_marginTop="11dp"
                        android:layout_marginEnd="14dp"
                        app:isOpened="false"
                        app:primaryColor="@color/blue_33c5e4"
                        app:primaryColorDark="@color/blue_33c5e4"
                        app:ratioAspect="0.6" />

                </RelativeLayout>

                <LinearLayout
                    android:id="@+id/aura_home_offline_ll"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/btn_rec_10_bg_with_code_blue"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="49dp"
                        android:layout_height="36dp"
                        android:layout_gravity="center_horizontal"
                        android:src="@mipmap/aura_offline_icon" />

                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="17dp"
                        android:gravity="center"
                        android:text="@string/offline_state"
                        android:textColor="@color/white"
                        android:textSize="18sp" />


                </LinearLayout>

                <LinearLayout
                    android:id="@+id/aura_home_pc_ll"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/btn_rec_10_bg_with_code_blue"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="55dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:adjustViewBounds="true"
                        android:scaleType="fitXY"
                        android:src="@mipmap/pc_mode" />

                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="17dp"
                        android:gravity="center"
                        android:maxWidth="250dp"
                        android:text="@string/pc_mode"
                        android:textColor="@color/white"
                        android:textSize="16sp" />


                </LinearLayout>

                <RelativeLayout
                    android:id="@+id/aura_home_title_ll"
                    android:layout_width="match_parent"
                    android:layout_height="46dp"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/aura_home_state_img"
                        android:layout_width="6dp"
                        android:layout_height="6dp"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="14dp"
                        android:src="@drawable/circle_with_green" />

                    <LinearLayout
                        android:id="@+id/aura_home_setting_ll"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_alignParentEnd="true"
                        android:orientation="horizontal">


                        <com.czur.cloud.ui.component.MediumBoldTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginEnd="7dp"
                            android:text="@string/setting"
                            android:textColor="@color/white"
                            android:textSize="13sp" />

                        <ImageView
                            android:layout_width="8dp"
                            android:layout_height="14dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginEnd="15dp"
                            android:src="@mipmap/white_arrow" />

                    </LinearLayout>


                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/aura_home_device_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignBaseline="@+id/aura_home_setting_ll"
                        android:layout_centerInParent="true"
                        android:ellipsize="end"
                        android:maxWidth="180dp"
                        android:singleLine="true"
                        android:textColor="@color/white"
                        android:textSize="18sp" />

                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/aura_home_state_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="7dp"
                        android:layout_toEndOf="@+id/aura_home_state_img"
                        android:text="@string/online"
                        android:textColor="@color/white"
                        android:textSize="13sp" />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/loading_rl"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/btn_rec_10_bg_with_code_blue"
                    android:clickable="true"
                    android:focusable="true"
                    android:visibility="gone">

                    <ImageView
                        android:id="@+id/loading_img"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_centerInParent="true"
                        android:src="@mipmap/main_loading" />
                </RelativeLayout>
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/aura_home_remote_video_rl"
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:layout_marginTop="10dp"
                android:foreground="?selectableItemBackground">

                <ImageView
                    android:id="@+id/aura_home_item_shadow_img"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@mipmap/shadow_aura_icon">

                </ImageView>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/btn_rec_10_bg_with_code_white">

                    <RelativeLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true">

                        <ImageView
                            android:id="@+id/remote_img"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_centerHorizontal="true"
                            android:src="@mipmap/aura_video_icon" />

                        <com.czur.cloud.ui.component.MediumBoldTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/remote_img"
                            android:layout_marginTop="12dp"
                            android:text="@string/aura_home_remote"
                            android:textColor="@color/gray_bb"
                            android:textSize="14sp" />
                    </RelativeLayout>
                </RelativeLayout>
            </RelativeLayout>


            <RelativeLayout
                android:id="@+id/aura_home_report_rl"
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:foreground="?selectableItemBackground">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@mipmap/shadow_aura_icon" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/btn_rec_10_bg_with_code_white">

                    <RelativeLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true">

                        <ImageView
                            android:id="@+id/report_img"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_centerHorizontal="true"
                            android:src="@mipmap/aura_report_icon" />

                        <com.czur.cloud.ui.component.MediumBoldTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/report_img"
                            android:layout_marginTop="12dp"
                            android:text="@string/aura_mate_report"
                            android:textColor="@color/gray_bb"
                            android:textSize="14sp" />
                    </RelativeLayout>

                    <TextView
                        android:id="@+id/report_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="10dp"
                        android:background="@drawable/oval_read"
                        android:gravity="center"
                        android:minWidth="15dp"
                        android:minHeight="15dp"
                        android:textColor="@color/white"
                        android:textSize="10sp"
                        android:visibility="visible" />
                </RelativeLayout>
            </RelativeLayout>
        </LinearLayout>

    </androidx.core.widget.NestedScrollView>
</com.scwang.smartrefresh.layout.SmartRefreshLayout>


