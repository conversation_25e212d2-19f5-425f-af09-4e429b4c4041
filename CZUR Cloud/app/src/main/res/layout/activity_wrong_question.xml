<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/gary_f9">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="44dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="44dp">

                <ImageView
                    android:id="@+id/wrong_question_back_btn"
                    android:layout_width="29.5dp"
                    android:layout_height="36dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9dp"
                    android:padding="10dp"
                    android:src="@mipmap/login_back_icon" />


                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/wrong_question_select_all_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:paddingLeft="18.5dp"
                    android:textColor="@color/black_22"
                    android:textSize="16sp"
                    android:visibility="gone" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/wrong_question_title_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:textColor="@color/black_22"
                    android:textSize="18sp"
                    android:visibility="gone" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/wrong_question_cancel_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignParentEnd="true"
                    android:gravity="center_vertical"
                    android:paddingRight="18.5dp"
                    android:textColor="@color/black_22"
                    android:textSize="16sp"
                    android:visibility="gone" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/wrong_question_multi_select_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignParentEnd="true"
                    android:gravity="center_vertical"
                    android:paddingEnd="18.5dp"
                    android:text="@string/select"
                    android:textColor="@color/black_22"
                    android:textSize="17sp" />
            </RelativeLayout>


        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/object_rl"
            android:layout_width="match_parent"
            android:layout_height="40dp">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/object_recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="10dp"
                android:layout_toStartOf="@+id/manage_object_btn"
                android:background="@color/gary_f9"
                android:overScrollMode="never" />

            <ImageView
                android:id="@+id/manage_object_btn"
                android:layout_width="38dp"
                android:layout_height="36dp"
                android:layout_alignParentEnd="true"
                android:layout_centerInParent="true"
                android:layout_marginEnd="10dp"
                android:padding="10dp"
                android:src="@mipmap/edit_wrong_icon" />
        </RelativeLayout>

        <com.scwang.smartrefresh.layout.SmartRefreshLayout
            android:id="@+id/refresh_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:srlEnableLoadMore="true"
            app:srlEnableRefresh="true">

            <com.scwang.smartrefresh.layout.header.ClassicsHeader
                android:layout_width="match_parent"
                android:layout_height="60dp"
                app:srlDrawableMarginRight="-10dp"
                app:srlTextFailed=""
                app:srlTextFinish=""
                app:srlTextLoading=""
                app:srlTextPulling=""
                app:srlTextRefreshing=""
                app:srlTextRelease=""
                app:srlTextUpdate="" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/wrong_question_recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:overScrollMode="never" />

                <RelativeLayout
                    android:id="@+id/wrong_question_empty_rl"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/gary_f9"
                    android:visibility="gone">


                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="120dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:adjustViewBounds="true"
                            android:scaleType="fitXY"
                            android:src="@mipmap/book_page_no_page_icon" />

                        <com.czur.cloud.ui.component.MediumBoldTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginTop="15.5dp"
                            android:text="@string/none_file_book_page"
                            android:textColor="@color/gary_c4"
                            android:textSize="15sp" />
                    </LinearLayout>


                </RelativeLayout>

            </RelativeLayout>

            <com.scwang.smartrefresh.layout.footer.ClassicsFooter
                android:layout_width="match_parent"
                android:layout_height="50dp"
                app:srlTextFailed=""
                app:srlTextFinish=""
                app:srlTextLoading="@string/czur_footer_loading"
                app:srlTextNothing="@string/czur_footer_nothing"
                app:srlTextPulling="@string/czur_footer_pulling"
                app:srlTextRefreshing=""
                app:srlTextRelease="" />

        </com.scwang.smartrefresh.layout.SmartRefreshLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/wrong_question_bottom_ll"
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:layout_alignParentBottom="true"
        android:layout_gravity="bottom"
        android:visibility="gone">


        <RelativeLayout
            android:id="@+id/wrong_question_save_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/black"
            android:visibility="gone">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/wrong_question_save_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_delete_icon" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/wrong_question_save_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/wrong_question_save_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/save_to_album"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/wrong_question_delete_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/black">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/wrong_question_delete_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_delete_icon" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/wrong_question_delete_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/wrong_question_delete_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/delete"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/wrong_question_share_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/black"
            android:visibility="gone">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/wrong_question_share_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_delete_icon" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/wrong_question_share_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/wrong_question_share_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/share"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>


    </LinearLayout>


</RelativeLayout>




