<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gary_f9"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="45dp"
        android:background="@color/white">

        <ImageView
            android:id="@+id/et_delete_share_user_back_btn"
            android:layout_width="29.5dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="9dp"
            android:padding="10dp"
            android:src="@mipmap/login_back_icon" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/et_delete_share_user_title"
            android:layout_width="130dp"
            android:gravity="center"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="@color/account_title"
            android:textSize="18sp" />

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true">
            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/et_delete_share_user_finish_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginLeft="18.5dp"
                android:layout_marginRight="18.5dp"
                android:textColor="@color/blue_29b0d7"
                android:textSize="18sp" />
        </RelativeLayout>
    </RelativeLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/et_delete_share_user_recyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/gary_f9"
        android:overScrollMode="never" />

</LinearLayout>

