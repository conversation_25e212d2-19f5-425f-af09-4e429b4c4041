<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:background="@drawable/btn_rec_5_bg_with_white"
        android:gravity="center_horizontal"
        android:orientation="vertical">


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:layout_marginLeft="29dp"
            android:layout_marginRight="29dp">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_marginTop="15dp"
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/prompt"
                android:textColor="@color/black_22"
                android:textSize="16sp" />


            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/message1"
                android:layout_marginTop="20dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/title"
                android:gravity="center"
                android:text="@string/white_list_prompt1"
                android:textColor="@color/black_22"
                android:textSize="13sp" />
        </RelativeLayout>



        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/gray_e5"/>
        <!-- button -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:orientation="horizontal">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/confirm_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/handwriting_confirm_text"
                android:textColor="@color/blue_positive_button"
                android:textSize="14sp" />
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>