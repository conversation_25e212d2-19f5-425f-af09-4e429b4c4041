<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="MissingPrefix">

    <LinearLayout
        android:layout_width="243dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:background="@drawable/btn_rec_8_bg_with_white"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/btn_back"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="end"
            android:padding="10dp"
            android:src="@mipmap/new_popup_close" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="35dp"
            android:orientation="vertical">

            <ImageView
                android:layout_width="70dp"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:scaleType="fitXY"
                android:layout_gravity="center_horizontal"
                android:src="@mipmap/pc_mode_blue" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:gravity="center"
                android:lineSpacingExtra="3dp"
                android:text="@string/pc_mode"
                android:textColor="@color/black_22"
                android:textSize="15sp" />

        </LinearLayout>


        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/btn_done"
            android:layout_width="161dp"
            android:layout_height="36dp"
            android:layout_marginTop="35dp"
            android:layout_marginBottom="15dp"
            android:background="@drawable/btn_rec_8_bg_with_blue_aura_home"
            android:gravity="center"
            android:text="@string/finish"
            android:textColor="@color/white"
            android:textSize="15sp" />
    </LinearLayout>
</RelativeLayout>