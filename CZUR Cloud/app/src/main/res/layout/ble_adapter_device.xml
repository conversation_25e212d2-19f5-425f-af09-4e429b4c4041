<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_centerVertical="true"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:textAlignment="center"
        android:padding="@dimen/jingMargin10">

        <ImageView
            android:id="@+id/img_blue"
            android:layout_width="50dp"
            android:layout_height="80dp"
            android:layout_centerVertical="true"
            android:src="@mipmap/sitting_icon" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_toEndOf="@id/img_blue"
            android:layout_centerVertical="true"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/ble_txt_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="2dp"
                android:textColor="@color/black_2a"
                android:text=""
                android:textSize="20sp" />

            <TextView
                android:id="@+id/ble_txt_mac"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:textSize="12sp"
                android:visibility="gone"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/id_select"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:button="@null"
                android:layout_marginStart="10dp"
                android:background="@mipmap/sitting_no_select"
                android:checked="false"
                android:clickable="false"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:visibility="invisible"/>

            <ImageView
                android:id="@+id/ble_img_select"
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:src="@mipmap/sitting_no_select"
                android:padding="@dimen/jingMargin20"
                android:visibility="visible"/>

        </LinearLayout>


    </RelativeLayout>


</LinearLayout>