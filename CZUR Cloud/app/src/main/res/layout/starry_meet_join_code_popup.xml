<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:layout_width="260dp"
        android:layout_height="200dp"
        android:layout_centerInParent="true"
        android:background="@drawable/btn_rec_5_bg_with_white"
        android:gravity="center_horizontal">


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/starryMargin20"
            android:layout_marginTop="@dimen/starryMargin50"
            android:layout_marginRight="@dimen/starryMargin20"
            android:layout_marginBottom="@dimen/starryMargin20">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="middle"
                android:gravity="center"
                android:maxLines="1"
                android:text=""
                android:textColor="@color/black_22"
                android:textSize="@dimen/starryFontSize16"
                tools:text="提示" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/title"
                android:layout_marginTop="@dimen/starryMargin20"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="@dimen/starryMargin10"
                    android:layout_height="@dimen/starryMargin10"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="@dimen/dp_5"
                    android:src="@drawable/starry_btn_rec_5_bg_with_blue" />

                <TextView
                    android:id="@+id/message"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text=""
                    android:textColor="@color/black_22"
                    android:textSize="@dimen/starryFontSize18"
                    tools:text="内容" />

            </LinearLayout>

        </RelativeLayout>

        <EditText
            android:id="@+id/editText"
            android:layout_width="210dp"
            android:layout_height="22dp"
            android:layout_marginBottom="10dp"
            android:background="@drawable/dialog_rec_1_bg_with_white_with_stroke"
            android:maxLength="20"
            android:paddingStart="3dp"
            android:singleLine="true"
            android:textColor="@color/black_22"
            android:textColorHint="@color/starry_title_gray"
            android:textCursorDrawable="@drawable/edittext_cursor_blue"
            android:textSize="12sp"
            android:visibility="gone" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_above="@+id/btn_ll"
            android:background="@color/gray_e5" />

        <!-- button -->
        <LinearLayout
            android:id="@+id/btn_ll"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_alignParentBottom="true"
            android:orientation="horizontal">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/negative_button"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:textColor="@color/title_black_color"
                android:textSize="@dimen/starryFontSize16"
                tools:text="取消" />

            <View
                android:layout_width="0.5dp"
                android:layout_height="match_parent"
                android:background="@color/gray_e5" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/positive_button"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text=""
                android:textColor="@color/blue_positive_button"
                android:textSize="@dimen/starryFontSize16"
                tools:text="确定" />
        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>