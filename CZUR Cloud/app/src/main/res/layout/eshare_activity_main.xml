<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@mipmap/logo_background">

    <include
        android:id="@+id/eshare_layout_top_bar_rl"
        layout="@layout/eshare_layout_activity_top_bar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/eshare_mid_btn_play_linshi_cl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="200dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/eshare_mid_btn_play"
            android:layout_width="95dp"
            android:layout_height="95dp"
            android:layout_alignParentEnd="true"
            android:layout_centerInParent="true"
            android:background="@drawable/eshare_circle_with_7ss"
            android:padding="18dp"
            android:src="@mipmap/eshare_btn_play_with_bg"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/eshare_mid_play_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:gravity="center"
            android:text="@string/eshare_mid_start_text"
            android:textColor="@color/white"
            android:textSize="15sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="@id/eshare_mid_btn_play"
            app:layout_constraintStart_toStartOf="@+id/eshare_mid_btn_play"
            app:layout_constraintTop_toBottomOf="@id/eshare_mid_btn_play" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <TextView
        android:id="@+id/tip_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:text="@string/eshare_tips_str"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/eshare_mid_btn_play_linshi_cl" />


    <androidx.constraintlayout.widget.Group
        android:id="@+id/eshare_fullscreen_ll"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginEnd="40dp"
        android:visibility="gone"
        app:constraint_referenced_ids="eshare_fullscreen_iv,eshare_fullscreen_tv">

    </androidx.constraintlayout.widget.Group>

    <ImageView
        android:id="@+id/eshare_fullscreen_iv"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="40dp"
        android:background="@drawable/eshare_circle_with_dark"
        android:padding="10dp"
        android:src="@mipmap/eshare_fullscreen"
        android:translationY="-10dp"
        app:layout_constraintBottom_toBottomOf="@id/eshare_mid_btn_play_linshi_cl"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/eshare_mid_btn_play_linshi_cl" />

    <TextView
        android:id="@+id/eshare_fullscreen_tv"
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/starryMargin5"
        android:text="@string/eshare_fullscreen_text"
        android:textColor="@color/eshare_text_color"
        android:textSize="@dimen/starryFontSize12"
        android:gravity="center_horizontal"
        app:layout_constraintEnd_toEndOf="@+id/eshare_fullscreen_iv"
        app:layout_constraintStart_toStartOf="@+id/eshare_fullscreen_iv"
        app:layout_constraintTop_toBottomOf="@+id/eshare_fullscreen_iv" />


    <androidx.constraintlayout.widget.Group
        android:id="@+id/eshare_paint_cl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="eshare_paint_iv,eshare_paint_tv">

    </androidx.constraintlayout.widget.Group>

    <ImageView
        android:id="@+id/eshare_paint_iv"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="40dp"
        android:background="@drawable/eshare_circle_with_dark"
        android:padding="11dp"
        android:src="@mipmap/icon_pen_def"
        android:translationY="-10dp"
        app:layout_constraintBottom_toBottomOf="@+id/eshare_mid_btn_play_linshi_cl"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/eshare_mid_btn_play_linshi_cl" />

    <TextView
        android:id="@+id/eshare_paint_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/starryMargin5"
        android:text="@string/eshare_paint_switch_str"
        android:textColor="@color/eshare_text_color"
        android:textSize="@dimen/starryFontSize12"
        app:layout_constraintEnd_toEndOf="@+id/eshare_paint_iv"
        app:layout_constraintStart_toStartOf="@+id/eshare_paint_iv"
        app:layout_constraintTop_toBottomOf="@+id/eshare_paint_iv" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/eshare_transmit_upload_file_cl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="520dp"
        android:layout_marginEnd="20dp"
        android:background="@drawable/eshare_circle_color_white_5dp"
        android:gravity="center_vertical"
        android:padding="@dimen/starryMargin20"
        android:visibility="visible"
        android:layout_marginBottom="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/eshare_transmit_download_file_cl">


        <ImageView
            android:id="@+id/chuanwenjian_iv"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:background="@mipmap/ic_eshare_transmit_up_file"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/upload_title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="36dp"
            android:layout_toEndOf="@+id/chuanwenjian_iv"
            android:text="@string/eshare_transmit_files_upload_str"
            android:textColor="@color/eshare_common_bg"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/textView2"
            app:layout_constraintStart_toEndOf="@+id/chuanwenjian_iv"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/textView2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5"
            android:layout_toEndOf="@+id/chuanwenjian_iv"
            android:text="@string/eshare_transmit_files_tips_str"
            android:textColor="@color/eshare_common_bg"
            android:textSize="12sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/upload_title_tv"
            app:layout_constraintTop_toBottomOf="@+id/upload_title_tv" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/eshare_transmit_download_file_cl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:background="@drawable/eshare_circle_color_white_5dp"
        android:gravity="center_vertical"
        android:padding="@dimen/starryMargin20"
        android:layout_marginBottom="20dp"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@+id/bottom_blank_fl"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">


        <ImageView
            android:id="@+id/download_file_iv"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:background="@mipmap/ic_eshare_transmit_down_file"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/download_title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="36dp"
            android:layout_toEndOf="@+id/download_file_iv"
            android:text="@string/eshare_transmit_files_download_str"
            android:textColor="@color/eshare_common_bg"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/download_content_tv"
            app:layout_constraintStart_toEndOf="@+id/download_file_iv"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/download_content_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5"
            android:layout_toEndOf="@+id/download_file_iv"
            android:text="@string/eshare_download_files_tips_str"
            android:textColor="@color/eshare_common_bg"
            android:textSize="12sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/download_title_tv"
            app:layout_constraintTop_toBottomOf="@+id/download_title_tv" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/bottom_blank_fl"
        android:layout_width="10dp"
        android:layout_height="30dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <LinearLayout
        android:id="@+id/eshare_main_top"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/eshare_layout_top_bar_rl">


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/starryMargin30"
            android:layout_marginBottom="@dimen/starryMargin30"
            android:gravity="center">

            <!--            <LinearLayout-->
            <!--                android:id="@+id/eshare_disconnect_ll"-->
            <!--                android:layout_width="wrap_content"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:gravity="center"-->
            <!--                android:layout_weight="1"-->
            <!--                android:orientation="vertical">-->

            <!--                <ImageView-->
            <!--                    android:layout_width="@dimen/starryMargin50"-->
            <!--                    android:layout_height="@dimen/starryMargin50"-->
            <!--                    android:padding="@dimen/starryMargin15"-->
            <!--                    android:background="@drawable/eshare_circle_with_dark"-->
            <!--                    android:src="@mipmap/eshare_disconnect"-->
            <!--                    />-->
            <!--                <TextView-->
            <!--                    android:layout_width="wrap_content"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:layout_marginTop="@dimen/starryMargin5"-->
            <!--                    android:text="@string/eshare_disconnect_text"-->
            <!--                    android:textColor="@color/eshare_text_color"-->
            <!--                    android:textSize="@dimen/starryFontSize12"-->
            <!--                    />-->

            <!--            </LinearLayout>-->


            <LinearLayout
                android:id="@+id/eshare_fullscreen_ll_yuanlai"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/starryMargin100"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="visible">
                <!--                <ImageView-->
                <!--                    android:id="@+id/eshare_fullscreen_iv"-->
                <!--                    android:layout_width="@dimen/starryMargin50"-->
                <!--                    android:layout_height="@dimen/starryMargin50"-->
                <!--                    android:padding="@dimen/starryMargin15"-->
                <!--                    android:background="@drawable/eshare_circle_with_dark"-->
                <!--                    android:src="@mipmap/eshare_fullscreen"-->
                <!--                    />-->
                <ImageView
                    android:id="@+id/eshare_fullscreen_iv_cancel"
                    android:layout_width="@dimen/starryMargin50"
                    android:layout_height="@dimen/starryMargin50"
                    android:background="@drawable/eshare_circle_with_gray"
                    android:padding="@dimen/starryMargin15"
                    android:src="@mipmap/eshare_fullscreen_no"
                    android:visibility="gone" />
                <!--                <TextView-->
                <!--                    android:id="@+id/eshare_fullscreen_tv"-->
                <!--                    android:layout_width="wrap_content"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:layout_marginTop="@dimen/starryMargin5"-->
                <!--                    android:text="@string/eshare_fullscreen_text"-->
                <!--                    android:textColor="@color/eshare_text_color"-->
                <!--                    android:textSize="@dimen/starryFontSize12"-->
                <!--                    />-->

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <LinearLayout
            style="@style/eshare_bottom_line"
            android:layout_margin="@dimen/starryMargin20"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:id="@+id/eshare_main_bottom_doc_ll"
                style="@style/eshare_bottom_pannel"
                android:layout_weight="1">

                <ImageView
                    android:layout_width="@dimen/starryMargin40"
                    android:layout_height="@dimen/starryMargin40"
                    android:src="@mipmap/eshare_share_doc" />


                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/starryMargin10"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/eshare_bottom_doc_name"
                        android:textSize="@dimen/starryFontSize18"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/eshare_bottom_doc_sub"
                        android:textColor="@color/eshare_text_sub_gray"
                        android:textSize="@dimen/starryFontSize12" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/eshare_main_bottom_pic_ll"
                style="@style/eshare_bottom_pannel"
                android:layout_marginStart="@dimen/starryMargin20"
                android:layout_weight="1">

                <ImageView
                    android:layout_width="@dimen/starryMargin40"
                    android:layout_height="@dimen/starryMargin40"
                    android:src="@mipmap/eshare_share_pic" />


                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/starryMargin10"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/eshare_bottom_pic_name"
                        android:textSize="@dimen/starryFontSize16"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/eshare_bottom_pic_sub"
                        android:textColor="@color/eshare_text_sub_gray"
                        android:textSize="@dimen/starryFontSize12" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            style="@style/eshare_bottom_line"
            android:layout_marginStart="@dimen/starryMargin20"
            android:layout_marginTop="0dp"
            android:layout_marginEnd="@dimen/starryMargin20"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <LinearLayout
                android:id="@+id/eshare_main_bottom_vdo_ll"
                style="@style/eshare_bottom_pannel"
                android:layout_weight="1">

                <ImageView
                    android:layout_width="@dimen/starryMargin40"
                    android:layout_height="@dimen/starryMargin40"
                    android:src="@mipmap/eshare_share_vio" />


                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/starryMargin10"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/eshare_bottom_vdo_name"
                        android:textSize="@dimen/starryFontSize18"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/eshare_bottom_vdo_sub"
                        android:textColor="@color/eshare_text_sub_gray"
                        android:textSize="@dimen/starryFontSize12" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/eshare_main_bottom_cam_ll"
                style="@style/eshare_bottom_pannel"
                android:layout_marginStart="@dimen/starryMargin20"
                android:layout_weight="1">

                <ImageView
                    android:layout_width="@dimen/starryMargin40"
                    android:layout_height="@dimen/starryMargin40"
                    android:src="@mipmap/eshare_share_cam" />


                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/starryMargin10"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/eshare_bottom_cam_name"
                        android:textSize="@dimen/starryFontSize16"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/eshare_bottom_cam_sub"
                        android:textColor="@color/eshare_text_sub_gray"
                        android:textSize="@dimen/starryFontSize12" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <Button
            android:id="@+id/btn_mirror"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginTop="370dp"
            android:layout_marginEnd="@dimen/starryMargin20"
            android:text="反向投屏"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>