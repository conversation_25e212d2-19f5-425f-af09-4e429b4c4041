<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/starry_white_bg"
    android:fitsSystemWindows="true"
    >

    <include
        android:id="@+id/starry_layout_top_bar_rl"
        layout="@layout/starry_layout_top_bar" />

    <include
        android:id="@+id/starry_layout_top_bar_sel_rl"
        layout="@layout/starry_layout_top_bar_sel"
        android:visibility="invisible" />

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/msg_bottom_bar_del_outer_rl"
        android:layout_below="@+id/starry_layout_top_bar_rl">

        <com.scwang.smartrefresh.layout.header.ClassicsHeader
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin60"
            app:srlDrawableMarginRight="-10dp"
            app:srlDrawableProgress="@mipmap/loading"
            app:srlTextFailed="@string/starry_reflash_fail"
            app:srlTextFinish="@string/starry_reflash_success"
            app:srlTextLoading=""
            app:srlTextNothing=""
            app:srlTextPulling=""
            app:srlTextRefreshing=""
            app:srlTextRelease=""
            app:srlTextUpdate="" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/messageList"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/starry_layout_top_bar_rl"
            android:background="@color/starry_white_bg"
            android:overScrollMode="never"
            android:padding="@dimen/starryMargin10"
            android:visibility="visible" />

        <com.scwang.smartrefresh.layout.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin60"
            app:srlDrawableProgress="@mipmap/loading"
            app:srlTextFailed="@string/starry_reflash_fail"
            app:srlTextFinish=""
            app:srlTextLoading="@string/starry_reflash_loading"
            app:srlTextNothing="@string/starry_reflash_nothing"
            app:srlTextPulling="@string/starry_reflash_pulling"
            app:srlTextRefreshing=""
            app:srlTextRelease="" />

    </com.scwang.smartrefresh.layout.SmartRefreshLayout>

    <RelativeLayout
        android:id="@+id/msg_bottom_bar_del_outer_rl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerInParent="true"
        android:background="@color/white"
        android:gravity="center"
        android:paddingStart="@dimen/starryMargin10"
        android:paddingEnd="@dimen/starryMargin10"
        android:paddingBottom="@dimen/starryMargin5"
        android:visibility="visible">

        <RelativeLayout
            android:id="@+id/msg_bottom_bar_del_rl"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="visible">

            <ImageView
                android:id="@+id/msg_btn_del"
                android:layout_width="@dimen/starryMargin30"
                android:layout_height="@dimen/starryMargin30"
                android:layout_alignParentTop="true"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                android:src="@mipmap/starry_msg_del_disable" />

            <TextView
                android:id="@+id/msg_btn_del_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/msg_btn_del"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                android:text="@string/starry_msg_bottom_del"
                android:textColor="@color/starry_title_gray"
                android:textSize="@dimen/starryFontSize10" />

        </RelativeLayout>
    </RelativeLayout>

    <include
        layout="@layout/starry_layout_no_data"
        android:id="@+id/starry_msg_nodata"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/starry_layout_top_bar_rl"
        android:visibility="gone"
        />

</RelativeLayout>

