<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/starry_item_member_rl"
    android:layout_width="match_parent"
    android:background="@color/white"
    android:layout_height="@dimen/starryMargin50">

    <TextView
        android:id="@+id/starry_item_msg_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/starryMargin20"
        android:text=""
        android:ellipsize="end"
        android:maxEms="14"
        android:singleLine="true"
        android:layout_gravity="center"
        android:textColor="@color/title_black_color"
        android:textSize="@dimen/starryCompanySubTitle" />

    <ImageView
        android:id="@+id/starry_item_arrow"
        android:layout_width="@dimen/starryMargin8"
        android:layout_height="@dimen/starryMargin15"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/starryMargin20"
        android:background="@mipmap/starry_right_arrow"
        android:padding="@dimen/starryMargin10" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/starry_list_liner_color"
        android:layout_marginStart="@dimen/starryMargin20"
        android:layout_alignParentBottom="true"/>
</RelativeLayout>