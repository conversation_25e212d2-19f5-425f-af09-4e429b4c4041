<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gary_f9"
    android:focusable="true"
    android:fitsSystemWindows="true"
    android:focusableInTouchMode="true">

    <include
        android:id="@+id/et_connect_wifi_top_bar"
        layout="@layout/layout_wifi_top_bar" />


    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/et_connect_wifi_title"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginTop="20dp"
        android:gravity="center_vertical"
        android:text="@string/et_connect_wifi_to"
        android:textColor="@color/black_22"
        android:textSize="22sp"
        app:autoSizeTextType="uniform"
        app:autoSizeMinTextSize="19sp"
        app:autoSizeMaxTextSize="22sp"
        app:autoSizeStepGranularity="1sp"
        app:layout_constraintLeft_toLeftOf="@+id/et_connect_wifi_password_edt"
        app:layout_constraintRight_toRightOf="@+id/et_wifi_recyclerView"
        app:layout_constraintTop_toBottomOf="@+id/et_connect_wifi_top_bar" />



    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/tv_2.4g"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/only_2.4G"
        app:layout_constraintTop_toBottomOf="@+id/et_connect_wifi_title"
        android:layout_marginTop="5dp"
        app:layout_constraintLeft_toLeftOf="@+id/et_connect_wifi_password_edt"
        android:textColor="@color/gray_ac"
        android:textSize="12sp" />

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/et_connect_wifi_name_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="38.5dp"
        android:background="@color/gary_f9"
        android:maxLines="1"
        android:text=""
        android:textColor="@color/black_22"
        android:textSize="15sp"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintLeft_toLeftOf="@id/et_connect_wifi_password_edt"
        app:layout_constraintRight_toLeftOf="@+id/et_wifi_choose_wifi_btn"
        app:layout_constraintTop_toBottomOf="@+id/tv_2.4g" />

    <ImageView
        android:id="@+id/wifi_name_white_img"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:src="@mipmap/wifi_white"
        app:layout_constraintBottom_toBottomOf="@+id/et_connect_wifi_name_tv"
        app:layout_constraintDimensionRatio="h,70:120"
        app:layout_constraintRight_toLeftOf="@+id/et_wifi_choose_wifi_btn"
        app:layout_constraintTop_toTopOf="@+id/et_connect_wifi_name_tv" />


    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/et_wifi_choose_wifi_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="10dp"
        android:paddingLeft="10dp"
        android:text="@string/choose_network"
        android:textColor="@color/blue_29b0d7"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@+id/et_connect_wifi_name_tv"
        app:layout_constraintLeft_toRightOf="@+id/et_connect_wifi_name_tv"
        app:layout_constraintRight_toRightOf="@id/et_connect_wifi_password_edt"
        app:layout_constraintTop_toTopOf="@+id/et_connect_wifi_name_tv" />


    <com.czur.cloud.ui.component.NoHintEditText
        android:id="@+id/et_connect_wifi_password_edt"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_marginLeft="60dp"
        android:layout_marginRight="60dp"
        android:layout_marginTop="25dp"
        android:background="@drawable/btn_rec_5_bg_with_gray_ef"
        android:gravity="center"
        android:hint="@string/input_wifi_password"
        android:singleLine="true"
        android:textSize="15sp"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/et_connect_wifi_name_tv" />

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/wifi_warn_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:text="@string/not_support_5g"
        android:textColor="@color/gary_c4"
        android:textSize="9sp"
        app:layout_constraintLeft_toLeftOf="@+id/et_connect_wifi_password_edt"
        app:layout_constraintTop_toBottomOf="@+id/et_connect_wifi_password_edt" />


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/et_wifi_recyclerView"
        android:layout_width="0dp"
        android:layout_height="390dp"
        android:scrollbars="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@+id/et_connect_wifi_password_edt"
        app:layout_constraintRight_toRightOf="@+id/et_connect_wifi_password_edt"
        app:layout_constraintTop_toTopOf="@+id/et_connect_wifi_name_tv"
        app:layout_constraintVertical_bias="0" />

    <View
        android:id="@+id/et_wifi_border"
        android:layout_width="0dp"
        android:layout_height="390dp"
        android:background="@drawable/btn_rec_5_bg_with_stroke"
        app:layout_constraintBottom_toBottomOf="@+id/et_wifi_recyclerView"
        app:layout_constraintLeft_toLeftOf="@+id/et_wifi_recyclerView"
        app:layout_constraintRight_toRightOf="@+id/et_wifi_recyclerView"
        app:layout_constraintTop_toTopOf="@+id/et_wifi_recyclerView"
        app:layout_constraintVertical_bias="0" />

    <ImageView
        android:id="@+id/et_wifi_no_wifi_img"
        android:layout_width="51dp"
        android:layout_height="44dp"
        android:src="@mipmap/no_wifi_list"
        app:layout_constraintBottom_toTopOf="@+id/no_wifi_tv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <LinearLayout
        android:id="@+id/wifi_anim_ll"
        android:layout_width="0dp"
        android:layout_height="436dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="@+id/et_wifi_recyclerView"
        app:layout_constraintLeft_toLeftOf="@+id/et_wifi_recyclerView"
        app:layout_constraintRight_toRightOf="@+id/et_wifi_recyclerView"
        app:layout_constraintTop_toTopOf="@+id/et_wifi_recyclerView"
        app:layout_constraintVertical_bias="0">

        <View
            android:id="@+id/wifi_anim_view"
            android:layout_width="match_parent"
            android:layout_height="0dp" />

        <ImageView
            android:id="@+id/wifi_anim_img"
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:src="@mipmap/wifi_list_end" />

        <View
            android:id="@+id/wifi_anim_bg"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="@color/gary_f9" />

    </LinearLayout>


    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="438dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="@+id/et_wifi_recyclerView"
        app:layout_constraintLeft_toLeftOf="@+id/et_wifi_recyclerView"
        app:layout_constraintRight_toRightOf="@+id/et_wifi_recyclerView"
        app:layout_constraintTop_toTopOf="@+id/et_wifi_recyclerView"
        app:layout_constraintVertical_bias="0">

        <ImageView
            android:id="@+id/wifi_fold_anim_img"
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:layout_above="@+id/wifi_fold_anim_bg"
            android:src="@mipmap/wifi_list_end" />

        <View
            android:id="@+id/wifi_fold_anim_bg"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_alignParentBottom="true"
            android:background="@color/gary_f9" />

    </RelativeLayout>


    <TextView
        android:id="@+id/no_wifi_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="23.5dp"
        android:text="@string/no_wifi_list"
        android:textColor="@color/gary_c4"
        android:textSize="15sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/et_wifi_no_wifi_img" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/no_wifi_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="no_wifi_tv,et_wifi_no_wifi_img" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/wifi_list_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="et_wifi_recyclerView,et_wifi_border" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/wifi_input_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="wifi_warn_tv,et_connect_wifi_password_edt,et_connect_wifi_name_tv,wifi_name_white_img,et_wifi_choose_wifi_btn" />


</androidx.constraintlayout.widget.ConstraintLayout>

