<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/gary_f9">

    <RelativeLayout
        android:id="@+id/starry_home_top_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/starry_top_bar_height"
        android:background="@color/account_bg_color">

        <ImageView
            android:id="@+id/starry_home_back_btn"
            android:layout_width="30dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="9dp"
            android:padding="8dp"
            android:src="@mipmap/starry_back_btn" />

        <RelativeLayout
            android:id="@+id/starry_home_more_btn"
            android:layout_width="44dp"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:visibility="visible">

            <ImageView
                android:id="@+id/starry_home_more_iv"
                android:layout_width="18.5dp"
                android:layout_height="17.5dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="18.5dp"
                android:background="@mipmap/starry_more_btn" />

            <View
                android:id="@+id/sitting_top_red_point"
                android:layout_width="5dp"
                android:layout_height="5dp"
                android:layout_alignParentTop="true"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="14dp"
                android:background="@drawable/circle_red"
                android:visibility="gone" />

        </RelativeLayout>

        <TextView
            android:id="@+id/starry_home_clear_btn"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="@dimen/starryMargin15"
            android:gravity="center"
            android:padding="@dimen/dp_5"
            android:text="@string/starry_recently_clear_btn"
            android:textColor="@color/starry_text_color_red"
            android:textSize="@dimen/starryFontSize16"
            android:visibility="gone" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/sitting_home_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/smart_starry"
            android:textColor="@color/white"
            android:textSize="18sp" />

    </RelativeLayout>

    <FrameLayout
        android:id="@+id/starry_home_frameLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/starry_home_top_bar"
        android:layout_marginBottom="@dimen/starryMargin50">

    </FrameLayout>

    <RelativeLayout
        android:id="@+id/index_bottom_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/starryMargin50"
        android:layout_alignParentBottom="true"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:layout_centerHorizontal="true"
        android:background="@color/starry_title_bar_bg_white"
        android:visibility="visible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            >

            <RelativeLayout
                android:id="@+id/bottom_tab_meet"
                style="@style/index_radio_button"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                >

                <ImageView
                    android:id="@+id/bottom_tab_meet_img"
                    android:layout_width="@dimen/starry_bottom_width"
                    android:layout_height="@dimen/starry_bottom_height"
                    android:layout_centerHorizontal="true"
                    android:src="@drawable/selector_starry_meet_icon" />

                <View
                    android:id="@+id/bottom_tab_meet_red_point"
                    android:layout_width="@dimen/starry_bottom_red_point"
                    android:layout_height="@dimen/starry_bottom_red_point"
                    android:layout_alignEnd="@+id/bottom_tab_meet_img"
                    android:background="@drawable/circle_red"
                    android:visibility="gone" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/bottom_tab_meet_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/bottom_tab_meet_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/starry_bottom_bar_title_margin_top"
                    android:text="@string/bottom_tab_meet"
                    android:textColor="@drawable/selector_starry_home_text_color"
                    android:textSize="@dimen/starry_bottom_bar_title_size" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/bottom_tab_schedule"
                style="@style/index_radio_button"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/bottom_tab_schedule_img"
                    android:layout_width="@dimen/starry_bottom_width"
                    android:layout_height="@dimen/starry_bottom_height"
                    android:layout_centerHorizontal="true"
                    android:src="@drawable/starry_selector_schedule_icon" />

                <View
                    android:id="@+id/bottom_tab_schedule_red_point"
                    android:layout_width="@dimen/starry_bottom_red_point"
                    android:layout_height="@dimen/starry_bottom_red_point"
                    android:layout_alignEnd="@+id/bottom_tab_schedule_img"
                    android:background="@drawable/circle_red"
                    android:visibility="gone" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/bottom_tab_schedule_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/bottom_tab_schedule_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/starry_bottom_bar_title_margin_top"
                    android:text="@string/bottom_tab_schedule"
                    android:textColor="@drawable/selector_starry_home_text_color"
                    android:textSize="@dimen/starry_bottom_bar_title_size" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/bottom_tab_recently"
                style="@style/index_radio_button"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                >

                <ImageView
                    android:id="@+id/bottom_tab_recently_img"
                    android:layout_width="@dimen/starry_bottom_width"
                    android:layout_height="@dimen/starry_bottom_height"
                    android:layout_centerHorizontal="true"
                    android:src="@drawable/selector_starry_recently_icon" />

                <View
                    android:id="@+id/bottom_tab_recently_red_point"
                    android:layout_width="@dimen/starry_bottom_red_point"
                    android:layout_height="@dimen/starry_bottom_red_point"
                    android:layout_alignEnd="@+id/bottom_tab_recently_img"
                    android:background="@drawable/circle_red"
                    android:visibility="gone" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/bottom_tab_recently_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/bottom_tab_recently_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/starry_bottom_bar_title_margin_top"
                    android:text="@string/bottom_tab_recently"
                    android:textColor="@drawable/selector_starry_home_text_color"
                    android:textSize="@dimen/starry_bottom_bar_title_size" />

            </RelativeLayout>

        </LinearLayout>


    </RelativeLayout>

    <include
        android:id="@+id/starry_main_no_network"
        layout="@layout/starry_no_network_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/starry_home_top_bar"
        android:visibility="gone" />


</RelativeLayout>
