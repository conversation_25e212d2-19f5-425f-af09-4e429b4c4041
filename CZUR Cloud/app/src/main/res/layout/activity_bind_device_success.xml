<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="194.5dp"
        android:orientation="vertical">

        <ImageView
            android:layout_width="130dp"
            android:layout_height="130dp"
            android:layout_gravity="center_horizontal"
            android:background="@mipmap/add_success_face_img" />

        <TextView
            android:id="@+id/add_success_tv"
            android:layout_marginTop="46.5dp"
            android:layout_gravity="center_horizontal"
            android:textColor="@color/normal_blue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textStyle="bold"
            android:gravity="center"
            android:textSize="18sp"
            />
    </LinearLayout>


    <TextView
        android:id="@+id/add_equipment_success_btn"
        android:layout_width="250dp"
        android:layout_height="40dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="30dp"
        android:background="@drawable/btn_rec_5_bg_with_blue"
        android:gravity="center"
        android:text="@string/finish"
        android:textColor="@color/white"
        android:textStyle="bold" />
</RelativeLayout>