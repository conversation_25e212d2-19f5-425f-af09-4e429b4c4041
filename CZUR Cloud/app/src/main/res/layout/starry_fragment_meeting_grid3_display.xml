<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/displayGrid3CL"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/transparent"
    android:animateLayoutChanges="false" >

    <com.czur.cloud.ui.starry.meeting.fragment.newmain.MeetingViewGrid
        android:id="@+id/displayGrid3Container"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        style="@style/display_grid_container"/>

</androidx.constraintlayout.widget.ConstraintLayout>