<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/equipment_rl"
    android:layout_width="match_parent"
    android:layout_height="45dp"
    android:background="@color/blue_29b0d7">

    <TextView
        android:id="@+id/et_device_name_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="18.5dp"
        android:ellipsize="end"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="15sp"
        android:textStyle="bold"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/et_device_more_rl"
        app:layout_constraintTop_toTopOf="parent" />


    <RelativeLayout
        android:id="@+id/et_device_more_rl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constrainedWidth="true"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintLeft_toRightOf="@+id/et_device_name_tv"
        app:layout_constraintRight_toLeftOf="@+id/et_device_to_use_btn">

        <ImageView
            android:id="@+id/et_device_more_btn"
            android:layout_width="51dp"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:layout_marginLeft="3.5dp"
            android:paddingLeft="18.5dp"
            android:paddingRight="18.5dp"
            android:src="@mipmap/et_setting_icon" />
    </RelativeLayout>


    <TextView
        android:id="@+id/et_device_to_use_btn"
        android:layout_width="59.5dp"
        android:layout_height="23dp"
        android:layout_centerInParent="true"
        android:layout_marginRight="18.5dp"
        android:layout_toLeftOf="@+id/et_device_wifi_btn"
        android:background="@drawable/btn_rec_5_bg_with_white"
        android:gravity="center"
        android:textColor="@color/blue_29b0d7"
        android:textSize="9sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/et_device_wifi_btn"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:visibility="gone"
        android:id="@+id/et_device_wifi_btn"
        android:layout_width="59.5dp"
        android:layout_height="23dp"
        android:layout_marginRight="18.5dp"
        android:background="@drawable/selector_et_use_btn"
        android:gravity="center"
        android:text="@string/et_connect_wifi"
        android:textColor="@color/blue_29b0d7"
        android:textSize="9sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>
