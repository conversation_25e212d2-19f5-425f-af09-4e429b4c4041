<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rl_no_network"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gary_f9"
    android:clickable="true"
    android:focusable="true">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <ImageView
            android:layout_width="96dp"
            android:layout_height="65dp"
            android:src="@mipmap/no_network" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/tv_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/no_network"
            android:textColor="@color/gray_d8d8d8"
            android:textSize="14sp" />


        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/tv_click_refresh"
            android:layout_width="wrap_content"
            android:minWidth="90dp"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:layout_height="30dp"
            android:layout_marginTop="34dp"
            android:background="@drawable/btn_rec_5_blue_bg"
            android:gravity="center"
            android:text="@string/click_refresh"
            android:textColor="@color/blue_29b0d7" />

    </LinearLayout>


</RelativeLayout>