<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_centerHorizontal="true"
    android:layout_gravity="center"
    android:layout_marginTop="10dp">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:background="@drawable/jing_circle_small_with_code_blue"
            android:orientation="horizontal"
            android:gravity="center">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/jing_circle_small_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="--"
                android:textColor="@color/white"
                android:textSize="24sp" />

            <TextView
                android:id="@+id/jing_circle_small_per"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="%"
                android:textColor="@color/white"
                android:textSize="@dimen/jingMainTitleSize" />

        </LinearLayout>

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/jing_circle_small_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text=""
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="@dimen/jingMainSmallCycleTitleSize" />

    </LinearLayout>
</RelativeLayout>