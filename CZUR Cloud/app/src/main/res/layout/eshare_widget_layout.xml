<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/eshare_search_ll"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    >

    <ImageView
        android:id="@+id/eshare_scan"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:scaleType="centerCrop"
        android:src="@mipmap/eshare_widget_logo"
        android:gravity="center"
        android:background="@drawable/set_img_round"
        android:layout_centerHorizontal="true"
        />

    <ImageView
        android:id="@+id/eshare_scan_czur_logo"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:scaleType="centerCrop"
        android:background="@drawable/circle_with_white"
        android:src="@mipmap/ic_czur_logo_round"
        android:layout_alignBottom="@id/eshare_scan"
        android:layout_alignEnd="@id/eshare_scan"
        android:layout_marginEnd="-13dp"
        android:layout_marginBottom="-5dp"
        android:padding="1dp"
        />

    <TextView
        android:id="@+id/eshare_scan_blank"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/shortcut_scan"
        android:layout_marginTop="-20dp"
        android:textSize="16sp"
        android:gravity="center"
        android:layout_below="@+id/eshare_scan"
        android:visibility="invisible"
        android:layout_centerHorizontal="true"
        />

    <TextView
        android:id="@+id/eshare_scan_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/shortcut_scan"
        android:layout_marginTop="6dp"
        android:textSize="12sp"
        android:gravity="center"
        android:textColor="@color/whiteOpaque100"
        android:layout_centerHorizontal="true"
        android:layout_below="@+id/eshare_scan"
        android:visibility="visible"
        />

</RelativeLayout>