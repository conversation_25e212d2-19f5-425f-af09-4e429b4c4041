<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:keepScreenOn="true">

    <View
        android:id="@+id/call_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:drawingCacheQuality="low"
        android:layerType="software"
        android:background="@drawable/call_bg" />

    <com.czur.cloud.ui.starry.meeting.widget.HeadImageView
        android:id="@+id/callin_icon"
        android:layout_width="@dimen/starryMargin70"
        android:layout_height="@dimen/starryMargin70"
        android:layout_centerVertical="true"
        android:layout_centerInParent="true"
        android:layout_marginTop="@dimen/starryMargin120"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:roundPercent="1"/>

    <TextView
        android:id="@+id/callin_username"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="艾华"
        android:text=""
        android:textColor="@color/white"
        android:textSize="@dimen/starryCallInTitle"
        android:ellipsize="end"
        android:maxEms="14"
        android:maxLines="1"
        android:layout_marginTop="@dimen/starryMargin10"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/callin_icon"
        app:layout_constraintVertical_chainStyle="packed"
        />

    <TextView
        android:id="@+id/callin_mobile"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="123456"
        android:text=""
        android:textColor="@color/white"
        android:textSize="@dimen/starryCallInNo"
        app:layout_constraintTop_toBottomOf="@+id/callin_username"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        />

    <TextView
        android:id="@+id/callin_welcome"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/starry_request_video"
        android:textColor="@color/white"
        android:textSize="@dimen/starryCallInSubTitle"
        android:layout_marginTop="@dimen/starryMargin15"
        app:layout_constraintTop_toBottomOf="@+id/callin_mobile"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        />

    <TextView
        android:id="@+id/callin_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="主题：设计讨论"
        android:text="@string/starry_callin_title"
        android:textColor="@color/white"
        android:textSize="@dimen/starryCallInSubTitle"
        android:ellipsize="end"
        android:maxEms="30"
        android:maxLines="2"
        android:gravity="center"
        android:layout_marginTop="@dimen/starryMargin5"
        android:layout_marginStart="@dimen/dp10"
        android:layout_marginEnd="@dimen/dp10"
        app:layout_constraintTop_toBottomOf="@+id/callin_welcome"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        />

    <LinearLayout
        android:id="@+id/callin_setting_cam"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/starryMargin120"
        app:layout_constraintTop_toBottomOf="@+id/callin_title"
        app:layout_constraintLeft_toLeftOf="@+id/callin_setting_mic"
        android:gravity="center_vertical"
        >

        <ImageView
            android:id="@+id/callin_setting_cam_check"
            android:layout_width="@dimen/starryMargin20"
            android:layout_height="@dimen/starryMargin20"
            android:src="@mipmap/starry_callin_unselect"
            android:layout_marginEnd="@dimen/starryMargin10"
            />

        <TextView
            android:id="@+id/callin_setting_cam_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/starry_setting_cam"
            android:textColor="@color/white"
            android:textSize="@dimen/starryCallInSetting"
            />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/callin_setting_mic"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/starryMargin20"
        app:layout_constraintTop_toBottomOf="@+id/callin_setting_cam"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:gravity="center_vertical"
        >

        <ImageView
            android:id="@+id/callin_setting_mic_check"
            android:layout_width="@dimen/starryMargin20"
            android:layout_height="@dimen/starryMargin20"
            android:src="@mipmap/starry_callin_select"
            android:layout_marginEnd="@dimen/starryMargin10"
            />

        <TextView
            android:id="@+id/callin_setting_mic_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/starry_setting_mic"
            android:textColor="@color/white"
            android:textSize="@dimen/starryCallInSetting"
            />
    </LinearLayout>

    <ImageView
        android:id="@+id/call_out_btn"
        android:layout_width="@dimen/starryMargin60"
        android:layout_height="@dimen/starryMargin60"
        android:layout_marginEnd="@dimen/starryMargin100"
        android:layout_marginBottom="@dimen/starryMargin60"
        android:src="@mipmap/call_out_icon"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/call_in_btn" />

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/callin_later"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/starryMargin8"
        android:text="@string/starry_call_out"
        android:textColor="@color/white"
        android:textSize="@dimen/starryCallInNo"
        app:layout_constraintLeft_toLeftOf="@+id/call_out_btn"
        app:layout_constraintRight_toRightOf="@+id/call_out_btn"
        app:layout_constraintTop_toBottomOf="@+id/call_out_btn" />

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/callin_join"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/starryMargin8"
        android:text="@string/starry_call_in"
        android:textColor="@color/white"
        android:textSize="@dimen/starryCallInNo"
        app:layout_constraintLeft_toLeftOf="@+id/call_in_btn"
        app:layout_constraintRight_toRightOf="@+id/call_in_btn"
        app:layout_constraintTop_toBottomOf="@+id/call_in_btn" />

    <ImageView
        android:id="@+id/call_in_btn"
        android:layout_width="@dimen/starryMargin60"
        android:layout_height="@dimen/starryMargin60"
        android:src="@mipmap/call_in_icon"
        app:layout_constraintBottom_toBottomOf="@+id/call_out_btn"
        app:layout_constraintLeft_toRightOf="@+id/call_out_btn"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
