<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="243dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:background="@drawable/btn_rec_5_bg_with_white"
        android:gravity="center_horizontal"
        android:orientation="vertical">


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:layout_marginLeft="29dp"
            android:layout_marginRight="29dp">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text=""
                android:textColor="@color/black_22"
                android:textSize="16sp" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/message"
                android:layout_marginTop="12.5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/title"
                android:gravity="center"
                android:text=""
                android:textColor="@color/black_22"
                android:textSize="12sp" />


        </RelativeLayout>

        <EditText
            android:paddingLeft="3dp"
            android:id="@+id/edt"
            android:maxLength="20"
            android:singleLine="true"
            android:layout_width="216.5dp"
            android:textColor="@color/black_22"
            android:textSize="12sp"
            android:background="@drawable/dialog_rec_1_bg_with_white_with_stroke"
            android:layout_marginBottom="10dp"
            android:layout_height="22dp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/gray_e5"/>
        <!-- button -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:orientation="horizontal">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/negative_button"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:textColor="@color/black_22"
                android:textSize="14sp" />

            <View
                android:layout_width="0.5dp"
                android:layout_height="match_parent"
                android:background="@color/gray_e5"/>

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/positive_button"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text=""
                android:textColor="@color/blue_positive_button"
                android:textSize="14sp" />
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>