<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/gary_f9"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <include
        android:id="@+id/select_equipment_rl"
        layout="@layout/layout_normal_top_bar" />

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/select_equipment_rl"
        android:layout_above="@+id/select_btn_ll"
        app:srlEnableAutoLoadMore="false"
        app:srlEnableLoadMore="false"
        app:srlEnableOverScrollBounce="true"
        app:srlEnableOverScrollDrag="true"
        app:srlEnableRefresh="false">

        <com.czur.cloud.ui.component.damping.VerticalScrollView
            android:id="@+id/home_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:overScrollMode="never"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/select_equipment_rl"
                android:layout_marginTop="23dp"
                android:orientation="vertical">

                <!--        3智能投影仪        -->
                <com.czur.cloud.ui.component.MediumBoldTextView
                    style="@style/add_equipment_title"
                    android:text="@string/smart_starry_title" />

                <LinearLayout style="@style/add_equipment_outer_ll">

                    <LinearLayout
                        android:id="@+id/equipment_starry_ll"
                        style="@style/add_equipment_panel_ll">


                        <RelativeLayout style="@style/add_equipment_panel_rl">


                            <ImageView
                                style="@style/add_equipment_image_icon"
                                android:layout_centerInParent="true"
                                android:layout_marginStart="@dimen/starryMargin8"
                                android:layout_marginTop="@dimen/starryMargin8"
                                android:layout_marginEnd="@dimen/starryMargin8"
                                android:layout_marginBottom="@dimen/starryMargin8"
                                android:background="@mipmap/mini_starry" />

                            <ImageView
                                android:id="@+id/add_equipment_starry_selection"
                                style="@style/add_equipment_image_select" />

                            <RelativeLayout
                                android:id="@+id/add_equipment_starry_has_added_rl"
                                style="@style/add_equipment_has_added_rl">

                                <ImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@color/whiteOpaque50" />

                                <com.czur.cloud.ui.component.MediumBoldTextView style="@style/add_equipment_text_add" />

                            </RelativeLayout>
                        </RelativeLayout>

                        <com.czur.cloud.ui.component.MediumBoldTextView
                            android:id="@+id/equipment_starry_tv"
                            style="@style/add_equipment_name"
                            android:text="@string/smart_starry" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/equipment_eshare_ll"
                        style="@style/add_equipment_panel_ll"
                        android:visibility="visible">

                        <RelativeLayout style="@style/add_equipment_panel_rl">


                            <ImageView
                                style="@style/add_equipment_image_icon"
                                android:layout_marginStart="7dp"
                                android:layout_marginTop="7dp"
                                android:layout_marginEnd="7dp"
                                android:layout_marginBottom="7dp"
                                android:background="@mipmap/mini_eshare" />

                            <ImageView
                                android:id="@+id/add_equipment_eshare_selection"
                                style="@style/add_equipment_image_select" />

                            <RelativeLayout
                                android:id="@+id/add_equipment_eshare_has_added_rl"
                                style="@style/add_equipment_has_added_rl">

                                <ImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@color/whiteOpaque50" />

                                <com.czur.cloud.ui.component.MediumBoldTextView style="@style/add_equipment_text_add" />

                            </RelativeLayout>
                        </RelativeLayout>

                        <com.czur.cloud.ui.component.MediumBoldTextView
                            android:id="@+id/equipment_eshare_tv"
                            style="@style/add_equipment_name"
                            android:text="@string/smart_eshare" />

                    </LinearLayout>

                </LinearLayout>

                <!--        1智能扫描仪        -->
                <com.czur.cloud.ui.component.MediumBoldTextView
                    style="@style/add_equipment_title"
                    android:text="@string/smart_scanner" />

                <LinearLayout style="@style/add_equipment_outer_ll">

                    <LinearLayout
                        android:id="@+id/equipment_aura_ll"
                        style="@style/add_equipment_panel_ll">

                        <RelativeLayout style="@style/add_equipment_panel_rl">


                            <ImageView
                                style="@style/add_equipment_image_icon"
                                android:layout_centerInParent="true"
                                android:src="@mipmap/mini_aura" />

                            <ImageView
                                android:id="@+id/add_equipment_aura_selection"
                                style="@style/add_equipment_image_select" />

                            <RelativeLayout
                                android:id="@+id/add_equipment_aura_has_added_rl"
                                style="@style/add_equipment_has_added_rl">

                                <ImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@color/whiteOpaque50" />

                                <com.czur.cloud.ui.component.MediumBoldTextView style="@style/add_equipment_text_add" />

                            </RelativeLayout>
                        </RelativeLayout>

                        <com.czur.cloud.ui.component.MediumBoldTextView
                            android:id="@+id/equipment_aura_tv"
                            style="@style/add_equipment_name"
                            android:text="@string/AURA" />

                    </LinearLayout>


                    <LinearLayout
                        android:id="@+id/equipment_et_ll"
                        style="@style/add_equipment_panel_ll">

                        <RelativeLayout style="@style/add_equipment_panel_rl">


                            <ImageView
                                style="@style/add_equipment_image_icon"
                                android:layout_centerInParent="true"
                                android:src="@mipmap/mini_et" />

                            <ImageView
                                android:id="@+id/add_equipment_et_selection"
                                style="@style/add_equipment_image_select" />

                            <RelativeLayout
                                android:id="@+id/add_equipment_et_has_added_rl"
                                style="@style/add_equipment_has_added_rl">

                                <ImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@color/whiteOpaque50" />

                                <com.czur.cloud.ui.component.MediumBoldTextView style="@style/add_equipment_text_add" />

                            </RelativeLayout>
                        </RelativeLayout>

                        <com.czur.cloud.ui.component.MediumBoldTextView
                            android:id="@+id/equipment_et_tv"
                            style="@style/add_equipment_name"
                            android:text="@string/ET" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/equipment_ets_ll"
                        style="@style/add_equipment_panel_ll">

                        <RelativeLayout style="@style/add_equipment_panel_rl">


                            <ImageView
                                style="@style/add_equipment_image_icon"
                                android:background="@mipmap/mini_aura" />

                            <ImageView
                                android:id="@+id/add_equipment_ets_selection"
                                style="@style/add_equipment_image_select" />

                            <RelativeLayout
                                android:id="@+id/add_equipment_ets_has_added_rl"
                                style="@style/add_equipment_has_added_rl">

                                <ImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@color/whiteOpaque50" />

                                <com.czur.cloud.ui.component.MediumBoldTextView style="@style/add_equipment_text_add" />

                            </RelativeLayout>
                        </RelativeLayout>

                        <com.czur.cloud.ui.component.MediumBoldTextView
                            android:id="@+id/equipment_ets_tv"
                            style="@style/add_equipment_name"
                            android:text="@string/ETS" />

                    </LinearLayout>

                </LinearLayout>

                <!--        2智能坐姿仪        -->
                <com.czur.cloud.ui.component.MediumBoldTextView
                    style="@style/add_equipment_title"
                    android:text="@string/smart_sitting_title" />

                <LinearLayout style="@style/add_equipment_outer_ll">

                    <LinearLayout
                        android:id="@+id/equipment_sitting_ll"
                        style="@style/add_equipment_panel_ll">


                        <RelativeLayout style="@style/add_equipment_panel_rl">


                            <ImageView
                                style="@style/add_equipment_image_icon"
                                android:layout_marginStart="@dimen/starryMargin8"
                                android:layout_marginTop="@dimen/starryMargin8"
                                android:layout_marginEnd="@dimen/starryMargin8"
                                android:layout_marginBottom="@dimen/starryMargin8"
                                android:background="@mipmap/mini_sitting" />

                            <ImageView
                                android:id="@+id/add_equipment_sitting_selection"
                                style="@style/add_equipment_image_select" />

                            <RelativeLayout
                                android:id="@+id/add_equipment_sitting_has_added_rl"
                                style="@style/add_equipment_has_added_rl">

                                <ImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@color/whiteOpaque50" />

                                <com.czur.cloud.ui.component.MediumBoldTextView style="@style/add_equipment_text_add" />

                            </RelativeLayout>
                        </RelativeLayout>

                        <com.czur.cloud.ui.component.MediumBoldTextView
                            android:id="@+id/equipment_sitting_tv"
                            style="@style/add_equipment_name"
                            android:text="@string/smart_sitting" />

                    </LinearLayout>

                </LinearLayout>

                <!--        4智能陪伴仪        -->
                <com.czur.cloud.ui.component.MediumBoldTextView
                    style="@style/add_equipment_title"
                    android:text="@string/smart_accompany_title" />

                <LinearLayout style="@style/add_equipment_outer_ll">

                    <LinearLayout
                        android:id="@+id/equipment_aura_mate_ll"
                        style="@style/add_equipment_panel_ll">

                        <RelativeLayout style="@style/add_equipment_panel_rl">


                            <ImageView
                                style="@style/add_equipment_image_icon"
                                android:layout_margin="@dimen/starryMargin8"
                                android:layout_marginStart="@dimen/starryMargin8"
                                android:layout_marginTop="@dimen/starryMargin8"
                                android:layout_marginEnd="@dimen/starryMargin8"
                                android:layout_marginBottom="@dimen/starryMargin8"
                                android:src="@mipmap/mini_aura_mate" />

                            <ImageView
                                android:id="@+id/add_equipment_aura_mate_selection"
                                style="@style/add_equipment_image_select" />

                            <RelativeLayout
                                android:id="@+id/add_equipment_aura_mate_has_added_rl"
                                style="@style/add_equipment_has_added_rl">

                                <ImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@color/whiteOpaque50" />

                                <com.czur.cloud.ui.component.MediumBoldTextView style="@style/add_equipment_text_add" />

                            </RelativeLayout>
                        </RelativeLayout>

                        <com.czur.cloud.ui.component.MediumBoldTextView
                            android:id="@+id/equipment_aura_mate_tv"
                            style="@style/add_equipment_name"
                            android:text="@string/AURA_MATE" />

                    </LinearLayout>

                </LinearLayout>

                <!--        5智能笔记本        -->
                <com.czur.cloud.ui.component.MediumBoldTextView
                    style="@style/add_equipment_title"
                    android:text="@string/smart_office_title" />

                <LinearLayout style="@style/add_equipment_outer_ll">

                    <LinearLayout
                        android:id="@+id/equipment_books_ll"
                        style="@style/add_equipment_panel_ll"
                        android:layout_marginBottom="110dp">

                        <RelativeLayout style="@style/add_equipment_panel_rl">

                            <ImageView
                                style="@style/add_equipment_image_icon"
                                android:layout_margin="@dimen/starryMargin8"
                                android:background="@mipmap/mini_books" />

                            <ImageView
                                android:id="@+id/add_equipment_books_selection"
                                style="@style/add_equipment_image_select" />

                            <RelativeLayout
                                android:id="@+id/add_equipment_books_has_added_rl"
                                style="@style/add_equipment_has_added_rl">

                                <ImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@color/whiteOpaque50" />

                                <com.czur.cloud.ui.component.MediumBoldTextView style="@style/add_equipment_text_add" />

                            </RelativeLayout>
                        </RelativeLayout>

                        <com.czur.cloud.ui.component.MediumBoldTextView
                            android:id="@+id/equipment_books_tv"
                            style="@style/add_equipment_name"
                            android:text="@string/smart_office" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>
        </com.czur.cloud.ui.component.damping.VerticalScrollView>
    </com.scwang.smartrefresh.layout.SmartRefreshLayout>


    <LinearLayout
        android:id="@+id/select_btn_ll"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_alignParentBottom="true"
        android:gravity="center">

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/chose_equipment_btn"
            android:layout_width="250dp"
            android:layout_height="40dp"
            android:background="@drawable/selector_select_equipment_btn"
            android:gravity="center"
            android:text="@string/checked"
            android:textColor="@color/white" />

    </LinearLayout>

</RelativeLayout>