<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/aura_mate_empty_rl"
    android:layout_width="290dp"
    android:layout_height="530dp"
    android:layout_marginLeft="7dp"
    android:layout_marginRight="7dp"
    android:layout_marginTop="15dp"
    android:background="@drawable/btn_rec_10_bg_with_code_blue">

    <LinearLayout
        android:id="@+id/aura_home_empty_inner_rl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/aura_home_add_btn"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:layout_gravity="center_horizontal"
            android:src="@mipmap/aura_home_add" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/add_aura_mate_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="18dp"
            android:text="@string/aura_home_add_text"
            android:textColor="@color/gary_ff"
            android:textSize="16sp" />

    </LinearLayout>


</RelativeLayout>

