<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/white">

    <RelativeLayout
        android:id="@+id/starry_search_rl"
        android:layout_width="match_parent"
        android:layout_height="@dimen/starryMargin60"
        android:gravity="center_vertical"
        android:paddingTop="@dimen/starryMargin10"
        android:paddingStart="@dimen/starryMargin20"
        android:paddingEnd="@dimen/starryMargin20"
        android:paddingBottom="@dimen/starryMargin10"
        android:background="@color/starry_white_bg"
        >

        <EditText
            android:id="@+id/starry_search_et"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/starryMargin10"
            android:layout_toStartOf="@+id/starry_search_tv"
            android:background="@drawable/starry_rec_10_bg_with_gray"
            android:drawableStart="@mipmap/starry_search_s"
            android:drawablePadding="@dimen/starryMargin10"
            android:hint="@string/starry_search_key"
            android:paddingStart="@dimen/starryMargin10"
            android:paddingEnd="@dimen/starrySearchPaddingEnd"
            android:singleLine="true"
            android:textCursorDrawable="@drawable/edittext_cursor_blue" />

        <ImageView
            android:id="@+id/starry_search_del_iv"
            android:layout_width="@dimen/starryMargin30"
            android:layout_height="@dimen/starryMargin30"
            android:layout_alignEnd="@+id/starry_search_et"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/starryMargin10"
            android:padding="@dimen/dp_5"
            android:scaleType="fitXY"
            android:src="@mipmap/tag_delete_icon"
            android:visibility="visible" />

        <TextView
            android:id="@+id/starry_search_tv"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:gravity="center_vertical"
            android:padding="@dimen/starryMargin10"
            android:text="@string/starry_top_ok_btn"
            android:textColor="@color/starry_top_btn_blue"
            android:textSize="@dimen/starryTopBtnSize" />

    </RelativeLayout>

    <TextView
        android:id="@+id/starry_search_result_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:layout_marginTop="@dimen/starryMargin10"
        android:layout_marginStart="@dimen/starryMargin10"
        android:padding="@dimen/starryMargin10"
        android:text="@string/starry_search_result_msg"
        android:textColor="@color/starry_title_gray"
        android:textSize="@dimen/starryCallInNo"
        android:visibility="invisible"
        android:layout_below="@+id/starry_search_rl"/>

    <TextView
        android:id="@+id/starry_no_search_result_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/starry_search_rl"
        android:layout_marginTop="25dp"
        android:gravity="center"
        android:padding="@dimen/starryMargin10"
        android:text="@string/starry_no_contact_search"
        android:textColor="@color/starry_title_gray"
        android:textSize="14sp"
        android:visibility="visible" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view_search_result_company"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/space_view"
        android:layout_below="@+id/starry_search_result_tv"
        android:background="@color/starry_white_bg"
        android:overScrollMode="never"
        android:paddingLeft="20dp"
        android:paddingRight="20dp"
        android:visibility="visible" />

    <View
        android:id="@+id/space_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_alignParentBottom="true"/>

</RelativeLayout>