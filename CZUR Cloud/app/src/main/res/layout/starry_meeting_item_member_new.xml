<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="70dp"
    tools:background="@color/white"
    tools:ignore="RtlHardcoded">


    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center_vertical">

        <com.czur.cloud.ui.starry.meeting.widget.HeadImageView
            android:id="@+id/itemMemberHeadIv"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginStart="25dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:roundPercent="1" />
        <ImageView
            android:id="@+id/itemMemberAdminIv"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginStart="60dp"
            android:visibility="gone"
            android:src="@drawable/starry_meeting_member_badge_admin"
            app:layout_constraintBottom_toBottomOf="@id/itemMemberHeadIv"
            app:layout_constraintLeft_toLeftOf="parent"/>

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:layout_weight="1"
            >

        <TextView
            android:id="@+id/itemMemberNickNameTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/starryMargin10"
            android:ellipsize="end"
            android:gravity="left|center_vertical"
            android:layout_centerVertical="true"
            android:singleLine="true"
            android:textColor="#5E5E5E"
            android:textSize="@dimen/meeting_memberlist_name_size"
            tools:text="FelixFelix"
            android:paddingEnd="30dp"
            />

        <ImageView
            android:id="@+id/name_edit_btn"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/starry_edit_mine_name"
            android:padding="8dp"
            android:gravity="center_vertical"
            android:layout_marginStart="@dimen/dp_5"
            android:layout_marginEnd="@dimen/dp_5"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/itemMemberNickNameTv"
            android:visibility="visible"
            android:layout_alignEnd="@id/itemMemberNickNameTv"
            />

        </RelativeLayout>

        <TextView
            android:id="@+id/itemMemberStatusTv"
            style="@style/tv_item_member_label"
            android:text="@string/member_status_calling"
            android:singleLine="true"
            android:textColor="@color/starry_text_color_blue"
            android:textSize="@dimen/meeting_memberlist_neterror_size"
            android:visibility="visible"
            tools:text="等待加入..." />

        <TextView
            android:id="@+id/itemMemberNetworkErrorTv"
            style="@style/tv_item_member_label"
            android:text="@string/member_status_network_error"
            android:textColor="@color/starry_text_color_red"
            android:visibility="gone"
            android:singleLine="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/itemMemberNickNameTv"
            android:layout_marginStart="@dimen/dp_5"
            app:layout_constraintTop_toTopOf="parent"
            android:textSize="@dimen/meeting_memberlist_neterror_size"
            android:maxLines="1"
            android:ellipsize="end"
            tools:text="网络异常"/>


    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingEnd="10dp">

        <ImageView
            android:id="@+id/itemMemberAudioIcon"
            style="@style/iv_item_member_icon"
            android:src="@mipmap/starry_meeting_member_icon_btn_mic_gray"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/itemMemberVideoIcon"
            style="@style/iv_item_member_icon"
            android:src="@mipmap/starry_meeting_member_icon_btn_video_gray"
            android:visibility="gone" />

        <TextView
            android:id="@+id/itemMemberAdminTv"
            style="@style/tv_item_member_label"
            android:background="@drawable/starry_btn_rec_5_bg_with_blue"
            android:paddingStart="@dimen/starryMargin10"
            android:paddingEnd="@dimen/starryMargin10"
            android:text="@string/member_admin_calling"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:textSize="@dimen/meeting_memberlist_neterror_size"
            tools:text="管理员" />

        <LinearLayout
            android:id="@+id/itemMemberRemindIcon"
            android:layout_width="@dimen/starryMargin80"
            android:layout_height="@dimen/starryMargin30"
            android:layout_marginEnd="2dp"
            android:background="@drawable/starry_btn_rec_5_bg_with_lightgray"
            android:gravity="center"
            android:orientation="horizontal"
            >

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_item_remind" />

            <View
                android:layout_width="6dp"
                android:layout_height="wrap_content" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/member_list_remind"
                android:textColor="@color/starry_text_color_red"
                android:textSize="@dimen/meeting_memberlist_neterror_size"
                android:textStyle="bold"
                android:lines="1"/>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/itemMemberMoreIconLL"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/itemMemberMoreIcon"
                style="@style/iv_item_member_icon"
                android:src="@mipmap/starry_member_list_more"
                android:visibility="visible"
                tools:ignore="MissingConstraints"
                />
        </LinearLayout>

    </LinearLayout>
</LinearLayout>