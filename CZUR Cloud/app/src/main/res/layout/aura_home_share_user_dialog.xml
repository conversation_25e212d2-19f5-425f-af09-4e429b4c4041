<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="250dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:background="@drawable/btn_rec_8_bg_with_white"
        android:orientation="vertical">


        <RelativeLayout

            android:layout_width="match_parent"
            android:layout_height="70dp">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_marginTop="20dp"
                android:layout_alignParentTop="true"
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/img"
                android:layout_marginLeft="20dp"
                android:text="@string/choose_member"
                android:textColor="@color/black_22"
                android:textSize="20sp" />

            <ImageView
                android:layout_centerHorizontal="true"
                android:id="@+id/img"
                android:layout_alignParentRight="true"
                android:layout_width="43dp"
                android:layout_height="43dp"
                android:padding="15dp"
                android:src="@mipmap/aura_home_close_icon" />


        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/aura_home_recyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:layout_marginBottom="30dp"
            android:overScrollMode="never">
        </androidx.recyclerview.widget.RecyclerView>
        
        <TextView
            android:id="@+id/confirm_btn"
            android:layout_marginBottom="20dp"
            android:layout_gravity="center_horizontal"
            android:layout_width="180dp"
            android:gravity="center"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:textSize="15sp"
            android:text="@string/confirm1"
            android:background="@drawable/btn_rec_5_bg_with_light_blue_aura_home"
            android:layout_height="40dp" />

    </LinearLayout>
</RelativeLayout>