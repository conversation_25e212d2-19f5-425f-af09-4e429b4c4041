<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/gary_f9">

    <LinearLayout xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <include layout="@layout/layout_user_top_bar" />

        <RelativeLayout
            android:id="@+id/book_menu_add_book_rl"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/gary_ff">


            <ImageView
                android:id="@+id/book_menu_add_book_img"
                android:layout_width="16.5dp"
                android:layout_height="16.5dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="16dp"
                android:background="@mipmap/add_book" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="17.5dp"
                android:layout_toRightOf="@+id/book_menu_add_book_img"
                android:text="@string/add_new_book"
                android:textColor="@color/blue_29b0d7"
                android:textSize="15sp" />


            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:layout_marginLeft="17dp"
                android:background="@color/gray_e5" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/book_menu_my_pdf_rl"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/gary_ff">


            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="17.5dp"
                android:text="@string/my_pdf"
                android:textColor="@color/normal_blue"
                android:textSize="15sp" />


            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="17dp"
                android:background="@mipmap/user_right_gray_arrow"
                android:padding="10dp" />


        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentTop="true"
            android:layout_marginLeft="17dp"
            android:background="@color/gray_e5" />

        <RelativeLayout
            android:id="@+id/book_menu_handwriting_rl"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/gary_ff">


            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/book_menu_handwriting_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="17.5dp"
                android:text="@string/add_handwriting"
                android:textColor="@color/normal_blue"
                android:textSize="15sp" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/book_menu_handwriting_new_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="10.5dp"
                android:layout_toRightOf="@+id/book_menu_handwriting_tv"
                android:text="@string/NEW"
                android:textColor="@color/red_e75252"
                android:textSize="15sp" />


            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/book_menu_handwriting_count_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginRight="10.5dp"
                android:layout_toLeftOf="@+id/book_menu_handwriting_right_arrow"
                android:textColor="@color/normal_blue"
                android:textSize="15sp" />

            <ImageView
                android:id="@+id/book_menu_handwriting_right_arrow"
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="17dp"
                android:background="@mipmap/user_right_gray_arrow"
                android:padding="10dp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:layout_marginLeft="17dp"
                android:background="@color/gray_e5" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/book_menu_language_rl"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/gary_ff">


            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/book_menu_language_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="17.5dp"
                android:text="@string/select_language"
                android:textColor="@color/normal_blue"
                android:textSize="15sp" />


            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/book_menu_language_name_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginRight="10.5dp"
                android:layout_toLeftOf="@+id/book_menu_language_right_arrow"
                android:textColor="@color/normal_blue"
                android:textSize="15sp" />

            <ImageView
                android:id="@+id/book_menu_language_right_arrow"
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="17dp"
                android:background="@mipmap/user_right_gray_arrow"
                android:padding="10dp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:background="@color/gray_e5" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="17dp"
            android:background="@color/gary_f9" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentTop="true"
            android:background="@color/gray_e5" />

        <RelativeLayout
            android:id="@+id/book_menu_auto_sync_rl"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/gary_ff">


            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="17.5dp"
                android:text="@string/auto_sync"
                android:textColor="@color/normal_blue"
                android:textSize="15sp" />

            <com.suke.widget.SwitchButton
                android:id="@+id/book_menu_auto_sync_switch_btn"
                android:layout_width="50dp"
                android:layout_height="30dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="17.5dp"
                app:sb_checked="false"
                app:sb_checked_color="@color/blue_29b0d7"
                app:sb_enable_effect="false"
                app:sb_shadow_effect="true"
                app:sb_show_indicator="false" />


        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="17dp"
            android:background="@color/gray_e5" />

        <RelativeLayout
            android:id="@+id/sync_now_rl"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/gary_ff">


            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/sync_now_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="17.5dp"
                android:text="@string/sync_now"
                android:textColor="@color/blue_29b0d7"
                android:textSize="15sp" />

            <ImageView
                android:id="@+id/sync_now_img"
                android:layout_width="13dp"
                android:layout_height="13dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="7.5dp"
                android:layout_toRightOf="@+id/sync_now_tv"
                android:src="@mipmap/loading_sync"
                android:visibility="gone" />

            <ImageView
                android:id="@+id/sync_now_right"
                android:layout_width="13dp"
                android:layout_height="13dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="7.5dp"
                android:layout_toRightOf="@+id/sync_now_tv"
                android:visibility="gone" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:background="@color/gray_e5" />
        </RelativeLayout>


        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/book_menu_last_sync_time"
            android:layout_width="match_parent"
            android:layout_height="40.5dp"
            android:background="@color/gary_f9"
            android:paddingLeft="18.5dp"
            android:paddingTop="10dp"
            android:text="@string/last_sync_time"
            android:textColor="@color/gary_c4"
            android:textSize="9sp" />

        <RelativeLayout
            android:id="@+id/book_menu_advice_rl"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/gary_ff">

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/gray_e5" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="17.5dp"
                android:text="@string/advice"
                android:textColor="@color/normal_blue"
                android:textSize="15sp" />


            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="17dp"
                android:background="@mipmap/user_right_gray_arrow"
                android:padding="10dp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:layout_marginLeft="17dp"
                android:background="@color/gray_e5" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/book_menu_question_rl"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/gary_ff">


            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="17.5dp"
                android:text="@string/question"
                android:textColor="@color/normal_blue"
                android:textSize="15sp" />


            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="17dp"
                android:background="@mipmap/user_right_gray_arrow"
                android:padding="10dp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:background="@color/gray_e5" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/book_menu_delete_rl"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_marginTop="20dp"
            android:background="@color/gary_ff">

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentTop="true"
                android:background="@color/gray_e5" />


            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="17.5dp"
                android:text="@string/delete_from_app"
                android:textColor="@color/red_e75252"
                android:textSize="15sp" />


            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:background="@color/gray_e5" />
        </RelativeLayout>


    </LinearLayout>


</RelativeLayout>

