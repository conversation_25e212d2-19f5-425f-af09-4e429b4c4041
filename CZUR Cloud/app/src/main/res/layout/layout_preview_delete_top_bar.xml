<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:background="@color/black_2a"
    android:layout_height="44dp">

    <ImageView
        android:id="@+id/preview_camera_back_btn"
        android:layout_width="29.5dp"
        android:layout_height="36dp"
        android:layout_centerVertical="true"
        android:layout_marginLeft="9dp"
        android:padding="10dp"
        android:src="@mipmap/white_back_icon" />


    <TextView
        android:id="@+id/preview_camera_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_centerInParent="true"
        android:textColor="@color/white"/>


    <RelativeLayout
        android:id="@+id/preview_camera_delete"
        android:layout_width="42.5dp"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true"
        android:layout_marginRight="10dp">

        <ImageView

            android:layout_width="22.5dp"
            android:layout_height="22.5dp"
            android:layout_centerInParent="true"
            android:background="@mipmap/book_delete"
            android:padding="10dp" />

    </RelativeLayout>

</RelativeLayout>


