<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/sitting_dialog_style"
    android:layout_margin="@dimen/jingMargin30"
    android:orientation="vertical">

    <TextView
        android:id="@+id/dialog_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_marginTop="@dimen/jingMargin20"
        android:text="@string/sitting_sensitivity_dialog_title"
        android:textStyle="bold"
        android:textColor="@color/black_2a"
        android:textSize="20sp"
        android:visibility="visible"/>

    <TextView
        android:id="@+id/dialog_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/jingMargin30"
        android:paddingBottom="@dimen/jingMargin30"
        android:paddingStart="@dimen/jingMargin20"
        android:paddingEnd="@dimen/jingMargin20"
        android:text="@string/sitting_sensitivity_note"
        android:textColor="@color/gray_5e"
        android:textSize="18sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#CFCFCF" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:padding="@dimen/jingMargin10"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/cancel"
            android:layout_width="0dp"
            android:layout_height="@dimen/reportLineHeight"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/sitting_sensitivity_dialog_no"
            android:textStyle="bold"
            android:textColor="@color/jing_main_bg_color"
            android:textSize="20sp"
            android:visibility="gone"/>

        <View
            android:id="@+id/sep"
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="#CFCFCF"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/confirm"
            android:layout_width="0dp"
            android:layout_height="@dimen/reportLineHeight"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/sitting_sensitivity_dialog_yes"
            android:textColor="@color/jing_main_bg_color"
            android:textSize="20sp" />
    </LinearLayout>

</LinearLayout>
