<?xml version="1.0" encoding="utf-8"?>
<com.lihang.ShadowLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:hl_cornerRadius="0dp"
    app:hl_shadowColor="@color/blackTransparent95"
    app:hl_layoutBackground="@color/white"
    app:hl_shadowLimit="0dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.facebook.drawee.view.SimpleDraweeView
            android:id="@+id/et_files_img"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/jingMargin2"
            app:actualImageScaleType="centerCrop" />

        <RelativeLayout
            android:layout_width="@dimen/jingMargin50"
            android:layout_height="@dimen/jingMargin50"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true">

            <CheckBox
                android:id="@+id/check"
                style="@style/MirrorCheckBox"
                android:layout_width="@dimen/jingMargin20"
                android:layout_height="@dimen/jingMargin20"
                android:layout_centerInParent="true"
                android:minHeight="@dimen/jingMargin20" />

        </RelativeLayout>
    </RelativeLayout>

</com.lihang.ShadowLayout>

