<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="55dp"
    android:background="@color/white">

    <ImageView
        android:id="@+id/missed_video_point"
        android:layout_width="5dp"
        android:layout_height="5dp"
        android:layout_centerVertical="true"
        android:layout_marginLeft="15dp"
        android:src="@drawable/circle_red" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="5dp"
        android:layout_toRightOf="@+id/missed_video_point"
        android:orientation="vertical">

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/missed_video_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@+id/et_user_head_img"
            android:text=""
            android:textColor="@color/black_22"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/missed_video_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="1dp"
            android:layout_marginTop="2dp"
            android:layout_toRightOf="@+id/et_user_head_img"
            android:textColor="@color/black_22"
            android:textSize="12sp" />

    </LinearLayout>


    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/call_now_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginLeft="9dp"
        android:layout_marginRight="5dp"
        android:padding="10dp"
        android:text="@string/phone_now"
        android:textColor="@color/blue_29b0d7"
        android:textSize="12sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_alignParentBottom="true"
        android:background="@color/gary_ba" />


</RelativeLayout>