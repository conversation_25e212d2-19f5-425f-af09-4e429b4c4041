<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_22"
    android:fitsSystemWindows="true"
    android:keepScreenOn="true">

    <FrameLayout
        android:id="@+id/remote_video_view_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/black_22"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="h,3:4"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/local_video_view_container"
        android:layout_width="150dp"
        android:layout_height="0dp"
        android:layout_marginEnd="5dp"
        android:layout_marginBottom="5dp"
        app:layout_constraintBottom_toBottomOf="@+id/remote_video_view_container"
        app:layout_constraintDimensionRatio="h,4:3"
        app:layout_constraintRight_toRightOf="@+id/remote_video_view_container" />

    <View
        android:id="@+id/tmp_view"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginTop="20dp"
        android:visibility="visible"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/remote_video_back_btn"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginTop="20dp"
        android:src="@mipmap/video_back_icon"
        android:visibility="invisible"
        app:layout_constraintLeft_toRightOf="@+id/tmp_view"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/dialog_out_btn"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:src="@mipmap/dialog_out"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toRightOf="@id/remote_video_view_container"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/save_bg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/black"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/save_close_img"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_marginTop="25dp"
        android:layout_marginEnd="15dp"
        android:background="@color/black"
        android:padding="15dp"
        android:src="@mipmap/hd_close_icon"
        android:visibility="invisible"
        app:layout_constraintLeft_toRightOf="@+id/save_show_img"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/save_img"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_marginEnd="15dp"
        android:background="@color/black"
        android:padding="5dp"
        android:src="@mipmap/hdview_btn_fav"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@id/save_tv"
        app:layout_constraintLeft_toRightOf="@+id/save_show_img"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/save_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="25dp"
        android:text="@string/hdview_fav_tv"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@id/save_img_ablum"
        app:layout_constraintLeft_toLeftOf="@id/save_img_ablum"
        app:layout_constraintRight_toRightOf="@id/save_img" />

    <ImageView
        android:id="@+id/save_img_ablum"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_marginEnd="15dp"
        android:background="@color/black"
        android:padding="5dp"
        android:src="@mipmap/hdview_btn_ablum"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/save_tv_ablum"
        app:layout_constraintBottom_toTopOf="@id/save_tv_ablum"
        app:layout_constraintLeft_toRightOf="@+id/save_show_img"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/save_tv_ablum"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="25dp"
        android:text="@string/hdview_ablum_tv"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/save_img_ablum"
        app:layout_constraintRight_toRightOf="@id/save_img_ablum" />


    <LinearLayout
        android:id="@+id/change_camera_ll"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@+id/dialog_out_btn"
        app:layout_constraintLeft_toRightOf="@+id/remote_video_view_container"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/change_camera_btn"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_gravity="center"
            android:background="@mipmap/scan_camera"
            app:layout_constraintBottom_toBottomOf="@+id/remote_video_back_btn"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/remote_video_back_btn" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/change_camera_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="6dp"
            android:text="@string/scan_camera"
            android:textColor="@color/white"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="@+id/change_camera_bg"
            app:layout_constraintHorizontal_bias="0.3"
            app:layout_constraintLeft_toRightOf="@+id/change_camera_icon"
            app:layout_constraintRight_toRightOf="@+id/change_camera_bg"
            app:layout_constraintTop_toTopOf="@+id/change_camera_bg" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/hd_imageview_ll"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toRightOf="@id/remote_video_view_container"
        app:layout_constraintRight_toLeftOf="@id/hd_video_ll"
        app:layout_constraintTop_toBottomOf="@id/dialog_out_btn">

        <ImageView
            android:id="@+id/hd_imageview_btn"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_gravity="center"
            android:background="@mipmap/ic_hd_image" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/hd_imageview_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="6dp"
            android:text="@string/hd_view_tv"
            android:textColor="@color/white"
            android:textSize="12sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/hd_video_ll"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginStart="50dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/hd_imageview_ll"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dialog_out_btn">

        <ImageView
            android:id="@+id/hd_video_btn"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_gravity="center"
            android:src="@mipmap/ic_hd_video" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/hd_video_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="6dp"
            android:text="@string/hd_video_tv"
            android:textColor="@color/white"
            android:textSize="12sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/reportTextMargin15"
        android:layout_marginBottom="@dimen/reportTextMargin"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/remote_video_view_container">

        <ImageView
            android:id="@+id/btn_video_record"
            android:layout_width="@dimen/videoMoreBtnHeight"
            android:layout_height="@dimen/videoMoreBtnHeight"
            android:layout_marginBottom="@dimen/videoMoreBtnSpace"
            android:src="@mipmap/ic_aura_record" />

        <ImageView
            android:id="@+id/btn_video_zan"
            android:layout_width="@dimen/videoMoreBtnHeight"
            android:layout_height="@dimen/videoMoreBtnHeight"
            android:layout_marginBottom="@dimen/videoMoreBtnSpace"
            android:src="@mipmap/btn_video_mic"
            android:visibility="gone" />

        <RelativeLayout
            android:layout_width="@dimen/videoMoreBtnHeight"
            android:layout_height="@dimen/videoMoreBtnHeight"
            android:layout_marginBottom="@dimen/videoMoreBtnSpace">

            <ImageView
                android:id="@+id/btn_video_album"
                android:layout_width="@dimen/videoMoreBtnHeight"
                android:layout_height="@dimen/videoMoreBtnHeight"
                android:src="@mipmap/btn_video_album"
                android:visibility="visible" />

            <TextView
                android:id="@+id/btn_video_album_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_alignParentEnd="true"
                android:background="@drawable/oval_read"
                android:gravity="center"
                android:minWidth="15dp"
                android:minHeight="15dp"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:visibility="gone" />

        </RelativeLayout>

        <ImageView
            android:id="@+id/btn_video_mic"
            android:layout_width="@dimen/videoMoreBtnHeight"
            android:layout_height="@dimen/videoMoreBtnHeight"
            android:layout_marginBottom="@dimen/videoMoreBtnSpace"
            android:src="@mipmap/btn_video_mic_gray" />

        <ImageView
            android:id="@+id/btn_video_sub"
            android:layout_width="@dimen/videoMoreBtnHeight"
            android:layout_height="@dimen/videoMoreBtnHeight"
            android:src="@mipmap/btn_video_sub_gray" />

    </LinearLayout>

    <RelativeLayout
        android:id="@+id/hd_view_loading_bg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/blackTransparent70"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="w,4:3"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <RelativeLayout
        android:id="@+id/hd_view_loading_rl"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/blackTransparent50"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="w,4:3"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/hd_view_loading_img"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:src="@mipmap/video_loading" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/hd_view_loading_tv"
                android:layout_width="120dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="25dp"
                android:gravity="center_horizontal"
                android:text="@string/getting"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </LinearLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/video_loading_rl"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/black_22"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="w,4:3"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/loading_img"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:src="@mipmap/video_loading" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/loading_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="25dp"
                android:text="@string/calling"
                android:textColor="@color/white"
                android:textSize="14sp" />

        </LinearLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/save_loading_rl"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/blackTransparent50"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/save_loading_img"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:src="@mipmap/video_loading" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="25dp"
                android:text="@string/saving"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </LinearLayout>
    </RelativeLayout>

    <com.davemorrissey.labs.subscaleview.SubsamplingScaleImageView
        android:id="@+id/save_show_img"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:background="@color/black_22"
        android:visibility="invisible"
        app:layout_constraintDimensionRatio="h,3:4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent" />

    <RelativeLayout
        android:id="@+id/failed_toast"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        android:layout_marginTop="15dp"
        android:background="@drawable/btn_rec_5_bg_with_white"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="26dp"
            android:layout_centerInParent="true"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_net_toast"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/network_bad"
                android:textColor="@color/red_de4d4d"
                android:textSize="16sp" />
        </LinearLayout>
    </RelativeLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginBottom="20dp"
        android:orientation="vertical"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent">

        <TextView
            android:id="@+id/tv_me_tx_quality"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="本机上行："
            android:textColor="@color/white" />

        <TextView
            android:id="@+id/tv_me_rx_quality"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="本机下行："
            android:textColor="@color/white" />

        <TextView
            android:id="@+id/tv_other_tx_quality"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="对方上行："
            android:textColor="@color/white" />

        <TextView
            android:id="@+id/tv_other_rx_quality"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="对方下行："
            android:textColor="@color/white" />
    </LinearLayout>

    <TextView
        android:id="@+id/countdown_time_tv"
        android:layout_width="60dp"
        android:layout_height="20dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        android:background="@drawable/shap_circle_gray_f2"
        android:gravity="center"
        android:text="09:21"
        android:textColor="@color/white"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/remote_video_view_container"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/last_count_down_time_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:text="10"
        android:textColor="@color/orange_fbb779"
        android:textSize="35sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="gone"/>
    <!--    4个保持最右侧不被缩-->
    <View
        android:layout_width="1dp"
        android:layout_height="1dp"
        app:layout_constraintStart_toEndOf="@+id/remote_video_view_container"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="1dp"
        android:layout_height="1dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="1dp"
        android:layout_height="1dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/remote_video_view_container" />

    <View
        android:layout_width="1dp"
        android:layout_height="1dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>
