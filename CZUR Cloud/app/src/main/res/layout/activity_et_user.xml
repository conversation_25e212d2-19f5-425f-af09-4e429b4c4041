<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gary_f9"
    android:fitsSystemWindows="true"
    android:orientation="vertical">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/gary_ff">

        <ImageView
            android:id="@+id/et_user_back_btn"
            android:layout_width="29.5dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="9dp"
            android:padding="10dp"
            android:src="@mipmap/login_back_icon" />


        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/et_user_title"
            android:layout_width="wrap_content"
            android:singleLine="true"
            android:maxWidth="260dp"
            android:marqueeRepeatLimit="marquee_forever"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:ellipsize="marquee"
            android:textColor="@color/account_title"
            android:textSize="18sp" />

    </RelativeLayout>
<androidx.core.widget.NestedScrollView
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/et_share_user_recyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:overScrollMode="never" />


        <View
           android:id="@+id/line_1"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/gray_e5" />

        <RelativeLayout
            android:id="@+id/et_change_name_rl"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/gary_ff">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="17.5dp"
                android:text="@string/et_change_equipment_name"
                android:textColor="@color/normal_blue"
                android:textSize="15sp" />

            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="17dp"
                android:background="@mipmap/user_right_gray_arrow"
                android:padding="10dp" />

        </RelativeLayout>

        <View
            android:id="@+id/line_4"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/gray_e5" />
        <RelativeLayout
            android:id="@+id/et_connect_wifi_rl"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/gary_ff">


            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="17.5dp"
                android:text="@string/et_connect_wifi"
                android:textColor="@color/normal_blue"
                android:textSize="15sp" />


            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="17dp"
                android:background="@mipmap/user_right_gray_arrow"
                android:padding="10dp" />

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/gray_e5" />
        <RelativeLayout
            android:id="@+id/et_transfer_equipment_rl"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/gary_ff">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="17.5dp"
                android:text="@string/et_transfer_equipment"
                android:textColor="@color/normal_blue"
                android:textSize="15sp" />


            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="17dp"
                android:background="@mipmap/user_right_gray_arrow"
                android:padding="10dp" />


        </RelativeLayout>

        <View
            android:id="@+id/line_2"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/gray_e5" />


        <RelativeLayout
            android:id="@+id/et_user_share_rl"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/gary_ff">


            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="17.5dp"
                android:text="@string/et_share_equipment"
                android:textColor="@color/normal_blue"
                android:textSize="15sp" />

            <com.suke.widget.SwitchButton
                android:id="@+id/et_user_share_switch_btn"
                android:layout_width="50dp"
                android:layout_height="30dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="17.5dp"
                app:sb_checked_color="@color/blue_29b0d7"
                app:sb_shadow_effect="true"
                app:sb_checked="false"
                app:sb_enable_effect="false"
                app:sb_show_indicator="false" />


        </RelativeLayout>

        <View
            android:id="@+id/line_3"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/gray_e5" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/user_manage_warn_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="18.5dp"
            android:layout_marginTop="10.5dp"
            android:text="@string/et_user_warning"
            android:textColor="@color/gary_c4"
            android:textSize="9sp" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:layout_marginBottom="80dp"
            android:id="@+id/remove_et_btn"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginLeft="75dp"
            android:layout_marginRight="75dp"
            android:layout_marginTop="23dp"
            android:background="@drawable/btn_rec_5_bg_with_red"
            android:clickable="false"
            android:gravity="center"
            android:text="@string/delete_from_et"
            android:textColor="@color/white"
            android:textSize="15sp" />

    </LinearLayout>
</androidx.core.widget.NestedScrollView>



</LinearLayout>

