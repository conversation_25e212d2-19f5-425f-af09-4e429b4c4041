<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/user_top_bar"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/gary_ff">
        <ImageView
            android:id="@+id/top_bar_back_btn"
            android:layout_width="29.5dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="10dp"
            android:padding="10dp"
            android:src="@mipmap/login_back_icon" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/top_bar_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="@color/account_title"
            android:textSize="18sp"
            android:text=""/>

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/top_bar_finish"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginEnd="15dp"
            android:layout_alignParentEnd="true"
            android:textColor="@color/jing_main_bg_color"
            android:textSize="18sp"
            android:text="@string/top_bar_btn_finish"
            android:visibility="gone"/>

    </RelativeLayout>
</LinearLayout>
