<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_22"
    android:fitsSystemWindows="true"
    android:keepScreenOn="true">

    <RelativeLayout
        android:id="@+id/rl_video"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/record_top_bar"
        android:visibility="gone">

        <FrameLayout
            android:id="@+id/remote_video_view_container"
            android:layout_width="match_parent"
            android:layout_height="300dp"
            android:layout_marginTop="50dp"
            android:layout_marginBottom="50dp" />

        <com.facebook.drawee.view.SimpleDraweeView
            android:id="@+id/record_img"
            android:layout_width="match_parent"
            android:layout_height="300dp"
            android:layout_marginTop="50dp"
            android:visibility="gone"
            android:layout_marginBottom="50dp" />
    </RelativeLayout>


    <RelativeLayout
        android:id="@+id/record_top_bar"
        android:layout_width="match_parent"
        android:layout_height="45dp">

        <ImageView
            android:id="@+id/position_record_back_btn"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="20dp"
            android:src="@mipmap/video_back_icon"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/normal_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/aura_home_sitting_record"
            android:textColor="@color/white"
            android:textSize="18sp" />

    </RelativeLayout>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/rl_video"
        android:layout_alignParentBottom="true">

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/record_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="10dp"
            android:layout_marginStart="60dp"
            android:layout_marginEnd="60dp"
            android:layout_marginBottom="5dp"
            android:gravity="center"
            android:text="@string/sit_correct"
            android:textColor="@android:color/white" />

        <LinearLayout
            android:id="@+id/record_again_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/record_tv"
            android:layout_centerHorizontal="true"
            android:orientation="vertical"
            android:padding="5dp"
            android:visibility="gone">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:text="@string/record_again"
                android:textColor="@color/blue_29b0d7" />

            <View
                android:layout_width="65dp"
                android:layout_height="2dp"
                android:background="@color/blue_29b0d7" />
        </LinearLayout>

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/record_btn"
            android:layout_width="wrap_content"
            android:minWidth="250dp"
            android:layout_height="40dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="20dp"
            android:background="@drawable/btn_rec_5_bg_with_blue_aura_home"
            android:gravity="center"
            android:text="@string/confirm_record"
            android:textColor="@color/white"
            android:textSize="15sp" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/record_again_btn1"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:minWidth="250dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="20dp"
            android:background="@drawable/btn_rec_5_bg_with_blue_aura_home"
            android:gravity="center"
            android:text="@string/record_again_tv"
            android:textColor="@color/white"
            android:textSize="15sp"
            android:visibility="gone" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/finish_btn"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:minWidth="250dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="20dp"
            android:background="@drawable/btn_rec_5_bg_with_blue_aura_home"
            android:gravity="center"
            android:text="@string/record_finish"
            android:textColor="@color/white"
            android:textSize="15sp"
            android:visibility="gone" />


        <RelativeLayout
            android:id="@+id/video_loading_rl"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/black_22"
            android:visibility="visible">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/loading_img"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_gravity="center_horizontal"
                    android:src="@mipmap/video_loading" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/tv_loading"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="25dp"
                    android:textColor="@color/white"
                    android:textSize="14sp" />

            </LinearLayout>


        </RelativeLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/failed_toast"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        android:paddingEnd="10dp"
        android:paddingStart="10dp"
        android:minWidth="130dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="220dp"
        android:background="@drawable/btn_rec_5_bg_with_white"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_centerInParent="true">

            <ImageView
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="9dp"
                android:src="@mipmap/toast_failed" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/record_failed"
                android:textColor="@color/red_de4d4d"
                android:textSize="16sp" />
        </LinearLayout>

    </RelativeLayout>


    <TextView
        android:id="@+id/tv_net_toast"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="140dp"
        android:background="@drawable/btn_rec_5_bg_with_white"
        android:gravity="center"
        android:textColor="@color/red_de4d4d"
        android:textSize="16sp"
        android:visibility="gone" />

    <RelativeLayout
        android:id="@+id/success_toast"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        android:minWidth="130dp"
        android:paddingEnd="10dp"
        android:paddingStart="10dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="220dp"
        android:background="@drawable/btn_rec_5_bg_with_white"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="15dp"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="9dp"
                android:src="@mipmap/toast_success" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/record_success"
                android:textColor="@color/blue_29b0d7"
                android:textSize="16sp" />
        </LinearLayout>

    </RelativeLayout>

</RelativeLayout>
