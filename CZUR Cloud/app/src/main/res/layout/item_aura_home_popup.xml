<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/aura_home_popup_rl"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:background="@color/gary_ff">


    <ImageView
        android:id="@+id/aura_home_popup_right"
        android:layout_width="17dp"
        android:layout_height="12dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="20dp"
        android:background="@mipmap/right_aura_home_icon"
        android:padding="10dp" />
    <View
        android:id="@+id/new_file_tip"
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:layout_centerVertical="true"
        android:layout_marginLeft="9dp"
        android:visibility="invisible"
        android:background="@drawable/circle_red" />

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/aura_home_popup_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="7dp"
        android:layout_marginRight="100dp"
        android:ellipsize="end"
        android:singleLine="true"
        android:layout_toRightOf="@+id/new_file_tip"
        android:textColor="@color/normal_blue"
        android:textSize="15sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_alignParentBottom="true"
        android:background="@color/gray_e5" />
</RelativeLayout>



