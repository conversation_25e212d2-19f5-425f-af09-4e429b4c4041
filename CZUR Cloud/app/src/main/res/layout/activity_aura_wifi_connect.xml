<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gary_f9"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/aura_home_gif1"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="15dp"
        android:layout_marginRight="20dp"
        android:background="@mipmap/check_aura_mate_1"
        app:layout_constraintDimensionRatio="h,560:365"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/wifi_retry_top_bar" />

    <ImageView
        android:id="@+id/aura_home_gif2"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="14dp"
        android:layout_marginRight="20dp"
        android:background="@mipmap/check_aura_mate_2"
        app:layout_constraintDimensionRatio="h,560:300"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/aura_home_gif1" />

    <ImageView
        android:id="@+id/aura_home_gif3"
        android:visibility="gone"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="14dp"
        android:layout_marginRight="20dp"
        android:background="@mipmap/check_aura_mate_3"
        app:layout_constraintDimensionRatio="h,28:30"
        app:layout_constraintLeft_toLeftOf="@+id/guideline"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/aura_home_gif1" />


    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/retry_text_1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/aura_home_connect_wifi_prompt"
        android:textColor="@color/black_22"
        android:textSize="15sp"
        android:textAlignment="center"
        app:layout_constraintBottom_toTopOf="@+id/next_step_btn"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/aura_home_gif2" />

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/next_step_btn"
        android:layout_width="250dp"
        android:layout_height="40dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/btn_rec_5_bg_with_code_blue"
        android:gravity="center"
        android:text="@string/aura_home_connect_wifi_confirm"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />


    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintGuide_percent="0.5"
        app:layout_constraintGuide_begin="0dp" />

    <include
        android:id="@+id/wifi_retry_top_bar"
        layout="@layout/layout_normal_top_bar" />
</androidx.constraintlayout.widget.ConstraintLayout>

