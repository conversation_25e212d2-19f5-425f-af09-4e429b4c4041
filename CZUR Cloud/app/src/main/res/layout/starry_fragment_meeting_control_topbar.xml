<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/control_layout"
    android:layout_width="match_parent"
    android:layout_height="@dimen/starry_meeting_main_topbar_height"
    android:visibility="visible"
    app:layout_constraintTop_toTopOf="parent">

    <View
        android:id="@+id/controlTopBarBg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/starry_meeting_main_topbar_bg_height"
        android:background="@color/meeting_main_navbar_bg_black"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />



    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/controlActionTopBar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/starry_meeting_main_topbar_height"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline5"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_begin="45dp" />

        <ImageView
            android:id="@+id/changeCam"
            style="@style/meeting_main_navbar_image_style"
            android:layout_marginStart="@dimen/starryMargin24"
            android:src="@mipmap/starry_meeting_ic_camera"
            app:layout_constraintBottom_toBottomOf="@+id/guideline5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/meetingTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5"
            android:ellipsize="end"
            android:gravity="center"
            android:includeFontPadding="false"
            android:maxEms="10"
            android:maxLines="1"
            android:text="@string/starry_main_meeting_title"
            android:textColor="#fff"
            android:textSize="@dimen/starryFontSize16"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <RelativeLayout
            android:id="@+id/meet_code_copy_rr"
            android:layout_width="@dimen/starryMeetingCodeWidth"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/meetingTitle"
            android:visibility="visible"
            android:layout_marginStart="@dimen/dp_5"
            >

            <TextView
                android:id="@+id/meeting_main_meetcode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textSize="@dimen/starryFontSize10"
                tools:text="会议号: 123 456 789"
                android:layout_alignParentStart="true"/>

            <ImageView
                android:id="@+id/name_edit_btn"
                android:layout_width="10dp"
                android:layout_height="10dp"
                android:layout_gravity="center"
                android:layout_marginStart="5dp"
                android:layout_marginEnd="5dp"
                android:layout_marginTop="1dp"
                android:layout_centerVertical="true"
                android:layout_toEndOf="@+id/meeting_main_meetcode"
                android:src="@drawable/starry_meeting_copy_code"
                android:visibility="gone" />

            <TextView
                android:id="@+id/meeting_main_timelong"
                android:layout_width="45dp"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerInParent="true"
                android:gravity="start"
                android:textColor="@color/white"
                android:textSize="@dimen/starryFontSize10"
                android:layout_marginStart="@dimen/dp_5"
                tools:text="00:00:00" />

        </RelativeLayout>

        <TextView
            android:id="@+id/meetingOver"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="24dp"
            android:gravity="center"
            android:includeFontPadding="false"
            android:padding="4dp"
            android:text="@string/starry_main_exit"
            android:textColor="@color/starry_text_color_red"
            android:textSize="16sp"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@+id/guideline5"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/mainBottomLcokIV"
            android:layout_width="@dimen/starryMargin50"
            android:layout_height="@dimen/starryMargin50"
            android:layout_marginStart="@dimen/starryMargin20"
            android:layout_marginTop="@dimen/starryMargin5"
            android:src="@drawable/starry_meeting_sel_lock"
            android:alpha="1.0"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@+id/changeCam"
            app:layout_constraintStart_toStartOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>