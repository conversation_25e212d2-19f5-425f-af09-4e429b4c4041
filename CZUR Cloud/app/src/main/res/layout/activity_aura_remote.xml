<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:fitsSystemWindows="true"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/aura_top_bar"
        android:layout_width="match_parent"
        android:layout_height="45dp">

        <ImageView
            android:id="@+id/more_back_btn"
            android:layout_width="29.5dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="9dp"
            android:padding="10dp"
            android:src="@mipmap/login_back_icon" />


        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/more_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/aura_title"
            android:layout_marginLeft="60dp"
            android:layout_marginRight="60dp"
            android:textColor="@color/account_title"
            android:gravity="center"
            android:layout_centerInParent="true"
            android:textSize="18sp" />

        <RelativeLayout
            android:id="@+id/aura_more_btn"
            android:layout_width="44dp"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true">

            <ImageView
                android:layout_width="18dp"
                android:layout_height="17.5dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="18.5dp"
                android:background="@mipmap/book_more_icon" />

        </RelativeLayout>

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/aura_top_bar"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="0.4875">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="276dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_horizontal"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:orientation="horizontal">

                        <RelativeLayout
                            android:id="@+id/aura_natural_light_rl"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1">

                            <RelativeLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true">

                                <ImageView
                                    android:id="@+id/aura_natural_light_img"
                                    android:layout_width="75dp"
                                    android:layout_height="75dp"
                                    android:layout_centerHorizontal="true"
                                    android:background="@drawable/selector_aura_natural_light" />


                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_below="@+id/aura_natural_light_img"
                                    android:layout_centerHorizontal="true"
                                    android:layout_marginTop="9dp"
                                    android:text="@string/natural_light"
                                    android:gravity="center"
                                    android:textColor="@color/gary_c4"
                                    android:textStyle="bold" />
                            </RelativeLayout>


                        </RelativeLayout>

                        <RelativeLayout
                            android:id="@+id/aura_read_book_rl"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1">

                            <RelativeLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true">

                                <ImageView
                                    android:id="@+id/aura_read_book_img"
                                    android:layout_width="75dp"
                                    android:layout_height="75dp"
                                    android:layout_centerHorizontal="true"
                                    android:background="@drawable/selector_aura_read_book" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_below="@+id/aura_read_book_img"
                                    android:layout_centerHorizontal="true"
                                    android:layout_marginTop="9dp"
                                    android:gravity="center"
                                    android:text="@string/read_book"
                                    android:textColor="@color/gary_c4"
                                    android:textStyle="bold" />
                            </RelativeLayout>


                        </RelativeLayout>


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:orientation="horizontal">

                        <RelativeLayout
                            android:id="@+id/aura_watch_computer_rl"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1">


                            <RelativeLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true">

                                <ImageView
                                    android:id="@+id/aura_watch_computer_img"
                                    android:layout_width="75dp"
                                    android:layout_height="75dp"
                                    android:layout_centerHorizontal="true"
                                    android:background="@drawable/selector_aura_watch_computer" />


                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_below="@+id/aura_watch_computer_img"
                                    android:layout_centerHorizontal="true"
                                    android:layout_marginTop="9dp"
                                    android:text="@string/watch_computer"
                                    android:gravity="center"
                                    android:textColor="@color/gary_c4"
                                    android:textStyle="bold" />
                            </RelativeLayout>


                        </RelativeLayout>

                        <RelativeLayout
                            android:id="@+id/aura_night_mode_rl"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1">

                            <RelativeLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true">

                                <ImageView
                                    android:id="@+id/aura_night_mode_img"
                                    android:layout_width="75dp"
                                    android:layout_height="75dp"
                                    android:layout_centerHorizontal="true"
                                    android:background="@drawable/selector_aura_night_mode" />


                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_below="@+id/aura_night_mode_img"
                                    android:layout_centerInParent="true"
                                    android:layout_marginTop="9dp"
                                    android:gravity="center"
                                    android:text="@string/night_mode"
                                    android:textColor="@color/gary_c4"
                                    android:textStyle="bold" />

                            </RelativeLayout>


                        </RelativeLayout>


                    </LinearLayout>


                </LinearLayout>


            </LinearLayout>


        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="0.1254">

            <ImageView
                android:id="@+id/aura_info_btn"
                android:layout_width="50.5dp"
                android:layout_height="50.5dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="20dp"
                android:padding="10dp"
                android:src="@mipmap/aura_info_icon" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:layout_marginLeft="22.5dp"
                android:layout_marginRight="22.5dp"
                android:background="@color/gray_e5" />

        </RelativeLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginBottom="27.5dp"
            android:layout_weight="0.3274"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1">

                <RelativeLayout
                    android:layout_width="213.5dp"
                    android:layout_height="75dp"
                    android:layout_centerInParent="true">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_centerInParent="true"
                        android:background="@mipmap/aura_brightness" />

                    <ImageView

                        android:layout_width="15dp"
                        android:layout_height="19.55dp"
                        android:layout_centerInParent="true"
                        android:background="@mipmap/aura_brightness_light_icon" />

                    <RelativeLayout
                        android:id="@+id/aura_light_minus_btn"
                        android:layout_width="106.75dp"
                        android:layout_height="match_parent">

                        <ImageView
                            android:layout_width="14dp"
                            android:layout_height="1.5dp"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="33dp"
                            android:background="@mipmap/aura_brightness_minus" />

                        <ImageView
                            android:id="@+id/aura_light_minus_img_bg"
                            android:layout_width="75dp"
                            android:layout_height="match_parent"
                            android:layout_alignParentLeft="true"
                            android:layout_centerVertical="true"
                            android:background="@mipmap/aura_brightness_bg"
                            android:visibility="gone" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/aura_light_plus_btn"
                        android:layout_width="106.75dp"
                        android:layout_height="match_parent"
                        android:layout_alignParentRight="true">

                        <ImageView
                            android:layout_width="14.5dp"
                            android:layout_height="14.5dp"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="33dp"
                            android:background="@mipmap/aura_brightness_plus" />

                        <ImageView
                            android:id="@+id/aura_light_plus_img_bg"
                            android:layout_width="75dp"
                            android:layout_height="match_parent"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:background="@mipmap/aura_brightness_bg"
                            android:visibility="gone" />
                    </RelativeLayout>


                </RelativeLayout>


            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1">

                <RelativeLayout
                    android:layout_width="213.5dp"
                    android:layout_height="75dp"
                    android:layout_centerInParent="true">

                    <ImageView
                        android:id="@+id/aura_on_btn"
                        android:layout_width="75dp"
                        android:layout_height="match_parent"
                        android:background="@drawable/selector_aura_on" />

                    <ImageView
                        android:id="@+id/aura_off_btn"
                        android:layout_width="75dp"
                        android:layout_height="match_parent"
                        android:layout_alignParentRight="true"
                        android:background="@drawable/selector_aura_off" />

                </RelativeLayout>

            </RelativeLayout>


        </LinearLayout>


    </LinearLayout>

    <RelativeLayout
        android:id="@+id/aura_guide_rl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/aura_top_bar"
        android:background="@color/gary_f9"
        android:clickable="true">

        <View
            android:id="@+id/guideline"
            android:layout_width="match_parent"
            android:layout_height="0.1dp"
            android:background="@color/whiteOpaque00" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/aura_guide_confirm_btn"
            android:layout_below="@+id/guideline">
            <LinearLayout
                android:layout_centerInParent="true"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <ImageView
                    android:layout_width="216dp"
                    android:layout_height="260dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="13dp"
                    android:background="@mipmap/aura_guide_bg" />


                <TextView
                    android:id="@+id/prompt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="50dp"
                    android:text="@string/confirm_open_aura"
                    android:textColor="@color/black_20"
                    android:textSize="18sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="5dp"
                    android:layout_marginLeft="16dp"
                    android:gravity="center_horizontal"
                    android:layout_marginRight="16dp"
                    android:lineSpacingExtra="5dp"
                    android:text="@string/confirm_open_aura1"
                    android:textColor="@color/black_20"
                    android:textSize="18sp" />
            </LinearLayout>



        </RelativeLayout>


        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/aura_guide_confirm_btn"
            android:layout_width="250dp"
            android:layout_height="40dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="30dp"
            android:background="@drawable/btn_rec_5_bg_with_blue"
            android:gravity="center"
            android:text="@string/handwriting_confirm_text"
            android:textColor="@color/white"
            android:textSize="15sp"
            />


    </RelativeLayout>
</RelativeLayout>






