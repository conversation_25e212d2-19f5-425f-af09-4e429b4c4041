<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:fitsSystemWindows="true"
    android:background="@color/gary_f9">

    <FrameLayout
        android:id="@+id/aura_home_frameLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/aura_home_top_bar"
        android:layout_marginBottom="49dp" />

    <RelativeLayout
        android:id="@+id/index_bottom_bar"
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:layout_alignParentBottom="true"
        android:background="@color/gary_fa">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/index_tab_ll"
                style="@style/index_radio_button"
                android:layout_width="46dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:checked="true"
                android:orientation="vertical"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginEnd="50dp"
                app:layout_constraintEnd_toStartOf="@+id/files_tab_movie_ll">

                <ImageView
                    android:id="@+id/index_tab_img"
                    android:layout_width="26dp"
                    android:layout_height="22.5dp"
                    android:src="@drawable/selector_aura_icon" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/index_tab_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="6dp"
                    android:text="@string/index_tab"
                    android:textColor="@drawable/selector_aura_home_text_color"
                    android:textSize="10sp" />

            </LinearLayout>
            <RelativeLayout
                android:id="@+id/files_tab_movie_ll"
                style="@style/index_radio_button"
                android:layout_width="46dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/index_tab_ll"
                app:layout_constraintEnd_toStartOf="@id/files_tab_ll">

                <ImageView
                    android:id="@+id/files_tab_movie_img"
                    android:layout_width="32dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:src="@drawable/selector_movie_icon" />
                <View
                    android:id="@+id/movies_tab_red_point"
                    android:layout_width="6dp"
                    android:layout_height="6dp"
                    android:layout_alignEnd="@+id/files_tab_movie_img"
                    android:background="@drawable/circle_red"
                    android:visibility="gone" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/files_tab_movie_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/files_tab_movie_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6dp"
                    android:text="@string/movie_tab"
                    android:textColor="@drawable/selector_aura_home_text_color"
                    android:textSize="10sp" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/files_tab_ll"
                style="@style/index_radio_button"
                android:layout_width="46dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                app:layout_constraintStart_toEndOf="@+id/files_tab_movie_ll"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginStart="50dp">

                <ImageView
                    android:id="@+id/files_tab_img"
                    android:layout_width="26dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:src="@drawable/selector_aura_file_icon" />


                <View
                    android:id="@+id/files_tab_red_point"
                    android:layout_width="6dp"
                    android:layout_height="6dp"
                    android:layout_alignEnd="@+id/files_tab_img"
                    android:background="@drawable/circle_red"
                    android:visibility="gone" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/files_tab_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/files_tab_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6dp"
                    android:text="@string/files_tab"
                    android:textColor="@drawable/selector_aura_home_text_color"
                    android:textSize="10sp" />

            </RelativeLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>


    </RelativeLayout>


    <RelativeLayout
        android:id="@+id/aura_home_top_bar"
        android:layout_width="match_parent"
        android:layout_height="44dp">

        <ImageView
            android:id="@+id/aura_home_back_btn"
            android:layout_width="29.5dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="9dp"
            android:padding="10dp"
            android:src="@mipmap/login_back_icon" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/tv_ip_port_config"
            android:layout_width="70dp"
            android:layout_height="match_parent"
            android:layout_toEndOf="@+id/aura_home_back_btn"
            android:gravity="center" />

        <RelativeLayout
            android:id="@+id/aura_home_more_btn"
            android:layout_width="44dp"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true">

            <ImageView
                android:layout_width="18.5dp"
                android:layout_height="17.5dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="18.5dp"
                android:background="@mipmap/book_more_icon" />

            <View
                android:id="@+id/aura_mate_top_red_point"
                android:layout_width="5dp"
                android:layout_height="5dp"
                android:layout_alignParentTop="true"
                android:layout_alignParentRight="true"
                android:layout_marginTop="8dp"
                android:layout_marginRight="14dp"
                android:background="@drawable/circle_red"
                android:visibility="gone" />

        </RelativeLayout>

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/aura_home"
            android:textColor="@color/account_title"
            android:textSize="18sp" />

    </RelativeLayout>

    <ImageView
        android:id="@+id/missed_call_guide_img"
        android:layout_width="12dp"
        android:layout_height="9dp"
        android:layout_alignParentRight="true"
        android:layout_marginTop="35dp"
        android:layout_marginRight="20dp"
        android:clickable="true"
        android:focusable="true"
        android:src="@drawable/at_vector_drawable_guide_triangle" />

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/missed_call_guide_tv"
        android:layout_width="130dp"
        android:layout_height="25dp"
        android:layout_below="@+id/missed_call_guide_img"
        android:layout_alignParentRight="true"
        android:layout_marginRight="10dp"
        android:background="@drawable/btn_rec_5_bg_with_code_blue_at"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:padding="5dp"
        android:textColor="@color/white"
        android:textSize="12sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_above="@+id/index_bottom_bar"
        android:background="@color/gary_e7" />

    <LinearLayout
        android:id="@+id/aura_home_bottom_ll"
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:layout_alignParentBottom="true"
        android:layout_gravity="bottom"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone">


        <RelativeLayout
            android:id="@+id/aura_home_rename_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/black">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/aura_home_rename_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_rename_icon" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/aura_home_rename_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/aura_home_rename_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/rename"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/save_movie_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/black">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/save_movie_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_save_icon" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/save_movie_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/save_movie_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/save_to_album"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/aura_home_pdf_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/black">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/aura_home_pdf_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_pdf_icon" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/aura_home_pdf_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/aura_home_pdf_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/PDF"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/aura_home_move_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/black">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/aura_home_move_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_move_icon" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/aura_home_move_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/aura_home_move_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/move"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/aura_home_delete_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/black">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/aura_home_delete_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_delete_icon" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/aura_home_delete_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/aura_home_delete_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/delete"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>
    </LinearLayout>

</RelativeLayout>
