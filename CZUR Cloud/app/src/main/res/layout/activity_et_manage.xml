<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/gary_f9">
    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/et_folder_bottom_ll"
        app:srlAccentColor="@color/blue_29b0d7"
        app:srlEnableLoadMore="false"
        app:srlEnableNestedScrolling="true"
        app:srlEnableRefresh="true"
        app:srlPrimaryColor="@color/blue_29b0d7">
        <com.scwang.smartrefresh.layout.header.ClassicsHeader
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@color/blue_29b0d7"
            app:layout_srlBackgroundColor="@color/blue_29b0d7"
            app:srlAccentColor="@color/white"
            app:srlDrawableMarginRight="-10dp"
            app:srlTextFailed=""
            app:srlTextFinish=""
            app:srlTextLoading=""
            app:srlTextPulling=""
            app:srlTextRefreshing=""
            app:srlTextRelease=""
            app:srlTextUpdate="" />
        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/et_folder_bottom_ll">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                app:layout_behavior="@string/appbar_scrolling_view_behavior">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:minHeight="50dp">

                    <ImageView
                        android:id="@+id/et_files_back_btn"
                        android:layout_width="29.5dp"
                        android:layout_height="36dp"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="9dp"
                        android:padding="10dp"
                        android:src="@mipmap/blue_back_icon"
                        android:visibility="gone" />

                    <LinearLayout
                        android:id="@+id/doc_pic_ll"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="44dp">

                        <LinearLayout
                            android:id="@+id/et_doc_ll"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:orientation="vertical">

                            <com.czur.cloud.ui.component.MediumBoldTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/et_doc"
                                android:textColor="@color/blue_29b0d7" />

                            <View
                                android:id="@+id/et_doc_line"
                                android:layout_width="15dp"
                                android:layout_height="2dp"
                                android:layout_gravity="center_horizontal"
                                android:layout_marginTop="7dp"
                                android:background="@color/blue_29b0d7" />

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/et_pic_ll"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="25dp"
                            android:orientation="vertical">

                            <com.czur.cloud.ui.component.MediumBoldTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/et_pic"
                                android:textColor="@color/blue_29b0d7" />

                            <View
                                android:id="@+id/et_pic_line"
                                android:layout_width="15dp"
                                android:layout_height="2dp"
                                android:layout_gravity="center_horizontal"
                                android:layout_marginTop="7dp"
                                android:background="@color/blue_29b0d7"
                                android:visibility="invisible" />

                        </LinearLayout>
                    </LinearLayout>


                    <RelativeLayout
                        android:id="@+id/et_files_unselected_top_bar_rl"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true">

                        <RelativeLayout
                            android:id="@+id/et_files_add_btn"
                            android:layout_width="44dp"
                            android:layout_height="wrap_content"
                            android:layout_toEndOf="@+id/et_files_multi_select_btn">

                            <ImageView
                                android:layout_width="23dp"
                                android:layout_height="22dp"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:layout_marginEnd="18.5dp"
                                android:background="@mipmap/et_add_icon" />

                        </RelativeLayout>

                        <RelativeLayout
                            android:id="@+id/et_files_multi_select_btn"
                            android:layout_width="52dp"
                            android:layout_height="wrap_content">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="21dp"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:layout_marginEnd="17.5dp"
                                android:background="@mipmap/et_multi_select" />
                        </RelativeLayout>
                    </RelativeLayout>

                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/et_files_no_title_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="52dp"
                        android:textColor="@color/blue_29b0d7"
                        android:textSize="18sp" />

                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/et_files_title_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:textColor="@color/black_22"
                        android:textSize="18sp"
                        android:visibility="gone" />

                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/et_files_cancel_btn"
                        android:layout_width="110dp"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:gravity="center_vertical|end"
                        android:paddingEnd="18.5dp"
                        android:textColor="@color/black_22"
                        android:textSize="16sp"
                        android:visibility="gone" />

                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/et_files_select_all_btn"
                        android:layout_width="110dp"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:gravity="center_vertical"
                        android:paddingStart="18.5dp"
                        android:textColor="@color/black_22"
                        android:textSize="16sp"
                        android:visibility="gone" />
                </RelativeLayout>


                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <com.scwang.smartrefresh.layout.SmartRefreshLayout
                        android:id="@+id/refresh_layout_rv"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        app:srlAccentColor="@color/blue_29b0d7"
                        app:srlEnableAutoLoadMore="true"
                        app:srlEnableFooterFollowWhenNoMoreData="true"
                        app:srlEnableLoadMore="true"
                        app:srlEnableLoadMoreWhenContentNotFull="false"
                        app:srlEnableNestedScrolling="true"
                        app:srlEnablePreviewInEditMode="true"
                        app:srlEnableRefresh="false"
                        app:srlPrimaryColor="@color/blue_29b0d7">

                        <com.czur.cloud.ui.component.recyclerview.CustomRecyclerView
                            android:id="@+id/et_files_recyclerView"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="8dp"
                            android:layout_marginEnd="8dp"
                            android:overScrollMode="never" />

                        <com.scwang.smartrefresh.layout.footer.ClassicsFooter
                            android:layout_width="match_parent"
                            android:layout_height="50dp"
                            android:background="@color/gary_f9"
                            app:srlTextFailed=""
                            app:srlTextFinish=""
                            app:srlTextLoading="@string/czur_footer_loading"
                            app:srlTextNothing="@string/czur_footer_nothing"
                            app:srlTextPulling="@string/czur_footer_pulling"
                            app:srlTextRefreshing=""
                            app:srlTextRelease="" />
                    </com.scwang.smartrefresh.layout.SmartRefreshLayout>

                    <RelativeLayout
                        android:id="@+id/ll_empty"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@color/gary_f9"
                        android:visibility="gone">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerHorizontal="true"
                            android:layout_marginTop="150dp"
                            android:orientation="vertical">

                            <ImageView
                                android:layout_width="120dp"
                                android:layout_height="112dp"
                                android:layout_gravity="center_horizontal"
                                android:adjustViewBounds="true"
                                android:scaleType="fitXY"
                                android:src="@mipmap/book_page_no_page_icon" />

                            <com.czur.cloud.ui.component.MediumBoldTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:layout_marginTop="15.5dp"
                                android:text="@string/none_file_book_page"
                                android:textColor="@color/gary_c4"
                                android:textSize="15sp" />
                        </LinearLayout>
                    </RelativeLayout>

                </RelativeLayout>
            </LinearLayout>

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/appbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/blue_29b0d7"
                android:fitsSystemWindows="true"
                app:elevation="0dp"
                app:layout_behavior="com.yuruiyin.appbarlayoutbehavior.AppBarLayoutBehavior">

                <com.google.android.material.appbar.CollapsingToolbarLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_scrollFlags="scroll|exitUntilCollapsed">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="44dp"
                            android:background="@color/blue_29b0d7">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="44dp">

                                <ImageView
                                    android:id="@+id/et_equipment_top_bar_back_btn"
                                    android:layout_width="29.5dp"
                                    android:layout_height="36dp"
                                    android:layout_centerVertical="true"
                                    android:layout_marginStart="9dp"
                                    android:padding="10dp"
                                    android:src="@mipmap/white_back_icon" />

                                <RelativeLayout
                                    android:id="@+id/et_equipment_top_bar_more_btn"
                                    android:layout_width="44dp"
                                    android:layout_height="match_parent"
                                    android:layout_alignParentEnd="true">

                                    <ImageView
                                        android:layout_width="16.5dp"
                                        android:layout_height="16.5dp"
                                        android:layout_alignParentEnd="true"
                                        android:layout_centerVertical="true"
                                        android:layout_marginEnd="18.5dp"
                                        android:src="@mipmap/et_more_icon" />
                                </RelativeLayout>


                                <com.czur.cloud.ui.component.MediumBoldTextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_centerInParent="true"
                                    android:text="@string/has_et_device"
                                    android:textColor="@color/white"
                                    android:textSize="18sp" />
                            </RelativeLayout>

                        </RelativeLayout>

                        <com.czur.cloud.ui.component.recyclerview.MaxHeightRecyclerView
                            android:id="@+id/et_equipment_recyclerView"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:overScrollMode="never"
                            app:maxHeight="300dp" />
                    </LinearLayout>
                </com.google.android.material.appbar.CollapsingToolbarLayout>
            </com.google.android.material.appbar.AppBarLayout>
        </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </com.scwang.smartrefresh.layout.SmartRefreshLayout>

    <LinearLayout
        android:id="@+id/et_folder_bottom_ll"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_alignParentBottom="true"
        android:background="@color/black"
        android:baselineAligned="false"
        android:clickable="true"
        android:focusable="true"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:visibility="gone">

        <RelativeLayout
            android:id="@+id/et_folder_rename_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/et_folder_rename_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="3dp"
                    android:background="@drawable/selector_rename_icon" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/et_folder_rename_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/et_folder_rename_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="3dp"
                    android:text="@string/rename"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/et_folder_pdf_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/et_folder_pdf_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="3dp"
                    android:background="@drawable/selector_pdf_icon" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/et_folder_pdf_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/et_folder_pdf_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="3dp"
                    android:text="@string/PDF"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/et_folder_share_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:visibility="gone">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/et_folder_share_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@mipmap/pdf_share" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/et_folder_share_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="3dp"
                    android:text="@string/share"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/et_folder_move_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/et_folder_move_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="3dp"
                    android:background="@drawable/selector_move_icon" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/et_folder_move_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/et_folder_move_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="3dp"
                    android:text="@string/move"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/et_folder_delete_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/et_folder_delete_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="3dp"
                    android:background="@drawable/selector_delete_icon" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/et_folder_delete_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/et_folder_delete_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="3dp"
                    android:text="@string/delete"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>
    </LinearLayout>
</RelativeLayout>