<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="400dp"
    android:elevation="10dp"
    app:bl_corners_radius="10dp"
    app:bl_solid_color="@color/white"
    tools:background="@color/white">

    <ImageView
        android:id="@+id/closeIv"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="20dp"
        android:src="@mipmap/starry_btn_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/mainLogoIv"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_marginTop="70dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/starry_icon_dialog_video" />

    <TextView
        android:id="@+id/infoTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="35dp"
        android:textColor="#5e5e5e"
        android:textSize="24sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/mainLogoIv"
        tools:text="主持人请你开启摄像头" />

    <TextView
        android:id="@+id/negativeTv"
        style="@style/tv_remote_req_float_action"
        android:text="@string/btn_tv_later"
        android:textColor="#5e5e5e"
        app:bl_corners_radius="10dp"
        app:bl_solid_color="#f2f2f2" />

    <TextView
        android:id="@+id/positiveTv"
        style="@style/tv_remote_req_float_action"
        android:textColor="@color/white"
        app:bl_corners_radius="10dp"
        app:bl_solid_color="#54b2f0"
        tools:text="开启摄像头" />

    <androidx.constraintlayout.helper.widget.Flow
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        app:constraint_referenced_ids="negativeTv,positiveTv"
        app:flow_horizontalGap="30dp"
        app:flow_horizontalStyle="packed"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>