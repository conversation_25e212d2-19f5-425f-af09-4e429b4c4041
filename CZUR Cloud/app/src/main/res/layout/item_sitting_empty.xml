<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/sitting_empty_rl"
    android:layout_width="match_parent"
    android:layout_height="530dp"
    android:layout_marginLeft="7dp"
    android:layout_marginRight="7dp"
    android:layout_marginTop="@dimen/jingNestedScrollViewMarginTop"
    android:background="@drawable/btn_rec_10_bg_with_code_green">

    <LinearLayout
        android:id="@+id/sitting_home_empty_inner_rl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:orientation="vertical">

        <ImageView
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:layout_gravity="center_horizontal"
            android:src="@mipmap/aura_home_add" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/add_sitting_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="@string/sitting_home_add_text"
            android:textColor="@color/gary_ff"
            android:textAlignment="center"
            android:textSize="18sp" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="@string/sitting_home_add_text_sub"
            android:textColor="@color/gary_f1"
            android:textAlignment="center"
            android:alpha="0.5"
            android:textSize="13sp" />

    </LinearLayout>


</RelativeLayout>

