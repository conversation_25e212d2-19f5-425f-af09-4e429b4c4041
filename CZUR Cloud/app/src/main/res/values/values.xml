<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="CropImageView">
        <attr name="cropGuidelines">
            <enum name="off" value="0" />
            <enum name="onTouch" value="1" />
            <enum name="on" value="2" />
        </attr>
        <attr name="cropScaleType">
            <enum name="fitCenter" value="0" />
            <enum name="center" value="1" />
            <enum name="centerCrop" value="2" />
            <enum name="centerInside" value="3" />
        </attr>
        <attr name="cropShape">
            <enum name="rectangle" value="0" />
            <enum name="oval" value="1" />
        </attr>
        <attr name="cropAutoZoomEnabled" format="boolean" />
        <attr name="cropShowTag" format="boolean" />
        <attr name="cropMaxZoom" format="integer" />
        <attr name="cropMultiTouchEnabled" format="boolean" />
        <attr name="cropFixAspectRatio" format="boolean" />
        <attr name="cropAspectRatioX" format="integer" />
        <attr name="cropAspectRatioY" format="integer" />
        <attr name="cropInitialCropWindowPaddingRatio" format="float" />
        <attr name="cropBorderLineThickness" format="dimension" />
        <attr name="cropBorderLineColor" format="color" />
        <attr name="cropBorderCornerThickness" format="dimension" />
        <attr name="cropBorderCornerOffset" format="dimension" />
        <attr name="cropBorderCornerLength" format="dimension" />
        <attr name="cropBorderCornerColor" format="color" />
        <attr name="cropGuidelinesThickness" format="dimension" />
        <attr name="cropGuidelinesColor" format="color" />
        <attr name="cropBackgroundColor" format="color" />
        <attr name="cropSnapRadius" format="dimension" />
        <attr name="cropTouchRadius" format="dimension" />
        <attr name="cropSaveBitmapToInstanceState" format="boolean" />
        <attr name="cropShowCropOverlay" format="boolean" />
        <attr name="cropShowProgressBar" format="boolean" />
        <attr name="isCrop" format="boolean" />
        <attr name="cropMinCropWindowWidth" format="dimension" />
        <attr name="cropMinCropWindowHeight" format="dimension" />
        <attr name="cropMinCropResultWidthPX" format="float" />
        <attr name="cropMinCropResultHeightPX" format="float" />
        <attr name="cropMaxCropResultWidthPX" format="float" />
        <attr name="cropMaxCropResultHeightPX" format="float" />
        <attr name="cropFlipHorizontally" format="boolean" />
        <attr name="cropFlipVertically" format="boolean" />
    </declare-styleable>
    <string name="crop_image_activity_no_permissions" translatable="false">Cancelling, required permissions are not granted</string>
    <string name="crop_image_activity_title" />
    <string name="pick_image_intent_chooser_title" translatable="false">Select source</string>

    <declare-styleable name="ShadowLayout">
        <attr name="sl_cornerRadius" format="dimension" />
        <attr name="sl_shadowRadius" format="dimension" />
        <attr name="sl_shadowColor" format="color" />
        <attr name="sl_dx" format="dimension" />
        <attr name="sl_dy" format="dimension" />
        <attr name="sl_borderColor" format="color" />
        <attr name="sl_borderWidth" format="dimension" />
        <attr name="sl_shadowSides" format="flags">
            <flag name="TOP" value="1" />
            <flag name="RIGHT" value="2" />
            <flag name="BOTTOM" value="4" />
            <flag name="LEFT" value="8" />
            <flag name="ALL" value="15" />
        </attr>
    </declare-styleable>

    <declare-styleable name="background">
        <attr name="bl_shape" format="enum">
            <enum name="rectangle" value="0" />
            <enum name="oval" value="1" />
            <enum name="line" value="2" />
            <enum name="ring" value="3" />
        </attr>
        <attr name="bl_solid_color" format="color" />
        <attr name="bl_corners_radius" format="dimension" />
        <attr name="bl_corners_bottomLeftRadius" format="dimension" />
        <attr name="bl_corners_bottomRightRadius" format="dimension" />
        <attr name="bl_corners_topLeftRadius" format="dimension" />
        <attr name="bl_corners_topRightRadius" format="dimension" />
        <attr name="bl_gradient_angle" format="integer" />
        <attr name="bl_gradient_centerX" format="float" />
        <attr name="bl_gradient_centerY" format="float" />
        <attr name="bl_gradient_centerColor" format="color" />
        <attr name="bl_gradient_endColor" format="color" />
        <attr name="bl_gradient_startColor" format="color" />
        <attr name="bl_gradient_gradientRadius" format="dimension" />
        <attr name="bl_gradient_type" format="enum">
            <enum name="linear" value="0" />
            <enum name="radial" value="1" />
            <enum name="sweep" value="2" />
        </attr>
        <attr name="bl_gradient_useLevel" format="boolean" />
        <attr name="bl_padding_left" format="dimension" />
        <attr name="bl_padding_top" format="dimension" />
        <attr name="bl_padding_right" format="dimension" />
        <attr name="bl_padding_bottom" format="dimension" />
        <attr name="bl_size_width" format="dimension">
            <enum name="wrap_content" value="-2" />
            <enum name="match_parent" value="-1" />
        </attr>
        <attr name="bl_size_height" format="dimension">
            <enum name="wrap_content" value="-2" />
            <enum name="match_parent" value="-1" />
        </attr>
        <attr name="bl_stroke_width" format="dimension" />
        <attr name="bl_stroke_color" format="color" />
        <attr name="bl_stroke_dashWidth" format="dimension" />
        <attr name="bl_stroke_dashGap" format="dimension" />
        <attr name="bl_ripple_enable" format="boolean" />
        <attr name="bl_ripple_color" format="color" />
        <attr name="bl_checkable_stroke_color" format="color" />
        <attr name="bl_checked_stroke_color" format="color" />
        <attr name="bl_enabled_stroke_color" format="color" />
        <attr name="bl_selected_stroke_color" format="color" />
        <attr name="bl_pressed_stroke_color" format="color" />
        <attr name="bl_focused_stroke_color" format="color" />
        <attr name="bl_unCheckable_stroke_color" format="color" />
        <attr name="bl_unChecked_stroke_color" format="color" />
        <attr name="bl_unEnabled_stroke_color" format="color" />
        <attr name="bl_unSelected_stroke_color" format="color" />
        <attr name="bl_unPressed_stroke_color" format="color" />
        <attr name="bl_unFocused_stroke_color" format="color" />
        <attr name="bl_checkable_solid_color" format="color" />
        <attr name="bl_checked_solid_color" format="color" />
        <attr name="bl_enabled_solid_color" format="color" />
        <attr name="bl_selected_solid_color" format="color" />
        <attr name="bl_pressed_solid_color" format="color" />
        <attr name="bl_focused_solid_color" format="color" />
        <attr name="bl_unCheckable_solid_color" format="color" />
        <attr name="bl_unChecked_solid_color" format="color" />
        <attr name="bl_unEnabled_solid_color" format="color" />
        <attr name="bl_unSelected_solid_color" format="color" />
        <attr name="bl_unPressed_solid_color" format="color" />
        <attr name="bl_unFocused_solid_color" format="color" />
        <attr name="bl_stroke_position" format="flags">
            <flag name="left" value="2" />
            <flag name="top" value="4" />
            <flag name="right" value="8" />
            <flag name="bottom" value="16" />
        </attr>
    </declare-styleable>
    <declare-styleable name="ESCheckBox">
        <attr name="checkedImage" format="reference" />
        <attr name="uncheckedImage" format="reference" />
        <attr name="defaultCheckStatus" format="boolean" />
    </declare-styleable>
</resources>