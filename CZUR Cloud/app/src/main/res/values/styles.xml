<resources>
    <!--BaseActivity全屏主题-->
    <!--BaseActivity全屏主题-->
    <!--BaseActivity全屏主题-->

    <style name="CZURAppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorAccent">@color/edittext_cursor_blue_color</item>
    </style>

    <!--闪屏页主题-->
    <style name="SplashTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowDisablePreview">true</item>
    </style>

    <!-- 主页RadioButton-->
    <style name="index_radio_button">
        <item name="android:button">@null</item>
        <item name="android:scaleType">centerInside</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_height">match_parent</item>
    </style>


    <!-- 登录页面底部弹出框-->
    <style name="SocialAccountDialogStyle" parent="@android:style/Theme.Dialog">
        <!-- 边框 -->
        <item name="android:windowFrame">@null</item>
        <!-- 没有标题 -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:layout_width">match_parent</item>
        <!-- 是否浮现在activity之上 -->
<!--        <item name="android:windowIsFloating">false</item>-->
        <!-- 半透明 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 不透明百分比-->
        <item name="android:backgroundDimAmount">0.2</item>
        <!-- 模糊 -->
<!--        <item name="android:backgroundDimEnabled">true</item>-->
        <!-- 对话框的背景-->
        <item name="android:windowBackground">@color/blackOpaque50</item>
        <item name="android:windowAnimationStyle">@style/BottomSheetAnimation</item>
    </style>

    <style name="TranslucentTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@android:style/Animation</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!-- 登录页面底部弹出框动画-->
    <style name="BottomSheetAnimation">
        <item name="android:windowEnterAnimation">@anim/bottom_sheet_enter_anim</item>
        <item name="android:windowExitAnimation">@anim/bottom_sheet_exit_anim</item>
    </style>


    <style name="BottomAnimation">
        <item name="android:windowEnterAnimation">@anim/bottom_sheet_enter_anim</item>
        <item name="android:windowExitAnimation">@anim/bottom_sheet_exit_anim</item>
    </style>

    <style name="CheckBoxBlue" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:background">@drawable/selector_checkbox_record_list</item>
        <item name="android:button">@null</item>
    </style>

    <style name="BookCheckBox" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:background">@drawable/selector_checkbox</item>
        <item name="android:button">@null</item>
    </style>

    <style name="MirrorCheckBox" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:background">@drawable/selector_checkbox_mirror</item>
        <item name="android:button">@null</item>
    </style>

    <style name="StarryMsgCheckBox" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:background">@drawable/starry_msg_sel</item>
        <item name="android:button">@null</item>
    </style>

    <style name="NewCheckBox" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:background">@drawable/selector_checkbox_new</item>
        <item name="android:button">@null</item>
    </style>

    <style name="BottomDialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="BottomDialogAnimation" parent="android:Animation">
        <item name="android:windowAnimationStyle">@style/BottomDialog.Animation</item>
    </style>
    <style name="BottomDialog.Animation">
        <item name="android:windowEnterAnimation">@anim/translate_dialog_in</item>
        <item name="android:windowExitAnimation">@anim/translate_dialog_out</item>
    </style>

    <style name="My_CheckBox" parent="@android:style/Widget.Material.CompoundButton.CheckBox">
        <item name="android:colorControlActivated">@color/normal_blue</item>
        <item name="android:colorControlNormal">@color/normal_blue</item>
    </style>

    <!-- 错误坐姿popmenu -->
    <style name="ErrorSitPicTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:dropDownListViewStyle">@style/MyListViewStyle</item>
        <item name="android:popupMenuStyle">@style/MyPopupMenu</item>
        <item name="android:textAppearanceSmallPopupMenu">@style/MyTextAppearance</item>
        <item name="android:textAppearanceLargePopupMenu">@style/MyTextAppearance</item>
    </style>

    <style name="MyListViewStyle">
        <item name="android:divider">#888</item>
        <item name="android:dividerHeight">1px</item>
    </style>

    <style name="MyPopupMenu" >
        <item name="android:popupBackground">#eee</item>
    </style>

    <style name="MyTextAppearance">
        <item name="android:textColor">#444</item>
        <item name="android:textSize">16sp</item>
        <item name="android:gravity">center</item>
    </style>

    <!--对话框样式-->
    <style name="sittingDialog" parent="Theme.AppCompat.Dialog">
        <!--背景透明-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--没有标题-->
        <item name="android:windowNoTitle">true</item>
        <!--背景昏暗-->
        <item name="android:backgroundDimEnabled">true</item>
    </style>

    <!-- Starry Meeting -->
    <style name="tv_remote_req_float_action">
        <item name="android:layout_width">200dp</item>
        <item name="android:layout_height">50dp</item>
        <item name="android:textSize">20sp</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:gravity">center</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="iv_item_member_icon">
        <item name="android:layout_width">40dp</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:padding">10dp</item>
    </style>

    <style name="tv_item_member_label">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/starryFontSize14</item>
        <item name="android:layout_marginEnd">5dp</item>
        <item name="android:padding">5dp</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="chat_head_iv">
        <item name="android:layout_width">30dp</item>
        <item name="android:layout_height">30dp</item>
    </style>
    <style name="chat_content_tv">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:maxWidth">230dp</item>
        <item name="android:padding">10dp</item>
        <item name="android:textSize">14sp</item>
    </style>

    <!-- member list bottom button style-->
    <style name="member_nav_btn_ll">
        <item name="android:layout_width">@dimen/starryMemberListBottomButtonWidth</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center</item>
        <item name="android:orientation">vertical</item>
        <item name="android:padding">8dp</item>
        <item name="android:layout_marginBottom">@dimen/starry_meeting_member_btn_margin</item>
    </style>

    <style name="meeting_main_navbar_ll_style">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">0dp</item>
        <item name="android:gravity">center</item>
        <item name="android:orientation">vertical</item>
        <item name="android:layout_marginBottom">5dp</item>
    </style>
    <style name="meeting_main_navbar_image_style">
        <item name="android:layout_width">@dimen/starry_meeting_nav_btn_width</item>
        <item name="android:layout_height">@dimen/starry_meeting_nav_btn_height</item>
        <item name="android:padding">@dimen/starry_meeting_nav_padding</item>
    </style>
    <style name="meeting_main_navbar_title_style">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">24dp</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">#ffffff</item>
        <item name="android:textSize">@dimen/starry_meeting_nav_title_size</item>
        <item name="android:layout_marginTop">-8dp</item>
    </style>

    <style name="starry_contact_cell_rl_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/starryMargin60</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="starry_contact_cell_title_style">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textColor">@color/starry_text_color_gray</item>
        <item name="android:textSize">@dimen/starryCompanySubTitle</item>
        <item name="android:layout_marginStart">@dimen/starryMargin20</item>
    </style>
    <style name="starry_contact_cell_value_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/starryMargin60</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textColor">@color/starry_title_value</item>
        <item name="android:textSize">@dimen/starryCompanySubTitle</item>
        <item name="android:paddingStart">@dimen/starryMargin20</item>
        <item name="android:paddingEnd">@dimen/starryMargin20</item>
    </style>

    <style name="starry_message_menu_title_style">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textSize">@dimen/starryFontSize14</item>
        <item name="android:padding">@dimen/starryMargin10</item>
    </style>

    <style name="starry_fragment_float_title_style">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/starry_title_value</item>
        <item name="android:textSize">@dimen/starryFontSize20</item>
        <item name="android:textStyle">bold</item>
        <item name="android:layout_marginStart">@dimen/starryMargin20</item>
    </style>

    <style name="starry_fragment_float_close_style">
        <item name="android:layout_width">@dimen/starryMargin50</item>
        <item name="android:layout_height">@dimen/starryMargin50</item>
        <item name="android:padding">@dimen/starryMargin10</item>
        <item name="android:src">@mipmap/starry_btn_close</item>
        <item name="android:layout_marginEnd">@dimen/starryMargin10</item>
    </style>
    <style name="starry_fragment_float_close_style_land" parent="starry_fragment_float_close_style">
        <item name="android:layout_width">@dimen/starryMargin45</item>
        <item name="android:layout_height">@dimen/starryMargin45</item>
    </style>

    <style name="anim_style">
        <!-- 指定显示的动画xml -->
        <item name="android:windowEnterAnimation">@anim/show_from_bottom</item>
        <!-- 指定消失的动画xml -->
        <item name="android:windowExitAnimation">@anim/hide_to_bottom</item>
    </style>

    <style name="starry_list_gray_line">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1dp</item>
        <item name="android:background">@color/starry_gray_bg</item>
    </style>

    <style name="starry_bottom_menu_text_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/starry_title_value</item>
        <item name="android:textSize">@dimen/starryFontSize16</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">center</item>
        <item name="android:padding">12dp</item>
    </style>

    <style name="starry_bottom_menu_text_style_nopadding">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/starry_title_value</item>
        <item name="android:textSize">@dimen/starryFontSize16</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="starry_contact_name_text_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/starry_title_value</item>
        <item name="android:textSize">@dimen/starryFontSize20</item>
        <item name="android:paddingStart">@dimen/starryMargin20</item>
    </style>

    <style name="starry_list_space_line_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0.5dp</item>
        <item name="android:layout_marginStart">@dimen/starryMargin20</item>
        <item name="android:background">@color/starry_contact_liner_color</item>
    </style>

    <style name="starry_button_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/starryMargin40</item>
        <item name="android:layout_margin">@dimen/starryMargin20</item>
        <item name="android:background">@drawable/starry_btn_rec_6_bg_with_blue</item>
        <item name="android:textSize">@dimen/starryCompanySubTitle</item>
        <item name="android:textColor">@color/starry_white_bg</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="starry_contact_detail_btn_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/starryMargin40</item>
        <item name="android:background">@drawable/starry_btn_rec_6_bg_with_blue</item>
        <item name="android:textSize">@dimen/starryCompanySubTitle</item>
        <item name="android:textColor">@color/starry_white_bg</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="starry_contact_detail_btn_red_style" parent="starry_contact_detail_btn_style">
        <item name="android:background">@drawable/starry_btn_rec_5_bg_with_red</item>
    </style>

    <style name="eshare_bottom_line" >
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:orientation">horizontal</item>
    </style>

    <style name="eshare_bottom_pannel" >
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">@dimen/starryMargin100</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:background">@drawable/eshare_pannel_bg</item>
        <item name="android:padding">@dimen/starryMargin10</item>
    </style>

    <!--  添加新设备  -->
    <style name="add_equipment_outer_ll" >
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">7dp</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:visibility">visible</item>
    </style>

    <style name="add_equipment_title" >
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginStart">18.5dp</item>
        <item name="android:layout_marginTop">25dp</item>
        <item name="android:textColor">@color/gary_c4</item>
        <item name="android:textSize">12sp</item>
    </style>

    <style name="add_equipment_name" >
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">center_horizontal</item>
        <item name="android:layout_marginTop">1dp</item>
        <item name="android:textColor">@color/black_22</item>
        <item name="android:textSize">15sp</item>
    </style>

    <style name="add_equipment_panel_ll" >
        <item name="android:layout_width">119.5dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">7dp</item>
        <item name="android:orientation">vertical</item>
    </style>

    <style name="add_equipment_panel_rl" >
        <item name="android:layout_width">119.5dp</item>
        <item name="android:layout_height">152dp</item>
    </style>

    <style name="add_equipment_image_icon" >
        <item name="android:layout_width">119.5dp</item>
        <item name="android:layout_height">152dp</item>
        <item name="android:layout_centerInParent">true</item>
        <item name="android:scaleType">centerInside</item>
    </style>

    <style name="add_equipment_image_select" >
        <item name="android:layout_width">107.5dp</item>
        <item name="android:layout_height">140dp</item>
        <item name="android:layout_centerInParent">true</item>
        <item name="android:background">@mipmap/equipment_black_selection</item>
        <item name="android:visibility">gone</item>
    </style>

    <style name="add_equipment_text_add" >
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_alignParentBottom">true</item>
        <item name="android:layout_centerHorizontal">true</item>
        <item name="android:layout_marginBottom">9dp</item>
        <item name="android:text">@string/has_added</item>
        <item name="android:textColor">@color/gary_c4</item>
        <item name="android:textSize">8sp</item>
    </style>

    <style name="add_equipment_has_added_rl" >
        <item name="android:layout_width">105dp</item>
        <item name="android:layout_height">140dp</item>
        <item name="android:layout_centerInParent">true</item>
        <item name="android:visibility">gone</item>
    </style>
    <style name="paint_color" >
        <item name="android:layout_width">20dp</item>
        <item name="android:layout_height">20dp</item>
        <item name="android:layout_margin">@dimen/starryMargin10</item>
        <item name="android:layout_centerInParent">true</item>
    </style>

    <style name="display_grid_container" >
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_marginStart">@dimen/starryMargin3</item>
        <item name="android:layout_marginEnd">@dimen/starryMargin3</item>
        <item name="android:layout_marginTop">@dimen/starryMargin10</item>
        <item name="android:layout_marginBottom">@dimen/starryMargin10</item>
        <item name="android:background">@color/transparent</item>
    </style>

    <style name="user_baseline_view_style" >
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0.5dp</item>
        <item name="android:layout_alignParentBottom">true</item>
        <item name="android:layout_marginStart">16dp</item>
        <item name="android:background">@color/gray_e5</item>
    </style>
    <style name="user_rightarrow_img_style" >
        <item name="android:layout_width">6dp</item>
        <item name="android:layout_height">10.5dp</item>
        <item name="android:layout_alignParentEnd">true</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:layout_marginEnd">17dp</item>
        <item name="android:background">@mipmap/user_right_gray_arrow</item>
        <item name="android:padding">10dp</item>
    </style>
    <style name="user_icon_img_style" >
        <item name="android:layout_width">18dp</item>
        <item name="android:layout_height">18dp</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:layout_marginStart">16dp</item>
    </style>
    <style name="user_title_tv_style" >
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:layout_marginStart">17.5dp</item>
        <item name="android:textColor">@color/normal_blue</item>
        <item name="android:textSize">15sp</item>
    </style>

    <style name="starry_meeting_share_ll" >
        <item name="android:layout_width">80dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:gravity">center</item>
        <item name="android:orientation">vertical</item>
    </style>

    <style name="starry_meeting_refresh_style" >
        <item name="android:layout_width">@dimen/starryMargin30</item>
        <item name="android:layout_height">@dimen/starryMargin30</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:padding">6dp</item>
        <item name="android:layout_marginStart">@dimen/starryMargin5</item>
    </style>

    <style name="animScaleY">
        <item name="android:windowEnterAnimation">@anim/anim_scale_y_in</item>
        <item name="android:windowExitAnimation">@anim/anim_scale_y_out</item>
    </style>

    <style name="animAlpha">
        <item name="android:windowEnterAnimation">@anim/anim_alpha_in</item>
        <item name="android:windowExitAnimation">@anim/anim_alpha_out</item>
    </style>

</resources>
