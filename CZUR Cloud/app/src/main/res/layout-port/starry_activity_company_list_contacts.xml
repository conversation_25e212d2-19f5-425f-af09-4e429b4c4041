<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/starry_white_bg">

    <RelativeLayout
        android:id="@+id/select_company_contact_top_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/starry_top_bar_height"
        android:background="@color/gary_ff">

        <ImageView
            android:id="@+id/user_back_btn"
            android:layout_width="@dimen/starryMargin30"
            android:layout_height="@dimen/starryMargin40"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/starryMargin10"
            android:padding="@dimen/starryMargin10"
            android:src="@mipmap/login_back_icon" />
        <ImageView
            android:id="@+id/user_back_btn_bg"
            android:layout_width="@dimen/starryMargin30"
            android:layout_height="@dimen/starryMargin30"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/starryMargin20"
            android:padding="@dimen/starryMargin8"
            android:background="@drawable/circle_with_gray_f2"
            android:src="@mipmap/login_back_icon"
            android:visibility="gone"/>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center_vertical"
            >

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/msg_top_select_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/account_title"
                android:text=""
                android:maxLines="1"
                android:ellipsize="end"
                android:maxEms="10"
                android:textSize="@dimen/starryTopTitleSize" />
        </LinearLayout>

        <TextView
            android:id="@+id/msg_top_select_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/starryMargin10"
            android:padding="@dimen/starryMargin10"
            android:text="@string/starry_msg_select_cancel"
            android:textColor="@color/starry_top_btn_blue"
            android:textSize="@dimen/starryTopBtnSize"
            android:visibility="gone" />

    </RelativeLayout>

    <com.czur.cloud.ui.starry.component.SearchView
        android:id="@+id/contacts_search_rl"
        android:layout_width="match_parent"
        android:layout_height="@dimen/starryMargin35"
        android:layout_below="@+id/select_company_contact_top_bar"
        android:layout_marginStart="@dimen/starryMargin20"
        android:layout_marginTop="@dimen/starryMargin20"
        android:layout_marginEnd="@dimen/starryMargin20"
        android:focusable="false"
        android:visibility="visible" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view_contacts"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/bottom_btn_ll"
        android:layout_below="@+id/contacts_search_rl"
        android:layout_marginStart="@dimen/starryMargin20"
        android:layout_marginTop="@dimen/starryMargin10"
        android:layout_marginEnd="@dimen/starryMargin20"
        android:background="@color/starry_white_bg"
        android:overScrollMode="never"
        android:visibility="visible" />

    <com.czur.cloud.ui.starry.component.LetterViewNew
        android:id="@+id/letter_view"
        android:layout_width="@dimen/starryMargin30"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="@dimen/starryMargin150"
        android:layout_marginBottom="@dimen/starryMargin10"
        android:visibility="gone"/>

    <RelativeLayout
        android:id="@+id/bottom_btn_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="@dimen/starryMargin5"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/contacts_selected_count_tv"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/starryMargin40"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/starryMargin20"
            android:layout_marginTop="@dimen/starryMargin10"
            android:layout_marginEnd="@dimen/starryMargin20"
            android:layout_marginBottom="@dimen/starryMargin5"
            android:gravity="center"
            android:text="@string/starry_company_list_contacts_selected_count"
            android:textColor="@color/starry_btn_blue"
            android:textSize="@dimen/starryFontSize18"
            android:visibility="visible" />

        <com.czur.cloud.ui.starry.component.DrawableCenterOneLineTextView
            android:id="@+id/contact_new_call_btn"
            style="@style/starry_contact_detail_btn_style"
            android:layout_width="@dimen/starryNewCallBtnWidth"
            android:layout_height="@dimen/starryMargin40"
            android:layout_alignParentEnd="true"
            android:layout_marginStart="@dimen/starryMargin20"
            android:layout_marginTop="@dimen/starryMargin10"
            android:layout_marginEnd="@dimen/starryMargin20"
            android:layout_marginBottom="@dimen/starryMargin5"
            android:drawableStart="@mipmap/starry_call_btn_logo"
            android:drawablePadding="@dimen/dp10"
            android:text="@string/starry_new_call_btn"
            android:textSize="@dimen/starryNetErrorSize"
            android:visibility="visible" />

        <TextView
            android:id="@+id/contact_add_btn"
            style="@style/starry_contact_detail_btn_style"
            android:layout_width="120dp"
            android:layout_height="@dimen/starryMargin40"
            android:layout_alignParentEnd="true"
            android:layout_marginStart="@dimen/starryMargin20"
            android:layout_marginTop="@dimen/starryMargin10"
            android:layout_marginEnd="@dimen/starryMargin20"
            android:layout_marginBottom="@dimen/starryMargin5"
            android:text="@string/starry_contact_add_btn"
            android:visibility="gone" />
    </RelativeLayout>
</RelativeLayout>

