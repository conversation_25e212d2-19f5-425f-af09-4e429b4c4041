<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:background="@color/starry_comm_gray_bg"
    xmlns:tools="http://schemas.android.com/tools">

    <include
        android:id="@+id/starry_layout_top_bar_rl"
        layout="@layout/starry_layout_top_bar" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/starry_layout_top_bar_rl"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/contact_user_name_rl"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin100"
            android:layout_gravity="center_vertical"
            android:background="@color/starry_white_bg">

            <EditText
                android:id="@+id/contact_user_name"
                style="@style/starry_contact_name_text_style"
                android:background="@color/transparent"
                android:hint="@string/starry_add_contact_name"
                android:paddingEnd="20dp"
                android:singleLine="true"
                android:textColorHint="@color/starry_title_gray"
                android:textCursorDrawable="@drawable/edittext_cursor_blue"
                android:visibility="visible" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/contact_nickname_rl"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin60"
            android:layout_gravity="center_vertical"
            android:background="@color/starry_white_bg">

            <View
                style="@style/starry_list_space_line_style"
                android:id="@+id/contact_nickname_line"
                />

            <TextView
                android:id="@+id/contact_nickname_title"
                style="@style/starry_contact_cell_title_style"
                android:text="@string/starry_company_nickname" />

            <TextView
                android:id="@+id/contact_nickname"
                style="@style/starry_contact_cell_value_style"
                android:layout_toEndOf="@+id/contact_nickname_title" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/contact_mobile_rl"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin60"
            android:layout_gravity="center_vertical"
            android:background="@color/starry_white_bg">

            <View
                style="@style/starry_list_space_line_style"
                android:id="@+id/contact_mobile_line"
                />

            <TextView
                android:id="@+id/contact_mobile_title1"
                style="@style/starry_contact_cell_title_style"
                android:text="@string/starry_company_mobile" />

            <TextView
                android:id="@+id/contact_mobile"
                style="@style/starry_contact_cell_value_style"
                android:layout_toEndOf="@+id/contact_mobile_title1" />

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin10"
            android:background="@color/starry_comm_gray_bg" />

        <LinearLayout
            android:id="@+id/contact_detail_company_out_ll"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/contact_detail_company_name_ll"
                android:layout_width="match_parent"
                android:layout_height="@dimen/starryMargin60"
                android:background="@color/starry_white_bg"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="@dimen/starryMargin15"
                    android:layout_height="@dimen/starryMargin15"
                    android:layout_marginStart="@dimen/starryMargin20"
                    android:src="@mipmap/starry_home_comapny_icon" />

                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="@dimen/starryMargin20"
                    android:layout_weight="1"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/contact_detail_company_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:maxLines="1"
                        android:padding="@dimen/starryMargin5"
                        android:textColor="@color/starry_title_value"
                        android:textSize="@dimen/starryCompanySubTitle"
                        tools:text="企业名称" />

                    <TextView
                        android:id="@+id/contact_detail_company_name_old"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/contact_detail_company_name"
                        android:layout_marginStart="@dimen/starryMargin5"
                        android:gravity="center_vertical"
                        android:text="@string/starry_company_old"
                        android:textColor="@color/starry_delete_red"
                        android:textSize="@dimen/starryCompanyBtnTitle"
                        android:visibility="gone" />

                </RelativeLayout>

                <ImageView
                    android:id="@+id/contact_detail_company_change_btn"
                    android:layout_width="@dimen/starryMargin35"
                    android:layout_height="@dimen/starryMargin35"
                    android:layout_marginEnd="@dimen/starryMargin20"
                    android:padding="@dimen/dp_5"
                    android:src="@mipmap/starry_change_company" />

            </LinearLayout>

            <View style="@style/starry_list_space_line_style" />

            <RelativeLayout
                android:id="@+id/contact_detail_company_name_rl"
                android:layout_width="match_parent"
                android:layout_height="@dimen/starryMargin60"
                android:layout_gravity="center_vertical"
                android:background="@color/starry_white_bg">

                <TextView
                    android:id="@+id/contact_detail_company_name_tv"
                    style="@style/starry_contact_cell_title_style"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="@string/starry_contact_company_name" />

                <TextView
                    android:id="@+id/contact_detail_company_name_value_tv"
                    style="@style/starry_contact_cell_value_style"
                    android:layout_toEndOf="@+id/contact_detail_company_name_tv" />

            </RelativeLayout>

            <View style="@style/starry_list_space_line_style" />

            <RelativeLayout
                android:id="@+id/contact_detail_company_title_rl"
                android:layout_width="match_parent"
                android:layout_height="@dimen/starryMargin60"
                android:layout_gravity="center_vertical"
                android:background="@color/starry_white_bg">

                <TextView
                    android:id="@+id/contact_detail_company_title_tv"
                    style="@style/starry_contact_cell_title_style"
                    android:text="@string/starry_contact_company_title" />

                <TextView
                    android:id="@+id/contact_detail_company_title_value_tv"
                    style="@style/starry_contact_cell_value_style"
                    android:layout_toEndOf="@+id/contact_detail_company_title_tv" />

            </RelativeLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/starryMargin20">

            <TextView
                android:id="@+id/contact_save_to_btn"
                style="@style/starry_contact_detail_btn_style"
                android:text="@string/starry_save_contact_btn"
                android:visibility="visible" />

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>

