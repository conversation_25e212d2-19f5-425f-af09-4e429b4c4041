<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:elevation="10dp"
    app:bl_corners_topLeftRadius="@dimen/starry_meeting_member_bg_corners_radius"
    app:bl_corners_topRightRadius="@dimen/starry_meeting_member_bg_corners_radius"
    app:bl_solid_color="@color/white"
    app:layout_constraintLeft_toLeftOf="parent"
    app:layout_constraintTop_toTopOf="parent">

    <FrameLayout
        android:id="@+id/chat_main_cl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guidelineTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_begin="60dp" />

    <TextView
        android:id="@+id/chatTitleTv"
        style="@style/starry_fragment_float_title_style"
        app:layout_constraintBottom_toBottomOf="@id/guidelineTitle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:text="@string/starry_tab_chat_title"
        />

    <ImageView
        android:id="@+id/btnCloseIv"
        style="@style/starry_fragment_float_close_style"
        app:layout_constraintBottom_toBottomOf="@id/guidelineTitle"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="0dp"
        android:layout_height="1.5dp"
        android:background="@color/starry_comm_gray_bg"
        app:layout_constraintBottom_toBottomOf="@+id/guidelineTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/chatRv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/guidelineBottom"
        app:layout_constraintTop_toTopOf="@+id/guidelineTitle"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:paddingTop="26dp"
        android:clipToPadding="false"
        android:overScrollMode="never"/>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guidelineBottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_end="45dp" />

    <EditText
        android:id="@+id/chatInputEt"
        android:layout_width="0dp"
        android:layout_height="36dp"
        android:layout_margin="6dp"
        android:background="@drawable/starry_rec_10_bg_with_gray"
        android:gravity="center_vertical"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:textColor="@color/black"
        android:textSize="18sp"
        android:maxLength="1000"
        android:textColorHint="@color/starry_title_gray"
        android:textCursorDrawable="@drawable/edittext_cursor_blue"
        app:layout_constraintTop_toTopOf="@+id/guidelineBottom"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btn_send_tv"
        app:layout_constraintStart_toStartOf="parent">

        <requestFocus />
    </EditText>
    <TextView
        android:id="@+id/btn_send_tv"
        android:layout_width="wrap_content"
        android:layout_height="45dp"
        android:gravity="center"
        android:paddingStart="15dp"
        android:paddingEnd="15dp"
        android:text="@string/starry_chat_send_btn"
        android:textColor="@color/global_accent"
        android:textSize="@dimen/global_text_size_large"
        android:textStyle="bold"
        app:layout_constraintTop_toTopOf="@+id/guidelineBottom"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        />


</androidx.constraintlayout.widget.ConstraintLayout>