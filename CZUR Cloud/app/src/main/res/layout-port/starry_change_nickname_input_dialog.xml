<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="300dp"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:background="@drawable/btn_rec_10_bg_with_white"
        android:gravity="center_horizontal"
        android:orientation="vertical">


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/starryMargin20"
            >

            <TextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/starry_changenickname_title"
                android:textStyle="bold"
                android:textColor="@color/black_22"
                android:textSize="@dimen/starryFontSize16" />

            <TextView
                android:id="@+id/message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/title"
                android:layout_marginTop="16dp"
                android:gravity="center"
                android:text=""
                android:textColor="@color/black_22"
                android:textSize="14sp"
                android:visibility="gone"/>

            <EditText
                android:id="@+id/edit_nickname"
                android:layout_width="match_parent"
                android:layout_height="@dimen/starryMargin45"
                android:layout_below="@+id/title"
                android:layout_marginTop="@dimen/starryMargin20"
                android:background="@drawable/eshare_input_bg"
                android:maxLength="15"
                android:hint="@string/starry_changenickname_hint"
                android:padding="@dimen/starryMargin8"
                android:singleLine="true"
                android:textAlignment="center"
                android:textCursorDrawable="@drawable/edittext_cursor_blue"
                android:textSize="@dimen/meeting_memberlist_name_size" />

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/gray_e5" />
        <!-- button -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/negative_button"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:textColor="@color/title_black_color"
                android:textSize="14sp"
                android:text="@string/starry_changenickname_cancel"
                android:textStyle="bold"
                />

            <View
                android:layout_width="0.5dp"
                android:layout_height="match_parent"
                android:background="@color/gray_e5" />

            <TextView
                android:id="@+id/positive_button"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/starry_changenickname_change"
                android:textStyle="bold"
                android:textColor="@color/blue_positive_button"
                android:textSize="14sp"
                />
        </LinearLayout>

    </LinearLayout>
</RelativeLayout>