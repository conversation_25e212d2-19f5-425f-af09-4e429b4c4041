<?xml version="1.0" encoding="utf-8"?>
<shortcuts xmlns:android="http://schemas.android.com/apk/res/android">


    <shortcut
        android:enabled="false"
        android:icon="@mipmap/eshare_widget_logo"
        android:shortcutId="test_1"
        android:shortcutShortLabel="@string/shortcut_wirlesscast">
        <intent
            android:action="android.intent.action.VIEW"
            android:targetClass="com.czur.cloud.ui.eshare.EShareEmptyActivity"
            android:targetPackage="com.czur.cloud">
            <extra android:name="eshareEmptyType" android:value="ESHARE_EMPTY_TYPE_FIND" />
        </intent>
        <categories android:name="android.shortcut.conversation" />
        <capability-binding android:key="actions.intent.CREATE_MESSAGE" />
    </shortcut>

    <shortcut
        android:enabled="true"
        android:icon="@mipmap/shortcut_starry_scan_small"
        android:shortcutId="test_0"
        android:shortcutShortLabel="@string/shortcut_scan">
        <intent
            android:action="android.intent.action.VIEW"
            android:targetClass="com.czur.cloud.ui.starry.livedatabus.ShortCutTrampolineActivity"
            android:targetPackage="com.czur.cloud">
            <extra android:name="eshareEmptyType" android:value="ESHARE_EMPTY_TYPE_SCAN" />
        </intent>
        <categories android:name="android.shortcut.conversation" />
        <capability-binding android:key="actions.intent.CREATE_MESSAGE" />
    </shortcut>
    
    <shortcut
        android:enabled="false"
        android:icon="@mipmap/shortcut_starry_join_small"
        android:shortcutId="test_2"
        android:shortcutShortLabel="@string/shortcut_joinmeet">
        <intent
            android:action="android.intent.action.VIEW"
            android:targetClass="com.czur.cloud.ui.starry.livedatabus.ShortCutTrampolineActivity"
            android:targetPackage="com.czur.cloud">
            <extra android:name="eshareEmptyType" android:value="STARRY_EMPTY_TYPE_JOIN" />
        </intent>
        <categories android:name="android.shortcut.conversation" />
        <capability-binding android:key="actions.intent.CREATE_MESSAGE" />
    </shortcut>

</shortcuts>