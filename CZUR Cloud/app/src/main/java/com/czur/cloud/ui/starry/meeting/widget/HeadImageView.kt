package com.czur.cloud.ui.starry.meeting.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import androidx.constraintlayout.utils.widget.ImageFilterView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.target.Target
import com.bumptech.glide.request.transition.Transition
import com.czur.cloud.R
import com.czur.cloud.ui.starry.meeting.baselib.utils.containsHan
import com.czur.cloud.ui.starry.meeting.baselib.utils.dp2px
import com.czur.cloud.ui.starry.meeting.baselib.utils.endSub
import com.czur.cloud.ui.starry.meeting.baselib.utils.startSub
import kotlin.math.min

/**
 * Created by 陈丰尧 on 2021/11/3
 * 绘制规则:
 *  1.头像默认为蓝底+白字（2汉字或2字母）；
 *  2. 背景为蓝色的部分(来电页面)为 白底蓝字
 *  3.如果名称包含汉字，则取后2位；如果不包含汉字，则取前2位。一个字母或汉字则居中处理。
 */
class HeadImageView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : ImageFilterView(context, attrs, defStyleAttr) {

    companion object {
        private const val THEME_CODE_DEF = 1
        private const val THEME_CODE_WHITE = 2

        private const val COLOR_BLUE = 0xFF2c86e4.toInt()
        private const val COLOR_WHITE = Color.WHITE

        // 文字大小比例
        private const val DEF_TEXT_SCALE: Float = 0.14F
        private const val DEF_TEXT_SCALE_HAN: Float = 0.11F
    }

    private enum class Theme(val code: Int, val bgColor: Int, val textColor: Int) {
        // 默认主题 蓝底白字
        DEF(THEME_CODE_DEF, COLOR_BLUE, COLOR_WHITE),

        // 白色主题 白底蓝字
        WHITE(THEME_CODE_WHITE, COLOR_WHITE, COLOR_BLUE);

        companion object {
            fun create(code: Int): Theme {
                return if (code == WHITE.code) {
                    WHITE
                } else {
                    DEF
                }
            }
        }
    }

    /**
     * 头像主题
     */
    private val theme: Theme

    var text: String? = null
        set(value) {
            val target = value?.let {
                when (it.length) {
                    0 -> null
                    1, 2 -> it
                    else -> {
                        val nameHasHans = it.containsHan()
                        if (nameHasHans) it.endSub(2) else it.startSub(2)
                    }
                }
            }
            field = target
            invalidate()
        }

    private val paint = Paint().apply {
        isAntiAlias = true  // 抗锯齿开
        style = Paint.Style.FILL
    }

    private val centerY: Float
        get() = height / 2F
    private val centerX: Float
        get() = width / 2F

    private val textScale: Float
//    private val textSize: Float = 40.0F
    private val textSize: Float = dp2px(context, 30.0F).toFloat()

    init {
        val ta = context.obtainStyledAttributes(attrs, R.styleable.HeadImageView)
        val scale = ta.getFloat(R.styleable.HeadImageView_text_scale, DEF_TEXT_SCALE)
        textScale = if (scale in 0F..1F) {
            scale
        } else {
            DEF_TEXT_SCALE
        }

        val themeCode = ta.getColor(R.styleable.HeadImageView_head_img_theme, Theme.DEF.code)
        theme = Theme.create(themeCode)

        ta.recycle()

        roundPercent = 1F
    }


    override fun onDraw(canvas: Canvas) {
        // 绘制背景
        paint.color = theme.bgColor
        val radius = min(width, height) / 2F
        canvas.drawCircle(width / 2F, height / 2F, radius, paint)

        if (text.isNullOrBlank()) {
            super.onDraw(canvas)
        } else {
            // 绘制文字
            val scale = if (text?.containsHan() == true){
                    DEF_TEXT_SCALE_HAN
                }else {
                    DEF_TEXT_SCALE
                }
            paint.color = theme.textColor
//            paint.textSize = textSize //height * textScale
            paint.textSize = dp2px(context, height * scale).toFloat()
            paint.textAlign = Paint.Align.CENTER
            val fontMetrics = paint.fontMetrics
            val top = fontMetrics.top
            val bottom = fontMetrics.bottom
//            val baseLineY = centerY - top / 2F - bottom / 2f
            val baseLineY = centerY - (top + bottom)/2f
            canvas.drawText(text!!, centerX, baseLineY, paint)
        }
    }

    fun clearImage(){
        Glide.with(this)
            .clear(this)
    }

    fun setUrlOrText(url: String, text: String) {
        // 1. 先设置默认文字
        if (url == "" || url == "null") {
            this.text = text
            // 没有图片URL 就直接return了
            return
        }
        // 2. 使用Glide加载图片
        Glide.with(this)
            .load(url)
            .placeholder(R.mipmap.starry_home_user_def)
            .apply(
                RequestOptions()
                .transform(
                    CenterCrop(),
                    RoundedCorners(100)
                ))
            .listener(object : RequestListener<Drawable> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Drawable>?,
                    isFirstResource: Boolean
                ): Boolean {
                    // 加载失败
                    return true
                }

                override fun onResourceReady(
                    resource: Drawable?,
                    model: Any?,
                    target: Target<Drawable>?,
                    dataSource: DataSource?,
                    isFirstResource: Boolean
                ): Boolean {
                    return false
                }
            })
            .into(this)
    }

    fun setRecycleViewImageText(text: String, position: Int) {
        // 1. 先设置默认文字
//        if (position == this.getTag(R.id.itemMemberHeadIv)){
//            this.text = text
//        }

        this.text = text

    }
    fun setRecycleViewImageUrl(url: String, accountId: String) {
        // 2. 使用Glide加载图片
        Glide.with(this)
            .load(url)
            .placeholder(R.mipmap.starry_home_user_def)
            .error(R.mipmap.starry_home_user_def)
            .apply(
                RequestOptions()
                    .transform(
                        CenterCrop(),
                        RoundedCorners(100)
                    ))
            .into(object: SimpleTarget<Drawable>(){
                override fun onResourceReady(
                    resource: Drawable,
                    transition: Transition<in Drawable>?
                ) {
                    if (accountId == <EMAIL>(R.id.itemMemberHeadIv)) {
                        <EMAIL>(resource)
                    }else{
                        <EMAIL>(R.mipmap.starry_home_user_def)
                    }
                }
            })
    }

    fun setRecycleViewImageUrl1(url: String, accountId: String) {
        // 2. 使用Glide加载图片
        Glide.with(this)
            .load(url)
            .placeholder(R.mipmap.starry_home_user_def)
            .error(R.mipmap.starry_home_user_def)
            .apply(
                RequestOptions()
                    .transform(
                        CenterCrop(),
                        RoundedCorners(100)
                    ))
            .into(this)
    }


}

fun HeadImageView.setUrlOrText(url: String?, text: String) {
    // 1. 先设置默认文字
    this.text = text
    if (url == null) {
        // 没有图片URL 就直接return了
        return
    }
    // 2. 使用Glide加载图片
    Glide.with(this)
        .load(url)
        .placeholder(R.mipmap.starry_home_user_def)
        .apply(RequestOptions()
            .transform(CenterCrop(),
                RoundedCorners(100)))
        .listener(object : RequestListener<Drawable> {
            override fun onLoadFailed(
                e: GlideException?,
                model: Any?,
                target: Target<Drawable>?,
                isFirstResource: Boolean
            ): Boolean {
                // 加载失败
                <EMAIL> = text
                return true
            }

            override fun onResourceReady(
                resource: Drawable?,
                model: Any?,
                target: Target<Drawable>?,
                dataSource: DataSource?,
                isFirstResource: Boolean
            ): Boolean {
                <EMAIL> = null
                return false
            }

        })
        .into(this)
}