package com.czur.cloud.ui.base;

import static com.czur.czurutils.log.CZURLogUtilsKt.logD;
import static com.czur.czurutils.log.CZURLogUtilsKt.logE;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.net.ConnectivityManager;
import android.net.NetworkCapabilities;
import android.net.NetworkRequest;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;

import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.OnLifecycleEvent;
import androidx.lifecycle.ProcessLifecycleOwner;
import androidx.lifecycle.ViewModelProvider;
import androidx.multidex.MultiDex;
import androidx.multidex.MultiDexApplication;

import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ServiceUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.blankj.utilcode.util.Utils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.cache.CustomCacheKeyFactory;
import com.czur.cloud.common.MobSDKUtils;
import com.czur.cloud.common.OSSInstance;
import com.czur.cloud.common.ShareSDKUtils;
import com.czur.cloud.entity.realm.BookEntity;
import com.czur.cloud.entity.realm.PageEntity;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.StarryClipboardEvent;
import com.czur.cloud.event.UpdateEvent;
import com.czur.cloud.netty.core.CZURTcpClient;
import com.czur.cloud.netty.observer.NettyService;
import com.czur.cloud.netty.observer.NettyUtils;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.ESharePreferences;
import com.czur.cloud.preferences.FirstPreferences;
import com.czur.cloud.preferences.MirrorOfflinePreferences;
import com.czur.cloud.preferences.StarryPreferences;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.preferences.VendorPushPreferences;
import com.czur.cloud.preferences.VersionPreferences;
import com.czur.cloud.ui.eshare.viewmodel.EShareApplicationViewModel;
import com.czur.cloud.ui.mirror.comm.OSSInstanceSitting;
import com.czur.cloud.ui.starry.meeting.model.MeetingModel;
import com.czur.cloud.ui.starry.network.StarryHttpManager;
import com.czur.cloud.util.Android16NotificationUtils;
import com.czur.cloud.util.Android16Utils;
import com.czur.cloud.util.AppClearUtils;
import com.czur.cloud.util.AppInitUtils;
import com.czur.cloud.util.NetworkStatusCallback;
import com.czur.cloud.vendorPush.VendorPushTask;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.imagepipeline.core.ImagePipelineConfig;
import com.jakewharton.threetenabp.AndroidThreeTen;
import com.tencent.bugly.crashreport.CrashReport;

import org.greenrobot.eventbus.EventBus;
import org.lsposed.hiddenapibypass.HiddenApiBypass;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;

import io.realm.Realm;
import io.realm.RealmConfiguration;
import io.realm.RealmResults;

/**
 * Created by Yz on 2018/2/14
 * Email：<EMAIL>
 */

public class CzurCloudApplication extends MultiDexApplication {

    private static String TAG = "CzurCloudApplication";
    private static CzurCloudApplication application;
    private final static String LOG_TAG = "CZUR";
    // 是否曾经入过会？没有：copy会弹出框；有入：copy则不弹；
    // isInMeeting=true时，赋值true，会议结束，不改变自己；
    // 当app进入后台，变为false
    public static Boolean isJoinedMeeting = false;

    // ta端登录,退出并弹窗提示的标志
    public static Boolean isOtherLogin = false;

    private EShareApplicationViewModel eshareViewModel = null;
    @Override
    public void onCreate() {
        super.onCreate();
        application = this;

        //初始化Log
//        initLog();
        //记录崩溃信息
//        getCrashLog();
        // CZURLog日志系统
        AppInitUtils.initCZURLog(application);
        eshareViewModel =
                new ViewModelProvider.AndroidViewModelFactory(this).create(EShareApplicationViewModel.class);

        UserPreferences.init(CzurCloudApplication.this);
        StarryPreferences.init(CzurCloudApplication.this);
        MirrorOfflinePreferences.init(CzurCloudApplication.this);
        ESharePreferences.init(CzurCloudApplication.this);
        VersionPreferences.init(this);
         String uniqueID = UUID.randomUUID().toString();
        if (Objects.equals(ESharePreferences.getInstance().getUUID(), "")) {
            ESharePreferences.getInstance().setUUID(uniqueID);
        }

        //初始化网络请求
        MiaoHttpManager.getInstance().init(this);
        //初始化网络请求
        StarryHttpManager.getInstance().init(this);

        //初始化工具类
        Utils.init(this);

        // Android 16适配初始化
        initAndroid16Adaptation();

        logI("======== CzurCloudApplication ========; this="+this);

        ProcessLifecycleOwner.get().getLifecycle().addObserver(new AppLifeObserver());

        MobSDKUtils.INSTANCE.preInit(application);

        // 初始化时它会加载threetenabp内置的time zone文件。
        AndroidThreeTen.init(this);

        VendorPushPreferences.INSTANCE.init(application);

        //首次进入需要显示隐私政策对话框
        if (!FirstPreferences.getInstance(this).isFirstEnterApp()) {

            //初始化fresco
            initFresco();
            //初始化数据库
            initRealm();
            ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
                @Override
                public Void doInBackground() throws Throwable {
                    //初始化OSS单例
                    OSSInstance.Companion.getInstance().init(CzurCloudApplication.this);
                    OSSInstanceSitting.Companion.getInstance().init(CzurCloudApplication.this);
                    return null;
                }

                @Override
                public void onSuccess(Void result) {

                }
            });
            ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
                @Override
                public Void doInBackground() throws Throwable {
                    // 友盟推送
                    String channel = getChannel();
                    logI("CzurCloudApplication.onCreate.channel="+channel);

                    // TODO OVERSEAS 海外注释掉
                    MobSDKUtils.INSTANCE.init(application);

                    //初始化Bugly
                    CrashReport.initCrashReport(getApplicationContext(), "615f3d0688", !BuildConfig.DEBUG);
                    CrashReport.setIsDevelopmentDevice(getApplicationContext(), BuildConfig.DEBUG);

                    // 初始化shareSdk
                    ShareSDKUtils.INSTANCE.init(CzurCloudApplication.this.getApplicationContext());
                    return null;
                }

                @Override
                public void onSuccess(Void result) {

                }
            });
            // 手机厂商检查
            VendorPushTask.INSTANCE.init(CzurCloudApplication.this);
            new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                @Override
                public void run() {
                    registerNetworkListener();
                    initChangeNetworkStatusListener();

                    // 息屏断网,联网,开屏,需要重新检测长连接
                    reTryConnectLong();

                }
            },1000);
        }
    }

    /** 初始化网络监听
     * 主要用于切换wifi时的监听 */
    private void initChangeNetworkStatusListener() {
        ConnectivityManager connMgr = (ConnectivityManager) Utils.getApp().getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connMgr != null) {
            //这里只监听了WIFI和蜂窝网络，正常APP够用了，如果有其他要求，可以增加
            NetworkRequest nr = new NetworkRequest.Builder()
                    .addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
                    .addTransportType(NetworkCapabilities.TRANSPORT_CELLULAR)
                    .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                    .build();
            connMgr.registerNetworkCallback(nr, new NetworkStatusCallback(Utils.getApp()));
        }
    }

    private void registerNetworkListener() {
        NetworkUtils.registerNetworkStatusChangedListener(onNetworkStatusChangedListener);
    }

    public static NetworkUtils.OnNetworkStatusChangedListener onNetworkStatusChangedListener = new NetworkUtils.OnNetworkStatusChangedListener() {
        @Override
        public void onDisconnected() {
            logD("<--- onDisconnected --->");
            ServiceUtils.stopService(NettyService.class);
            NettyUtils.getInstance().stopNettyService();
        }

        @Override
        public void onConnected(NetworkUtils.NetworkType networkType) {
            logD("<--- onConnected --->");
            EventBus.getDefault().post(new UpdateEvent(EventType.CHECK_FORCE_NEW_VERSION));

//            startService();
            reTryConnectLong();
        }
    };

    // 重连长连接
    private static void reTryConnectLong() {
        logI(TAG+".reTryConnectLong.startNettyStarryService");

        if (ServiceUtils.isServiceRunning(NettyService.class)) {
            logI(TAG+".reTryConnectLong.isServiceRunning");
            if (!CZURTcpClient.getInstance().getIsConnected().get()) {
                logI(TAG+".reTryConnectLong.isServiceRunning.getIsConnected: false");
                ServiceUtils.stopService(NettyService.class);
                try {
                    Thread.sleep(200);
                    AppClearUtils.startNettyStarryService();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        } else {
            ServiceUtils.stopService(NettyService.class);
            try {
                Thread.sleep(200);
                AppClearUtils.startNettyStarryService();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        MultiDex.install(this);
//        Reflection.unseal(base);

//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
//            HiddenApiBypass.addHiddenApiExemptions("L");
//        }

    }


    /**
     * 获取context
     *
     * @return
     */
    public static Context getApplication() {
        if (application == null) {
            application = new CzurCloudApplication();
        }
        return application;
    }

    private void initRealm() {
        Realm.init(this);
        RealmConfiguration config = new RealmConfiguration.Builder()
                //文件名
                .name("czur.realm")
                //版本号
                .schemaVersion(7)
                .compactOnLaunch()
                .deleteRealmIfMigrationNeeded()
                .migration(new MyMigration())
                .allowWritesOnUiThread(true)
                .allowQueriesOnUiThread(true)
                .build();
        Realm.setDefaultConfiguration(config);
        fillNoteName();
    }

    private void fillNoteName() {
        Realm realm = Realm.getDefaultInstance();
        realm.beginTransaction();
        RealmResults<BookEntity> bookEntities = realm.where(BookEntity.class).equalTo("isDelete", 0).findAll();
        for (BookEntity bookEntity : bookEntities) {
            RealmResults<PageEntity> pageEntities = realm.where(PageEntity.class).equalTo("bookId", bookEntity.getBookId()).findAll();
            for (PageEntity pageEntity : pageEntities) {
                pageEntity.setNoteName(bookEntity.getBookName());
            }
        }
        realm.commitTransaction();
        realm.close();
    }

    private void initFresco() {
        ImagePipelineConfig config = ImagePipelineConfig.newBuilder(this)
                .setCacheKeyFactory(CustomCacheKeyFactory.getInstance())
                .setDownsampleEnabled(true)
                .build();
        Fresco.initialize(this, config);
    }

    private String getChannel(){
        PackageManager pm = getPackageManager();
        ApplicationInfo applicationInfo = null;
        try {
            applicationInfo = pm.getApplicationInfo(getPackageName(), PackageManager.GET_META_DATA);
            return applicationInfo.metaData.getString("UMENG_CHANNEL");
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return "";
    }

    class AppLifeObserver implements LifecycleObserver {

        @OnLifecycleEvent(Lifecycle.Event.ON_START)
        private void  onForeground() {
            // 会议中，直接进入会议！！！
//            if (MeetingModel.isInMeeting){
//                ActivityUtils.startActivity(MeetingMainActivity.class);
//            }
//            LiveDataBus.get().with(StarryConstants.MEETING_CLIPBOARD).setValue(true);
            EventBus.getDefault().post(new StarryClipboardEvent(EventType.STARRY_MEETING_CLIPBOARD, ""));

        }

        @OnLifecycleEvent(Lifecycle.Event.ON_STOP)
        private void  onBackground() {
            if (!Boolean.TRUE.equals(MeetingModel.isInMeeting.getValue())) {
                isJoinedMeeting = false;
            }
        }
    }

    //记录崩溃信息
    private void getCrashLog(){
        final Thread.UncaughtExceptionHandler defaultHandler = Thread.getDefaultUncaughtExceptionHandler();
        Thread.setDefaultUncaughtExceptionHandler((thread, throwable) -> {
            //获取崩溃时的UNIX时间戳
            long timeMillis = System.currentTimeMillis();
            //将时间戳格式化，建立一个String拼接器
            StringBuilder stringBuilder = new StringBuilder(new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date(timeMillis)));
            stringBuilder.append(":\n");
            //获取错误信息
            stringBuilder.append(throwable.getMessage());
            stringBuilder.append("\n");
            //获取堆栈信息
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            throwable.printStackTrace(pw);
            stringBuilder.append(sw.toString());

            //这就是完整的错误信息了，你可以拿来上传服务器，或者做成本地文件保存等等等等
            String errorLog = stringBuilder.toString();
            logE("==>Crash<==", errorLog);

            //最后如何处理这个崩溃，这里使用默认的处理方式让APP停止运行
            assert defaultHandler != null;
            defaultHandler.uncaughtException(thread, throwable);
        });
    }

    public EShareApplicationViewModel getEshareViewModel1(){
        return eshareViewModel;
    }

    /**
     * Android 16适配初始化
     */
    private void initAndroid16Adaptation() {
        if (Build.VERSION.SDK_INT >= 36) {
            logI("CzurCloudApplication.initAndroid16Adaptation: Starting Android 16 global adaptation");

            // 1. 初始化Android 16通知渠道
            Android16NotificationUtils.INSTANCE.initNotificationChannels(this);

            // 2. 记录Android 16适配信息
            Android16Utils.INSTANCE.logAndroid16Info(this);

            logI("CzurCloudApplication.initAndroid16Adaptation: Android 16 global adaptation completed");
        }
    }
}
