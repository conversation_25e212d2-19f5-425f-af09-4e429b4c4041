package com.czur.cloud.ui.component.popup;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.czur.cloud.R;

public class AuraMatePCPopup extends Dialog {
    public static final float DIMMED_OPACITY = 0.2f;

    private AuraMatePCPopup(Context context, int theme) {
        super(context, theme);
    }

    public static class Builder {
        private Context context;
        public Builder(Context context) {
            this.context = context;
        }
        private OnEnsureClickListener onEnsureClickListener;

        public interface OnEnsureClickListener {
            void onEnsureClick();
        }
        public Builder setOnPositiveListener(OnEnsureClickListener onEnsureClickListener) {
            this.onEnsureClickListener = onEnsureClickListener;
            return this;
        }

        public AuraMatePCPopup create() {
            LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            final AuraMatePCPopup dialog = new AuraMatePCPopup(context, R.style.TransparentProgressDialog);
            View layout = commonCustomPopLayout(inflater, dialog);
            dialog.setContentView(layout);
            dialog.setCanceledOnTouchOutside(false);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            params.dimAmount = DIMMED_OPACITY;
            return dialog;
        }

        private View commonCustomPopLayout(LayoutInflater inflater, final AuraMatePCPopup dialog) {
            WindowManager.LayoutParams lp = dialog.getWindow().getAttributes();
            lp.dimAmount = 0.6f;
            dialog.getWindow().setAttributes(lp);
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            View layout = inflater.inflate(R.layout.dialog_auramate_pc, null, false);
            ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);
            dialog.addContentView(layout, params);
            ImageView backBtn = (ImageView) layout.findViewById(R.id.btn_back);
            TextView doneBtn = (TextView) layout.findViewById(R.id.btn_done);
            doneBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.cancel();
                    if (onEnsureClickListener != null) {
                        onEnsureClickListener.onEnsureClick();
                    }
                }
            });
            backBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.cancel();
                    if (onEnsureClickListener != null) {
                        onEnsureClickListener.onEnsureClick();
                    }
                }
            });
            return layout;
        }
    }
}
