package com.czur.cloud.ui.mirror.comm;

import static com.czur.czurutils.log.CZURLogUtilsKt.logE;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattDescriptor;
import android.bluetooth.BluetoothGattService;
import android.content.Context;
import android.graphics.Bitmap;
import android.os.Handler;
import android.os.Looper;

import com.blankj.utilcode.util.ActivityUtils;
import com.clj.fastble.BleManager;
import com.clj.fastble.callback.BleMtuChangedCallback;
import com.clj.fastble.callback.BleNotifyCallback;
import com.clj.fastble.callback.BleWriteCallback;
import com.clj.fastble.data.BleDevice;
import com.clj.fastble.exception.BleException;
import com.clj.fastble.utils.HexUtil;
import com.czur.cloud.R;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.SittingAuthEvent;
import com.czur.cloud.event.SittingAuthWriteEvent;
import com.czur.cloud.event.SittingCommonEvent;
import com.czur.cloud.event.SittingModelChangeEvent;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.mirror.model.SittingDeviceModel;
import com.czur.cloud.ui.mirror.model.SittingUpdateModel;
import com.czur.cloud.ui.mirror.mydialog.SittingStandarNoteDialog;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONObject;

import java.lang.reflect.Method;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.ArrayList;
import java.util.List;
import java.util.TimeZone;
import java.util.UUID;

public class FastBleOperationUtils {

    private static Boolean isShowAlertForSensitivity = true;
    private static BleDevice bleDevice;
    private static BluetoothGatt bleGatt;
    private static Handler mHandler = new Handler(Looper.getMainLooper());

    private static int newMaxMtuNumber = FastBleConstants.MAX_MTU_NUMBER;
    private static String retClassTypeName = "";   //仅用于显示而已
    private static String currentClassType = "";   //当前的接收数据的分类类型
    private static int currentDataLength = 0;      //当前获取的数据体的长度
    private static int countDataLength = 0;      //数据体的总长度
    private static String allReturnData = "";  //返回数据整体

    private static String reportNowData;    // 当前报告：0
    private static String reportHistData;   // 全部未传报告：1
    private static String reportAlgoJson;    // 算法学习更新json数据

    private static Bitmap bmpStandar;
    private static String bmpStandarName;
    private static Bitmap bmpHappy;
    private static String bmpHappyName;
    private static Bitmap bmpError;
    private static String bmpErrorName;
    private static Bitmap bmpStandarError;
    private static String bmpStandarErrorName;

    private static String localStandarPicturePath;

    private static boolean isExperience = false;    //是否正在体验模式 true：体验中；false：体验结束

    private static int sittingUpdateStatus = 0;     // 固件升级的app的UI状态记录;
    private static boolean isSittingUpdateSend = false; // 固件升级,再发送状态时，是否已经发送了升级包?
    private static boolean isChangeUpdateActivity = false;  // 是否已经进入到固件升级页面了？防止重复跳入该页面
    private static long seekFile = 0L;      // 续传的字节数
    private static boolean isDeviceUpdating = false;  // 是否已经在升级中？

    private static boolean isChangeConnectActivity = false;  // 是否已经进入到connect页面了？在该页面，主页的reconnect不需要重连；

    private static String newConnectDeviceMac="";   // 新绑定成功的设备的mac地址

    private static boolean isUpdateSend660 = false; //是app已经成功发送过660了，无需重发

    private static String lastClassType = "";    //当前发送的指令类型

    private static int deviceSilent = 1;  // 设备静音 设备端返回：0， 提醒静音；1，提醒非静音"

    private static boolean isAPPUpdateOK = true; //是app的升级包已经准备好了吗？

    // 报告的类型文件名列表，读出来，然后发送成功后删除
    private static List<String> offlineReportFileList = new ArrayList<>();

    public static List<String> getOfflineReportFileList() {        return offlineReportFileList;    }
    public static void setOfflineReportFileList(List<String> offlineReportFileList) {        FastBleOperationUtils.offlineReportFileList = offlineReportFileList;    }

    public static Boolean getIsShowAlertForSensitivity() {   return isShowAlertForSensitivity; }
    public static void setIsShowAlertForSensitivity(Boolean isShowAlertForSensitivity) { FastBleOperationUtils.isShowAlertForSensitivity = isShowAlertForSensitivity; }

    public static boolean isIsAPPUpdateOK() {        return isAPPUpdateOK;    }
    public static void setIsAPPUpdateOK(boolean isAPPUpdateOK) {        FastBleOperationUtils.isAPPUpdateOK = isAPPUpdateOK;    }

    public static int getDeviceSilent() {        return deviceSilent;    }
    public static void setDeviceSilent(int deviceSilent) {        FastBleOperationUtils.deviceSilent = deviceSilent; }

    public static boolean isIsUpdateSend660() {        return isUpdateSend660;    }
    public static void setIsUpdateSend660(boolean isUpdateSend660) {        FastBleOperationUtils.isUpdateSend660 = isUpdateSend660;    }

    public static String getNewConnectDeviceMac() {        return newConnectDeviceMac;    }
    public static void setNewConnectDeviceMac(String newConnectDeviceMac) {        FastBleOperationUtils.newConnectDeviceMac = newConnectDeviceMac;    }

    public static boolean isIsDeviceUpdating() {        return isDeviceUpdating;    }
    public static void setIsDeviceUpdating(boolean isDeviceUpdating) {        FastBleOperationUtils.isDeviceUpdating = isDeviceUpdating;    }

    public static boolean isIsChangeConnectActivity() {        return isChangeConnectActivity;    }
    public static void setIsChangeConnectActivity(boolean isChangeConnectActivity) {        FastBleOperationUtils.isChangeConnectActivity = isChangeConnectActivity;    }

    public static long getSeekFile() {        return seekFile;    }
    public static void setSeekFile(long seekFile) {        FastBleOperationUtils.seekFile = seekFile;    }

    public static boolean isIsChangeUpdateActivity() {        return isChangeUpdateActivity;    }
    public static void setIsChangeUpdateActivity(boolean isChangeUpdateActivity) {        FastBleOperationUtils.isChangeUpdateActivity = isChangeUpdateActivity;    }

    public static boolean isIsSittingUpdateSend() {        return isSittingUpdateSend;    }
    public static void setIsSittingUpdateSend(boolean isSittingUpdateSend) {        FastBleOperationUtils.isSittingUpdateSend = isSittingUpdateSend;    }

    public static int getSittingUpdateStatus() {        return sittingUpdateStatus;    }
    public static void setSittingUpdateStatus(int sittingUpdateStatus) {        FastBleOperationUtils.sittingUpdateStatus = sittingUpdateStatus;    }

    public static boolean isIsExperience() {        return isExperience;    }
    public static void setIsExperience(boolean isExperience) {        FastBleOperationUtils.isExperience = isExperience; }

    public static String getLocalStandarPicturePath() {        return localStandarPicturePath;    }

    public static void setLocalStandarPicturePath(String localStandarPicturePath) {        FastBleOperationUtils.localStandarPicturePath = localStandarPicturePath; }

    public static BleDevice getBleDevice() {        return bleDevice;    }
    public static void setBleDevice(BleDevice bleDevice) { FastBleOperationUtils.bleDevice = bleDevice; }

    public static BluetoothGatt getBluetoothGatt() {        return bleGatt;    }
    public static void setBluetoothGatt(BluetoothGatt bleGatt) { FastBleOperationUtils.bleGatt = bleGatt; }

    public static int getNewMaxMtuNumber() {        return newMaxMtuNumber;    }
    public static void setNewMaxMtuNumber(int newMaxMtuNumber) {        FastBleOperationUtils.newMaxMtuNumber = newMaxMtuNumber;    }

    public static String getRetClassTypeName() {        return retClassTypeName;    }
    public static void setRetClassTypeName(String retClassTypeName) {        FastBleOperationUtils.retClassTypeName = retClassTypeName;    }

    public static String getCurrentClassType() {        return currentClassType;    }
    public static void setCurrentClassType(String currentClassType) {        FastBleOperationUtils.currentClassType = currentClassType;    }

    public static int getCurrentDataLength() {        return currentDataLength;    }
    public static void setCurrentDataLength(int currentDataLength) {        FastBleOperationUtils.currentDataLength = currentDataLength;    }

    public static int getCountDataLength() {        return countDataLength;    }
    public static void setCountDataLength(int countDataLength) {        FastBleOperationUtils.countDataLength = countDataLength;    }

    public static String getAllReturnData() {        return allReturnData;    }
    public static void setAllReturnData(String allReturnData) {        FastBleOperationUtils.allReturnData = allReturnData;    }

    public static String getBmpStandarName() {        return bmpStandarName;    }
    public static void setBmpStandarName(String bmpStandarName) {        FastBleOperationUtils.bmpStandarName = bmpStandarName; }

    public static String getBmpHappyName() {        return bmpHappyName;    }
    public static void setBmpHappyName(String bmpHappyName) {        FastBleOperationUtils.bmpHappyName = bmpHappyName; }

    public static String getBmpErrorName() {        return bmpErrorName;    }
    public static void setBmpErrorName(String bmpErrorName) {        FastBleOperationUtils.bmpErrorName = bmpErrorName;    }

    public static String getBmpStandarErrorName() {        return bmpStandarErrorName;    }
    public static void setBmpStandarErrorName(String bmpStandarErrorName) {        FastBleOperationUtils.bmpStandarErrorName = bmpStandarErrorName;    }

    public static Bitmap getBmpError() {        return bmpError;    }

    public static void setBmpError(Bitmap bmpError) {        FastBleOperationUtils.bmpError = bmpError;    }

    public static Bitmap getBmpStandarError() {        return bmpStandarError;    }

    public static void setBmpStandarError(Bitmap bmpStandarError) {        FastBleOperationUtils.bmpStandarError = bmpStandarError; }

    public static Bitmap getBmpStandar() {        return bmpStandar;    }

    public static void setBmpStandar(Bitmap bmpStandar) {        FastBleOperationUtils.bmpStandar = bmpStandar;    }

    public static Bitmap getBmpHappy() {        return bmpHappy;    }

    public static void setBmpHappy(Bitmap bmpHappy) {        FastBleOperationUtils.bmpHappy = bmpHappy;    }

    /////////// ******** //////// ******** ///////// ******** ////////
    public static void closeBlooth(){
        logI("FastBleOperationUtils.closeBlooth");

        BleManager.getInstance().disableBluetooth();
        threadSleep(300);
        BleManager.getInstance().enableBluetooth();
    }

    public static void closeGatt(BluetoothGatt gatt, BleDevice bleDevice){

        if (gatt != null) {
            refreshDeviceCache(gatt);
            refreshGattCache(gatt);
            gatt.disconnect();
            threadSleep();
            gatt.close();
            gatt = null;
            setBluetoothGatt(gatt);
        }

        if (getBluetoothGatt() != null) {
            getBluetoothGatt().disconnect();
            threadSleep();
            getBluetoothGatt().close();
        }

        BleManager.getInstance().disconnect(bleDevice);
        BleManager.getInstance().disconnectAllDevice();
    }

    /**
     * 清理本地的BluetoothGatt 的缓存，以保证在蓝牙连接设备的时候，设备的服务、特征是最新的
     * @param gatt
     * @return
     */
    private static boolean refreshDeviceCache(BluetoothGatt gatt) {
        if(null != gatt){
            try {
                BluetoothGatt localBluetoothGatt = gatt;
                Method localMethod = localBluetoothGatt.getClass().getMethod( "refresh", new Class[0]);
                if (localMethod != null) {
                    boolean bool = ((Boolean) localMethod.invoke(
                            localBluetoothGatt, new Object[0])).booleanValue();
                    return bool;
                }
            } catch (Exception localException) {
                localException.printStackTrace();
            }
        }
        return false;
    }

    private static void refreshGattCache(BluetoothGatt gatt) {
        boolean result = false;
        try {
            if (gatt != null) {
                Method refresh = BluetoothGatt.class.getMethod("refresh");
                if (refresh != null) {
                    refresh.setAccessible(true);
                    result = (boolean) refresh.invoke(gatt, new Object[0]);
                }
            }
        } catch (Exception e) {
        }
    }


    public static boolean isMainThread() {
        return Looper.myLooper() == Looper.getMainLooper();
    }

    public static void runOnMainThread(Runnable runnable) {
        if (isMainThread()) {
            runnable.run();
        } else {
            if (mHandler != null) {
                mHandler.post(runnable);
            }
        }
    }

    //判断当前设备是否连接
    public static boolean isBleDeviceConnected(){
        return BleManager.getInstance().isConnected(getBleDevice());
    }

    // 获取ble设备的device name
    public static String getBelDeviceName(BleDevice bleDevice) {
        String ret="";
        if (isBelDeviceOK(bleDevice)){
            // 4. 获取设备UUID 和 设备名
            ret = FastBleOperationUtils.parseDeviceName(bleDevice.getScanRecord());
        }
        return ret;
    }

    // 获取ble设备的uuid
    public static String getBelDeviceUUID(BleDevice bleDevice) {
        String ret="";
        if (isBelDeviceOK(bleDevice)){
            // 4. 获取设备UUID 和 设备名
            byte[] devData = FastBleOperationUtils.parseBroadcastData(bleDevice.getScanRecord());
            String groupFourth = HexUtil.formatHexString(devData);
            ret = FastBleOperationUtils.parseAdvDataUUID(groupFourth);
        }
        return ret;
    }

    // scan的设备过滤，并获取设备的uuid
    public static boolean isBelDeviceOK(BleDevice bleDevice){
        // 1.获取广播数据信息
        byte[] scanRecord = bleDevice.getScanRecord();
        if (scanRecord == null){
            return false;
        }
        byte[] devData = FastBleOperationUtils.parseBroadcastData(scanRecord);
        if (devData == null){
            return false;
        }

        // 2.获取自定义信息数据[自定义数据长度为18位]
        String groupFourth = HexUtil.formatHexString(devData);
        if (groupFourth == null || groupFourth.length()<18*2){
            return false;
        }

        // 3.获取vid，pid进行设备过滤
        String vid = FastBleOperationUtils.parseAdvDataVID(groupFourth);
        if (vid == null || !vid.toLowerCase().equals(FastBleConstants.ADV_VID)){
            return false;
        }
        String pid = FastBleOperationUtils.parseAdvDataPID(groupFourth);
        if (pid == null || !pid.toLowerCase().equals(FastBleConstants.ADV_PID)){
            return false;
        }

//        // 4. 获取设备UUID 和 设备名
//        String devName = FastBleOperationUtils.parseDeviceName(scanRecord);
//        String uuid = FastBleOperationUtils.parseAdvDataUUID(groupFourth);

        return true;
    }

    // scan的设备过滤，并获取设备的uuid
    public static boolean isBelDeviceBind(BleDevice bleDevice){
        // 1.获取广播数据信息
        byte[] scanRecord = bleDevice.getScanRecord();

        byte[] devData = FastBleOperationUtils.parseBroadcastData(scanRecord);

        // 2.获取自定义信息数据[自定义数据长度为19位]
        String groupFourth = HexUtil.formatHexString(devData);

        // 3.  // 00：设备未绑定，01设备已绑定
        String discover = FastBleOperationUtils.parseAdvDataDiscover(groupFourth);
        if (discover != null && discover.equals("00")){
            return false;
        }
        return true;
    }

    //--长度02；类型01-LE 普通发现模式，数据02
    //02 01 02
    //--长度03；类型03-完整的 16 bit UUID 列表： TYPE = 0x03;，数据1019
    //03 03 1019
    //--长度：0c--12；类型09--设备简称： TYPE = 0x09；数据：435a5552204d6972726f72--CZUR Mirror
    //0c 09 435a5552204d6972726f72
    //--长度：14--20；类型：ff；数据：1b2e020208045401000006a5e4aeef22220100---【自定义的广播数据】
    //14 ff 1b2e020208045401000006a5e4aeef22220100

    // 1b2e020208045401000006a5e4aeef22220100
    //解析自定义的adv格式数据
    // adv字段定义：
    //字节索引	1,2	        4,5	        6~10	    11	            12~18       19
    //含义	    VID	        PID	        版本信息	    BT MAC长度（6）	BT MAC      0：设备未绑定，1设备已绑定
    //备注	    扫描过滤用	扫描过滤用	暂时无用		                鉴权安全用   设备发现用
    public static String parseAdvDataVID(String scanRecord){
        String ret = scanRecord.substring(0*2, 2*2);
        return ret;
    }
    public static String parseAdvDataPID(String scanRecord){
        String ret = scanRecord.substring(3*2, 3*2+2*2);
        return ret;
    }
    public static String parseAdvDataUUID(String scanRecord){
        int bLen = 11*2+6*2;
        if (scanRecord.length()<bLen)
            return "";

        String ret = scanRecord.substring(11*2, bLen);
        return ret;
    }
    public static String parseAdvDataDiscover(String scanRecord){
        int bLen = 18*2+2;
        if (scanRecord.length()<bLen)
            return "";

        String ret = scanRecord.substring(18*2, bLen);
        return ret;
    }

    // 获取广播中的设备名称
    public static String parseDeviceName(byte[] scanRecord) {
        String ret = null;
        if(null == scanRecord) {
            return ret;
        }

        ByteBuffer buffer = ByteBuffer.wrap(scanRecord).order(ByteOrder.LITTLE_ENDIAN);
        while (buffer.remaining() > 2) {
            byte length = buffer.get();
            if (length == 0)
                break;

            byte type = buffer.get();
            length -= 1;
            switch (type) {
                case 0x01: // Flags
                    buffer.get(); // flags
                    length--;
                    break;
                case 0x02: // Partial list of 16-bit UUIDs
                case 0x03: // Complete list of 16-bit UUIDs
                case 0x14: // List of 16-bit Service Solicitation UUIDs
                    while (length >= 2) {
                        buffer.getShort();
                        length -= 2;
                    }
                    break;
                case 0x04: // Partial list of 32 bit service UUIDs
                case 0x05: // Complete list of 32 bit service UUIDs
                    while (length >= 4) {
                        buffer.getInt();
                        length -= 4;
                    }
                    break;
                case 0x06: // Partial list of 128-bit UUIDs
                case 0x07: // Complete list of 128-bit UUIDs
                case 0x15: // List of 128-bit Service Solicitation UUIDs
                    while (length >= 16) {
                        long lsb = buffer.getLong();
                        long msb = buffer.getLong();
                        length -= 16;
                    }
                    break;
                case 0x08: // Short local device name
                case 0x09: // Complete local device name
                    byte sb[] = new byte[length];
                    buffer.get(sb, 0, length);
                    length = 0;
                    ret = new String(sb).trim();
                    return ret;

                case (byte) 0xFF: // Manufacturer Specific Data
                    buffer.getShort();
                    length -= 2;
                    break;

                default: // skip
                    break;
            }
            if (length > 0) {
                buffer.position(buffer.position() + length);
            }
        }
        return ret;
    }

    // 获取自定义广播数据
    public static byte[] parseBroadcastData(byte[] scanRecord) {
        byte[] ret = null;
        if(null == scanRecord) {
            return ret;
        }

        ByteBuffer buffer = ByteBuffer.wrap(scanRecord).order(ByteOrder.LITTLE_ENDIAN);
        while (buffer.remaining() > 2) {
            byte length = buffer.get();
            if (length == 0)
                break;

            byte type = buffer.get();
            length -= 1;
            switch (type) {
                case 0x01: // Flags
                    buffer.get(); // flags
                    length--;
                    break;
                case 0x02: // Partial list of 16-bit UUIDs
                case 0x03: // Complete list of 16-bit UUIDs
                case 0x14: // List of 16-bit Service Solicitation UUIDs
                    while (length >= 2) {
                        buffer.getShort();
                        length -= 2;
                    }
                    break;
                case 0x04: // Partial list of 32 bit service UUIDs
                case 0x05: // Complete list of 32 bit service UUIDs
                    while (length >= 4) {
                        buffer.getInt();
                        length -= 4;
                    }
                    break;
                case 0x06: // Partial list of 128-bit UUIDs
                case 0x07: // Complete list of 128-bit UUIDs
                case 0x15: // List of 128-bit Service Solicitation UUIDs
                    while (length >= 16) {
                        long lsb = buffer.getLong();
                        long msb = buffer.getLong();
                        length -= 16;
                    }
                    break;
                case 0x08: // Short local device name
                case 0x09: // Complete local device name
                    byte sb[] = new byte[length];
                    buffer.get(sb, 0, length);
                    length = 0;
                    break;

                case (byte) 0xFF: // Manufacturer Specific Data
                    byte sb1[] = new byte[length];
                    buffer.get(sb1, 0, length);
                    length = 0;
                    return sb1;

                default: // skip
                    break;
            }
            if (length > 0) {
                buffer.position(buffer.position() + length);
            }
        }
        return ret;
    }

    // 打开notify进行监听，接收数据
    public static void openNotify(BluetoothGatt bleGatt, String params){
        BluetoothGattService bleService = bleGatt.getService(UUID.fromString(FastBleConstants.exportUUID.toLowerCase()));
        BluetoothGattCharacteristic bleCharacteristicRead =
                bleService.getCharacteristic(UUID.fromString(FastBleConstants.readUUID.toLowerCase()));
        for(BluetoothGattDescriptor dp : bleCharacteristicRead.getDescriptors()){
            dp.setValue(BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE);
            bleGatt.writeDescriptor(dp);
            try {
                Thread.sleep(FastBleConstants.RUN_DELAY_TIMES300);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        threadSleep();
        BleManager.getInstance().notify(
                getBleDevice(),
                FastBleConstants.exportUUID,
                FastBleConstants.readUUID,
                new BleNotifyCallback() {
                    @Override
                    public void onNotifySuccess() {
                        // 打开通知操作成功
                        logI("FastBleOperationUtils.notify.onNotifySuccess=notify success");
                        runOnMainThread(new Runnable() {
                            @Override
                            public void run() {
                                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_OPEN_NOTIFY_SUCCESS, params));
                            }
                        });
                    }

                    @Override
                    public void onNotifyFailure(BleException exception) {
                        // 打开通知操作失败
                        logE("FastBleOperationUtils.notify.onNotifyFailure="+exception.toString());
                        runOnMainThread(new Runnable() {
                            @Override
                            public void run() {
                                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_OPEN_NOTIFY_FAIL, params));
                            }
                        });
                    }

                    @Override
                    public void onCharacteristicChanged(byte[] data) {
                        // 打开通知后，设备发过来的数据将在这里出现
                        // 对接收到的数据进行解析
                        runOnMainThread(new Runnable() {
                            @Override
                            public void run() {
                                onAnalysisData(data);
                            }
                        });

                    }
                });
    }

    private static void onAnalysisData(byte[] data){
        // 对接收到的数据进行解析
        String retStr = HexUtil.formatHexString(data);
        String head_type="";
        boolean classFlag=false;
        int body_start_index = FastBleConstants.HEAD_LENGTH_COUNT*2;
        if (retStr.length() > FastBleConstants.HEAD_LENGTH_COUNT * 2) {
            head_type = retStr.substring(8, 14);

            // Jason 2021-04-08
            // 针对个别类型指令，直接发送EventBus消息并返回，无需再次分析；
            String bodyStr="";
            String retClass ="";

            ///// 鉴权
            if ( head_type.equals(FastBleConstants.HEAD_AUTH_READ)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                if (bodyStr.length() > 5) {
                    retClass ="鉴权:APP端鉴权 OK,SN="+bodyStr;
                    EventBus.getDefault().post(new SittingAuthEvent(EventType.SITTING_AUTH_SUCCESS,bodyStr));
//                            SetDeviceParamsStr("0", FastBleConstants.HEAD_AUTH_BACK);
                }else{
                    retClass ="鉴权:APP端鉴权 NO.";
                    EventBus.getDefault().post(new SittingAuthEvent(EventType.SITTING_AUTH_FAIL,bodyStr));
                    SetDeviceParamsStr("1", FastBleConstants.HEAD_AUTH_BACK);
                }
                logI("onAnalysisData.retClass="+retClass + ";head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_AUTH_BACK)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                if (bodyStr.equals("0")){
                    retClass ="鉴权:授权返回 OK" + "=" + bodyStr ;
                }else{
                    retClass ="鉴权:授权返回 NO" + "=" + bodyStr ;
                }
                logI("onAnalysisData.retClass="+retClass +
                        ";head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_AUTH_ALREADY)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                if (bodyStr.equals("0")){
                    retClass ="鉴权:已绑定设备鉴权 OK" + "=" + bodyStr;
                }else{
                    retClass ="鉴权:已绑定设备鉴权 NO" + "=" + bodyStr;
                }
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_AUTH_CLEAR_OK)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                if (bodyStr.equals("0")){
                    retClass ="鉴权:前次绑定设备解绑完成 OK =" + bodyStr;
                    EventBus.getDefault().post(new SittingAuthEvent(EventType.SITTING_AUTH_CLEAR_SUCCESS,bodyStr));
                }else{
                    retClass ="鉴权:前次绑定设备解绑完成 NO=" + bodyStr;
                    EventBus.getDefault().post(new SittingAuthEvent(EventType.SITTING_AUTH_CLEAR_FAIL,bodyStr));
                }
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

                ////// 设定：1
            }else if(head_type.equals(FastBleConstants.HEAD_SETTING_VOICE_VOLUME)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                if (bodyStr.equals("0")) {
                    retClass = "设定:音频:音量 OK" + "=" + bodyStr;
                }else{
                    retClass = "设定:音频:音量 NO" + "=" + bodyStr;
                }
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_SETTING_VOICE_SWITCH)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                if (bodyStr.equals("0")){
                    retClass ="设定:音频:开关 OK" + "=" + bodyStr;
                }else{
                    retClass ="设定:音频:开关 NO" + "=" + bodyStr;
                }
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_SETTING_LASER_SWITCH)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                if (bodyStr.equals("0")){
                    retClass ="设定:激光灯:开关 OK" + "=" + bodyStr;
                }else{
                    retClass ="设定:激光灯:开关 NO" + "=" + bodyStr;
                }
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_SETTING_LASER_LIGHT)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                if (bodyStr.equals("0")){
                    retClass ="设定:激光灯:亮度 OK" + "=" + bodyStr;
                }else{
                    retClass ="设定:激光灯:亮度 NO" + "=" + bodyStr;
                }
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_SETTING_ALERT_SENSITIVITY)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                if (bodyStr.equals("0")){
                    retClass ="设定:提醒:灵敏度 OK" + "=" + bodyStr;
                }else{
                    retClass ="设定:提醒:灵敏度 NO" + "=" + bodyStr;
                }
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_SETTING_ALERT_SITTING)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                if (bodyStr.equals("0")){
                    retClass ="设定:提醒:坐姿开关 OK" + "=" + bodyStr;
                }else{
                    retClass ="设定:提醒:坐姿开关 NO" + "=" + bodyStr;
                }
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_SETTING_ALERT_SEDENTARY)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                if (bodyStr.equals("0")){
                    retClass ="设定:提醒:久坐开关 OK" + "=" + bodyStr;
                }else{
                    retClass ="设定:提醒:久坐开关 NO" + "=" + bodyStr;
                }
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_SETTING_ALERT_LONGSIT)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                if (bodyStr.equals("0")){
                    retClass ="设定:提醒:久坐时长 OK" + "=" + bodyStr ;
                }else{
                    retClass ="设定:提醒:久坐时长 NO" + "=" + bodyStr ;
                }
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_SETTING_UNBUND)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                if (bodyStr.equals("0")){
                    retClass ="设定:解绑:00 OK" + "=" + bodyStr;
                }else{
                    retClass ="设定:解绑:00 NO" + "=" + bodyStr;
                }
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_SETTING_ZONE)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                if (bodyStr.equals("0")){
                    retClass ="设定:语言时区:00 OK=" + bodyStr;
                    EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_TIMEZONE_SUCCESS,bodyStr));
                }else{
                    retClass ="设定:语言时区:00 NO=" + bodyStr;
                    EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_TIMEZONE_FAIL,bodyStr));
                }
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_SETTING_PARAM0)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                if (bodyStr.equals("0")){
                    retClass ="设定:参数设定:算法参数 OK" + "=" + bodyStr ;
                }else{
                    retClass ="设定:参数设定:算法参数 NO" + "=" + bodyStr ;
                }
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_SETTING_PARAM1)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                if (bodyStr.equals("0")){
                    retClass ="设定:参数设定:APP参数 OK" + "=" + bodyStr;
                }else{
                    retClass ="设定:参数设定:APP参数 NO" + "=" + bodyStr;
                }
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;


            }else if(head_type.equals(FastBleConstants.HEAD_SETTING_PARAM2)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                if (bodyStr.equals("0")){
                    retClass ="设定:参数设定:系统默认配置 OK" + "=" + bodyStr ;
                }else{
                    retClass ="设定:参数设定:系统默认配置 NO" + "=" + bodyStr ;
                }
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_SETTING_INPUT)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                if (bodyStr.equals("0")){
                    retClass ="设定:坐姿录入:00 OK" + "=" + bodyStr;
                }else{
                    retClass ="设定:坐姿录入:00 NO" + "=" + bodyStr;
                }
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_SETTING_NAME)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                if (bodyStr.equals("0")){
                    retClass ="设定:设备命名:00 OK" + "=" + bodyStr;
                }else{
                    retClass ="设定:设备命名:00 NO" + "=" + bodyStr;
                }
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_SETTING_EXPER)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                if (bodyStr.equals("0")){
                    retClass ="设定:坐姿体验状态:00 OK=" + bodyStr;
                }else{
                    retClass ="设定:坐姿体验状态:00 NO=" + bodyStr;
                }
                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_EXPER_STATUS,bodyStr));
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_SETTING_ADB)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                if (bodyStr.equals("0")){
                    retClass ="设定:adb后门:00 OK" + "=" + bodyStr;
                }else{
                    retClass ="设定:adb后门:00 NO" + "=" + bodyStr;
                }
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_SETTING_BURN)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                retClass ="设定:进入烧写模式:00" + "=" + bodyStr;
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_SETTING_MONITOR)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                retClass ="设定:设定坐姿监控模式:00" + "=" + bodyStr;
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_SETTING_SWITCH_ERROR)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                retClass = "设定:错误坐姿收集开关:00"+ "=" + bodyStr;
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_SETTING_SWITCH_HAPPY)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                retClass = "设定:开心图片收集开关:00"+ "=" + bodyStr;
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

                //////APP获取/设备端发送：2
            }else if(head_type.equals(FastBleConstants.HEAD_APPGET_STATUS)) {
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                retClass = "APP获取:坐姿监控模式:00" + "=" + bodyStr;
                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_APPGET_STATUS, bodyStr));
                logI("onAnalysisData.retClass=" + retClass,
                        "head_type="+head_type);
                return;

//                    }else if (head_type.equals(FastBleConstants.HEAD_APPGET_REPORT0)){
//                        retClass = "APP获取:报告:当前报告"+"="+bodyStr;
//                    }else if (head_type.equals(FastBleConstants.HEAD_APPGET_REPORT1)){
//                        retClass = "APP获取:报告:全部未传报告"+"="+bodyStr;
//                    }else if (head_type.equals(FastBleConstants.HEAD_APPGET_STANDER)){
//                        retClass = "APP获取:标准坐姿:00"+"="+bodyStr;
//                    }else if (head_type.equals(FastBleConstants.HEAD_APPGET_ERROR)){
//                        retClass = "APP获取:错误坐姿:00"+"="+bodyStr;
//                    }else if (head_type.equals(FastBleConstants.HEAD_APPGET_HAPPY)){
//                        retClass = "APP获取:最开心图:00"+"="+bodyStr;
//                    }else if (head_type.equals(FastBleConstants.HEAD_APPGET_STANDER_ERROR)){
//                        retClass = "APP获取:标准坐姿错误图:00"+"="+bodyStr;

            }else if (head_type.equals(FastBleConstants.HEAD_APPGET_REPORT_HISTORY)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                retClass = "APP获取:历史报告发送状态:00"+"="+bodyStr;
                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_APPGET_REPORT_HISTORY,bodyStr));
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if (head_type.equals(FastBleConstants.HEAD_APPGET_SILENT)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                retClass = "APP获取:静音状态:00"+"="+bodyStr;
                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_APPGET_SILENT,bodyStr));
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if (head_type.equals(FastBleConstants.HEAD_APPGET_ALGO)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                retClass = "APP获取:算法更新json:00"+"="+bodyStr;
                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_APPGET_ALGO,bodyStr));
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;


                ////// 设备端发送：3
            }else if (head_type.equals(FastBleConstants.HEAD_DEVSEND_STATU)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                retClass = "设备端发送:设备状态:00"+"="+bodyStr;
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

//                    }else if (head_type.equals(FastBleConstants.HEAD_DEVSEND_REPORT0)){
//                        retClass = "设备端发送:报告:当前报告"+"="+bodyStr;
//                    }else if (head_type.equals(FastBleConstants.HEAD_DEVSEND_REPORT1)){
//                        retClass = "设备端发送:报告:全部未传报告"+"="+bodyStr;

                ////// 设备反馈：4
            }else if(head_type.equals(FastBleConstants.HEAD_FEEDBACK_SETTING)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                retClass ="设备反馈:配置结果:00" + "=" + bodyStr ;
                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_FEEDBACK_SETTING,bodyStr));
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_FEEDBACK_INPUT)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                //"字符串：0，初次成功；1，重新录入成功；2，初次录入失败。"
                retClass ="设备反馈:坐姿录入结果:00" + "=" + bodyStr ;
                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_FEEDBACK_INPUT,bodyStr));
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_FEEDBACK_EXIT)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                retClass = "设备反馈:退出坐姿体验:00"+"="+bodyStr;
                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_EXPER_EXIT,bodyStr));
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_FEEDBACK_MTU)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                retClass = "设备反馈:MTU协商结果:00"+"="+bodyStr;

                int mtu_new = Integer.parseInt(bodyStr);
//                if (mtu_new > FastBleConstants.MAX_MTU_NUMBER) {
////                    EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_MTU_FAIL, ""));
//                    // 设置mtu失败，mtu=514了，需要断开ble，重连
//                    SittingDeviceModel currentModel = UserPreferences.getInstance().getSittingDeviceModel();
//                    EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_MTU_FAIL_RETRY, currentModel));
//                    logI("onAnalysisData.SITTING_MTU_FAIL_RETRY");
//
//                }else {
                    if (mtu_new > FastBleConstants.MAX_MTU_NUMBER) {
                        mtu_new = 512;
                    }
                    FastBleOperationUtils.setNewMaxMtuNumber(mtu_new);
                    BleManager.getInstance().setSplitWriteNum(mtu_new);
                    EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_FEEDBACK_MTU,String.valueOf(mtu_new)));
//                }

                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;
                //// 固件升级：6
            }else if(head_type.equals(FastBleConstants.HEAD_UPDATE_QUERY)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                //// 设备端返回：固件版本号字符串"
                retClass = "固件升级:固件版本查询:00"+"="+bodyStr;
                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_UPDATE_QUERY,bodyStr));
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_UPDATE_RESEND)){

                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                ////"设备端：续发文件偏移位置   //APP：固件偏移数据+MD5校验值"
                retClass = "固件升级:固件续发:00"+"="+bodyStr;
                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_UPDATE_RESEND,bodyStr));
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_UPDATE_RECV_OK)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                ////字符串：0，接收成功；1，接收数据发生错误	设备端必须保证数据接收结果被正确发送给APP一次
                retClass = "固件升级:固件接收完毕:00"+"="+bodyStr;
//                        if (bodyStr.equals("0")) {
//                            EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_UPDATE_RECV_OK, bodyStr));
//                        }
                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_UPDATE_RECV_OK650, bodyStr));
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_UPDATE_UPDATEING)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                //// "APP端：NULL    //设备端返回：0，开始升级；1，其他错误"
                retClass = "固件升级:设备开始升级:00"+"="+bodyStr;
                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_UPDATE_UPDATEING660,bodyStr));
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_UPDATE_RESULT)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                ////字符串：0，升级成功；1，升级发生错误
                retClass = "固件升级:设备升级结果:00"+"="+bodyStr;
                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_UPDATE_RESULT670,bodyStr));
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_UPDATE_RECV_SIZE)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                ////字符串：固件接收长度
                retClass = "固件升级:固件接收长度:00"+"="+bodyStr;
                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_UPDATE_RECV_SIZE,bodyStr));
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else if(head_type.equals(FastBleConstants.HEAD_UPDATE_CANCEL)){
                bodyStr = FastBleHexUtils.hexStr2Str(retStr.substring(body_start_index));
                ////字符串：固件升级取消
                retClass = "固件升级:固件升级取消:00"+"="+bodyStr;
                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_UPDATE_CANCEL,bodyStr));
                logI("onAnalysisData.retClass="+retClass,
                        "head_type="+head_type);
                return;

            }else{
                classFlag = FastBleOperationUtils.getClassType(head_type);
                currentClassType = FastBleOperationUtils.getCurrentClassType();
                retClassTypeName = FastBleOperationUtils.getRetClassTypeName();
                logI("onAnalysisData.retClass="+retClassTypeName,
                        "head_type="+currentClassType,
                        "data.lenght="+data.length);
            }

        }

        onAnalysisRetData(retStr);
    }

    // 对接收到的数据进行解析
    private static void onAnalysisRetData(String retStr) {
        boolean isReviceDataOver = true;
        String ret_body = "";
        String head_len = "";
        int i_head_len = 0;
        String head_type = "";
        boolean classFlag = false;
        boolean isSplitePackage = false;    // 当前是否为分包数据

        int body_start_index = FastBleConstants.HEAD_LENGTH_COUNT*2;
        if (retStr.length() > FastBleConstants.HEAD_LENGTH_COUNT * 2){
            // 1，获取数据长度
            try{
            head_len = retStr.substring(0, 8);
            i_head_len = Integer.valueOf(head_len, 16);   //d=255
            }catch(Exception e){
                i_head_len = 0;
            }

            // 2，获取123级分类
            head_type = retStr.substring(8, 14);
            classFlag = FastBleOperationUtils.getClassType(head_type);
            currentClassType = FastBleOperationUtils.getCurrentClassType();
            retClassTypeName = FastBleOperationUtils.getRetClassTypeName();

        }else {
            isSplitePackage = true;
        }

        // 3，获取数据，并解析数据，显示
        if (classFlag) {    // 分类类型正确
            countDataLength = i_head_len;
            allReturnData = "";
            currentDataLength = 0;
            if (countDataLength >= FastBleOperationUtils.getNewMaxMtuNumber()) {
                // 如果是分包数据，则从开始取数据
                isSplitePackage = true;
            }

        }else{
            body_start_index = 0;
            isSplitePackage = true;
        }

        String body = retStr.substring(body_start_index);
        int body_len = body.length()/2;
        if (!isSplitePackage){  // 不是分包
            currentDataLength = body_len;
            allReturnData = body;
            isReviceDataOver = true;
        }else{  //是分包
            currentDataLength = currentDataLength+body_len;
            allReturnData = allReturnData + body;
            isReviceDataOver = false;

            //  7+2=9,每包要少9个？  514-505=9
//            if (countDataLength - FastBleConstants.HEAD_LENGTH_COUNT-2 <= currentDataLength ) {
//                isReviceDataOver = true;
//            }
            if (countDataLength - FastBleConstants.HEAD_LENGTH_COUNT <= currentDataLength ) {
                isReviceDataOver = true;
            }

        }

        if (isReviceDataOver) {
            currentDataLength = 0;

            // 算法学习更新json数据
            if (currentClassType.equals(FastBleConstants.HEAD_APPGET_ALGO)){
                reportAlgoJson = FastBleHexUtils.hexStr2Str(allReturnData);
                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_APPGET_ALGO, reportAlgoJson));

                return;
            }

            // 坐姿报告数据接收-当前报告：0
            if (currentClassType.equals(FastBleConstants.HEAD_APPGET_REPORT0)){
                reportNowData = FastBleHexUtils.hexStr2Str(allReturnData);
                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_REPORT_NOW, reportNowData));

                return;
            }

            // 坐姿报告数据接收-全部未传报告：1
            if (currentClassType.equals(FastBleConstants.HEAD_APPGET_REPORT1)){
                reportHistData = FastBleHexUtils.hexStr2Str(allReturnData);
                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_REPORT_HISTORY, reportHistData));

                return;
            }

            ret_body = FastBleHexUtils.hexStr2Str(allReturnData);
        }
    }


    // 获取当前返回的数据的类别，让后分别处理
    // 设定系，原路返回，0：成功；其它：失败；
    public static boolean getClassType(String type){
        String retClass="";
        boolean flag = true;

        switch (type) {

            case FastBleConstants.HEAD_APPGET_REPORT0:
                retClass = "APP获取:报告:当前报告";
                break;
            case FastBleConstants.HEAD_APPGET_REPORT1:
                retClass = "APP获取:报告:全部未传报告";
                break;
            case FastBleConstants.HEAD_APPGET_STANDER:
                retClass = "APP获取:标准坐姿:00";
                break;
            case FastBleConstants.HEAD_APPGET_ERROR:
                retClass = "APP获取:错误坐姿:00";
                break;
            case FastBleConstants.HEAD_APPGET_HAPPY:
                retClass = "APP获取:最开心图:00";
                break;
            case FastBleConstants.HEAD_APPGET_STANDER_ERROR:
                retClass = "APP获取:标准坐姿错误图:00";
                break;

                ////////////////////////////
            case FastBleConstants.HEAD_DEVSEND_STATU:
                retClass = "设备端发送:设备状态:00";
                break;
            case FastBleConstants.HEAD_DEVSEND_REPORT0:
                retClass = "设备端发送:报告:当前报告";
                break;
            case FastBleConstants.HEAD_DEVSEND_REPORT1:
                retClass = "设备端发送:报告:全部未传报告";
                break;

                ////////////////////////////
            default:
                retClass = "Other:00:00";
                flag = false;
                break;
        }
        if (flag) {
            setCurrentClassType(type);
        }

        setRetClassTypeName(retClass);

        return flag;

    }

    // 打开ble提醒通知
    public static void enableNotification() {
        BluetoothGatt gatt = getBluetoothGatt();
        UUID serviceUUID = UUID.fromString(FastBleConstants.exportUUID);
        UUID characteristicUUID = UUID.fromString(FastBleConstants.readUUID);

        boolean success = false;
        BluetoothGattService service = gatt.getService(serviceUUID);

        if (service != null) {
            BluetoothGattCharacteristic characteristic = findNotifyCharacteristic(service, characteristicUUID);
            if (characteristic != null) {
                success = gatt.setCharacteristicNotification(characteristic, true);
                if (success) {
                    // 来源：http://stackoverflow.com/questions/38045294/oncharacteristicchanged-not-called-with-ble
                    for(BluetoothGattDescriptor dp: characteristic.getDescriptors()){
                        if (dp != null) {
                            if ((characteristic.getProperties() & BluetoothGattCharacteristic.PROPERTY_NOTIFY) != 0) {
                                dp.setValue(BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE);
                            } else if ((characteristic.getProperties() & BluetoothGattCharacteristic.PROPERTY_INDICATE) != 0) {
                                dp.setValue(BluetoothGattDescriptor.ENABLE_INDICATION_VALUE);
                            }
                            gatt.writeDescriptor(dp);
                        }
                        try {
                            Thread.sleep(FastBleConstants.RUN_DELAY_TIMES300);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
        }
    }

    private static BluetoothGattCharacteristic findNotifyCharacteristic(BluetoothGattService service, UUID characteristicUUID) {
        BluetoothGattCharacteristic characteristic = null;
        List<BluetoothGattCharacteristic> characteristics = service.getCharacteristics();
        for (BluetoothGattCharacteristic c : characteristics) {
            if ((c.getProperties() & BluetoothGattCharacteristic.PROPERTY_NOTIFY) != 0
                    && characteristicUUID.equals(c.getUuid())) {
                characteristic = c;
                break;
            }
        }
        if (characteristic != null)
            return characteristic;
        for (BluetoothGattCharacteristic c : characteristics) {
            if ((c.getProperties() & BluetoothGattCharacteristic.PROPERTY_INDICATE) != 0
                    && characteristicUUID.equals(c.getUuid())) {
                characteristic = c;
                break;
            }
        }
        return characteristic;
    }

    // ble设备参数数字写入
    public static void SetDeviceParams(int number, String type){
        String hexStr = FastBleHexUtils.str2HexStr(String.valueOf(number));
        int len = FastBleConstants.HEAD_LENGTH_COUNT + hexStr.length()/2;
        String sixteen = FastBleHexUtils.tenToHex(len);
        String sendData = sixteen + type + hexStr;

        logI("FastBleOperationUtils.SetDeviceParams.type="+type+";send number="+number);
//                "number="+number,
//                "hexStr="+hexStr,
//                "hexStr2Bytes(sendData)="+ Arrays.toString(FastBleHexUtils.hexStr2Bytes(sendData)));

        writeMessage(sendData);
    }

    // ble设备参数字符串写入
    public static void SetDeviceParamsStr(String str, String type){
        String hexStr = FastBleHexUtils.str2HexStr(str);

        lastClassType = type;
        int len = FastBleConstants.HEAD_LENGTH_COUNT + str.length();
        String sixteen = FastBleHexUtils.tenToHex(len);
        String sendData = sixteen + type + hexStr;

        logI("FastBleOperationUtils.SetDeviceParamsStr.type="+type,
                "send str="+str,
                "length="+len);
//                 "str="+str,
//                "hexStr="+hexStr,
//                "hexStr2Bytes(sendData)="+ Arrays.toString(FastBleHexUtils.hexStr2Bytes(sendData)));

        writeMessage(sendData);
    }

    // ble写入操作
    // `void write(BleDevice bleDevice,
    //                  String uuid_service,
    //                  String uuid_write,
    //                  byte[] data,
    //                  boolean split,
    //                  boolean sendNextWhenLastSuccess,
    //                  long intervalBetweenTwoPackage,
    //                  BleWriteCallback callback)`
    //Tips:
    //- 在没有扩大MTU及扩大MTU无效的情况下，当遇到超过20字节的长数据需要发送的时候，需要进行分包。参数`boolean split`表示是否使用分包发送；无`boolean split`参数的`write`方法默认对超过20字节的数据进行分包发送。
    //- 关于`onWriteSuccess`回调方法: `current`表示当前发送第几包数据，`total`表示本次总共多少包数据，`justWrite`表示刚刚发送成功的数据包。
    //- 对于分包发送的辅助策略，可以选择发送上一包数据成功之后发送下一包，或直接发送下一包，参数`sendNextWhenLastSuccess`表示是否待收到`onWriteSuccess`之后再进行下一包的发送。默认true。
    //- 参数`intervalBetweenTwoPackage`表示延时多长时间发送下一包，单位ms，默认0。
    private static void writeMessage(String msg){

        if (!BleManager.getInstance().isConnected(getBleDevice())){
            logI("FastBleOperationUtils.writeMessage==设备未连接");
            return;
        }

        threadSleep();
        //写入
        BleManager.getInstance().write(
                getBleDevice(),
                FastBleConstants.exportUUID,
                FastBleConstants.writeUUID,
                FastBleHexUtils.hexStr2Bytes(msg),
                new BleWriteCallback() {
                    @Override
                    public void onWriteSuccess(int current, int total, byte[] justWrite) {
                        // 发送数据到设备成功（分包发送的情况下，可以通过方法中返回的参数可以查看发送进度）
                        logI("FastBleOperationUtils.write.onWriteSuccess.current="+current,
                                "total="+total,
                                "size="+justWrite.length);
                    }

                    @Override
                    public void onWriteFailure(BleException exception) {
                        // 发送数据到设备失败
                        logE("FastBleOperationUtils.write.onWriteFailure="+exception.toString());
                        if (lastClassType.equals(FastBleConstants.HEAD_SETTING_ZONE)){
                            EventBus.getDefault().post(new SittingAuthWriteEvent(EventType.SITTING_TIMEZONE_FAIL));
                            lastClassType="";
                            return;
                        }
                        EventBus.getDefault().post(new SittingAuthWriteEvent(EventType.SITTING_AUTH_WRITE_FAIL));
                    }
                });
    }


    // 设置Mtu
    public static void setMtu(String params) {
        int mtu = FastBleConstants.MAX_MTU_NUMBER;
        setMtu(mtu, params);
    }

    public static void setMtu(int mtu, String params){
        BleDevice device = getBleDevice();
        threadSleep(200);
        BleManager.getInstance().setMtu(device, mtu, new BleMtuChangedCallback() {
            @Override
            public void onSetMTUFailure(BleException exception) {
                // 设置MTU失败
                logE("FastBleOperationUtils.onSetMTUFailure="+exception.toString());
                // 判断一下错误原因
                // code=102, description='This device is not connected!'
                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_MTU_FAIL, params));
            }

            @Override
            public void onMtuChanged(int mtu1) {
                // 设置MTU成功，并获得当前设备传输支持的MTU值
                logI("FastBleOperationUtils.onMtuChanged.mtu1="+mtu1);
                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_MTU_SUCCESS,params));
            }
        });
    }

    //延迟函数
    public static void threadSleep() {
        threadSleep(200);
    }

    public static void threadSleep(int time){
        try {
            Thread.sleep(time);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public static void threadSleepThenRun(int times){

        new Thread() {
            @Override
            public void run() {
                super.run();
                try {
                    Thread.sleep(times);//休眠3秒
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                /**
                 * 要执行的操作
                 */
            }
        }.start();
    }


    // 获取timezone，返回字符串
    public static String getTimeZone(Context context){
        TimeZone tz = TimeZone.getDefault();
        int mRawOffset = tz.getRawOffset() / 1000 / 60;
        int mDstSavings = tz.getDSTSavings();
        String lang = FastBleToolUtils.getCurrentLanguage(context);
        String dateTime= FastBleToolUtils.getCurrentSysTime();
        JSONObject object =new JSONObject();
        try {
            object.put("dateTime",dateTime);
            object.put("timeZone",mRawOffset);
            object.put("timeDst",mDstSavings);
            object.put("lang",lang);
        }catch (Exception e){
            //
        }
        return object.toString();
    }

    // 获取timezone，返回字符串
    public static String getTimeZoneNew(){
        TimeZone tz = TimeZone.getDefault();
        int mRawOffset = tz.getRawOffset() / 1000 / 60;
        int mDstSavings = tz.getDSTSavings();
        String lang = FastBleToolUtils.getCurrentLanguage(ActivityUtils.getTopActivity().getApplicationContext());
        String dateTime= FastBleToolUtils.getCurrentSysTime();
        JSONObject object =new JSONObject();
        try {
            object.put("dateTime",dateTime);
            object.put("timeZone",mRawOffset);
            object.put("timeDst",mDstSavings);
            object.put("lang",lang);
        }catch (Exception e){
            //
        }
        return object.toString();
    }

    // 根据数值获取下标，如： 45，--》在[30, 45, 60]中下标为：1
    public static int getValueInArrayIndex(int[] array, int value){
        int index = array.length;
        while (index-- > 0){
            if (array[index] == value){
                return index;
            }
        }

        return index;
    }

    // 设备离线
    public static void setOffline(){
        UserPreferences userPreferences = UserPreferences.getInstance();
        SittingDeviceModel model = userPreferences.getSittingDeviceModel();
        EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_OFFLINE, model));
    }

    public static SyncSittingReportEntity setStringToReportBean(String jsonStr){
        SyncSittingReportEntity entry;

        // 注意是否结尾处有重复的现象，需要去掉！
        int indexEndJson = jsonStr.indexOf("}");
        if (indexEndJson >0 && (indexEndJson < jsonStr.length()-1)){
            jsonStr = jsonStr.substring(0,indexEndJson+1);
        }

        Gson gs = new Gson();
        entry = gs.fromJson(jsonStr, SyncSittingReportEntity.class);//把JSON字符串转为对象

        return entry;
    }

    public static List<SyncSittingReportEntity> setStringToReportBeanList(String jsonStr){
        Gson gs = new Gson();

        List<SyncSittingReportEntity> jsonListObject = gs.fromJson(jsonStr, new TypeToken<List<SyncSittingReportEntity>>(){}.getType());//把JSON格式的字符串转为List
        for (SyncSittingReportEntity p : jsonListObject) {
            System.out.println("把JSON格式的字符串转为List///"+p.toString());
        }
        return jsonListObject;
    }

    /**
     * 显示对话框
     */
    public static void showNoteDialog(Context context) {
        SittingStandarNoteDialog dialog = new SittingStandarNoteDialog(context, R.style.sittingDialog,
                new SittingStandarNoteDialog.OncloseListener() {
                    @Override
                    public void onClick(boolean confirm) {
                    }
                });
        dialog.show();
    }

    public static void clearUpdateModel(){
        SittingUpdateModel model = new SittingUpdateModel();
        model.initSittingUpdateModel();
        UserPreferences.getInstance().setSittingUpdateModel(model);
        FastBleOperationUtils.setSittingUpdateStatus(0);
    }

    // 升级中，需要置灰设置项按钮 flag=true置灰；flag=false恢复
    public static void setSettingItemDisable(boolean flag) {
        FastBleOperationUtils.setIsDeviceUpdating(flag);

        SittingDeviceModel model = UserPreferences.getInstance().getSittingDeviceModel();
        model.setDeviceUpdating(flag);
        EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_DEVICE_UPDATING, model));

    }

    // 取消升级后复位操作
    public static void onUpdateCancelOperation() {
        // 升级状态复位
        FastBleOperationUtils.setSittingUpdateStatus(FastBleConstants.STATE_PREPARE);

        // 已取消状态
        SittingDeviceModel currentDeviceModel = UserPreferences.getInstance().getSittingDeviceModel();
        currentDeviceModel.setUpdateCancel(false);
        UserPreferences.getInstance().setSittingDeviceModel(currentDeviceModel);

        //升级完成，需要恢复置灰设置项按钮
        setSettingItemDisable(false);
    }


}
