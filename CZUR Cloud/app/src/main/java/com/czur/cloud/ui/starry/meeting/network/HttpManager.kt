package com.czur.cloud.ui.starry.meeting.network

import com.czur.cloud.BuildConfig

/**
 * Created by 陈丰尧 on 1/5/21
 */
object HttpManager {
    // ForKotlin
    inline fun <reified T> getService(BASE_URL:String= BuildConfig.BASE_STARRY_URL): T {
        return MiaoHttpManager.getInstance()
            .create(T::class.java, BASE_URL)
    }

    // ForJava,因为Java无法调用Kotlin的内联函数
    fun <T> getService(clazz: Class<T>): T {
        return MiaoHttpManager.getInstance()
            .create(clazz, BuildConfig.BASE_STARRY_URL)
    }
}