package com.czur.cloud.event;

import com.czur.cloud.netty.bean.ReceivedMsgBodyBean;

public class AuraMateReadyUpdateEvent extends BaseEvent {
    private String deviceUdid;
    private String dataBegin;

    private ReceivedMsgBodyBean.BodyBean.DataBean.DeviceStatusBean statusBean;

    public AuraMateReadyUpdateEvent(EventType eventType, String deviceUdid, ReceivedMsgBodyBean.BodyBean.DataBean.DeviceStatusBean statusBean) {
        super(eventType);
        this.deviceUdid = deviceUdid;
        this.statusBean = statusBean;
    }

    public AuraMateReadyUpdateEvent(EventType eventType, String deviceUdid, String dataBegin) {
        super(eventType);
        this.deviceUdid = deviceUdid;
        this.dataBegin = dataBegin;
    }

    public ReceivedMsgBodyBean.BodyBean.DataBean.DeviceStatusBean getStatusBean() {
        return statusBean;
    }

    public String getDataBegin() {
        return dataBegin;
    }

    public String getDeviceUdid() {
        return deviceUdid;
    }

    @Override
    public boolean match(Object obj) {
        return true;
    }
}
