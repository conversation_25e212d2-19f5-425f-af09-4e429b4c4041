package com.czur.cloud.event;

public class AuraMateCheckVideoEvent extends BaseEvent {
    private String deviceUdid;
    private int isRequestActive;


    public AuraMateCheckVideoEvent(EventType eventType, String deviceUdid,int isRequestActive) {
        super(eventType);
        this.deviceUdid = deviceUdid;
        this.isRequestActive = isRequestActive;
    }

    public String getDeviceUdid() {
        return deviceUdid;
    }

    @Override
    public boolean match(Object obj) {
        return true;
    }

    public int getIsRequestActive() {
        return isRequestActive;
    }

    public void setIsRequestActive(int isRequestActive) {
        this.isRequestActive = isRequestActive;
    }
}

