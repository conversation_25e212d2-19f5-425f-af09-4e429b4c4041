package com.czur.cloud.event.starry;


import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;

/**
 * 上线通知
 */
public class VideoEvent extends BaseEvent {
    private String video_chat_room_no;
    private String deviceUdid;
    private String isReadyForCalibrate;

    public VideoEvent(EventType eventType, String deviceUdid, String video_chat_room_no) {
        super(eventType);
        this.deviceUdid = deviceUdid;
        this.video_chat_room_no = video_chat_room_no;
    }
    public VideoEvent(EventType eventType, String deviceUdid, String video_chat_room_no,String isReadyForCalibrate) {
        super(eventType);
        this.deviceUdid = deviceUdid;
        this.video_chat_room_no = video_chat_room_no;
        this.isReadyForCalibrate=isReadyForCalibrate;
    }

    public String getIsReadyForCalibrate() {
        return isReadyForCalibrate;
    }

    public String getVideo_chat_room_no() {
        return video_chat_room_no;
    }

    public String getDeviceUdid() {
        return deviceUdid;
    }
    @Override
    public boolean match(Object obj) {
        return true;
    }
}
