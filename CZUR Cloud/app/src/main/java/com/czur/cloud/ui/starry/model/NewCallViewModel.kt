package com.czur.cloud.ui.starry.model

import android.app.Application
import android.content.Context
import android.content.res.AssetFileDescriptor
import android.media.AudioManager
import android.media.MediaPlayer
import androidx.lifecycle.AndroidViewModel
import com.czur.cloud.R
import com.czur.cloud.ui.starry.meeting.base.CZURAtyManager

/**
 * created by wangh 22.0419
 */


class NewCallViewModel(application: Application) : AndroidViewModel(application)  {
    companion object {
        private const val TAG = "NewCallViewModel"
    }
    // 音频管理
    private val audioManager by lazy {
        CZURAtyManager.appContext.getSystemService(Context.AUDIO_SERVICE) as AudioManager
    }
    private var hasPrepare = false
    private val mediaPlayer = MediaPlayer().apply {
        isLooping = true
        // 设置mediaPlayer为通话通道
        setAudioStreamType(AudioManager.STREAM_RING)
        setOnPreparedListener {
            it.start()
        }

        val afd: AssetFileDescriptor = getApplication<Application>()
            .resources.openRawResourceFd(R.raw.bright_60s)

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            setDataSource(afd)
        }

    }



    private var afChangeListener: AudioManager.OnAudioFocusChangeListener =
        AudioManager.OnAudioFocusChangeListener {
           /* when (it) {
                AudioManager.AUDIOFOCUS_GAIN -> mediaPlayer.setVolume(1F,1F)
                AudioManager.AUDIOFOCUS_LOSS,
                AudioManager.AUDIOFOCUS_LOSS_TRANSIENT,
                AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK
                -> mediaPlayer.setVolume(0.2F,0.2F)
            }*/
        }


    /**
     * 播放铃声
     */
    fun playRing() {
        if (!hasPrepare) {
            mediaPlayer.prepareAsync()
            hasPrepare = true
        }
        if (!mediaPlayer.isPlaying) {
            requestAudioFocus(
                streamType = AudioManager.STREAM_RING,
                focusListener = afChangeListener
            )
            mediaPlayer.start()
        }

    }

    /**
     * 请求音频焦点
     */
    fun requestAudioFocus(
        streamType: Int = AudioManager.STREAM_RING,
        durationHint: Int = AudioManager.AUDIOFOCUS_GAIN,
        focusListener: (AudioManager.OnAudioFocusChangeListener)? = null
    ) {
        val listener = focusListener ?: defAfChangeListener
        afChangeListener = listener

        val result = audioManager.requestAudioFocus(
            listener,
            streamType,
            durationHint
        )
        if (result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
        } else {
        }
    }

    private val defAfChangeListener by lazy {
        AudioManager.OnAudioFocusChangeListener {
        }
    }

    /**
     * 暂停铃声
     */
    fun pauseRing() {
        abandonAudioFocus()
        if (mediaPlayer.isPlaying) {
            mediaPlayer.pause()
        }
    }

    /**
     * 释放音频焦点
     */
    fun abandonAudioFocus(focusListener: (AudioManager.OnAudioFocusChangeListener)? = null) {
        val listener = focusListener ?: afChangeListener
        audioManager.abandonAudioFocus(listener)
    }

    override fun onCleared() {
        mediaPlayer.stop()
        mediaPlayer.release()
        abandonAudioFocus()
        super.onCleared()
    }
}