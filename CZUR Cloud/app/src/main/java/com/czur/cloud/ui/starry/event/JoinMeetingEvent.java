package com.czur.cloud.ui.starry.event;

import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.ui.starry.meeting.bean.Token;

public class JoinMeetingEvent {

	private String meetingRoomNo = "0";
	private Token token = null;

	public JoinMeetingEvent(String meetingRoomNo, Token token) {
		this.meetingRoomNo = meetingRoomNo;
		this.token = token;
	}

	public Token getToken() {
		return token;
	}

	public void setToken(Token token) {
		this.token = token;
	}

	public String getMeetingRoomNo() {
		return meetingRoomNo;
	}

	public void setMeetingRoomNo(String meetingRoomNo) {
		this.meetingRoomNo = meetingRoomNo;
	}
}
