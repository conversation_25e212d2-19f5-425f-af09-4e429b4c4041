package com.czur.cloud.ui.mirror.comm;

import static com.czur.czurutils.log.CZURLogUtilsKt.logE;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattDescriptor;
import android.bluetooth.BluetoothGattService;
import android.graphics.Bitmap;
import android.os.Handler;
import android.os.Looper;

import com.clj.fastble.BleManager;
import com.clj.fastble.callback.BleNotifyCallback;
import com.clj.fastble.exception.BleException;
import com.clj.fastble.utils.HexUtil;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.SittingCommonEvent;

import org.greenrobot.eventbus.EventBus;

import java.io.ByteArrayOutputStream;
import java.util.UUID;

public class FastBleOperationPictureErrorUtils {

    private static String retErrorPicClassTypeName = "";   //仅用于显示而已
    private static String currentErrorPicClassType = "";   //当前的接收数据的分类类型
    private static int currentDataLength = 0;      //当前获取的数据体的长度
    private static int countDataLength = 0;      //数据体的总长度
    private static int countPackageNum = 0;      //数据包的总数量
    private static boolean isSplitePackageData = false;     //是多包数据吗？
    private static byte[] allRetByteData;     //返回的字节数的全部
    private static ByteArrayOutputStream swapStream = new ByteArrayOutputStream();

    private static Handler mHandler = new Handler(Looper.getMainLooper());

    private static boolean isMainThread() {
        return Looper.myLooper() == Looper.getMainLooper();
    }

    private static void runOnMainThread(Runnable runnable) {
        if (isMainThread()) {
            runnable.run();
        } else {
            if (mHandler != null) {
                mHandler.post(runnable);
            }
        }
    }

    // 打开notify进行监听，接收数据
    public static void openNotifyForErrorPicture(BluetoothGatt bleGatt1){
        BluetoothGattService bleService = bleGatt1.getService(UUID.fromString(FastBleConstants.exportUUID.toLowerCase()));
        BluetoothGattCharacteristic bleCharacteristicReadPic1 =
                bleService.getCharacteristic(UUID.fromString(FastBleConstants.readErrPicUUID.toLowerCase()));
        for(BluetoothGattDescriptor dp : bleCharacteristicReadPic1.getDescriptors()){
            dp.setValue(BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE);
            bleGatt1.writeDescriptor(dp);
            try {
                Thread.sleep(FastBleConstants.RUN_DELAY_TIMES300);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        FastBleOperationUtils.threadSleep();
        BleManager.getInstance().notify(
                FastBleOperationUtils.getBleDevice(),
                FastBleConstants.exportUUID,
                FastBleConstants.readErrPicUUID,
                new BleNotifyCallback() {
                    @Override
                    public void onNotifySuccess() {
                        // 打开通知操作成功
                        logI("FastBleOperationPictureErrorUtils.notify.onNotifySuccess=notify success");
                        runOnMainThread(new Runnable() {
                            @Override
                            public void run() {
                                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_OPEN_NOTIFY_PICERR_SUCCESS, ""));
                            }
                        });
                    }

                    @Override
                    public void onNotifyFailure(BleException exception) {
                        // 打开通知操作失败
                        logE("FastBleOperationPictureErrorUtils.notify.onNotifyFailure="+exception.toString());
                        runOnMainThread(new Runnable() {
                            @Override
                            public void run() {
                                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_OPEN_NOTIFY_PICERR_FAIL, ""));
                            }
                        });
                    }

                    @Override
                    public void onCharacteristicChanged(byte[] data) {
                        // 打开通知后，设备发过来的数据将在这里出现
//                        String retStr = HexUtil.formatHexString(data);
                        // 对接收到的数据进行解析
                        runOnMainThread(new Runnable() {
                            @Override
                            public void run() {
                                onAnalysisRetDataByte(data);
                            }
                        });
                    }
                });
    }

    // 对接收到的数据进行解析
    private static void onAnalysisRetDataByte(byte[] data){
        boolean isReviceDataOver = true;
        int data_len = 0;

        if (!isSplitePackageData) {
            swapStream.reset();
            isSplitePackageData = true;
            isReviceDataOver = false;
            countPackageNum = 0;

            try {
                // 1，获取数据长度
                byte[] len_byte = FastBleHexUtils.subBytes(data, 0, 4);
                String head_len = HexUtil.formatHexString(len_byte);
                countDataLength = Integer.valueOf(head_len, 16);   //d=255
                // 2，获取123级分类
                byte[] type_byte = FastBleHexUtils.subBytes(data, 4, 3);
                String head_type = HexUtil.formatHexString(type_byte);
                boolean classFlag = getErrorPicClassType(head_type);

                if (classFlag){
                    allRetByteData = new byte[countDataLength];
                }
                data_len = data.length-FastBleConstants.HEAD_LENGTH_COUNT;
                byte[] ret_body = FastBleHexUtils.subBytes(data, FastBleConstants.HEAD_LENGTH_COUNT, data_len);
                swapStream.write(ret_body, 0, data_len);
                currentDataLength += data_len;

                if (countDataLength <= FastBleOperationUtils.getNewMaxMtuNumber()-FastBleConstants.HEAD_LENGTH_COUNT){
                    isReviceDataOver = true;
                }

            } catch (Exception e) {
                logE("FastBleOperationPictureErrorUtils.onAnalysisRetDataByte.error1="+e.toString());
                isSplitePackageData =false;
                countPackageNum = 0;
                currentDataLength = 0;
                swapStream.reset();
                return;
            }

        }else{
            isReviceDataOver = false;
            countPackageNum++;

            data_len = data.length;
            swapStream.write(data, 0, data_len);
            currentDataLength += data_len;

            if (data_len < FastBleOperationUtils.getNewMaxMtuNumber()){
                isReviceDataOver = true;
            }
            if (currentDataLength + FastBleConstants.HEAD_LENGTH_COUNT == countDataLength){
                isReviceDataOver = true;
            }
        }

        if (isReviceDataOver) {
            if (countDataLength == 0 || currentDataLength > countDataLength){
                isSplitePackageData =false;
                countPackageNum = 0;
                currentDataLength = 0;
                swapStream.reset();
                return;
            }

            isSplitePackageData =false;
            countPackageNum = 0;
            currentDataLength = 0;
            allRetByteData = swapStream.toByteArray();
            swapStream.reset();

            // 是否是图片接收？
            logI("FastBleOperationPictureErrorUtils.onAnalysisRetData="+retErrorPicClassTypeName+"("+currentErrorPicClassType+")图片接收完成！");
            if (allRetByteData.length < FastBleOperationUtils.getNewMaxMtuNumber()){
                String str = HexUtil.formatHexString(allRetByteData);
                String ret_body = FastBleHexUtils.hexStr2Str(str);

                return;
            }

            // 需要截取前14个字符，日期：20210319115000
            byte[] tmp = FastBleHexUtils.subBytes(allRetByteData,0, FastBleConstants.DATA_PRE_NUM);
            byte[] allRetByteDataTmp = FastBleHexUtils.subBytes(allRetByteData,
                    FastBleConstants.DATA_PRE_NUM,
                    allRetByteData.length-FastBleConstants.DATA_PRE_NUM);
            String str = HexUtil.formatHexString(tmp);
            String filenameDate = FastBleHexUtils.hexStr2Str(str);
            logI("FastBleOperationPictureErrorUtils.onAnalysisRetData.filenameDate="+filenameDate);

            // 收到图片了，需要传送回去，通过event
            if (currentErrorPicClassType.equals(FastBleConstants.HEAD_APPGET_STANDER) ) {
                //

            }else if (currentErrorPicClassType.equals(FastBleConstants.HEAD_APPGET_ERROR) ) {
                byte[] byteMD5Error = FastBleHexUtils.subBytes(allRetByteDataTmp,
                        allRetByteDataTmp.length-FastBleConstants.DATA_MD5_NUM,
                        FastBleConstants.DATA_MD5_NUM);
                String strMD5ErrorHex = HexUtil.formatHexString(byteMD5Error);
                String strMD5Error = FastBleHexUtils.hex2Str(strMD5ErrorHex);

                byte[] byteErrorBmp = FastBleHexUtils.subBytes(allRetByteData,
                        0,
                        allRetByteData.length-FastBleConstants.DATA_MD5_NUM);
                String strMD5ErrorBmp = FastBleMD5Utils.getMD5String(byteErrorBmp);

                if (strMD5Error.equals(strMD5ErrorBmp)) {
                    Bitmap bmp = FastBleHexUtils.getBitmapFromByte(allRetByteDataTmp);
                    FastBleOperationUtils.setBmpError(bmp);
                    FastBleOperationUtils.setBmpErrorName(filenameDate);
                    if (bmp != null) {
                        // 接收错误坐姿图片，但是不处理了
//                        EventBus.getDefault().post(new SittingPictureEvent(EventType.SITTING_PICTURE_ERROR, bmp, filenameDate));
                    } else {
                        logI("FastBleOperationPictureErrorUtils.onAnalysisRetData.SITTING_PICTURE_ERROR.getBitmapFromByte is NULL");
                    }
                }else{
                    // 校验失败
                }

            }else if (currentErrorPicClassType.equals(FastBleConstants.HEAD_APPGET_HAPPY) ) {
                //

            }
        }
    }

    // 获取当前返回的数据的类别，让后分别处理
    // 设定系，原路返回，0：成功；其它：失败；
    public static boolean getErrorPicClassType(String type){
        String retClass="";
        boolean flag = true;

        switch (type) {
//            case FastBleConstants.HEAD_APPGET_STATUS:
//                retClass = "APP获取:设备状态:00";
//                break;
//            case FastBleConstants.HEAD_APPGET_REPORT0:
//                retClass = "APP获取:报告:当前报告";
//                break;
//            case FastBleConstants.HEAD_APPGET_REPORT1:
//                retClass = "APP获取:报告:全部未传报告";
//                break;
            case FastBleConstants.HEAD_APPGET_STANDER:
                retClass = "APP获取:标准坐姿:00";
                break;
            case FastBleConstants.HEAD_APPGET_ERROR:
                retClass = "APP获取:错误坐姿:00";
                break;
            case FastBleConstants.HEAD_APPGET_HAPPY:
                retClass = "APP获取:最开心图:00";
                break;
            case FastBleConstants.HEAD_APPGET_STANDER_ERROR:
                retClass = "APP获取:标准坐姿错误图:00";
                break;
//            case FastBleConstants.HEAD_APPGET_REPORT_HISTORY:
//                retClass = "APP获取:历史报告发送状态:00";
//                break;

            ////////////////////////////

            default:
                retClass = "Other:00:00";
                flag = false;
                break;
        }
        if (flag) {
            currentErrorPicClassType=type;
        }

        retErrorPicClassTypeName = retClass;

        return flag;

    }
}
