package com.czur.cloud.ui.et;

import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;

import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.event.CropSuccessEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.model.CropModel;
import com.czur.cloud.model.EtPreviewModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.cropper.CropImage;
import com.czur.cloud.ui.component.cropper.CropImageView;
import com.facebook.common.executors.CallerThreadExecutor;
import com.facebook.common.references.CloseableReference;
import com.facebook.datasource.DataSource;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.imagepipeline.core.ImagePipeline;
import com.facebook.imagepipeline.datasource.BaseBitmapDataSubscriber;
import com.facebook.imagepipeline.image.CloseableImage;
import com.facebook.imagepipeline.request.ImageRequest;
import com.facebook.imagepipeline.request.ImageRequestBuilder;

import org.greenrobot.eventbus.EventBus;

import java.util.Calendar;

public class EtCropImageActivity extends BaseActivity implements View.OnClickListener {

    public static final int CANNOT_CUT_FILE = 1036;
    public static final int BAD_JPEG_IMG = 1037;
    public static final int MIN_CLICK_DELAY_TIME = 3000;
    private long lastClickTime = 0;
    private boolean isRun = false;
    private CropImageView cropImageView;
    private EtPreviewModel.FileListBean entity;
    private int x, y;
    private int width;
    private int height;
    private Bitmap originalBitmap;
    private int rotated;
    private String currentMode;
    private int position;
    private boolean isFolder;
    private boolean hasBig;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.black_2a);
        BarUtils.setNavBarColor(this, getColor(R.color.black_2a));
        BarUtils.setStatusBarLightMode(this, false);
        setContentView(R.layout.activity_crop_image);
        entity = (EtPreviewModel.FileListBean) getIntent().getSerializableExtra("image");
        currentMode = getIntent().getStringExtra("mode");
        isFolder = getIntent().getBooleanExtra("isFolder", false);
        position = getIntent().getIntExtra("position", 0);
        hasBig = getIntent().getBooleanExtra("hasBig", false);
        findViewById(R.id.img_back).setOnClickListener(this);
        findViewById(R.id.btn_layout_turn_right).setOnClickListener(this);
        findViewById(R.id.crop_btn).setOnClickListener(this);
        cropImageView = (CropImageView) findViewById(R.id.cropImageView);

        final ImageRequest imageRequest = ImageRequestBuilder.newBuilderWithSource(Uri.parse(hasBig ? entity.getBig() : entity.getMiddle())).build();
        final ImagePipeline imagePipeline = Fresco.getImagePipeline();
        DataSource<CloseableReference<CloseableImage>> dataSource = imagePipeline.fetchDecodedImage(imageRequest, this);
        dataSource.subscribe(new BaseBitmapDataSubscriber() {
            @Override
            protected void onNewResultImpl(final Bitmap bitmap) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        originalBitmap = bitmap;
                        cropImageView.setImageBitmap(originalBitmap);
                    }
                });
            }

            @Override
            protected void onFailureImpl(DataSource<CloseableReference<CloseableImage>> dataSource) {
            }
        }, CallerThreadExecutor.getInstance());
    }

    @Override
    public void onClick(final View v) {
        switch (v.getId()) {
            case R.id.img_back:
                finish();
                break;
            case R.id.btn_layout_turn_right:
                rotated = rotated == 360 ? 90 : rotated + 90;
                cropImageView.setRotatedDegrees(rotated);
                break;
            case R.id.crop_btn:
                long currentTime = Calendar.getInstance().getTimeInMillis();
                if (currentTime - lastClickTime > MIN_CLICK_DELAY_TIME && !isRun) {
                    lastClickTime = currentTime;
                    cropImage();
                }
                break;
        }
    }

    private void cropImage() {
        isRun = false;
        Bitmap cropped = cropImageView.getCroppedImage();
        float[] points = cropImageView.getCropPoints();
        int originalWidth = originalBitmap.getWidth();
        int originalHeight = originalBitmap.getHeight();
        switch (rotated) {
            case 0:
                x = (int) points[0];
                y = (int) points[1];
                width = cropped.getWidth();
                height = cropped.getHeight();
                break;
            case 90:
                x = (int) points[2];
                y = (int) points[3];
                width = cropped.getHeight();
                height = cropped.getWidth();
                break;
            case 180:
                x = (int) points[4];
                y = (int) points[5];
                width = cropped.getWidth();
                height = cropped.getHeight();
                break;
            case 270:
                x = (int) points[6];
                y = (int) points[7];
                width = cropped.getHeight();
                height = cropped.getWidth();
                break;
            case 360:
                x = (int) points[0];
                y = (int) points[1];
                width = cropped.getWidth();
                height = cropped.getHeight();
                break;
            default:
                break;
        }

        HttpManager.getInstance().request().mCrop(UserPreferences.getInstance(this).getUserId(), entity.getId(),
                "flatten", currentMode, originalWidth + CZURConstants.EMPTY, originalHeight + CZURConstants.EMPTY, rotated + CZURConstants.EMPTY,
                x + CZURConstants.EMPTY, y + CZURConstants.EMPTY, width + CZURConstants.EMPTY, height + CZURConstants.EMPTY,
                CropModel.class, new MiaoHttpManager.Callback<CropModel>() {
                    @Override
                    public void onStart() {
                        showProgressDialog(true, false);
                        isRun = true;
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<CropModel> entity) {
                        hideProgressDialog();
                        isRun = false;
                        CropModel cropEntity = entity.getBody();
                        Intent intent = new Intent();
                        intent.putExtra("cropEntity", cropEntity);
                        EventBus.getDefault().post(new CropSuccessEvent(EventType.CROP_SUCCESS, position, isFolder, cropEntity));
                        setResult(CropImage.CROP_IMAGE_ACTIVITY_REQUEST_CODE, intent);
                        finish();
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<CropModel> entity) {
                        isRun = false;
                        hideProgressDialog();
                        if (entity.getCode() == CANNOT_CUT_FILE) {
                            showMessage(R.string.crop_image_failure);

                        } else if (entity.getCode() == BAD_JPEG_IMG) {
                            showMessage(R.string.crop_image_failure);

                        }
                    }

                    @Override
                    public void onError(Exception e) {
                        isRun = false;
                        hideProgressDialog();
                        showMessage(R.string.request_server_error);
                    }
                });
    }

    @Override
    public void onBackPressed() {
        if (!isRun) {
            super.onBackPressed();
        }
    }

}
