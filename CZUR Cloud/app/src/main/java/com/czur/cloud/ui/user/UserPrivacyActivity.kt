package com.czur.cloud.ui.user

import android.content.Intent
import android.os.Bundle
import android.view.View
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.czur.cloud.BuildConfig
import com.czur.cloud.R
import com.czur.cloud.common.CZURConstants
import com.czur.cloud.ui.market.WebViewActivity
import com.czur.cloud.ui.starry.base.StarryBaseActivity
import com.czur.cloud.ui.starry.utils.singleClick
import kotlinx.android.synthetic.main.layout_user_top_bar.*
import kotlinx.android.synthetic.main.user_privacy.*
import java.util.*

/**
 * 隐私协议页面
 */
class UserPrivacyActivity : StarryBaseActivity(), View.OnClickListener {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.white)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.user_privacy)
        initComponent()
    }

    private fun initComponent() {
        user_title?.setText(R.string.user_agreement_privacy_policy)
        user_back_btn?.setOnClickListener(this)
        user_logout_btn?.singleClick(this)
        user_menu_privacy_rl?.singleClick(this)
        user_menu_privacy_rl_privacy?.singleClick(this)
        user_menu_privacy_rl_info?.singleClick(this)
        user_menu_privacy_rl_share?.singleClick(this)

        if (BuildConfig.IS_OVERSEAS){
            user_menu_privacy_rl_info?.visibility = View.GONE
            user_menu_privacy_rl_share?.visibility = View.GONE

            user_title?.textSize = 15f

            // 海外暂时不要注销功能，下个版本上
//            user_logout_btn?.visibility = View.GONE
        }
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.user_back_btn -> ActivityUtils.finishActivity(this)

            R.id.user_menu_privacy_rl -> {
                val intent = Intent( this, WebViewActivity::class.java )
                intent.putExtra("title", getString(R.string.user_privacy_user))
                val cTime = Calendar.getInstance().timeInMillis
//                intent.putExtra("url", CZURConstants.PRIVACY_AGREEMENT1 + cTime + "")
                intent.putExtra("url", BuildConfig.TERMS_URL);
                ActivityUtils.startActivity(intent)
            }

            R.id.user_menu_privacy_rl_privacy -> {
                val intent = Intent( this, WebViewActivity::class.java )
                intent.putExtra("title", getString(R.string.user_privacy_privacy))
                val cTime = Calendar.getInstance().timeInMillis
//                intent.putExtra("url", CZURConstants.PRIVACY_AGREEMENT2 + cTime + "")
                //                intent2.putExtra("url", CZURConstants.PRIVACY_AGREEMENT + cTime2 + "");
                intent.putExtra("url", BuildConfig.PRIVACY_AGREEMENT)
                ActivityUtils.startActivity(intent)
            }

            R.id.user_menu_privacy_rl_info -> {
                val intent = Intent( this, WebViewActivity::class.java )
                intent.putExtra("title", getString(R.string.user_privacy_info))
                val cTime = Calendar.getInstance().timeInMillis
                intent.putExtra("url", CZURConstants.PRIVACY_AGREEMENT3 + cTime + "")
                ActivityUtils.startActivity(intent)
            }

            R.id.user_menu_privacy_rl_share -> {
                val intent = Intent( this, WebViewActivity::class.java )
                intent.putExtra("title", getString(R.string.user_privacy_share))
                val cTime = Calendar.getInstance().timeInMillis
                intent.putExtra("url", CZURConstants.PRIVACY_AGREEMENT4 + cTime + "")
                ActivityUtils.startActivity(intent)
            }

            R.id.user_logout_btn -> {
                ActivityUtils.startActivity(UserRemoveAccountActivity::class.java)
            }
            else -> {
            }
        }
    }

}