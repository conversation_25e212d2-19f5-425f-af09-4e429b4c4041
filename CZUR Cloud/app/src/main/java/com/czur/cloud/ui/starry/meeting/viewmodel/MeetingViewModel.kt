package com.czur.cloud.ui.starry.meeting.viewmodel

//import io.agora.rtc.ss.ScreenSharingClient
import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Looper
import android.os.SystemClock
import android.view.View
import android.widget.LinearLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import com.blankj.utilcode.util.NetworkUtils
import com.blankj.utilcode.util.ServiceUtils
import com.blankj.utilcode.util.TimeUtils
import com.blankj.utilcode.util.ToastUtils
import com.czur.cloud.BuildConfig
import com.czur.cloud.R
import com.czur.cloud.event.EventType
import com.czur.cloud.event.StarryCommonEvent
import com.czur.cloud.netty.observer.NettyService
import com.czur.cloud.netty.observer.NettyUtils
import com.czur.cloud.preferences.StarryPreferences
import com.czur.cloud.preferences.UserPreferences
import com.czur.cloud.ui.base.CzurCloudApplication
import com.czur.cloud.ui.eshare.ESPermissionUtils
import com.czur.cloud.ui.eshare.EShareActivity
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.event.ServiceJoiningMeetingRoomEvent
import com.czur.cloud.ui.starry.livedatabus.LiveDataBus
import com.czur.cloud.ui.starry.meeting.agora.AgoraManager
import com.czur.cloud.ui.starry.meeting.agora.AgoraManager.Companion.JOIN_CHANNEL_SUCCESS
import com.czur.cloud.ui.starry.meeting.agora.DisplayManager
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.meeting.bean.Token
import com.czur.cloud.ui.starry.meeting.bean.UserStatus.STATUS_IOS_HOLD
import com.czur.cloud.ui.starry.meeting.bean.UserStatus.STATUS_JOINED
import com.czur.cloud.ui.starry.meeting.bean.UserStatus.STATUS_OFFLINE
import com.czur.cloud.ui.starry.meeting.bean.vo.DisplayData
import com.czur.cloud.ui.starry.meeting.bean.vo.InitParam
import com.czur.cloud.ui.starry.meeting.bean.vo.Member
import com.czur.cloud.ui.starry.meeting.common.CAM_AUDIO_STATUS_NO
import com.czur.cloud.ui.starry.meeting.common.CAM_AUDIO_STATUS_OPEN
import com.czur.cloud.ui.starry.meeting.common.JOIN_MEETING_TIMEOUT
import com.czur.cloud.ui.starry.meeting.common.JOIN_MEETING_TIMEOUT_OVERSEA
import com.czur.cloud.ui.starry.meeting.common.MeetingCMD
import com.czur.cloud.ui.starry.meeting.common.MeetingHandler
import com.czur.cloud.ui.starry.meeting.common.MeetingStatus
import com.czur.cloud.ui.starry.meeting.common.MsgProcessor
import com.czur.cloud.ui.starry.meeting.common.SMART_FOCUS_DEF
import com.czur.cloud.ui.starry.meeting.common.StreamType
import com.czur.cloud.ui.starry.meeting.common.UserHandler
import com.czur.cloud.ui.starry.meeting.model.MeetingModel
import com.czur.cloud.ui.starry.meeting.model.ModelManager
import com.czur.cloud.ui.starry.meeting.screensharing.MediaProjectFgService
import com.czur.cloud.ui.starry.meeting.screensharing.RefusedPermissionListener
import com.czur.cloud.ui.starry.meeting.viewmodel.action.DisplayDataActionImpl
import com.czur.cloud.ui.starry.meeting.viewmodel.action.IDisplayDataAction
import com.czur.cloud.ui.starry.meeting.viewmodel.action.IMeetingAction
import com.czur.cloud.ui.starry.meeting.viewmodel.action.IMemberControlAction
import com.czur.cloud.ui.starry.meeting.viewmodel.action.MeetingActionImpl
import com.czur.cloud.ui.starry.meeting.viewmodel.action.MemberControlActionImpl
import com.czur.cloud.ui.starry.model.StarryCallInModel
import com.czur.cloud.ui.starry.model.StarryUserInfoModel
import com.czur.cloud.ui.starry.utils.Tools
import com.czur.czurutils.log.logD
import com.czur.czurutils.log.logE
import com.czur.czurutils.log.logI
import io.agora.rtc2.Constants
import io.agora.rtc2.Constants.VIDEO_STREAM_HIGH
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import java.util.Date
import java.util.Timer
import java.util.TimerTask
import kotlin.collections.set

class MeetingViewModel : ViewModel(),
    IMemberControlAction by MemberControlActionImpl(),  // 成员管理
    IDisplayDataAction by DisplayDataActionImpl(),      // 显示管理
    IMeetingAction by MeetingActionImpl() {             // 会议属性信息

    companion object {
        private const val TAG = "MeetingViewModel"
    }

    // 声网Manager
    private var agoraManager: AgoraManager = AgoraManager(
        ::onRemoteUserOnLine,
        ::onRemoteUserOffLine
    )

    // 网络异常标识  true:异常；false：正常
    var networkErrorStatus = MutableLiveData(false)

    // 是否可以显示会议画面
    val showMeeting = MutableLiveData(false)

    // 聊天是否可用
    val chatEnable = MutableLiveData(false)

    // 当前activeSpeaker的人员；
    var mCurrentActiveSpeakerUid = ""

    // 存放所有的UID和 对应的SurfaceView
    // 在 Agora SDK 中一个远程视频的显示只和该用户的 UID 有关，
    // 所以使用的数据源只需要简单定义为包含 UID 和对应的 SurfaceView 即可，
    val displayUids = MutableLiveData<List<DisplayData>>()
    val displayJoinedUids = MutableLiveData<List<DisplayData>>()    //已经加入的

    // displayUidsMap在找成员的时候快一些
    private val displayUidsMap = HashMap<String, DisplayData>()
    private val displayUidsJoinedMap = HashMap<String, DisplayData>()

    // 存储另一套surface的视频流，为滑动屏幕切换替换使用
    // 在收到userlist时候增加赋值，在user离线时去掉uid值，在onclear时清空
    val displayDataJoinedMapMain = HashMap<String, DisplayData>()

    var displayUidsMain = listOf<DisplayData>()
    var displayUidsMainLive = MutableLiveData<List<DisplayData>>()
    var displayUidsGrid1 = listOf<DisplayData>()
    var displayUidsGrid1Live = MutableLiveData<List<DisplayData>>()
    var displayUidsGrid2 = listOf<DisplayData>()
    var displayUidsGrid2Live = MutableLiveData<List<DisplayData>>()
    var displayUidsGrid3 = listOf<DisplayData>()
    var displayUidsGrid3Live = MutableLiveData<List<DisplayData>>()
    var displayUidsGrid4 = listOf<DisplayData>()
    var displayUidsGrid4Live = MutableLiveData<List<DisplayData>>()
    var displayUidsGrid5 = listOf<DisplayData>()
    var displayUidsGrid5Live = MutableLiveData<List<DisplayData>>()

    var displayMainUid = MutableLiveData<String>()

    // 双击跳转到主页面
    val doubleClickUid = MutableLiveData<String>()

    // 保存声网返回的加入会议的uid的顺序
    private val uidFromAgoraList = arrayListOf<String>()
    val uidFromAgoraListLive = MutableLiveData<List<String>>()

    // 当前活动的人员
    val activeSpeaker = MutableLiveData<String?>()

    // NetworkQuality
    val networkQuality = MutableLiveData<Int>()           // 自己的网络质量
    val selfNetworkTxQuality = MutableLiveData<Int>()           // 自己的网络质量
    val selfNetworkRxQuality = MutableLiveData<Int>()           // 自己的网络质量

    // main lock
    val isMainLock = MutableLiveData<Boolean>(SMART_FOCUS_DEF)

    // 数据列表,数据来源是长连接
    val memberList: LiveData<List<Member>> = ModelManager.membersModel.memberListLive

    // 正在更新数据，防止重复！
    private var isUpdateFlag = false

    //声网的智能检测发言人是否工作过
    private var agoraActiveSpeakerWorkStatus = false

    //正在开始分享(申请权限弹窗弹出),为了避免MeetingMainAty 的onresume刷新memberlist,与分享cmd的memberlist冲突
    var isStartingShare = false

    // 加入的成员个数
    val joinedMemberCount: Int
        get() = ModelManager.membersModel.memberList.count {
            it.isInMeeting
        }

    // 停止分享dialog是否正在显示
    var isStopShareDialogShowing = MutableLiveData(false)

    // 是否要退出
    var finish = MutableLiveData(false)

    // 自己的视频状态
    var selfVideoInUse: MutableLiveData<Boolean> = ModelManager.membersModel.selfVideoInUseLive

    // 自己的音频状态
    var selfAudioInUse: MutableLiveData<Boolean> = ModelManager.membersModel.selfAudioInUseLive

    lateinit var tabMemberLl: LinearLayout

    // Jason Test
    // 当前是否share screen，默认没有分享
    var selfShareStatus = MutableLiveData(false)

    // 当前是否有ta人正在分享,点击分享前需要先判断一下；
    // 当自己分享时，不设置此值
    val isOtherSharing = MutableLiveData(false)

    // 来电信息
    var callInModel: StarryCallInModel = StarryCallInModel()

    var shareChangeStationTime = 0L;// 分享和停止操作之间需要时长大于3秒,防止服务器返回消息的错序

    var needRejoinMeeting = false;//是否需要重新加入会议
    var needRejoinMeetingOnce = false;//是否处理过需要重新加入会议

    private var fgServiceIntent: Intent? = null

    // chat重新连接的次数
    private var chatRetryCount = 0

    init {
        // 标记会议状态
        MeetingHandler.meetingStatus = MeetingStatus.STARTING
        agoraActiveSpeakerWorkStatus = false //初始化变量
        // 智能检测发言人
        agoraManager.getActiveSpeaker().observeForever {
            if (it != null) {
                agoraActiveSpeakerWorkStatus = true
            }
            onActiveSpeakerChange(it)
        }

        memberList.observeForever {
            if (needRejoinMeeting && MeetingModel.isInMeeting.value == true && !needRejoinMeetingOnce) {

                val count = ModelManager.membersModel.memberList.count {
                    it.isInMeeting
                }
                if (count > 1) {
                    needRejoinMeeting = false
                    needRejoinMeetingOnce = true
                    launch {
                        delay(1000)
                        //会议中至少有2人在线
                        logI("中兴离开会议一下2")
                        pauseExitMeeting()
                        delay(1000)
                        logI("中兴重新加入会议2")
                        preJoin()
                        joinChannelLongService(
                            MeetingModel.getTokenByJoin(callInModel.room),
                            callInModel.room
                        )
//                        joinChannel(
//                                MeetingModel.getTokenByJoin(callInModel.room),
//                                callInModel.room
//                        )
                    }


                } else {
                    updateDisplayData()
                }


            } else {
                updateDisplayData()
            }
        }

        launch {
            agoraManager.getSelfNetQualityFlow().collect {
                if (it == Constants.NETWORK_TYPE_DISCONNECTED) {
                    EventBus.getDefault()
                        .post(StarryCommonEvent(EventType.STARRY_SHOW_NONETWORK_ALERT, ""))
                } else {
                    EventBus.getDefault()
                        .post(StarryCommonEvent(EventType.STARRY_DISMISS_NONETWORK_ALERT, ""))
                }
            }
        }

    }

    // 无网络下点击按钮,提示
    fun clickNoNetwork(): Boolean {
        if (NetworkUtils.isConnected()) {
            return false
        } else {
            ToastUtils.showLong(R.string.starry_network_error_msg)
            return true
        }
    }

    // 网络质量监测
    var networkQualityDialogShowFlag = true //是否显示对话框提示
    private val networkQualityObserver = Observer<Int> {
        networkQuality.postValue(it)
    }

    fun observeNetworkQuality(observe: Boolean) {
        val networkQuality = getSelfNetworkQualityLiveData()
        if (observe) {
            networkQuality.observeForever(networkQualityObserver)
        } else {
            networkQuality.removeObserver(networkQualityObserver)
        }
    }

    private val selfNetworkTxQualityObserver = Observer<Int> {
        selfNetworkTxQuality.postValue(it)
    }
    private val selfNetworkRxQualityObserver = Observer<Int> {
        selfNetworkRxQuality.postValue(it)
    }

    fun observerSelfNetworkTxQuality(observe: Boolean) {
        val networkQuality = getSelfNetworkTxQualityLiveData()
        if (observe) {
            networkQuality.observeForever(selfNetworkTxQualityObserver)
        } else {
            networkQuality.removeObserver(selfNetworkTxQualityObserver)
        }
    }

    fun observerSelfNetworkRxQuality(observe: Boolean) {
        val networkQuality = getSelfNetworkRxQualityLiveData()
        if (observe) {
            networkQuality.observeForever(selfNetworkRxQualityObserver)
        } else {
            networkQuality.removeObserver(selfNetworkRxQualityObserver)
        }
    }

// 设置当前自己正在分享true
    /**
     * isShare 0结束分享 1开始分享
     */
    fun changeSelfShareStatus(isShare: String = "") {
        if (isShare.isNotEmpty()) {
            selfShareStatus.postValue(isShare == "1")
        } else {
            selfShareStatus.postValue(!selfShareStatus.value!!)
        }
    }

//    // 当前活动的参会人员
//    private val activeSpeakerObserver = Observer<String> {
//        activeSpeaker.postValue(it)
//    }
//    private fun observeActiveSpeaker(observe: Boolean) {
//        val activeSpeaker = agoraManager.getActiveSpeaker()
//        if (observe) {
//            activeSpeaker.observeForever(activeSpeakerObserver)
//        } else {
//            activeSpeaker.removeObserver(activeSpeakerObserver)
//        }
//    }

    /**
     * 智能检测发言人改变了
     */
    private fun onActiveSpeakerChange(czurId: String) {
//        logI("${TAG}.onActiveSpeakerChange.czurId=${czurId},displayMode.value=${displayMode.value},smartFocus.value=${smartFocus.value}")
        when {
            smartFocus.value == false -> {
                // 单人模式, 智能检测发言人功能关闭
                logI("智能检测发言人功能关闭")
                return
            }

            (displayJoinedUids.value?.size ?: 0) > 2 -> {
                // 更新主画面
                logI("当前发言人:czurID:${czurId}")
                activeSpeaker.postValue(czurId)
                mCurrentActiveSpeakerUid = czurId
            }

            else -> {
                logI("人数不够, 智能检测发言人功能关闭")
            }
        }
    }

    //检测所有用户的音频状态,如果全部静音,智能检测发言人默认放在成员列表非自己的第一位
    fun checkActiveSpeaker(
        memberList: LiveData<List<Member>>,
        displayUidsMain: List<DisplayData>
    ) {
        var allMemberMute = true
        val value = memberList.value
        if (value != null) {
            for (member: Member in value) {
                if (member.isAudioInUse) {
                    allMemberMute = false
                }
            }
        }
        var memberUid = "0"
        for (displayData: DisplayData in displayUidsMain) {
            if (displayData.uid != UserHandler.czurId) {
                memberUid = displayData.uid
                break
            }
        }

        if (allMemberMute) {
            onActiveSpeakerChange(memberUid)
        } else {
            if (!agoraActiveSpeakerWorkStatus) {//如果不工作,指定第一个为智能发言人
                onActiveSpeakerChange(memberUid)
            }

        }
    }

    fun switchMainLockEnable() {
        isMainLock.postValue(true)
    }

    // 切换摄像头
    fun changeLocalCarmea() {
        agoraManager.changeLocalCarmea()
    }

    // cam静音开关
    fun notMuteLocalAudioOrVideo(stream: StreamType, flag: Boolean) {
        agoraManager.notMuteLocalAudioOrVideo(stream, flag)
    }

    // 开启/关闭本地视频采集。
    fun enableLocalVideo(flag: Boolean = true) {
        agoraManager.enableLocalVideo(flag)
    }

    // 开启/关闭本地音频采集。
    fun enableLocalAudio(flag: Boolean = true) {
        agoraManager.enableLocalAudio(flag)
    }

    /**
     * 更新主画面的uid信息
     */
//    fun updateMainUid(uid: String, smalluid: String = UserHandler.czurId.toString()) {
//        displaySmallUid.value = smalluid
//        displayMainUid.value = uid
//        if (displaySmallUid.value == displayMainUid.value){
//            displaySmallUid.value = null
//        }
//        changeDisplayMode(MeetingDisplayMode.SINGLE)
//    }

    fun sendMsg(text: String) {
        agoraManager.sendMsg(text)
    }

    /**
     * 初始化数据
     */
    fun initData(
        initParam: InitParam,
        initListener: () -> Unit,
    ) {
        logD("初始化数据: initParam-${initParam}")
        initListener()

        launch {
            MeetingModel.initMeeting(initParam)

            // 判断发起会议是否成功
            if (MeetingModel.room == "0") {
                logI("${TAG}.initData.startMeetingCall.initParam-${initParam}!")
                logI("${TAG}.initData.startMeetingCall Error!")
                EventBus.getDefault().post(
                    StarryCommonEvent(EventType.STARRY_MEETING_ERROR_EXIT, "")
                )
                // 标记呼叫结束，进入会议
                MeetingModel.isCallingOutMeeting = false

                return@launch
            }

            callInModel.room = MeetingModel.room
            if (ESPermissionUtils.checkVideoAndAudioPermission()) { //有音频视频权限
                val isMic = StarryPreferences.getInstance()?.lastMic ?: true
                val isCam = StarryPreferences.getInstance()?.lastCam ?: false
                ModelManager.membersModel.selfAudioInUseLive.postValue(isMic)
                ModelManager.membersModel.selfVideoInUseLive.postValue(isCam)
            } else {//没有权限,全设置为false
                StarryPreferences.getInstance()?.lastMic = false
                StarryPreferences.getInstance()?.lastCam = false
                ModelManager.membersModel.selfAudioInUseLive.postValue(false)
                ModelManager.membersModel.selfVideoInUseLive.postValue(false)
            }


            // 保存用户信息
            saveUserHandlerInfo()

            // 开始计时
            startTimer()
            joinChannelLongService(MeetingModel.token, MeetingModel.room)
//            joinChannel(MeetingModel.token, MeetingModel.room)
//            initListener()
        }
    }

    public fun joinChannelLongService(token: Token, room: String) {
        var tmpToken = token
        if (tmpToken.rtcToken.isBlank()) {
            tmpToken = MeetingModel.token
        }
        EventBus.getDefault().postSticky(ServiceJoiningMeetingRoomEvent(room, tmpToken))
        starryJoinMeeting()
    }

    fun joinChannelAgora(token: Token, room: String) {
        joinChannel(token, room)
    }

    /**
     * 加入视频和聊天的Channel
     */
    public fun joinChannel(token: Token, room: String) {
        // 接入声网的SDK
        agoraManager.joinChannel(token, room, displayJoinResult = {
            if (it != JOIN_CHANNEL_SUCCESS) {
//                return@joinChannel
            }
            showMeeting.postValue(it == JOIN_CHANNEL_SUCCESS)

            MeetingModel.isInMeeting.postValue(it == JOIN_CHANNEL_SUCCESS)
            CzurCloudApplication.isJoinedMeeting = (it == JOIN_CHANNEL_SUCCESS)

            // 声网在会议中
            MeetingModel.isAgoraInMeeting = true

            // 标记呼叫结束，进入会议
            MeetingModel.isCallingOutMeeting = false

            // 视频channel加入完成后, 获取自身的surfaceView
            logI("joinChannel.视频channel加入成功:${it == JOIN_CHANNEL_SUCCESS}")

//            launch{
//                // 分享流成功入会(但还未允许),刷新一下屏幕,防止横屏下,小窗竖屏
//                if (MeetingModel.isScreenSharePermissionDialg){
//                    delay(100)
//                    // 有时黑屏，刷新一下主屏
//                    MeetingModel.isForceRefeshMain.postValue(true)
//                }
//            }

            displayMainUid.postValue(UserHandler.czurId.toString())
            uidFromAgoraList.add(UserHandler.czurId.toString())
            uidFromAgoraListLive.postValue(uidFromAgoraList)

            launch(Dispatchers.Main) {
                // 加入会议，告知CZUR服务器
//                starryJoinMeeting()
                // 获取Surface 需要在主线程
                if (ESPermissionUtils.checkVideoAndAudioPermission()) {
                    notMuteLocalAudioOrVideo(
                        StreamType.AUDIO,
                        ModelManager.membersModel.selfAudioInUse
                    )
                    notMuteLocalAudioOrVideo(
                        StreamType.VIDEO,
                        ModelManager.membersModel.selfVideoInUse
                    )
                } else {
                    selfVideoInUse.postValue(false)
                    selfAudioInUse.postValue(false)
                    if (StarryPreferences.getInstance().callInTargetMicStatus == CAM_AUDIO_STATUS_NO) {
                        notMuteLocalAudioOrVideo(StreamType.AUDIO, false)
                    } else {
                        if (StarryPreferences.getInstance().callInTargetMicStatus == CAM_AUDIO_STATUS_OPEN) {
                            notMuteLocalAudioOrVideo(StreamType.AUDIO, false)
                        }

                        if (StarryPreferences.getInstance().callInTargetCamStatus == CAM_AUDIO_STATUS_OPEN) {
                            notMuteLocalAudioOrVideo(StreamType.VIDEO, false)
                        }

                    }

                }
                StarryPreferences.getInstance().callInTargetMicStatus = CAM_AUDIO_STATUS_NO
                StarryPreferences.getInstance().callInTargetCamStatus = CAM_AUDIO_STATUS_NO


                updateDisplayData()

//                // 关麦入会，对端可以听见声音解决
//                if (!ModelManager.membersModel.selfAudioInUse) {
//                    muteLocalAudioOrVideo(StreamType.AUDIO, ModelManager.membersModel.selfAudioInUse)
//                }

            }
        }, chatJoinResult = {
            chatEnable.postValue(it == JOIN_CHANNEL_SUCCESS)
//            ChatModel.addMsg(ChatActionMsg(UserHandler.czurId.toString(), ChatMemberMsgAction.ONLINE))
            // 标记呼叫结束，进入会议
            MeetingModel.isCallingOutMeeting = false
            logI("joinChannel.聊天channel加入成功:${it == JOIN_CHANNEL_SUCCESS}")

            if (it != JOIN_CHANNEL_SUCCESS) {
//                return@joinChannel
                rejoinChat(token, room)
            }
        })

    }

    // 重新登录聊天服务器
    private fun rejoinChat(token: Token, room: String) {
        agoraManager.joinChatChannel(token, room, chatJoinResult = {
            launch {
                chatEnable.postValue(it == JOIN_CHANNEL_SUCCESS)
//            ChatModel.addMsg(ChatActionMsg(UserHandler.czurId.toString(), ChatMemberMsgAction.ONLINE))
                // 标记呼叫结束，进入会议
                MeetingModel.isCallingOutMeeting = false
                logI("joinChannel.聊天channel加入成功:${it == JOIN_CHANNEL_SUCCESS}")
                if (it != JOIN_CHANNEL_SUCCESS) {
                    chatRetryCount++
                    delay(5000)
                    if (chatRetryCount > 20) {
                        return@launch
                    }
                    rejoinChat(token, room)
                } else {
                    chatRetryCount = 0
                }
            }

        })
    }

    /**
     * 更新界面显示的数据
     */
    private fun updateDisplayData() {
        isUpdateFlag = true
        val members = ModelManager.membersModel.memberList
        logI(
            "${TAG}.updateDisplayData.更新显示视图.size=${members.size};",
            "members=${members}"
        )

        val isShareMode = MeetingModel.shareMode ?: false

        isOtherSharing.postValue(isShareMode)

        members.forEach {
            val uid = it.czurID
            if (it.status == STATUS_JOINED || it.status == STATUS_OFFLINE || it.status == STATUS_IOS_HOLD) {
                val dataMain = getDisplayDataMain(it, agoraManager)
                displayDataJoinedMapMain[uid] = dataMain
            }
            if (it.sharing) {
                val dataShare = getDisplayDataShare(it, agoraManager)
                MeetingModel.shareId = it.shareStream ?: uid
                displayDataJoinedMapMain[MeetingModel.shareId] = dataShare
            }
        }
//        logI("${TAG}.updateDisplayData.size=${displayDataJoinedMapMain.size};displayDataJoinedMapMain=${displayDataJoinedMapMain}")

        // 改变界面显示的数据
        val displayMap = getDisplayDataListNew(members, isShareMode, agoraManager)

        // 判断远程的状态是否与本地一致？否则，同步远程状态
        if (members.isNotEmpty()) {
            syncRemoteStatus(members)
        }

        displayUidsMap.clear()
        displayUidsJoinedMap.clear()
        displayMap.forEach {
            displayUidsMap[it.uid] = it

            // displayUidsJoinedMap 还需要包含掉线的成员
            if (it.status == STATUS_JOINED || it.status == STATUS_OFFLINE || it.status == STATUS_IOS_HOLD) {
                displayUidsJoinedMap[it.uid] = it
            }
        }

        if (displayUids.value != displayMap) {
            // 需要判断一下线程
            if (Looper.getMainLooper().isCurrentThread) {
                displayUids.value = displayMap
            } else {
                displayUids.postValue(displayMap)
            }
        }

        launch {
            // 设置displayUids的各种值 // 4宫格
            setDisplayUids4(displayMap)

        }

        isUpdateFlag = false
    }

    // 判断远程的状态是否与本地一致？否则，同步远程状态
    private fun syncRemoteStatus(members: List<Member>) {
        val myselfmember: Member? = members.firstOrNull {
            it.czurID == UserHandler.czurId.toString()
        }
        val videoInUse = myselfmember?.isVideoInUse ?: ModelManager.membersModel.selfVideoInUse
        val audioInUse = myselfmember?.isAudioInUse ?: ModelManager.membersModel.selfAudioInUse

        val videoFlag = videoInUse == ModelManager.membersModel.selfVideoInUse
        val audioFlag = audioInUse == ModelManager.membersModel.selfAudioInUse

        if (videoFlag && audioFlag) {
            // 相同，不处理
        } else {
            // 同步一下
            logI("${TAG}.syncRemoteStatus.myselfmember=${myselfmember}")
            syncSelfVideoAudio()
        }

    }

    // 该方法涉及到一些更新UI的操作, 只能在主线程中执行
    private suspend fun setDisplayUids4(displayMap: List<DisplayData>) =
        withContext(Dispatchers.Main) {
            var tmpDisplayMap = displayMap.filter {
                it.status == STATUS_JOINED || it.status == STATUS_OFFLINE || it.status == STATUS_IOS_HOLD
            }
            if (tmpDisplayMap.isEmpty()) {
                tmpDisplayMap = displayMap
            }
//            logI("setDisplayUids4.tmpDisplayMap=${tmpDisplayMap}")

            if (displayJoinedUids.value != tmpDisplayMap) {
                displayJoinedUids.value = tmpDisplayMap

                logI("setDisplayUids4.displayJoinedUids=${displayJoinedUids.value}")

                val shareModeMainUidsList = arrayListOf<DisplayData>()

                if (MeetingModel.shareMode && tmpDisplayMap.isNotEmpty()) {
                    shareModeMainUidsList.add(tmpDisplayMap[0])
                    shareModeMainUidsList.addAll(tmpDisplayMap.filter {
                        it.uid == ModelManager.membersModel.getShareMember().czurID
                    })
                    tmpDisplayMap = tmpDisplayMap.filterNot {
                        it.uid == tmpDisplayMap[0].uid
                    }
                    logI("setDisplayUids4.shareModeMainUidsList=${shareModeMainUidsList}")
                }

                when (tmpDisplayMap.size ?: 0) {
                    1, 2 -> {
                        if (MeetingModel.shareMode && !ModelManager.membersModel.isMyselfSharing()) {
                            displayUidsMain = shareModeMainUidsList
                        } else {
                            displayUidsMain = tmpDisplayMap
                        }
                        displayUidsGrid2 = listOf<DisplayData>()
                        displayUidsGrid3 = listOf<DisplayData>()
                        displayUidsGrid4 = listOf<DisplayData>()
                        displayUidsGrid5 = listOf<DisplayData>()
                    }

                    3, 4 -> {
                        if (MeetingModel.shareMode) {
                            displayUidsMain = shareModeMainUidsList
                        } else {
                            displayUidsMain = tmpDisplayMap.subList(0, 2)
                        }
                        displayUidsGrid2 = tmpDisplayMap
                        displayUidsGrid3 = listOf<DisplayData>()
                        displayUidsGrid4 = listOf<DisplayData>()
                        displayUidsGrid5 = listOf<DisplayData>()
                    }

                    5, 6, 7, 8 -> {
                        if (MeetingModel.shareMode) {
                            displayUidsMain = shareModeMainUidsList
                        } else {
                            displayUidsMain = tmpDisplayMap.subList(0, 2)
                        }
                        displayUidsGrid2 = tmpDisplayMap.subList(0, 4)
                        displayUidsGrid3 = tmpDisplayMap.subList(4, tmpDisplayMap.size)
                        displayUidsGrid4 = listOf<DisplayData>()
                        displayUidsGrid5 = listOf<DisplayData>()
                    }

                    9, 10, 11, 12 -> {
                        if (MeetingModel.shareMode) {
                            displayUidsMain = shareModeMainUidsList
                        } else {
                            displayUidsMain = tmpDisplayMap.subList(0, 2)
                        }
                        displayUidsGrid2 = tmpDisplayMap.subList(0, 4)
                        displayUidsGrid3 = tmpDisplayMap.subList(4, 8)
                        displayUidsGrid4 = tmpDisplayMap.subList(8, tmpDisplayMap.size)
                        displayUidsGrid5 = listOf<DisplayData>()
                    }

                    13, 14, 15 -> {
                        if (MeetingModel.shareMode) {
                            displayUidsMain = shareModeMainUidsList
                        } else {
                            displayUidsMain = tmpDisplayMap.subList(0, 2)
                        }
                        displayUidsGrid2 = tmpDisplayMap.subList(0, 4)
                        displayUidsGrid3 = tmpDisplayMap.subList(4, 8)
                        displayUidsGrid4 = tmpDisplayMap.subList(8, 12)
                        displayUidsGrid5 = tmpDisplayMap.subList(12, tmpDisplayMap.size)
                    }

                    else -> {
                    }
                }

                logI("setDisplayUids4.displayUidsMain=${displayUidsMain}")

                // 检查主屏的uids
                checkDisplayUidsMain()

                if (displayUidsMain.isNotEmpty() && displayUidsMainLive.value != displayUidsMain) {
                    displayUidsMainLive.postValue(displayUidsMain)
                }
                if (displayUidsMain.isNotEmpty() && displayUidsMain.size >= 2 && !ModelManager.membersModel.selfVideoInUse) {
                    displayUidsMainLive.postValue(displayUidsMain)
                }

                if (displayUidsGrid2.isNotEmpty() && displayUidsGrid2Live.value != displayUidsGrid2) {
                    displayUidsGrid2Live.postValue(displayUidsGrid2)
                }
                if (displayUidsGrid3.isNotEmpty() && displayUidsGrid3Live.value != displayUidsGrid3) {
                    displayUidsGrid3Live.postValue(displayUidsGrid3)
                }
                if (displayUidsGrid4.isNotEmpty() && displayUidsGrid4Live.value != displayUidsGrid4) {
                    displayUidsGrid4Live.postValue(displayUidsGrid4)
                }
                if (displayUidsGrid5.isNotEmpty() && displayUidsGrid5Live.value != displayUidsGrid5) {
                    displayUidsGrid5Live.postValue(displayUidsGrid5)
                }

            } else {
                if (tmpDisplayMap.size > 1) {
                    // 检查主屏的uids
                    checkDisplayUidsMain()
                } else {
                    forceRefreshUI()
                }
            }
            if (tmpDisplayMap.size > 2) {
                checkActiveSpeaker(memberList, displayUidsGrid2)
            }

        }

    // 确定主屏的视频顺序，哪个大哪个小：根据MeetingModel.curMainId=0;curSmallId=1
    private fun checkDisplayUidsMain() {
        if (!MeetingModel.shareMode) {
            val tmpMain = arrayListOf<DisplayData>()
            // 1, 数量<2，都显示主屏自己；
            // 2, 数量=2，主屏他人，副屏自己；
            // 3，数量>2，给grid使用了；
            if (displayUidsJoinedMap.size < 2) {
                val main = UserHandler.czurId.toString()
                MeetingModel.curMainId = main
                MeetingModel.curSmallId = null
                displayUidsJoinedMap[main]?.let { tmpMain.add(it) }
            } else {
                val oldMain = MeetingModel.curMainId
                val selfCZURId = UserHandler.czurId.toString()
                if (!displayUidsJoinedMap.containsKey(MeetingModel.curMainId)) {
                    // 当前主画面的uid并不在成员列表中, 表示当前的主画面信息已经需要重置了
                    logD("当前画面uid:${MeetingModel.curMainId} 已经不存在")
                    MeetingModel.curMainId = ""
                    MeetingModel.curSmallId = null
                }
                // 首先检查小屏有没有信息, 正常来说, 在这个位置, 一定有小屏信息的
                // 如果没有, 可以认为是没有设置好, 此时, 重设一次大小屏信息
                // 但是实际上不应该在这个位置进行修改, 不过找不到正确的位置了
                // 右上角小画面在 非分享模式下, 多人时 只能显示自己
                // 在只有一个人时, 即使这里被补充上了, 也会由后面 主画面和小画面不能显示同一人的逻辑再删掉
                if (MeetingModel.curSmallId == null ||
                    (MeetingModel.curSmallId != selfCZURId && displayUidsJoinedMap.size > 2)
                ) {
                    logD("缺失小屏信息, 补充上")
                    MeetingModel.curSmallId = selfCZURId
                    // 如果主画面和小画面一样, 则修改主画面
                    // 保证主画面显示的是别人
                    if (MeetingModel.curMainId == MeetingModel.curSmallId) {
                        MeetingModel.curMainId = displayUidsJoinedMap.keys.firstOrNull {
                            it != selfCZURId
                        } ?: selfCZURId
                    }
                }

                var main = MeetingModel.curMainId

                // 主画面也有可能为空??
                if (main.isEmpty()) {
                    logD("主画面为空!!")
                    main = displayJoinedUids.value?.first {
                        // 找到第一个不是自己的画面
                        it.uid != selfCZURId
                    }?.uid ?: UserHandler.czurId.toString()
                    MeetingModel.curMainId = main
                }

                var small = MeetingModel.curSmallId

                if (small == main) {
                    small = displayJoinedUids.value?.first {
                        it.uid != main
                    }?.uid ?: UserHandler.czurId.toString()
                }
                displayUidsJoinedMap[main]?.let { tmpMain.add(it) }
                displayUidsJoinedMap[small]?.let { tmpMain.add(it) }

                // 设置订阅的视频流类型。
                if (oldMain == main) {
                    setRomoteVideoStreamType(main, VIDEO_STREAM_HIGH)
                } else {
                    setRomoteVideoStreamType(oldMain, VIDEO_STREAM_HIGH)
                    setRomoteVideoStreamType(main, VIDEO_STREAM_HIGH)
                }
            }
            displayUidsMain = tmpMain
        }
    }

    // 修改主屏的uids
    fun resetDisplayUidsMain(main: String, small: String? = UserHandler.czurId.toString()) {
        if (!MeetingModel.shareMode) {
            val oldMainUid = MeetingModel.curMainId

            val tmpMain = arrayListOf<DisplayData>()
            MeetingModel.curMainId = main
            MeetingModel.curSmallId = small
            if (displayUidsJoinedMap.size > 1) {
                displayUidsJoinedMap[main]?.let { tmpMain.add(it) }
                displayUidsJoinedMap[small]?.let { tmpMain.add(it) }
            } else {
                MeetingModel.curSmallId = null
            }
            displayUidsMain = tmpMain
            if (displayUidsMain.isNotEmpty() && displayUidsMainLive.value != displayUidsMain) {
                displayUidsMainLive.postValue(displayUidsMain)
            }

//            setRomoteVideoStreamType(oldMainUid, VIDEO_STREAM_LOW)
            setRomoteVideoStreamType(oldMainUid, VIDEO_STREAM_HIGH)
            setRomoteVideoStreamType(MeetingModel.curMainId, VIDEO_STREAM_HIGH)
        }
    }

    /**
     * 当远程用户上线了
     */
    private fun onRemoteUserOnLine(uid: String) {
        logI("${TAG}.onRemoteUserOnLine.远端用户上线-uid: $uid")

        // 屏幕分享权限弹窗弹出,不予处理
        if (MeetingModel.isScreenSharePermissionDialg) {
            return
        }

        uidFromAgoraList.add(uid)
        uidFromAgoraListLive.postValue(uidFromAgoraList.toSet().toList())
        if (uidFromAgoraList.isNotEmpty() && uidFromAgoraList.size == 2) {
            MeetingModel.curMainId = uid
            MeetingModel.curSmallId = UserHandler.czurId.toString()
//            forceRefreshUI()
        }

        // 获取最新的用户列表
        updateDisplayData()
//        forceRefreshUI()
    }

    private fun setRomoteVideoStreamType(uid: String, videoStreamHigh: Int = VIDEO_STREAM_HIGH) {
        if (uid.isNotEmpty()) {
            agoraManager?.setRemoteVideoStreamType(uid.toInt(), videoStreamHigh)
        }
    }

    // 当远程用户离线了
    private fun onRemoteUserOffLine(uid: String) {
        logI("${TAG}.onRemoteUserOffLine.远端用户离线-uid: $uid")
        uidFromAgoraList.remove(uid)
        uidFromAgoraListLive.postValue(uidFromAgoraList)

        // 有离开的用户，请求一下userlist，更新列表
//        syncSelfVideoAudio()

        val data = displayDataJoinedMapMain[uid]
        if (data?.status == STATUS_IOS_HOLD || data?.status == STATUS_JOINED) {

        } else {
            displayDataJoinedMapMain.remove(uid)
        }

        val uidData = displayUidsJoinedMap[uid]
        // 异常掉线，暂不处理
        if (uidData?.status == STATUS_OFFLINE || uidData?.status == STATUS_IOS_HOLD || uidData?.status == STATUS_JOINED) {
            return
        }

        displayUidsJoinedMap.remove(uid)

        if (uidFromAgoraList.isNotEmpty()) {
            if (uidFromAgoraList.size < 2) {
                MeetingModel.curMainId = UserHandler.czurId.toString()
                MeetingModel.curSmallId = null
            } else {
                val main = MeetingModel.curMainId
                val small = MeetingModel.curSmallId
                val selfCZURId = UserHandler.czurId.toString()
                logI("${TAG}.onRemoteUserOffLine111.MeetingModel.curMainId=${MeetingModel.curMainId};MeetingModel.curSmallId=${MeetingModel.curSmallId}")
                if (main == uid) {
                    MeetingModel.curMainId = displayJoinedUids.value?.first {
                        it.uid != main && it.uid != small
                    }?.uid ?: selfCZURId
                }
                if (small == uid) {
                    MeetingModel.curSmallId = displayJoinedUids.value?.first {
                        it.uid != small && it.uid != main
                    }?.uid ?: selfCZURId
                }
                if (MeetingModel.curMainId == MeetingModel.curSmallId) {
                    MeetingModel.curMainId = displayUidsJoinedMap.keys.firstOrNull {
                        it != selfCZURId
                    } ?: selfCZURId
                }

            }
//            forceRefreshUI()
        }
        // 获取最新的用户列表
        updateDisplayData()
//        forceRefreshUI()
    }

    private fun forceRefreshUI() {
        displayMainUid.postValue(MeetingModel.curMainId)
    }

    /**
     * 自己是否是会议主持人
     * @return   true: 是主持人
     *           false:不是主持人
     */
    fun isAdmin(): Boolean {
        val members = ModelManager.membersModel.memberList
        val selfMember = members.find { it.isSelf }
        return selfMember?.isAdmin ?: false
    }

    fun getSelfVolumeLiveData() = agoraManager.getVolumeLiveData(UserHandler.czurId.toString());

    private fun getSelfNetworkQualityLiveData() =
        agoraManager.getNetworkQualityLiveData(UserHandler.czurId.toString());

    private fun getSelfNetworkTxQualityLiveData() =
        agoraManager.getSelfNetworkTxQualityLiveData(UserHandler.czurId.toString());

    private fun getSelfNetworkRxQualityLiveData() =
        agoraManager.getSelfNetworkRxQualityLiveData(UserHandler.czurId.toString());


    /**
     * 关闭声网相关的
     */
    fun onClearedAgoraManager() {
        MeetingModel.isAgoraInMeeting = false
        if (agoraManager != null) {
            agoraManager.leave()
        }
    }

    /**
     * ViewModel被销毁
     */
    override fun onCleared() {
        logI("${TAG}.onCleared.viewModel销毁, 断开声网连接")

        // 标记呼叫结束，进入会议
        MeetingModel.isCallingOutMeeting = false
        MeetingModel.isCallingInMeeting = false
        MeetingModel.isCallingInMeetingFlag.postValue(false)

        agoraManager.leave()
        // 清空数据源
        ModelManager.doClear()
        MeetingModel.doClear()

        displayDataJoinedMapMain.clear()

        displayUids.value = listOf()

        displayJoinedUids.value = listOf()

        displayUidsMain = listOf()
        displayUidsMainLive.postValue(displayUidsMain)
        displayUidsGrid1 = listOf()
        displayUidsGrid1Live.postValue(displayUidsMain)
        displayUidsGrid2 = listOf()
        displayUidsGrid2Live.postValue(displayUidsMain)
        displayUidsGrid3 = listOf<DisplayData>()
        displayUidsGrid3Live = MutableLiveData<List<DisplayData>>()
        displayUidsGrid4 = listOf<DisplayData>()
        displayUidsGrid4Live = MutableLiveData<List<DisplayData>>()
        displayUidsGrid5 = listOf<DisplayData>()
        displayUidsGrid5Live = MutableLiveData<List<DisplayData>>()

        // 恢复会议状态
        MeetingHandler.meetingStatus = MeetingStatus.STOPPED

        Thread.sleep(500)

        super.onCleared()
    }

    // 被移除会议
    fun removedStopMeeting() {
        MeetingModel.isCallingOutMeeting = false
        showMeeting.postValue(false)
        onClearedAgoraManager()
        finish.postValue(true)
    }

    //////////////
    fun stepOut() {
        MeetingModel.isCallingOutMeeting = false
        val cmd = MeetingCMD.STEPOUT.cmd
        val meetingNo = UserHandler.accountNo.toString() ?: "0"
        MsgProcessor.commonMeetCMD(cmd, meetingNo)
        finish.postValue(true)
        onClearedAgoraManager()
    }

    //发送结束会议指令
//  "cmd":"stop",                  // 指令类型
//  "meeting_no":"666666"            // 发送方的会议号
    fun stopMeeting() {
        MeetingModel.isCallingOutMeeting = false
        val cmd = MeetingCMD.STOP.cmd
        val meetingNo = UserHandler.accountNo.toString() ?: "0"
        MsgProcessor.commonMeetCMD(cmd, meetingNo)
//        finish.postValue(true)
//        onClearedAgoraManager()
    }

    //发送自己退出会议消息
//  "cmd":"exit",                    // 指令类型
//  "meeting_no":"666666"            // 发送方的会议号
    fun exitMeeting() {
        MeetingModel.isCallingOutMeeting = false
        val cmd = MeetingCMD.EXIT.cmd
        val meetingNo = UserHandler.accountNo.toString() ?: "0"
        MsgProcessor.commonMeetCMD(cmd, meetingNo)
        finish.postValue(true)
        onClearedAgoraManager()
    }

    fun preJoin() {
        agoraManager = AgoraManager(
            ::onRemoteUserOnLine,
            ::onRemoteUserOffLine
        )
    }

    //重新入会之前,退出会议
    fun pauseExitMeeting() {
        val cmd = MeetingCMD.STEPOUT.cmd
        val meetingNo = UserHandler.accountNo.toString() ?: "0"
        MsgProcessor.commonMeetCMD(cmd, meetingNo)
        onClearedAgoraManager()


//        ModelManager.doClear()
//        MeetingModel.doClear()

        displayDataJoinedMapMain.clear()

        displayUids.value = listOf()

        displayJoinedUids.value = listOf()

        displayUidsMain = listOf()
        displayUidsMainLive.postValue(displayUidsMain)
        displayUidsGrid1 = listOf()
        displayUidsGrid1Live.postValue(displayUidsMain)
        displayUidsGrid2 = listOf()
        displayUidsGrid2Live.postValue(displayUidsMain)
        displayUidsGrid3 = listOf<DisplayData>()
        displayUidsGrid3Live = MutableLiveData<List<DisplayData>>()
        displayUidsGrid4 = listOf<DisplayData>()
        displayUidsGrid4Live = MutableLiveData<List<DisplayData>>()
        displayUidsGrid5 = listOf<DisplayData>()
        displayUidsGrid5Live = MutableLiveData<List<DisplayData>>()
    }

    fun pauseStopMeeting() {
        val cmd = MeetingCMD.STOP.cmd
        val meetingNo = UserHandler.accountNo.toString() ?: "0"
        MsgProcessor.commonMeetCMD(cmd, meetingNo)
        onClearedAgoraManager()

        displayDataJoinedMapMain.clear()

        displayUids.value = listOf()

        displayJoinedUids.value = listOf()

        displayUidsMain = listOf()
        displayUidsMainLive.postValue(displayUidsMain)
        displayUidsGrid1 = listOf()
        displayUidsGrid1Live.postValue(displayUidsMain)
        displayUidsGrid2 = listOf()
        displayUidsGrid2Live.postValue(displayUidsMain)
        displayUidsGrid3 = listOf<DisplayData>()
        displayUidsGrid3Live = MutableLiveData<List<DisplayData>>()
        displayUidsGrid4 = listOf<DisplayData>()
        displayUidsGrid4Live = MutableLiveData<List<DisplayData>>()
        displayUidsGrid5 = listOf<DisplayData>()
        displayUidsGrid5Live = MutableLiveData<List<DisplayData>>()
    }

    // 自动入会
//    fun joinMeetingFormLink(room: String) {
//        launch {
//            delay(1000)
//            preJoin()
//            joinChannel(
//                    MeetingModel.getTokenByJoin(room),
//                    room
//            )
//        }
//
//    }

    // 更改本地的音视频状态，（mute=true:关; mute=false:开）
//DisplayManager.STREAM_TYPE_VIDEO
//DisplayManager.STREAM_TYPE_AUDIO
    fun changeLocalVideoAudio(
        streamType: Int,
        audioInUse: Boolean = selfAudioInUse.value == true,
        videoInUse: Boolean = selfVideoInUse.value == true,
        mute: Boolean = true
    ) {
        val meetNo = UserHandler.accountNo.toString() ?: "0"
        var audioStatus = if (audioInUse) 1 else 0
        var videoStatus = if (videoInUse) 1 else 0
        val cmd = MeetingCMD.SYNC_SELF.cmd

        val agoraManagerStreamType = when (streamType) {
            DisplayManager.STREAM_TYPE_VIDEO -> {
                videoStatus = if (mute) 0 else 1
                StreamType.VIDEO
            }

            DisplayManager.STREAM_TYPE_AUDIO -> {
                audioStatus = if (mute) 0 else 1
                StreamType.AUDIO
            }

            else -> null
        }

        // 更新本地的声网
        agoraManagerStreamType?.let {
            // 同步一次声网的开关
            // 声网的开关和方法名是反着的, 但是调用的地方太多了, 没法修改
            // 所以这里添加了一个取反操作
            agoraManager.notMuteLocalAudioOrVideo(it, !mute)
        }

        MsgProcessor.makeCMDDataStatus(cmd, meetNo, audioStatus, videoStatus)
    }

    // 同步本地音视频状态到服务器
    fun syncSelfVideoAudio() {
        val meetNo = UserHandler.accountNo.toString() ?: "0"
        val audioStatus = if (ModelManager.membersModel.selfAudioInUse) 1 else 0
        val videoStatus = if (ModelManager.membersModel.selfVideoInUse) 1 else 0
        val cmd = MeetingCMD.SYNC_SELF.cmd
        MsgProcessor.makeCMDDataStatus(cmd, meetNo, audioStatus, videoStatus)
    }

    //// 加入会议，告知CZUR服务器
//   CZURTcpClient.getInstance().starryJoinMeeting(CZURAtyManager.getContext(), udidFrom, channel, agoraID, meetNo)
    private fun starryJoinMeeting() {
        val meetNo = UserHandler.accountNo.toString() ?: "0"
        val cmd = MeetingCMD.JOIN.cmd
        val audioStatus = if (ModelManager.membersModel.selfAudioInUse) 1 else 0
        val videoStatus = if (ModelManager.membersModel.selfVideoInUse) 1 else 0
        //"audioStatus": 1,       // 音频状态 -1 不可用， 0 关闭， 1 开启
        // "videoStatus": 1        // 视频状态 -1 不可用， 0 关闭， 1 开启
        MsgProcessor.makeCMDDataJoin(cmd, meetNo, audioStatus, videoStatus)

    }

    // 掉线。重新加入
    fun starryReJoinMeetingAndUpdateStatus() {
        // 判断一下，如果是退出会议了，就不要重新入会了
        if (finish.value == true) {
            return
        }

        val meetNo = UserHandler.accountNo.toString() ?: "0"
        val cmd = MeetingCMD.JOIN.cmd
        val audioStatus = if (ModelManager.membersModel.selfAudioInUse) 1 else 0
        val videoStatus = if (ModelManager.membersModel.selfVideoInUse) 1 else 0
        MsgProcessor.makeCMDDataJoin(cmd, meetNo, audioStatus, videoStatus)
        if (ESPermissionUtils.checkVideoAndAudioPermission()) {
            notMuteLocalAudioOrVideo(StreamType.AUDIO, ModelManager.membersModel.selfAudioInUse)
            notMuteLocalAudioOrVideo(StreamType.VIDEO, ModelManager.membersModel.selfVideoInUse)

            val cmd1 = MeetingCMD.SYNC_SELF.cmd
            MsgProcessor.makeCMDDataStatus(cmd1, meetNo, audioStatus, videoStatus)
        } else {
            selfVideoInUse.postValue(false)
            selfAudioInUse.postValue(false)
            notMuteLocalAudioOrVideo(StreamType.AUDIO, false)
            notMuteLocalAudioOrVideo(StreamType.VIDEO, false)

            val cmd1 = MeetingCMD.SYNC_SELF.cmd
            MsgProcessor.makeCMDDataStatus(cmd1, meetNo, 0, 0)
        }

    }


    // 记录用户个人信息
    private fun saveUserHandlerInfo() {
        val user = UserPreferences.getInstance().user
        val starryModel =
            StarryPreferences.getInstance().starryUserinfoModel ?: StarryUserInfoModel()
        var username = starryModel.name
        if (username.isEmpty() || username == "") {
            username = user.name
        }

        UserHandler.login(
            token = user.token,
            accountNo = starryModel.accountNo ?: "",
            nickName = username,
            mobile = user.mobile,
            pwd = "",
            accountType = 0,
            bucketName = "",
            czurId = starryModel.czurId ?: "",
            dirPath = "",
            headImage = starryModel.headImage,
            inEnterprise = true,
            portLimit = 15,
            udidFrom = callInModel.udid_from ?: "",
            useridFrom = callInModel.userid_from ?: "",
            roomNo = callInModel.room ?: "0",
            agoraId = callInModel.czurId ?: "0"
        )
    }

    // 入会时长
    private fun startTimer() {
        isShowTimeAndCode.value = true
        startTime = SystemClock.uptimeMillis()
        isJoinMeetingAndMemberList = false
        val task = MyTimerTask()
        Timer().schedule(task, Date(), 1000)
    }

    val nowTime = MutableLiveData("00:00:00")

    private var startTime = SystemClock.uptimeMillis()
    private var millisecondsRecord = 0L
    private var timeBuff = 0L
    var isShowTimeAndCode = MutableLiveData(false)  // 控制title的时间信息的显示，会议开始才显示

    // 标记 已入会还未成功返回memberlist，=false； 当返回memberList时，=true
    var isJoinMeetingAndMemberList = false

    inner class MyTimerTask() : TimerTask() {

        override fun run() {
            millisecondsRecord = SystemClock.uptimeMillis() - startTime
            val accumulatedTime = ((timeBuff + millisecondsRecord) / 1000).toInt()
            val timeSpace = Tools.meetingTimeLongFormat(accumulatedTime)
            nowTime.postValue(timeSpace)

            // 判断当前时间是否为15s，
            val joinMeetingTimeout =
                if (BuildConfig.IS_OVERSEAS)
                    JOIN_MEETING_TIMEOUT_OVERSEA
                else JOIN_MEETING_TIMEOUT

            // 判断isJoinMeetingAndMemberList==false，没有返回memberlist，
            // 则退出当前会议
            if (!isJoinMeetingAndMemberList) {
                if (millisecondsRecord >= joinMeetingTimeout) {
                    isJoinMeetingAndMemberList = true
                    EventBus.getDefault().post(
                        StarryCommonEvent(EventType.STARRY_JOIN_MEETING_FAIL, "")
                    )
                }
            }
        }
    }

// 他人分享时候，设置保存mainuid和smalluid
// 分享结束，恢复mainuid和smalluid
    /**
     * flag true开始已分享,false结束分享
     */
    fun settingOtherShare(flag: Boolean, shareid: String = "") {
        if (flag) {
            MeetingModel.oldMainUid = MeetingModel.curMainId
            MeetingModel.oldSmallUid = MeetingModel.curSmallId.toString()
            MeetingModel.curMainId = shareid
//            MeetingModel.curSmallId = MembersModel.getShareMember()?.czurID.toString()
        } else {
            MeetingModel.curMainId = MeetingModel.oldMainUid
            MeetingModel.curSmallId = MeetingModel.oldSmallUid
            MeetingModel.oldMainUid = ""
            MeetingModel.oldSmallUid = ""
        }
        MeetingModel.shareId = shareid
        MeetingModel.shareMode = flag
        isOtherSharing.value = flag

    }


    fun startShareScreen(context: AppCompatActivity) {


        val time = (TimeUtils.getNowMills() - shareChangeStationTime) / 1000
        if (time >= 2) {
            shareChangeStationTime = TimeUtils.getNowMills()
        } else {
            ToastUtils.showShort(R.string.starry_share_quick_msg)
            return
        }

        isStartingShare = true
        MeetingModel.isScreenSharePermissionDialg = true
        try {
            launch {
                if (EShareActivity.eshareIsRunning) {// 如果正在无线投屏,关闭已经打开的无线投屏
                    LiveDataBus.get().with(StarryConstants.ESHARE_MEETING_CALLIN).value = true
                    delay(500)
                }

                val shareTokenWrapper = MeetingModel.getShareUidAndToken()
                val shareToken = shareTokenWrapper.shareToken

                if (shareToken == null) {
                    if (shareTokenWrapper.errorCode == 2044) {//当返回code为2044时，标识其他成员正在分享中
                        ToastUtils.showShort(R.string.starry_meeting_share_forbid)
                    } else {
                        logI("${TAG}获取分享Token失败")
                    }
                    return@launch
                }

                val shareUid = shareToken.shareId.toString()
                val rtcToken = shareToken.rtc_token
                val meetNo = UserHandler.accountNo.toString() ?: ""
                val room = MeetingModel.room ?: ""

                logI("${TAG}.startShareScreen.token:${rtcToken},shareUID:${shareUid},meetNo=${meetNo},room=${room}")

                agoraManager.setRefusedPermissionListener(object : RefusedPermissionListener {
                    override fun onRefusedPermission() {
                        logD("${TAG}.startShareScreen.用户拒绝了权限")
                        isStartingShare = false
                        MeetingModel.isScreenSharePermissionDialg = false

                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                            context.stopService(fgServiceIntent)
                        }
                        agoraManager.stopScreenCaption()
                        MeetingModel.shareMode = false

                        MsgProcessor.makeCMDDatashare(MeetingCMD.STOP_SHARE.cmd, meetNo, shareUid)
                        changeSelfShareStatus("0")//0 结束分享

//                        // 有时黑屏，刷新一下主屏
//                        MeetingModel.isForceRefeshMain.postValue(true)

                    }

                    override fun onAllowPermission() {
                        isStartingShare = false
                        MeetingModel.isScreenSharePermissionDialg = false
                        logD("${TAG}.startShareScreen.用户同意了权限")

                        MeetingModel.shareId = shareToken.shareId.toString()
                        MeetingModel.oldMainUid = MeetingModel.curMainId
                        MeetingModel.oldSmallUid = MeetingModel.curSmallId.toString()

                        launch {
                            MsgProcessor.makeCMDDatashare(MeetingCMD.SHARE.cmd, meetNo, shareUid)
                            changeSelfShareStatus()
                        }
                    }
                })

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    fgServiceIntent = Intent(context, MediaProjectFgService::class.java)
                    context.startForegroundService(fgServiceIntent)
                }
                agoraManager.startScreenCapture(
                    rtcToken,
                    room,
                    shareUid.toInt()
                )
            }

        } catch (e: Exception) {
            logE("${TAG}.startShareScreen.start.e=${e.toString()}")
        }

    }

    /**
     *  isClickEvent 只判断用户自身的点击行为
     */
    fun stopShareScreen(context: Context? = null, isClickEvent: Boolean = false) {
        if (isClickEvent) {
            val time = (TimeUtils.getNowMills() - shareChangeStationTime) / 1000
            if (time >= 2) {
                shareChangeStationTime = TimeUtils.getNowMills()
            } else {
                ToastUtils.showShort(R.string.starry_share_quick_msg)
                return
            }
        }

        launch {
            logI("ControlBarFragment.stopShareScreen.结束共享")

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                context?.stopService(fgServiceIntent)
            }

            val meetNo = UserHandler.accountNo.toString() ?: ""
            val shareUid = MeetingModel.shareId

            agoraManager.stopScreenCaption()
            MeetingModel.shareMode = false

            MsgProcessor.makeCMDDatashare(MeetingCMD.STOP_SHARE.cmd, meetNo, shareUid)
            changeSelfShareStatus("0")//0 结束分享
            // 有时黑屏，刷新一下主屏
            MeetingModel.isForceRefeshMain.postValue(true)

            // 恢复主屏显示
            MeetingModel.curMainId = MeetingModel.oldMainUid
            MeetingModel.curSmallId = MeetingModel.oldSmallUid

            MeetingModel.shareMode = false

            // 临时修改,需要重新捋一下逻辑,为了刷新一下界面
            // Bug #18107
            //【视频会议】一人会议中，屏幕分享，进入1位成员，结束分享后不显示副窗
            val flag = ModelManager.membersModel.selfAudioInUse
            switchSelfAudio(flag)
            notMuteLocalAudioOrVideo(StreamType.AUDIO, flag)
            ModelManager.membersModel.selfAudioInUseLive.value = flag
            // 同步需要更新列表的音视频开关
            ModelManager.membersModel.updateSelfMemberListStatus()
        }

    }

    // Starry检查长连接服务是否运行
    fun checkStarryServiceRunning(context: Context) {
        val flag = ServiceUtils.isServiceRunning(NettyService::class.java)
        val isLogin = UserPreferences.getInstance().isUserLogin
        // 只有登录的用户才重连
        if (!flag && isLogin) {
            if (NetworkUtils.isConnected()) {
                logI("${TAG}.NettyService is Not running, startService.NettyService.class...")
                NettyUtils.getInstance().startNettyService()
            }
        }
    }

    //判断是否app开启了某个Service
    fun isServiceRunning(mContext: Context): Boolean {
        return isOpenedService(mContext, "NettyService")
    }

    private fun isOpenedService(mContext: Context, className: String): Boolean {
        var isRunning = false
        val systemService: ActivityManager =
            mContext.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val serviceList: List<ActivityManager.RunningServiceInfo> = systemService
            .getRunningServices(Int.MAX_VALUE)
        if (serviceList.isEmpty()) {
            return false
        }
        serviceList.forEach {
            if (it.service.className.contains(className)) {
                isRunning = true
            }
        }
        return isRunning
    }

    // 重新设置视图显示
    fun setupViewForVideo(view: View, uid: String) {
        agoraManager.setupViewForVideo(view, uid)
    }

    // 重新入会成功执行
    fun onRejoinChannel() {
        // 视频channel加入完成后, 获取自身的surfaceView
        logI("onRejoinChannel.视频channel加入成功:${true}")

        // 判断一下，如果是退出会议了，就不要重新入会了
        if (finish.value == true) {
            return
        }

        showMeeting.postValue(true)
        MeetingModel.isInMeeting.postValue(true)
        CzurCloudApplication.isJoinedMeeting = true
        displayMainUid.postValue(UserHandler.czurId.toString())
        uidFromAgoraList.add(UserHandler.czurId.toString())
        uidFromAgoraListLive.postValue(uidFromAgoraList)

        launch(Dispatchers.Main) {
            // 加入会议，告知CZUR服务器
            starryJoinMeeting()
            // 获取Surface 需要在主线程
            updateDisplayData()
        }
    }

    fun goToBackend() {
        val meetNo = UserHandler.accountNo.toString() ?: "0"
        val cmd = MeetingCMD.TO_BACKGROUND.cmd
        MsgProcessor.commonMeetCMD(cmd, meetNo)
    }

    fun backToTheFrontend() {
        val meetNo = UserHandler.accountNo.toString() ?: "0"
        val cmd = MeetingCMD.TO_FRONT.cmd
        MsgProcessor.commonMeetCMD(cmd, meetNo)
    }

}