package com.czur.cloud.ui.account;

import static com.czur.cloud.common.CZURConstants.PWD_MIN_LENGTH;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.KeyboardUtils;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.common.MD5Utils;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.LoginEvent;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.NoHintEditText;
import com.czur.cloud.ui.component.ProgressButton;
import com.czur.cloud.util.StringToolsUtils;

import org.greenrobot.eventbus.EventBus;

/**
 * Created by Yz on 2018/3/7.
 * Email：<EMAIL>
 */

public class ResetPasswordActivity extends BaseActivity implements View.OnClickListener {

    private ImageView accountBackBtn;
    private TextView accountTitle;
    private NoHintEditText firstSetPasswordEdt;
    private ProgressButton confirmBtn;


    private boolean hasFirstPassword = false;
    private HttpManager httpManager;
    private String account;
    private String resetCode;
    private UserPreferences userPreferences;
    private long currentTime;
    private MiaoHttpEntity<String> resetEntity;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_reset_password);
        initComponent();
        registerEvent();
    }

    private void initComponent() {
        account = getIntent().getStringExtra("account");
        resetCode = getIntent().getStringExtra("resetCode");
        httpManager = HttpManager.getInstance();
        userPreferences = UserPreferences.getInstance(this);

        accountBackBtn = (ImageView) findViewById(R.id.account_back_btn);
        accountTitle = (TextView) findViewById(R.id.account_title);
        firstSetPasswordEdt = (NoHintEditText) findViewById(R.id.first_set_password_edt);
        confirmBtn = (ProgressButton) findViewById(R.id.confirm_btn);


        //设置标题
        accountTitle.setText(R.string.forget_password);

    }

    private void registerEvent() {
        accountBackBtn.setOnClickListener(this);
        confirmBtn.setOnClickListener(this);
        confirmBtn.setOnProgressFinishListener(onProgressFinish);
        confirmBtn.setSelected(false);
        confirmBtn.setClickable(false);
        firstSetPasswordEdt.addTextChangedListener(firstPswTextWatcher);

    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            case R.id.confirm_btn:
                confirmPassword();
                break;
            case R.id.account_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            default:
                break;
        }
    }

    private void failedDelay(final int failedText) {

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    long sleepTime;
                    if (System.currentTimeMillis() - currentTime < 1000) {
                        sleepTime = 1000 - (System.currentTimeMillis() - currentTime);
                    } else {
                        sleepTime = 1;
                    }
                    Thread.sleep(sleepTime);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            showMessage(failedText);
                            confirmBtn.stopLoading();
                        }
                    });
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }


    private void successDelay(final MiaoHttpEntity<String> entity) {

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    long sleepTime;
                    if (System.currentTimeMillis() - currentTime < 1000) {
                        sleepTime = 1000 - (System.currentTimeMillis() - currentTime);
                    } else {
                        sleepTime = 1;
                    }
                    Thread.sleep(sleepTime);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            confirmBtn.stopLoadingSuccess();
                        }
                    });
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }

    /**
     * @des: 确认新密码
     * @params:[]
     * @return:void
     */
    private void confirmPassword() {
        String firstPassword = firstSetPasswordEdt.getText().toString();

        // 修改密码时不符合密码规则的toast提示修改为“密码为8-20位数字及字母组合”
        if (firstPassword.length() <= PWD_MIN_LENGTH ||
                !StringToolsUtils.isLetterDigit(firstPassword)) {
            showMessage(R.string.toast_pwd_length);
        } else {
            currentTime = System.currentTimeMillis();
            KeyboardUtils.hideSoftInput(this);
            httpManager.requestPassport().againPwd(CZURConstants.CLOUD_ANDROID, userPreferences.getIMEI(), userPreferences.getChannel(),
                    resetCode, MD5Utils.md5(firstPassword), String.class, new MiaoHttpManager.Callback<String>() {
                        @Override
                        public void onStart() {
                            confirmBtn.startLoading();
                        }

                        @Override
                        public void onResponse(MiaoHttpEntity<String> entity) {
                            resetEntity = entity;
                            successDelay(entity);


                        }

                        @Override
                        public void onFailure(MiaoHttpEntity<String> entity) {
                            if (entity.getCode() == MiaoHttpManager.STATUS_TIME_OUT) {
                                failedDelay(R.string.toast_operate_time_out);
                            } else {
                                failedDelay(R.string.request_failed_alert);
                            }
                        }

                        @Override
                        public void onError(Exception e) {
                            failedDelay((R.string.request_failed_alert));
                        }
                    });
        }
    }

    public void resetSuccess(MiaoHttpEntity<String> entity) {
        showMessage(R.string.toast_pwd_success);
        EventBus.getDefault().post(new LoginEvent(EventType.RESET_PASSWORD));
        //跳转到ResetPasswordActivity
        Intent intent = new Intent(ResetPasswordActivity.this, LoginActivity.class);
        intent.putExtra(LoginActivity.FROM_FORGET_PASSWORD, true);
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
        ActivityUtils.startActivity(intent);
    }

    private ProgressButton.OnProgressFinish onProgressFinish = new ProgressButton.OnProgressFinish() {
        @Override
        public void onFinish() {
            resetSuccess(resetEntity);
        }
    };

    private TextWatcher firstPswTextWatcher = new TextWatcher() {


        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            // 输入的内容变化的监听
            if (s.length() > 0) {
                hasFirstPassword = true;
            } else {
                hasFirstPassword = false;
            }

            checkNextStepButtonToClick();
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count,
                                      int after) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            // 输入后的监听
            if (s.length() > 0) {
                hasFirstPassword = true;
            } else {
                hasFirstPassword = false;
            }
            checkNextStepButtonToClick();
        }
    };

    /**
     * @des: 检查注册按钮是否可以点击
     * @params:
     * @return:
     */

    private void checkNextStepButtonToClick() {

        if (hasFirstPassword) {
            confirmBtn.setSelected(true);
            confirmBtn.setClickable(true);
        } else {
            confirmBtn.setSelected(false);
            confirmBtn.setClickable(false);
        }
    }
}
