package com.czur.cloud.ui.starry.meeting.fragment

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.os.Build
import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.ToastUtils
import com.czur.cloud.R
import com.czur.cloud.event.BaseEvent
import com.czur.cloud.event.EventType
import com.czur.cloud.event.StarryUserListEvent
import com.czur.cloud.netty.bean.StarryRecivedUser
import com.czur.cloud.preferences.StarryPreferences
import com.czur.cloud.ui.base.CzurCloudApplication
import com.czur.cloud.ui.starry.activity.StarryActivity
import com.czur.cloud.ui.starry.adapter.StarryCompanyListContactAdapter
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.component.LetterViewNew
import com.czur.cloud.ui.starry.meeting.MeetingMainActivity
import com.czur.cloud.ui.starry.meeting.base.FloatFragment
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.meeting.bean.UserStatus
import com.czur.cloud.ui.starry.meeting.common.UserHandler
import com.czur.cloud.ui.starry.meeting.common.UserHandler.accountNo
import com.czur.cloud.ui.starry.meeting.model.MeetingModel
import com.czur.cloud.ui.starry.model.MeetingMember
import com.czur.cloud.ui.starry.utils.Tools
import com.czur.cloud.ui.starry.utils.singleClick
import com.czur.cloud.ui.starry.viewmodel.StarryContactViewModel
import com.czur.cloud.ui.starry.viewmodel.StarryViewModel
import com.czur.czurutils.log.logI
import kotlinx.android.synthetic.main.starry_activity_company_list_contacts.contact_add_btn
import kotlinx.android.synthetic.main.starry_activity_company_list_contacts.contact_new_call_btn
import kotlinx.android.synthetic.main.starry_activity_company_list_contacts.contacts_search_rl
import kotlinx.android.synthetic.main.starry_activity_company_list_contacts.contacts_selected_count_tv
import kotlinx.android.synthetic.main.starry_activity_company_list_contacts.letter_view
import kotlinx.android.synthetic.main.starry_activity_company_list_contacts.msg_top_select_cancel
import kotlinx.android.synthetic.main.starry_activity_company_list_contacts.msg_top_select_title
import kotlinx.android.synthetic.main.starry_activity_company_list_contacts.recycler_view_contacts
import kotlinx.android.synthetic.main.starry_activity_company_list_contacts.user_back_btn
import kotlinx.android.synthetic.main.starry_activity_company_list_contacts.user_back_btn_bg
import kotlinx.coroutines.delay
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

private const val TAG = "MeetingCompanyListContactsFragment"

// * 某个企业选择人员列表
class MeetingCompanyListContactsFragment : FloatFragment() {

    override fun getLayoutId() = R.layout.starry_activity_company_list_contacts

    private val viewModel by lazy {
        ViewModelProvider(StarryActivity.mainActivity ?: this).get(StarryViewModel::class.java)
    }

    private val contactViewModel by lazy {
        ViewModelProvider(
            StarryActivity.mainActivity ?: this
        ).get(StarryContactViewModel::class.java)
    }

    private val linearLayoutManager by lazy { LinearLayoutManager(requireContext()) }

    private val mAdapter by lazy {
        StarryCompanyListContactAdapter(requireContext())
    }

    private var selectMaxCountNumber = MeetingModel.memberLimitCount ?: UserHandler.portLimit
    private var preClassName = ""
    private var isMaxSelectedCount = false  //是否为当前选中的最大值？
    private var callBtnClicked = false
    private val selectType by lazy {
        viewModel.getSelectType() ?: StarryConstants.STARRY_SELECT_TYPE_START
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    override fun onEvent(event: BaseEvent) {
        when (event.eventType) {
            // 接收到的会议结束指令-会议结束
            // 接收到的离开会议室指令--被移除
            EventType.STARRY_MEETING_CMD_STOP,
            EventType.STARRY_MEETING_CMD_REMOVE -> {
                dismiss()
            }

            EventType.STARRY_ROOM_USER_LIST -> {
                if (selectType == StarryConstants.STARRY_SELECT_TYPE_ADD) {

                    val newUserListEvent = event as StarryUserListEvent
                    val bean = newUserListEvent.params
                    val replyBean = bean.body.reply
                    launch {
                        val userJson = replyBean.users as ArrayList<StarryRecivedUser>
//                        logI("StarryCompanyListContactsActivity.userJson=${userJson}")
                        val isCheckedMapTmp = LinkedHashMap<String, String>()
                        userJson.forEach {
                            if (it.status == UserStatus.STATUS_CALLING ||
                                it.status == UserStatus.STATUS_TIMEOUT ||
                                it.status == UserStatus.STATUS_JOINED ||
                                it.status == UserStatus.STATUS_OFFLINE ||
                                it.status == UserStatus.STATUS_IOS_HOLD ||
                                it.status == UserStatus.STATUS_HOLD_ON
                            ) {
                                isCheckedMapTmp[it.meetingAccout] = it.name ?: ""
                            }
                        }
//                        logI("StarryCompanyListContactsActivity.isCheckedMapTmp=${isCheckedMapTmp}")
                        contactViewModel.isDisableCheckedMap.value = isCheckedMapTmp
                        val checkedList = ArrayList<String>()
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                            isCheckedMapTmp.forEach { (t, _) ->
                                checkedList.add(t)
                            }
                            mAdapter.setDisableCheckedAndClearOld(checkedList)
                        }
                        refreshSelectTv(mAdapter.getCheckMap(), false)
                    }
                }
            }

            else -> {
            }
        }
    }

    override fun initView() {
        super.initView()

        logI("${TAG}.initViews")

        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }

        val accountNo = StarryPreferences.getInstance().accountNo

        // top bar
        user_back_btn?.visibility = View.INVISIBLE
        user_back_btn_bg?.visibility = View.VISIBLE
        user_back_btn_bg?.singleClick {
            dismiss()
        }
        // 取消
        msg_top_select_cancel?.singleClick {
            dismiss()
        }

        // ui.starry.activity.StarryCompanyListActivity
//        preClassName = intent.getStringExtra(StarryConstants.STARRY_PRE_ACTIVITY) ?: ""
        preClassName = viewModel.starryPrePageName
        if (preClassName.isNotEmpty()) {
            msg_top_select_cancel?.visibility = View.GONE
            user_back_btn?.visibility = View.VISIBLE
        }

        if (selectMaxCountNumber < 1) {
            selectMaxCountNumber = UserHandler.portLimit
        }
        // Title
        msg_top_select_title?.text =
            String.format(
                getString(R.string.starry_company_list_contacts_title_num), "1"
            )
        contacts_selected_count_tv?.text =
            String.format(
                getString(R.string.starry_company_list_contacts_selected_count),
                "1",
                selectMaxCountNumber.toString()
            )

        Tools.setViewButtonEnable(contact_new_call_btn, false)

        launch {
            var list = viewModel.currentContactsList.value?.toMutableList() as ArrayList
            delay(200)
            mAdapter.setListContacts(list)
            list = mAdapter.getListContacts()
            viewModel.currentContactsSortList.postValue(list)
        }

        // list
        recycler_view_contacts?.visibility = View.VISIBLE

        recycler_view_contacts?.apply {
            setHasFixedSize(true)
            layoutManager = linearLayoutManager
            adapter = mAdapter
        }

        mAdapter.setOnItemClickListener(object : StarryCompanyListContactAdapter.OnClickListener {
            override fun onclickSel(position: Int, checkMap: LinkedHashMap<String, String>) {
                logI("setOnItemClickListener.onclickSel.position=${position}")
                refreshSelectTv(checkMap)
            }
        })

        recycler_view_contacts?.postDelayed(kotlinx.coroutines.Runnable {
            recycler_view_contacts?.requestFocus()
            (recycler_view_contacts.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(
                0,
                0
            )
        }, 100)

        // 字母索引定位
        letter_view?.initViewNew(mAdapter.getCharacterList())
        letter_view?.setCharacterListener(object : LetterViewNew.CharacterClickListener {
            override fun clickCharacter(character: String?) {
                character?.let { mAdapter.getScrollPosition(it) }?.let {
                    linearLayoutManager.scrollToPositionWithOffset(it, 0)
                }
            }

            override fun clickArrow() {
                linearLayoutManager.scrollToPositionWithOffset(0, 0)
            }
        })

        // 搜索
        contacts_search_rl?.setOnClickListener {
            if (MeetingModel.currentOrientationStatus == Configuration.ORIENTATION_LANDSCAPE) {
                MeetingSearchCompanyContactFragment().show(
                    ByScreenParams(),
                    AnimDirection.RIGHT
                )
            } else {
                MeetingSearchCompanyContactFragment().show(
                    ByScreenParams(),
                    AnimDirection.BOTTOM
                )
            }
        }

//        // 发起会议
//        contact_new_call_btn?.setOnClickListener {
//            logI("${TAG}.contact_new_call_btn")
//            callBtnClicked = true
//            viewModel.getEnterpriseMembers{
//                checkCompanyPerson()
//            }
//
//        }

        // 会议中添加成员
        contact_add_btn?.setOnClickListener {
            logI("${TAG}.contact_add_btn")
            if (viewModel.currentCompanyModel.value?.expired == true) {//当前选择的企业过期
                ToastUtils.showShort(R.string.starry_invite_msg_no_permission)
                return@setOnClickListener
            }

            if (!isAdded) {
                return@setOnClickListener
            }

            Tools.setViewButtonEnable(contact_add_btn, false)
            // 竖屏下,有loading;横屏下,无loading
            if (requireActivity().resources.configuration.orientation == Configuration.ORIENTATION_PORTRAIT) {
                showProgressDialog()
            }
            val enterpriseList = viewModel.enterpriseList.value ?: listOf()
            // 要先调佣一下getCheckedMemberInfo,
            // 在刷新getEnterpriseMembers前,先 获取选中的用户的信息
            contactViewModel.getCheckedMemberInfo(enterpriseList)

            viewModel.getEnterpriseMembers {
                checkCompanyPersonAdd()
                if (isAdded) {
                    Tools.setViewButtonEnable(contact_add_btn, true)
                    hideProgressDialog()
                }
            }
        }

        // 设置选中的项
        if (selectType == StarryConstants.STARRY_SELECT_TYPE_ADD) {
            // 设置选中的成员
            val checkedList = ArrayList<String>()
            contactViewModel.isDisableCheckedMap.value?.forEach { (t, u) ->
                checkedList.add(t)
            }
            mAdapter.setDisableChecked(checkedList)

            contact_new_call_btn?.visibility = View.GONE
            contact_add_btn?.visibility = View.VISIBLE

        } else {
            // 设置选中的自己
            val checkedList = ArrayList<String>()
            checkedList.add(accountNo)
            mAdapter.setDisableChecked(checkedList)

            contact_new_call_btn?.visibility = View.VISIBLE
            contact_add_btn?.visibility = View.GONE
        }

        // 初始化时，把原来的isCheckMap内容带入
        if (contactViewModel.isCheckedMap.value != null && contactViewModel.isCheckedMap.value?.size ?: 0 > 0) {
            // 设置原有的选择项
            val checkedListAll = ArrayList<String>()
            contactViewModel.isCheckedMap.value?.forEach { (t, u) ->
                checkedListAll.add(t)
            }
            mAdapter.setAllreadyChecked(checkedListAll)
        }

        val tempMap1 = contactViewModel.tempCheckedMap.value
        val tempMap2 = mAdapter.getCheckMap()
        val tempMap = if (tempMap1.isNullOrEmpty()) tempMap2 else tempMap1.plus(tempMap2)
        if (tempMap.isNotEmpty()) {
            contactViewModel.tempCheckedMap.value = tempMap as LinkedHashMap<String, String>?
        }
//        logI("${TAG}.initViews.contactViewModel.tempCheckedMap.value=${contactViewModel.tempCheckedMap.value}")

    }

    /**
     * 刷新选择人数textview
     */
    fun refreshSelectTv(checkMap: LinkedHashMap<String, String>, isToast: Boolean = true) {
        if (isToast && isMaxSelectedCount && (checkMap.size >= selectMaxCountNumber)) {
            ToastUtils.showLong(
                String.format(
                    getString(R.string.starry_company_list_contacts_selected_more),
                    (selectMaxCountNumber - 1).toString()
                )
            )
        }
        isMaxSelectedCount = checkMap.size >= selectMaxCountNumber
        contactViewModel.tempCheckedMap.value = checkMap
    }

    // 会议中添加成员检查
    private fun checkCompanyPersonAdd() {
        contactViewModel.isCheckedMap.value = contactViewModel.tempCheckedMap.value

        val members = ArrayList<MeetingMember>()
        val meetingAccountsList = ArrayList<String>()
        contactViewModel.isCheckedMap.value?.forEach { (t, u) ->
            if (t != accountNo) {
                meetingAccountsList.add(t)
                members.add(MeetingMember(t, u))
            }
        }
        contactViewModel.isDisableCheckedMap.value?.forEach { (dis_k, dis_v) ->
            if (meetingAccountsList.contains(dis_k)) {
                meetingAccountsList.remove(dis_k)

                val member = MeetingMember(dis_k, dis_v)
                members.remove(member)
            }
        }


        val enterpriseList = viewModel.enterpriseList.value ?: listOf()
        // 没有返回的数据,则认为是弱网环境,提示网络错误
        if (enterpriseList.isEmpty()) {
            ToastUtils.showLong(R.string.starry_network_error_msg)
            return
        }

//        问题: java.lang.IllegalStateException: Fragment <...> not attached to a context.
//        根源: fragment 没有和 activity绑定, context为空.
        if (!this.isAdded) {
            return
        }
        // 检查成员的有效性
        val context = requireContext() ?: CzurCloudApplication.getApplication().applicationContext
        if (!contactViewModel.checkValidMembers(context, enterpriseList)) {
            return
        }

        viewModel.inviteMemberPC(meetingAccountsList.toList())

        if (preClassName.isNotEmpty()) {
            viewModel.meetingCompanyListFragment?.dismiss()
        }

        dismiss()
    }

    // 发起会议和会议中添加成员，都需要检查成员的有效性（是否在企业中，是否已注销等）
    private fun checkValidMembers(members: ArrayList<MeetingMember>): Boolean {
        logI("checkValidMembers.members=${members}")

        //邀请/发起会议等选择企业成员页面，如果企业成员已退出toast提示：
        //一个人：xx 企业信息有误
        //2人及以上：xx等x人企业信息有误
        var count = 0
        var msgName = ""
        var isInEnterprise = false
        members.forEach { it1 ->
            val meetingNo = it1.accountNo.toString()
            val enterpriseList = viewModel.enterpriseList.value ?: listOf()
            enterpriseList.forEach { it2 ->
                val memberList = it2.membersList ?: listOf()
                val flagInEnterprise = memberList.count {
                    it.meetingAccout == meetingNo
                } > 0
                logI("${TAG}.contact_new_call_btn.meetingNo=${meetingNo};isInEnterprise=${isInEnterprise}")
                if (flagInEnterprise) {
                    isInEnterprise = true
                    return@forEach
                }
            }

            if (!isInEnterprise) {
                count++
                if (count == 1) {
                    msgName = it1.contactID ?: meetingNo
                }
            }
        }

        if (count == 1) {
            ToastUtils.showLong(
                String.format(
                    getString(R.string.starry_contact_startmeeting_check_msg),
                    msgName
                )
            )
            return false
        }
        if (count > 1) {
            ToastUtils.showLong(
                String.format(
                    getString(R.string.starry_contact_startmeeting_check2_msg),
                    msgName, count
                )
            )
            return false
        }

        return true
    }

    private fun checkCompanyPerson() {
        val accountNo = StarryPreferences.getInstance().accountNo
        contactViewModel.isCheckedMap.value = contactViewModel.tempCheckedMap.value

        val members = ArrayList<MeetingMember>()
        contactViewModel.isCheckedMap.value?.forEach { (t, u) ->
            if (t != accountNo) {
                members.add(MeetingMember(t, u))
            }
        }

        // 检查成员的有效性
        if (!checkValidMembers(members)) {
            return
        }

        val jsonParam = viewModel.startNewMeeting(members)

        val i = Intent(requireContext(), MeetingMainActivity::class.java)
        i.putExtra(MeetingMainActivity.KEY_BOOT_DATA, jsonParam)
        i.putExtra(MeetingMainActivity.KEY_BOOT_TYPE, MeetingMainActivity.BOOT_TYPE_START)
        startActivity(i)

        if (preClassName.isNotEmpty()) {
//            ActivityUtils.finishActivity(StarryCompanyListActivity::class.java)
        }
        dismiss()

    }

    override fun initData() {
        super.initData()
        contactViewModel.isDisableCheckedMap.observe(this) {
            // 设置选中的项
            if (selectType == StarryConstants.STARRY_SELECT_TYPE_ADD) {
                // 设置选中的成员
                val checkedList = ArrayList<String>()
                contactViewModel.isDisableCheckedMap.value?.forEach { (t, u) ->
                    checkedList.add(t)
                }
                mAdapter.setDisableChecked(checkedList)
//                logI("isDisableCheckedMap.observe.checkedList=${checkedList}")
            }
//            logI("isDisableCheckedMap.observe.checkedList=${it}")
        }

        viewModel.currentContactsList.observe(this) {
            var list = it?.toMutableList() as ArrayList
            mAdapter.setListContacts(list)
            list = mAdapter.getListContacts()
            viewModel.currentContactsSortList.postValue(list)
        }

        contactViewModel.tempCheckedMap.observe(this) {
            val checkCount = it.size ?: 1
            if (checkCount >= selectMaxCountNumber) {
                mAdapter.setSelectedMore(false)
            } else {
                mAdapter.setSelectedMore(true)
            }

            msg_top_select_title?.text =
                String.format(
                    getString(R.string.starry_company_list_contacts_title_num),
                    checkCount.toString()
                )
            contacts_selected_count_tv?.text =
                String.format(
                    getString(R.string.starry_company_list_contacts_selected_count),
                    checkCount.toString(),
                    selectMaxCountNumber.toString()
                )

            if (selectType == StarryConstants.STARRY_SELECT_TYPE_ADD) {
                if (it.size > contactViewModel.isDisableCheckedMap.value?.size ?: 0) {
                    Tools.setViewButtonEnable(contact_add_btn, true)
                } else {
                    Tools.setViewButtonEnable(contact_add_btn, false)
                }
            } else {
                if (checkCount > 1) {
                    Tools.setViewButtonEnable(contact_new_call_btn, true)
                } else {
                    Tools.setViewButtonEnable(contact_new_call_btn, false)
                }
            }

            // 初始化时，把原来的isCheckMap内容带入
            if (contactViewModel.tempCheckedMap.value != null && contactViewModel.tempCheckedMap.value?.size ?: 0 > 0) {
                // 设置原有的选择项
                val checkedListAll = ArrayList<String>()
                contactViewModel.tempCheckedMap.value?.forEach { (t, u) ->
                    checkedListAll.add(t)
                }
                mAdapter.setAllreadyChecked(checkedListAll)
                isMaxSelectedCount = it?.size ?: 0 >= selectMaxCountNumber
            }
        }

        MeetingModel.isMeetingLocked.observe(this) {
            if (it && !MeetingModel.isAdmin()) {
                dismiss()
            }
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
    }

    override fun onDetach() {
        super.onDetach()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

    override fun handleBackPressed(): Boolean {
        //处理自己的逻辑
        logI("${TAG}.handleBackPressed-关闭")
        dismiss()
        return true
    }

}
