package com.czur.cloud.ui.camera.gallery;

import android.content.Context;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;

import androidx.viewpager.widget.PagerAdapter;

import com.blankj.utilcode.util.ImageUtils;
import com.czur.cloud.entity.realm.PageEntity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * viewpager适配器
 */
public class ImagePagerAdapter extends PagerAdapter {

    private HashMap<Integer, ZoomImageView> viewMap = new HashMap<>();
    private List<PageEntity> mDataList = new ArrayList<>();
    private Context context;

     ImagePagerAdapter(Context context, HashMap<Integer, ZoomImageView> viewMap, List<PageEntity> mDataList) {
        this.context = context;
        this.viewMap = viewMap;
        this.mDataList = mDataList;
    }


    public void refreshData( int position){
        viewMap.remove(position);
        notifyDataSetChanged();
    }
    @Override
    public int getCount() {
        return mDataList.size();
    }

    @Override
    public void setPrimaryItem(ViewGroup container, int position, Object object) {
        super.setPrimaryItem(container, position, object);
        ZoomImageView zoomImage = viewMap.get(position);
        ((GalleryViewPager) container).setZoomView(zoomImage);
    }

    @Override
    public Object instantiateItem(ViewGroup container, int position) {
        ZoomImageView zoomImage = viewMap.get(position);
        if (zoomImage == null) {
            zoomImage = setImageToIndex(position);
        }
        container.addView(zoomImage);
        return zoomImage;
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
        Log.i("czur", "remove: " + position, null);
        container.removeView((View) object);
        ZoomImageView zoomImage = viewMap.get(position);
        if (zoomImage != null) {
            zoomImage.setImageBitmap(null);
            viewMap.remove(position);
        }
    }

    public HashMap<Integer, ZoomImageView>  getViewMap(){
         return viewMap;
    }
    private ZoomImageView setImageToIndex(int index) {
        ZoomImageView zoomImage = new ZoomImageView(context);
        String path = mDataList.get(index).getPicUrl();
        zoomImage.setImageBitmap(ImageUtils.getBitmap(path));
        viewMap.put(index, zoomImage);
        return zoomImage;
    }

    @Override
    public boolean isViewFromObject(View view, Object object) {
        return view == object;
    }

    @Override
    public int getItemPosition(Object object) {
        return POSITION_NONE;
    }
}
