package com.czur.cloud.ui.starry.meeting.viewmodel

import androidx.lifecycle.ViewModel
import com.czur.cloud.ui.starry.meeting.base.FloatFragment
import com.czur.cloud.ui.starry.meeting.baselib.utils.DifferentLiveData
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.meeting.bean.vo.Member
import com.czur.cloud.ui.starry.meeting.common.ONE_SECOND
import com.czur.cloud.ui.starry.model.StarryAddressBookModel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.receiveAsFlow

/**
 * Created by 陈丰尧 on 2021/6/16
 */
private const val HIDE_DELAY = 3 * ONE_SECOND
class ControlBarViewModel : ViewModel() {
    val userIdle = DifferentLiveData<Boolean>()

    var memberListFragment: FloatFragment? = null
    var chartFragment: FloatFragment? = null
    var detailFragment: FloatFragment? = null
    var addToFragment: FloatFragment? = null
    var detailFragmentAddressBookModel: StarryAddressBookModel? = null
    var detailFragmentMember: Member? = null

    private var userClickHide = false

    private val operatorChannel = Channel<Long>()

    init {
        launch {
            operatorChannel.receiveAsFlow()
                .onEach {
                    userIdle.value = false
                }.collectLatest {
                    // collectLatest 方法当有新的值时 会自动丢弃之前未完成的操作
                    delay(HIDE_DELAY)
                    userIdle.value = true
                    userClickHide = true
                }

            operatorChannel.trySend(System.currentTimeMillis()).isSuccess
        }
    }

    /**
     * 用户有操作时调用
     */
    fun onUserOperation() {
//        userIdle.postValue(false)
        if (userIdle.value == true) {
            userIdle.postValue(false)
        }else{
            userIdle.postValue(true)
        }
        operatorChannel.trySend(System.currentTimeMillis())
    }

}