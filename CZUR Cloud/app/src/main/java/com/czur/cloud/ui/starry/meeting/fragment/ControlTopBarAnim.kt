package com.czur.cloud.ui.starry.meeting.fragment

import android.view.View
import com.czur.cloud.ui.starry.meeting.utils.anim.*

private const val TAG = "ControlTopBarAnim"

class ControlTopBarAnim(
    private val controlActionBar: View
) {
    // 操作栏的动画属性
    private val controlBarAnim: AnimParam by lazy {
        AnimParam.flyFromTop(controlActionBar)
    }

    /**
     * 更新动画状态
     * @param userIdle: 用户操作是否空闲
     * @param floatShow: 是否有悬浮窗正在显示
     */
    fun updateAnim(userIdle: Boolean) {
        if (userIdle) {
            controlBarAnim.hide()
        } else {
            controlBarAnim.show()
        }
    }

}