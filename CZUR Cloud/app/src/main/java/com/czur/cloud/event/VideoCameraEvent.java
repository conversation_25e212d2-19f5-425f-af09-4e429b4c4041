package com.czur.cloud.event;

public class VideoCameraEvent extends BaseEvent {


	private String camera;
	private String equipmentUid;
	public VideoCameraEvent(EventType eventType,  String equipmentUid,String camera) {
		super(eventType);
		this.camera=camera;
		this.equipmentUid=equipmentUid;
	}
	public String getCamera() {
		return camera;
	}

	public String getEquipmentUid() {
		return equipmentUid;
	}

	@Override
	public boolean match(Object obj) {
		return true;
	}
}
