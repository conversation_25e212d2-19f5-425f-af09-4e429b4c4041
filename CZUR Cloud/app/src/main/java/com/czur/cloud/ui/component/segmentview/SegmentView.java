package com.czur.cloud.ui.component.segmentview;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.ColorStateList;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.czur.cloud.R;

public class SegmentView extends LinearLayout{
    private final int SEGMENUCOUNT = 4;
    private TextView leftTextView;
    private TextView rightTextView;
    private TextView leftTextView2;
    private TextView leftTextView3;
    private onSegmentViewClickListener segmentListener;
    private TextView bgTextView;

    // 这是代码加载ui必须重写的方法
    public SegmentView(Context context) {
        super(context);
        initView();
    }

    public interface OnSegmentControlClickListener{
        void onSegmentControlClick(int index);
    }

    // 这是在xml布局使用必须重写的方法
    public SegmentView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    private void initView() {
        leftTextView = new TextView(getContext());
        rightTextView = new TextView(getContext());
        leftTextView2 = new TextView(getContext());
        leftTextView3 = new TextView(getContext());
        bgTextView = new TextView(getContext());

        // 设置textview的布局宽高并设置为weight属性都为1
        int widthPixels = 200;//(750 / 120 = 6.25)
        WindowManager windowManager = (WindowManager) getContext().getSystemService(Context.WINDOW_SERVICE);
        if (windowManager != null) {
            DisplayMetrics outMetrics = new DisplayMetrics();
            windowManager.getDefaultDisplay().getMetrics(outMetrics);
            widthPixels = outMetrics.widthPixels/6;
        }
        leftTextView.setLayoutParams(new LayoutParams(widthPixels, LayoutParams.WRAP_CONTENT, 1));
        rightTextView.setLayoutParams(new LayoutParams(widthPixels, LayoutParams.WRAP_CONTENT, 1));
        leftTextView2.setLayoutParams(new LayoutParams(widthPixels, LayoutParams.WRAP_CONTENT, 1));
        leftTextView3.setLayoutParams(new LayoutParams(widthPixels, LayoutParams.WRAP_CONTENT, 1));
        bgTextView.setLayoutParams(new LayoutParams(widthPixels*SEGMENUCOUNT, LayoutParams.WRAP_CONTENT, 1));

        // 初始化的默认文字  全部 日 周 月
        leftTextView.setText(R.string.segment_all);
        leftTextView2.setText(R.string.segment_day);
        leftTextView3.setText(R.string.segment_week);
        rightTextView.setText(R.string.segment_month);
        bgTextView.setText(R.string.segment_all);

        // 实现不同的按钮状态，不同的颜色
        @SuppressLint("ResourceType") ColorStateList csl = getResources().getColorStateList(R.drawable.segment_text_color_selector);
        leftTextView.setTextColor(csl);
        leftTextView2.setTextColor(csl);
        leftTextView3.setTextColor(csl);
        rightTextView.setTextColor(csl);

        // 设置textview的内容位置居中
        leftTextView.setGravity(Gravity.CENTER);
        leftTextView2.setGravity(Gravity.CENTER);
        leftTextView3.setGravity(Gravity.CENTER);
        rightTextView.setGravity(Gravity.CENTER);
        bgTextView.setGravity(Gravity.CENTER);

        // 设置textview的内边距
        leftTextView.setPadding(15, 4, 15, 5);
        leftTextView2.setPadding(15, 4, 15, 5);
        leftTextView3.setPadding(15, 4, 15, 5);
        rightTextView.setPadding(15, 4, 15, 5);
        bgTextView.setPadding(15, 4, 15, 5);

        // 设置文字大小
        setSegmentTextSize(18);

        // 设置背景资源
        leftTextView.setBackgroundResource(R.drawable.segment_left_background);
        leftTextView2.setBackgroundResource(R.drawable.segment_center_background);
        leftTextView3.setBackgroundResource(R.drawable.segment_center_background);
        rightTextView.setBackgroundResource(R.drawable.segment_right_background);

        bgTextView.setBackgroundResource(R.drawable.segment_bg_background);
        bgTextView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 19);

        // 默认左侧textview为选中状态
        leftTextView.setSelected(true);


        // 加入textview
        LinearLayout layout = new LinearLayout(getContext());
        layout.setOrientation(LinearLayout.HORIZONTAL);
        LinearLayout.LayoutParams lLayoutlayoutParams = new LinearLayout.LayoutParams(
                widthPixels*SEGMENUCOUNT, ViewGroup.LayoutParams.WRAP_CONTENT);
        lLayoutlayoutParams.gravity=Gravity.CENTER;
        layout.setLayoutParams(lLayoutlayoutParams);
        layout.setPadding(2,2,2,2);
//        layout.setGravity(Gravity.CENTER);
        layout.addView(leftTextView);
        layout.addView(leftTextView2);
        layout.addView(leftTextView3);
        layout.addView(rightTextView);

        //new layout
        LinearLayout bglayout = new LinearLayout(getContext());
        LinearLayout.LayoutParams bglLayoutlayoutParams = new LinearLayout.LayoutParams(
                widthPixels*SEGMENUCOUNT, ViewGroup.LayoutParams.WRAP_CONTENT);
        bglayout.setLayoutParams(bglLayoutlayoutParams);
        bglayout.addView(bgTextView);

        RelativeLayout relativeLayout = new RelativeLayout(getContext());
        relativeLayout.setLayoutParams(new RelativeLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));

        relativeLayout.addView(bglayout);
        relativeLayout.addView(layout);

        this.removeAllViews();
        this.addView(relativeLayout);

        this.invalidate();//重新draw()

        leftTextView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (leftTextView.isSelected()) {
                    return;
                }
                leftTextView.setSelected(true);
                leftTextView2.setSelected(false);
                leftTextView3.setSelected(false);
                rightTextView.setSelected(false);
                if (segmentListener != null) {
                    segmentListener.onSegmentViewClick(leftTextView, 0);
                }
            }
        });

        leftTextView2.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (leftTextView2.isSelected()) {
                    return;
                }
                leftTextView.setSelected(false);
                leftTextView2.setSelected(true);
                leftTextView3.setSelected(false);
                rightTextView.setSelected(false);
                if (segmentListener != null) {
                    segmentListener.onSegmentViewClick(leftTextView2, 1);
                }
            }
        });

        leftTextView3.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (leftTextView3.isSelected()) {
                    return;
                }
                leftTextView.setSelected(false);
                leftTextView2.setSelected(false);
                leftTextView3.setSelected(true);
                rightTextView.setSelected(false);
                if (segmentListener != null) {
                    segmentListener.onSegmentViewClick(leftTextView3, 2);
                }
            }
        });

        rightTextView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (rightTextView.isSelected()) {
                    return;
                }
                rightTextView.setSelected(true);
                leftTextView2.setSelected(false);
                leftTextView3.setSelected(false);
                leftTextView.setSelected(false);
                if (segmentListener != null) {
                    segmentListener.onSegmentViewClick(rightTextView, 3);
                }
            }
        });

    }

    /**
     * 设置字体大小
     *
     * @param dp
     */
    private void setSegmentTextSize(int dp) {
        leftTextView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, dp);
        leftTextView2.setTextSize(TypedValue.COMPLEX_UNIT_DIP, dp);
        leftTextView3.setTextSize(TypedValue.COMPLEX_UNIT_DIP, dp);
        rightTextView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, dp);
    }

    /**
     * 手动设置选中的状态
     *
     * @param i
     */
    public void setSelect(int i) {
        if (i == 0) {
            leftTextView.setSelected(true);
            leftTextView2.setSelected(false);
            leftTextView3.setSelected(false);
            rightTextView.setSelected(false);
        }else if (i == 1) {
                leftTextView.setSelected(false);
                leftTextView2.setSelected(true);
                leftTextView3.setSelected(false);
                rightTextView.setSelected(false);
        }else if (i == 2) {
            leftTextView.setSelected(false);
            leftTextView2.setSelected(false);
            leftTextView3.setSelected(true);
            rightTextView.setSelected(false);
        } else {
            leftTextView.setSelected(false);
            leftTextView2.setSelected(false);
            leftTextView3.setSelected(false);
            rightTextView.setSelected(true);
        }
    }

    /**
     * 设置控件显示的文字
     *
     * @param text
     * @param position
     */
    public void setSegmentText(CharSequence text, int position) {
        if (position == 0) {
            leftTextView.setText(text);
        }
        if (position == 1) {
            rightTextView.setText(text);
        }
    }

    // 定义一个接口接收点击事件
    public interface onSegmentViewClickListener {
        public void onSegmentViewClick(View view, int postion);
    }

    public void setOnSegmentViewClickListener(onSegmentViewClickListener segmentListener) {
        this.segmentListener = segmentListener;
    }
}