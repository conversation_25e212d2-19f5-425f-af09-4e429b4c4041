package com.czur.cloud.ui.starry.livedatabus;

import android.app.Dialog;
import android.content.DialogInterface;
import android.os.Handler;

import java.util.LinkedList;
import java.util.List;
import java.util.Queue;

public class DialogQueueUtils {

    private static final String TAG = "DialogQueueUtils";
    private final Queue<Dialog> dialogQueue;

    private Dialog mCurrentDialog = null;//当前显示的Dialog

    public DialogQueueUtils() {
        dialogQueue = new LinkedList<>();
    }


    public void addDialog(List<Dialog> dialogs) {
        for (Dialog dialog : dialogs) {
            if (dialog != null) {
                dialogQueue.offer(dialog);
            }
        }
    }

    public void addDialog(Dialog dialog) {
        if (dialog != null) {
            if (!dialogQueue.contains(dialog)) {
                dialogQueue.offer(dialog);
            }
        }
    }

    // 获取当前dialogQueue的长度
    public int getDialogQueueSize() {
        return dialogQueue.size();
    }

    //检查queue中是否有此元素
    public boolean checkQueueContains(Dialog dialog) {
        if (dialogQueue.contains(dialog)) {
            return true;
        } else {
            return false;
        }
    }

    // 删除掉queen中的dlg
    public void dialogDelete(Dialog dlg) {
        if (dlg != null) {
            if (dialogQueue.contains(dlg)) {
                dialogQueue.remove(dlg);
            }
        }
    }

    public void show() {
        show("first");
    }

    public void show(String type) {
        if (mCurrentDialog == null) {
            //从队列中拿出一个Dialog实例,并从列表中移除
            mCurrentDialog = dialogQueue.poll();
            if (mCurrentDialog != null) {//当队列为空的时候拿出来的会是null
                if ("first".equals(type)) {

                    mCurrentDialog.show();
                    mCurrentDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                        @Override
                        public void onDismiss(DialogInterface dialog) {
                            //这边设置了dismiss监听,在监听回调中再次调用show方法,可以获取下一个弹窗
                            mCurrentDialog = null;
                            show();
                        }
                    });
                } else if ("reshow".equals(type)) {
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            mCurrentDialog.show();
                            mCurrentDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                                @Override
                                public void onDismiss(DialogInterface dialog) {
                                    //这边设置了dismiss监听,在监听回调中再次调用show方法,可以获取下一个弹窗
                                    mCurrentDialog = null;
                                    show("reshow");
                                }
                            });
                        }
                    }, 500);
                }
            }


        }
    }
}
