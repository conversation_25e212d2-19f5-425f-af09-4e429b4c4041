package com.czur.cloud.ui.starry.meeting.base

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.KeyEvent
import android.view.Window
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import com.czur.cloud.common.MobSDKUtils
import com.czur.cloud.ui.base.BaseActivity
import com.czur.cloud.ui.starry.meeting.baselib.listener.KeyDownListener
import com.czur.cloud.ui.starry.meeting.baselib.listener.KeyUpListener
import com.czur.cloud.util.ProgressDialogShowingHelper
import com.czur.czurutils.log.logI

/**
 * Created by 陈丰尧 on 12/29/20
 */
private const val TAG = "BaseActivity"

abstract class BaseActivity : AppCompatActivity() {
    var onDestroyListeners = mutableListOf<OnDestroyListener>()
    private val TAG = BaseActivity::class.java.simpleName
    companion object {
        const val RESULT_PRE = 1
        const val RESULT_FLOW = "resultFlow"

        const val LAYOUT_NONE = 0
    }

    /**
     * Activity真实的类名
     */
    private val realAtyName: String by lazy {
        this::class.java.simpleName
    }

    // 是否禁止后台
    open val banBackGround: Boolean = false
    open val keepScreenOn: Boolean = false
    protected var skipAndFinish = false

    var progressDialogShowingHelper:ProgressDialogShowingHelper? = null
    private val fragments: List<Fragment>
        get() = supportFragmentManager.fragments

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        progressDialogShowingHelper = ProgressDialogShowingHelper()
        if (skipAndFinish()) {
            logI("${realAtyName}skipAndFinish!!!")
            skipAndFinish = true
            finish()
            return
        }
        intent?.let {
            handlePreIntent(it)
        }
        if (keepScreenOn) {
            // 设定屏幕常量
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        }
        initWindow(window)
        val resId = getLayout()
        if (resId > 0) {
            setContentView(resId)
        }
        initViews()
        initListener()
        initData(savedInstanceState)
        Log.i(TAG, String.format("life-%s.onCreate()", javaClass.simpleName))
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
//        intent?.let {
//            handlePreIntent(it)
//        }
//        initData()
        Log.i(TAG, String.format("%s.onNewIntent()", javaClass.simpleName))
    }

    open fun skipAndFinish(): Boolean {
        return false
    }

    open fun handlePreIntent(preIntent: Intent) {}
    open fun initWindow(window: Window) {}
    abstract fun getLayout(): Int
    open fun initViews() {}
    open fun initListener() {}
    open fun initData(savedInstanceState: Bundle? = null) {}

    override fun onPause() {
        super.onPause()
        MobSDKUtils.onPause(this)
        Log.i(TAG, String.format("life-%s.onPause()", javaClass.simpleName))
    }

    override fun onResume() {
        super.onResume()
        MobSDKUtils.onResume(this)
        Log.i(TAG, String.format("life-%s.onResume()", javaClass.simpleName))
    }


    override fun onDestroy() {
        onDestroyListeners.forEach {
            try {
                it.onAtyDestroy()
            } catch (e: Exception) {
                Log.w(TAG, "Error in OnDestroyListener: ${this.localClassName}", e)
            }
        }
        // 清除所有的引用关系
        onDestroyListeners.clear()
        Log.i(TAG, String.format("life-%s.onDestroy()", javaClass.simpleName))
        super.onDestroy()

    }

    /**
     * 注册Activity销毁的回调
     * 因为SDK7的限制, 不能使用Android原生的生命周期回调,
     * 只能根据需求添加对应的生命周期回调
     */
    fun registerOnDestroyListener(listener: OnDestroyListener) {
        onDestroyListeners.add(listener)
    }

    /**
     * 取消注册Activity销毁的回调
     */
    fun unregisterOnDestroyListener(listener: OnDestroyListener) {
        onDestroyListeners.remove(listener)
    }


    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        // 将事件分发给Fragment
        val fragmentKeyDown =
            fragments
                .filter { it is KeyDownListener && it.isVisible }
                .fold(false) { res, it ->
                    (it as KeyDownListener).onKeyDown(keyCode, event) || res
                }
        return if (fragmentKeyDown) fragmentKeyDown else super.onKeyDown(keyCode, event)
    }

    override fun onKeyUp(keyCode: Int, event: KeyEvent?): Boolean {
        val fragmentKeyUp =
            fragments
                .filter { it is KeyUpListener && it.isVisible }
                .fold(false) { res, it ->
                    (it as KeyUpListener).onKeyUp(keyCode, event) || res
                }
        return if (fragmentKeyUp) fragmentKeyUp else super.onKeyUp(keyCode, event)
    }

    override fun onStart() {
        super.onStart()
        Log.i(TAG, String.format("%s.onStart()", javaClass.simpleName))
    }

    override fun onStop() {
        super.onStop()
        Log.i(TAG, String.format("%s.onStop()", javaClass.simpleName))
    }

    fun showProgressDialog(){
        progressDialogShowingHelper?.showProgressDialog(this)
    }

    fun hideProgressDialog(){
        progressDialogShowingHelper?.hideProgressDialog(this)
    }
}

interface OnDestroyListener {
    fun onAtyDestroy()
}