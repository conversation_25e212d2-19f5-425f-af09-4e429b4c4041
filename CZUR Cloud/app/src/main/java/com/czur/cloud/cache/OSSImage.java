package com.czur.cloud.cache;

public class OSSImage {

    private String key;
    private String url;
    private boolean isOriginal;
    private String fileId;
    private boolean isLocal;

    public OSSImage(String key, String url) {
        this.key = key;
        this.url = url;
    }

    public OSSImage(String key, String url, boolean isOriginal, String fileId) {
        this.key = key;
        this.url = url;
        this.isOriginal = isOriginal;
        this.fileId = fileId;
    }

    @Override
    public int hashCode() {
        return key.hashCode();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        OSSImage image = (OSSImage) o;
        return key.equals(image.key);
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public boolean isOriginal() {
        return isOriginal;
    }

    public void setOriginal(boolean original) {
        isOriginal = original;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public boolean isLocal() {
        return isLocal;
    }

    public void setLocal(boolean local) {
        isLocal = local;
    }
}
