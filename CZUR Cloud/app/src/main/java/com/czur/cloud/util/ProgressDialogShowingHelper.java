package com.czur.cloud.util;

import android.app.Activity;
import android.content.DialogInterface;

import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.FragmentTransaction;

import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.ProgressDialogFragment;

import java.util.Timer;
import java.util.TimerTask;

public class ProgressDialogShowingHelper{

    private static final String TAG = BaseActivity.class.getSimpleName();
    protected boolean activityDestroyed = false;
    private int progressDialogRefCount = 0;
    public static boolean isActive = true;

    /**
     * @des: 显示加载
     * @params:
     * @return:
     */
    public void showProgressDialog(AppCompatActivity activity) {
        showProgressDialog(activity,true, false, null, false,false);
    }

    public void showProgressDialog(AppCompatActivity activity,boolean isDark) {
        showProgressDialog(activity, true, false, null, isDark,false);
    }

    public void showProgressDialog(AppCompatActivity activity,boolean isDark, boolean isCancelable) {
        showProgressDialog(activity, isCancelable, false, null, isDark,false);
    }

    public void showProgressDialog(AppCompatActivity activity,String tag) {
        showProgressDialog(activity, true, false, tag, false,false);
    }

    public void showProgressDialogMoment(AppCompatActivity activity){
        showProgressDialog(activity, true, false, null, false,true);
        Timer timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                hideProgressDialog(activity);
            }
        },300);
    }

    /**
     * @des: 初始化一个加载dialog
     * @params:[cancelable, touchable, tag]
     * @return:void
     */

    public void showProgressDialog(AppCompatActivity activity, boolean cancelable, boolean touchable, String tag, boolean isDark, boolean isMomentDialog) {
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (progressDialogRefCount == 0) {
                    FragmentTransaction fragmentTransaction = activity.getSupportFragmentManager().beginTransaction();
                    ProgressDialogFragment prevDialog = (ProgressDialogFragment) activity.getSupportFragmentManager().findFragmentByTag(ProgressDialogFragment.TAG);
                    if (prevDialog != null) {
                        fragmentTransaction.remove(prevDialog);
                    }

                    ProgressDialogFragment progressDialog = ProgressDialogFragment.newInstance(tag);
                    progressDialog.setIsDark(isDark);
                    progressDialog.setCancelable(cancelable);
                    progressDialog.setTouchable(touchable);
                    progressDialog.setMomentDialog(isMomentDialog);
                    progressDialog.setOnCancelListener(new ProgressDialogFragment.OnCancelListener() {
                        @Override
                        public void onCancel(DialogInterface dialog, String tag) {
                            hideProgressDialog(activity,true);
                        }
                    });
                    fragmentTransaction.add(progressDialog, ProgressDialogFragment.TAG);
                    fragmentTransaction.commitAllowingStateLoss();
                }

                progressDialogRefCount++;
            }
        });

    }



    public void hideProgressDialog(AppCompatActivity activity) {
        hideProgressDialog(activity,false);
    }


    /**
     * @des: 隐藏加载
     * @params:
     * @return:
     */
    public void hideProgressDialog(AppCompatActivity activity, final boolean immediately) {
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                progressDialogRefCount--;
                if (immediately || progressDialogRefCount <= 0) {
                    progressDialogRefCount = 0;
                    ProgressDialogFragment fragment = null;
                    try {
                        fragment = (ProgressDialogFragment) activity.getSupportFragmentManager().findFragmentByTag(ProgressDialogFragment.TAG);
                    }catch (Exception e) {
                        //
                    }
                    if (fragment == null) {
                        return;
                    }
                    fragment.getDialog().cancel();
                    FragmentTransaction fragmentTransaction = activity.getSupportFragmentManager().beginTransaction();
                    fragmentTransaction.remove(fragment);
                    fragmentTransaction.commitAllowingStateLoss();
                }
            }
        });

    }

}
