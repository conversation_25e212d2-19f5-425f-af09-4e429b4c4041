package com.czur.cloud.adapter;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.blankj.utilcode.constant.PermissionConstants;
import com.blankj.utilcode.constant.TimeConstants;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.blankj.utilcode.util.UtilsTransActivity;
import com.czur.cloud.R;
import com.czur.cloud.entity.realm.MissedCallEntity;
import com.czur.cloud.ui.auramate.AuraMateRemoteVideoActivity;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import io.realm.Realm;
import io.realm.RealmResults;
import io.realm.Sort;

/**
 * Created by Yz on 2018/3/16
 * Email：<EMAIL>
 */


public class MissedCallAdapter extends RecyclerView.Adapter<ViewHolder> {


    private static final int ITEM_TYPE_NORMAL = 0;
    private Activity mActivity;
    //当前需要显示的所有的图片数据
    private List<MissedCallEntity> datas;
    private LayoutInflater mInflater;
    private final SimpleDateFormat formatter;
    private final SimpleDateFormat formatter1;
    private String time;

    private Realm realm;

    /**
     * 构造方法
     */
    public MissedCallAdapter(Activity activity, List<MissedCallEntity> datas, Realm realm) {
        this.realm = realm;
        this.mActivity = activity;
        this.datas = datas;
        mInflater = LayoutInflater.from(activity);
        formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        formatter1 = new SimpleDateFormat("HH:mm:ss");
    }

    public void refreshData(String time, Realm realm) {
        this.time = time;
        this.datas = realm.where(MissedCallEntity.class).sort("createTime", Sort.DESCENDING).findAll();
        notifyDataSetChanged();

    }


    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return new MissedCallViewHolder(mInflater.inflate(R.layout.item_missed_video, parent, false));
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {
        if (holder instanceof MissedCallViewHolder) {
            final MissedCallViewHolder mHolder = (MissedCallViewHolder) holder;
            mHolder.mItem = datas.get(position);
            mHolder.missedVideoName.setText(String.format(mActivity.getString(R.string.from_aura_mate), mHolder.mItem.getDeviceName()));

            mHolder.missedVideoDate.setText(getFriendlyTimeSpanByNow(mHolder.mItem.getCreateTime()));
            if (mHolder.mItem.getHaveRead() == 0) {
                mHolder.missedVideoPoint.setVisibility(View.VISIBLE);
            } else {
                mHolder.missedVideoPoint.setVisibility(View.INVISIBLE);
            }

            mHolder.callNowBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!TextUtils.isEmpty(mHolder.mItem.getEquipmentUuid())) {
                        openCamera(mHolder.mItem.getEquipmentUuid());
                        RealmResults<MissedCallEntity> all = realm.where(MissedCallEntity.class).findAll();
                        for (MissedCallEntity missedCallEntity : all) {
                            if (missedCallEntity.getId() == mHolder.mItem.getId()) {
                                realm.executeTransaction(new Realm.Transaction() {
                                    @Override
                                    public void execute(Realm realm) {
                                        missedCallEntity.setHaveRead(1);
                                    }
                                });
                                notifyDataSetChanged();
                                break;
                            }
                        }
                    }
                }
            });
        }
    }


    private String getFriendlyTimeSpanByNow(final long millis) {
        long now = System.currentTimeMillis();
        long span = now - millis;

        // 获取当天 00:00
        long wee = getWeeOfToday();
        if (millis >= wee) {
            return String.format(mActivity.getString(R.string.today), millis);
        } else if (millis >= wee - TimeConstants.DAY) {
            return String.format(mActivity.getString(R.string.yesterday), millis);
        } else {
            return formatter.format(new Date(millis));
        }
    }

    private long getWeeOfToday() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTimeInMillis();
    }

    private void openCamera(String equipmentUID) {
        PermissionUtils.permission(PermissionConstants.CAMERA, PermissionConstants.MICROPHONE)
                .rationale(new PermissionUtils.OnRationaleListener() {
                    @Override
                    public void rationale(UtilsTransActivity activity, ShouldRequest shouldRequest) {
                        ToastUtils.showShort(R.string.denied_camera);
                        shouldRequest.again(true);
                    }
                })
                .callback(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(List<String> permissionsGranted) {
                        Intent intent = new Intent(mActivity, AuraMateRemoteVideoActivity.class);
                        intent.putExtra("isCallIn", false);
                        intent.putExtra("udidFrom", equipmentUID);
                        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS);
                        intent.putExtra("equipmentId", equipmentUID);
                        ActivityUtils.startActivity(intent);
                    }

                    @Override
                    public void onDenied(List<String> permissionsDeniedForever,
                                         List<String> permissionsDenied) {
                        ToastUtils.showShort(R.string.denied_camera);
                    }
                })
                .theme(new PermissionUtils.ThemeCallback() {
                    @Override
                    public void onActivityCreate(Activity activity) {
                        ScreenUtils.setFullScreen(activity);
                    }
                })
                .request();
    }

    public int getTotalSize() {
        return datas.size();
    }


    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return datas.size();
    }


    private static class MissedCallViewHolder extends ViewHolder {
        public final View mView;
        MissedCallEntity mItem;
        ImageView missedVideoPoint;
        TextView missedVideoName;
        TextView missedVideoDate;
        TextView callNowBtn;


        MissedCallViewHolder(View itemView) {
            super(itemView);
            mView = itemView;
            missedVideoPoint = (ImageView) itemView.findViewById(R.id.missed_video_point);
            missedVideoName = (TextView) itemView.findViewById(R.id.missed_video_name);
            missedVideoDate = (TextView) itemView.findViewById(R.id.missed_video_date);
            callNowBtn = (TextView) itemView.findViewById(R.id.call_now_btn);


        }


    }


    private class AddTagsHolder extends ViewHolder {

        public final View mView;
        RelativeLayout tagAddItem;


        public AddTagsHolder(View view) {
            super(view);
            mView = view;
            tagAddItem = (RelativeLayout) view.findViewById(R.id.tag_add_item);
        }
    }


    private OnClickListener onClickListener;

    public interface OnClickListener {
        void onTagDeleteClick(MissedCallEntity missedCallModel, int position);
    }

    public void setOnClickListener(OnClickListener onClickListener) {
        this.onClickListener = onClickListener;
    }


}
