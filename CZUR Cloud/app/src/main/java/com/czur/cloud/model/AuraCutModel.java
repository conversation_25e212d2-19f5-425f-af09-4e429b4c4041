package com.czur.cloud.model;

public class AuraCutModel {

    /**
     * code : 1000
     * msg : Success
     * body : {"fileId":"rkttzcrozkggwat","ossKey":"test/9/qaz-147-wsx-258/2018-09-21/process/94fa4117-ee91-4b01-a5c7-fa6b45d950d9_cut376807.jpg","ossKeyUrl":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-09-21/process/94fa4117-ee91-4b01-a5c7-fa6b45d950d9_cut376807.jpg?Expires=1537873052&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=Lp%2F1KJqUBDbeS9P7eBz1zrXSCr8%3D","ossMiddleKeyUrl":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-09-21/process/94fa4117-ee91-4b01-a5c7-fa6b45d950d9_cut376807.jpg?Expires=1537873052&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=P2ilyu5GSi8cuqVpM%2BFhlZqbKuA%3D&x-oss-process=image%2Fresize%2Cm_fixed%2Cw_1080%2Ch_1080","ossSmallKeyUrl":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-09-21/process/94fa4117-ee91-4b01-a5c7-fa6b45d950d9_cut376807.jpg?Expires=1537873052&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=o1LnYUOdxGpTaWp4Ho7mtP8NF54%3D&x-oss-process=image%2Fresize%2Cm_fixed%2Cw_150%2Ch_150","fileSize":"18527"}
     */

    private int code;
    private String msg;
    private BodyBean body;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public BodyBean getBody() {
        return body;
    }

    public void setBody(BodyBean body) {
        this.body = body;
    }

    public static class BodyBean {
        /**
         * fileId : rkttzcrozkggwat
         * ossKey : test/9/qaz-147-wsx-258/2018-09-21/process/94fa4117-ee91-4b01-a5c7-fa6b45d950d9_cut376807.jpg
         * ossKeyUrl : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-09-21/process/94fa4117-ee91-4b01-a5c7-fa6b45d950d9_cut376807.jpg?Expires=1537873052&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=Lp%2F1KJqUBDbeS9P7eBz1zrXSCr8%3D
         * ossMiddleKeyUrl : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-09-21/process/94fa4117-ee91-4b01-a5c7-fa6b45d950d9_cut376807.jpg?Expires=1537873052&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=P2ilyu5GSi8cuqVpM%2BFhlZqbKuA%3D&x-oss-process=image%2Fresize%2Cm_fixed%2Cw_1080%2Ch_1080
         * ossSmallKeyUrl : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-09-21/process/94fa4117-ee91-4b01-a5c7-fa6b45d950d9_cut376807.jpg?Expires=1537873052&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=o1LnYUOdxGpTaWp4Ho7mtP8NF54%3D&x-oss-process=image%2Fresize%2Cm_fixed%2Cw_150%2Ch_150
         * fileSize : 18527
         */

        private String fileId;
        private String ossKey;
        private String ossKeyUrl;
        private String ossMiddleKeyUrl;
        private String ossSmallKeyUrl;
        private String fileSize;

        public String getFileId() {
            return fileId;
        }

        public void setFileId(String fileId) {
            this.fileId = fileId;
        }

        public String getOssKey() {
            return ossKey;
        }

        public void setOssKey(String ossKey) {
            this.ossKey = ossKey;
        }

        public String getOssKeyUrl() {
            return ossKeyUrl;
        }

        public void setOssKeyUrl(String ossKeyUrl) {
            this.ossKeyUrl = ossKeyUrl;
        }

        public String getOssMiddleKeyUrl() {
            return ossMiddleKeyUrl;
        }

        public void setOssMiddleKeyUrl(String ossMiddleKeyUrl) {
            this.ossMiddleKeyUrl = ossMiddleKeyUrl;
        }

        public String getOssSmallKeyUrl() {
            return ossSmallKeyUrl;
        }

        public void setOssSmallKeyUrl(String ossSmallKeyUrl) {
            this.ossSmallKeyUrl = ossSmallKeyUrl;
        }

        public String getFileSize() {
            return fileSize;
        }

        public void setFileSize(String fileSize) {
            this.fileSize = fileSize;
        }
    }
}
