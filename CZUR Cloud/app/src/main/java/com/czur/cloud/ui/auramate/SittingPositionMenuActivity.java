package com.czur.cloud.ui.auramate;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.constant.PermissionConstants;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.UtilsTransActivity;
import com.czur.cloud.R;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.FirstCalibrateEvent;
import com.czur.cloud.event.aurahome.ATCheckDeviceIsOnlineEvent;
import com.czur.cloud.netty.CZURMessageConstants;
import com.czur.cloud.netty.bean.ReceivedMsgBodyBean;
import com.czur.cloud.netty.core.CZURTcpClient;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.progressbar.AuraMateProgressBar;
import com.github.iielse.switchbutton.SwitchView;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;
import java.util.UUID;

/**
 * Created by Yz on 2018/6/1.
 * Email：<EMAIL>
 */
public class SittingPositionMenuActivity extends AuramateBaseActivity implements View.OnClickListener {

    private CloudCommonPopup commonPopup;
    private ImageView userBackBtn;
    private TextView userTitle;
    private SwitchView sittingPositionMenuRemindSwitchBtn;
    private LinearLayout sittingPositionMenuSensitivityRl;
    private RelativeLayout sensitivityHighRl;
    private ImageView sensitivityHighRight;
    private RelativeLayout sensitivityMiddelRl;
    private ImageView sensitivityMiddleRight;
    private RelativeLayout sensitivityLowRl;
    private ImageView sensitivityLowRight;
    private RelativeLayout sittingPositionMenuRecordRl;
    private ImageView volMinus;
    private TextView volTv;
    private ImageView volPlus;
    private AuraMateProgressBar volProgress;
    private boolean isCalibrate;

    private String level;
    private int volume;
    private boolean positionSwitch;


    private int lastVol;
    private String lastSPLevel;
    private long currentTimeMillis;
    private RelativeLayout volumeRl;

    private TextView sittingPositionMenuRecordTitle;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_record_sitting_position_menu);
        initComponent();
        registerEvent();

    }

    @Override
    public boolean PCNeedFinish() {
        return !TextUtils.isEmpty(equipmentId);
    }


    private void initComponent() {
        userBackBtn = (ImageView) findViewById(R.id.user_back_btn);

        userBackBtn = (ImageView) findViewById(R.id.user_back_btn);
        userTitle = (TextView) findViewById(R.id.user_title);
        sittingPositionMenuRemindSwitchBtn = findViewById(R.id.sitting_position_menu_remind_switch_btn);
        sittingPositionMenuSensitivityRl = (LinearLayout) findViewById(R.id.sitting_position_menu_sensitivity_rl);
        volumeRl = (RelativeLayout) findViewById(R.id.volume_rl);

        sensitivityHighRl = (RelativeLayout) findViewById(R.id.sensitivity_high_rl);
        sensitivityHighRight = (ImageView) findViewById(R.id.sensitivity_high_right);
        sensitivityMiddelRl = (RelativeLayout) findViewById(R.id.sensitivity_middel_rl);
        sensitivityMiddleRight = (ImageView) findViewById(R.id.sensitivity_middle_right);
        sensitivityLowRl = (RelativeLayout) findViewById(R.id.sensitivity_low_rl);
        sensitivityLowRight = (ImageView) findViewById(R.id.sensitivity_low_right);
        sittingPositionMenuRecordRl = (RelativeLayout) findViewById(R.id.sitting_position_menu_record_rl);

        volMinus = (ImageView) findViewById(R.id.vol_minus);
        volTv = (TextView) findViewById(R.id.vol_tv);
        volPlus = (ImageView) findViewById(R.id.vol_plus);
        volProgress = (AuraMateProgressBar) findViewById(R.id.vol_progress);

        userTitle = (TextView) findViewById(R.id.user_title);
        userTitle.setText(R.string.aura_home_sitting_remind);

        isCalibrate = getIntent().getBooleanExtra("isCalibrate", false);

        volume = getIntent().getIntExtra("volume", 5);
        level = getIntent().getStringExtra("sittingPositionLevel");
        positionSwitch = getIntent().getBooleanExtra("sittingPositionSwitch", false);

        volTv.setText(volume - 5 + "");
        volProgress.setProgress(volume - 5);
        lastVol = volume;
        lastSPLevel = level;
        checkAll(positionSwitch);
        volProgress.setOnSlideListener(new AuraMateProgressBar.OnSlideListener() {
            @Override
            public void onSlide(int progress, boolean onlyShow) {
                if (!onlyShow) {
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            CZURTcpClient.getInstance().volumeLevel(SittingPositionMenuActivity.this, equipmentId, progress + 5 + "", lastVol + "");
                        }
                    }).start();

                }
                volTv.setText(progress + "");
                lastVol = progress;
            }
        });

        sittingPositionMenuRemindSwitchBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (sittingPositionMenuRemindSwitchBtn.isOpened()) {
                    if (!isCalibrate) {
                        startRecord();
                    } else {
                        CZURTcpClient.getInstance().sittingPositionSensitivitySwitch(SittingPositionMenuActivity.this, equipmentId, CZURMessageConstants.SensitivitySwitch.SENSITIVITY_SWITCH_ON.getSensitivitySwitch(), CZURMessageConstants.SensitivitySwitch.SENSITIVITY_SWITCH_OFF.getSensitivitySwitch());
                    }
                } else {
                    CZURTcpClient.getInstance().sittingPositionSensitivitySwitch(SittingPositionMenuActivity.this, equipmentId, CZURMessageConstants.SensitivitySwitch.SENSITIVITY_SWITCH_OFF.getSensitivitySwitch(), CZURMessageConstants.SensitivitySwitch.SENSITIVITY_SWITCH_ON.getSensitivitySwitch());
                }
                checkShowLevel(sittingPositionMenuRemindSwitchBtn.isOpened());
            }
        });

//        sittingPositionMenuRecordTitle = (TextView) findViewById(R.id.sitting_position_menu_record_title);
        //Jason
//        if (isCalibrate){
//            sittingPositionMenuRecordTitle.setText(R.string.aura_home_sitting_record_re);
//            sittingPositionMenuRecordTitle.setTextColor(getResources().getColor(R.color.blue_33c5e4,null));
//        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case SP_VOLUME:
            case SP_SWITCH:
            case SP_LEVEL:
                setRefreshData(event);
                break;
            case SITTING_POSITION_FIRST_CALIBRATE:
                FirstCalibrateEvent event1 = (FirstCalibrateEvent) event;
                isCalibrate = event1.isSuccess();
                if (!isCalibrate) {
                    if (sittingPositionMenuRemindSwitchBtn.isOpened()) {
                        sittingPositionMenuRemindSwitchBtn.toggleSwitch(false);
                        checkShowLevel(false);
                    }
                } else {
                    if (sittingPositionMenuRemindSwitchBtn.isOpened()) {
                        CZURTcpClient.getInstance().sittingPositionSensitivitySwitch(SittingPositionMenuActivity.this, equipmentId, CZURMessageConstants.SensitivitySwitch.SENSITIVITY_SWITCH_ON.getSensitivitySwitch(), CZURMessageConstants.SensitivitySwitch.SENSITIVITY_SWITCH_OFF.getSensitivitySwitch());
                    }
                }
                break;
            default:
                break;
        }
    }


    private void registerEvent() {
        userBackBtn.setOnClickListener(this);
        sensitivityHighRl.setOnClickListener(this);
        sensitivityMiddelRl.setOnClickListener(this);
        sensitivityLowRl.setOnClickListener(this);
        sittingPositionMenuRecordRl.setOnClickListener(this);
        volMinus.setOnClickListener(this);
        volPlus.setOnClickListener(this);
        setNetListener();
    }

    private void checkAll(boolean isChecked) {
        checkLevel();
        checkShowLevel(isChecked);
        checkSwitch(isChecked);
    }

    private void setRefreshData(BaseEvent event) {
        ReceivedMsgBodyBean.BodyBean.DataBean.DeviceStatusBean statusBean = null;
        ATCheckDeviceIsOnlineEvent onlineEvent = (ATCheckDeviceIsOnlineEvent) event;
        statusBean = onlineEvent.getStatusBean();
        if (statusBean == null) {
            return;
        }

        isCalibrate = statusBean.getHas_calibrated_sp() == 1;
        volume = statusBean.getSp_reminder_sensitivity_volume();
        level = statusBean.getSp_reminder_sensitivity_level();
        positionSwitch = statusBean.getSp_reminder_switch().equals("On");

        volTv.setText(volume - 5 + "");
        volProgress.setProgress(volume - 5);
        lastVol = volume;
        lastSPLevel = level;
        checkAll(positionSwitch);
    }

    /**
     * @des: 是否显示灵敏度
     * @params:
     * @return:
     */

    public void checkShowLevel(boolean isChecked) {
        if (isChecked) {
            sittingPositionMenuSensitivityRl.setVisibility(View.VISIBLE);
            volumeRl.setVisibility(View.VISIBLE);
        } else {
            sittingPositionMenuSensitivityRl.setVisibility(View.GONE);
            volumeRl.setVisibility(View.GONE);
        }
    }

    private void checkSwitch(boolean isChecked) {
        if (isChecked) {
            sittingPositionMenuRemindSwitchBtn.setOpened(true);
        } else {
            sittingPositionMenuRemindSwitchBtn.setOpened(false);
        }
    }

    public void checkLevel() {
        int positionLevel = 0;
        if (level.equals(CZURMessageConstants.SensitivityLevel.SENSITIVITY_LEVEL_LOW.getLevel())) {
            positionLevel = 1;
        } else if (level.equals(CZURMessageConstants.SensitivityLevel.SENSITIVITY_LEVEL_HIGH.getLevel())) {
            positionLevel = 3;
        } else {
            positionLevel = 2;
        }
        switch (positionLevel) {
            case 1:
                sensitivityHighRight.setVisibility(View.GONE);
                sensitivityMiddleRight.setVisibility(View.GONE);
                sensitivityLowRight.setVisibility(View.VISIBLE);
                break;
            case 2:
                sensitivityHighRight.setVisibility(View.GONE);
                sensitivityMiddleRight.setVisibility(View.VISIBLE);
                sensitivityLowRight.setVisibility(View.GONE);
                break;
            case 3:
                sensitivityHighRight.setVisibility(View.VISIBLE);
                sensitivityMiddleRight.setVisibility(View.GONE);
                sensitivityLowRight.setVisibility(View.GONE);
                break;
            default:
                sensitivityHighRight.setVisibility(View.GONE);
                sensitivityMiddleRight.setVisibility(View.VISIBLE);
                sensitivityLowRight.setVisibility(View.GONE);
                break;
        }
    }


    private void startRecord() {
        PermissionUtils.permission(PermissionConstants.CAMERA, PermissionConstants.MICROPHONE)
                .rationale(new PermissionUtils.OnRationaleListener() {
                    @Override
                    public void rationale(UtilsTransActivity activity, ShouldRequest shouldRequest) {
                        showMessage(R.string.denied_camera);
                        shouldRequest.again(true);
                    }
                })
                .callback(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(List<String> permissionsGranted) {
                        String channel = UUID.randomUUID().toString();
                        Intent intent = new Intent(SittingPositionMenuActivity.this, AuraMateRecordActivity.class);
                        intent.putExtra("isFirst", !isCalibrate);
                        intent.putExtra("equipmentId", equipmentId);
                        intent.putExtra("channel", channel);
                        ActivityUtils.startActivity(intent);
                        new Thread(new Runnable() {
                            @Override
                            public void run() {
                                CZURTcpClient.getInstance().sittingPositionCalibrate(SittingPositionMenuActivity.this, equipmentId, channel);
                            }
                        }).start();
                    }

                    @Override
                    public void onDenied(List<String> permissionsDeniedForever,
                                         List<String> permissionsDenied) {
                        showMessage(R.string.denied_camera);
                    }
                })
                .theme(new PermissionUtils.ThemeCallback() {
                    @Override
                    public void onActivityCreate(Activity activity) {
                        ScreenUtils.setFullScreen(activity);
                    }
                })
                .request();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.vol_minus:
                if (System.currentTimeMillis() - currentTimeMillis > 400) {
                    currentTimeMillis = System.currentTimeMillis();
                    volProgress.setProgress(volProgress.getProgress() - 1);
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            CZURTcpClient.getInstance().volumeLevel(SittingPositionMenuActivity.this, equipmentId, volProgress.getProgress() + 5 + "", volProgress.getProgress() + 5 + 1 + "");

                        }
                    }).start();
                    volTv.setText(volProgress.getProgress() + "");
                }
                break;
            case R.id.vol_plus:
                if (System.currentTimeMillis() - currentTimeMillis > 400) {
                    currentTimeMillis = System.currentTimeMillis();
                    volProgress.setProgress(volProgress.getProgress() + 1);
                    CZURTcpClient.getInstance().volumeLevel(SittingPositionMenuActivity.this, equipmentId, volProgress.getProgress() + 5 + "", volProgress.getProgress() + 5 - 1 + "");
                    volTv.setText(volProgress.getProgress() + "");
                }
                break;
            case R.id.user_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.sitting_position_menu_record_rl:
                if (System.currentTimeMillis() - currentTimeMillis > 1500) {
                    startRecord();
                    currentTimeMillis = System.currentTimeMillis();
                }
                break;
            case R.id.sensitivity_high_rl:
                sensitivityHighRight.setVisibility(View.VISIBLE);
                sensitivityMiddleRight.setVisibility(View.GONE);
                sensitivityLowRight.setVisibility(View.GONE);
                CZURTcpClient.getInstance().sittingPositionSensitivity(SittingPositionMenuActivity.this,
                        equipmentId, CZURMessageConstants.SensitivityLevel.SENSITIVITY_LEVEL_HIGH.getLevel(), lastSPLevel);
                break;
            case R.id.sensitivity_middel_rl:
                sensitivityHighRight.setVisibility(View.GONE);
                sensitivityMiddleRight.setVisibility(View.VISIBLE);
                sensitivityLowRight.setVisibility(View.GONE);
                CZURTcpClient.getInstance().sittingPositionSensitivity(SittingPositionMenuActivity.this,
                        equipmentId, CZURMessageConstants.SensitivityLevel.SENSITIVITY_LEVEL_MEDIUM.getLevel(), lastSPLevel);

                break;
            case R.id.sensitivity_low_rl:
                sensitivityHighRight.setVisibility(View.GONE);
                sensitivityMiddleRight.setVisibility(View.GONE);
                sensitivityLowRight.setVisibility(View.VISIBLE);
                CZURTcpClient.getInstance().sittingPositionSensitivity(SittingPositionMenuActivity.this,
                        equipmentId, CZURMessageConstants.SensitivityLevel.SENSITIVITY_LEVEL_LOW.getLevel(), lastSPLevel);
                break;
            default:
                break;
        }
    }

}
