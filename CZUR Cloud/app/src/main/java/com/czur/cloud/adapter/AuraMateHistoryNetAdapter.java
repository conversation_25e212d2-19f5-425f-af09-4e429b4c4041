package com.czur.cloud.adapter;

import android.content.Intent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.czur.cloud.R;
import com.czur.cloud.entity.realm.WifiHistoryEntity;
import com.czur.cloud.ui.auramate.AuraMateWifiActivity;
import com.czur.cloud.ui.base.BaseActivity;

import java.util.List;

public class AuraMateHistoryNetAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private List<WifiHistoryEntity> wifiHistoryList;
    private BaseActivity activity;
    private boolean noNeedKey;


    public AuraMateHistoryNetAdapter(List<WifiHistoryEntity> wifiHistoryList, BaseActivity activity, boolean noNeedKey) {
        this.wifiHistoryList = wifiHistoryList;
        this.activity = activity;
        this.noNeedKey = noNeedKey;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(activity.getLayoutInflater().inflate(R.layout.item_history_wifi, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        WifiHistoryEntity entity = wifiHistoryList.get(position);
        ViewHolder viewHolder = (ViewHolder) holder;
        viewHolder.tvSsid.setText(entity.getSsid());
        viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(activity, AuraMateWifiActivity.class);
                intent.putExtra("ssid", entity.getSsid());
                intent.putExtra("password", entity.getPassword());
                intent.putExtra("noNeedKey", noNeedKey);
                ActivityUtils.startActivity(intent);
//                ActivityUtils.finishActivity(activity);
            }
        });

    }

    @Override
    public int getItemCount() {
        return Math.min(wifiHistoryList.size(), 3);
    }

    private static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvSsid;

        ViewHolder(View itemView) {
            super(itemView);
            tvSsid = (TextView) itemView.findViewById(R.id.tv_ssid);
        }
    }

}
