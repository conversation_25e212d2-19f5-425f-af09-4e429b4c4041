package com.czur.cloud.ui.starry.meeting.model

import android.util.Log
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.map
import com.czur.cloud.ui.starry.meeting.bean.*
import com.czur.cloud.ui.starry.meeting.bean.vo.MemberInfo
import com.czur.cloud.ui.starry.meeting.common.ONE_MINUTE
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

/**
 * Created by 陈丰尧 on 2021/5/13
 * 聊天消息的Model
 */
class ChatModel : Model() {
    companion object {
        private const val TAG = "ChatModel"

        // 两条消息之间间隔超过2分钟, 则插入时间信息
        private const val TIME_SHOW_INTERVAL = 2 * ONE_MINUTE
    }
    val messages = MediatorLiveData<MutableList<ChatMsg>>()

    // 最新一条接收的消息
    val lastReceiveMsg = messages.map {
        // 最后一条 不是自己发的  文字信息/行为 并且未读的 信息
        it.findLast { msg ->
            (msg is ChatActionMsg || !msg.isSelf) // 不是自己
                    && msg is ChatMemberInfoMsg
                    && !msg.read
        } as ChatMemberInfoMsg?
    }

    private var job = Job()
    private var scope = CoroutineScope(job)

    private val joinedUser = mutableSetOf<String>()

    /**
     * 添加Members的数据源
     * 当Members的数据更新之后, 才可以看到头像和昵称
     */
    private fun addMemberSource() {
        messages.addSource(ModelManager.membersModel.memberMap) { memberMap ->
            // 更新联系人的头像和昵称信息
            val msgList = getMsgList()
            var updated = false
            msgList.forEach {
                if (it is ChatMemberInfoMsg && !it.hasUpdate) {
                    // 找到发送方的,没有更新用户信息的消息
                    val member = memberMap[it.fromCzurID]?.let { member ->
                        MemberInfo(member.czurID, it.headImg)
                    }
                    if (it.updateInfoByMember(member)) {
                        Log.i(TAG, "addMemberSource,member被更新-czurID:${it.fromCzurID}")
                        updated = true
                    } else {
                        Log.i(TAG, "addMemberSource,没有找到对应的数据")
                    }
                }
            }
            if (updated) {
                messages.value = msgList
            }
            addOrRemoveMemberSource()
        }
    }

    /**
     * 添加/删除 members的数据源
     */
    private fun addOrRemoveMemberSource() {
        if (needAddMemberSource()) {
            Log.i(TAG, "addSource")
            try {
                addMemberSource()
            } catch (exp: Exception) {
                Log.i(TAG, "删除之前的Source")
                messages.removeSource(ModelManager.membersModel.memberMap)
                addMemberSource()
            }
        } else {
            Log.i(TAG, "removeSource")
            messages.removeSource(ModelManager.membersModel.memberMap)
        }
    }

    /**
     * 是否需要添加Member的数据源
     */
    private fun needAddMemberSource(): Boolean {
        val msgList = getMsgList()
        msgList.forEach {
            if (it is ChatMemberInfoMsg && !it.hasUpdate) {
                return true
            }
        }
        return false
    }

    /**
     * 添加一条消息
     */
    fun addMsg(chatMsg: ChatMsg) {
        scope.launch(Dispatchers.Main) {
            addMsgOnUIThread(chatMsg)
        }
    }

    /**
     * 修改消息的发送状态
     */
    fun changeMsgStatus(msgID: String, newStatus: ChatSendMsgStatus) {
        Log.i(TAG, "修改消息发送状态:${msgID}")
        val msgList = getMsgList()
        val theMsg = msgList.find {
            it.id == msgID
        }
        if (theMsg == null) {
            Log.i(TAG, "没有找到对应的ID:${msgID}")
            return
        }

        theMsg.status = newStatus
        if (newStatus == ChatSendMsgStatus.FAIL) {
            // 发送失败才去更新UI,否则不需要更新UI
            Log.i(TAG, "消息发送失败")
            messages.postValue(msgList)
        }
    }

    /**
     * 判断是否是合法的消息
     */
    private fun isLegalActionMsg(chatMsg: ChatActionMsg): Boolean {
        val fromCzurID = chatMsg.fromCzurID
        when (chatMsg.action) {
            ChatMemberMsgAction.ONLINE -> {
                if (fromCzurID in joinedUser) {
                    Log.i(TAG, "${fromCzurID}已经发过上线消息了, 不再发送")
                    return false
                } else {
                    joinedUser.add(fromCzurID)
                }
            }
            ChatMemberMsgAction.OFFLINE -> {
                if (fromCzurID in joinedUser) {
                    Log.i(TAG, "${fromCzurID}还没有发过上线消息, 不再发送")
                    return false
                } else {
                    joinedUser.remove(fromCzurID)
                }
            }
            else -> { }
        }
        return true
    }

    private fun addMsgOnUIThread(chatMsg: ChatMsg) {
        if (chatMsg is ChatActionMsg) {
            if (!isLegalActionMsg(chatMsg)) {
                Log.i(TAG, "消息不合法,忽略")
            }
        }

        val msgList = getMsgList()
        // 检查是否要插入时间
        // 最后一条文字时间
        val lastMsgTime = msgList.lastOrNull {
            it is ChatTextMsg
        }.msgTime()
        val nowTime = System.currentTimeMillis()
        if (nowTime - lastMsgTime >= TIME_SHOW_INTERVAL || chatMsg is ChatActionMsg) {
            Log.i(TAG, "时间超过2分钟, 添加时间标记")
            msgList.add(ChatTimeMsg())
        }
        if (chatMsg is ChatMemberInfoMsg && !chatMsg.isSelf) {
            // 尝试更新头像/昵称信息
            val member = ModelManager.membersModel.getMemberInfo(chatMsg.fromCzurID)
            Log.i(TAG, "尝试获取member:${member}")
            chatMsg.updateInfoByMember(member)
        }
        msgList.add(chatMsg)
        messages.value = msgList

        addOrRemoveMemberSource()
    }


    private fun getMsgList() = messages.value ?: mutableListOf()

    override fun doClear() {
        job.cancel()

        job = Job()
        scope = CoroutineScope(job)
        messages.value = mutableListOf()
    }
}

/**
 * 获取消息的时间信息
 */
private fun ChatMsg?.msgTime(): Long = this?.time ?: 0L
