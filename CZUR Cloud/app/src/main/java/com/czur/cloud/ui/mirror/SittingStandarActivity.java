package com.czur.cloud.ui.mirror;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.czur.cloud.R;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.SittingCommonEvent;
import com.czur.cloud.ui.mirror.comm.FastBleConstants;
import com.czur.cloud.ui.mirror.comm.FastBleHexUtils;
import com.czur.cloud.ui.mirror.comm.FastBleHttpUtils;
import com.czur.cloud.ui.mirror.comm.FastBleOperationUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

public class SittingStandarActivity extends SittingBaseActivity {
    private ImageView standarBigPic;
    private ImageView standarTitleImg;
    private TextView standarTitle, standarNote;
    private int iSittingStatus=0;
    private TextView btnFinish;

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {

        switch (event.getEventType()) {
            case SITTING_PICTURE_STANDAR_CHANGE:
                FastBleOperationUtils.threadSleep();
                reflashUI();
                break;
            case SITTING_FEEDBACK_INPUT://设备主动发出 //坐姿录入结果
                SittingCommonEvent commonEvent = (SittingCommonEvent)event;
                String params = commonEvent.getParams();
                // 0，初次成功；
                // 1，重新录入成功
                // 2，初次录入失败。"
                if (params.equals("0") || params.equals("1")){
                    String msg = getString(R.string.sitting_standar_input_msg_sucess);
                    ToastUtils.showLong(msg);
                }else{
                    // 录入失败，显示录入指南对话框
                    FastBleOperationUtils.showNoteDialog(this);
                }

                break;

            default:
              break;
        }
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_sitting_standar);

        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }

        initView();
        initListener();
        setPageTitle(R.string.sitting_model_define);
    }

    protected void initView() {
        deviceModel = userPreferences.getSittingDeviceModel();

        btnFinish = findViewById(R.id.top_bar_finish);
        btnFinish.setText(getString(R.string.sitting_standar_bar_btn));
        btnFinish.setVisibility(View.VISIBLE);
        btnFinish.setTextColor(getColor(R.color.jing_main_bg_color));
        btnFinish.setOnClickListener(this);

        imgBack = findViewById(R.id.top_bar_back_btn);
        standarBigPic = findViewById(R.id.sitting_standar_bigimg);
        standarTitleImg = findViewById(R.id.sitting_standar_note_tile_img);
        standarTitle = findViewById(R.id.sitting_standar_note_tile);
        standarTitle.setOnClickListener(this);
        standarNote = findViewById(R.id.sitting_standar_note);

        // 获取本地的坐姿图片
        if (deviceModel.getInputStatus() == FastBleConstants.SITTING_DEF_STANDARY_SUCESS_INT) {
            String img_name = FastBleOperationUtils.getLocalStandarPicturePath() + FastBleConstants.SITTING_PICTURE_NAME_STANDAR;
            boolean flag = FastBleHexUtils.getLocalStandarPicture(img_name);
            if (!flag){
                FastBleOperationUtils.setBmpStandar(null);
                // 本地没有，则从服务器获取；
                String localStandardImagePath=FastBleOperationUtils.getLocalStandarPicturePath();
                String imageKey = deviceModel.getStandPositionImage();
                new Thread(new Runnable(){
                    @Override
                    public void run() {
                        boolean flag1 = FastBleHttpUtils.downloadOSSPicture(localStandardImagePath,
                                FastBleConstants.SITTING_PICTURE_NAME_STANDAR,
                                imageKey);
                    }
                }).start();
            }
        }

        // 状态改变了，重新刷新界面；
        reflashUI();

//        if (BuildConfig.IS_OVERSEAS){
//            TextView sitting_standar_note = findViewById(R.id.sitting_standar_note);
//            sitting_standar_note.setGravity(Gravity.LEFT);
//        }

    }

    // 状态改变了，重新刷新界面；
    private void reflashUI(){
        deviceModel = userPreferences.getSittingDeviceModel();
        iSittingStatus = deviceModel.getInputStatus();
        reflashUI(iSittingStatus);
    }

    private void reflashUI(int iStatus){

        //坐姿录入状态 0：未录入；1：已经录入；2：录入失败；3：重新录入；4：正在录入
        if (iStatus == 1){
            standarBigPic.setImageBitmap(FastBleOperationUtils.getBmpStandar());
            standarTitleImg.setVisibility(View.GONE);
            standarTitle.setText(getString(R.string.sitting_standar_sitting_right_title));
            standarTitle.setTextColor(getColor(R.color.jing_main_bg_color));
            standarNote.setText(getString(R.string.sitting_standar_sitting_right_note));
        }else {
            standarBigPic.setImageDrawable(getDrawable(R.mipmap.sitting_guide_white2));
            standarTitleImg.setImageDrawable(getDrawable(R.mipmap.sitting_no));
            standarTitleImg.setVisibility(View.VISIBLE);
            standarTitle.setTextColor(getColor(R.color.jing_standar_fault_color));
            standarTitle.setText(getString(R.string.sitting_standar_sitting_noinput_title));
            standarNote.setText(getString(R.string.sitting_standar_sitting_noinput_note));
        }

    }

    protected void initListener() {
        imgBack.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.top_bar_back_btn:
                ActivityUtils.finishActivity(this, true);
                break;

            case R.id.top_bar_finish:
                Intent intent = new Intent(v.getContext(), SittingStandarNoteActivity.class);
                ActivityUtils.startActivity(intent);
                break;

        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

}
