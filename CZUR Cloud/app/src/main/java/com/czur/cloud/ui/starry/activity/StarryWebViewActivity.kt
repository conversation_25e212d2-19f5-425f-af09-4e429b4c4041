package com.czur.cloud.ui.starry.activity

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.webkit.*
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import com.badoo.mobile.util.WeakHandler
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.NetworkUtils
import com.czur.cloud.R
import com.czur.cloud.ui.base.BaseActivity
import com.czur.cloud.ui.market.MallFragment

/**
 */
class StarryWebViewActivity : BaseActivity(), View.OnClickListener {
    private var webContainer: FrameLayout? = null
    private var webView: WebView? = null
    private var webviewBackBtn: ImageView? = null
    private var webviewTitleTv: TextView? = null
    private var title: String? = null
    private var url: String? = null
    private var reloadWebviewRl: RelativeLayout? = null
    private var reloadBtn: TextView? = null
    private var isSuccess = false
    private var isError = false
    private var isFirstFinish = true
    private var handler: WeakHandler? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        BarUtils.setStatusBarColor(this, 0, true)
        BarUtils.setStatusBarLightMode(window, true)
        setContentView(R.layout.activity_webview)
        showProgressDialog()
        initComponent()
        registerEvent()
    }

    private fun initComponent() {
        title = intent.getStringExtra("title")
        url = intent.getStringExtra("url")
        handler = WeakHandler()
        reloadWebviewRl = findViewById<View>(R.id.reload_webview_rl) as RelativeLayout
        reloadBtn = findViewById<View>(R.id.reload_btn) as TextView
        webviewBackBtn = findViewById<View>(R.id.webview_back_btn) as ImageView
        webviewTitleTv = findViewById<View>(R.id.webview_title_tv) as TextView
        webContainer = findViewById<View>(R.id.web_frame) as FrameLayout
        webviewTitleTv?.text = title
        webView = WebView(this)
        val settings = webView?.settings
        settings?.domStorageEnabled = true
        //解决一些图片加载问题
        settings?.javaScriptEnabled = true
        settings?.blockNetworkImage = false
        webView?.webViewClient = webClient
        webView?.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView, progress: Int) {
                //当进度走到100的时候做自己的操作，我这边是弹出dialog
                if (progress == 100) {
                    hideProgressDialog()
                }
            }
        }
        webContainer?.addView(webView)
        webView?.addJavascriptInterface(JSCallAndroidObject(), "jsCallAndroidObject")
        webView?.loadUrl(url ?: "")
    }

    private fun registerEvent() {
        if (NetworkUtils.isConnected()) {
            reloadWebviewRl?.visibility = View.GONE
        } else {
            reloadWebviewRl?.visibility = View.VISIBLE
        }
        webviewBackBtn?.setOnClickListener {
            if (webView?.canGoBack() == true) {
                webView?.goBack() //goBack()表示返回WebView的上一页面
            } else {
                ActivityUtils.finishActivity(this@StarryWebViewActivity)
            }
        }
        reloadBtn?.setOnClickListener {
            showProgressDialog()
            isFirstFinish = true
            webView?.reload()
        }
    }

    /***
     * 设置Web视图的方法
     */
    private val webClient: WebViewClient = object : WebViewClient() {
        //处理网页加载失败时
        override fun onReceivedError(
            view: WebView,
            request: WebResourceRequest,
            error: WebResourceError
        ) {
            super.onReceivedError(view, request, error)
            reloadProgress()
            isError = true
            isSuccess = false
            reloadWebviewRl?.visibility = View.VISIBLE
            webContainer?.visibility = View.GONE
        }

        override fun onReceivedError(
            view: WebView,
            errorCode: Int,
            description: String,
            failingUrl: String
        ) {
            super.onReceivedError(view, errorCode, description, failingUrl)
            reloadProgress()
            isError = true
            isSuccess = false
            reloadWebviewRl?.visibility = View.VISIBLE
            webContainer?.visibility = View.GONE
        }

        override fun onPageFinished(view: WebView, url: String) {
            reloadProgress()
            if (!isFirstFinish) {
                return
            }
            isFirstFinish = false
            if (!isError) {
                isSuccess = true
                //回调成功后的相关操作
                reloadWebviewRl?.visibility = View.GONE
                webContainer?.visibility = View.VISIBLE
            } else {
                isError = false
                reloadWebviewRl?.visibility = View.VISIBLE
                webContainer?.visibility = View.GONE
            }
        }

        override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
            Log.d("webview", "url: $url")
            view.loadUrl(url)
            return true
        }
    }

    private fun reloadProgress() {
        handler?.postDelayed({ hideProgressDialog() }, 600)
    }

    /**
     * @des: js交互
     * @params:
     * @return:
     */
    inner class JSCallAndroidObject {
        private val TAG = MallFragment.JSCallAndroidObject::class.java.simpleName
        @JavascriptInterface
        fun jsCallAndroid(msg: String?): String {
            val uri = Uri.parse(msg)
            val intent = Intent(Intent.ACTION_VIEW, uri)
            startActivity(intent)
            return "from Android"
        }
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.normal_back_btn -> ActivityUtils.finishActivity(this)
            else -> {
            }
        }
    }

    override fun onDestroy() {
        if (webView != null) {
            webView?.loadDataWithBaseURL(null, "", "text/html", "uft-8", null)
            webView?.clearHistory()
            (webView?.parent as ViewGroup).removeView(webView)
            webView?.destroy()
            webView = null
        }
        super.onDestroy()
    }
}