package com.czur.cloud.ui.starry.meeting.base

import android.view.MotionEvent
import com.czur.cloud.ui.starry.meeting.baselib.utils.MouseUtil

/**
 * Created by 陈丰尧 on 2021/6/11
 */
abstract class BaseMeetingActivity : NoNavActivity() {

//    override fun dispatchGenericMotionEvent(ev: MotionEvent?): Bo<PERSON>an {
//        ev?.let {
//            MouseUtil.updateMousePoint(it.rawX,it.rawY)
//        }
//        return super.dispatchGenericMotionEvent(ev)
//    }
}