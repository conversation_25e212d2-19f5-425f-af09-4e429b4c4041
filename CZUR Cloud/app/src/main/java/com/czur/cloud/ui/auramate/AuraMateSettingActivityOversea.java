package com.czur.cloud.ui.auramate;

import android.app.AppOpsManager;
import android.app.NotificationManager;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.core.app.NotificationManagerCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.KeyboardUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.AuraMateManageUserAdapter;
import com.czur.cloud.adapter.TransShareUserPopupAdapter;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.entity.realm.SPReportEntity;
import com.czur.cloud.entity.realm.SPReportEntitySub;
import com.czur.cloud.event.AuraMateOperateEvent;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.ChangeLanguageEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.model.AuraMateShareUserModel;
import com.czur.cloud.model.NotificationModel;
import com.czur.cloud.netty.CZURMessageConstants;
import com.czur.cloud.netty.core.CZURTcpClient;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.component.CustomClickListener;
import com.czur.cloud.ui.component.popup.AuraMateButtonPopup;
import com.czur.cloud.ui.component.popup.AuraMateUpdatePopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.util.EtUtils;
import com.czur.cloud.util.validator.Validator;
import com.github.iielse.switchbutton.SwitchView;
import com.google.gson.reflect.TypeToken;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

import io.realm.Realm;
import io.realm.RealmResults;

/**
 * Created by shaojun on 2020/4/1.
 * Email：<EMAIL>
 */
public class AuraMateSettingActivityOversea extends AuramateBaseActivity implements View.OnClickListener {
    private final String TYPE_NOTIFY_USE = "notify_use";
    private final String TYPE_NOTIFY_FILE = "notify_file";
    private final String TYPE_NOTIFY_OFFLINE = "notify_offline";

    private HttpManager httpManager;
    private ImageView normalBackBtn;
    private TextView normalTitle;
    private EditText etAddUserEdt;
    private RelativeLayout auraHomeReconnectRl;
    private LinearLayout auramateChangeLanguageRl;
    private LinearLayout auramateErrorSitRl;

    private UserPreferences userPreferences;
    private String deviceId;
    private TextView editAuraHomeNameFinish;
    private boolean isOnline;

    private RelativeLayout auraHomeUnbindRl;
    private String ownerId;
    private String language;

    private RelativeLayout auraHomeTransRl;
    private TextView manageDeviceTitle;
    private RecyclerView settingRecyclerView;
    private RelativeLayout settingRecyclerViewRl;
    private LinearLayout auraHomeInviteRl;
    private List<AuraMateShareUserModel> auraMateShareUserModels;
    private List<AuraMateShareUserModel> tempModels;
    private AuraMateManageUserAdapter auraMateManageUserAdapter;
    private boolean isShared;
    private AuraMateButtonPopup.Builder builder;
    private AuraMateButtonPopup auraMateButtonPopup;
    private RecyclerView dialogRecyclerView;
    private TransShareUserPopupAdapter popupAdapter;
    private TextView confirmBtn;
    private int memberId;
    private TextView deviceStatusText;
    private ImageView deviceStatusImg;
    private TextView statusInfo;
    private String updateVersion;
    private String currentVersion;
    private boolean isNeedUpdate;
    private TextView currentVersionTv;
    private ImageView needUpdateImg;
    private TextView needUpdateTv;
    private String ssid;
    private LinearLayout auraHomeUpdateRl;

    private RelativeLayout lightDetailRl;
    private ImageView updateArrow;
    private LinearLayout offlineUpdateLL;
    private String name;
    private AuraMateUpdatePopup.Builder builder1;
    private TextView deviceLatestTv;
    private Realm realm;

    private SwitchView notifySwitchUse, notifySwitchFile, notifySwitchOffline;
    private SwitchView notifySwitch, errorSitSwitch;
    private static final int SUCCESS_CODE = 666;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_aura_home_setting_oversea);
        initComponent();
        registerEvent();
    }

    @Override
    public boolean PCNeedFinish() {
        return !TextUtils.isEmpty(equipmentId);
    }


    private void initComponent() {
        httpManager = HttpManager.getInstance();
        realm = Realm.getDefaultInstance();
        ownerId = getIntent().getStringExtra("ownerId");
        language = getIntent().getStringExtra("language");
        isShared = getIntent().getBooleanExtra("isShared", false);
        deviceId = getIntent().getStringExtra("deviceId");
        isOnline = getIntent().getBooleanExtra("isOnline", false);
        isNeedUpdate = getIntent().getBooleanExtra("isNeedUpdate", false);
        currentVersion = getIntent().getStringExtra("currentVersion");
        updateVersion = getIntent().getStringExtra("updateVersion");
        ssid = getIntent().getStringExtra("ssid");
        name = getIntent().getStringExtra("name");
        userPreferences = UserPreferences.getInstance(this);
        normalBackBtn = (ImageView) findViewById(R.id.normal_back_btn);
        normalTitle = (TextView) findViewById(R.id.normal_title);
        etAddUserEdt = (EditText) findViewById(R.id.et_add_user_edt);
        auraHomeReconnectRl = (RelativeLayout) findViewById(R.id.aura_home_reconnect_rl);
        editAuraHomeNameFinish = (TextView) findViewById(R.id.edit_aura_home_name_finish);
        auramateChangeLanguageRl = (LinearLayout) findViewById(R.id.aura_home_language_rl);
        auramateErrorSitRl = (LinearLayout) findViewById(R.id.aura_home_errorsit_rl);
        deviceLatestTv = (TextView) findViewById(R.id.device_latest_tv);

        settingRecyclerView = (RecyclerView) findViewById(R.id.setting_recyclerView);
        settingRecyclerViewRl = (RelativeLayout) findViewById(R.id.setting_recyclerView_rl);
        auraHomeTransRl = (RelativeLayout) findViewById(R.id.aura_home_trans_rl);
        manageDeviceTitle = (TextView) findViewById(R.id.manage_device_title);
        auraHomeUnbindRl = (RelativeLayout) findViewById(R.id.aura_home_unbind_rl);
        auraHomeInviteRl = (LinearLayout) findViewById(R.id.aura_home_invite_rl);
        lightDetailRl = findViewById(R.id.rl_light_detail);

        deviceStatusText = (TextView) findViewById(R.id.device_status_text);
        deviceStatusImg = (ImageView) findViewById(R.id.device_status_img);
        statusInfo = (TextView) findViewById(R.id.status_info);
        currentVersionTv = (TextView) findViewById(R.id.current_version_tv);
        needUpdateImg = (ImageView) findViewById(R.id.need_update_img);
        needUpdateTv = (TextView) findViewById(R.id.need_update_tv);
        auraHomeUpdateRl = (LinearLayout) findViewById(R.id.aura_home_update_rl);
        updateArrow = (ImageView) findViewById(R.id.update_arrow);
        offlineUpdateLL = (LinearLayout) findViewById(R.id.offline_update_tv);
        notifySwitchUse = findViewById(R.id.switch_notify_use);
        notifySwitchFile = findViewById(R.id.switch_notify_file);
        notifySwitchOffline = findViewById(R.id.switch_notify_offline);
        notifySwitch = findViewById(R.id.switch_notify);
        errorSitSwitch = findViewById(R.id.switch_errorsit);

        if (Validator.isEmpty(currentVersion)) {
            currentVersion = "";
        }
        currentVersionTv.setText(String.format(getString(R.string.device_version), currentVersion));
        if (isNeedUpdate) {
            needUpdateImg.setVisibility(View.VISIBLE);
            needUpdateTv.setVisibility(View.VISIBLE);
            updateArrow.setVisibility(View.VISIBLE);
            deviceLatestTv.setVisibility(View.GONE);
        } else {
            needUpdateImg.setVisibility(View.GONE);
            needUpdateTv.setVisibility(View.GONE);
            deviceLatestTv.setVisibility(View.VISIBLE);
            updateArrow.setVisibility(View.INVISIBLE);
        }
        if (isOnline) {
            offlineUpdateLL.setVisibility(View.GONE);
            auramateChangeLanguageRl.setVisibility(View.VISIBLE);
            auramateErrorSitRl.setVisibility(View.VISIBLE);
            deviceStatusText.setText(R.string.online);
            deviceStatusImg.setImageResource(R.mipmap.online_icon);
            statusInfo.setText(String.format(getString(R.string.online_info), ssid));
        } else {
            offlineUpdateLL.setVisibility(View.VISIBLE);
            auramateChangeLanguageRl.setVisibility(View.GONE);
            auramateErrorSitRl.setVisibility(View.GONE);
            updateArrow.setVisibility(View.GONE);
            needUpdateTv.setVisibility(View.GONE);
            deviceLatestTv.setVisibility(View.GONE);
            deviceStatusText.setText(R.string.offline);
            deviceStatusImg.setImageResource(R.mipmap.offline_icon);
            statusInfo.setText(R.string.offline_warning);
        }
        if (!isShared) {
            etAddUserEdt.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {

                }

                @Override
                public void afterTextChanged(Editable s) {
                    if (!TextUtils.isEmpty(s.toString())) {
                        if (s.toString().equals(name)) {
                            editAuraHomeNameFinish.setAlpha(0.5f);
                            editAuraHomeNameFinish.setEnabled(false);
                            editAuraHomeNameFinish.setClickable(false);
                        } else {
                            editAuraHomeNameFinish.setAlpha(1);
                            editAuraHomeNameFinish.setEnabled(true);
                            editAuraHomeNameFinish.setClickable(true);
                        }
                    }
                }
            });
        }
        etAddUserEdt.setText(name);
        if (isShared) {
            auraHomeTransRl.setVisibility(View.GONE);
            auraHomeUnbindRl.setVisibility(View.GONE);
            auraHomeInviteRl.setVisibility(View.GONE);
            auraHomeUpdateRl.setVisibility(View.GONE);
            auramateChangeLanguageRl.setVisibility(View.GONE);
            auramateErrorSitRl.setVisibility(View.GONE);
            etAddUserEdt.setClickable(false);
            etAddUserEdt.setEnabled(false);
            etAddUserEdt.setSelected(false);
            editAuraHomeNameFinish.setVisibility(View.GONE);
        }
        normalTitle.setText(getString(R.string.aura_home_setting));
        getShareUserList(false);
        initRecyclerView();
        initDialogList();
        getNotificationSetting();

        getErrorSitSetting();
    }


    private void getNotificationSetting() {
        httpManager.request().getNoticeSetting(userPreferences.getUserId(), equipmentId, NotificationModel.class, new MiaoHttpManager.CallbackNetwork<NotificationModel>() {
            @Override
            public void onNoNetwork() {
                showMessage(R.string.toast_no_connection_network);
            }

            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<NotificationModel> entity) {
                hideProgressDialog();
                NotificationModel body = entity.getBody();
                checkNotifyForAll(body);
            }

            @Override
            public void onFailure(MiaoHttpEntity<NotificationModel> entity) {
                checkNotifyForAll(null);
                hideProgressDialog();
                showMessage(R.string.request_failed_alert);
            }

            @Override
            public void onError(Exception e) {
                checkNotifyForAll(null);
                hideProgressDialog();
                showMessage(R.string.request_failed_alert);
            }
        });
    }

    private void getErrorSitSetting() {
        httpManager.request().getErrorSitSetting(
                userPreferences.getUserId(),
                equipmentId,
                NotificationModel.class,
                new MiaoHttpManager.CallbackNetwork<NotificationModel>() {
            @Override
            public void onNoNetwork() {
                showMessage(R.string.toast_no_connection_network);
            }

            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<NotificationModel> entity) {
                hideProgressDialog();
                NotificationModel body = entity.getBody();
                if (Validator.isNotEmpty(body.isDataCollection())) {
                    checkErrorSit(body.isDataCollection());
                } else{
                    checkErrorSit(true);
                }
            }

            @Override
            public void onFailure(MiaoHttpEntity<NotificationModel> entity) {
                checkErrorSit(userPreferences.isErrorsitAll());
                hideProgressDialog();
                showMessage(R.string.request_failed_alert);
            }

            @Override
            public void onError(Exception e) {
                checkErrorSit(userPreferences.isErrorsitAll());
                hideProgressDialog();
                showMessage(R.string.request_failed_alert);
            }
        });
    }

    private void registerEvent() {
        auraHomeInviteRl.setOnClickListener(this);
        auraHomeTransRl.setOnClickListener(this);
        normalBackBtn.setOnClickListener(this);
        auraHomeReconnectRl.setOnClickListener(this);
        editAuraHomeNameFinish.setOnClickListener(this);
        auraHomeUnbindRl.setOnClickListener(this);
        auraHomeUpdateRl.setOnClickListener(this);
        auramateChangeLanguageRl.setOnClickListener(this);
        lightDetailRl.setOnClickListener(this);

        notifySwitchUse.setOnClickListener(new CustomClickListener() {
            @Override
            protected void onSingleClick() {
                checkAndSetNotifyEach(notifySwitchUse, TYPE_NOTIFY_USE, notifySwitchUse.isOpened());
            }

            @Override
            protected void onFastClick() {

            }
        });
        notifySwitchFile.setOnClickListener(new CustomClickListener() {
            @Override
            protected void onSingleClick() {
                checkAndSetNotifyEach(notifySwitchFile, TYPE_NOTIFY_FILE, notifySwitchFile.isOpened());
            }

            @Override
            protected void onFastClick() {

            }
        });
        notifySwitchOffline.setOnClickListener(new CustomClickListener() {
            @Override
            protected void onSingleClick() {
                checkAndSetNotifyEach(notifySwitchOffline, TYPE_NOTIFY_OFFLINE, notifySwitchOffline.isOpened());
            }

            @Override
            protected void onFastClick() {

            }
        });

        errorSitSwitch.setOnClickListener(new CustomClickListener() {
            @Override
            protected void onSingleClick() {
                boolean isChecked = errorSitSwitch.isOpened();
                checkErrorSit(isChecked);
            }

            @Override
            protected void onFastClick() {

            }
        });

        setNetListener();
    }

    private void showOpenNotifyForAllDialog(SwitchView sw) {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(AuraMateSettingActivityOversea.this, CloudCommonPopupConstants.NOTIFY_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.no_notify_text));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                goNotifySetting();
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                sw.setOpened(false);
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.setCanceledOnTouchOutside(false);
        commonPopup.show();
    }

    private void goNotifySetting() {
        Intent localIntent = new Intent();
        //直接跳转到应用通知设置的代码：
        if (Build.VERSION.SDK_INT == Build.VERSION_CODES.O) {
            localIntent.setAction(Settings.ACTION_APP_NOTIFICATION_SETTINGS);
            localIntent.putExtra(Settings.EXTRA_APP_PACKAGE, getPackageName());
        } else if (android.os.Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP && Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
            localIntent.setAction("android.settings.APP_NOTIFICATION_SETTINGS");
            localIntent.putExtra("app_package", getPackageName());
            localIntent.putExtra("app_uid", getApplicationInfo().uid);
        } else if (android.os.Build.VERSION.SDK_INT == Build.VERSION_CODES.KITKAT) {
            localIntent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            localIntent.addCategory(Intent.CATEGORY_DEFAULT);
            localIntent.setData(Uri.parse("package:" + getPackageName()));
        } else {
            //4.4以下没有从app跳转到应用通知设置页面的Action，可考虑跳转到应用详情页面,
            localIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            if (Build.VERSION.SDK_INT >= 9) {
                localIntent.setAction("android.settings.APPLICATION_DETAILS_SETTINGS");
                localIntent.setData(Uri.fromParts("package", getPackageName(), null));
            } else if (Build.VERSION.SDK_INT <= 8) {
                localIntent.setAction(Intent.ACTION_VIEW);
                localIntent.setClassName("com.android.settings", "com.android.setting.InstalledAppDetails");
                localIntent.putExtra("com.android.settings.ApplicationPkgName", getPackageName());
            }
        }
        startActivityForResult(localIntent, SUCCESS_CODE);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == SUCCESS_CODE) {

        }
    }

    private void checkNotifyEach(SwitchView sw,String typeName, boolean isChecked) {
        sw.toggleSwitch(isChecked);
        if (isChecked) {
            boolean isNotifyOpened = isNotificationEnabled(this);
            if (isNotifyOpened) {
                sw.toggleSwitch(true);
            } else {
                sw.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        sw.toggleSwitch(false);
                        showOpenNotifyForAllDialog(sw);
                    }
                }, 300);
            }
        }
    }

    private void checkAndSetNotifyEach(SwitchView sw,String typeName, boolean isChecked) {
        if (isChecked) {
            boolean isNotifyOpened = isNotificationEnabled(this);
            if (isNotifyOpened) {
                sw.toggleSwitch(true);
                setSettingToServerEach(typeName, true);
            } else {
                sw.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        sw.toggleSwitch(false);
                        showOpenNotifyForAllDialog(sw);
                    }
                }, 300);
            }
        } else {
            setSettingToServerEach(typeName, false);
        }
    }

    private void setSettingToServerEach(String typeName, boolean isChecked){
        if (typeName.equals(TYPE_NOTIFY_USE)) {
            setUseSettingToServer(isChecked);
        }
        if (typeName.equals(TYPE_NOTIFY_FILE)) {
            setFileSettingToServer(isChecked);
        }
        if (typeName.equals(TYPE_NOTIFY_OFFLINE)) {
            setOfflineSettingToServer(isChecked);
        }
    }

    private void checkNotifyForAll(NotificationModel body) {
        if (Validator.isEmpty(body)){
            setUseSettingToServer(false);
            setFileSettingToServer(true);
            setOfflineSettingToServer(true);
            notifySwitchUse.setOpened(false);
            notifySwitchFile.setOpened(true);
            notifySwitchOffline.setOpened(true);
            return;
        }

        boolean isUse = body.isUseNotification();
        boolean isFile = body.isNewFileNotification();
        boolean isOffline = body.isOfflineNotification();

        checkNotifyEach(notifySwitchUse, TYPE_NOTIFY_USE, isUse);

        checkNotifyEach(notifySwitchFile, TYPE_NOTIFY_FILE, isFile);

        checkNotifyEach(notifySwitchOffline, TYPE_NOTIFY_OFFLINE, isOffline);

    }

    private void setNotificationUseLocalData(boolean isChecked) {
        userPreferences.setNotifyUse(isChecked);
    }
    private void setNotificationFileLocalData(boolean isChecked) {
        userPreferences.setNotifyFile(isChecked);
    }
    private void setNotificationOfflineLocalData(boolean isChecked) {
        userPreferences.setNotifyOffline(isChecked);
    }

    private void checkErrorSit(boolean isChecked) {
        errorSitSwitch.toggleSwitch(isChecked);
        setSettingToServerErrorSit(isChecked);
    }

    private void setNotificationLocalData(boolean isChecked) {
        userPreferences.setNotifyAll(isChecked);
    }

    private void setErrorSitLocalData(boolean isChecked) {
        userPreferences.setErrorsitAll(isChecked);

        //同时通过长连接通知设备
        //int	0:关，1:开
        if (isChecked) {
            CZURTcpClient.getInstance().sittingWrongSitSwitch(AuraMateSettingActivityOversea.this,
                    equipmentId,
                    CZURMessageConstants.WrongSitSwitch.WRONG_SIT_SWITCH_ON.getWrongSitSwitch(),
                    CZURMessageConstants.WrongSitSwitch.WRONG_SIT_SWITCH_OFF.getWrongSitSwitch());
        }else{
            CZURTcpClient.getInstance().sittingWrongSitSwitch(AuraMateSettingActivityOversea.this,
                    equipmentId,
                    CZURMessageConstants.WrongSitSwitch.WRONG_SIT_SWITCH_OFF.getWrongSitSwitch(),
                    CZURMessageConstants.WrongSitSwitch.WRONG_SIT_SWITCH_ON.getWrongSitSwitch());
        }
    }

    //dataCollection	true	int	0:关，1:开
    private void setSettingToServerErrorSit(boolean isChecked) {
        httpManager.request().setErrorSitSetting(
                userPreferences.getUserId(),
                equipmentId,
                isChecked ? "1" : "0",
                String.class, new MiaoHttpManager.CallbackNetwork<String>() {
                    @Override
                    public void onNoNetwork() {
                        showMessage(R.string.toast_no_connection_network);
                    }

                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                        setErrorSitLocalData(isChecked);
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                        showMessage(R.string.request_failed_alert);
                    }

                    @Override
                    public void onError(Exception e) {
                        showMessage(R.string.request_failed_alert);
                    }
                });
    }


    // 使用通知
    private void setUseSettingToServer(boolean isChecked) {
        httpManager.request().setNoticeUseSetting(userPreferences.getUserId(), equipmentId, isChecked ? "1" : "0", String.class, new MiaoHttpManager.CallbackNetwork<String>() {
            @Override
            public void onNoNetwork() {
                showMessage(R.string.toast_no_connection_network);
            }

            @Override
            public void onStart() {
            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                setNotificationUseLocalData(isChecked);
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                showMessage(R.string.request_failed_alert);
            }

            @Override
            public void onError(Exception e) {
                showMessage(R.string.request_failed_alert);
            }
        });
    }

    // 离线通知
    private void setOfflineSettingToServer(boolean isChecked) {
        httpManager.request().setNoticeOfflineSetting(userPreferences.getUserId(), equipmentId, isChecked ? "1" : "0", String.class, new MiaoHttpManager.CallbackNetwork<String>() {
            @Override
            public void onNoNetwork() {
                showMessage(R.string.toast_no_connection_network);
            }

            @Override
            public void onStart() {
            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                setNotificationOfflineLocalData(isChecked);
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                showMessage(R.string.request_failed_alert);
            }

            @Override
            public void onError(Exception e) {
                showMessage(R.string.request_failed_alert);
            }
        });
    }

    // 新增文件通知
    private void setFileSettingToServer(boolean isChecked) {
        httpManager.request().setNoticeFileSetting(userPreferences.getUserId(), equipmentId, isChecked ? "1" : "0", String.class, new MiaoHttpManager.CallbackNetwork<String>() {
            @Override
            public void onNoNetwork() {
                showMessage(R.string.toast_no_connection_network);
            }

            @Override
            public void onStart() {
            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                setNotificationFileLocalData(isChecked);
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                showMessage(R.string.request_failed_alert);
            }

            @Override
            public void onError(Exception e) {
                showMessage(R.string.request_failed_alert);
            }
        });
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    public static boolean isNotificationEnabled(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            NotificationManagerCompat notificationManagerCompat = NotificationManagerCompat.from(context);
            return notificationManagerCompat.areNotificationsEnabled();
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            //8.0手机以上
            if (((NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE)).getImportance() == NotificationManager.IMPORTANCE_NONE) {
                return false;
            }
        }
        String CHECK_OP_NO_THROW = "checkOpNoThrow";
        String OP_POST_NOTIFICATION = "OP_POST_NOTIFICATION";
        AppOpsManager mAppOps = (AppOpsManager) context.getSystemService(Context.APP_OPS_SERVICE);
        ApplicationInfo appInfo = context.getApplicationInfo();
        String pkg = CZURConstants.PACKAGE_NAME;
        int uid = appInfo.uid;
        Class appOpsClass = null;
        try {
            appOpsClass = Class.forName(AppOpsManager.class.getName());
            Method checkOpNoThrowMethod = appOpsClass.getMethod(CHECK_OP_NO_THROW, Integer.TYPE, Integer.TYPE,
                    String.class);
            Field opPostNotificationValue = appOpsClass.getDeclaredField(OP_POST_NOTIFICATION);
            int value = (Integer) opPostNotificationValue.get(Integer.class);
            return ((Integer) checkOpNoThrowMethod.invoke(mAppOps, value, uid, pkg) == AppOpsManager.MODE_ALLOWED);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private void initRecyclerView() {
        auraMateShareUserModels = new ArrayList<>();
        tempModels = new ArrayList<>();
        auraMateManageUserAdapter = new AuraMateManageUserAdapter(this, isShared, auraMateShareUserModels);
        auraMateManageUserAdapter.setOnItemClickListener(onItemClickListener);
        settingRecyclerView.setHasFixedSize(true);
        settingRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        settingRecyclerView.setAdapter(auraMateManageUserAdapter);
    }

    private void initDialogList() {
        builder = new AuraMateButtonPopup.Builder(this);
        auraMateButtonPopup = builder.create();
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialogInterface, int i) {
                CZURTcpClient.getInstance().noticeRelationShipChange(AuraMateSettingActivityOversea.this, equipmentId);
                transferShareUser();
                auraMateButtonPopup.dismiss();
            }
        });
        confirmBtn = (TextView) auraMateButtonPopup.findViewById(R.id.confirm_btn);
        dialogRecyclerView = (RecyclerView) auraMateButtonPopup.findViewById(R.id.aura_home_recyclerView);
        popupAdapter = new TransShareUserPopupAdapter(this, tempModels);
        popupAdapter.setOnItemClickListener(onItemClickListener1);
        dialogRecyclerView.setHasFixedSize(true);
        dialogRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        dialogRecyclerView.setAdapter(popupAdapter);
    }


    private TransShareUserPopupAdapter.onItemClickListener onItemClickListener1 = new TransShareUserPopupAdapter.onItemClickListener() {
        @Override
        public void onItemClick(int position, AuraMateShareUserModel auraMateShareUserModel, boolean isEmpty) {
            if (isEmpty) {
                memberId = auraMateShareUserModel.getMemberId();
                confirmBtn.setBackground(getResources().getDrawable(R.drawable.btn_rec_5_bg_with_blue_aura_home));
                confirmBtn.setEnabled(true);
                confirmBtn.setClickable(true);
            } else {
                confirmBtn.setBackground(getResources().getDrawable(R.drawable.btn_rec_5_bg_with_light_blue_aura_home));
                confirmBtn.setEnabled(false);
                confirmBtn.setClickable(false);
            }
        }
    };
    private AuraMateManageUserAdapter.onItemClickListener onItemClickListener = new AuraMateManageUserAdapter.onItemClickListener() {
        @Override
        public void onItemClick(int position, AuraMateShareUserModel auraMateShareUserModel) {
            if (isShared) {
                exitShareUser();
            } else {
                confirmDeleteDialog(auraMateShareUserModel);
            }
        }
    };

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {

            case ADD_AURA_SHARE_USER:
                getShareUserList(false);
                break;
            case CHANGE_LANGUAGE:
                ChangeLanguageEvent event1 = (ChangeLanguageEvent) event;
                language = event1.getLanguage();
                break;
            default:
                break;
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.aura_home_update_rl:
                if (isNeedUpdate) {
                    Intent intent = new Intent(this, AuraMateUpdateActivity.class);
                    intent.putExtra("equipmentId", equipmentId);
                    intent.putExtra("readyForOTAUpdate", true);
                    ActivityUtils.startActivity(intent);
                }
                break;
            case R.id.aura_home_trans_rl:
                confirmBtn.setEnabled(false);
                confirmBtn.setClickable(false);
                confirmBtn.setBackground(getResources().getDrawable(R.drawable.btn_rec_5_bg_with_light_blue_aura_home));
                popupAdapter.resetSelect();
                auraMateButtonPopup.show();
                break;
            case R.id.aura_home_invite_rl:
                Intent intent = new Intent(AuraMateSettingActivityOversea.this, AuraMateAddUserActivity.class);
                intent.putExtra("ownerId", ownerId);
                intent.putExtra("equipmentId", equipmentId);
                ActivityUtils.startActivity(intent);
                break;
            case R.id.aura_home_unbind_rl:
                confirmDeleteDialog();
                break;
            case R.id.normal_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.aura_home_reconnect_rl:
                //重新联网不需要配置key
                Intent intent1 = new Intent(AuraMateSettingActivityOversea.this, AuraMateWifiHistoryActivity.class);
                intent1.putExtra("noNeedKey", true);
                intent1.putExtra("equipmentId", equipmentId);
                ActivityUtils.startActivity(intent1);
                break;
            case R.id.edit_aura_home_name_finish:
                changeAuraName();
                break;
            case R.id.aura_home_language_rl:
                Intent intent2 = new Intent(AuraMateSettingActivityOversea.this, ChangeLanguageActivity.class);
                intent2.putExtra("language", language);
                intent2.putExtra("equipmentId", equipmentId);
                ActivityUtils.startActivity(intent2);
                break;
            case R.id.rl_light_detail:
                Intent intent3 = new Intent(AuraMateSettingActivityOversea.this, AuraMateLightActivity.class);
                intent3.putExtra("equipmentId", equipmentId);
                ActivityUtils.startActivity(intent3);
                break;
            default:
                break;
        }
    }

    private void getShareUserList(boolean isTransfer) {
        HttpManager.getInstance().request().getAuraMateShareUser(userPreferences.getUserId(), equipmentId, ownerId, new TypeToken<List<AuraMateShareUserModel>>() {
        }.getType(), new MiaoHttpManager.CallbackNetwork<AuraMateShareUserModel>() {
            @Override
            public void onNoNetwork() {

            }

            @Override
            public void onStart() {

            }

            @Override
            public void onResponse(MiaoHttpEntity<AuraMateShareUserModel> entity) {
                List<AuraMateShareUserModel> auraMateShareUserModels = entity.getBodyList();
                if (isTransfer) {
                    if (auraMateShareUserModels.size() > 1) {
                        manageDeviceTitle.setVisibility(View.VISIBLE);
                        manageDeviceTitle.setText(R.string.share_devices_title1);
                        settingRecyclerView.setVisibility(View.VISIBLE);
                        settingRecyclerViewRl.setVisibility(View.VISIBLE);
                        auraMateManageUserAdapter.refreshData(auraMateShareUserModels);
                    } else {
                        settingRecyclerView.setVisibility(View.GONE);
                        settingRecyclerViewRl.setVisibility(View.GONE);
                        manageDeviceTitle.setVisibility(View.GONE);
                    }
                } else {
                    if (auraMateShareUserModels.size() > 1) {
                        manageDeviceTitle.setVisibility(View.VISIBLE);
                        manageDeviceTitle.setText(R.string.share_devices_title);
                        if (!isShared) {
                            auraHomeTransRl.setVisibility(View.VISIBLE);
                        }
                        settingRecyclerView.setVisibility(View.VISIBLE);
                        settingRecyclerViewRl.setVisibility(View.VISIBLE);
                        auraMateManageUserAdapter.refreshData(auraMateShareUserModels);
                    } else {
                        settingRecyclerView.setVisibility(View.GONE);
                        settingRecyclerViewRl.setVisibility(View.GONE);
                        manageDeviceTitle.setVisibility(View.GONE);
                        auraHomeTransRl.setVisibility(View.GONE);
                    }
                }
                refreshShareList(auraMateShareUserModels);
            }

            @Override
            public void onFailure(MiaoHttpEntity<AuraMateShareUserModel> entity) {

            }

            @Override
            public void onError(Exception e) {
            }
        });

    }

    private void refreshShareList(List<AuraMateShareUserModel> auraMateShareUserModels) {
        tempModels = new ArrayList<>();
        for (AuraMateShareUserModel auraMateShareUserModel : auraMateShareUserModels) {
            if (auraMateShareUserModel.getMemberId() != auraMateShareUserModel.getOwnerId()) {
                tempModels.add(auraMateShareUserModel);
            }
        }
        popupAdapter.refreshData(tempModels);
    }

    private void deleteShareUser(String searchUserId) {
        httpManager.request().deleteAuraMateShareUser(userPreferences.getUserId(), equipmentId, searchUserId, String.class, new MiaoHttpManager.Callback<String>() {
            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                getShareUserList(false);
                EventBus.getDefault().post(new AuraMateOperateEvent(EventType.DELETE_AURA_SHARE_USER, equipmentId));
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                showMessage(R.string.toast_server_error);
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
                showMessage(R.string.toast_server_error);
            }
        });
    }

    private void exitShareUser() {
        httpManager.request().exitShareMember(userPreferences.getUserId(), equipmentId, ownerId, String.class, new MiaoHttpManager.Callback<String>() {
            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                KeyboardUtils.hideSoftInput(AuraMateSettingActivityOversea.this);
                hideProgressDialog();
                ActivityUtils.finishActivity(AuraMateSettingActivityOversea.this);
                EventBus.getDefault().post(new AuraMateOperateEvent(EventType.EXIT_SHARE_USER, equipmentId));
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                showMessage(R.string.toast_server_error);
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
                showMessage(R.string.toast_server_error);
            }
        });

    }

    private void transferShareUser() {
        httpManager.request().transferAuraMateShareUser(deviceId, userPreferences.getUserId(), memberId + "", String.class, new MiaoHttpManager.Callback<String>() {
            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                auraHomeTransRl.setVisibility(View.GONE);
                auraHomeUnbindRl.setVisibility(View.GONE);
                auraHomeInviteRl.setVisibility(View.GONE);
                EventBus.getDefault().post(new AuraMateOperateEvent(EventType.TRANSFER_AURA_MATE, equipmentId));
                CZURTcpClient.getInstance().transferDevice(AuraMateSettingActivityOversea.this, equipmentId);
                getShareUserList(true);
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                showMessage(R.string.toast_server_error);
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
                showMessage(R.string.toast_server_error);
            }
        });

    }

    private void confirmDeleteDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(AuraMateSettingActivityOversea.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.confirm_unbind));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                unbind();
                dialog.dismiss();
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();
    }

    private void confirmDeleteDialog(AuraMateShareUserModel auraMateShareUserModel) {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(AuraMateSettingActivityOversea.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.confirm_delete));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                deleteShareUser(auraMateShareUserModel.getMemberId() + "");
                dialog.dismiss();
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();
    }

    private void unbind() {
        HttpManager.getInstance().request().unbindAuraMate(userPreferences.getUserId(), deviceId, String.class, new MiaoHttpManager.Callback<String>() {
            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                CZURTcpClient.getInstance().unbindDevice(AuraMateSettingActivityOversea.this, equipmentId);
                EventBus.getDefault().post(new AuraMateOperateEvent(EventType.UNBIND_AURA_MATE, equipmentId));
                RealmResults<SPReportEntity> spReportEntities = realm.where(SPReportEntity.class).equalTo("equipmentUuid", equipmentId).findAll();
                if (spReportEntities != null) {
                    if (spReportEntities.size() > 0) {
                        realm.beginTransaction();
                        spReportEntities.deleteAllFromRealm();
                        realm.commitTransaction();
                    }
                }
                RealmResults<SPReportEntitySub> spReportEntitiesSub = realm.where(SPReportEntitySub.class).equalTo("equipmentUuid", equipmentId).findAll();
                if (spReportEntitiesSub != null) {
                    if (spReportEntitiesSub.size() > 0) {
                        realm.beginTransaction();
                        spReportEntitiesSub.deleteAllFromRealm();
                        realm.commitTransaction();
                    }
                }
                hideProgressDialog();
                ActivityUtils.finishActivity(AuraMateSettingActivity.class);
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
            }
        });
    }

    private void changeAuraName() {
        if (Validator.isEmpty(etAddUserEdt.getText().toString())) {
            showMessage(R.string.aura_mate_should_not_be_empty);
        } else {
            if (EtUtils.containsEmoji(etAddUserEdt.getText().toString())) {
                CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(AuraMateSettingActivityOversea.this, CloudCommonPopupConstants.COMMON_ONE_BUTTON);
                builder.setTitle(getResources().getString(R.string.prompt));
                builder.setMessage(getResources().getString(R.string.nickname_toast_symbol));
                builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.dismiss();
                    }
                });
                CloudCommonPopup commonPopup = builder.create();
                commonPopup.show();
            } else {
                String userEdt = etAddUserEdt.getText().toString();
                if (userEdt.length() > 20){
                    showMessage(R.string.aura_mate_should_too_long);
                    return;
                }
                httpManager.request().changeAuraName(deviceId, userPreferences.getUserId(), userEdt, String.class, new MiaoHttpManager.Callback<String>() {
                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                        hideProgressDialog();
                        EventBus.getDefault().post(new AuraMateOperateEvent(EventType.AURA_RENAME_DEVICE, equipmentId));
                        finish();
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                        hideProgressDialog();
                        showMessage(R.string.request_failed_alert);

                    }

                    @Override
                    public void onError(Exception e) {
                        hideProgressDialog();
                        showMessage(R.string.request_failed_alert);
                    }
                });
            }

        }

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        realm.close();
    }
}
