package com.czur.cloud.ui.starry.meeting.fragment

import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.viewModels
import com.czur.cloud.BuildConfig
import com.czur.cloud.R
import com.czur.cloud.ui.eshare.ESPermissionUtils
import com.czur.cloud.ui.starry.meeting.base.BaseFragment
import com.czur.cloud.ui.starry.meeting.base.FloatFragment
import com.czur.cloud.ui.starry.meeting.baselib.utils.gone
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.meeting.baselib.utils.show
import com.czur.cloud.ui.starry.meeting.common.StreamType
import com.czur.cloud.ui.starry.meeting.common.UserHandler
import com.czur.cloud.ui.starry.meeting.dialog.StarryCommonPopup
import com.czur.cloud.ui.starry.meeting.model.MeetingModel
import com.czur.cloud.ui.starry.meeting.model.ModelManager
import com.czur.cloud.ui.starry.meeting.viewmodel.ChatViewModel
import com.czur.cloud.ui.starry.meeting.viewmodel.ControlBarViewModel
import com.czur.cloud.ui.starry.meeting.viewmodel.MeetingViewModel
import com.czur.cloud.ui.starry.utils.Tools
import com.czur.cloud.ui.starry.utils.singleClick
import com.czur.czurutils.log.logI
import kotlinx.android.synthetic.main.starry_fragment_meeting_control_bar.*

/**
 * Created by 陈丰尧 on 4/19/21
 * 画面下方的控制条
 * 同时附带显示消息的功能
 */
private const val TAG = "ControlBarFragment"

open class ControlBarFragment : BaseFragment() {

    override fun getLayoutId() = R.layout.starry_fragment_meeting_control_bar

    private val meetVM: MeetingViewModel by viewModels({ requireActivity() })
    private val chatVM: ChatViewModel by viewModels({ requireActivity() })
    private val controlBarVM: ControlBarViewModel by viewModels({ requireActivity() })

    private var floatShow = false
    private var floatFragmentShow = false

    var stopShareDialog: StarryCommonPopup? = null

    private val controlBarAnim: ControlBarAnim by lazy {
        ControlBarAnim(controlActionBar, bulletLayout, controlBarBg, this)
    }


    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        val mCurrentOrientation = resources.configuration.orientation
        if (mCurrentOrientation == Configuration.ORIENTATION_PORTRAIT) {
            // If current screen is portrait
            // 成员列表
            tabMemberLl?.singleClick {
                logI("ControlBarFragment.tabMemberLl-成员列表")
                showMemberFloat(it)
            }
            // 聊天按钮
            tabChatLl?.singleClick {
                logI("ControlBarFragment.tabChatLl-聊天按钮")
                showChatFloat(it)
            }

        } else if (mCurrentOrientation == Configuration.ORIENTATION_LANDSCAPE) {
            //If current screen is landscape
            // 成员列表
            tabMemberLl?.singleClick {
                logI("ControlBarFragment.land.tabMemberLl-成员列表")
                showMemberFloatLand()
            }
            // 聊天按钮
            tabChatLl?.singleClick {
                logI("ControlBarFragment.land.tabChatLl-聊天按钮")
                showChatFloatLand()
            }
        }

    }

    override fun initView() {
        super.initView()

        // oversea底部没有5dp，视频按钮文字需要折行显示
//        val constraintSet = ConstraintSet()
//        constraintSet.clone(controlActionBar)
//        val margin = if (BuildConfig.IS_OVERSEAS) 0f else 5f
//        constraintSet.connect(R.id.tabCamRl, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, dp2px(requireContext(), margin))
//        constraintSet.connect(R.id.tabCamLl, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, dp2px(requireContext(), margin))
//        constraintSet.applyTo(controlActionBar)

        // rotate 横竖屏切换
//        mainBottomRotateBtn?.visibility = View.GONE
        Tools.setViewButtonEnable(mainBottomRotateBtn, false)
        Tools.setViewButtonEnable(tabMemberLl, false)
        Tools.setViewButtonEnable(tabShareLl, false)
        Tools.setViewButtonEnable(tabChatLl, false)
        meetVM.showMeeting.observe(this) {
            if (it) {
                Tools.setViewButtonEnable(mainBottomRotateBtn, true)
                Tools.setViewButtonEnable(tabMemberLl, true)
                Tools.setViewButtonEnable(tabShareLl, true)
                Tools.setViewButtonEnable(tabChatLl, true)
            }
        }
        mainBottomRotateBtn?.singleClick {
            logI("ControlBarFragment.mainBottomRotateBtn")
            if (floatFragmentShow) {
                return@singleClick
            }

            if (requireActivity().resources.configuration.orientation == Configuration.ORIENTATION_PORTRAIT) {
                // 标记当前横竖屏状态
                MeetingModel.currentOrientationStatus = Configuration.ORIENTATION_LANDSCAPE
                requireActivity().requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
            } else {
                // 标记当前横竖屏状态
                MeetingModel.currentOrientationStatus = Configuration.ORIENTATION_PORTRAIT
                requireActivity().requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
            }

        }


        // 切换音频状态 tabMicLl  micIv
        tabMicLl?.singleClick {
            logI("ControlBarFragment.micIv-切换音频状态:MembersModel.selfAudioInUse=${ModelManager.membersModel.selfAudioInUse}")
            if (meetVM.clickNoNetwork()) {
                return@singleClick
            }

            if (ESPermissionUtils.checkVideoAndAudioPermission()) {
                val flag = !ModelManager.membersModel.selfAudioInUse
                meetVM.switchSelfAudio(flag)
                meetVM.notMuteLocalAudioOrVideo(StreamType.AUDIO, flag)
                ModelManager.membersModel.selfAudioInUseLive.value = flag

                // 同步需要更新列表的音视频开关
                ModelManager.membersModel.updateSelfMemberListStatus()
            }else{
                meetVM.switchSelfAudio(false)
                meetVM.notMuteLocalAudioOrVideo(StreamType.AUDIO, false)
            }

        }

        // 切换视频状态 tabCamLl  cameraIv
        tabCamRl?.visibility = View.GONE
        tabCamLl?.visibility = View.VISIBLE
        tabCamLl?.singleClick {
            CamRLOrLLOnClick()
        }

        // 分享按钮
        tabShareLl?.singleClick {

            logI("ControlBarFragment.tabShareLl-分享按钮")
            launch {

                // 有他人在分享，禁止分享
                if (MeetingModel.shareMode) {
                    val isAdmin = ModelManager.membersModel.selfMember.isAdmin
                    logI("ControlBarFragment.MembersModel.selfMember=${ModelManager.membersModel.selfMember}")
                    if (meetVM.selfShareStatus.value == false) {
                        if (isAdmin) {
                            //抢占分享
                            showAdminWarmAlertDialog()
                        } else {
                            showWarmAlertDialog()
                        }
                        return@launch
                    }
                }

                // 自己在分享中
                if (meetVM.selfShareStatus.value == true) {
                    showConfirmShareScreenChangeOnDialog()
                } else {  // 还没有分享
                    if (meetVM.clickNoNetwork()) {
                        return@launch
                    }
                    meetVM.startShareScreen(
                        requireActivity() as AppCompatActivity
                    )
                }
            }
        }


        meetVM.tabMemberLl = tabMemberLl
        if (MeetingModel.currentOrientationStatus == Configuration.ORIENTATION_LANDSCAPE) {
            //If current screen is landscape
            // 成员列表
            tabMemberLl?.singleClick {
                logI("ControlBarFragment.land.tabMemberLl-成员列表")
                showMemberFloatLand()
            }
            // 聊天按钮
            tabChatLl?.singleClick {
                logI("ControlBarFragment.land.tabChatLl-聊天按钮")
                showChatFloatLand()
            }
        } else {
            // If current screen is portrait
            // 成员列表
            tabMemberLl?.singleClick {
                logI("ControlBarFragment.tabMemberLl-成员列表")
                showMemberFloat(it)
            }
            // 聊天按钮
            tabChatLl?.singleClick {
                logI("ControlBarFragment.tabChatLl-聊天按钮")
                showChatFloat(it)
            }
        }

        // 初始设置自己的麦克风音量数据源
        val selfAudio = meetVM.selfAudioInUse.value ?: true
        if (selfAudio) {
            micIv?.setVolumeData(meetVM.getSelfVolumeLiveData())
        }
    }

    private fun CamRLOrLLOnClick() {
        logI("ControlBarFragment.cameraIv-切换视频状态")
        if (meetVM.clickNoNetwork()) {
            return
        }

        if (ESPermissionUtils.checkVideoAndAudioPermission()) {
            val flag = !ModelManager.membersModel.selfVideoInUse
            meetVM.switchSelfVideo(flag)
            meetVM.notMuteLocalAudioOrVideo(StreamType.VIDEO, flag)
            ModelManager.membersModel.selfVideoInUseLive.value = flag

            // 同步需要更新列表的音视频开关
            ModelManager.membersModel.updateSelfMemberListStatus()
        } else {
            meetVM.switchSelfVideo(false)
            meetVM.notMuteLocalAudioOrVideo(StreamType.VIDEO, false)
        }
    }


    private fun showConfirmShareScreenChangeOnDialog() {
        if (meetVM.isStopShareDialogShowing.value == false) {
            stopShareDialog =
                StarryCommonPopup.Builder(requireActivity())
                    .setTitle(getString(R.string.starry_popupwindow_title))
                    .setMessage(getString(R.string.starry_popupwindow_msg_sharescreen_change))
                    .setOnPositiveListener { dialog, _ ->
                        meetVM.stopShareScreen(requireContext(), true)
                        dialog?.dismiss()
                    }
                    .setOnDismissListener{
                        meetVM.isStopShareDialogShowing.postValue(false)
                    }
                    .setOnNegativeListener { dialog, _ -> dialog.dismiss() }
                    .create()
            meetVM.isStopShareDialogShowing.postValue(true)
            stopShareDialog!!.show()
        }

    }


    /**
     * 显示Member的Float
     */
    private fun showMemberFloat(it: View) {
//        floatShow = true
        floatFragmentShow = true
        controlBarVM.memberListFragment = MemberListFragment()
        controlBarVM.memberListFragment?.setOnDismissListener {
            logI("ControlBarFragment.showMemberFloat.setOnDismissListener")
//            floatShow = false
            floatFragmentShow = false
        }
        controlBarVM.memberListFragment?.show(FloatFragment.ByViewParams(it).apply {
            location = FloatFragment.ByViewParams.Location.NONE
            align = FloatFragment.ByViewParams.Align.NONE
            marginY = 10
            marginX = 10
        })

    }

    private fun showMemberFloatLand() {
        floatFragmentShow = true
        controlBarVM.memberListFragment = MemberListFragment()
        controlBarVM.memberListFragment?.setOnDismissListener {
            floatFragmentShow = false
        }
        controlBarVM.memberListFragment?.show(
            FloatFragment.ByScreenParams(),
            FloatFragment.AnimDirection.RIGHT
        )
    }

    /**
     * 显示聊天页面
     */
    private fun showChatFloat(it: View) {
//        floatShow = true
        floatFragmentShow = true
        controlBarVM.chartFragment = MeetingChartFragment()
        controlBarVM.chartFragment?.setOnDismissListener {
//                floatShow = false
            floatFragmentShow = false
        }
        controlBarVM.chartFragment?.show(FloatFragment.ByViewParams(it).apply {
            location = FloatFragment.ByViewParams.Location.NONE
            align = FloatFragment.ByViewParams.Align.NONE
            marginY = 10
            marginX = 10
        })
    }

    private fun showChatFloatLand() {
        floatFragmentShow = true
        controlBarVM.chartFragment = MeetingChartFragment()
        controlBarVM.chartFragment?.setOnDismissListener {
            floatFragmentShow = false
        }
        controlBarVM.chartFragment?.show(
            FloatFragment.ByScreenParams(),
            FloatFragment.AnimDirection.RIGHT
        )
    }

    override fun initData() {
        super.initData()


        meetVM.selfShareStatus.observe(this) {
            if (it) {
                shareScreenIv?.isActivated = true
                shareScreenTv?.setTextColor(requireActivity().getColor(R.color.starry_text_color_red))
                shareScreenTv?.text = getString(R.string.starry_main_share_close)
            } else {
                shareScreenIv?.isActivated = false
                shareScreenTv?.setTextColor(requireActivity().getColor(R.color.white))
                shareScreenTv?.text = getString(R.string.starry_main_share)
            }
        }

        meetVM.memberList.observe(this) {
            // 参会人数
            val maxCount = it.size
//            val currentCount = it.count { it1 ->
//                it1.status == 2 || it1.isAdmin
//            }

            if (maxCount > 0) {
                val title = getString(R.string.starry_main_member_F, maxCount)
                showMembersTv.text = title

                if (BuildConfig.IS_OVERSEAS && maxCount>9){
                    showMembersTv?.textSize = 8f
                }

                // 成员列表按钮
                Tools.setViewButtonEnable(tabMemberLl, true)
            }else{
                // 成员列表按钮
                Tools.setViewButtonEnable(tabMemberLl, false)
            }
        }

        chatVM.unreadMsgCount.observe(this) {
            if (it > 0) {
                unreadCountTv.show()
                unreadCountTv.text = if (it < 100) {
                    it.toString()
                } else {
                    "99+"
                }
            } else {
                unreadCountTv.gone()
            }
        }

        // 视频状态
        meetVM.selfVideoInUse.observe(this) {
            if (it) {
                cameraIv.setImageResource(R.mipmap.starry_meeting_ic_tab_video_on)
                cameraTv.setText(R.string.starry_main_video_on)
                cameraTv.setTextColor(requireContext().getColor(R.color.white))
                cameraIvRL.setImageResource(R.mipmap.starry_meeting_ic_tab_video_on)
                cameraTvRL.setText(R.string.starry_main_video_on)
                cameraTvRL.setTextColor(requireContext().getColor(R.color.white))
            } else {
                cameraIv.setImageResource(R.mipmap.starry_meeting_ic_tab_video_off)
                cameraTv.setText(R.string.starry_main_video_off)
                cameraTv.setTextColor(requireContext().getColor(R.color.starry_text_color_red))
                cameraIvRL.setImageResource(R.mipmap.starry_meeting_ic_tab_video_off)
                cameraTvRL.setText(R.string.starry_main_video_off)
                cameraTvRL.setTextColor(requireContext().getColor(R.color.starry_text_color_red))
            }
        }

        // 音频状态
        meetVM.selfAudioInUse.observe(this) {
            if (it) {
//                Log.i("ControlBarFragment", "========ControlBarFragment.getSelfVolumeLiveData.volumeData=${meetVM.getSelfVolumeLiveData().value}===============================================================")
                micIv?.setVolumeData(meetVM.getSelfVolumeLiveData())
                micIv?.setImageResource(R.mipmap.starry_meeting_ic_tab_mic_on)
                micTv?.setText(R.string.starry_main_audio_on)
                micTv?.setTextColor(requireContext().getColor(R.color.white))
            } else {
                micIv?.removeVolumeData()
                micIv?.setImageResource(R.mipmap.starry_meeting_ic_tab_mic_off)
                micTv?.setText(R.string.starry_main_audio_off)
                micTv?.setTextColor(requireContext().getColor(R.color.starry_text_color_red))
            }
        }

        // 聊天弹幕
        chatVM.bulletScreenShow.observe(this) {
            bulletLayout.visibility = if (it) View.GONE else View.VISIBLE
            if (it) {
                bulletLayout.clearAllMsg()
            }
        }

        chatVM.lastReceiveMsg.observe(this) { msg ->
            msg?.let {
                bulletLayout.addMessage(it)
            }
        }

        // 用户是否空闲的监听
        controlBarVM.userIdle.observe(this) { userIdle ->
            // userIdle=true:用户没有操作, 并且没有悬浮窗弹出时,隐藏
            // userIdle=false:显示的时候, 先显示背景,再显示操作栏
            controlBarAnim.updateAnim(userIdle, floatShow)
        }

    }

    private fun showWarmAlertDialog() {
        showMessage(R.string.starry_meeting_share_forbid)
//        val builder = StarryCommonPopupAlert.Builder(requireActivity())
//            .setTitle(getString(R.string.starry_popupwindow_title))
//            .setMessage(getString(R.string.starry_meeting_share_forbid))
//            .setOnPositiveListener { dialog, _ ->
//                dialog?.dismiss()
//            }
//            .create()
//            .show()
    }

    private fun showAdminWarmAlertDialog() {
        StarryCommonPopup.Builder(requireActivity())
            .setTitle(getString(R.string.starry_popupwindow_title))
            .setMessage(getString(R.string.starry_meeting_share_admin))
            .setPositiveTitle(getString(R.string.starry_common_dialog_yes))
            .setNegativeTitle(getString(R.string.starry_common_dialog_not))
            .setOnPositiveListener { dialog, _ ->
                onQuitShareAndShare()
                dialog?.dismiss()
            }
            .setOnNegativeListener { dialog, _ -> dialog.dismiss() }
            .create()
            .show()
    }

    // 停止他人分享，开始自己分享
    private fun onQuitShareAndShare() {
        launch {
            val meetingNo = UserHandler.accountNo.toString()
            val targetNo = ModelManager.membersModel.getShareMember().accountId
            MeetingModel.quitShare(meetingNo, targetNo)
            MeetingModel.isQuitShare = true
        }
    }

    override fun onDestroyView() {
        if (stopShareDialog != null && stopShareDialog?.isShowing == true) {
            stopShareDialog?.dismiss()
        }
        super.onDestroyView()

    }

}