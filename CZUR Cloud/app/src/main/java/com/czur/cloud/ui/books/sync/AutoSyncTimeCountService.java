package com.czur.cloud.ui.books.sync;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.blankj.utilcode.util.ServiceUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.czur.cloud.R;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.ui.starry.livedatabus.BlankActivityForNotification;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Created by Yz on 2018/5/11
 * Email：<EMAIL>
 */


public class AutoSyncTimeCountService extends BaseService {

    public static final int AUTO_SYNC_TIME = 1 * 1000;
    private SimpleDateFormat formatter;
    private int i;
    private boolean isRun;


    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        EventBus.getDefault().register(this);
        initNotification();
        initComponent();
        initThreadToSync();
//        initNotification();
    }


    private void initNotification() {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            int messageId = 1;
            Intent intent = new Intent(getApplicationContext(), BlankActivityForNotification.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//            PendingIntent contentIntent = PendingIntent.getActivity(getApplicationContext(), messageId, intent, PendingIntent.FLAG_UPDATE_CURRENT);
            PendingIntent contentIntent;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S){
                contentIntent = PendingIntent.getActivity(getApplicationContext(), messageId, intent, PendingIntent.FLAG_IMMUTABLE);
            }else {
                contentIntent = PendingIntent.getActivity(getApplicationContext(), messageId, intent, PendingIntent.FLAG_UPDATE_CURRENT);
            }

            // 提高进程优先级 ，就会在通知栏中出现自己的应用，如果不想提高优先级，可以把这个注释
            // 参数1：id 参数2：通知
            String channelId = "com.czur.cloud";
            String channelName = getString(R.string.background);
            ;
            NotificationChannel notificationChannel = null;

            notificationChannel = new NotificationChannel(channelId,
                    channelName, NotificationManager.IMPORTANCE_HIGH);
            notificationChannel.setShowBadge(false);
            notificationChannel.enableVibration(false);
            notificationChannel.setSound(null, null);
            notificationChannel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
            NotificationManager manager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            manager.createNotificationChannel(notificationChannel);
            Notification notification = new NotificationCompat.Builder(this)
                    .setChannelId(channelId)
                    .setContentTitle(getString(R.string.aura_mate_title))
                    .setCategory(Notification.CATEGORY_CALL)
                    .setWhen(System.currentTimeMillis())
                    .setSmallIcon(R.mipmap.small_icon)
                    .setContentIntent(contentIntent)
                    .build();
            startForeground(messageId, notification);
        }
    }


    /**
     * Activity中一启动Service之后，就会调用 onStartCommand()方法
     */
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        initNotification();
        return START_STICKY;
    }

    private void initComponent() {
        formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        isRun = true;
        i = 0;

    }

    /**
     * @des: 初始化单线程线程池
     * @params:
     * @return:
     */
    private void initThreadToSync() {

        String curDate = formatter.format(new Date(System.currentTimeMillis()));
        new Thread(new Runnable() {
            @Override
            public void run() {
                while (i <= 59) {
                    try {
                        if (!isRun) {
                            stopTimeCount();
                            return;
                        }
                        i++;
                        Thread.sleep(AUTO_SYNC_TIME);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
                if (!ServiceUtils.isServiceRunning(SyncService.class)) {
                    Intent intent = new Intent(AutoSyncTimeCountService.this, SyncService.class);
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        startForegroundService(intent);
                    } else {
                        startService(intent);
                    }
                }
                stopTimeCount();
            }

        }).start();

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case STOP_SYNC_TIME_COUNT:
                i = 0;
                isRun = false;
                break;

            case RESET_TIME_COUNT:
                i = 0;
                isRun = true;
                break;
            default:
                break;
        }
    }

    /**
     * @des: 停止Service
     * @params:
     * @return:
     */

    private void stopTimeCount() {
        isRun = false;
        if (ServiceUtils.isServiceRunning(AutoSyncTimeCountService.class)) {
            stopSelf();
            ServiceUtils.stopService(AutoSyncTimeCountService.class);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            stopForeground(true);
        } else {
            stopSelf();
        }
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

    public void showMessage(int resId) {
        ToastUtils.showShort(resId);
    }

    public void showMessage(String text) {
        ToastUtils.showShort(text);
    }
}

