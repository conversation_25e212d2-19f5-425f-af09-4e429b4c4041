package com.czur.cloud.ui.account;

import static com.czur.czurutils.log.CZURLogUtilsKt.logE;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.CleanUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.KeyboardUtils;
import com.blankj.utilcode.util.RegexUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.blankj.utilcode.util.Utils;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.entity.realm.BookEntity;
import com.czur.cloud.entity.realm.BookPdfEntity;
import com.czur.cloud.entity.realm.DownloadEntity;
import com.czur.cloud.entity.realm.HomeCacheEntity;
import com.czur.cloud.entity.realm.OcrEntity;
import com.czur.cloud.entity.realm.PageEntity;
import com.czur.cloud.entity.realm.PdfDownloadEntity;
import com.czur.cloud.entity.realm.PdfEntity;
import com.czur.cloud.entity.realm.SPReportEntity;
import com.czur.cloud.entity.realm.SPReportEntitySub;
import com.czur.cloud.entity.realm.SPReportSittingEntity;
import com.czur.cloud.entity.realm.SyncBookEntity;
import com.czur.cloud.entity.realm.SyncPageEntity;
import com.czur.cloud.entity.realm.SyncPdfEntity;
import com.czur.cloud.entity.realm.SyncTagEntity;
import com.czur.cloud.entity.realm.TagEntity;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.LoginEvent;
import com.czur.cloud.model.ChannelModel;
import com.czur.cloud.model.RegisterModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.FirstPreferences;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.NoHintEditText;
import com.czur.cloud.ui.component.ProgressButton;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.home.IndexActivity;
import com.czur.cloud.util.EtUtils;
import com.czur.cloud.util.validator.StringUtilsKt;
import com.czur.cloud.util.validator.Validator;
import com.facebook.drawee.backends.pipeline.Fresco;

import org.greenrobot.eventbus.EventBus;

import java.io.File;
import java.util.Locale;

import io.realm.Realm;

/**
 * Created by Yz on 2018/3/7.
 * Email：<EMAIL>
 */

public class ThirdPartyBindActivity extends BaseActivity implements View.OnClickListener {

    private ImageView accountBackBtn;
    private TextView accountTitle;
    private NoHintEditText bindThirdPartyAccountEdt;
    private NoHintEditText codeEdt;
    private TextView getCodeBtn;
    private ProgressButton nextStepBtn;
    private TimeCount timeCount;
    private boolean isMobile = false;
    private boolean isEmail = false;
    private boolean codeHasContent = false;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private String platName;
    private String userId;
    private String thirdPartyToken;
    private String thirdPartyOpenId;
    private String thirdPartyPlatName;
    private String thirdPartyRefreshToken;
    private Realm realm;
    private FirstPreferences firstPreferences;
    private CloudCommonPopup commonPopup;
    private long currentTime;
    private String account;
    private String code;
    private boolean isBind;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_bind_third_party);
        initComponent();
        registerEvent();
    }

    private void initComponent() {
        realm = Realm.getDefaultInstance();
        firstPreferences = FirstPreferences.getInstance(this);
        httpManager = HttpManager.getInstance();
        userPreferences = UserPreferences.getInstance(this);
        thirdPartyToken = getIntent().getStringExtra("thirdPartyToken");
        thirdPartyOpenId = getIntent().getStringExtra("thirdPartyOpenId");
        thirdPartyPlatName = getIntent().getStringExtra("thirdPartyPlatName");
        thirdPartyRefreshToken = getIntent().getStringExtra("thirdPartyRefreshToken");
        platName = getIntent().getStringExtra("platName");
        userId = getIntent().getStringExtra("userId");

        accountBackBtn = (ImageView) findViewById(R.id.account_back_btn);
        accountTitle = (TextView) findViewById(R.id.account_title);
        bindThirdPartyAccountEdt = (NoHintEditText) findViewById(R.id.bind_third_party_account_edt);
        codeEdt = (NoHintEditText) findViewById(R.id.bind_third_party_code_edt);
        getCodeBtn = (TextView) findViewById(R.id.get_code_btn);
        nextStepBtn = (ProgressButton) findViewById(R.id.next_step_btn);

        //设置标题
        accountTitle.setText(R.string.bind_third_party_account);
    }

    private void registerEvent() {
        accountBackBtn.setOnClickListener(this);
        getCodeBtn.setOnClickListener(this);
        getCodeBtn.setSelected(true);

        nextStepBtn.setOnClickListener(this);
        nextStepBtn.setOnProgressFinishListener(onProgressFinish);
        nextStepBtn.setSelected(false);
        nextStepBtn.setClickable(false);
        bindThirdPartyAccountEdt.addTextChangedListener(accountTextWatcher);
        codeEdt.addTextChangedListener(codeTextWatcher);
    }

    private ProgressButton.OnProgressFinish onProgressFinish = new ProgressButton.OnProgressFinish() {
        @Override
        public void onFinish() {
            if (isBind) {
                bindAccount();
            } else {
                goRegister();
            }
        }
    };

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.get_code_btn:
                validatorAccount();
                break;
            case R.id.next_step_btn:
                if (isMobile) {
                    confirmMobileIdentifyCode();
                } else if (isEmail) {
                    confirmMailIdentifyCode();
                } else {
                    showMessage(R.string.account_format_error);
                }
                break;
            case R.id.account_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            default:
                break;
        }
    }


    /**
     * @des: 验证手机号或者邮箱
     * @params:[]
     * @return:void
     */
    private void validatorAccount() {

        String accountStr = bindThirdPartyAccountEdt.getText().toString();
        if (Validator.isEmpty(accountStr)) {
            showMessage(R.string.account_empty);
        } else {
            if (RegexUtils.isMobileExact(accountStr)) {
                getMobileCode(accountStr);
            } else if (StringUtilsKt.isValidEmail(accountStr)) {
                getMailCode(accountStr);
            } else {
                showMessage(R.string.account_format_error);
            }

        }
    }

    /**
     * @des: 获取手机验证码
     * @params:[accountStr]
     * @return:void
     */
    private void getMobileCode(String accountStr) {

        httpManager.requestPassport().mobileCode(accountStr, String.class, new MiaoHttpManager.Callback<String>() {
            @Override
            public void onStart() {

            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {

                timeCountBegin();
                showMessage(R.string.toast_code_send);

            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {

                if (entity.getCode() == MiaoHttpManager.STATUS_CODE_1_MIN) {
                    showMessage(R.string.toast_code_1_min);
                } else if (entity.getCode() == MiaoHttpManager.STATUS_5_MIN_4_TIME) {
                    showMessage(R.string.toast_5_min_4_time);
                } else if (entity.getCode() == MiaoHttpManager.STATUS_5_TIME_IN_ONE_DAY) {
                    showMessage(R.string.toast_5_time_in_one_day);
                } else {
                    showMessage(R.string.request_failed_alert);
                }

            }

            @Override
            public void onError(Exception e) {

                showMessage((R.string.request_failed_alert));
            }
        });
    }

    /**
     * @des: 获取邮箱验证码
     * @params:[accountStr]
     * @return:void
     */
    private void getMailCode(String accountStr) {

        Locale locale = getResources().getConfiguration().locale;
        String language = locale.toString();
        HttpManager.getInstance().requestPassport().mailCode(
                accountStr,
                EtUtils.getLocale(language),
                String.class,
                new MiaoHttpManager.Callback<String>() {
            @Override
            public void onStart() {

            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {

                timeCountBegin();
                showMessage(R.string.toast_code_send);

            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {

                if (entity.getCode() == MiaoHttpManager.STATUS_CODE_1_MIN) {
                    showMessage(R.string.toast_code_1_min);
                } else if (entity.getCode() == MiaoHttpManager.STATUS_5_MIN_4_TIME) {
                    showMessage(R.string.toast_5_min_4_time);
                } else if (entity.getCode() == MiaoHttpManager.STATUS_5_TIME_IN_ONE_DAY) {
                    showMessage(R.string.toast_5_time_in_one_day);
                } else {
                    showMessage(R.string.request_failed_alert);
                }

            }

            @Override
            public void onError(Exception e) {

                showMessage(R.string.request_failed_alert);

            }
        });
    }

    /**
     * @des: 校验手机验证码
     * @params:
     * @return:
     */

    private void confirmMobileIdentifyCode() {
        currentTime = System.currentTimeMillis();
        KeyboardUtils.hideSoftInput(this);
        account = bindThirdPartyAccountEdt.getText().toString();
        code = codeEdt.getText().toString();
        httpManager.requestPassport().confirmThirdPartyByMobile(userPreferences.getChannel(), userPreferences.getIMEI(), CZURConstants.CLOUD_ANDROID,
                account, platName, code, String.class, new MiaoHttpManager.Callback<String>() {
                    @Override
                    public void onStart() {
                        nextStepBtn.startLoading();
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                        isBind = false;
                        registerSuccessDelay();
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                        if (entity.getCode() == MiaoHttpManager.STATUS_HAS_NOT_BINDED_THIRD_PARTY_BY_MOBILE) {
                            isBind = true;
                            registerSuccessDelay();
                        } else if (entity.getCode() == MiaoHttpManager.STATUS_HAS_BINDED_THIRD_PARTY_BY_MOBILE) {
                            registerFailedDelay(-1, String.format(getString(R.string.mobile_has_binded), platName), true);
                        } else if (entity.getCode() == MiaoHttpManager.STATUS_INVALID_MOBILE) {
                            registerFailedDelay(R.string.invalid_mobile, null, false);
                        } else if (entity.getCode() == MiaoHttpManager.STATUS_INVALID_CODE) {
                            registerFailedDelay(R.string.toast_code_error, null, false);
                        } else if (entity.getCode() == MiaoHttpManager.STATUS_FAIL) {
                            registerFailedDelay(R.string.toast_internal_error, null, false);
                        } else {
                            registerFailedDelay(R.string.request_failed_alert, null, false);
                        }
                    }

                    @Override
                    public void onError(Exception e) {
                        registerFailedDelay(R.string.request_failed_alert, null, false);
                    }
                });
    }

    public void goRegister() {
        Intent intent = new Intent(ThirdPartyBindActivity.this, ThirdPartyRegisterActivity.class);
        intent.putExtra("account", account);
        intent.putExtra("code", code);
        intent.putExtra("userId", userId);
        intent.putExtra("thirdPartyOpenId", thirdPartyOpenId);
        intent.putExtra("thirdPartyToken", thirdPartyToken);
        intent.putExtra("thirdPartyPlatName", thirdPartyPlatName);
        intent.putExtra("platName", platName);
        intent.putExtra("thirdPartyRefreshToken", thirdPartyRefreshToken);
        ActivityUtils.startActivity(intent);
    }

    private void registerFailedDelay(final int failedText, final String toast, final boolean isString) {

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    long sleepTime;
                    if (System.currentTimeMillis() - currentTime < 1000) {
                        sleepTime = 1000 - (System.currentTimeMillis() - currentTime);
                    } else {
                        sleepTime = 1;
                    }
                    Thread.sleep(sleepTime);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (isString) {
                                showMessage(toast);
                            } else {
                                showMessage(failedText);
                            }

                            nextStepBtn.stopLoading();
                        }
                    });
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }


    private void registerSuccessDelay() {

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    long sleepTime;
                    if (System.currentTimeMillis() - currentTime < 1000) {
                        sleepTime = 1000 - (System.currentTimeMillis() - currentTime);
                    } else {
                        sleepTime = 1;
                    }
                    Thread.sleep(sleepTime);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            nextStepBtn.stopLoadingSuccess();
                        }
                    });
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }

    /**
     * @des: 校验邮箱验证码
     * @params:
     * @return:
     */

    private void confirmMailIdentifyCode() {
        currentTime = System.currentTimeMillis();
        KeyboardUtils.hideSoftInput(this);
        account = bindThirdPartyAccountEdt.getText().toString();
        code = codeEdt.getText().toString();
        httpManager.requestPassport().confirmThirdPartyByEmail(userPreferences.getChannel(), userPreferences.getIMEI(), CZURConstants.CLOUD_ANDROID,
                account, platName, code, String.class, new MiaoHttpManager.Callback<String>() {
                    @Override
                    public void onStart() {
                        nextStepBtn.startLoading();
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                        isBind = false;
                        registerSuccessDelay();
                    }

                    @Override
                    public void onFailure(final MiaoHttpEntity<String> entity) {
                        if (entity.getCode() == MiaoHttpManager.STATUS_HAS_NOT_BINDED_THIRD_PARTY_BY_EMAIL) {
                            isBind = true;
                            registerSuccessDelay();
                        } else if (entity.getCode() == MiaoHttpManager.STATUS_HAS_BINDED_THIRD_PARTY_BY_EMAIL) {
                            registerFailedDelay(-1, String.format(getString(R.string.email_has_binded), platName), true);
                        } else if (entity.getCode() == MiaoHttpManager.STATUS_INVALID_EMAIL) {
                            registerFailedDelay(R.string.invalid_email, null, false);
                        } else if (entity.getCode() == MiaoHttpManager.STATUS_INVALID_CODE) {
                            registerFailedDelay(R.string.toast_code_error, null, false);
                        } else if (entity.getCode() == MiaoHttpManager.STATUS_FAIL) {
                            registerFailedDelay(R.string.toast_internal_error, null, false);
                        } else {
                            registerFailedDelay(R.string.request_failed_alert, null, false);
                        }
                    }

                    @Override
                    public void onError(Exception e) {
                        registerFailedDelay(R.string.request_failed_alert, null, false);
                    }
                });
    }

    public void bindAccount() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(ThirdPartyBindActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON_YES_NO);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getString(R.string.exist_to_merge));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (TextUtils.isEmpty(userPreferences.getChannel())) {
                    getChannel();
                } else {
                    bindThirdPartyAccount();
                }
                dialog.dismiss();
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                ActivityUtils.finishActivity(ThirdPartyBindActivity.this);
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();
    }

    private void bindThirdPartyAccount() {
        String account = bindThirdPartyAccountEdt.getText().toString();
        String code = codeEdt.getText().toString();
        //userPreferences.getChannel()
        HttpManager.getInstance().requestPassport().thirdPartyBind(userPreferences.getChannel(), userPreferences.getIMEI(), CZURConstants.CLOUD_ANDROID, userId, account, code,
                RegisterModel.class, new MiaoHttpManager.Callback<RegisterModel>() {
                    @Override
                    public void onStart() {
                        showProgressDialog();
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<RegisterModel> entity) {
                        hideProgressDialog();
                        confirmToClearLastUserData(entity);
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<RegisterModel> entity) {
                        hideProgressDialog();
                        switch (entity.getCode()) {

                            case MiaoHttpManager.STATUS_INVALID_THIRD_PARTY:
                                showMessage(R.string.toast_error_third_party_user);
                                break;
                            case MiaoHttpManager.STATUS_NOT_USER:
                                showMessage(R.string.toast_user_no_exist);
                                break;
                            case MiaoHttpManager.STATUS_INVALID_CODE:
                                showMessage(R.string.toast_code_error);
                                break;
                            case MiaoHttpManager.STATUS_ERROR:
                                showMessage(R.string.toast_internal_error);
                                break;
                            default:
                                showMessage(R.string.request_failed_alert);
                                break;
                        }
                    }

                    @Override
                    public void onError(Exception e) {
                        logE(e.toString());
                        hideProgressDialog();
                        showMessage(R.string.request_failed_alert);
                    }
                });


    }

    /**
     * @des:获取channel
     * @params:[userId, token, platformName, isThirdParty, mobileMail, pwd]
     * @return:void
     */
    private void getChannel() {


        HttpManager.getInstance().request().channel(ChannelModel.class, new MiaoHttpManager.Callback<ChannelModel>() {
            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<ChannelModel> entity) {
                hideProgressDialog();
                userPreferences.setChannel(entity.getBody().getChannel());
                userPreferences.setEndpoint(entity.getBody().getEndPoint());
                bindThirdPartyAccount();
            }

            @Override
            public void onFailure(MiaoHttpEntity<ChannelModel> entity) {
                hideProgressDialog();
                showMessage(R.string.request_failed_alert);
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
            }
        });
    }

    /**
     * @des: 确认删除用户信息
     * @params:
     * @return:
     */

    private void confirmToClearLastUserData(final MiaoHttpEntity<RegisterModel> entity) {
        String currentUserId = entity.getBody().getId();
        String account = bindThirdPartyAccountEdt.getText().toString();
        boolean isSameAccount = true;
        if (RegexUtils.isMobileExact(account)) {
            isSameAccount = entity.getBody().getMobile().equals(userPreferences.getUserMobile());
        } else if (StringUtilsKt.isValidEmail(account)) {
            isSameAccount = entity.getBody().getEmail().equals(userPreferences.getUserEmail());
        } else {
            isSameAccount = false;
        }
        if (!isSameAccount && userPreferences.isValidUser()) {

            CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(ThirdPartyBindActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON_YES_NO);
            builder.setTitle(getResources().getString(R.string.prompt));
            String title = String.format(getString(R.string.confirm_to_clear_account), userPreferences.getUserName());
            builder.setMessage(title);
            builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    if (commonPopup != null) {
                        commonPopup.dismiss();
                    }
                    clearLastUserDataAndSetCurrentData(entity);

                }
            });
            builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    dialog.dismiss();
                    ActivityUtils.finishActivity(ThirdPartyBindActivity.this);
                }
            });
            commonPopup = builder.create();
            commonPopup.show();

        } else {
            setCurrentUserData(entity);
        }

    }

    /**
     * @des: 如果和上次userId不一样 就清除sp  data/file并且设置用户信息sp
     * @params:
     * @return:
     */

    private void clearLastUserDataAndSetCurrentData(final MiaoHttpEntity<RegisterModel> entity) {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                logI("clean last user file and sp");
                String filePath = getFilesDir() + File.separator + userPreferences.getUserId();
                FileUtils.deleteAllInDir(new File(filePath));
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        //清空sp
                        userPreferences.resetUser();
                        firstPreferences.resetFirstPreference();
                        //清空数据库
                        realm.executeTransaction(new Realm.Transaction() {
                            @Override
                            public void execute(Realm realms) {
                                realm.where(SPReportEntity.class).findAll().deleteAllFromRealm();
                                realm.where(SPReportEntitySub.class).findAll().deleteAllFromRealm();
                                realm.where(SPReportSittingEntity.class).findAll().deleteAllFromRealm();

                                realm.where(PageEntity.class).findAll().deleteAllFromRealm();
                                realm.where(BookEntity.class).findAll().deleteAllFromRealm();
                                realm.where(OcrEntity.class).findAll().deleteAllFromRealm();
                                realm.where(PdfEntity.class).findAll().deleteAllFromRealm();
                                realm.where(BookPdfEntity.class).findAll().deleteAllFromRealm();
                                realm.where(SyncPdfEntity.class).findAll().deleteAllFromRealm();
                                realm.where(TagEntity.class).findAll().deleteAllFromRealm();
                                realm.where(SyncTagEntity.class).findAll().deleteAllFromRealm();
                                realm.where(HomeCacheEntity.class).findAll().deleteAllFromRealm();
                                realm.where(SyncPageEntity.class).findAll().deleteAllFromRealm();
                                realm.where(SyncBookEntity.class).findAll().deleteAllFromRealm();
                                realm.where(PdfDownloadEntity.class).findAll().deleteAllFromRealm();
                                realm.where(DownloadEntity.class).findAll().deleteAllFromRealm();

                            }
                        });
                        Fresco.getImagePipeline().clearCaches();
                        CleanUtils.cleanCustomDir(Utils.getApp().getFilesDir() + File.separator + CZURConstants.PDF_PATH);
                        setCurrentUserData(entity);
                    }
                });
                return null;
            }

            @Override
            public void onSuccess(Void result) {

            }
        });

    }

    /**
     * @des: 显示登录成功并且跳转到主页
     * @params:
     * @return:
     */
    private void showLoginSuccessAndGoIndex() {
        EventBus.getDefault().post(new LoginEvent(EventType.THIRD_PARTY_BIND_ACCOUNT_SUCCESS));
        Intent intent = new Intent(ThirdPartyBindActivity.this, IndexActivity.class);
        intent.putExtra("needSync", true);
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
        ActivityUtils.startActivity(intent);
    }

    /**
     * @des: 存储当前用户信息到SP
     * @params:
     * @return:
     */
    private void setCurrentUserData(MiaoHttpEntity<RegisterModel> entity) {
        userPreferences.setUser(entity.getBody());
        userPreferences.setIsUserLogin(true);

        userPreferences.setIsThirdParty(true);
        userPreferences.setThirdPartyOpenid(thirdPartyOpenId);
        userPreferences.setThirdPartyToken(thirdPartyToken);
        userPreferences.setThirdPartyRefreshToken(thirdPartyRefreshToken);
        userPreferences.setThirdPartyPlatName(thirdPartyPlatName);
        userPreferences.setServicePlatName(platName);

        showLoginSuccessAndGoIndex();
    }

    /**
     * @des: 计时器
     * @params:
     * @return:
     */

    class TimeCount extends CountDownTimer {

        public TimeCount(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @Override
        public void onFinish() {
            getCodeBtn.setText(R.string.resend_code);
            getCodeBtn.setClickable(true);
            getCodeBtn.setSelected(true);
            getCodeBtn.setTextColor(getColor(R.color.black_272f44));
        }

        @Override
        public void onTick(long millisUntilFinished) {
            getCodeBtn.setClickable(false);
            getCodeBtn.setText(millisUntilFinished / 1000 + " s");
            getCodeBtn.setSelected(false);
            getCodeBtn.setTextColor(getColor(R.color.grey_AEAEAE));
        }

    }


    /**
     * @des: 计时器开始倒计时60S
     * @params:
     * @return:void
     */
    private void timeCountBegin() {
        timeCount = new TimeCount(60000, 1000);
        timeCount.start();

    }

    private TextWatcher codeTextWatcher = new TextWatcher() {


        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            // 输入的内容变化的监听
            if (s.length() > 0) {
                codeHasContent = true;
            } else {
                codeHasContent = false;
            }

            checkNextStepButtonToClick();
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count,
                                      int after) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            // 输入后的监听
            if (s.length() > 0) {
                codeHasContent = true;
            } else {
                codeHasContent = false;
            }
            checkNextStepButtonToClick();
        }
    };

    private TextWatcher accountTextWatcher = new TextWatcher() {


        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            // 输入的内容变化的监听
            judgingAccount(s);

        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count,
                                      int after) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            // 输入后的监听
            judgingAccount(s);
        }
    };

    private void judgingAccount(CharSequence s) {
        if (RegexUtils.isMobileExact(s)) {
            isMobile = true;
            isEmail = false;
        } else if (StringUtilsKt.isValidEmail(s.toString())) {
            isMobile = false;
            isEmail = true;
        } else {
            isMobile = false;
            isEmail = false;
        }
        checkNextStepButtonToClick();
    }

    /**
     * @des: 检查注册按钮是否可以点击
     * @params:
     * @return:
     */

    private void checkNextStepButtonToClick() {

        boolean accountIsNotEmpty = Validator.isNotEmpty(bindThirdPartyAccountEdt.getText().toString());
        boolean codeIsNotEmpty = Validator.isNotEmpty(codeEdt.getText().toString());

        if (accountIsNotEmpty && codeIsNotEmpty && codeHasContent) {
            nextStepBtn.setSelected(true);
            nextStepBtn.setClickable(true);
        } else {
            nextStepBtn.setSelected(false);
            nextStepBtn.setClickable(false);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (Validator.isNotEmpty(timeCount)) {
            timeCount.cancel();
        }
        if (realm != null) {
            realm.close();
        }

    }
}
