package com.czur.cloud.ui.auramate;


import static com.czur.czurutils.log.CZURLogUtilsKt.logE;
import static com.czur.czurutils.log.CZURLogUtilsKt.logTagD;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.AuraMateFolderAdapter;
import com.czur.cloud.adapter.AuraMatePopupAdapter;
import com.czur.cloud.entity.AuraEntity;
import com.czur.cloud.event.AuraCropSuccessEvent;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.DeleteFilesEvent;
import com.czur.cloud.event.SwitchAuraFlattenEvent;
import com.czur.cloud.event.SwitchAuraMateColorEvent;
import com.czur.cloud.model.AuraCropModel;
import com.czur.cloud.model.AuraDeviceModel;
import com.czur.cloud.model.AuraHomeFileModel;
import com.czur.cloud.model.AuraMateColorModel;
import com.czur.cloud.model.AuraMateDeviceModel;
import com.czur.cloud.model.AuraMateNewFileRemind;
import com.czur.cloud.model.AuraResultModel;
import com.czur.cloud.model.PdfModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.LazyLoadBaseFragment;
import com.czur.cloud.ui.component.AuraLoadingView;
import com.czur.cloud.ui.component.popup.AuraHomePopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.component.popup.ProgressPopup;
import com.czur.cloud.ui.component.progressbar.RoundedRectProgressBar;
import com.czur.cloud.util.EtUtils;
import com.czur.cloud.util.validator.Validator;
import com.google.gson.reflect.TypeToken;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnLoadMoreListener;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.jetbrains.annotations.NotNull;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import io.realm.Realm;


public class AuraMateFilesFragment extends LazyLoadBaseFragment implements View.OnClickListener {

    private TextView auraHomeCurrentItemTv;
    private TextView auraHomeSelectAllBtn;
    private TextView auraHomeCancelBtn;
    private TextView auraFilesTitleTv;
    private RelativeLayout auraHomeUnselectedTopBarRl;
    private RelativeLayout auraHomeCurrentItemRl;
    private RelativeLayout auraHomeAddBtn;
    private RelativeLayout auraHomeMultiSelectBtn;
    private RecyclerView dialogRecyclerView;
    private LinearLayout auraHomeBottomLl;
    private RelativeLayout auraHomeRenameRl;
    private RelativeLayout auraHomePdfRl;
    private RelativeLayout auraHomeMoveRl;
    private RelativeLayout saveMovieRl;
    private RelativeLayout auraHomeDeleteRl;
    private ImageView auraHomeRenameImg;
    private TextView auraHomeRenameTv;
    private ImageView auraHomePdfImg;
    private TextView auraHomePdfTv;
    private ImageView auraHomeMoveImg;
    private TextView auraHomeMoveTv;
    private ImageView auraHomeDeleteImg;
    private TextView auraHomeDeleteTv;
    private AuraHomePopup.Builder builder;
    private AuraHomePopup auraHomePopup;
    private ImageView auraHomeCurrentItemImg;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private List<AuraMateDeviceModel> datas;
    private AuraMatePopupAdapter deviceAdapter;
    private SmartRefreshLayout refreshLayout;
    private View auraMateEmptyRl;
    private List<AuraHomeFileModel.FilesBean> filesBeans;
    private List<AuraMateDeviceModel> deviceModels;
    private List<AuraHomeFileModel.FoldersBean> foldersBeans;
    private List<AuraHomeFileModel.FilesBean> addFilesBeans;
    private List<AuraHomeFileModel.FoldersBean> addFoldersBeans;
    private List<String> folderIds;
    private List<String> fileIds;
    private List<String> pdfIds;
    private String seqNum;
    private String type;
    private String folderName;
    private AuraMateFolderAdapter auraFolderAdapter;
    private RecyclerView auraRecyclerView;
    private LinkedHashMap<String, AuraEntity> isCheckedMap = new LinkedHashMap<>();
    private EditText dialogEdt;
    private String equipmentId;
    private String initEquipmentId, initRelationId;
    private String ownerId;
    private AuraLoadingView loadingView;
    private SimpleDateFormat formatter;
    private boolean isPdfRun = true;
    private ProgressPopup progressPopup;
    private RoundedRectProgressBar progressBar;
    private TextView pdfDialogTitle;
    private AuraMateDeviceModel currentModel;
    private RelativeLayout wrongQuestionRl;
    private AuraMateActivity activity;
    private RelativeLayout rlNoNetWork;
    private TextView tvNoNetWork;
    private Realm realm;

    public void setNeedRefresh(boolean needRefresh) {
        this.needRefresh = needRefresh;
    }

    private boolean needRefresh = false;

    public static AuraMateFilesFragment newInstance(String device, String relationId) {
        AuraMateFilesFragment auraMateFilesFragment = new AuraMateFilesFragment();
        Bundle bundle = new Bundle();
        bundle.putString("device", device);
        bundle.putString("relationId", relationId);
        auraMateFilesFragment.setArguments(bundle);
        return auraMateFilesFragment;
    }


    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_aura_home_files;
    }


    @Override
    protected void initView(View view) {
        realm = Realm.getDefaultInstance();
        datas = new ArrayList<>();
        EventBus.getDefault().register(this);
        activity = (AuraMateActivity) getActivity();
        httpManager = HttpManager.getInstance();
        userPreferences = UserPreferences.getInstance(getActivity());
        formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        loadingView = AuraLoadingView.addFullScreen(getActivity(), (RelativeLayout) view.findViewById(R.id.loading_container).getRootView());
        loadingView.startLoading();
        wrongQuestionRl = (RelativeLayout) view.findViewById(R.id.wrong_question_rl);
        refreshLayout = view.findViewById(R.id.refresh_layout);
        auraMateEmptyRl = view.findViewById(R.id.aura_mate_empty_rl);
        auraFilesTitleTv = (TextView) view.findViewById(R.id.aura_files_title_tv);
        auraRecyclerView = (RecyclerView) view.findViewById(R.id.aura_home_recyclerView);
        auraHomeCurrentItemTv = (TextView) view.findViewById(R.id.aura_home_current_item_tv);
        auraHomeCurrentItemRl = (RelativeLayout) view.findViewById(R.id.aura_home_current_item_rl);
        auraHomeSelectAllBtn = (TextView) view.findViewById(R.id.aura_home_select_all_btn);
        auraHomeCancelBtn = (TextView) view.findViewById(R.id.aura_home_cancel_btn);
        auraHomeUnselectedTopBarRl = (RelativeLayout) view.findViewById(R.id.aura_home_unselected_top_bar_rl);
        auraHomeAddBtn = (RelativeLayout) view.findViewById(R.id.aura_home_add_btn);
        auraHomeMultiSelectBtn = (RelativeLayout) view.findViewById(R.id.aura_home_multi_select_btn);
        FragmentActivity activity = getActivity();
        auraHomeBottomLl = (LinearLayout) activity.findViewById(R.id.aura_home_bottom_ll);
        auraHomeRenameRl = (RelativeLayout) activity.findViewById(R.id.aura_home_rename_rl);
        auraHomePdfRl = (RelativeLayout) activity.findViewById(R.id.aura_home_pdf_rl);
        auraHomeMoveRl = (RelativeLayout) activity.findViewById(R.id.aura_home_move_rl);
        saveMovieRl = (RelativeLayout) activity.findViewById(R.id.save_movie_rl);
        auraHomeDeleteRl = (RelativeLayout) activity.findViewById(R.id.aura_home_delete_rl);
        auraHomeRenameImg = (ImageView) activity.findViewById(R.id.aura_home_rename_img);
        auraHomeRenameTv = (TextView) activity.findViewById(R.id.aura_home_rename_tv);
        auraHomePdfImg = (ImageView) activity.findViewById(R.id.aura_home_pdf_img);
        auraHomePdfTv = (TextView) activity.findViewById(R.id.aura_home_pdf_tv);
        auraHomeMoveImg = (ImageView) activity.findViewById(R.id.aura_home_move_img);
        auraHomeMoveTv = (TextView) activity.findViewById(R.id.aura_home_move_tv);
        auraHomeDeleteImg = (ImageView) activity.findViewById(R.id.aura_home_delete_img);
        auraHomeDeleteTv = (TextView) activity.findViewById(R.id.aura_home_delete_tv);
        auraHomeCurrentItemImg = (ImageView) view.findViewById(R.id.aura_home_current_item_img);
        refreshLayout.setEnableOverScrollDrag(false);
        refreshLayout.setEnableOverScrollBounce(false);
        refreshLayout.setEnableAutoLoadMore(true);
        refreshLayout.setEnableRefresh(true);
        refreshLayout.setEnableNestedScroll(false);
        refreshLayout.setEnableFooterFollowWhenNoMoreData(true);
        refreshLayout.setEnableLoadMoreWhenContentNotFull(false);
        refreshLayout.setEnableLoadMore(true);
        refreshLayout.setOnLoadMoreListener(new OnLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                auraRecyclerView.stopScroll();
                loadMore();
            }
        });
        refreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                resetToFresh();
                getDevicesAndFolderList(false);
            }
        });

        initDialogList();
        initAuraFolderRecyclerView();
        rlNoNetWork = view.findViewById(R.id.rl_no_network);
        tvNoNetWork = view.findViewById(R.id.tv_click_refresh);
        tvNoNetWork.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showProgressDialog();
                rlNoNetWork.setVisibility(View.GONE);
                resetToFresh();
                getDevicesAndFolderList(false);
            }
        });
    }

    private void initNetListener() {
        NetworkUtils.registerNetworkStatusChangedListener(new NetworkUtils.OnNetworkStatusChangedListener() {
            @Override
            public void onDisconnected() {
                rlNoNetWork.setVisibility(View.VISIBLE);
            }

            @Override
            public void onConnected(NetworkUtils.NetworkType networkType) {
                rlNoNetWork.setVisibility(View.GONE);
            }
        });
    }

    /**
     * 首次可见加载数据
     */
    @Override
    public void onFragmentFirstVisible() {
        super.onFragmentFirstVisible();
        if (!needRefresh) {
            getDevicesAndFolderList(true);
        }
    }


    @Override
    public void onFragmentResume() {
        super.onFragmentResume();
        if (needRefresh) {
            needRefresh = false;
            resetToFresh();
            getDevicesAndFolderList(false);
        }
    }

    private void initDialogList() {
        builder = new AuraHomePopup.Builder(getActivity());
        auraHomePopup = builder.create();
        dialogRecyclerView = (RecyclerView) auraHomePopup.findViewById(R.id.aura_home_recyclerView);
        deviceAdapter = new AuraMatePopupAdapter(getActivity(), datas, realm);
        deviceAdapter.setOnItemClickListener(onItemClickListener);
        dialogRecyclerView.setHasFixedSize(true);
        dialogRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        dialogRecyclerView.setAdapter(deviceAdapter);
    }

    /**
     * @des: 初始化列表
     * @params:
     * @return:
     */

    private void initAuraFolderRecyclerView() {
        folderIds = new ArrayList<>();
        fileIds = new ArrayList<>();
        pdfIds = new ArrayList<>();
        filesBeans = new ArrayList<>();
        deviceModels = new ArrayList<>();
        foldersBeans = new ArrayList<>();
        addFilesBeans = new ArrayList<>();
        addFoldersBeans = new ArrayList<>();
        isCheckedMap = new LinkedHashMap<>();
        auraFolderAdapter = new AuraMateFolderAdapter(getActivity(), filesBeans, foldersBeans, false);
        auraFolderAdapter.setOnItemCheckListener(onItemCheckListener);
        auraFolderAdapter.setOnAuraFolderClickListener(onAuraFolderClickListener);

        //加载更多事件
        auraRecyclerView.setHasFixedSize(true);
        auraRecyclerView.setLayoutManager(new GridLayoutManager(getActivity(), 3));
        auraRecyclerView.setAdapter(auraFolderAdapter);
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case AURA_RENAME_DEVICE:
                getAuraDevice();
                break;
            case AURA_MOVE_SUCCESS:
                resetToFresh();
                getDevicesAndFolderList(true);
                break;
            case AURA_DELETE_FILE:
                DeleteFilesEvent deleteFilesEvent = (DeleteFilesEvent) event;
                refreshAfterDeleteSuccess(deleteFilesEvent.getKey());
                break;
            case AURA_CROP_SUCCESS:
                AuraCropSuccessEvent cropSuccessEvent = (AuraCropSuccessEvent) event;
                if (!cropSuccessEvent.isFolder()) {
                    int position = cropSuccessEvent.getPosition();
                    AuraCropModel cropModel = cropSuccessEvent.getCropModel();
                    filesBeans.get(position).setMiddle(cropModel.getOssMiddleKeyUrl());
                    filesBeans.get(position).setSmall(cropModel.getOssSmallKeyUrl());
                    filesBeans.get(position).setBig(cropModel.getOssKeyUrl());
                    filesBeans.get(position).setSingle(cropModel.getOssKeyUrl());
                    filesBeans.get(position).setSingleKey(cropModel.getOssKey());
                    filesBeans.get(position).setBook(cropModel.getOssKeyUrl());
                    filesBeans.get(position).setMiddleSingle(cropModel.getOssMiddleKeyUrl());
                    filesBeans.get(position).setMiddleBook(cropModel.getOssMiddleKeyUrl());
                    filesBeans.get(position).setSmallSingle(cropModel.getOssSmallKeyUrl());
                    filesBeans.get(position).setSmallBook(cropModel.getOssSmallKeyUrl());
                    filesBeans.get(position).setFileSize(Integer.parseInt(cropModel.getFileSize()));
                    filesBeans.get(position).setId(cropModel.getFileId());
                    auraFolderAdapter.refreshData(position, filesBeans);
                }
                break;
            case AURA_SWITCH_FLATTEN_SUCCESS:
                SwitchAuraFlattenEvent switchSuccessEvent = (SwitchAuraFlattenEvent) event;
                if (!switchSuccessEvent.isFolder()) {
                    AuraResultModel flattenImageModel = switchSuccessEvent.getFlattenImageModel();
                    int position = switchSuccessEvent.getPosition();
                    filesBeans.get(position).setMiddle(flattenImageModel.getOssMiddleKeyUrl());
                    filesBeans.get(position).setSmall(flattenImageModel.getOssSmallKeyUrl());
                    filesBeans.get(position).setBig(flattenImageModel.getUrl());
                    filesBeans.get(position).setSingle(flattenImageModel.getUrl());
                    filesBeans.get(position).setMiddleSingle(flattenImageModel.getOssMiddleKeyUrl());
                    filesBeans.get(position).setSmallSingle(flattenImageModel.getOssSmallKeyUrl());
                    filesBeans.get(position).setUserSelectMode(switchSuccessEvent.getUserSelectMode());
                    filesBeans.get(position).setFileSize(flattenImageModel.getFileSize());
                    filesBeans.get(position).setSingleKey(flattenImageModel.getOssKey());
                    auraFolderAdapter.refreshData(position, filesBeans);
                }
                break;
            case AURA_SWITCH_COLOR_FAILED:
            case AURA_SWITCH_COLOR_SUCCESS:
                SwitchAuraMateColorEvent switchAuraMateColorEvent = (SwitchAuraMateColorEvent) event;
                if (!switchAuraMateColorEvent.isFolder()) {
                    AuraMateColorModel auraMateColorModel = switchAuraMateColorEvent.getAuraMateColorModel();
                    int position = switchAuraMateColorEvent.getPosition();
                    filesBeans.get(position).setMiddle(auraMateColorModel.getOssMiddleKeyUrl());
                    filesBeans.get(position).setSmall(auraMateColorModel.getOssSmallKeyUrl());
                    filesBeans.get(position).setBig(auraMateColorModel.getUrl());
                    filesBeans.get(position).setMiddleSingle(auraMateColorModel.getOssMiddleKeyUrl());
                    filesBeans.get(position).setSingle(auraMateColorModel.getUrl());
                    filesBeans.get(position).setSmallSingle(auraMateColorModel.getOssSmallKeyUrl());
                    filesBeans.get(position).setFileSize(auraMateColorModel.getFileSize().intValue());
                    filesBeans.get(position).setSingleKey(auraMateColorModel.getOssKey());
                    auraFolderAdapter.refreshData(position, filesBeans);
                }
                break;
            default:
                break;
        }
    }

    private void registerEvent() {
        auraHomeCurrentItemTv.setOnClickListener(this);
        auraHomeCurrentItemRl.setOnClickListener(this);
        auraHomeCurrentItemImg.setOnClickListener(this);
        auraHomeAddBtn.setOnClickListener(this);
        auraHomeSelectAllBtn.setOnClickListener(this);
        auraHomeCancelBtn.setOnClickListener(this);
        auraHomeMultiSelectBtn.setOnClickListener(this);
        auraHomeRenameRl.setOnClickListener(this);
        auraHomeDeleteRl.setOnClickListener(this);
        auraHomeMoveRl.setOnClickListener(this);
        auraHomePdfRl.setOnClickListener(this);
        wrongQuestionRl.setOnClickListener(this);

    }

    /**
     * @des: 拉取et设备列表
     * @params:[]
     * @return:void
     */
    private void getDevicesAndFolderList(boolean showLoading) {
        if (!NetworkUtils.isConnected()) {
            rlNoNetWork.setVisibility(View.VISIBLE);
            rlNoNetWork.postDelayed(new Runnable() {
                @Override
                public void run() {
                    hideProgressDialog();
                }
            }, 300);
            loadingView.stopLoading();
            return;
        }
        if (showLoading) {
            loadingView.startLoading();
        }
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                try (Realm realm = Realm.getDefaultInstance()) {
                    deviceModels = getDeviceList();
                    if (deviceModels == null) {
                        realm.close();
                        return null;
                    }
                    if (Validator.isNotEmpty(deviceModels)) {
                        Collections.sort(deviceModels, new Comparator<AuraMateDeviceModel>() {
                            @Override
                            public int compare(AuraMateDeviceModel o1, AuraMateDeviceModel o2) {
                                return Double.compare(o1.getCreateOn(), o2.getCreateOn());
                            }
                        });

                        if (!TextUtils.isEmpty(initEquipmentId) && !TextUtils.isEmpty(initRelationId)) {
                            for (int i = 0; i < deviceModels.size(); i++) {
                                if (deviceModels.get(i).getEquipmentUid().equals(initEquipmentId) &&
                                        deviceModels.get(i).getReleationId().equals(initRelationId)) {
                                    currentModel = deviceModels.get(i);
                                    initEquipmentId = null;
                                    initRelationId = null;
                                    break;
                                }
                            }
                        }
                        if (currentModel == null) {
                            currentModel = deviceModels.get(0);
                        }
                        equipmentId = currentModel.getEquipmentUid();
                        ownerId = currentModel.getReleationId() + "";
                        AuraMateNewFileRemind remind = realm.where(AuraMateNewFileRemind.class).equalTo("releationId", currentModel.getReleationId()).findFirst();
                        realm.executeTransaction(new Realm.Transaction() {
                            @Override
                            public void execute(@NotNull Realm realm) {
                                if (remind != null) {
                                    remind.setHaveRead(true);
                                    //刷新红点
                                }
                            }
                        });
                        activity.getRedTip();
                    }
                    AuraHomeFileModel fileModel = getAuraFolders("root");
                    if (fileModel == null) {
                        realm.close();
                        return null;
                    }
                    List<AuraHomeFileModel.FilesBean> addFilesBeans = fileModel.getFiles();
                    List<AuraHomeFileModel.FoldersBean> addFoldersBeans = fileModel.getFolders();
                    if (Validator.isNotEmpty(addFilesBeans)) {
                        filesBeans.addAll(addFilesBeans);
                    }
                    if (Validator.isNotEmpty(addFoldersBeans)) {
                        foldersBeans.addAll(addFoldersBeans);
                    }
                    getSeqNum(addFilesBeans, addFoldersBeans);
                }
                return null;
            }

            @Override
            public void onSuccess(Void result) {
                if (deviceModels == null) {
                    refreshLayout.finishRefresh(false);
                    wrongQuestionRl.setVisibility(View.VISIBLE);
                } else if (!Validator.isNotEmpty(deviceModels)) {
                    wrongQuestionRl.setVisibility(View.GONE);
                    refreshLayout.finishRefresh();
                } else {
                    refreshLayout.finishRefresh();
                    wrongQuestionRl.setVisibility(View.VISIBLE);
                }
                isShowEmptyPrompt();
                refreshDevices();
                refreshFolders();
                if (showLoading) {
                    loadingView.stopLoading();
                }
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                if (!NetworkUtils.isConnected()) {
                    showMessage(R.string.toast_no_connection_network);
                }
                if (showLoading) {
                    loadingView.stopLoading();
                }
                isShowEmptyPrompt();
                checkDeviceSize();
                refreshLayout.finishRefresh(false);
            }
        });
    }

    private void refreshDevices() {
        checkDeviceSize();
        if (deviceModels != null && deviceModels.size() > 0) {
            int position = 0;
            if (currentModel != null) {
                for (int i = 0; i < deviceModels.size(); i++) {
                    if (deviceModels.get(i).getReleationId().equals(currentModel.getReleationId()) &&
                            deviceModels.get(i).getEquipmentUid().equals(currentModel.getEquipmentUid())) {
                        position = i;
                        break;
                    }
                }
            }
            deviceModels.get(position).setSelect(true);
            deviceAdapter.refreshData(deviceModels);
            auraHomeCurrentItemTv.setText(deviceModels.get(position).getDeviceName());
            equipmentId = deviceModels.get(position).getEquipmentUid();
            ownerId = deviceModels.get(position).getReleationId() + "";
        }
    }

    private void checkDeviceSize() {
        if (Validator.isEmpty(deviceModels)) {
            auraHomeCurrentItemImg.setVisibility(View.GONE);
            auraHomeCurrentItemTv.setClickable(false);
            auraHomeCurrentItemTv.setEnabled(false);
            auraHomeCurrentItemRl.setClickable(false);
            auraHomeCurrentItemRl.setEnabled(false);
        } else {
            if (deviceModels.size() > 1) {
                auraHomeCurrentItemImg.setVisibility(View.VISIBLE);
                auraHomeCurrentItemTv.setClickable(true);
                auraHomeCurrentItemTv.setEnabled(true);
                auraHomeCurrentItemRl.setClickable(true);
                auraHomeCurrentItemRl.setEnabled(true);
            } else {
                auraHomeCurrentItemImg.setVisibility(View.GONE);
                auraHomeCurrentItemTv.setClickable(false);
                auraHomeCurrentItemTv.setEnabled(false);
                auraHomeCurrentItemRl.setClickable(false);
                auraHomeCurrentItemRl.setEnabled(false);
            }
        }
    }

    private AuraMateFolderAdapter.OnAuraFolderClickListener onAuraFolderClickListener = new AuraMateFolderAdapter.OnAuraFolderClickListener() {
        @Override
        public void onAuraFolderClick(AuraHomeFileModel.FoldersBean foldersBean, AuraHomeFileModel.FilesBean filesBean, int position, CheckBox checkBox) {
            if (isMultiSelect) {
                checkBox.setChecked(!checkBox.isChecked());
            } else {
                if (foldersBean != null) {
                    Intent intent = new Intent(getActivity(), AuraMateFilesActivity.class);
                    intent.putExtra("equipmentId", equipmentId);
                    intent.putExtra("ownerId", ownerId);
                    intent.putExtra("folderName", foldersBean.getName());
                    intent.putExtra("folderId", foldersBean.getId());
                    ActivityUtils.startActivity(intent);
                } else if (filesBean != null) {
                    Intent intent = new Intent(getActivity(), AuraMatePreviewActivity.class);
                    intent.putExtra("size", filesBean.getFileSize() + "");
                    intent.putExtra("equipmentId", equipmentId);
                    intent.putExtra("ownerId", ownerId);
                    intent.putExtra("mode", filesBean.getUserSelectMode());
                    intent.putExtra("folderId", "root");
                    intent.putExtra("seqNum", filesBean.getSeqNum() + "");
                    Date date = new Date(Long.parseLong(filesBean.getTakeOn()));
                    intent.putExtra("date", formatter.format(date));
                    ActivityUtils.startActivity(intent);
                }

            }
        }
    };
    private AuraMateFolderAdapter.OnItemCheckListener onItemCheckListener = new AuraMateFolderAdapter.OnItemCheckListener() {
        @Override
        public void onItemCheck(int position, LinkedHashMap<String, AuraEntity> isCheckedMap, int totalSize, String folderName) {
            AuraMateFilesFragment.this.isCheckedMap = isCheckedMap;
            //如果选中一个 文案变为已选中1个
            if (isCheckedMap.size() == 1) {
                AuraMateFilesFragment.this.folderName = folderName;
            }
            checkSize(isCheckedMap, totalSize);
        }
    };

    /**
     * @des: 重置准备刷新
     * @params:
     * @return:
     */

    private void resetToFresh() {
        refreshLayout.closeHeaderOrFooter();
        refreshLayout.resetNoMoreData();
        filesBeans = new ArrayList<>();
        foldersBeans = new ArrayList<>();
        folderIds = new ArrayList<>();
        fileIds = new ArrayList<>();
        pdfIds = new ArrayList<>();
        isCheckedMap.clear();
        isCheckedMap = new LinkedHashMap<>();
    }


    private void getAuraDevice() {
        httpManager.request().getAuraDevices(userPreferences.getUserId(), new TypeToken<List<AuraDeviceModel>>() {
        }.getType(), new MiaoHttpManager.Callback<AuraDeviceModel>() {

            @Override
            public void onStart() {
            }

            @Override
            public void onResponse(MiaoHttpEntity<AuraDeviceModel> entity) {
                hideProgressDialog();
                refreshDevices();
            }

            @Override
            public void onFailure(MiaoHttpEntity<AuraDeviceModel> entity) {
                hideProgressDialog();
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
            }
        });
    }

    @Override
    public void onClick(View v) {
        final int id = v.getId();
        switch (id) {
            case R.id.aura_home_current_item_tv:
            case R.id.aura_home_current_item_img:
            case R.id.aura_home_current_item_rl:
                auraHomePopup.show();
                break;
            case R.id.aura_home_cancel_btn:
                cancelEvent();
                break;
            case R.id.aura_home_select_all_btn:
                selectAll();
                break;
            case R.id.aura_home_multi_select_btn:
                multiSelect();
                break;
            case R.id.aura_home_move_rl:
                Intent intent = new Intent(getActivity(), AuraMateMoveActivity.class);
                String files = EtUtils.transFiles(fileIds);
                intent.putExtra("equipmentId", equipmentId);
                intent.putExtra("ownerId", ownerId);
                intent.putExtra("isRoot", true);
                intent.putExtra("files", files);
                ActivityUtils.startActivity(intent);
                resetCheckList();
                refreshFolders();
                break;
            case R.id.aura_home_rename_rl:
                createOrRenameFolder(1);
                break;
            case R.id.aura_home_add_btn:
                createOrRenameFolder(0);
                break;
            case R.id.aura_home_pdf_rl:
                createOrRenameFolder(2);
                break;
            case R.id.aura_home_delete_rl:
                showConfirmDeleteDialog();
                break;
            case R.id.wrong_question_rl:
                Intent intent1 = new Intent(getActivity(), AuraMateWrongQuestionActivity.class);
                intent1.putExtra("isNormal", true);
                intent1.putExtra("equipmentId", equipmentId);
                intent1.putExtra("ownerId", ownerId);
                ActivityUtils.startActivity(intent1);
                break;
            default:
                break;
        }
    }

    private void showConfirmDeleteDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(getActivity(), CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        if (isAdded()) {
            builder.setTitle(getResources().getString(R.string.prompt));
            if (folderIds.size() > 0) {
                builder.setMessage(getResources().getString(R.string.confirm_delete_folder));
            } else {
                builder.setMessage(getResources().getString(R.string.confirm_delete));
            }
        }
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                delete();

            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();
    }

    /**
     * @des: 删除文件或者图片
     * @params:
     * @return:
     */

    private void delete() {
        httpManager.request().deleteAuraFolder(userPreferences.getUserId(), EtUtils.transFiles(fileIds), EtUtils.transFiles(folderIds), ownerId, String.class, new MiaoHttpManager.CallbackNetwork<String>() {
            @Override
            public void onNoNetwork() {
                showMessage(R.string.toast_no_connection_network);
            }

            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                hideSelectTopBar();
//                refreshAfterDeleteSuccess(EtUtils.transFiles(fileIds));
                resetToFresh();
                getDevicesAndFolderList(false);
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                resetCheckList();
                refreshFolders();
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
                resetCheckList();
                refreshFolders();
            }
        });
    }

    private void refreshAfterDeleteSuccess(String key) {
        Iterator<AuraHomeFileModel.FilesBean> it = filesBeans.iterator();
        while (it.hasNext()) {
            if (key.contains(it.next().getId())) {
                it.remove();
            }
        }
        cancelEvent();
        isShowEmptyPrompt();
    }

    /**
     * @des: 创建或者重命名文件夹
     * @params:
     * @return:
     */

    private void createOrRenameFolder(final int type) {
        final CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(getActivity(), CloudCommonPopupConstants.EDT_TWO_BUTTON);
        if (isAdded()) {
            builder.setTitle(getResources().getString(R.string.prompt));
            if (type == 2) {
                builder.setMessage(getResources().getString(R.string.input_pdf_name));
            } else {
                builder.setMessage(getResources().getString(R.string.input_folder_name));
            }
        }
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (Validator.isNotEmpty(dialogEdt.getText().toString())) {
                    //不能含有表情
                    if (EtUtils.containsEmoji(dialogEdt.getText().toString())) {
                        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(getActivity(), CloudCommonPopupConstants.COMMON_ONE_BUTTON);
                        if (isAdded()) {
                            builder.setTitle(getResources().getString(R.string.prompt));
                            builder.setMessage(getResources().getString(R.string.nickname_toast_symbol));
                        }
                        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                dialog.dismiss();
                            }
                        });
                        CloudCommonPopup commonPopup = builder.create();
                        commonPopup.show();
                    } else {
                        chooseType(type);
                        dialog.dismiss();
                    }
                } else {
                    showMessage(R.string.tip_file_rename_length_toast);
                }
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        dialogEdt = (EditText) commonPopup.getWindow().findViewById(R.id.edt);
        commonPopup.show();
    }

    private void resetCheckList() {
        folderIds = new ArrayList<>();
        fileIds = new ArrayList<>();
        pdfIds = new ArrayList<>();
        isCheckedMap.clear();
        isCheckedMap = new LinkedHashMap<>();
    }

    /**
     * @des:根据类型选择动作
     * @params:
     * @return:
     */
    private void chooseType(int type) {
        switch (type) {
            case 0:
                createFolder();
                break;
            case 1:
                renameFolder(dialogEdt.getText().toString());
                break;
            case 2:
                if (isCheckedMap.size() > 100) {
                    showMessage(R.string.pdf_100_files_tip);
                } else {
                    generatePdf();
                }
                break;
            default:
                break;
        }
    }

    /**
     * @des: 生成Pdf
     * @params:
     * @return:
     */

    private void generatePdf() {
        final ProgressPopup.Builder builder = new ProgressPopup.Builder(getActivity());
        if (isAdded()) {
            builder.setTitle(getResources().getString(R.string.pdf_ready_text));
        }
        builder.setProgress(0);
        progressPopup = builder.create();
        progressBar = (RoundedRectProgressBar) progressPopup.getWindow().findViewById(R.id.progress);
        pdfDialogTitle = (TextView) progressPopup.getWindow().findViewById(R.id.title);
        progressPopup.show();
        requestServerProgress();
    }

    /**
     * @des: 请求服务器生成pdf百分比
     * @params:
     * @return:
     */

    @SuppressWarnings("AlibabaAvoidManuallyCreateThread")
    private void requestServerProgress() {
        isPdfRun = true;
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    MiaoHttpEntity<PdfModel> generatePdfEntity = HttpManager.getInstance().request().auraPdf(userPreferences.getUserId(), EtUtils.transFiles(pdfIds), dialogEdt.getText().toString(), ownerId, PdfModel.class);
                    if (generatePdfEntity == null) {
                        generatePdfFailed();
                    } else if (generatePdfEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                        while (isPdfRun) {
                            MiaoHttpEntity<PdfModel> resultEntity = HttpManager.getInstance().request().auraPdfResult(userPreferences.getUserId(), generatePdfEntity.getBody().getId(), generatePdfEntity.getBody().getRandomKey(), ownerId, PdfModel.class);
                            if (resultEntity.getCode() == MiaoHttpManager.STATUS_PDF_RETURN_CODE) {
                                Thread.sleep(1000);
                            } else if (resultEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                                final PdfModel pdfModel = resultEntity.getBody();
                                if (pdfModel.getPercent() != null) {
                                    getActivity().runOnUiThread(new Runnable() {
                                        @Override
                                        public void run() {
                                            pdfDialogTitle.setText(getString(R.string.pdf_server_generating) + String.format("%.0f", Double.parseDouble(pdfModel.getPercent())) + "%");
                                            progressBar.setProgress(EtUtils.stringToInt(pdfModel.getPercent()));
                                        }
                                    });
                                } else {
                                    generatePdfSuccess();
                                }
                            } else {
                                generatePdfFailed();
                            }
                        }
                    } else {
                        generatePdfFailed();
                    }
                } catch (Exception e) {
                    logE(e.toString());
                    generatePdfFailed();
                }
            }
        }).start();

    }


    private void generatePdfFailed() {
        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                isPdfRun = false;
                progressPopup.dismiss();
                showMessage(R.string.request_server_error);
                resetCheckList();
                refreshFolders();
            }
        });

    }

    private void generatePdfSuccess() {
        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                isPdfRun = false;
                progressPopup.dismiss();
                showMessage(R.string.pdf_server_generating_success);
                resetCheckList();
                refreshFolders();
                ActivityUtils.startActivity(AuraMatePdfActivity.class);
            }
        });
    }

    /**
     * @des: 创建文件夹
     * @params:
     * @return:
     */

    private void createFolder() {
        httpManager.request().createAuraFolder(userPreferences.getUserId(), equipmentId, dialogEdt.getText().toString(), ownerId, String.class, new MiaoHttpManager.Callback<String>() {
            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                resetToFresh();
                getDevicesAndFolderList(true);
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                if (entity.getCode() == MiaoHttpManager.STATUS_NAME_IS_SAMED) {
                    showMessage(R.string.had_same_name_folder);
                }
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
            }
        });
    }

    private void renameFolder(String namedString) {
        String fileId = "";
        for (String s : isCheckedMap.keySet()) {
            fileId = s;
        }
        if (namedString.equals(folderName)) {
            showMessage(R.string.tip_file_rename_toast);
        } else {
            httpManager.request().changeAuraFolderName(userPreferences.getUserId(), fileId, namedString, ownerId, String.class, new MiaoHttpManager.Callback<String>() {
                @Override
                public void onStart() {
                    showProgressDialog();
                }

                @Override
                public void onResponse(MiaoHttpEntity<String> entity) {
                    hideProgressDialog();
                    showMessage(R.string.tip_file_rename_success);
                    resetToFresh();
                    getDevicesAndFolderList(true);
                }

                @Override
                public void onFailure(MiaoHttpEntity<String> entity) {
                    hideProgressDialog();
                    resetCheckList();
                    refreshFolders();
                    if (entity.getCode() == MiaoHttpManager.STATUS_NAME_IS_SAMED) {
                        showMessage(R.string.rename_empty);
                    }
                }

                @Override
                public void onError(Exception e) {
                    hideProgressDialog();
                    resetCheckList();
                    refreshFolders();
                }
            });
        }

    }


    /**
     * @des: 是否显示空文件夹提示区域
     * @params:
     * @return:
     */

    private void isShowEmptyPrompt() {
        if (Validator.isNotEmpty(filesBeans) || Validator.isNotEmpty(foldersBeans)) {
            auraRecyclerView.setVisibility(View.VISIBLE);
            auraMateEmptyRl.setVisibility(View.GONE);
        } else {
            auraRecyclerView.setVisibility(View.GONE);
            auraMateEmptyRl.setVisibility(View.VISIBLE);

        }
    }

    /**
     * @des: 显示选择TopBar
     * @params:
     * @return:
     */

    private void showSelectTopBar() {
        auraHomeBottomLl.setVisibility(View.VISIBLE);
        saveMovieRl.setVisibility(View.GONE);
        auraHomeMoveRl.setVisibility(View.VISIBLE);
        auraHomePdfRl.setVisibility(View.VISIBLE);
        auraHomeRenameRl.setVisibility(View.VISIBLE);
        auraHomeDeleteRl.setVisibility(View.VISIBLE);
        darkAll();
        auraHomeUnselectedTopBarRl.setVisibility(View.GONE);
        auraHomeCancelBtn.setVisibility(View.VISIBLE);
        auraHomeSelectAllBtn.setVisibility(View.VISIBLE);
        auraHomeCancelBtn.setText(R.string.cancel);
        auraFilesTitleTv.setVisibility(View.VISIBLE);
        auraFilesTitleTv.setText(String.format(getString(R.string.select_num_et), isCheckedMap.size() + ""));
        auraHomeCurrentItemTv.setVisibility(View.GONE);
        auraHomeCurrentItemImg.setVisibility(View.GONE);
        auraHomeSelectAllBtn.setText(R.string.select_all);
    }

    /**
     * @des: 隐藏选择TopBar
     * @params:
     * @return:
     */

    private void hideSelectTopBar() {
        auraHomeBottomLl.setVisibility(View.GONE);
        auraHomeUnselectedTopBarRl.setVisibility(View.VISIBLE);
        auraHomeCancelBtn.setVisibility(View.GONE);
        auraHomeSelectAllBtn.setVisibility(View.GONE);
        auraFilesTitleTv.setVisibility(View.GONE);
        auraHomeCurrentItemTv.setVisibility(View.VISIBLE);
        checkDeviceSize();
    }


    private boolean isMultiSelect = false;
    private boolean isSelectAll = false;

    /**
     * @des: 多选
     * @params:
     * @return:
     */

    private void multiSelect() {
        if (Validator.isNotEmpty(filesBeans) || Validator.isNotEmpty(foldersBeans)) {
            isMultiSelect = !isMultiSelect;
            auraFolderAdapter.refreshData(isMultiSelect);
            if (isMultiSelect) {
                showSelectTopBar();
            } else {
                hideSelectTopBar();
            }
        }
    }

    /**
     * @des: 选中所有
     * @params:
     * @return:
     */

    private void selectAll() {
        if (!isSelectAll) {
            for (int i = 0; i < foldersBeans.size(); i++) {
                if (!isCheckedMap.containsKey((foldersBeans.get(i).getId()))) {
                    AuraEntity auraEntity = new AuraEntity();
                    auraEntity.setType(0);
                    isCheckedMap.put(foldersBeans.get(i).getId(), auraEntity);
                }
            }
            for (int i = 0; i < filesBeans.size(); i++) {
                if (!isCheckedMap.containsKey((filesBeans.get(i).getId()))) {
                    AuraEntity auraEntity = new AuraEntity();
                    auraEntity.setType(1);
                    auraEntity.setOsskey(filesBeans.get(i).getSingleKey());
                    isCheckedMap.put(filesBeans.get(i).getId(), auraEntity);
                }
            }
            auraHomeSelectAllBtn.setText(R.string.not_select_all);
            isSelectAll = true;
        } else {
            isCheckedMap.clear();
            isCheckedMap = new LinkedHashMap<>();
            auraHomeSelectAllBtn.setText(R.string.select_all);
            isSelectAll = false;
        }
        auraFilesTitleTv.setText(String.format(getString(R.string.select_num_et), isCheckedMap.size() + ""));
        auraFolderAdapter.refreshData(filesBeans, foldersBeans, true, isCheckedMap);
    }

    /**
     * @des: 取消事件
     * @params:
     * @return:
     */

    private void cancelEvent() {
        folderIds = new ArrayList<>();
        fileIds = new ArrayList<>();
        pdfIds = new ArrayList<>();
        isMultiSelect = false;
        isSelectAll = false;
        isCheckedMap.clear();
        isCheckedMap = new LinkedHashMap<>();
        auraFolderAdapter.refreshData(filesBeans, foldersBeans, false, isCheckedMap);
        hideSelectTopBar();
    }

    /**
     * @des: 重置选中状态并且刷新
     * @params:
     * @return:
     */
    private void refreshFolders() {
        isMultiSelect = false;
        isSelectAll = false;
        hideSelectTopBar();
        auraFolderAdapter.refreshData(filesBeans, foldersBeans, isMultiSelect, isCheckedMap);
    }

    /**
     * @des: 下拉加载
     * params:
     * @return:
     */

    private void loadMore() {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                AuraHomeFileModel auraHomeFile = getAuraFolders(seqNum);
                if (auraHomeFile == null) {
                    return null;
                }
                addFilesBeans = auraHomeFile.getFiles();
                addFoldersBeans = auraHomeFile.getFolders();
                if (Validator.isNotEmpty(addFilesBeans)) {
                    filesBeans.addAll(addFilesBeans);
                }
                if (Validator.isNotEmpty(addFoldersBeans)) {
                    foldersBeans.addAll(addFoldersBeans);
                }
                getSeqNum(addFilesBeans, addFoldersBeans);
                return null;
            }

            @Override
            public void onSuccess(Void result) {
                checkSize(isCheckedMap, auraFolderAdapter.getTotalSize());
                if (addFilesBeans == null && addFoldersBeans == null) {
                    refreshLayout.finishLoadMore(false);
                } else if (Validator.isEmpty(addFilesBeans) && Validator.isEmpty(addFoldersBeans)) {
                    refreshLayout.finishLoadMoreWithNoMoreData();
                } else {
                    auraFolderAdapter.setLoadMoreData(filesBeans, foldersBeans);
                    refreshLayout.finishLoadMore(true);
                }
            }
        });

    }


    /**
     * @des: 检查选中个数
     * @params:
     * @return:
     */

    private void checkSize(LinkedHashMap<String, AuraEntity> isCheckedMap, int totalSize) {
        judgeToShowBottom(isCheckedMap);
        if (isCheckedMap.size() == 1) {
            auraFilesTitleTv.setText(R.string.select_one_et);
        } else if (isCheckedMap.size() > 1) {
            auraFilesTitleTv.setText(String.format(getString(R.string.select_num_et), isCheckedMap.size() + ""));
        } else {
            if (isMultiSelect) {
                auraFilesTitleTv.setText(String.format(getString(R.string.select_num_et), isCheckedMap.size() + ""));
            }
        }
        checkSelectAll(isCheckedMap);
    }

    private void checkSelectAll(LinkedHashMap<String, AuraEntity> isCheckedMap) {
        //如果选择不是全部Item  text变为取消全选
        if (isCheckedMap.size() < auraFolderAdapter.getTotalSize()) {
            auraHomeSelectAllBtn.setText(R.string.select_all);
            isSelectAll = false;
        } else {
            auraHomeSelectAllBtn.setText(R.string.not_select_all);
            isSelectAll = true;
        }
    }

    private void judgeToShowBottom(LinkedHashMap<String, AuraEntity> isCheckedMap) {
        int folderSize = 0;
        int fileSize = 0;
        folderIds = new ArrayList<>();
        pdfIds = new ArrayList<>();
        fileIds = new ArrayList<>();

        for (Map.Entry<String, AuraEntity> stringEtEntityEntry : isCheckedMap.entrySet()) {
            if (stringEtEntityEntry.getValue().getType() == 0) {
                folderSize++;
                folderIds.add(stringEtEntityEntry.getKey());
            } else {
                fileSize++;
                pdfIds.add(stringEtEntityEntry.getValue().getOsskey());
                fileIds.add(stringEtEntityEntry.getKey());
            }
        }
        auraHomeDeleteRl.setVisibility(View.VISIBLE);
        if (folderSize == 0 && fileSize != 0) {
            darkRename();
            showMove();
            showPdf();
            showDelete();

        } else if (folderSize == 1 && fileSize == 0) {
            showRename();
            darkMove();
            darkPdf();
            showDelete();

        } else if (folderSize == 0 && fileSize == 0) {
            darkAll();

        } else {
            darkRename();
            darkMove();
            darkPdf();
            showDelete();
        }
    }

    /**
     * @des: 获取文件夹
     * @params:
     * @return:
     */

    private AuraHomeFileModel getAuraFolders(String seqNum) {
        if (seqNum == null) {
            seqNum = "root";
            type = "0";
        }
        try {
            final MiaoHttpEntity<AuraHomeFileModel> auraFileEntity = httpManager.request().getAuraHomeFileSync(equipmentId, "root", seqNum, 51 + "", type + "",
                    userPreferences.getUserId(), ownerId, AuraHomeFileModel.class);
            if (auraFileEntity != null && auraFileEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return auraFileEntity.getBody();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private List<AuraMateDeviceModel> getDeviceList() {
        try {
            final MiaoHttpEntity<AuraMateDeviceModel> auraShareDeviceEntity = httpManager.request().getAuraShareDeviceSync(
                    userPreferences.getUserId(), new TypeToken<List<AuraMateDeviceModel>>() {
                    }.getType());
            if (auraShareDeviceEntity == null) {
                return null;
            }
            if (auraShareDeviceEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return auraShareDeviceEntity.getBodyList();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * @des: 获得下拉加载ID
     * @params:
     * @return:
     */
    private void getSeqNum(List<AuraHomeFileModel.FilesBean> addFilesBeans, List<AuraHomeFileModel.FoldersBean> addFoldersBeans) {
        if (addFilesBeans == null) {
            seqNum = addFoldersBeans.get(addFoldersBeans.size() - 1).getSeqId() + "";
            type = 0 + "";
        } else if (addFoldersBeans == null) {
            seqNum = addFilesBeans.get(addFilesBeans.size() - 1).getSeqNum() + "";
            type = 1 + "";
        } else {
            if (addFilesBeans.size() > 0) {
                seqNum = addFilesBeans.get(addFilesBeans.size() - 1).getSeqNum() + "";
                type = 1 + "";
            } else if (addFoldersBeans.size() > 0) {
                seqNum = addFoldersBeans.get(addFoldersBeans.size() - 1).getSeqId() + "";
                type = 0 + "";
            }
        }
    }

    private AuraMatePopupAdapter.onItemClickListener onItemClickListener = new AuraMatePopupAdapter.onItemClickListener() {
        @Override
        public void onItemClick(int position, AuraMateDeviceModel auraMateDeviceModel) {
            seqNum = "root";
            type = "0";
            auraHomeCurrentItemTv.setText(auraMateDeviceModel.getDeviceName());
            currentModel = auraMateDeviceModel;
            auraHomePopup.dismiss();
            filesBeans = new ArrayList<>();
            foldersBeans = new ArrayList<>();
            getDevicesAndFolderList(true);

        }
    };

    private void showAll() {
        showDelete();
        showPdf();
        showRename();
        showMove();
    }

    private void darkAll() {
        darkDelete();
        darkPdf();
        darkRename();
        darkMove();
    }

    private void showRename() {
        auraHomeRenameRl.setClickable(true);
        auraHomeRenameRl.setEnabled(true);
        auraHomeRenameImg.setSelected(true);
        if (isAdded()) {
            auraHomeRenameTv.setTextColor(getResources().getColor(R.color.white));
        }
    }

    private void darkRename() {
        auraHomeRenameRl.setClickable(false);
        auraHomeRenameRl.setEnabled(false);
        auraHomeRenameImg.setSelected(false);
        if (isAdded()) {
            auraHomeRenameTv.setTextColor(getResources().getColor(R.color.dark_text));
        }
    }

    private void showDelete() {
        auraHomeDeleteRl.setClickable(true);
        auraHomeDeleteRl.setEnabled(true);
        auraHomeDeleteImg.setSelected(true);
        if (isAdded()) {
            auraHomeDeleteTv.setTextColor(getResources().getColor(R.color.white));
        }
    }

    private void darkDelete() {
        auraHomeDeleteRl.setClickable(false);
        auraHomeDeleteRl.setEnabled(false);
        auraHomeDeleteImg.setSelected(false);
        if (isAdded()) {
            auraHomeDeleteTv.setTextColor(getResources().getColor(R.color.dark_text));
        }
    }

    private void showMove() {
        auraHomeMoveRl.setClickable(true);
        auraHomeMoveRl.setEnabled(true);
        auraHomeMoveImg.setSelected(true);
        if (isAdded()) {
            auraHomeMoveTv.setTextColor(getResources().getColor(R.color.white));
        }
    }

    private void darkMove() {
        auraHomeMoveRl.setClickable(false);
        auraHomeMoveRl.setEnabled(false);
        auraHomeMoveImg.setSelected(false);
        if (isAdded()) {
            auraHomeMoveTv.setTextColor(getResources().getColor(R.color.dark_text));
        }
    }


    private void showPdf() {
        auraHomePdfRl.setClickable(true);
        auraHomePdfRl.setEnabled(true);
        auraHomePdfImg.setSelected(true);
        if (isAdded()) {
            auraHomePdfTv.setTextColor(getResources().getColor(R.color.white));
        }
    }

    private void darkPdf() {
        auraHomePdfRl.setClickable(false);
        auraHomePdfRl.setEnabled(false);
        auraHomePdfImg.setSelected(false);
        if (isAdded()) {
            auraHomePdfTv.setTextColor(getResources().getColor(R.color.dark_text));
        }
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if ((getArguments() != null ? getArguments().getString("device") : null) != null) {
            initEquipmentId = getArguments().getString("device");
        }
        if ((getArguments() != null ? getArguments().getString("relationId") : null) != null) {
            initRelationId = getArguments().getString("relationId");
        }
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        registerEvent();
        initNetListener();
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        if (realm != null) {
            realm.close();
        }
    }

    @Override
    public void onResume() {
        super.onResume();

    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (!hidden) {
            if (activity != null) {
                auraHomeMoveRl = (RelativeLayout) activity.findViewById(R.id.aura_home_move_rl);
                auraHomeMoveRl.setOnClickListener(this);
                auraHomePdfRl = (RelativeLayout) activity.findViewById(R.id.aura_home_pdf_rl);
                auraHomePdfRl.setOnClickListener(this);
                auraHomeRenameRl = (RelativeLayout) activity.findViewById(R.id.aura_home_rename_rl);
                auraHomeRenameRl.setOnClickListener(this);
                auraHomeDeleteRl = (RelativeLayout) activity.findViewById(R.id.aura_home_delete_rl);
                auraHomeDeleteRl.setOnClickListener(this);

            }
        }
    }
}








