package com.czur.cloud.ui.starry.meeting.baselib.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.recyclerview.widget.RecyclerView
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexboxLayoutManager
import android.graphics.Color
import com.czur.cloud.R
import com.czur.cloud.ui.starry.meeting.baselib.utils.IndexUtils.INDEX_WORDS
import com.czur.cloud.ui.starry.meeting.baselib.utils.doOnItemClick
import com.czur.cloud.ui.starry.meeting.baselib.utils.dp2px
import com.noober.background.drawable.DrawableCreator
import kotlinx.android.synthetic.main.meeting_baselib_item_word.view.*
import kotlinx.android.synthetic.main.meeting_baselib_widget_wordindex.view.*

private const val DISABLE_ALPHA = 0.2f

class WordIndexView : FrameLayout {
    var colorText = Color.WHITE
    var colorBg = Color.parseColor("#33ffffff")
    private var chooseListener: ((chooseChar: Char) -> Unit)? = null

    val adapter = WordAdapter()

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        val ta = context.obtainStyledAttributes(attrs, R.styleable.BaseLib_WordItem)
        colorText = ta.getColor(R.styleable.BaseLib_WordItem_baselib_tv_color, colorText)
        colorBg = ta.getColor(R.styleable.BaseLib_WordItem_baselib_bg_color, colorBg)
        ta.recycle()
        init(context)
    }

    private fun init(context: Context) {
        LayoutInflater.from(context).inflate(R.layout.meeting_baselib_widget_wordindex, this, true)
        initView()
    }


    fun setWords(words: Collection<Char>) {
        adapter.words = words
    }

    fun doOnChoose(listener:(Char) -> Unit){
        this.chooseListener = listener
    }

    private fun initView() {
        rv_words.layoutManager = FlexboxLayoutManager(this.context, FlexDirection.COLUMN)
        adapter.setItemStyle(colorText, colorBg)
        rv_words.adapter = adapter

        rv_words.doOnItemClick { vh, view ->
            val pos = vh.adapterPosition
            val c = INDEX_WORDS[pos]
            chooseListener?.let {
                if (adapter.isEnablePosition(pos)) {
                    it(c)
                }
            }
            true
        }
    }

    class WordAdapter
        : RecyclerView.Adapter<WordAdapter.ViewHolder>() {
        var words: Collection<Char> = emptyList()
            set(value) {
                field = value
                notifyDataSetChanged()
            }

        var colorTv = Color.WHITE
        var colorBg = Color.parseColor("#33ffffff")

        fun setItemStyle(colorTv: Int, colorBg: Int) {
            this.colorBg = colorBg
            this.colorTv = colorTv
            notifyDataSetChanged()
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val itemView = LayoutInflater.from(parent.context)
                .inflate(R.layout.meeting_baselib_item_word, parent, false)
            itemView.tvWords.setTextColor(colorTv)
            itemView.background = DrawableCreator.Builder()
                .setCornersRadius(dp2px(itemView.context, 15f).toFloat())
                .setSolidColor(colorBg)
                .build()
            return ViewHolder(itemView)
        }

        override fun getItemCount(): Int {
            return INDEX_WORDS.size
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            holder.tvWords.text = INDEX_WORDS[position].toString()

            if (isEnablePosition(position)) {
                holder.itemView.alpha = 1.0f
            } else {
                holder.itemView.alpha = DISABLE_ALPHA
            }
        }

        fun isEnablePosition(position: Int) = words.contains(INDEX_WORDS[position])

        class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            var tvWords = itemView.tvWords
        }
    }
}