package com.czur.cloud.util

object StringToolsUtils {

    // 判断字符串中必须包含字母或者数字
    @JvmStatic
    fun isLetterDigit(str: String): Boolean {
        var isDigit = false //定义一个boolean值，用来表示是否包含数字
        var isLetter = false //定义一个boolean值，用来表示是否包含字母
        for (i in str.indices) {
            if (Character.isDigit(str[i])) {   //用char包装类中的判断数字的方法判断每一个字符
                isDigit = true
            }
            if (Character.isLetter(str[i])) {  //用char包装类中的判断字母的方法判断每一个字符
                isLetter = true
            }
        }
        val regex = Regex("^[a-zA-Z0-9]+$")
        return isDigit && isLetter && str.matches(regex)
    }


}