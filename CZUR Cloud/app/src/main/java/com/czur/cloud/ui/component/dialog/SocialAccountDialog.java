package com.czur.cloud.ui.component.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.LinearLayout;

import com.czur.cloud.R;

public class SocialAccountDialog extends Dialog implements View.OnClickListener {
    private Context mContext;
    private LinearLayout weixin;
    private LinearLayout qq;
    private LinearLayout weibo;

    public SocialAccountDialog(Context context, SocialAccountDialogOnClickListener socialAccountDialogOnClickListener) {
        //重点实现R.style.DialogStyle 动画效果
        this(context, R.style.SocialAccountDialogStyle);
        this.socialAccountDialogOnClickListener = socialAccountDialogOnClickListener;
        mContext = context;
    }

    public SocialAccountDialog(Context context, int themeResId) {
        super(context, themeResId);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_login_bottom_sheet);
        WindowManager.LayoutParams params = getWindow().getAttributes();
        weixin = (LinearLayout) getWindow().findViewById(R.id.weixin_account);
        qq = (LinearLayout) getWindow().findViewById(R.id.qq_account);
        weibo = (LinearLayout) getWindow().findViewById(R.id.weibo_account);

        //设置显示的位置
        params.gravity = Gravity.BOTTOM;
        //设置dialog的宽度
        params.width = WindowManager.LayoutParams.MATCH_PARENT;
        params.height = WindowManager.LayoutParams.WRAP_CONTENT;
        getWindow().setAttributes(params);

        weixin.setOnClickListener(this);
        qq.setOnClickListener(this);
        weibo.setOnClickListener(this);


    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.weixin_account:
                if (socialAccountDialogOnClickListener!=null) {
                socialAccountDialogOnClickListener.onAccountClick(R.id.weixin_account);
                }
                break;
            case R.id.qq_account:
                if (socialAccountDialogOnClickListener!=null) {
                    socialAccountDialogOnClickListener.onAccountClick(R.id.qq_account);
                }
                break;
            case R.id.weibo_account:
                if (socialAccountDialogOnClickListener!=null) {
                    socialAccountDialogOnClickListener.onAccountClick(R.id.weibo_account);
                }
                break;
            default:
                break;
        }
    }


    /**

       点击事件接口

     **/
    public interface SocialAccountDialogOnClickListener {
        /**
         *
         * @param viewId
         */
        void onAccountClick(int viewId);
    }
    private SocialAccountDialogOnClickListener socialAccountDialogOnClickListener;

    private void setSocialAccountDialogOnClickListener(SocialAccountDialogOnClickListener socialAccountDialogOnClickListener) {
        this.socialAccountDialogOnClickListener = socialAccountDialogOnClickListener;

    }

}