package com.czur.cloud.ui.mirror.adapter;

/*
 * Created by <PERSON> on 2020/12/25.
 */

import android.animation.ObjectAnimator;
import android.animation.PropertyValuesHolder;
import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.mirror.SittingDeviceNameActivity;
import com.czur.cloud.ui.mirror.SittingHappyReportActivity;
import com.czur.cloud.ui.mirror.SittingHomeShareActivity;
import com.czur.cloud.ui.mirror.SittingLightActivity;
import com.czur.cloud.ui.mirror.SittingLongReportActivity;
import com.czur.cloud.ui.mirror.SittingLongSitActivity;
import com.czur.cloud.ui.mirror.SittingModelActivity;
import com.czur.cloud.ui.mirror.SittingReportActivity;
import com.czur.cloud.ui.mirror.SittingVolumeActivity;
import com.czur.cloud.ui.mirror.comm.FastBleConstants;
import com.czur.cloud.ui.mirror.comm.FastBleToolUtils;
import com.czur.cloud.ui.mirror.component.RoundProgress;
import com.czur.cloud.ui.mirror.model.SittingDeviceModel;
import com.czur.cloud.ui.mirror.mydialog.SittingSensitivityDialog;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.header.ClassicsHeader;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import io.realm.Realm;

public class SittingEquipmentAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    public final static int MIN_CLICK_TIME = 1000;
    private long lastTime = 0;

    private static final int TYPE_NORMAL = 0;
    private static final int TYPE_ADD_EQUIPMENT = 1;
    private List<SittingDeviceModel> datas;
    private Context context;
    private Realm realm;

    public SittingEquipmentAdapter(BaseActivity activity, List<SittingDeviceModel> datas, Realm realm) {
        this.realm = realm;
        this.context = activity;
        if (datas == null) {
            this.datas = new ArrayList<>();
        } else {
            this.datas = datas;
        }
    }

    public void refreshData(List<SittingDeviceModel> datas) {
        if (datas != null) {
            this.datas = datas;
            notifyDataSetChanged();
        }
    }

    @NotNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NotNull ViewGroup parent, int viewType) {
        if (viewType == TYPE_NORMAL) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.activity_sitting_device_item, parent, false);
            return new NormalViewHolder(view);
        } else {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_sitting_empty, parent, false);
            return new AddEquipmentViewHolder(view);
        }
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, @SuppressLint("RecyclerView") final int position) {
        if (holder instanceof NormalViewHolder) {
            final NormalViewHolder mHolder = (NormalViewHolder) holder;
            mHolder.mItem = datas.get(position);

            mHolder.scroll.setSmoothScrollingEnabled(true);
            mHolder.loadingImg.clearAnimation();
            PropertyValuesHolder valuesHolder = PropertyValuesHolder.ofFloat("rotation", 0, 360);
            ObjectAnimator objectAnimator = ObjectAnimator.ofPropertyValuesHolder(mHolder.loadingImg, valuesHolder);
            objectAnimator.setDuration(2000);
            objectAnimator.setInterpolator(new LinearInterpolator());
            objectAnimator.setRepeatCount(ValueAnimator.INFINITE);
            objectAnimator.start();

            mHolder.refreshHeader.setFinishDuration(2000);//设置刷新完成显示的停留时间（设为0可以关闭停留功能）
            //回弹动画时长（毫秒）
            mHolder.refreshLayout.setReboundDuration(FastBleConstants.RUN_DELAY_TIMES1000*2);
            //显示下拉高度/手指真实下拉高度=阻尼效果
            mHolder.refreshLayout.setDragRate(0.5f);

            // 下拉刷新，同步数据？？？
            mHolder.refreshLayout.setOnRefreshListener(new com.scwang.smartrefresh.layout.listener.OnRefreshListener() {
                @Override
                public void onRefresh(@NonNull @NotNull RefreshLayout refreshLayout) {

                    if (onRefreshListener != null) {
                        onRefreshListener.onRefreshListener(refreshLayout);
                    }
                }
            });

            // 设备状态
            if (mHolder.mItem.isOnline()) {
                mHolder.sittingHomeStateTv.setText(context.getString(R.string.sitting_main_status_OK));
                mHolder.sittingHomeStateTv.setTextColor(context.getColor(R.color.white));
                mHolder.sittingHomeStateImg.setImageResource(R.drawable.jing_main_dot_with_green);
            }else{
                mHolder.sittingHomeStateTv.setText(context.getString(R.string.sitting_main_status_NO));
                mHolder.sittingHomeStateTv.setTextColor(context.getColor(R.color.jing_main_dot_color_gray));
                mHolder.sittingHomeStateImg.setImageResource(R.drawable.jing_main_dot_with_gray);
            }

            if (!NetworkUtils.isConnected()) {
                float alpha = FastBleConstants.BUTTON_DISABLE_ALPHA;
                FastBleToolUtils.setViewButtonGray(mHolder.sittingHomeShareLl, false, alpha);
                FastBleToolUtils.setViewButtonGray(mHolder.sittingHomeCircleRl, false, alpha);
                FastBleToolUtils.setViewButtonGray(mHolder.sittingHomeTimeTv, false, alpha);
                FastBleToolUtils.setViewButtonGray(mHolder.sittingHomeSmallCircleLeftRl, false, alpha);
                FastBleToolUtils.setViewButtonGray(mHolder.sittingHomeSmallCircleRightRl, false, alpha);
                FastBleToolUtils.setViewButtonEnable(mHolder.sittingHomeDeviceUpdateRl, false, alpha);
                mHolder.setting_home_unbind_tv.setTextColor(context.getColor(R.color.gray_99));
                FastBleToolUtils.setViewButtonEnable(mHolder.sittingHomeUnbindBtn, false, alpha);

            } else {
                float alpha = FastBleConstants.BUTTON_DISABLE_ALPHA;
                FastBleToolUtils.setViewButtonGray(mHolder.sittingHomeShareLl, true, alpha);
                FastBleToolUtils.setViewButtonGray(mHolder.sittingHomeCircleRl, true, alpha);
                FastBleToolUtils.setViewButtonGray(mHolder.sittingHomeTimeTv, true, alpha);
                FastBleToolUtils.setViewButtonGray(mHolder.sittingHomeSmallCircleLeftRl, true, alpha);
                FastBleToolUtils.setViewButtonGray(mHolder.sittingHomeSmallCircleRightRl, true, alpha);
                FastBleToolUtils.setViewButtonEnable(mHolder.sittingHomeDeviceUpdateRl, true, alpha);
                mHolder.setting_home_unbind_tv.setTextColor(context.getColor(R.color.red_e44e4e));
                FastBleToolUtils.setViewButtonEnable(mHolder.sittingHomeUnbindBtn, true, alpha);
            }

            setOnSettingItemEnable(mHolder);

            //设备名称
//            mHolder.sittingHomeDeviceName.setText(mHolder.mItem.getDeviceName());
            mHolder.sittingHomeDeviceName.setText(mHolder.mItem.getAlias());

            mHolder.loadingImg.clearAnimation();
            mHolder.loadingRl.setVisibility(View.GONE);

            if (!mHolder.mItem.isHaveDataToday()){
                mHolder.sittingHomeShareLl.setVisibility(View.GONE);
                mHolder.bigCircleRoundProgress.setProgress(0, false);
                mHolder.bigCircleRoundProgress.setTextIsDisplayable(false);
                String str1 = context.getString(R.string.sitting_home_sitting_time) + context.getString(R.string.sitting_report_data_none);
                mHolder.socProgress_name.setVisibility(View.GONE);
                mHolder.sittingHomeTimeTv.setText(str1);
                mHolder.smallCircleLeftValue.setText(context.getString(R.string.sitting_report_data_none));
                mHolder.smallCircleLeftPer.setText("");
                mHolder.smallCircleRightValue.setText(context.getString(R.string.sitting_report_data_none));
                mHolder.smallCircleRightPer.setText("");
            }else {
                mHolder.sittingHomeShareLl.setVisibility(View.VISIBLE);
                // 是否需要大圆圈刷新
                int perRight = mHolder.mItem.getPerRight();
                int perHappy = mHolder.mItem.getPerHappy();
                int perLong = mHolder.mItem.getPerLong();
                int dayUsing = mHolder.mItem.getDayUsing() / 1000;
                String str = context.getString(R.string.sitting_home_sitting_time);
                String strDayUsing = str + FastBleToolUtils.getDayUsingAllTime(dayUsing / 60);

                if (dayUsing > 0 && dayUsing < 60) {
                    strDayUsing = str + context.getString(R.string.sitting_home_sitting_1min_time);
                }
                mHolder.socProgress_name.setVisibility(View.VISIBLE);
                mHolder.sittingHomeTimeTv.setText(strDayUsing);

                mHolder.smallCircleLeftValue.setText(perLong + "");
                mHolder.smallCircleRightValue.setText(perHappy + "");
                mHolder.smallCircleRightPer.setText("%");
//            mHolder.bigCircleRoundProgress.setProgress(perRight, true);
                mHolder.bigCircleRoundProgress.setProgress(perRight, false);
//            mHolder.bigCircleRoundProgress.setProgress(perRight, mHolder.mItem.isCircleLoad());
                mHolder.bigCircleRoundProgress.setTextIsDisplayable(true);
            }

//            String modeName = mHolder.mItem.getPostureModeName();
//            mHolder.sittingHomeModelSittingValueTv.setText(modeName);
            int imode = mHolder.mItem.getPostureMode();
            String modeName = CZURConstants.SITTING_MODEL_HOME[imode];
            mHolder.sittingHomeModelSittingValueTv.setText(modeName);

            int vol = mHolder.mItem.getSound();
            mHolder.sittingHomeSittingVolValueTv.setText(vol+"");
            String sens = mHolder.mItem.getSensitivity();
            mHolder.sittingHomeSensitivityValueTv.setText(sens);
            String longSit = mHolder.mItem.getLongSit();
            mHolder.sittingHomeSittingLongValueTv.setText(longSit);
//            String deviceName = mHolder.mItem.getDeviceName();
            String deviceName = mHolder.mItem.getAlias();
            mHolder.sittingHomeDeviceNameValueTv.setText(deviceName);
//            String happyTime = mHolder.mItem.getHappyTimeName();
//            mHolder.sittingHomeHappyTimeValueTv.setText(happyTime);

            // Sensitivity Panel
            int level = mHolder.mItem.getLevel();
            setSensitivityStatus(mHolder, level);

            String lightLevelName = mHolder.mItem.getLightName();
            mHolder.sittingHomeSittingLightValueTv.setText(lightLevelName);

            if (mHolder.mItem.isReadyForCheckUpdate()){
                mHolder.updateImg.setVisibility(View.VISIBLE);
                mHolder.sittingHomeDeviceUpdateValueTv.setText(R.string.sitting_home_update_new);
            }else{
                mHolder.updateImg.setVisibility(View.GONE);
                mHolder.sittingHomeDeviceUpdateValueTv.setText(R.string.sitting_home_update_ok);
            }

            // 设备升级中，禁用设置和解绑按钮
            setSettingItemDisenableForUpdating(mHolder);

            //添加：坐姿仪
            mHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onItemClickListener != null) {
                        onItemClickListener.onItemClick(position, mHolder.mItem);
                    }
                }
            });

            mHolder.sittingHomeShareLl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    long cTime = Calendar.getInstance().getTimeInMillis();
                    if (cTime - lastTime < MIN_CLICK_TIME){
                        return;
                    }
                    lastTime = cTime;

                    if (!NetworkUtils.isConnected()) {
                        FastBleToolUtils.onNoNetWorkButtonClick();
                        return;
                    }

                    Intent intent = new Intent(v.getContext(), SittingHomeShareActivity.class);
                    intent.putExtra("deviceModel", mHolder.mItem);
                    intent.putExtra("deviceId", mHolder.mItem.getId() + "");
                    intent.putExtra("equipmentId", mHolder.mItem.getEquipmentUID());
                    ActivityUtils.startActivity(intent);
                }
            });

            //点击大圆圈,坐姿准确略
            mHolder.sittingHomeCircleRl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!NetworkUtils.isConnected()) {
                        FastBleToolUtils.onNoNetWorkButtonClick();
                        return;
                    }

                    Intent intent = new Intent(v.getContext(), SittingReportActivity.class);
                    intent.putExtra("deviceModel", mHolder.mItem);
                    intent.putExtra("deviceId", mHolder.mItem.getId() + "");
                    intent.putExtra("equipmentId", mHolder.mItem.getEquipmentUID());
                    ActivityUtils.startActivity(intent);
                }
            });

            //点击小圆圈，久坐超时次数
            mHolder.sittingHomeSmallCircleLeftRl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!NetworkUtils.isConnected()) {
                        FastBleToolUtils.onNoNetWorkButtonClick();
                        return;
                    }

                    Intent intent = new Intent(v.getContext(), SittingLongReportActivity.class);
                    intent.putExtra("deviceModel", mHolder.mItem);
                    intent.putExtra("deviceId", mHolder.mItem.getId() + "");
                    intent.putExtra("equipmentId", mHolder.mItem.getEquipmentUID());
                    ActivityUtils.startActivity(intent);
                }
            });

            //点击小圆圈，愉悦指数
            mHolder.sittingHomeSmallCircleRightRl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!NetworkUtils.isConnected()) {
                        FastBleToolUtils.onNoNetWorkButtonClick();
                        return;
                    }

                    Intent intent = new Intent(v.getContext(), SittingHappyReportActivity.class);
                    intent.putExtra("deviceModel", mHolder.mItem);
                    intent.putExtra("deviceId", mHolder.mItem.getId() + "");
                    intent.putExtra("equipmentId", mHolder.mItem.getEquipmentUID());
                    ActivityUtils.startActivity(intent);
                }
            });

            //坐姿模式
            mHolder.sittingHomeModelSittingRl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Intent intent = new Intent(v.getContext(), SittingModelActivity.class);
                    intent.putExtra("deviceModel", mHolder.mItem);
                    ActivityUtils.startActivity(intent);
                }
            });

            //久坐时长
            mHolder.sittingHomeSittingLongRl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Intent intent = new Intent(v.getContext(), SittingLongSitActivity.class);
                    intent.putExtra("deviceModel", mHolder.mItem);
                    ActivityUtils.startActivity(intent);
                }
            });

            //坐姿灵敏度sensitivity
            mHolder.sittingHomeSensitivityRl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
//                    Intent intent = new Intent(v.getContext(), SittingSensitivityActivity.class);
//                    intent.putExtra("deviceModel", mHolder.mItem);
//                    ActivityUtils.startActivity(intent);

                    showSettingSensitivityDialog(v.getContext());
                }
            });
            // Jason 20210923
            mHolder.sittingHomeDeviceSensitivityLevelLlE.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    setAllButtonDisSelected(mHolder, v);
                    if (onSensitivityClickListener != null) {
                        onSensitivityClickListener.onSensitivityClickListener(v);
                    }
                }
            });
            mHolder.sittingHomeDeviceSensitivityLevelLlH.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    setAllButtonDisSelected(mHolder, v);
                    if (onSensitivityClickListener != null) {
                        onSensitivityClickListener.onSensitivityClickListener(v);
                    }
                }
            });
            mHolder.sittingHomeDeviceSensitivityLevelLlM.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    setAllButtonDisSelected(mHolder, v);
                    if (onSensitivityClickListener != null) {
                        onSensitivityClickListener.onSensitivityClickListener(v);
                    }
                }
            });
            mHolder.sittingHomeDeviceSensitivityLevelLlL.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    setAllButtonDisSelected(mHolder, v);
                    if (onSensitivityClickListener != null) {
                        onSensitivityClickListener.onSensitivityClickListener(v);
                    }
                }
            });
            //设备音量
            mHolder.sittingHomeSittingVolRl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Intent intent = new Intent(v.getContext(), SittingVolumeActivity.class);
                    intent.putExtra("deviceModel", mHolder.mItem);
                    ActivityUtils.startActivity(intent);
                }
            });

            //提示灯亮度
            mHolder.sittingHomeSittingLightRl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Intent intent = new Intent(v.getContext(), SittingLightActivity.class);
                    intent.putExtra("deviceModel", mHolder.mItem);
                    ActivityUtils.startActivity(intent);
                }
            });

            //设备名称
            mHolder.sittingHomeDeviceNameRl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Intent intent = new Intent(v.getContext(), SittingDeviceNameActivity.class);
                    intent.putExtra("deviceModel", mHolder.mItem);
                    ActivityUtils.startActivity(intent);
                }
            });

            //update
            mHolder.sittingHomeDeviceUpdateRl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onUpdateClickListener != null) {
                        onUpdateClickListener.onUpdateClickListener();
                    }
                }
            });

            mHolder.sittingHomeUnbindBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onUnbindClickListener != null) {
                        onUnbindClickListener.onUnbindClickListener();
                    }
                }
            });

            mHolder.scroll.setOnScrollChangeListener(new NestedScrollView.OnScrollChangeListener() {
                @Override
                public void onScrollChange(NestedScrollView v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
                    if (onItemScrollListener != null) {
                        onItemScrollListener.onItemScroll(position, scrollY);
                    }
                }
            });

            if (mHolder.mItem.isScrollTop()){
                mHolder.scroll.scrollTo(0, 0);
                mHolder.mItem.setScrollTop(false);
            }

        } else if (holder instanceof AddEquipmentViewHolder) {
            AddEquipmentViewHolder mHolder = (AddEquipmentViewHolder) holder;
            mHolder.addSittingTv.setText(datas.size() > 0 ? R.string.sitting_home_add_text_again : R.string.sitting_home_add_text);
            mHolder.addView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onItemAddClickListener != null) {
                        onItemAddClickListener.onItemAddClick(position);
                    }
                }
            });
        }
    }

    private void showSettingSensitivityDialog(Context context) {
        SittingSensitivityDialog dialog = new SittingSensitivityDialog(context, R.style.sittingDialog,
                new SittingSensitivityDialog.OncloseListener() {
                    @Override
                    public void onClick(boolean confirm) {
                    }
                });
        dialog.setOneButton(true);
        dialog.setPositiveButton(context.getString(R.string.sitting_sensitivity_dialog_yes));
        dialog.setTitle(context.getString(R.string.sitting_sensitivity_dialog_title));
        dialog.setContent(context.getString(R.string.sitting_sensitivity_note));
        dialog.create();
        dialog.show();
    }

    private void setSensitivityStatus(NormalViewHolder mHolder, int level) {
        mHolder.sittingHomeDeviceSensitivityLevelLlE.setSelected(false);
        mHolder.sittingHomeDeviceSensitivityLevelLlH.setSelected(false);
        mHolder.sittingHomeDeviceSensitivityLevelLlM.setSelected(false);
        mHolder.sittingHomeDeviceSensitivityLevelLlL.setSelected(false);

        // 坐姿识别灵敏度 0 - 慢 1 - 适中 2 - 快
        if (level == 0){
            mHolder.sittingHomeDeviceSensitivityLevelLlL.setSelected(true);
        }else if (level == 1){
            mHolder.sittingHomeDeviceSensitivityLevelLlM.setSelected(true);
        }else if (level == 2){
            mHolder.sittingHomeDeviceSensitivityLevelLlH.setSelected(true);
        }else if (level == 3){
            mHolder.sittingHomeDeviceSensitivityLevelLlE.setSelected(true);
        }
    }

    private void setAllButtonDisSelected(NormalViewHolder mHolder, View v) {
        mHolder.sittingHomeDeviceSensitivityLevelLlE.setSelected(false);
        mHolder.sittingHomeDeviceSensitivityLevelLlH.setSelected(false);
        mHolder.sittingHomeDeviceSensitivityLevelLlM.setSelected(false);
        mHolder.sittingHomeDeviceSensitivityLevelLlL.setSelected(false);
        v.setSelected(true);
    }

    @Override
    public int getItemViewType(int position) {
        if (datas.size() == 0) {
            return TYPE_ADD_EQUIPMENT;
        } else if (datas.size() == 1) {
            return TYPE_NORMAL;
        } else {
            if (position >= 0 && position < datas.size()) {
                return TYPE_NORMAL;
            } else {
                return TYPE_ADD_EQUIPMENT;
            }
        }

    }

    @Override
    public int getItemCount() {
//        if (datas.size() == 1) {
//            return datas.size();
//        } else {
//            return datas.size() + 1;
//        }
        if (datas.size() < 1){
            return  1;
        }else {
            return datas.size();
        }
    }

     public static class NormalViewHolder extends RecyclerView.ViewHolder {
        public final View mView;
        RelativeLayout itemView;
        SittingDeviceModel mItem;
        //打开
        ImageView sittingHomeStateImg;
        TextView sittingHomeStateTv;
        TextView sittingHomeDeviceName;
        LinearLayout sittingHomeShareLl;

        RelativeLayout sittingHomeCircleRl; //坐姿准确率
         TextView sitting_home_time;
        RelativeLayout sittingHomeSmallCircleLeftRl;    //久坐超时次数
        RelativeLayout sittingHomeSmallCircleRightRl;   //愉悦指数
        NestedScrollView scroll;
        RelativeLayout loadingRl;
        ImageView loadingImg;

        RelativeLayout sittingHomeModelSittingRl;
        TextView sittingHomeModelSittingTv;
        TextView sittingHomeModelSittingValueTv;
        ImageView sittingHomeModelNewImg;

        RelativeLayout sittingHomeStandarSittingRl;
        TextView sittingHomeStandarSittingTv;
        TextView sittingHomeStandarSittingValueTv;
        ImageView sittingHomeStandarSittingStatusImg;
        ImageView sittingHomeStandarSittingSmallImg;

        RelativeLayout sittingHomeSensitivityRl;
        TextView sittingHomeSensitivityTv;
         TextView sittingHomeSensitivityValueTv;
         ImageView sittingHomeSensitivityIv;

         RelativeLayout sittingHomeSittingLongRl;
        TextView sittingHomeSittingLongTv;
        TextView sittingHomeSittingLongValueTv;

        RelativeLayout sittingHomeSittingVolRl;
        TextView sittingHomeSittingVolTv;
        TextView sittingHomeSittingVolValueTv;

        RelativeLayout sittingHomeSittingLightRl;
        TextView sittingHomeSittingLightTv;
        TextView sittingHomeSittingLightValueTv;

        RelativeLayout sittingHomeDeviceNameRl;
        TextView sittingHomeDeviceNameTv;
        TextView sittingHomeDeviceNameValueTv;

        RelativeLayout sittingHomeHappyTimeRl;
        TextView sittingHomeHappyTimeTv;
        TextView sittingHomeHappyTimeValueTv;

        RoundProgress bigCircleRoundProgress;
        TextView sittingHomeTimeTv;
        TextView socProgress_name;

        TextView smallCircleLeftValue;
        TextView smallCircleLeftPer;
        TextView smallCircleRightPer;
        TextView smallCircleLeftTitle;
        TextView smallCircleRightValue;
        TextView smallCircleRightTitle;


        RelativeLayout sittingHomeUnbindBtn;
        TextView setting_home_unbind_tv;
        ClassicsHeader refreshHeader;
        SmartRefreshLayout refreshLayout;
        TextView statusTv;

        RelativeLayout sittingHomeDeviceUpdateRl;
        TextView sittingHomeDeviceUpdateTv;
        TextView sittingHomeDeviceUpdateValueTv;
        ImageView updateImg;

         LinearLayout sittingHomeDeviceSensitivityLevelLlEll;
         LinearLayout sittingHomeDeviceSensitivityLevelLlHll;
         LinearLayout sittingHomeDeviceSensitivityLevelLlMll;
         LinearLayout sittingHomeDeviceSensitivityLevelLlLll;
        ImageView sittingHomeDeviceSensitivityLevelLlE;
         ImageView sittingHomeDeviceSensitivityLevelLlH;
         ImageView sittingHomeDeviceSensitivityLevelLlM;
         ImageView sittingHomeDeviceSensitivityLevelLlL;
         LinearLayout sittingHomeDeviceSensitivityLL;


         NormalViewHolder(View view) {
            super(view);
            mView = view;
            itemView = (RelativeLayout) view.findViewById(R.id.sitting_home_device_rl);
            scroll = view.findViewById(R.id.scroll);

            // loading
            loadingRl = (RelativeLayout) view.findViewById(R.id.loading_rl);
            loadingImg = (ImageView) view.findViewById(R.id.loading_img);

            refreshLayout = (SmartRefreshLayout)view.findViewById(R.id.refresh_layout);
            refreshHeader = (ClassicsHeader)view.findViewById(R.id.refresh_header);
            statusTv = view.findViewById(R.id.refresh_header_status);

            //设备的状态
            sittingHomeStateImg = (ImageView) view.findViewById(R.id.sitting_home_state_img);
            sittingHomeStateTv = (TextView) view.findViewById(R.id.sitting_home_state_tv);
            sittingHomeDeviceName = (TextView) view.findViewById(R.id.sitting_home_device_name);
            sittingHomeShareLl = (LinearLayout) view.findViewById(R.id.sitting_home_share_ll);

            //主画面，大圆圈
            sittingHomeCircleRl = (RelativeLayout) view.findViewById(R.id.sitting_home_circle_rl);
            bigCircleRoundProgress = view.findViewById(R.id.socProgress);
            sittingHomeTimeTv = (TextView) view.findViewById(R.id.sitting_home_time);
            socProgress_name = (TextView) view.findViewById(R.id.socProgress_name);

            //主画面,小圆圈
            sittingHomeSmallCircleLeftRl = (RelativeLayout) view.findViewById(R.id.jing_circle_small_left);
            sittingHomeSmallCircleRightRl = (RelativeLayout) view.findViewById(R.id.jing_circle_small_right);

            //小圆圈
            smallCircleLeftValue = (TextView) sittingHomeSmallCircleLeftRl.findViewById(R.id.jing_circle_small_value);
            smallCircleLeftTitle = (TextView) sittingHomeSmallCircleLeftRl.findViewById(R.id.jing_circle_small_title);
            smallCircleLeftTitle.setText(R.string.sitting_home_sitting_long);
            smallCircleLeftPer = (TextView) (sittingHomeSmallCircleLeftRl.findViewById(R.id.jing_circle_small_per));
            smallCircleLeftPer.setVisibility(View.GONE);

            smallCircleRightValue = (TextView) (sittingHomeSmallCircleRightRl.findViewById(R.id.jing_circle_small_value));
            smallCircleRightTitle = (TextView) (sittingHomeSmallCircleRightRl.findViewById(R.id.jing_circle_small_title));
            smallCircleRightTitle.setText(R.string.sitting_home_sitting_happy);
            smallCircleRightPer = (TextView) (sittingHomeSmallCircleRightRl.findViewById(R.id.jing_circle_small_per));

            // 坐姿模式
            sittingHomeModelSittingRl = (RelativeLayout) view.findViewById(R.id.jing_main_sitting_model_rl);
            sittingHomeModelSittingTv = (TextView) view.findViewById(R.id.jing_main_setting_model_tv);
            sittingHomeModelSittingValueTv = (TextView) view.findViewById(R.id.jing_main_setting_model_value_tv);
            sittingHomeModelNewImg = (ImageView)view.findViewById(R.id.need_new_img);

            //标准坐姿
            sittingHomeStandarSittingRl = (RelativeLayout) view.findViewById(R.id.jing_main_sitting_rl);
            sittingHomeStandarSittingTv = (TextView) view.findViewById(R.id.jing_main_setting_tv);
            sittingHomeStandarSittingValueTv = (TextView) view.findViewById(R.id.jing_main_setting_value_tv);
            sittingHomeStandarSittingStatusImg = (ImageView)view.findViewById(R.id.jing_main_setting_statue_img);
            sittingHomeStandarSittingSmallImg = (ImageView)view.findViewById(R.id.jing_main_setting_small_img);

            //坐姿灵敏度
//            sittingHomeSensitivityRl = (RelativeLayout) view.findViewById(R.id.jing_main_sensitivity_rl);
            sittingHomeSensitivityTv = (TextView) view.findViewById(R.id.jing_main_sensitivity_tv);
            sittingHomeSensitivityValueTv = (TextView) view.findViewById(R.id.jing_main_sensitivity_value_tv);
            // Jason 20210923
            sittingHomeSensitivityRl = (RelativeLayout)view.findViewById(R.id.jing_main_sensitivity_title_rl);
            sittingHomeSensitivityIv = (ImageView)view.findViewById(R.id.jing_main_sensitivity_title_img);
             sittingHomeDeviceSensitivityLevelLlEll = (LinearLayout)view.findViewById(R.id.jing_main_sensitivity_level1_ll);
             sittingHomeDeviceSensitivityLevelLlHll = (LinearLayout)view.findViewById(R.id.jing_main_sensitivity_level2_ll);
             sittingHomeDeviceSensitivityLevelLlMll = (LinearLayout)view.findViewById(R.id.jing_main_sensitivity_level3_ll);
             sittingHomeDeviceSensitivityLevelLlLll = (LinearLayout)view.findViewById(R.id.jing_main_sensitivity_level4_ll);
             sittingHomeDeviceSensitivityLevelLlE = (ImageView)view.findViewById(R.id.jing_main_sensitivity_level1_iv);
             sittingHomeDeviceSensitivityLevelLlH = (ImageView)view.findViewById(R.id.jing_main_sensitivity_level2_iv);
             sittingHomeDeviceSensitivityLevelLlM = (ImageView)view.findViewById(R.id.jing_main_sensitivity_level3_iv);
             sittingHomeDeviceSensitivityLevelLlL = (ImageView)view.findViewById(R.id.jing_main_sensitivity_level4_iv);

             sittingHomeDeviceSensitivityLL = (LinearLayout)view.findViewById(R.id.jing_main_sensitivity_ll);

             //久坐提醒
            sittingHomeSittingLongRl = (RelativeLayout) view.findViewById(R.id.jing_main_long_sit_rl1);
            sittingHomeSittingLongTv = (TextView) view.findViewById(R.id.jing_main_long_sit_tv);
            sittingHomeSittingLongValueTv = (TextView) view.findViewById(R.id.jing_main_long_sit_value_tv);

            //设备音量
            sittingHomeSittingVolRl = (RelativeLayout) view.findViewById(R.id.jing_main_device_vol_rl);
            sittingHomeSittingVolTv = (TextView) view.findViewById(R.id.jing_main_device_vol_tv);
            sittingHomeSittingVolValueTv = (TextView) view.findViewById(R.id.jing_main_device_vol_value_tv);

            //提示灯亮度
            sittingHomeSittingLightRl = (RelativeLayout) view.findViewById(R.id.jing_main_device_light_rl);
            sittingHomeSittingLightTv = (TextView) view.findViewById(R.id.jing_main_device_light_tv);
            sittingHomeSittingLightValueTv = (TextView) view.findViewById(R.id.jing_main_device_light_value_tv);

            //设备名称
            sittingHomeDeviceNameRl = (RelativeLayout) view.findViewById(R.id.jing_main_device_name_rl);
            sittingHomeDeviceNameTv = (TextView) view.findViewById(R.id.jing_main_device_name_tv);
            sittingHomeDeviceNameValueTv = (TextView) view.findViewById(R.id.jing_main_device_name_value_tv);

            //坐姿心情瞬间
            sittingHomeHappyTimeRl = (RelativeLayout) view.findViewById(R.id.jing_main_happytime_rl);
            sittingHomeHappyTimeTv = (TextView) view.findViewById(R.id.jing_main_happytime_tv);
            sittingHomeHappyTimeValueTv = (TextView) view.findViewById(R.id.jing_main_happytime_value_tv);

            // update
            sittingHomeDeviceUpdateRl = (RelativeLayout)view.findViewById(R.id.jing_main_update_rl);
            sittingHomeDeviceUpdateTv = (TextView) view.findViewById(R.id.jing_main_update_tv);
            sittingHomeDeviceUpdateValueTv = (TextView) view.findViewById(R.id.jing_main_update_value_tv);
            updateImg =view.findViewById(R.id.need_update_img);

            sittingHomeUnbindBtn = (RelativeLayout) view.findViewById(R.id.sitting_home_unbind_rl);
            setting_home_unbind_tv = (TextView) view.findViewById(R.id.setting_home_unbind_tv);
        }
    }

    public static class AddEquipmentViewHolder extends RecyclerView.ViewHolder {
        public final View mView;
        RelativeLayout itemView;
        LinearLayout addView;
        TextView addSittingTv;

        AddEquipmentViewHolder(View view) {
            super(view);
            mView = view;
            addSittingTv = (TextView) view.findViewById(R.id.add_sitting_tv);
            itemView = (RelativeLayout) view.findViewById(R.id.sitting_empty_rl);
            addView = (LinearLayout) view.findViewById(R.id.sitting_home_empty_inner_rl);
        }
    }

    //
    public OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void onItemClick(int position, SittingDeviceModel AuraDeviceModel);
    }

    // 添加坐姿仪
    private OnItemAddClickListener onItemAddClickListener;

    public void setOnItemAddClickListener(OnItemAddClickListener onItemAddClickListener) {
        this.onItemAddClickListener = onItemAddClickListener;
    }

    public interface OnItemAddClickListener {
        void onItemAddClick(int position);
    }

    // 坐姿仪主界面下拉刷新数据
    private OnRefreshListener onRefreshListener;

    public void setOnRefreshListener(OnRefreshListener onRefreshListener) {
        this.onRefreshListener = onRefreshListener;
    }

    public interface OnRefreshListener {
        void onRefreshListener(RefreshLayout refreshLayout);
    }

    // 主界面上下滑动记录滚动位置
    private OnItemScrollListener onItemScrollListener;

    public void setOnItemScrollListener(OnItemScrollListener onItemScrollListener) {
        this.onItemScrollListener = onItemScrollListener;
    }

    public interface OnItemScrollListener {
        void onItemScroll(int position, int y);
    }

    // 坐姿仪主界面 update
    private OnUpdateClickListener onUpdateClickListener;

    public void setOnUpdateClickListener(OnUpdateClickListener onUpdateClickListener) {
        this.onUpdateClickListener = onUpdateClickListener;
    }

    public interface OnUpdateClickListener {
        void onUpdateClickListener();
    }

    // 坐姿仪主界面 解绑
    private OnUnbindClickListener onUnbindClickListener;

    public void setOnUnbindClickListener(OnUnbindClickListener onUnbindClickListener) {
        this.onUnbindClickListener = onUnbindClickListener;
    }

    public interface OnUnbindClickListener {
        void onUnbindClickListener();
    }

    // 坐姿仪主界面 Sensitivity panel
    private OnSensitivityClickListener onSensitivityClickListener;

    public void setOnSensitivityClickListener(OnSensitivityClickListener onSensitivityClickListener) {
        this.onSensitivityClickListener = onSensitivityClickListener;
    }

    public interface OnSensitivityClickListener {
        void onSensitivityClickListener(View v);
    }


    // 设置项置灰（online=false）
    private void setOnSettingItemEnable(NormalViewHolder mHolder){
        boolean flag = mHolder.mItem.isOnline();
        float alpha = FastBleConstants.BUTTON_DISABLE_ALPHA4;
        if (flag) {
            alpha = FastBleConstants.BUTTON_NO_ALPHA;
        }
        //坐姿模式
        mHolder.sittingHomeModelSittingRl.setAlpha(alpha);
        mHolder.sittingHomeModelSittingRl.setEnabled(flag);
        mHolder.sittingHomeModelSittingRl.setClickable(flag);
        //坐姿灵敏度
//        mHolder.sittingHomeSensitivityRl.setAlpha(alpha);
//        mHolder.sittingHomeSensitivityRl.setEnabled(flag);
//        mHolder.sittingHomeSensitivityRl.setClickable(flag);

        mHolder.sittingHomeDeviceSensitivityLevelLlE.setAlpha(alpha);
        mHolder.sittingHomeDeviceSensitivityLevelLlE.setEnabled(flag);
        mHolder.sittingHomeDeviceSensitivityLevelLlE.setClickable(flag);

        mHolder.sittingHomeDeviceSensitivityLevelLlH.setAlpha(alpha);
        mHolder.sittingHomeDeviceSensitivityLevelLlH.setEnabled(flag);
        mHolder.sittingHomeDeviceSensitivityLevelLlH.setClickable(flag);

        mHolder.sittingHomeDeviceSensitivityLevelLlM.setAlpha(alpha);
        mHolder.sittingHomeDeviceSensitivityLevelLlM.setEnabled(flag);
        mHolder.sittingHomeDeviceSensitivityLevelLlM.setClickable(flag);

        mHolder.sittingHomeDeviceSensitivityLevelLlL.setAlpha(alpha);
        mHolder.sittingHomeDeviceSensitivityLevelLlL.setEnabled(flag);
        mHolder.sittingHomeDeviceSensitivityLevelLlL.setClickable(flag);

        mHolder.sittingHomeDeviceSensitivityLevelLlEll.setAlpha(alpha);
        mHolder.sittingHomeDeviceSensitivityLevelLlHll.setAlpha(alpha);
        mHolder.sittingHomeDeviceSensitivityLevelLlMll.setAlpha(alpha);
        mHolder.sittingHomeDeviceSensitivityLevelLlLll.setAlpha(alpha);

        //久坐提醒
        mHolder.sittingHomeSittingLongRl.setAlpha(alpha);
        mHolder.sittingHomeSittingLongRl.setEnabled(flag);
        mHolder.sittingHomeSittingLongRl.setClickable(flag);
        //设备音量
        mHolder.sittingHomeSittingVolRl.setAlpha(alpha);
        mHolder.sittingHomeSittingVolRl.setEnabled(flag);
        mHolder.sittingHomeSittingVolRl.setClickable(flag);
        //提示灯亮度
        mHolder.sittingHomeSittingLightRl.setAlpha(alpha);
        mHolder.sittingHomeSittingLightRl.setEnabled(flag);
        mHolder.sittingHomeSittingLightRl.setClickable(flag);
        //设备名称
        mHolder.sittingHomeDeviceNameRl.setAlpha(alpha);
        mHolder.sittingHomeDeviceNameRl.setEnabled(flag);
        mHolder.sittingHomeDeviceNameRl.setClickable(flag);
        //坐姿心情瞬间
        mHolder.sittingHomeHappyTimeRl.setAlpha(alpha);
        mHolder.sittingHomeHappyTimeRl.setEnabled(flag);
        mHolder.sittingHomeHappyTimeRl.setClickable(flag);
        // update
        mHolder.sittingHomeDeviceUpdateRl.setAlpha(alpha);
        mHolder.sittingHomeDeviceUpdateRl.setEnabled(flag);
        mHolder.sittingHomeDeviceUpdateRl.setClickable(flag);
    }

    // 坐姿模式切换、坐姿灵敏度、久坐时长、设备名称、设备音量、提示灯亮度、解除绑定操作”进行置灰显示，不可对相关功能进行操作。
    // 同时在固件升级过程中，进入主页时“固件升级”模块的状态变为：“正在升级...”
    // （isDeviceUpdating=true）
    private void setSettingItemDisenableForUpdating(NormalViewHolder mHolder){
        boolean isOnline = mHolder.mItem.isOnline();
        // 如果设备不在线，不做判断了，离线已经设置好了。
        if (!isOnline){
            if (!NetworkUtils.isConnected()) {
                return;
            }
            // 解绑
            mHolder.sittingHomeUnbindBtn.setAlpha(FastBleConstants.BUTTON_NO_ALPHA);
            mHolder.sittingHomeUnbindBtn.setEnabled(true);
            mHolder.sittingHomeUnbindBtn.setClickable(true);
            return;
        }

        // if isDeviceUpdating=true,则flag=false，设置是为灰，不可点击
        boolean flag = !mHolder.mItem.isDeviceUpdating();
        float alpha = FastBleConstants.BUTTON_DISABLE_ALPHA4;
        if (flag) {
            alpha = FastBleConstants.BUTTON_NO_ALPHA;
        }

        //坐姿模式
        mHolder.sittingHomeModelSittingRl.setAlpha(alpha);
        mHolder.sittingHomeModelSittingRl.setEnabled(flag);
        mHolder.sittingHomeModelSittingRl.setClickable(flag);
        //坐姿灵敏度
//        mHolder.sittingHomeSensitivityRl.setAlpha(alpha);
//        mHolder.sittingHomeSensitivityRl.setEnabled(flag);
//        mHolder.sittingHomeSensitivityRl.setClickable(flag);

        mHolder.sittingHomeDeviceSensitivityLevelLlE.setAlpha(alpha);
        mHolder.sittingHomeDeviceSensitivityLevelLlE.setEnabled(flag);
        mHolder.sittingHomeDeviceSensitivityLevelLlE.setClickable(flag);

        mHolder.sittingHomeDeviceSensitivityLevelLlH.setAlpha(alpha);
        mHolder.sittingHomeDeviceSensitivityLevelLlH.setEnabled(flag);
        mHolder.sittingHomeDeviceSensitivityLevelLlH.setClickable(flag);

        mHolder.sittingHomeDeviceSensitivityLevelLlM.setAlpha(alpha);
        mHolder.sittingHomeDeviceSensitivityLevelLlM.setEnabled(flag);
        mHolder.sittingHomeDeviceSensitivityLevelLlM.setClickable(flag);

        mHolder.sittingHomeDeviceSensitivityLevelLlL.setAlpha(alpha);
        mHolder.sittingHomeDeviceSensitivityLevelLlL.setEnabled(flag);
        mHolder.sittingHomeDeviceSensitivityLevelLlL.setClickable(flag);

        mHolder.sittingHomeDeviceSensitivityLevelLlEll.setAlpha(alpha);
        mHolder.sittingHomeDeviceSensitivityLevelLlHll.setAlpha(alpha);
        mHolder.sittingHomeDeviceSensitivityLevelLlMll.setAlpha(alpha);
        mHolder.sittingHomeDeviceSensitivityLevelLlLll.setAlpha(alpha);

        //久坐提醒
        mHolder.sittingHomeSittingLongRl.setAlpha(alpha);
        mHolder.sittingHomeSittingLongRl.setEnabled(flag);
        mHolder.sittingHomeSittingLongRl.setClickable(flag);
        //设备音量
        mHolder.sittingHomeSittingVolRl.setAlpha(alpha);
        mHolder.sittingHomeSittingVolRl.setEnabled(flag);
        mHolder.sittingHomeSittingVolRl.setClickable(flag);
        //提示灯亮度
        mHolder.sittingHomeSittingLightRl.setAlpha(alpha);
        mHolder.sittingHomeSittingLightRl.setEnabled(flag);
        mHolder.sittingHomeSittingLightRl.setClickable(flag);
        //设备名称
        mHolder.sittingHomeDeviceNameRl.setAlpha(alpha);
        mHolder.sittingHomeDeviceNameRl.setEnabled(flag);
        mHolder.sittingHomeDeviceNameRl.setClickable(flag);
        // update不置灰，需要改文字
        if (!flag) {
            mHolder.sittingHomeDeviceUpdateValueTv.setText(R.string.sitting_updating_msg);
        }
        // 解绑
        if (NetworkUtils.isConnected()) {
            mHolder.sittingHomeUnbindBtn.setAlpha(alpha);
            mHolder.sittingHomeUnbindBtn.setEnabled(flag);
            mHolder.sittingHomeUnbindBtn.setClickable(flag);
        }

        if (!NetworkUtils.isConnected()) {
            FastBleToolUtils.setViewButtonEnable(mHolder.sittingHomeDeviceUpdateRl, false);
        } else {
            FastBleToolUtils.setViewButtonEnable(mHolder.sittingHomeDeviceUpdateRl, true);
        }

    }

}