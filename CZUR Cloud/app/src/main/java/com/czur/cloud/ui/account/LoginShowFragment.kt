package com.czur.cloud.ui.account

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import com.czur.cloud.R
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.livedatabus.LiveDataBus
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.utils.RomUtils
import com.czur.cloud.ui.starry.utils.singleClick
import com.czur.cloud.util.AppClearUtils
import com.czur.czurutils.log.logI
import kotlinx.android.synthetic.main.login_show_fragment.*
import kotlinx.coroutines.delay

class LoginShowFragment: Fragment() {

    private val isBtnClick = MutableLiveData<Boolean>(false) // 无线投屏按钮和登录按钮是否点击了其中一个

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.login_show_fragment, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        loginshow_btn_eshare?.singleClick {
            logI("loginshow_btn_eshare")
            if (isBtnClick.value == false) {
                launch {
                    isBtnClick.value = true
                    AppClearUtils.startScreenNotify()
                    AppClearUtils.checkESharePermission(requireContext(), requireActivity())
                    delay(800)
                    isBtnClick.value = false
                }
            }
        }

        loginshow_btn_login?.singleClick {
            logI("loginshow_btn_login")
            Thread.sleep(200)
            if (isBtnClick.value == false) {
                launch {
                    AppClearUtils.stopScreenNotify()
                    isBtnClick.value = true
                    LiveDataBus.get().with(StarryConstants.LOGIN_SHOW_LOGIN_BTN).value = true
                    delay(800)
                    isBtnClick.value = false
                }
            }
        }

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == StarryConstants.RESULT_CHECK_OVERLAYS_CODE) {
            launch {
                AppClearUtils.startScreenNotify()
                if (RomUtils.isXiaoMi()) {
                    delay(500)
                }
                AppClearUtils.checkESharePermission(requireContext(), requireActivity())
            }

        }
    }

}