package com.czur.cloud.ui.starry.adapter.viewpager

import android.annotation.SuppressLint
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.czur.cloud.ui.starry.meeting.fragment.newmain.*

class MyFragmentStateAdapter : FragmentStateAdapter {

    private val fid1 = MeetingDisplayMainFragment().hashCode().toLong()
    private val fid2 = MeetingDisplayGridTwoFragment().hashCode().toLong()
    private val fid3 = MeetingDisplayGridThreeFragment().hashCode().toLong()
    private val fid4 = MeetingDisplayGridFourFragment().hashCode().toLong()
    private val fid5 = MeetingDisplayGridFiveFragment().hashCode().toLong()

    private val ids: ArrayList<Long>
        get() = arrayListOf(fid1, fid2, fid3, fid4, fid5)

    private var fragments: List<Fragment> = emptyList()
    private val createdIds = hashSetOf<Long>()
    constructor(manager: FragmentManager,lifecycle: Lifecycle) : super(manager,lifecycle) {
    }

    constructor(fragmentActivity: FragmentActivity) : super(fragmentActivity) {
    }

    constructor(fragmentActivity: FragmentActivity, fragments: List<Fragment>) : super(fragmentActivity) {
        this.fragments = fragments
    }

    constructor(fragment: Fragment, fragments: List<Fragment>) : super(fragment) {
        this.fragments = fragments
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setFragmentList(fragments: List<Fragment>) {
        if (this.fragments !== fragments || this.fragments.size != fragments.size) {
            // 数据有改变,才去notify
            this.fragments = fragments
            notifyDataSetChanged()
        }
    }

    override fun getItemCount(): Int {
        return fragments.size
    }

    override fun getItemId(position: Int): Long {
        return ids[position]
    }

    override fun containsItem(itemId: Long): Boolean {
        return createdIds.contains(itemId)
    }

    override fun createFragment(position: Int): Fragment {
//        logI("MyFragmentStateAdapter.createFragment.position=${position}")
        val id = ids[position]
        createdIds.add(id)
        return fragments[position]
    }
}