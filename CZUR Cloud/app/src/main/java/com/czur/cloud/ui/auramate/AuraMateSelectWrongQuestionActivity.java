package com.czur.cloud.ui.auramate;

import static com.czur.czurutils.log.CZURLogUtilsKt.logE;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.model.AuraFileTotalModel;
import com.czur.cloud.model.BaseModel;
import com.czur.cloud.model.PositionListModel;
import com.czur.cloud.model.WrongQuestionModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.component.cropper.CropImageView;
import com.facebook.common.executors.CallerThreadExecutor;
import com.facebook.common.references.CloseableReference;
import com.facebook.datasource.DataSource;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.imagepipeline.core.ImagePipeline;
import com.facebook.imagepipeline.datasource.BaseBitmapDataSubscriber;
import com.facebook.imagepipeline.image.CloseableImage;
import com.facebook.imagepipeline.request.ImageRequest;
import com.facebook.imagepipeline.request.ImageRequestBuilder;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

public class AuraMateSelectWrongQuestionActivity extends AuramateBaseActivity implements View.OnClickListener {

    public static final int CANNOT_CUT_FILE = 1036;
    public static final int BAD_JPEG_IMG = 1037;
    public static final int MIN_CLICK_DELAY_TIME = 800;
    private long lastClickTime = 0;
    private boolean isRun = false;
    private CropImageView cropImageView;
    private AuraFileTotalModel.FileListBean entity;
    private int x, y;
    private int width;
    private int height;
    private Bitmap originalBitmap;
    private int rotated;
    private String currentMode;
    private int position;
    private boolean isFolder;
    private boolean hasBig;
    private int[] wrongCircles = new int[]{R.mipmap.wrong_circle_1, R.mipmap.wrong_circle_2, R.mipmap.wrong_circle_3
            , R.mipmap.wrong_circle_4, R.mipmap.wrong_circle_5, R.mipmap.wrong_circle_6, R.mipmap.wrong_circle_7
            , R.mipmap.wrong_circle_8, R.mipmap.wrong_circle_9};

    private View imgBack;
    private TextView finishBtn;
    private TextView cropBtn;
    private String tagId;
    private List<PositionListModel> positionListModels;
    private UserPreferences userPreferences;

    private int wrongCount = 0;

    private String ownerId;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.black_2a);
        BarUtils.setNavBarColor(this, getColor(R.color.black_2a));
        BarUtils.setStatusBarLightMode(this, false);
        setContentView(R.layout.activity_select_wrong_question);
        initComponent();
        registerEvent();

    }

    @Override
    public boolean PCNeedFinish() {
        return false;
    }

    private void registerEvent() {
        imgBack.setOnClickListener(this);
        finishBtn.setOnClickListener(this);
        cropBtn.setOnClickListener(this);
        setNetListener();
    }

    private void initComponent() {
        userPreferences = UserPreferences.getInstance(this);
        positionListModels = new ArrayList<>();

        ownerId = getIntent().getStringExtra("ownerId");
        entity = (AuraFileTotalModel.FileListBean) getIntent().getSerializableExtra("image");
        tagId = getIntent().getStringExtra("tagId");
        currentMode = getIntent().getStringExtra("mode");
        isFolder = getIntent().getBooleanExtra("isFolder", false);
        position = getIntent().getIntExtra("position", 0);
        hasBig = getIntent().getBooleanExtra("hasBig", false);

        imgBack = findViewById(R.id.img_back);
        finishBtn = (TextView) findViewById(R.id.finish_btn);
        cropBtn = (TextView) findViewById(R.id.crop_btn);
        cropImageView = (CropImageView) findViewById(R.id.cropImageView);


        final ImageRequest imageRequest = ImageRequestBuilder.newBuilderWithSource(Uri.parse(hasBig ? getBigUrl(entity) : getMiddleUrl(entity))).build();
        final ImagePipeline imagePipeline = Fresco.getImagePipeline();
        DataSource<CloseableReference<CloseableImage>> dataSource = imagePipeline.fetchDecodedImage(imageRequest, this);
        dataSource.subscribe(new BaseBitmapDataSubscriber() {
            @Override
            protected void onNewResultImpl(final Bitmap bitmap) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        originalBitmap = bitmap;
                        cropImageView.setImageBitmap(originalBitmap);
                    }
                });
            }

            @Override
            protected void onFailureImpl(DataSource<CloseableReference<CloseableImage>> dataSource) {

            }
        }, CallerThreadExecutor.getInstance());

    }

    private String getBigUrl(AuraFileTotalModel.FileListBean entity) {
        return entity.getSingle();
    }


    private String getMiddleUrl(AuraFileTotalModel.FileListBean entity) {
        return entity.getMiddleSingle();
    }

    @Override
    public void onClick(final View v) {
        switch (v.getId()) {
            case R.id.finish_btn:
                selectWrongQuestion();
                break;
            case R.id.img_back:
                ActivityUtils.finishToActivity(AuraMatePreviewActivity.class, false);
                break;

            case R.id.crop_btn:
                long currentTime = Calendar.getInstance().getTimeInMillis();
                if (currentTime - lastClickTime > MIN_CLICK_DELAY_TIME && !isRun) {
                    lastClickTime = currentTime;
                    cropImage();
                }
                break;
        }
    }

    @SuppressWarnings("AlibabaAvoidManuallyCreateThread")
    private void selectWrongQuestion() {
        addToCutList();
        int originalWidth = originalBitmap.getWidth();
        int originalHeight = originalBitmap.getHeight();
        isRun = true;
        showProgressDialog(true);
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    MiaoHttpEntity<BaseModel> questionCropEntity = HttpManager.getInstance().request().questionCrop(userPreferences.getUserId(), entity.getId(), 1 + "", originalWidth + CZURConstants.EMPTY, originalHeight + CZURConstants.EMPTY, rotated + CZURConstants.EMPTY,
                            tagId, ownerId, new Gson().toJson(positionListModels), BaseModel.class);
                    if (questionCropEntity == null) {
                        generatePdfFailed();
                    } else {
                        if (questionCropEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                            while (isRun) {
                                MiaoHttpEntity<WrongQuestionModel> resultEntity = HttpManager.getInstance().request().questionCropResult(userPreferences.getUserId(), questionCropEntity.getBody() + "", WrongQuestionModel.class);
                                if (resultEntity == null) {
                                    generatePdfFailed();
                                } else {
                                    if (resultEntity.getCode() == MiaoHttpManager.STATUS_PDF_RETURN_CODE) {
                                        Thread.sleep(1000);
                                    } else if (resultEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                                        final WrongQuestionModel wrongQuestionModel = resultEntity.getBody();
                                        if (wrongQuestionModel.getPercent() == 100.0) {
                                            generatePdfSuccess();
                                        }
                                    } else {
                                        generatePdfFailed();
                                    }
                                }
                            }
                        } else {
                            generatePdfFailed();
                        }
                    }
                } catch (Exception e) {
                    logE(e.toString());
                    generatePdfFailed();
                }
            }
        }).start();

    }


    private void generatePdfFailed() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                isRun = false;
                hideProgressDialog();
                showMessage(R.string.request_server_error);
            }
        });

    }

    private void generatePdfSuccess() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                hideProgressDialog();
                isRun = false;
                Intent intent = new Intent(AuraMateSelectWrongQuestionActivity.this, AuraMateWrongQuestionActivity.class);
                intent.putExtra("isNormal", false);
                intent.putExtra("equipmentId", equipmentId);
                intent.putExtra("ownerId", ownerId);
                intent.putExtra("tagId", tagId);
                ActivityUtils.startActivity(intent);
            }
        });

    }

    private void cropImage() {

        if (wrongCount > 7) {
            showMessage(R.string.over_range_than_9);
        } else {
            isRun = false;
            addToCutList();

            Bitmap tempBitmap = originalBitmap.copy(Bitmap.Config.ARGB_8888, true);
            Canvas canvas = new Canvas(tempBitmap);
            //图像上画矩形
            Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG);
            paint.setColor(0xfff07574);
            paint.setStyle(Paint.Style.STROKE);//不填充
            paint.setStrokeWidth(SizeUtils.dp2px(1));  //线的宽度
            canvas.drawRect(x, y, x + width, y + height, paint);


            Paint paint1 = new Paint(Paint.ANTI_ALIAS_FLAG);
            paint1.setColor(0xfff07574);
            paint1.setStyle(Paint.Style.FILL_AND_STROKE);//不填充
            paint1.setStrokeWidth(SizeUtils.dp2px(1));  //线的宽度
            RectF oval = new RectF(x + width - SizeUtils.dp2px(12), y, x + width, y + SizeUtils.dp2px(12));
            canvas.drawBitmap(ImageUtils.getBitmap(wrongCircles[wrongCount]), null, oval, paint);
            wrongCount++;


            cropImageView.clearImage();
            cropImageView.clearAspectRatio();
            cropImageView.wrongCountAdded();
            cropImageView.setImageBitmap(tempBitmap);
            int screenHeight = ScreenUtils.getScreenHeight();
            int screenWidth = ScreenUtils.getScreenWidth();

            originalBitmap = tempBitmap;
        }


    }

    private void addToCutList() {
        Bitmap cropped = cropImageView.getCroppedImage();
        float[] points = cropImageView.getCropPoints();
        x = (int) points[0];
        y = (int) points[1];
        width = cropped.getWidth();
        height = cropped.getHeight();
        PositionListModel positionListModel = new PositionListModel();
        positionListModel.setCutX(x);
        positionListModel.setCutY(y);
        positionListModel.setCutHeight(height);
        positionListModel.setCutWidth(width);
        positionListModels.add(positionListModel);
    }

    @Override
    public void onBackPressed() {
        if (!isRun) {
            super.onBackPressed();
        }
    }

}
