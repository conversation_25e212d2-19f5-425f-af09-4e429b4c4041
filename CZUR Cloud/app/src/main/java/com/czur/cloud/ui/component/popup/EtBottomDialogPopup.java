package com.czur.cloud.ui.component.popup;

import android.app.Dialog;
import android.content.Context;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.R;

/**
 * Created by Yz on 2018/3/20
 * Email：<EMAIL>
 */

public class EtBottomDialogPopup extends Dialog {
    public static final float DIMMED_OPACITY = 0.2f;


    public EtBottomDialogPopup(Context context) {
        super(context);
    }

    public EtBottomDialogPopup(Context context, int theme) {
        super(context, theme);
    }


    public static class Builder implements View.OnClickListener {
        private Context mContext;
        private RelativeLayout etBottomSheetSingleBtn;
        private TextView etBottomSheetSingleTv;
        private ImageView etBottomSheetSingleImg;
        private RelativeLayout etBottomSheetSurfacesBtn;
        private TextView etBottomSheetSurfacesTv;
        private ImageView etBottomSheetSurfacesImg;
        private RelativeLayout etPreviewBottomSheetCancelBtn;


        public Builder(Context mContext, OnBottomSheetClickListener onBottomSheetClickListener) {
            this.mContext = mContext;
            this.onBottomSheetClickListener = onBottomSheetClickListener;
        }

        /**
         * 点击事件接口
         **/
        public interface OnBottomSheetClickListener {
            /**
             * @param viewId
             */
            void onClick(int viewId);
        }

        private OnBottomSheetClickListener onBottomSheetClickListener;

        private void setOnBottomSheetClickListener(OnBottomSheetClickListener onBottomSheetClickListener) {
            this.onBottomSheetClickListener = onBottomSheetClickListener;

        }

        @Override
        public void onClick(View v) {
            switch (v.getId()) {
                case R.id.et_bottom_sheet_single_btn:
                    etBottomSheetSingleTv.setTextColor(mContext.getResources().getColor(R.color.blue_29b0d7));
                    etBottomSheetSingleImg.setVisibility(View.VISIBLE);
                    etBottomSheetSurfacesTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetSurfacesImg.setVisibility(View.GONE);
                    if (onBottomSheetClickListener != null) {
                        onBottomSheetClickListener.onClick(R.id.et_bottom_sheet_single_btn);
                    }
                    break;
                case R.id.et_bottom_sheet_surfaces_btn:
                    etBottomSheetSingleTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetSingleImg.setVisibility(View.GONE);
                    etBottomSheetSurfacesTv.setTextColor(mContext.getResources().getColor(R.color.blue_29b0d7));
                    etBottomSheetSurfacesImg.setVisibility(View.VISIBLE);
                    if (onBottomSheetClickListener != null) {
                        onBottomSheetClickListener.onClick(R.id.et_bottom_sheet_surfaces_btn);
                    }
                    break;
                case R.id.et_preview_bottom_sheet_cancel_btn:
                    if (onBottomSheetClickListener != null) {
                        onBottomSheetClickListener.onClick(R.id.et_preview_bottom_sheet_cancel_btn);
                    }
                    break;
                default:
                    break;
            }
        }


        public EtBottomDialogPopup create() {
            LayoutInflater inflater = (LayoutInflater) mContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);

            final EtBottomDialogPopup dialog = new EtBottomDialogPopup(mContext, R.style.SocialAccountDialogStyle);
            View layout = commonCustomPopLayout(inflater, dialog);
            dialog.setContentView(layout);
            dialog.setCanceledOnTouchOutside(true);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            params.dimAmount = DIMMED_OPACITY;
            params.gravity = Gravity.BOTTOM;
            params.width = WindowManager.LayoutParams.MATCH_PARENT;
            params.height = SizeUtils.dp2px(160);
            dialog.getWindow().setAttributes(params);
            return dialog;
        }

        private View commonCustomPopLayout(LayoutInflater inflater, final EtBottomDialogPopup dialog) {
            WindowManager.LayoutParams lp = dialog.getWindow().getAttributes();
            lp.dimAmount = 0.8f;
            dialog.getWindow().setAttributes(lp);
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

            View layout = inflater.inflate(R.layout.dialog_et_preview_bottom_sheet, null, false);
            RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT,
                    RelativeLayout.LayoutParams.WRAP_CONTENT);
            dialog.addContentView(layout, params);
            etBottomSheetSingleBtn = (RelativeLayout) layout.findViewById(R.id.et_bottom_sheet_single_btn);
            etBottomSheetSingleTv = (TextView) layout.findViewById(R.id.et_bottom_sheet_single_tv);
            etBottomSheetSingleImg = (ImageView) layout.findViewById(R.id.et_bottom_sheet_single_img);
            etBottomSheetSurfacesBtn = (RelativeLayout) layout.findViewById(R.id.et_bottom_sheet_surfaces_btn);
            etBottomSheetSurfacesTv = (TextView) layout.findViewById(R.id.et_bottom_sheet_surfaces_tv);
            etBottomSheetSurfacesImg = (ImageView) layout.findViewById(R.id.et_bottom_sheet_surfaces_img);
            etPreviewBottomSheetCancelBtn = (RelativeLayout) layout.findViewById(R.id.et_preview_bottom_sheet_cancel_btn);


            etBottomSheetSingleBtn.setOnClickListener(this);
            etBottomSheetSurfacesBtn.setOnClickListener(this);
            etPreviewBottomSheetCancelBtn.setOnClickListener(this);

            return layout;
        }
    }
}
