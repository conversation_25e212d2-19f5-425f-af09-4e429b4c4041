package com.czur.cloud.model;

import java.io.Serializable;
import java.util.List;

public class EtPreviewModel {


    /**
     * fileList : [{"id":"alhmhfbeslmna7w","seqNum":13,"name":"0608100913.jpg","userSelectMode":0,"ossKeyRotateAngle":0,"ossFlattenKeyRotateAngle":0,"ossManualKeyRotateAngle":null,"smallRotateAngle":0,"ocrLanguage":null,"mode":2,"original":"https://changer-et.oss-cn-beijing.aliyuncs.com/EZUU9X5WUH7M9M6GU1Y1_20160608101019736_middle.jpg?Expires=1465981021&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=%2BflYIhk2pKjx3PQLyS6Tmq4mO2s%3D","originalOssKey":"EZUU9X5WUH7M9M6GU1Y1_20160608101019736_middle.jpg","middle":"https://changer-et.oss-cn-beijing.aliyuncs.com/EZUU9X5WUH7M9M6GU1Y1_20160608101019736_flatten_middle.jpg?Expires=1465981021&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=rU/B8AO7k1iEMULTqJu%2B/BOck%2BM%3D","middleOssKey":"EZUU9X5WUH7M9M6GU1Y1_20160608101019736_flatten_middle.jpg","small":"https://changer-et.oss-cn-beijing.aliyuncs.com/EZUU9X5WUH7M9M6GU1Y1_20160608101019736_flatten_small.jpg?Expires=1465981021&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=O/yQ2CfYE45DAeZkVOXaUaJLt38%3D","smallOssKey":"EZUU9X5WUH7M9M6GU1Y1_20160608101019736_flatten_small.jpg","flatten":"EZUU9X5WUH7M9M6GU1Y1_20160608101019736_flatten.jpg","whiteboard":null,"pixel":"4608 pixels x 3456 pixels","fileSizeUnit":"3.1 MB","make":"ET16","exposureTime":"1/30 sec","isoSpeedRatings":"200","takeOn":"2016-06-08 10:09:13","date":"20160608","localeDate":"2016-06-08 10:09:13"},{"id":"z6w4xi0kngedj9j","seqNum":12,"name":"0608100910.jpg","userSelectMode":0,"ossKeyRotateAngle":0,"ossFlattenKeyRotateAngle":0,"ossManualKeyRotateAngle":null,"smallRotateAngle":0,"ocrLanguage":null,"mode":2,"original":"https://changer-et.oss-cn-beijing.aliyuncs.com/EZUU9X5WUH7M9M6GU1Y1_20160608101010515_middle.jpg?Expires=1465981021&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=pcawvi7ZD1yK9LnPH%2B6yc5wGxkU%3D","originalOssKey":"EZUU9X5WUH7M9M6GU1Y1_20160608101010515_middle.jpg","middle":"https://changer-et.oss-cn-beijing.aliyuncs.com/EZUU9X5WUH7M9M6GU1Y1_20160608101010515_flatten_middle.jpg?Expires=1465981021&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=WAxJjikIL4tqheyV3nhFtw4x6ew%3D","middleOssKey":"EZUU9X5WUH7M9M6GU1Y1_20160608101010515_flatten_middle.jpg","small":"https://changer-et.oss-cn-beijing.aliyuncs.com/EZUU9X5WUH7M9M6GU1Y1_20160608101010515_flatten_small.jpg?Expires=1465981021&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=%2BBQE4DoW/ZpMNoKWnYkcmbX1SwM%3D","smallOssKey":"EZUU9X5WUH7M9M6GU1Y1_20160608101010515_flatten_small.jpg","flatten":"EZUU9X5WUH7M9M6GU1Y1_20160608101010515_flatten.jpg","whiteboard":null,"pixel":"4608 pixels x 3456 pixels","fileSizeUnit":"3.1 MB","make":"ET16","exposureTime":"1/30 sec","isoSpeedRatings":"200","takeOn":"2016-06-08 10:09:10","date":"20160608","localeDate":"2016-06-08 10:09:10"},{"id":"t7uacktrpqgl35b","seqNum":11,"name":"0608100909.jpg","userSelectMode":0,"ossKeyRotateAngle":0,"ossFlattenKeyRotateAngle":0,"ossManualKeyRotateAngle":null,"smallRotateAngle":0,"ocrLanguage":null,"mode":2,"original":"https://changer-et.oss-cn-beijing.aliyuncs.com/EZUU9X5WUH7M9M6GU1Y1_20160608101000500_middle.jpg?Expires=1465981021&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=uXn6SXhdENLbg5CxG1soW71gD1M%3D","originalOssKey":"EZUU9X5WUH7M9M6GU1Y1_20160608101000500_middle.jpg","middle":"https://changer-et.oss-cn-beijing.aliyuncs.com/EZUU9X5WUH7M9M6GU1Y1_20160608101000500_flatten_middle.jpg?Expires=1465981021&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=0NSxK4LrXNHxZ6FgBRvQ25QwSFE%3D","middleOssKey":"EZUU9X5WUH7M9M6GU1Y1_20160608101000500_flatten_middle.jpg","small":"https://changer-et.oss-cn-beijing.aliyuncs.com/EZUU9X5WUH7M9M6GU1Y1_20160608101000500_flatten_small.jpg?Expires=1465981021&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=TvoTwos9SfjEJAXQ3fF2hJ4Juak%3D","smallOssKey":"EZUU9X5WUH7M9M6GU1Y1_20160608101000500_flatten_small.jpg","flatten":"EZUU9X5WUH7M9M6GU1Y1_20160608101000500_flatten.jpg","whiteboard":null,"pixel":"4608 pixels x 3456 pixels","fileSizeUnit":"3.1 MB","make":"ET16","exposureTime":"1/30 sec","isoSpeedRatings":"200","takeOn":"2016-06-08 10:09:09","date":"20160608","localeDate":"2016-06-08 10:09:09"},{"id":"mc9q0sag27ngigv","seqNum":10,"name":"0608100906.jpg","userSelectMode":0,"ossKeyRotateAngle":0,"ossFlattenKeyRotateAngle":0,"ossManualKeyRotateAngle":null,"smallRotateAngle":0,"ocrLanguage":null,"mode":2,"original":"https://changer-et.oss-cn-beijing.aliyuncs.com/EZUU9X5WUH7M9M6GU1Y1_20160608100949769_middle.jpg?Expires=1465981021&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=5W8700GotXyU2dRvS1yVZCQDFKk%3D","originalOssKey":"EZUU9X5WUH7M9M6GU1Y1_20160608100949769_middle.jpg","middle":"https://changer-et.oss-cn-beijing.aliyuncs.com/EZUU9X5WUH7M9M6GU1Y1_20160608100949769_flatten_middle.jpg?Expires=1465981021&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=0u5bVlTeqH5GNh37KR%2BSrvN0Ais%3D","middleOssKey":"EZUU9X5WUH7M9M6GU1Y1_20160608100949769_flatten_middle.jpg","small":"https://changer-et.oss-cn-beijing.aliyuncs.com/EZUU9X5WUH7M9M6GU1Y1_20160608100949769_flatten_small.jpg?Expires=1465981021&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=tWxd1E0vBAW1k6rU4bswExFX4hM%3D","smallOssKey":"EZUU9X5WUH7M9M6GU1Y1_20160608100949769_flatten_small.jpg","flatten":"EZUU9X5WUH7M9M6GU1Y1_20160608100949769_flatten.jpg","whiteboard":null,"pixel":"4608 pixels x 3456 pixels","fileSizeUnit":"3.1 MB","make":"ET16","exposureTime":"1/30 sec","isoSpeedRatings":"200","takeOn":"2016-06-08 10:09:06","date":"20160608","localeDate":"2016-06-08 10:09:06"},{"id":"wr7w877ozneyk8e","seqNum":9,"name":"0608100904.jpg","userSelectMode":1,"ossKeyRotateAngle":0,"ossFlattenKeyRotateAngle":null,"ossManualKeyRotateAngle":0,"smallRotateAngle":0,"ocrLanguage":"ChinesePRC","mode":2,"original":"https://changer-et.oss-cn-beijing.aliyuncs.com/EZUU9X5WUH7M9M6GU1Y1_20160608100938883_middle.jpg?Expires=1465981021&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=FY75rb0GDtcsSS%2BhxHyKxN6YC0Q%3D","originalOssKey":"EZUU9X5WUH7M9M6GU1Y1_20160608100938883_middle.jpg","middle":"https://changer-et.oss-cn-beijing.aliyuncs.com/EZUU9X5WUH7M9M6GU1Y1_20160608100938883_manual_middle.jpg?Expires=1465981021&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=%2B8DODRuKugvFMVOKUgPkitE7QCc%3D","middleOssKey":"EZUU9X5WUH7M9M6GU1Y1_20160608100938883_manual_middle.jpg","small":"https://changer-et.oss-cn-beijing.aliyuncs.com/EZUU9X5WUH7M9M6GU1Y1_20160608100938883_manual_small.jpg?Expires=1465981021&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=uZe17fqPoRNikfwt1uQDATHB6DU%3D","smallOssKey":"EZUU9X5WUH7M9M6GU1Y1_20160608100938883_manual_small.jpg","flatten":null,"whiteboard":null,"pixel":"4608 pixels x 3456 pixels","fileSizeUnit":"3.1 MB","make":"ET16","exposureTime":"1/30 sec","isoSpeedRatings":"200","takeOn":"2016-06-08 10:09:04","date":"20160608","localeDate":"2016-06-08 10:09:04"},{"id":"ug24y8w008aw643","seqNum":8,"name":"0608100902.jpg","userSelectMode":0,"ossKeyRotateAngle":0,"ossFlattenKeyRotateAngle":0,"ossManualKeyRotateAngle":null,"smallRotateAngle":0,"ocrLanguage":null,"mode":2,"original":"https://changer-et.oss-cn-beijing.aliyuncs.com/EZUU9X5WUH7M9M6GU1Y1_20160608100928309_middle.jpg?Expires=1465981021&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=2GG7ob12FLGS7QFwgtr2rkhVAwM%3D","originalOssKey":"EZUU9X5WUH7M9M6GU1Y1_20160608100928309_middle.jpg","middle":"https://changer-et.oss-cn-beijing.aliyuncs.com/EZUU9X5WUH7M9M6GU1Y1_20160608100928309_flatten_middle.jpg?Expires=1465981021&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=TK5EL9YmQbpHTCPAqGKokYq9P0U%3D","middleOssKey":"EZUU9X5WUH7M9M6GU1Y1_20160608100928309_flatten_middle.jpg","small":"https://changer-et.oss-cn-beijing.aliyuncs.com/EZUU9X5WUH7M9M6GU1Y1_20160608100928309_flatten_small.jpg?Expires=1465981021&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=BTADmldZM87lA4OBRqDt7CotQ3Y%3D","smallOssKey":"EZUU9X5WUH7M9M6GU1Y1_20160608100928309_flatten_small.jpg","flatten":"EZUU9X5WUH7M9M6GU1Y1_20160608100928309_flatten.jpg","whiteboard":null,"pixel":"4608 pixels x 3456 pixels","fileSizeUnit":"3.1 MB","make":"ET16","exposureTime":"1/30 sec","isoSpeedRatings":"200","takeOn":"2016-06-08 10:09:02","date":"20160608","localeDate":"2016-06-08 10:09:02"},{"id":"mbrtoxl0xj19qlp","seqNum":7,"name":"0608100900.jpg","userSelectMode":0,"ossKeyRotateAngle":0,"ossFlattenKeyRotateAngle":0,"ossManualKeyRotateAngle":null,"smallRotateAngle":0,"ocrLanguage":null,"mode":2,"original":"https://changer-et.oss-cn-beijing.aliyuncs.com/EZUU9X5WUH7M9M6GU1Y1_20160608100906080_middle.jpg?Expires=1465981021&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=GWIqV3i/Hi2XNNiLLllJBGQ%2BjeM%3D","originalOssKey":"EZUU9X5WUH7M9M6GU1Y1_20160608100906080_middle.jpg","middle":"https://changer-et.oss-cn-beijing.aliyuncs.com/EZUU9X5WUH7M9M6GU1Y1_20160608100906080_flatten_middle.jpg?Expires=1465981021&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=2pv5WAUamP9XrCsA0pIby4WBBTU%3D","middleOssKey":"EZUU9X5WUH7M9M6GU1Y1_20160608100906080_flatten_middle.jpg","small":"https://changer-et.oss-cn-beijing.aliyuncs.com/EZUU9X5WUH7M9M6GU1Y1_20160608100906080_flatten_small.jpg?Expires=1465981021&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=XsvrLIPRlFN/tgIuP1HR2PUQo7I%3D","smallOssKey":"EZUU9X5WUH7M9M6GU1Y1_20160608100906080_flatten_small.jpg","flatten":"EZUU9X5WUH7M9M6GU1Y1_20160608100906080_flatten.jpg","whiteboard":null,"pixel":"4608 pixels x 3456 pixels","fileSizeUnit":"3.1 MB","make":"ET16","exposureTime":"1/30 sec","isoSpeedRatings":"400","takeOn":"2016-06-08 10:09:00","date":"20160608","localeDate":"2016-06-08 10:09:00"}]
     * offset : 4
     */

    private int offset;
    private List<FileListBean> fileList;

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public List<FileListBean> getFileList() {
        return fileList;
    }

    public void setFileList(List<FileListBean> fileList) {
        this.fileList = fileList;
    }

    public static class FileListBean implements Serializable {
        /**
         * id : alhmhfbeslmna7w
         * seqNum : 13
         * name : 0608100913.jpg
         * userSelectMode : 0
         * ossKeyRotateAngle : 0
         * ossFlattenKeyRotateAngle : 0
         * ossManualKeyRotateAngle : null
         * smallRotateAngle : 0
         * ocrLanguage : null
         * mode : 2
         * original : https://changer-et.oss-cn-beijing.aliyuncs.com/EZUU9X5WUH7M9M6GU1Y1_20160608101019736_middle.jpg?Expires=1465981021&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=%2BflYIhk2pKjx3PQLyS6Tmq4mO2s%3D
         * originalOssKey : EZUU9X5WUH7M9M6GU1Y1_20160608101019736_middle.jpg
         * middle : https://changer-et.oss-cn-beijing.aliyuncs.com/EZUU9X5WUH7M9M6GU1Y1_20160608101019736_flatten_middle.jpg?Expires=1465981021&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=rU/B8AO7k1iEMULTqJu%2B/BOck%2BM%3D
         * middleOssKey : EZUU9X5WUH7M9M6GU1Y1_20160608101019736_flatten_middle.jpg
         * small : https://changer-et.oss-cn-beijing.aliyuncs.com/EZUU9X5WUH7M9M6GU1Y1_20160608101019736_flatten_small.jpg?Expires=1465981021&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=O/yQ2CfYE45DAeZkVOXaUaJLt38%3D
         * smallOssKey : EZUU9X5WUH7M9M6GU1Y1_20160608101019736_flatten_small.jpg
         * flatten : EZUU9X5WUH7M9M6GU1Y1_20160608101019736_flatten.jpg
         * whiteboard : null
         * pixel : 4608 pixels x 3456 pixels
         * fileSizeUnit : 3.1 MB
         * make : ET16
         * exposureTime : 1/30 sec
         * isoSpeedRatings : 200
         * takeOn : 2016-06-08 10:09:13
         * date : 20160608
         * localeDate : 2016-06-08 10:09:13
         */

        private String id;
        private int seqNum;
        private String name;
        private String big;
        private String bigOssKey;
        private int userSelectMode;
        private int ossKeyRotateAngle;
        private int ossFlattenKeyRotateAngle;
        private Object ossManualKeyRotateAngle;
        private int smallRotateAngle;
        private Object ocrLanguage;
        private int mode;
        private int colorMode;
        private String original;
        private String originalOssKey;
        private String middle;
        private long flattenSize;
        private String middleOssKey;
        private String small;
        private String smallOssKey;
        private String flatten;
        private Object whiteboard;
        private String pixel;
        private String fileSizeUnit;
        private String make;
        private String exposureTime;
        private String isoSpeedRatings;
        private String takeOn;
        private String date;
        private String localeDate;

        public long getOriginalSize() {
            return originalSize;
        }

        public void setOriginalSize(long originalSize) {
            this.originalSize = originalSize;
        }

        private long  originalSize;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public int getSeqNum() {
            return seqNum;
        }

        public void setSeqNum(int seqNum) {
            this.seqNum = seqNum;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getUserSelectMode() {
            return userSelectMode;
        }

        public void setUserSelectMode(int userSelectMode) {
            this.userSelectMode = userSelectMode;
        }

        public int getOssKeyRotateAngle() {
            return ossKeyRotateAngle;
        }

        public void setOssKeyRotateAngle(int ossKeyRotateAngle) {
            this.ossKeyRotateAngle = ossKeyRotateAngle;
        }

        public int getOssFlattenKeyRotateAngle() {
            return ossFlattenKeyRotateAngle;
        }

        public void setOssFlattenKeyRotateAngle(int ossFlattenKeyRotateAngle) {
            this.ossFlattenKeyRotateAngle = ossFlattenKeyRotateAngle;
        }

        public Object getOssManualKeyRotateAngle() {
            return ossManualKeyRotateAngle;
        }

        public void setOssManualKeyRotateAngle(Object ossManualKeyRotateAngle) {
            this.ossManualKeyRotateAngle = ossManualKeyRotateAngle;
        }

        public int getSmallRotateAngle() {
            return smallRotateAngle;
        }

        public void setSmallRotateAngle(int smallRotateAngle) {
            this.smallRotateAngle = smallRotateAngle;
        }

        public Object getOcrLanguage() {
            return ocrLanguage;
        }

        public void setOcrLanguage(Object ocrLanguage) {
            this.ocrLanguage = ocrLanguage;
        }

        public int getMode() {
            return mode;
        }

        public void setMode(int mode) {
            this.mode = mode;
        }

        public String getOriginal() {
            return original;
        }

        public void setOriginal(String original) {
            this.original = original;
        }

        public String getOriginalOssKey() {
            return originalOssKey;
        }

        public void setOriginalOssKey(String originalOssKey) {
            this.originalOssKey = originalOssKey;
        }

        public String getMiddle() {
            return middle;
        }

        public void setMiddle(String middle) {
            this.middle = middle;
        }

        public String getMiddleOssKey() {
            return middleOssKey;
        }

        public void setMiddleOssKey(String middleOssKey) {
            this.middleOssKey = middleOssKey;
        }

        public String getSmall() {
            return small;
        }

        public void setSmall(String small) {
            this.small = small;
        }

        public String getSmallOssKey() {
            return smallOssKey;
        }

        public void setSmallOssKey(String smallOssKey) {
            this.smallOssKey = smallOssKey;
        }

        public String getFlatten() {
            return flatten;
        }

        public void setFlatten(String flatten) {
            this.flatten = flatten;
        }

        public Object getWhiteboard() {
            return whiteboard;
        }

        public void setWhiteboard(Object whiteboard) {
            this.whiteboard = whiteboard;
        }

        public String getPixel() {
            return pixel;
        }

        public void setPixel(String pixel) {
            this.pixel = pixel;
        }

        public String getFileSizeUnit() {
            return fileSizeUnit;
        }

        public void setFileSizeUnit(String fileSizeUnit) {
            this.fileSizeUnit = fileSizeUnit;
        }

        public String getMake() {
            return make;
        }

        public void setMake(String make) {
            this.make = make;
        }

        public String getExposureTime() {
            return exposureTime;
        }

        public void setExposureTime(String exposureTime) {
            this.exposureTime = exposureTime;
        }

        public String getIsoSpeedRatings() {
            return isoSpeedRatings;
        }

        public void setIsoSpeedRatings(String isoSpeedRatings) {
            this.isoSpeedRatings = isoSpeedRatings;
        }

        public String getTakeOn() {
            return takeOn;
        }

        public void setTakeOn(String takeOn) {
            this.takeOn = takeOn;
        }

        public String getDate() {
            return date;
        }

        public void setDate(String date) {
            this.date = date;
        }

        public String getLocaleDate() {
            return localeDate;
        }

        public void setLocaleDate(String localeDate) {
            this.localeDate = localeDate;
        }

        public String getBig() {
            return big;
        }

        public void setBig(String big) {
            this.big = big;
        }

        public String getBigOssKey() {
            return bigOssKey;
        }

        public void setBigOssKey(String bigOssKey) {
            this.bigOssKey = bigOssKey;
        }
        public long getFlattenSize() {
            return flattenSize;
        }

        public void setFlattenSize(long flattenSize) {
            this.flattenSize = flattenSize;
        }

        public int getColorMode() {
            return colorMode;
        }

        public void setColorMode(int colorMode) {
            this.colorMode = colorMode;
        }


    }
}
