package com.czur.cloud.ui.starry.network;

import android.app.Application;
import android.os.Handler;
import android.os.Looper;

import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.starry.meeting.network.core.interceptor.TokenInterceptor;

import java.lang.annotation.Annotation;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;

public class StarryHttpManager {

    public static final int STATUS_FAIL = 1001;
    public static final int STATUS_TIMEOUT = 1002;
    public static final int STATUS_NOT_MANAGER = 1003;
    public static final int STATUS_SUCCESS = 1000;
    public static final int STATUS_INVALID_USER_OR_PASSWORD = 1004;
    public static final int STATUS_USER_EXISTS = 1011;
    public static final int STATUS_INVALID_CODE = 1014;
    public static final int STATUS_INVALID_MOBILE = 1007;
    public static final int STATUS_INVALID_EMAIL = 1008;
    public static final int STATUS_5_TIME_IN_ONE_DAY = 1009;
    public static final int STATUS_CODE_1_MIN = 1010;
    public static final int STATUS_COND_1_MAIL = 1013;
    public static final int STATUS_FlATTEN_ERROR = 1015;
    public static final int STATUS_COLOR_ERROR = 1016;
    public static final int STATUS_ERROR = 1017;
    public static final int STATUS_NOT_USER = 1018;
    public static final int STATUS_TIME_OUT = 1019;
    public static final int IMG_NO_FOUND = 1020;
    public static final int STATUS_IMAGE_PROCESS_ERROR = 1015;
    public static final int STATUS_THIS_PEOPLE_IS_USING = 1022;
    public static final int STATUS_5_MIN_4_TIME = 1023;
    public static final int STATUS_DEVICE_NOT_EXIST = 1024;
    public static final int STATUS_DEVICE_NOT_SAME_AREA = 1025;
    public static final int STATUS_DEVICE_NOT_BIND = 1026;
    public static final int STATUS_NETWORK_FAIL = 1028;
    public static final int STATUS_EMAIL_BIND_OTHER_USER = 1035;
    public static final int STATUS_MOBILE_BIND_OTHER_USER = 1034;
    public static final int STATUS_CODE_EXPIRED = 1033;
    public static final int STATUS_OLD_PWD_FAIL = 1031;
    public static final int STATUS_PDF_RETURN_CODE = 1039;
    public static final int STATUS_NAME_IS_SAMED = 1073;
    public static final int STATUS_HAS_BINDED_THIRD_PARTY_BY_MOBILE = 1046;
    public static final int STATUS_HAS_NOT_BINDED_THIRD_PARTY_BY_MOBILE = 1047;
    public static final int STATUS_HAS_BINDED_THIRD_PARTY_BY_EMAIL = 1048;
    public static final int STATUS_HAS_NOT_BINDED_THIRD_PARTY_BY_EMAIL = 1049;
    public static final int STATUS_INVALID_THIRD_PARTY = 1050;
    public static final int STATUS_EQUIPMENT_IS_ACTIVED = 1051;
    public static final int STATUS_EQUIPMENT_HAS_ALREADY_REACTIVED = 1052;
    public static final int STATUS_BOOK_OCR_INVALID = 1053;
    public static final int STATUS_BOOK_INVALID = 1054;
    public static final int STATUS_BOOKS_HANDWRITING_NOTICED = 1055;
    public static final int STATUS_BOOKS_HANDWRITING_NOT_NOTICED = 1056;
    public static final int STATUS_BOOK_OCR_HAS_BEEN_USED = 1057;
    public static final int STATUS_THIRD_PARTY_TIME_OUT = 1058;
    public static final int STATUS_BOOK_CODE_INCORRECT = 1070;
    public static final int STATUS_SHARED_MEMBER_ADDED = 1080;
    public static final int STATUS_SHARED_MEMBER_MORE_THAN_THREE = 1081;
    public static final int STATUS_SINGLE_LOGIN = 1111;


    private static final int METHOD_NONE = 0;
    private static final int METHOD_GET = 1;
    private static final int METHOD_POST = 2;

    private Application application;
    private ExecutorService executor = Executors.newFixedThreadPool(5);
    private OkHttpClient client, clientNoRetry;
    private Handler handler = new Handler(Looper.getMainLooper());

    private StarryHttpManager() {
    }

    public static StarryHttpManager getInstance() {
        return SingletonHolder.instance;
    }

    private static class SingletonHolder {
        static final StarryHttpManager instance = new StarryHttpManager();
    }

    public void init(Application application) {
        this.application = application;
        client = new OkHttpClient.Builder()
                .connectTimeout(20, TimeUnit.SECONDS)
                .readTimeout(20, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .addInterceptor(new TokenInterceptor())     // Token过期后 刷新Token
//                .addInterceptor(new CommonParamInterceptor()) // 通用请求参数(请求头和必须参数)
//                .addInterceptor(new LoggingInterceptor())   // 输出Log
                .build();
        clientNoRetry = new OkHttpClient.Builder()
                .connectTimeout(20, TimeUnit.SECONDS)
                .readTimeout(20, TimeUnit.SECONDS)
                .retryOnConnectionFailure(false)
                .build();
    }

    public <T> T create(Class<T> service, String endpoint, boolean isNotPassport) {
        return (T) Proxy.newProxyInstance(service.getClassLoader(), new Class<?>[]{service}, new HttpHandler<T>(endpoint));
    }

    private class HttpHandler<T> implements InvocationHandler {

        private String endpoint;

        HttpHandler(String endpoint) {
            super();
            this.endpoint = endpoint;
        }

        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
            if (method.getDeclaringClass() == Object.class) {
                return method.invoke(this, args);
            }

            if (args == null || args.length == 0) {
                throw new Exception("方法参数为空或者规则不正确!");
            }

            Annotation[][] paramAnnotationArr = null;
            StarryHttpMethod miaoHttpMethod = new StarryHttpMethod();
            miaoHttpMethod.setIsAsync(args[args.length - 1] instanceof Callback);
            miaoHttpMethod.setNoParamCount(miaoHttpMethod.isAsync() ? 2 : 1);
            paramAnnotationArr = method.getParameterAnnotations();
            for (int i = 0; i < paramAnnotationArr.length - miaoHttpMethod.getNoParamCount(); i++) {
                Annotation tempAnnotation = paramAnnotationArr[i][0];
                if (tempAnnotation instanceof MiaoHttpParam) {
                    miaoHttpMethod.getParams().put(((MiaoHttpParam) tempAnnotation).value(), i + "");
                } else if (tempAnnotation instanceof MiaoHttpHeader) {
                    miaoHttpMethod.getHeaders().put(((MiaoHttpHeader) tempAnnotation).value(), i + "");
                } else if (tempAnnotation instanceof MiaoHttpPath) {
                    miaoHttpMethod.getPaths().put(((MiaoHttpPath) tempAnnotation).value(), i + "");
                }
            }
            Annotation[] annotations = method.getAnnotations();
            int witchMethod = METHOD_NONE;
            Class<?> clazz;
            MiaoHttpGet miaoGet = null;
            MiaoHttpPost miaoPost = null;
            boolean isRetry = true;
            for (Annotation annotation : annotations) {
                clazz = annotation.annotationType();
                if (clazz == MiaoHttpGet.class) {
                    witchMethod = METHOD_GET;
                    miaoGet = (MiaoHttpGet) annotation;
                } else if (clazz == MiaoHttpPost.class) {
                    witchMethod = METHOD_POST;
                    miaoPost = (MiaoHttpPost) annotation;
                } else if (clazz == MiaoRetry.class) {
                    isRetry = ((MiaoRetry) annotation).value();
                }
            }
            if (witchMethod == METHOD_NONE) {
                throw new Exception("方法上面说好的注解呢?MiaoGet或者MiaoPost什么的?");
            }

            switch (witchMethod) {
                case METHOD_GET:
                    miaoHttpMethod.setMethod(StarryHttpMethod.Method.Get);
                    miaoHttpMethod.setUrl(endpoint + miaoGet.value());
                    break;
                case METHOD_POST:
                    miaoHttpMethod.setMethod(StarryHttpMethod.Method.Post);
                    miaoHttpMethod.setUrl(endpoint + miaoPost.value());
                    break;
            }

            HashMap<String, String> postParam = null;
            ArrayList<String> logs = new ArrayList<>();
            logs.add("接口url:");
            logs.add(miaoHttpMethod.getUrl());
            logs.add("接口方法名称:");
            logs.add(method.getName());
            logs.add("接口参数:");
            String url = "";
            if (StarryHttpMethod.Method.Get.equals(miaoHttpMethod.getMethod())) {
                boolean isFirstParam = true;
                for (Map.Entry<String, String> entry : miaoHttpMethod.getParams().entrySet()) {
                    String value = (String) args[Integer.parseInt(entry.getValue())];
                    if (value == null) {
                        value = "";
                    }
                    logs.add(entry.getKey() + " --> " + value);
                    url += (isFirstParam ? "?" : "&") + entry.getKey() + "=" + value;
                    isFirstParam = false;
                }
            } else {
                postParam = new HashMap<>();
                for (Map.Entry<String, String> entry : miaoHttpMethod.getParams().entrySet()) {
                    String value = (String) args[Integer.parseInt(entry.getValue())];
                    if (value == null) {
                        value = "";
                    }
                    logs.add(entry.getKey() + " --> " + value);
                    postParam.put(entry.getKey(), value);
                }
            }
            String baseUrl = miaoHttpMethod.getUrl();
            for (Map.Entry<String, String> entry : miaoHttpMethod.getPaths().entrySet()) {
                String value = (String) args[Integer.parseInt(entry.getValue())];
                if (value == null) {
                    value = "";
                }
                baseUrl = baseUrl.replace("{" + entry.getKey() + "}", value);
            }
            miaoHttpMethod.setUrl(baseUrl + url);
            if (miaoHttpMethod.getHeaders().size() != 0) {
                for (Map.Entry<String, String> entry : miaoHttpMethod.getHeaders().entrySet()) {
                    String value = (String) args[Integer.parseInt(entry.getValue())];
                    if (value == null) {
                        value = "";
                    }
                    logs.add("header: " + entry.getKey() + " --> " + value);
                    miaoHttpMethod.getHeaders().put(entry.getKey(), value);
                }
            }
            UserPreferences userPreferences = UserPreferences.getInstance(application);
            logs.add("固定Header:");
            miaoHttpMethod.getHeaders().put("udid", userPreferences.getIMEI());
            logs.add("udid --> " + userPreferences.getIMEI());
            miaoHttpMethod.getHeaders().put("App-Key", CZURConstants.CLOUD_ANDROID);
            logs.add("App-Key --> " + CZURConstants.CLOUD_ANDROID);
            miaoHttpMethod.getHeaders().put("Api-Build", String.valueOf(AppUtils.getAppVersionCode()));
            logs.add("Api-Build --> " + AppUtils.getAppVersionCode());
            miaoHttpMethod.getHeaders().put("App-Bundle", AppUtils.getAppPackageName());
            logs.add("App-Bundle --> " + AppUtils.getAppPackageName());
            if (userPreferences.getUser() != null) {
                miaoHttpMethod.getHeaders().put("T-ID", userPreferences.getToken());
                logs.add("T-ID --> " + userPreferences.getToken());
                miaoHttpMethod.getHeaders().put("U-ID", userPreferences.getUserId());
                logs.add("U-ID --> " + userPreferences.getUserId());
                miaoHttpMethod.getHeaders().put("X-COUNTRY-CODE", userPreferences.getCountryCode());
                logs.add("X-COUNTRY-CODE --> " + userPreferences.getCountryCode());
            }

            if (miaoHttpMethod.isAsync()) {
                final Callback<T> callback = (Callback<T>) args[args.length - 1];
                if (!com.blankj.utilcode.util.NetworkUtils.isConnected()) {
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            ToastUtils.showShort(R.string.toast_no_connection_network);
                            if (callback instanceof CallbackNetwork) {
                                ((CallbackNetwork) callback).onNoNetwork();
                            }
                        }
                    });
                    return null;
                }
                Type type = (Type) args[args.length - 2];
                callback.onStart();
                if (StarryHttpMethod.Method.Get.equals(miaoHttpMethod.getMethod())) {
                    executor.execute(new AsyncHttpTaskStarry<>(application, miaoHttpMethod.getUrl(), type, callback, miaoHttpMethod.getHeaders(), logs, isRetry));
                } else {
                    executor.execute(new AsyncHttpTaskStarry<>(application, miaoHttpMethod.getUrl(), type, postParam, callback, miaoHttpMethod.getHeaders(), logs, isRetry));
                }
            } else {
                if (!com.blankj.utilcode.util.NetworkUtils.isConnected()) {
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            ToastUtils.showShort(R.string.toast_no_connection_network);
                        }
                    });
                    throw new NoNetworkException();
                }
                Type type = (Type) args[args.length - 1];
                if (StarryHttpMethod.Method.Get.equals(miaoHttpMethod.getMethod())) {
                    return SyncHttpTaskStarry.getInstance().syncGet(application, miaoHttpMethod.getUrl(), type, miaoHttpMethod.getHeaders(), logs, isRetry);
                } else {
                    return SyncHttpTaskStarry.getInstance().syncPost(application, miaoHttpMethod.getUrl(), type, postParam, miaoHttpMethod.getHeaders(), logs, isRetry);
                }
            }
            return null;
        }
    }

    public interface Callback<T> {
        void onStart();

        void onResponse(StarryHttpEntity<T> entity);

        void onFailure(StarryHttpEntity<T> entity);

        void onError(Exception e);
    }

    public interface CallbackNetwork<T> extends Callback<T> {
        void onNoNetwork();
    }

    public OkHttpClient getHttpClient() {
        return client;
    }

    OkHttpClient getHttpClientNoRetry() {
        return clientNoRetry;
    }

}
