package com.czur.cloud.network;

import com.czur.cloud.BuildConfig;
import com.czur.cloud.network.core.MiaoHttpManager;

public class HttpManager {

    private HttpManager() {
    }

    public static HttpManager getInstance() {
        return SingletonHolder.instance;
    }

    private static class SingletonHolder {
        protected static final HttpManager instance = new HttpManager();
    }

    public HttpServices request() {
        return MiaoHttpManager.getInstance().create(HttpServices.class, BuildConfig.BASE_URL, true);
    }
    public HttpServices requestFeedBack() {
        return MiaoHttpManager.getInstance().create(HttpServices.class, BuildConfig.FEEDBACK_URL, true);
    }
    public PassportServices requestPassport() {
        return MiaoHttpManager.getInstance().create(PassportServices.class, BuildConfig.PASSPORT_URL, false);
    }

    public HttpServices requestStarry() {
        return MiaoHttpManager.getInstance().create(HttpServices.class, BuildConfig.BASE_STARRY_URL, true);
    }

}
