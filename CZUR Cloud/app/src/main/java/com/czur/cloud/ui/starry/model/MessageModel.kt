package com.czur.cloud.ui.starry.model

import android.util.Log
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.NetworkUtils
import com.czur.cloud.BuildConfig
import com.czur.cloud.common.CZURConstants
import com.czur.cloud.network.core.MiaoHttpManager
import com.czur.cloud.preferences.UserPreferences
import com.czur.cloud.ui.starry.meeting.common.JSON
import com.czur.cloud.ui.starry.meeting.model.Model
import com.czur.cloud.ui.starry.meeting.model.StarryHttpResData
import com.czur.cloud.ui.starry.meeting.network.HttpManager
import com.czur.cloud.ui.starry.meeting.network.IMeetService
import com.czur.cloud.ui.starry.network.NoNetworkException
import com.czur.czurutils.log.logE
import com.czur.czurutils.log.logI
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.Request
import okhttp3.RequestBody

object MessageModel: Model() {
    private const val TAG = "MessageModel"

    private val netService: IMeetService by lazy { HttpManager.getService() }

    private val userPreferences by lazy {
        UserPreferences.getInstance()
    }

    override fun doClear() {
    }

    fun deleteByIds( syncJson: String ): Boolean {
        return if (!NetworkUtils.isConnected()) {
            false
        } else try {
            val requestBody = RequestBody.create(JSON, syncJson)
            val request: Request = Request.Builder()
                .header("udid", userPreferences.imei)
                .header("App-Key", CZURConstants.CLOUD_ANDROID)
                .header("Api-Build", AppUtils.getAppVersionCode().toString())
                .header("App-Bundle", AppUtils.getAppPackageName())
                .header("T-ID", userPreferences.token)
                .header("U-ID", userPreferences.userId)
                .header("Content-Type", "application/json")
                .header("X-COUNTRY-CODE", userPreferences.countryCode)
                .url(BuildConfig.BASE_STARRY_URL + "/notice/deleteByIds")
                .post(requestBody)
                .build()
            Log.i("MessageModel", "deleteByIds.request=${request}")

            val response = MiaoHttpManager.getInstance().httpClient.newCall(request).execute()
            val responseString = response.body!!.string()
            Log.i("MessageModel", "deleteByIds.responseString=$responseString")
            if (responseString.trim { it <= ' ' }
                    .startsWith("<html>") || responseString.trim { it <= ' ' }
                    .endsWith("</html>")) {
                return false
            }
            val turnsType = object : TypeToken<StarryHttpResData<Boolean>>() {}.type
            val model = Gson().fromJson<StarryHttpResData<Boolean>>(responseString, turnsType)
            // 请求成功
            if (response.isSuccessful) {
                val code = model.code
                if (code == MiaoHttpManager.STATUS_SUCCESS) {
                    logI("deleteByIds:${responseString}.成功!")
                    // model.data
                    true
                } else {
                    logI("deleteByIds:${responseString}.失败!")
                    false
                }
            } else {
                false
            }
        } catch (e: java.lang.Exception) {
            logE("deleteByIds.e=${e.toString()}")
            false
        }
    }


    suspend fun getNoticesList(pageNum: String, pageSize: String, accountNo:String): StarryNoticeData {
        val model = withContext(Dispatchers.IO) {
            netService.getNotices(pageNum, pageSize, accountNo).body ?: StarryNoticeData()
        }
        return model
    }

    suspend fun deleteNotices(id: String, accountNo:String): Boolean {
        val model = withContext(Dispatchers.IO) {
            val response = netService.deleteNotices(id, accountNo)
            if (response.isNetError) {
                throw NoNetworkException()
            }
            netService.deleteNotices(id, accountNo).body ?: false
        }
        return model
    }

    suspend fun updateNoticesStatus(id: String, status: String, accountNo:String): Boolean {
        val model = withContext(Dispatchers.IO) {
            netService.updateNoticesStatus(id, status, accountNo).body ?: false
        }
        return model
    }

    suspend fun getNotice(id: String, accountNo:String): Notice {
        val model = withContext(Dispatchers.IO) {
            netService.getNotice(id, accountNo).body
        }
        return model
    }

}