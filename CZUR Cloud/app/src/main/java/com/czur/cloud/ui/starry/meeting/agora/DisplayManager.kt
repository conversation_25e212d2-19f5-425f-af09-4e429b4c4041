package com.czur.cloud.ui.starry.meeting.agora

import android.Manifest
import android.app.Activity
import android.content.Context
import android.util.DisplayMetrics
import android.util.Log
import android.view.View
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.PermissionUtils
import com.czur.cloud.BuildConfig
import com.czur.cloud.R
import com.czur.cloud.event.EventType
import com.czur.cloud.event.StarryCommonEvent
import com.czur.cloud.preferences.StarryPreferences
import com.czur.cloud.ui.eshare.ESPermissionUtils
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.livedatabus.LiveDataBus
import com.czur.cloud.ui.starry.meeting.MeetingMainActivity
import com.czur.cloud.ui.starry.meeting.agora.AgoraManager.Companion.JOIN_CHANNEL_SUCCESS
import com.czur.cloud.ui.starry.meeting.base.CZURAtyManager
import com.czur.cloud.ui.starry.meeting.common.*
import com.czur.cloud.ui.starry.meeting.dialog.StarryCommonPopup
import com.czur.cloud.ui.starry.meeting.model.MeetingModel
import com.czur.cloud.ui.starry.meeting.model.ModelManager
import com.czur.cloud.ui.starry.meeting.screensharing.RefusedPermissionListener
import com.czur.cloud.ui.starry.utils.RomUtils
import com.czur.cloud.ui.starry.utils.inNeedRejoinMeetingList
import com.czur.czurutils.log.logE
import com.czur.czurutils.log.logI
import io.agora.rtc2.*
import io.agora.rtc2.Constants.*
import io.agora.rtc2.video.CameraCapturerConfiguration
import io.agora.rtc2.video.VideoCanvas
import io.agora.rtc2.video.VideoEncoderConfiguration
import io.agora.rtc2.video.VideoEncoderConfiguration.STANDARD_BITRATE
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import org.greenrobot.eventbus.EventBus
import kotlin.math.max

/**
 * Created by 陈丰尧 on 4/19/21
 */
private const val TAG = "DisplayManager"
const val STREAM_FALLBACK_OPTION_AUDIO_ONLY = 2
const val WARN_ADM_PLAYOUT_ABNORMAL_FREQUENCY = 1020
private const val DEFAULT_SHARE_FRAME_RATE = 15

class DisplayManager(
    val context: Context,
    val onRemoteUserOnLine: ((uid: String) -> Unit)?,   // 远端用户上线
    val onRemoteUserOffLine: ((uid: String) -> Unit)?   // 远端用户离线
) {
    companion object {
        const val STREAM_TYPE_VIDEO = 1             // 视频
        const val STREAM_TYPE_AUDIO = 1 shl 1   // 音频
        const val STREAM_TYPE_ALL = STREAM_TYPE_AUDIO or STREAM_TYPE_VIDEO
    }

    private val mainScope = MainScope()

//    private var mRtcEngine: RtcEngine? = null
    private var mRtcEngine: RtcEngineEx? = null

    private var joinListener: ((joinResult: Int) -> Unit)? = null

    private var displayModel = DisplayModel()
    private var displayMainModel = DisplayMainModel()
    private val volumeModel = VolumeModel()

    // 网络质量
    private val networkQualityModel = NetworkQualityModel()

    // 自己的上行网速
    private val selfNetworkTxQualityModel = NetworkQualityModel()
    private val selfNetworkRxQualityModel = NetworkQualityModel()

    fun getSelfNetQualityFlow(): Flow<Int> = networkQualityModel.selfQualityFlow

    // 远端最活跃用户
    private val activeSpeaker = MutableLiveData<String>()

    private var snapJob: Job? = null
    private val scope = CoroutineScope(Job())

    private var snapFlow = MutableSharedFlow<Long>()

    var requestPermissionClickTime: Long = 0

    private var refusedPermissionListener: RefusedPermissionListener? = null

    private val rtcConnection2 = RtcConnection()

    fun setRefusedPermissionListener(_refusedPermissionListener: RefusedPermissionListener){
        this.refusedPermissionListener = _refusedPermissionListener
    }


    private val mRtcEventHandler = object : IRtcEngineEventHandler() {

        override fun onPermissionError(permission: Int) {
            super.onPermissionError(permission)
            logI("DisplayManager.onPermissionError.permission=${permission}")

        }

        // 注册 onUserJoined 回调。
        // 远端用户成功加入频道时，会触发该回调。
        // 可以在该回调用调用 setupRemoteVideo 方法设置远端视图。
        override fun onUserJoined(uid: Int, elapsed: Int) {
            super.onUserJoined(uid, elapsed)
            logI("DisplayManager.00-onUserJoined.uid=${uid}")
            // 屏幕分享权限弹窗弹出,不予处理
            if (MeetingModel.isScreenSharePermissionDialg){
                return
            }

            mainScope.launch {
                // 需要将该操作发送到主线程中执行
                // 所有对displayModel的操作, 均在主线程中
                displayModel[uid.toString()] = null
                displayMainModel[uid.toString()] = null
                onRemoteUserOnLine?.invoke(uid.toString())
            }
        }

        override fun onUserOffline(uid: Int, reason: Int) {
            super.onUserOffline(uid, reason)
            logI("DisplayManager.00-onUserOffline.uid=${uid},销毁SurfaceView")
            onRemoteUserOffLine?.invoke(uid.toString())
            mainScope.launch {
                // 这里面有更新UI的操作, 将他放到主线程中
                displayModel.release(uid.toString())
                displayMainModel.release(uid.toString())
            }
        }

        override fun onJoinChannelSuccess(channel: String?, uid: Int, elapsed: Int) {
            super.onJoinChannelSuccess(channel, uid, elapsed)
            logI("DisplayManager.00-onJoinChannelSuccess.加入会议成功.uid=${uid},channel=${channel}")
            // 屏幕分享权限弹窗弹出,不予处理
            if (MeetingModel.isScreenSharePermissionDialg){
                return
            }
            displayModel[uid.toString()] = null
            displayMainModel[uid.toString()] = null
            joinListener?.invoke(JOIN_CHANNEL_SUCCESS)
        }

        // 用户音量提示回调
        override fun onAudioVolumeIndication(
            speakers: Array<out AudioVolumeInfo>?,
            totalVolume: Int
        ) {
            super.onAudioVolumeIndication(speakers, totalVolume)
//            logITAG, "onAudioVolumeIndication:speakers:${speakers.toString()}:totalVolume:${totalVolume}")
            speakers?.forEach {
                var uid = it.uid // 用户的UID信息(czurID) 自己的UID为0
                if (uid == 0) {
                    uid = UserHandler.czurId.toInt()
                }
                val volume = it.volume // 取值范围为[0-255]
//                Log.i(TAG, "音频更新:uid:${uid} - volume:${volume}")
                volumeModel[uid.toString()].postValue(volume)
            }
        }

        override fun onActiveSpeaker(uid: Int) {
            super.onActiveSpeaker(uid)
            logI("onActiveSpeaker:${uid}")
            mainScope.launch {
                activeSpeaker.postValue(uid.toString())
            }
        }

        // 网络信号--通话中每个用户的网络上下行 last mile 质量报告回调。
        // QUALITY_UNKNOWN(0)：质量未知
        //QUALITY_EXCELLENT(1)：质量极好
        //QUALITY_GOOD(2)：用户主观感觉和极好差不多，但码率可能略低于极好
        //===QUALITY_POOR(3)：用户主观感受有瑕疵但不影响沟通
        //===QUALITY_BAD(4)：勉强能沟通但不顺畅
        //===QUALITY_VBAD(5)：网络质量非常差，基本不能沟通
        //QUALITY_DOWN(6)：网络连接断开，完全无法沟通
        //QUALITY_DETECTING(8)：SDK 正在探测网络质量
        override fun onNetworkQuality(uid: Int, txQuality: Int, rxQuality: Int) {
            super.onNetworkQuality(uid, txQuality, rxQuality)
//            logI("DisplayManager.onNetworkQuality:uid:${uid},txQuality:${txQuality},rxQuality:${rxQuality},")
            var user_id = uid
            var quality = max(txQuality, rxQuality)
            if (user_id == 0) {
                user_id = UserHandler.czurId.toInt()
//                quality = txQuality
                scope.launch {
                    selfNetworkTxQualityModel[user_id.toString()].postValue(txQuality)
                    selfNetworkRxQualityModel[user_id.toString()].postValue(rxQuality)
                }
            }
//            Log.i(TAG, "onNetworkQuality:uid=${uid};txQuality=${txQuality},rxQuality=${rxQuality}")
            scope.launch {
                networkQualityModel[user_id.toString()].postValue(quality)
            }
        }

        override fun onError(err: Int) {
            super.onError(err)
            logI("DisplayManager.onError:${err}")
//            when (err) {
//                ERR_START_CAMERA -> Log.i(TAG, "摄像头未打开")
//            }

//            joinListener?.invoke(JOIN_CHANNEL_FAIL)
        }

        override fun onNetworkTypeChanged(type: Int) {
            super.onNetworkTypeChanged(type)
            logI("DisplayManager.onNetworkTypeChanged:${type}")
            //NETWORK_TYPE_DISCONNECTED(0)：网络连接已断开
            mainScope.launch {
                networkQualityModel.updateSelfNetQuality(type)
            }
        }

        // 网络连接状态已改变回调
        override fun onConnectionStateChanged(state: Int, reason: Int) {
            super.onConnectionStateChanged(state, reason)
            logI(
                "DisplayManager.onConnectionStateChanged.${
                    getConnectionStateChangedName(state, reason)
                }"
            )
            when (state) {
                // "CONNECTION_STATE_CONNECTED(3)：网络已连接" 重新检查一下长连接
                CONNECTION_STATE_CONNECTED -> {
                    LiveDataBus.get().with(StarryConstants.MEETING_LONG_CONNECTION_CHECK).value =
                        true
                }

                // DisplayManager.onConnectionStateChanged.state=5--CONNECTION_STATE_FAILED(5)：网络连接失败
                //,reson=4--CONNECTION_CHANGED_JOIN_FAILED(4)：加入频道失败
                CONNECTION_STATE_FAILED -> {
                    if (reason == CONNECTION_CHANGED_JOIN_FAILED) {
//                        AppClearUtils.startNettyStarryService()
                        LiveDataBus.get().with(StarryConstants.MEETING_AGORA_CONNECT_FAILED).value =
                            true
                    }

                    // CONNECTION_CHANGED_INVALID_TOKEN(8): 生成的 Token 无效-->会导致无法正确入会,导致没有视频流,全部显示小头像
                    // 解决办法:类似中兴手机,重新入会
                    if (reason == CONNECTION_CHANGED_INVALID_TOKEN) {
                        LiveDataBus.get().with(StarryConstants.MEETING_AGORA_CONNECT_INVALID_TOKEN).value =
                            true
                    }

                }

            }
        }

        override fun onApiCallExecuted(error: Int, api: String?, result: String?) {
            super.onApiCallExecuted(error, api, result)
//            logI("DisplayManager.onApiCallExecuted.error=${error},api=${api},result=${result}")
        }

//        // 输入在线媒体流状态回调
//        override fun onStreamInjectedStatus(url: String?, uid: Int, status: Int) {
//            super.onStreamInjectedStatus(url, uid, status)
//            logI("DisplayManager.onApiCallExecuted.uid=${uid},status=${getStreamInjectedStatus(status)}")
//        }

        // 重新加入频道回调
        override fun onRejoinChannelSuccess(channel: String?, uid: Int, elapsed: Int) {
            super.onRejoinChannelSuccess(channel, uid, elapsed)
//            // 重新加入频道成功回调
            EventBus.getDefault().post(StarryCommonEvent(EventType.STARRY_REJOIN_SUCCESS, ""))
            logI("DisplayManager.onRejoinChannelSuccess.channel=${channel}, uid=${uid}, elapsed=${elapsed}")
        }

        // 远端音频状态发生改变回调
        override fun onRemoteAudioStateChanged(uid: Int, state: Int, reason: Int, elapsed: Int) {
            super.onRemoteAudioStateChanged(uid, state, reason, elapsed)
//            logI("DisplayManager.onRemoteAudioStateChanged.uid=${uid}, elapsed=${elapsed}, ${getRemoteAudioStateChanged(state, reason)}")
        }

        // 远端用户视频状态发生改变回调
        override fun onRemoteVideoStateChanged(uid: Int, state: Int, reason: Int, elapsed: Int) {
            super.onRemoteVideoStateChanged(uid, state, reason, elapsed)
//            logI("DisplayManager.onRemoteVideoStateChanged.uid=${uid}, elapsed=${elapsed}, ${getRemoteVideoStateChanged(state, reason)}")
        }

        // 本地音频状态发生改变回调。
        override fun onLocalAudioStateChanged(state: Int, error: Int) {
            super.onLocalAudioStateChanged(state, error)
            logI(
                "DisplayManager.onLocalAudioStateChanged=${
                    getLocalAudioStateChanged(state, error)
                }"
            )
            if (state == 3) {
                if (error > 0) {
                    // 通知app，重新进入会议
//                    LiveDataBus.get().with(STARRY_LOCAL_AUDIO_STREAM_STATE_FAILED).value = true
                }
            }
        }

        // 本地视频状态发生改变回调。
        // source:区分camera和screenshare
        override fun onLocalVideoStateChanged(source: VideoSourceType, state: Int, error: Int) {
            super.onLocalVideoStateChanged(source, state, error)
            logI(
                "DisplayManager.onLocalVideoStateChanged=source=${source},${
                    getLocalVideoStateChanged(state, error)
                }"
            )

            // LOCAL_VIDEO_STREAM_STATE_FAILED(3)：本地视频启动失败

            // LOCAL_VIDEO_STREAM_ERROR_OK(0)：本地视频状态正常
            //LOCAL_VIDEO_STREAM_ERROR_FAILURE(1)：出错原因不明确
            //LOCAL_VIDEO_STREAM_ERROR_DEVICE_NO_PERMISSION(2)：没有权限启动本地视频采集设备。请提示用户开启权限再重新加入频道。
            //LOCAL_VIDEO_STREAM_ERROR_DEVICE_BUSY(3)：本地视频采集设备正在使用中。请检查摄像头是否被其他应用占用，或者尝试重新加入频道。
            //LOCAL_VIDEO_STREAM_ERROR_CAPTURE_FAILURE(4)：本地视频采集失败。 请检查视频采集设备是否正常工作，检查摄像头是否被其他应用占用，或者尝试重新加入频道。
            //LOCAL_VIDEO_STREAM_ERROR_ENCODE_FAILURE(5)：本地视频编码失败。
            //LOCAL_VIDEO_STREAM_ERROR_DEVICE_NOT_FOUND(8)：找不到本地视频采集设备。请检查摄像头是否与设备连接正常，检查摄像头是否正常工作，或者尝试重新加入频道。
            if (source == VideoSourceType.VIDEO_SOURCE_SCREEN_PRIMARY) {
                if (state == LOCAL_VIDEO_STREAM_STATE_ENCODING) {
                    if (error == ERR_OK) {
                        Log.i(
                            TAG,
                            "Screen sharing start successfully."
                        )
                        logI( "onLocalVideoStateChanged state=LOCAL_VIDEO_STREAM_STATE_ENCODING, error=ERR_OK")
                        refusedPermissionListener?.onAllowPermission()
                    }
                } else if (state == LOCAL_VIDEO_STREAM_STATE_FAILED) {
                    Log.i(
                        TAG,
                        "Screen sharing start failed for error $error"
                    )
                    logE("onLocalVideoStateChanged state=LOCAL_VIDEO_STREAM_STATE_FAILED, error=$error")

                    refusedPermissionListener?.onRefusedPermission()
                }
            }else{
                if ( state == 3) {
                    if (error > 0) {
                        // 通知app，重新进入会议
                        LiveDataBus.get()
                            .with(StarryConstants.STARRY_LOCAL_VIDEO_STREAM_STATE_FAILED).value = true
                    }
                }
            }

        }

        // 当前通话统计回调。 该回调在通话中每两秒触发一次。
        override fun onRtcStats(stats: RtcStats?) {
            super.onRtcStats(stats)
//            if (stats != null){
//                val user_id = UserHandler.czurId
//                val txQuality = stats?.txKBitRate
//                val rxQuality = stats?.rxKBitRate
//                selfNetworkTxQualityModel[user_id.toString()].postValue(txQuality)
//                selfNetworkRxQualityModel[user_id.toString()].postValue(rxQuality)
////                logI("DisplayManager.onRtcStats.stats=${stats}, txKBitRate=${txQuality}, rxKBitRate=${rxQuality}")
//            }
        }

        // RTMP/RTMPS 推流状态发生改变回调
        override fun onRtmpStreamingStateChanged(url: String?, state: Int, errCode: Int) {
            super.onRtmpStreamingStateChanged(url, state, errCode)
//            logI("DisplayManager.onRtmpStreamingStateChanged=${getRtmpStreamingStateChanged(state, errCode)}")
        }

        // 接收对方数据流消息发生错误的回调。
        override fun onStreamMessageError(
            uid: Int,
            streamId: Int,
            error: Int,
            missed: Int,
            cached: Int
        ) {
            super.onStreamMessageError(uid, streamId, error, missed, cached)
            logI("DisplayManager.onStreamMessageError.uid=${uid}, streamId=${streamId}, error=${error}, missed=${missed}, cached=${cached}")
        }

        override fun onFirstRemoteVideoDecoded(uid: Int, width: Int, height: Int, elapsed: Int) {
            super.onFirstRemoteVideoDecoded(uid, width, height, elapsed)
            mainScope.launch {
                snapFlow.emit(System.currentTimeMillis())
            }
        }

        override fun onFirstLocalVideoFramePublished(source: VideoSourceType?, elapsed: Int) {
            super.onFirstLocalVideoFramePublished(source, elapsed)
            mainScope.launch {
                snapFlow.emit(System.currentTimeMillis())
            }
        }

        override fun onWarning(warn: Int) {
            super.onWarning(warn)

            if (warn == WARN_ADM_PLAYOUT_ABNORMAL_FREQUENCY) {//解决中兴说话听不到的问题
                //https://docs.agora.io/cn/Interactive%20Broadcast/API%20Reference/java/classio_1_1agora_1_1rtc_1_1_constants.html?platform=Android#a67dfe3691ed974e46f6f37cb696b01b3
                if (inNeedRejoinMeetingList) {
//                    val map = HashMap<String, String>()
//                    map["zhongxingAudioError"] = "zhongxingAudioError"
//                    MobclickAgent.onEvent(context, "zhongxingAudioError", map)

                    logI("中兴_WARN_ADM_PLAYOUT_ABNORMAL_FREQUENCY_1020")
                    EventBus.getDefault().post(
                        StarryCommonEvent(
                            EventType.STARRY_PLAYOUT_WARN, ""
                        )
                    )
                }
            }
        }

    }

    // ScreenSharing
    fun startScreenCapture(token: String,
                           channelName: String,
                           shareUid: Int){

        logI("$TAG.startScreenCapture()")

        val metrics = DisplayMetrics()
        CZURAtyManager.currentActivity().windowManager?.defaultDisplay?.getRealMetrics(metrics)

        val screenCaptureParameters = ScreenCaptureParameters()
        screenCaptureParameters.apply {
            captureVideo = true
            captureAudio = ModelManager.membersModel.selfAudioInUse
//            videoCaptureParameters.width = metrics.widthPixels
//            videoCaptureParameters.height = metrics.heightPixels
            videoCaptureParameters.width = 720
            videoCaptureParameters.height =
                (720 * 1.0f / metrics.widthPixels * metrics.heightPixels).toInt()
            videoCaptureParameters.framerate = DEFAULT_SHARE_FRAME_RATE
        }
        mRtcEngine?.startScreenCapture(screenCaptureParameters)
        mRtcEngine?.setParameters("{\"che.audio.restartWhenInterrupted\": true}");
        val options = ChannelMediaOptions()
        options.apply {
            clientRoleType = Constants.CLIENT_ROLE_BROADCASTER
            autoSubscribeAudio = true
            autoSubscribeVideo = true
            publishCameraTrack = false
            publishMicrophoneTrack = false
            publishScreenCaptureVideo = true
            publishScreenCaptureAudio = ModelManager.membersModel.selfAudioInUse
            // 你在共享uid的MediaOptions中把enableAudioRecordingOrPlayout这个参数设为false就不会录制麦克风声音了
            // 取消分享的回声
            enableAudioRecordingOrPlayout = false
        }
        rtcConnection2.apply {
            channelId = channelName
            localUid = shareUid
        }

        mRtcEngine?.joinChannelEx(token,
            rtcConnection2,
            options,
            mRtcEventHandler
        )

    }


    fun stopScreenCaption() {
        logI("${TAG}.stopScreenCaption()")

        try {
//            val engine: RtcEngineEx = mRtcEngine as RtcEngineEx
            mRtcEngine?.apply {
                leaveChannelEx(rtcConnection2)
                stopScreenCapture()
            }
        }catch (e: Exception){
            logE("stopScreenCaption.e=${e.toString()}")
        }
    }

    /**
     * 初始化方法
     */
    fun init() {
        logI("DisplayManager.01-init")
        initializeAgoraEngine()
        mRtcEngine?.let {
            config(it)
        }
    }

    /**
     * 加入会议室(视频)
     */
    fun joinChannel(
        rtcToken: String,
        room: String,
        czurID: Int,
        joinListener: (joinResult: Int) -> Unit
    ) {
        this.joinListener = joinListener

        val option = ChannelMediaOptions()
        option.autoSubscribeAudio = true
        option.autoSubscribeVideo = true
        option.publishCameraTrack = true
        option.publishMicrophoneTrack = true
//        option.publishScreenCaptureVideo = false

        mRtcEngine?.joinChannel(
            rtcToken,
            room,
            czurID,
            option
        )

        mRtcEngine?.startPreview()

        logI("mRtcEngine?.joinChannel.room=${room},czurID=${czurID},rtcToken=${rtcToken}")

//        // 针对滑动屏幕时，黑屏问题，使用截图方式临时解决，放置10秒前的截图来缓解该问题；
//        mainScope.launch {
//            // 两次截图的时间间隔不能小于1秒
////            snapFlow.sample(1000L).collect {
////                displayModel.screenshots()
////            }
//        }
//
//        // 统一截图协程
//        snapJob = mainScope.launch {
//            while (isActive) {
//                delay(10 * 1000L)
//                snapFlow.emit(System.currentTimeMillis())
//            }
//        }

    }

    // 切换摄像头
    fun changeLocalCarmea() {
        mRtcEngine?.switchCamera()
    }

    // mic静音开关
    fun muteLocalAudio(flag: Boolean) {
        mRtcEngine?.muteLocalAudioStream(!flag)
//        mRtcEngine?.enableLocalAudio(flag)
    }

    // cam开关
    fun muteLocalVideo(flag: Boolean) {
        mRtcEngine?.muteLocalVideoStream(!flag)
//        mRtcEngine?.enableLocalVideo(flag)
    }

    fun notMuteVideoOrAudio(stream: StreamType, flag: Boolean) {
        if (PermissionUtils.isGranted(
                Manifest.permission.RECORD_AUDIO,
                Manifest.permission.CAMERA
            )
        ) {//权限开启状态
            mRtcEngine?.setClientRole(CLIENT_ROLE_BROADCASTER)

            if (stream == StreamType.VIDEO) {
                muteLocalVideo(flag)
                muteLocalAudio(StarryPreferences.getInstance().lastMic)
                StarryPreferences.getInstance().targetCamStatus = CAM_AUDIO_STATUS_NO
            } else
                if (stream == StreamType.AUDIO) {
                    muteLocalAudio(flag)
                    muteLocalVideo(StarryPreferences.getInstance().lastCam)
                    StarryPreferences.getInstance().targetMicStatus = CAM_AUDIO_STATUS_NO
                }


        } else {
            mRtcEngine?.setClientRole(CLIENT_ROLE_AUDIENCE)

            if (stream == StreamType.VIDEO) {
                StarryPreferences.getInstance().targetCamStatus = CAM_AUDIO_STATUS_OPEN
            } else
                if (stream == StreamType.AUDIO) {
                    StarryPreferences.getInstance().targetMicStatus = CAM_AUDIO_STATUS_OPEN
                }

            ModelManager.membersModel.selfVideoInUseLive.value = false
            ModelManager.membersModel.selfAudioInUseLive.value = false
            checkPermissionWithDialog {
                if (it) {//点击去设置
                    useToolsRequestPermission(stream)
                } else {//点击取消
                    if (stream == StreamType.VIDEO) {
                        StarryPreferences.getInstance().targetCamStatus = CAM_AUDIO_STATUS_CLOSE
                    } else
                        if (stream == StreamType.AUDIO) {
                            StarryPreferences.getInstance().targetMicStatus = CAM_AUDIO_STATUS_CLOSE
                        }
                }
            }
        }

    }


    var checkPermissionDialog: StarryCommonPopup? = null

    /**
     * 在Aty栈中寻找MeetingMainActivity
     * 从最近会议页面进入时,由于小米12的bug(存疑), 导致MeetingMainActivity,并不是在 ActivityUtils内部维护的"栈顶"
     * 所以循环去寻找对应的Activity
     */
    private suspend fun getMeetingActivity(): Context? {
        for (i in 0..4) { // 寻找5次, 其实1次就找到了
            ActivityUtils.getActivityList().forEach { aty ->
                if (aty::class.java.simpleName == MeetingMainActivity::class.java.simpleName) {
                    return aty
                }
            }
            delay(200)
        }
        return null
    }

    /**
     * true 点击去设置
     * false 点击取消
     */
    private fun checkPermissionWithDialog(block: (Boolean) -> Unit) {
        scope.launch(Dispatchers.Main) {
            if (checkPermissionDialog?.isShowing == true) {
                return@launch
            }

            // 没找到对应的Activity, 直接退出, 不走这个逻辑了
            val topActivity = getMeetingActivity() ?: return@launch
            checkPermissionDialog = StarryCommonPopup.Builder(topActivity)
                .setTitle(topActivity.getString(R.string.starry_popupwindow_title))
                .setMessage(
                    topActivity.getString(R.string.starry_alert_need_power_phone)
                )
                .setPositiveTitle(
                    topActivity.getString(R.string.starry_go_open_permission)
                )
                .setNegativeTitle(
                    topActivity
                        .getString(R.string.starry_background_start_msg_cancel)
                )
                .setOnPositiveListener { dialog, _ ->
                    dialog.dismiss()
                    block.invoke(true)
                }
                .setOnNegativeListener { dialog, _ ->
                    dialog.dismiss()
                    block.invoke(false)
                }
                .create().apply {
                    show()
                }
        }
    }

    private fun useToolsRequestPermission(stream: StreamType) {
        requestPermissionClickTime = System.currentTimeMillis()
        val isRefuseSecondPermission = booleanArrayOf(false) //当时点击了永久拒绝
        PermissionUtils.permission(
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.CAMERA
        )
            .rationale { activity, shouldRequest -> //解释后是否需要继续弹出请求权限弹窗
                shouldRequest.again(true)
                isRefuseSecondPermission[0] = true
            }
            .callback(object : PermissionUtils.FullCallback {
                override fun onGranted(granted: List<String>) {
                    //权限通过
                    if (checkPermissionDialog != null && checkPermissionDialog!!.isShowing()) {
                        checkPermissionDialog!!.dismiss()
                    }

                    if (stream == StreamType.VIDEO) {
                        StarryPreferences.getInstance().targetCamStatus = CAM_AUDIO_STATUS_OPEN
                    } else
                        if (stream == StreamType.AUDIO) {
                            StarryPreferences.getInstance().targetMicStatus = CAM_AUDIO_STATUS_OPEN
                        }


                    EventBus.getDefault()
                        .post(StarryCommonEvent(EventType.STARRY_CHANGE_CAMARA_MIC_STATUS, ""))

                }

                override fun onDenied(deniedForever: List<String>, denied: List<String>) {
                    //权限拒绝
                    if (deniedForever.size > 0) { //永久拒绝
                        if (isRefuseSecondPermission[0]) {
                            //当时点击永久拒绝的时候不做处理
                        } else {
                            if (System.currentTimeMillis() - requestPermissionClickTime < 500) {//500ms之内 主观认为是系统返回,而非用户点击
                                scope.launch(Dispatchers.Main) {
                                    val aty = getMeetingActivity() as Activity

                                    if (stream == StreamType.VIDEO) {
                                        StarryPreferences.getInstance().targetCamStatus =
                                            CAM_AUDIO_STATUS_OPEN
                                    } else
                                        if (stream == StreamType.AUDIO) {
                                            StarryPreferences.getInstance().targetMicStatus =
                                                CAM_AUDIO_STATUS_OPEN
                                        }
                                    RomUtils.PermissionPageManagement.goToSetting(getMeetingActivity() as Activity)
                                }
                            }
                        }
                    } else if (denied.size > 0) {
                        //第一次拒绝
                    }
                    EventBus.getDefault()
                        .post(StarryCommonEvent(EventType.STARRY_CHANGE_CAMARA_MIC_STATUS, ""))

                }
            })
            .explain { activity, denied, shouldRequest -> //第一次拒绝过了, 现在进行第二次权限弹窗,提示你需要弹出解释窗了
                shouldRequest.start(true)
            }.request()
    }


    /**
     * 改变 音/视频 流的状态到 [mute],
     * @param streamType: 要操作的流 视频: [STREAM_TYPE_VIDEO],音频: [STREAM_TYPE_AUDIO],全部: [STREAM_TYPE_ALL]
     * @param mute:        是否要静音, true: 是, false: 取消mute
     * @param excludeUids: 在此数组中的uid状态与 [mute]的状态相反
     */
    fun muteExclude(streamType: Int, mute: Boolean = true, vararg excludeUids: String = arrayOf()) {
        displayModel
            .forEach { (uid, _) ->
                if (uid in excludeUids) {
                    mute(streamType, !mute, uid)
                } else {
                    mute(streamType, mute, uid)
                }
            }

        displayMainModel
            .forEach { (uid, _) ->
                if (uid in excludeUids) {
                    mute(streamType, !mute, uid)
                } else {
                    mute(streamType, mute, uid)
                }
            }

    }

    private fun mute(streamType: Int, mute: Boolean = true, uid: String) {
        if (uid == UserHandler.czurId.toString()) {
            muteLocal(streamType, mute)
        } else {
            muteRemote(uid, streamType, mute)
        }
    }

    /**
     * mute自己的流
     */
    private fun muteLocal(streamType: Int, mute: Boolean = true) {
        mRtcEngine?.let {
            if (streamType and STREAM_TYPE_VIDEO != 0) {
                it.muteLocalVideoStream(mute)
            }
            if (streamType and STREAM_TYPE_AUDIO != 0) {
                it.muteLocalAudioStream(mute)
            }
        }
    }

    /**
     * mute远程的流
     */
    private fun muteRemote(uid: String, streamType: Int, mute: Boolean = true) {
        mRtcEngine?.let {
            if (streamType and STREAM_TYPE_VIDEO != 0) {
                it.muteRemoteVideoStream(uid.toInt(), mute)
            }
            if (streamType and STREAM_TYPE_AUDIO != 0) {
                it.muteRemoteAudioStream(uid.toInt(), mute)
            }
        }
    }

    /**
     * 获取对应的视图
     */
    fun getSurface(uid: String): View? {
        return displayModel.getOrPut(uid) {
            createSurface(uid)
        }
    }

    fun getNewSurface(uid: String): View? {
        return displayMainModel.getOrPut(uid) {
            createSurface(uid)
        }
    }

    /**
     * 获取对应的视图
     */
    fun getTextTrue(uid: String): View? {
        return displayModel.getOrPut(uid) {
            createSurface(uid, 1)
        }
    }

    fun getNewTextTrue(uid: String): View? {
        return displayMainModel.getOrPut(uid) {
            createSurface(uid, 1)
        }
    }

    /**
     * 获取volume信息的LiveData
     */
    fun getVolumeLiveData(uid: String): LiveData<Int> = volumeModel[uid]

    // 获取网络质量
    fun getNetworkQualityLiveData(uid: String): LiveData<Int> = networkQualityModel[uid]
    fun getSelfNetworkTxQualityLiveData(uid: String): LiveData<Int> = selfNetworkTxQualityModel[uid]
    fun getSelfNetworkRxQualityLiveData(uid: String): LiveData<Int> = selfNetworkRxQualityModel[uid]

    fun getActiveSpeaker(): LiveData<String> = activeSpeaker

    /**
     * 创建主视图的Surface
     * type: 0创建 surfaceview  1创建texttureview
     */
    private fun createSurface(uid: String, type: Int = 0): View? {
//        logI("DisplayManager.createSurface.创建Surface: uid: $uid , Thread:${Thread.currentThread().name}")
        if (mRtcEngine == null) {
            return null
        }
        lateinit var view: View
        view = RtcEngine.CreateTextureView(context)

        // 设置视图
        if (uid == UserHandler.czurId.toString()) {
            Log.i(TAG, "设置本地视图")
            // RENDER_MODE_FIT: 等比放大视频直至视频窗口的一边与边框对齐。由于长宽比不一致所造成的未被填满的区域将被涂黑
            mRtcEngine?.setupLocalVideo(
                VideoCanvas(
                    view,
//                    VideoCanvas.RENDER_MODE_HIDDEN,
                    VideoCanvas.RENDER_MODE_FIT,
                    uid.toInt()
                )
            )
        } else {
            Log.i(TAG, "设置远端视图")
            // 设置订阅的视频流类型。
//            if (uid == MeetingModel.curMainId ||
//                uid == MeetingModel.curSmallId
//            ) {
//                mRtcEngine?.setRemoteVideoStreamType(uid.toInt(), VIDEO_STREAM_HIGH)
//            } else {
//                mRtcEngine?.setRemoteVideoStreamType(uid.toInt(), VIDEO_STREAM_LOW)
//            }
            mRtcEngine?.setupRemoteVideo(
                VideoCanvas(
                    view,
//                    VideoCanvas.RENDER_MODE_HIDDEN,
                    VideoCanvas.RENDER_MODE_FIT,
                    uid.toInt()
                )
            )
        }
        return view
    }


    // 重新设置视图显示
    fun setupViewForVideo(view: View, uid: String) {
//        logI("DisplayManager.setupViewForVideo.uid: $uid , Thread:${Thread.currentThread().name}, view=${view}")
        if (mRtcEngine == null) {
            return
        }
        if (uid == UserHandler.czurId.toString()) {
            mRtcEngine?.setupLocalVideo(
                VideoCanvas(
                    view,
//                    VideoCanvas.RENDER_MODE_HIDDEN,
                    VideoCanvas.RENDER_MODE_FIT,
                    uid.toInt()
                )
            )
        } else {
            // 设置订阅的视频流类型。
            mRtcEngine?.setupRemoteVideo(
                VideoCanvas(
                    view,
                    VideoCanvas.RENDER_MODE_FIT,
                    uid.toInt()
                )
            )
        }

    }


    /**
     *调用 Agora SDK 的方法初始化 RtcEngine。
     */
    private fun initializeAgoraEngine() {
        logI("DisplayManager.02-initializeAgoraEngine")

        try {
            mRtcEngine =
                RtcEngine.create(
                    context,
                    BuildConfig.AGORA_STARRY_APP_ID,
                    mRtcEventHandler) as RtcEngineEx

//            val config = RtcEngineConfig().apply {
//                mContext = context
//                mAppId = BuildConfig.AGORA_STARRY_APP_ID
//                mChannelProfile = CHANNEL_PROFILE_LIVE_BROADCASTING
//                mEventHandler = mRtcEventHandler
//                mAudioScenario = AudioScenario.getValue(AudioScenario.DEFAULT)
//                mAreaCode = RtcEngineConfig.AreaCode.AREA_CODE_CN
//            }
//            mRtcEngine = RtcEngine.create(config) as RtcEngineEx

            mRtcEngine?.setParameters("{"
                        + "\"rtc.report_app_scenario\":"
                        + "{"
                        + "\"appScenario\":" + 100 + ","
                        + "\"serviceType\":" + 11 + ","
                        + "\"appVersion\":\"" + RtcEngine.getSdkVersion() + "\""
                        + "}"
                        + "}")

            // 4.x的ai降噪可通过私参开启哈：
            mRtcEngine?.setParameters("{\"che.audio.ains_mode\":2}")    //开启ai降噪
            // 降噪强度参数按如下值设置：
            mRtcEngine?.setParameters("{\"che.audio.nsng.lowerBound\":80}")
            mRtcEngine?.setParameters("{\"che.audio.nsng.lowerMask\":50}")
            mRtcEngine?.setParameters("{\"che.audio.nsng.statisticalbound\":5}")
            mRtcEngine?.setParameters("{\"che.audio.nsng.finallowermask\":30}")
            mRtcEngine?.setParameters("{\"che.audio.nsng.enhfactorstastical\":200}")

        } catch (e: Exception) {
            Log.i(TAG, "初始化失败", e)
            throw RuntimeException(
                "NEED TO check rtc sdk init fatal error\n" + Log.getStackTraceString(
                    e
                )
            )
        }
    }

    /**
     * 配置视频sdk
     */
    private fun config(rtcEngine: RtcEngine) {
        rtcEngine.setChannelProfile(CHANNEL_PROFILE_LIVE_BROADCASTING)
        if (!ESPermissionUtils.checkVideoAndAudioPermission()) {//观众
            mRtcEngine?.setClientRole(CLIENT_ROLE_AUDIENCE)
        } else {//主播
            mRtcEngine?.setClientRole(CLIENT_ROLE_BROADCASTER)
        }
        logI("DisplayManager.03-config")

//        rtcEngine.enableWebSdkInteroperability(true)
//        rtcEngine.setLogFilter(Constants.LOG_FILTER_DEBUG)
//        rtcEngine.setClientRole(Constants.CLIENT_ROLE_BROADCASTER)
//        rtcEngine.setChannelProfile(Constants.CHANNEL_PROFILE_LIVE_BROADCASTING)

//        rtcEngine.setVideoEncoderConfiguration(VideoEncoderConfiguration().apply {
////            // 视频编码发送时，使用480p
////            val outputHeight = 480
////            val scale = ScreenUtils.getAppScreenHeight() / outputHeight
////            val outputWidth = ScreenUtils.getScreenWidth() / scale
////            dimensions = VideoEncoderConfiguration.VideoDimensions(outputWidth, outputHeight)
//
//            dimensions = VideoEncoderConfiguration.VideoDimensions(
//                ScreenUtils.getAppScreenHeight(),
//                ScreenUtils.getScreenWidth()
//            )
////            dimensions =
////                VideoEncoderConfiguration.VD_1280x720
////                VideoEncoderConfiguration.VideoDimensions(1280, 720)
////                VideoEncoderConfiguration.VideoDimensions(640, 360)
//
//            orientationMode =
//                VideoEncoderConfiguration.ORIENTATION_MODE.ORIENTATION_MODE_ADAPTIVE
////                VideoEncoderConfiguration.ORIENTATION_MODE.ORIENTATION_MODE_FIXED_LANDSCAPE
////                VideoEncoderConfiguration.ORIENTATION_MODE.ORIENTATION_MODE_FIXED_PORTRAIT
//
//            degradationPrefer = VideoEncoderConfiguration.DEGRADATION_PREFERENCE.MAINTAIN_BALANCED
//
//        })
//     public VideoEncoderConfiguration(
//     VideoDimensions dimensions,
//     FRAME_RATE frameRate,
//     int bitrate,
//     ORIENTATION_MODE orientationMode)
        rtcEngine.setVideoEncoderConfiguration(VideoEncoderConfiguration().apply{
//            dimensions = VideoEncoderConfiguration.VideoDimensions(ScreenUtils.getAppScreenHeight(), ScreenUtils.getScreenWidth() )
            dimensions = VideoEncoderConfiguration.VD_1280x720
            frameRate = VideoEncoderConfiguration.FRAME_RATE.FRAME_RATE_FPS_15.value
            bitrate = STANDARD_BITRATE
            orientationMode = VideoEncoderConfiguration.ORIENTATION_MODE.ORIENTATION_MODE_ADAPTIVE
        })

        rtcEngine.setCameraCapturerConfiguration(
            CameraCapturerConfiguration(
//                    // 实现摄像头超采样 1080p
//                    CameraCapturerConfiguration.CD_1920x1080,
                CameraCapturerConfiguration.CAMERA_DIRECTION.CAMERA_FRONT
//                CameraCapturerConfiguration.CAPTURER_OUTPUT_PREFERENCE.CAPTURER_OUTPUT_PREFERENCE_AUTO
                )
        )

        // 启用用户音量提示
        val vad = rtcEngine.enableAudioVolumeIndication(200, 5, true)
        if (vad < 0) {
            Thread.sleep(200)
            rtcEngine.enableAudioVolumeIndication(200, 5, true)
        }
        // 开启视频
//        rtcEngine.disableVideo()
        rtcEngine.enableVideo()
//         开启音频
//        rtcEngine.disableAudio()
        rtcEngine.enableAudio()

        // 开启双流模式
        rtcEngine.enableDualStreamMode(true)

        // 发送端的配置。网络较差时，只发送音频流。
        rtcEngine.setLocalPublishFallbackOption(STREAM_FALLBACK_OPTION_AUDIO_ONLY)

        // 接收端的配置。弱网环境下先尝试接收小流；若当前网络环境无法显示视频，则只接收音频流。
        rtcEngine.setRemoteSubscribeFallbackOption(STREAM_FALLBACK_OPTION_AUDIO_ONLY)

    }

    /**
     * 退出视频
     */
    fun leaveChannel() {
        logI("DisplayManager.04-leaveChannel")
        Log.i(TAG, "退出视频")
        snapJob?.cancel()   // 关闭截图协程
        SurfaceViewSnapCache.clearAll()     // 清除所有的截图
        mRtcEngine?.apply {
            Log.v(TAG, "释放资源")
            muteAllRemoteAudioStreams(true)
            muteLocalAudioStream(true)
            leaveChannel()
        }
        RtcEngine.destroy()
        mRtcEngine = null
    }

    private fun getConnectionStateChangedName(state: Int, reason: Int): String {
        val stateName = when (state) {
            1 -> {
                "CONNECTION_STATE_DISCONNECTED(1)：网络连接断开"
            }
            2 -> {
                "CONNECTION_STATE_CONNECTING(2)：建立网络连接中"
            }
            3 -> {
                "CONNECTION_STATE_CONNECTED(3)：网络已连接"
            }
            4 -> {
                "CONNECTION_STATE_RECONNECTING(4)：重新建立网络连接中"
            }
            5 -> {
                "CONNECTION_STATE_FAILED(5)：网络连接失败"
            }
            else -> {
                "no name"
            }
        }
        val resonName = when (reason) {
            0 -> {
                "CONNECTION_CHANGED_CONNECTING(0)：建立网络连接中"
            }
            1 -> {
                "CONNECTION_CHANGED_JOIN_SUCCESS(1)：成功加入频道"
            }
            2 -> {
                "CONNECTION_CHANGED_INTERRUPTED(2)：网络连接中断"
            }
            3 -> {
                "CONNECTION_CHANGED_BANNED_BY_SERVER(3)：网络连接被服务器禁止。可能服务端踢人场景时会报这个错。"
            }
            4 -> {
                "CONNECTION_CHANGED_JOIN_FAILED(4)：加入频道失败"
            }
            5 -> {
                "CONNECTION_CHANGED_LEAVE_CHANNEL(5)：离开频道"
            }
            6 -> {
                "CONNECTION_CHANGED_INVALID_APP_ID(6)：不是有效的 APP ID。请更换有效的 APP ID 重新加入频道"
            }
            7 -> {
                "CONNECTION_CHANGED_INVALID_CHANNEL_NAME(7)：不是有效的频道名。请更换有效的频道名重新加入频道"
            }
            8 -> {
                "CONNECTION_CHANGED_INVALID_TOKEN(8)：生成的 Token 无效"
            }
            9 -> {
                "CONNECTION_CHANGED_TOKEN_EXPIRED(9)：当前使用的 Token 过期，不再有效，需要重新在你的服务端申请生成 Token"
            }
            10 -> {
                "CONNECTION_CHANGED_REJECTED_BY_SERVER(10)：此用户被服务器禁止"
            }
            11 -> {
                "CONNECTION_CHANGED_SETTING_PROXY_SERVER(11)：由于设置了代理服务器，SDK 尝试重连"
            }
            12 -> {
                "CONNECTION_CHANGED_RENEW_TOKEN(12)：更新 Token 引起网络连接状态改变"
            }
            13 -> {
                "CONNECTION_CHANGED_CLIENT_IP_ADDRESS_CHANGED(13)：客户端 IP 地址变更，可能是由于网络类型，或网络运营商的 IP 或端口发生改变引起"
            }
            14 -> {
                "CONNECTION_CHANGED_KEEP_ALIVE_TIMEOUT(14)：SDK 和服务器连接保活超时，进入自动重连状态"
            }
            else -> {
                "no reson"
            }
        }
        return "state=${state}--${stateName}\n,reson=${reason}--${resonName}"
    }

    // 远端用户视频状态发生改变回调
    private fun getRemoteVideoStateChanged(status: Int, reason: Int): String {
        // 远端视频流状态:
        val statusName = when (status) {
            0 -> {
                "REMOTE_VIDEO_STATE_STOPPED(0)：远端视频默认初始状态。"
            }
            1 -> {
                "REMOTE_VIDEO_STATE_STARTING(1)：本地用户已接收远端视频首包"
            }
            2 -> {
                "REMOTE_VIDEO_STATE_DECODING(2)：远端视频流正在解码，正常播放。"
            }
            3 -> {
                "REMOTE_VIDEO_STATE_FROZEN(3)：远端视频流卡顿。"
            }
            4 -> {
                "REMOTE_VIDEO_STATE_FAILED(4)：远端视频流播放失败。"
            }
            else -> {
                "no name"
            }
        }
        //远端视频流状态改变的具体原因：
        val reasonName = when (reason) {
            0 -> {
                "REMOTE_VIDEO_STATE_REASON_INTERNAL(0)：视频状态发生改变时，会报告该原因"
            }
            1 -> {
                "REMOTE_VIDEO_STATE_REASON_NETWORK_CONGESTION(1)：网络阻塞"
            }
            2 -> {
                "REMOTE_VIDEO_STATE_REASON_NETWORK_RECOVERY(2)：网络恢复正常"
            }
            3 -> {
                "REMOTE_VIDEO_STATE_REASON_LOCAL_MUTED(3)：本地用户停止接收远端视频流或本地用户禁用视频模块"
            }
            4 -> {
                "REMOTE_VIDEO_STATE_REASON_LOCAL_UNMUTED(4)：本地用户恢复接收远端视频流或本地用户启动视频模块"
            }
            5 -> {
                "REMOTE_VIDEO_STATE_REASON_REMOTE_MUTED(5)：远端用户停止发送视频流或远端用户禁用视频模块"
            }
            6 -> {
                "REMOTE_VIDEO_STATE_REASON_REMOTE_UNMUTED(6)：远端用户恢复发送视频流或远端用户启用视频模块"
            }
            7 -> {
                "REMOTE_VIDEO_STATE_REASON_REMOTE_OFFLINE(7)：远端用户离开频道"
            }
            8 -> {
                "REMOTE_VIDEO_STATE_REASON_AUDIO_FALLBACK(8)：远端视频流已回退为音频流"
            }
            9 -> {
                "REMOTE_VIDEO_STATE_REASON_AUDIO_FALLBACK_RECOVERY(9)：回退的远端音频流恢复为视频流"
            }
            else -> {
                "no name"
            }
        }

        return "status=${status}--${statusName}\n,reason=${reason}--${reasonName}"
    }

    //远端音频流状态:
    //reason	远端音频流状态改变的具体原因：
    private fun getRemoteAudioStateChanged(state: Int, reason: Int): String {
        val statesName = when (state) {
            0 -> {
                "REMOTE_AUDIO_STATE_STOPPED(0)：远端音频流默认初始状态。"
            }
            1 -> {
                "REMOTE_AUDIO_STATE_STARTING(1)：本地用户已接收远端音频首包"
            }
            2 -> {
                "REMOTE_AUDIO_STATE_DECODING(2)：远端音频流正在解码，正常播放。"
            }
            3 -> {
                "REMOTE_AUDIO_STATE_FROZEN(3)：远端音频流卡顿。"
            }
            4 -> {
                "REMOTE_AUDIO_STATE_FAILED(4)：远端音频流播放失败。"
            }
            else -> {
                "no states"
            }
        }
        val reasonName = when (reason) {
            0 -> {
                "REMOTE_AUDIO_REASON_INTERNAL(0)：音频状态发生改变时，会报告该原因"
            }
            1 -> {
                "REMOTE_AUDIO_REASON_NETWORK_CONGESTION(1)：网络阻塞"
            }
            2 -> {
                "REMOTE_AUDIO_REASON_NETWORK_RECOVERY(2)：网络恢复正常"
            }
            3 -> {
                "REMOTE_AUDIO_REASON_LOCAL_MUTED(3)：本地用户停止接收远端音频流或本地用户禁用音频模块"
            }
            4 -> {
                "REMOTE_AUDIO_REASON_LOCAL_UNMUTED(4)：本地用户恢复接收远端音频流或本地用户启用音频模块"
            }
            5 -> {
                "REMOTE_AUDIO_REASON_REMOTE_MUTED(5)：远端用户停止发送音频流或远端用户禁用音频模块"
            }
            6 -> {
                "REMOTE_AUDIO_REASON_REMOTE_UNMUTED(6)：远端用户恢复发送音频流或远端用户启用音频模块"
            }
            7 -> {
                "REMOTE_AUDIO_REASON_REMOTE_OFFLINE(7)：远端用户离开频道"
            }
            else -> {
                "no reason"
            }
        }

        return "stats=${state}--${statesName}\n,reason=${reason}--${reasonName}"
    }

    // 本地音频状态发生改变回调
    private fun getLocalAudioStateChanged(state: Int, err: Int): String {
        val statesName = when (state) {

            0 -> {
                "LOCAL_AUDIO_STREAM_STATE_STOPPED(0)：本地音频默认初始状态"
            }
            1 -> {
                "LOCAL_AUDIO_STREAM_STATE_CAPTURING(1)：本地音频采集设备启动成功"
            }
            2 -> {
                "LOCAL_AUDIO_STREAM_STATE_ENCODING(2)：本地音频首帧编码成功"
            }
            3 -> {
                "LOCAL_AUDIO_STREAM_STATE_FAILED(3)：本地音频启动失败"
            }
            else -> {
                "no states"
            }
        }
        val errorName = when (err) {
            0 -> {
                "LOCAL_AUDIO_STREAM_ERROR_OK(0)：本地音频状态正常"
            }
            1 -> {
                "LOCAL_AUDIO_STREAM_ERROR_FAILURE(1)：本地音频出错原因不明确"
            }
            2 -> {
                "LOCAL_AUDIO_STREAM_ERROR_DEVICE_NO_PERMISSION(2)：没有权限启动本地音频采集设备"
            }
            3 -> {
                "LOCAL_AUDIO_STREAM_ERROR_DEVICE_BUSY(3)：本地音频采集设备已经在使用中"
            }
            4 -> {
                "LOCAL_AUDIO_STREAM_ERROR_CAPTURE_FAILURE(4)：本地音频录制失败，建议你检查采集设备是否正常工作"
            }
            8 -> {
                "LOCAL_AUDIO_STREAM_ERROR_INTERRUPTED(8)：本地音频采集被系统电话中断"
            }
            else -> {
                "no reason"
            }
        }

        return "stats=${state}--${statesName}\n,reason=${err}--${errorName}"
    }

    // 本地视频状态发生改变回调
    private fun getLocalVideoStateChanged(state: Int, err: Int): String {
        val statesName = when (state) {
            0 -> {
                "LOCAL_VIDEO_STREAM_STATE_STOPPED(0)：本地视频默认初始状态"
            }
            1 -> {
                "LOCAL_VIDEO_STREAM_STATE_CAPTURING(1)：本地视频采集设备启动成功"
            }
            2 -> {
                "LOCAL_VIDEO_STREAM_STATE_ENCODING(2)：本地视频首帧编码成功"
            }
            3 -> {
                "LOCAL_VIDEO_STREAM_STATE_FAILED(3)：本地视频启动失败"
            }
            else -> {
                "no states"
            }
        }
        val errorName = when (err) {
            0 -> {
                "LOCAL_VIDEO_STREAM_ERROR_OK(0)：本地视频状态正常"
            }
            1 -> {
                "LOCAL_VIDEO_STREAM_ERROR_FAILURE(1)：出错原因不明确"
            }
            2 -> {
                "LOCAL_VIDEO_STREAM_ERROR_DEVICE_NO_PERMISSION(2)：没有权限启动本地视频采集设备"
            }
            3 -> {
                "LOCAL_VIDEO_STREAM_ERROR_DEVICE_BUSY(3)：本地视频采集设备正在使用中"
            }
            4 -> {
                "LOCAL_VIDEO_STREAM_ERROR_CAPTURE_FAILURE(4)：本地视频采集失败，建议检查采集设备是否正常工作"
            }
            5 -> {
                "LOCAL_VIDEO_STREAM_ERROR_ENCODE_FAILURE(5)：本地视频编码失败"
            }
            8 -> {
                "LOCAL_VIDEO_STREAM_ERROR_DEVICE_NOT_FOUND(8)：找不到本地视频采集设备"
            }
            else -> {
                "no reason"
            }
        }

        return "stats=${state}--${statesName}\n,reason=${err}--${errorName}"
    }

    // RTMP/RTMPS 推流状态发生改变回调
    private fun getRtmpStreamingStateChanged(state: Int, err: Int): String {

        val statesName = when (state) {
            0 -> {
                "RTMP_STREAM_PUBLISH_STATE_IDLE(0)：推流未开始或已结束。"
            }
            1 -> {
                "RTMP_STREAM_PUBLISH_STATE_CONNECTING(1)：正在连接 Agora 推流服务器和 CDN 服务器。"
            }
            2 -> {
                "RTMP_STREAM_PUBLISH_STATE_RUNNING(2)：推流正在进行。"
            }
            3 -> {
                "RTMP_STREAM_PUBLISH_STATE_RECOVERING(3)：正在恢复推流。"
            }
            4 -> {
                "RTMP_STREAM_PUBLISH_STATE_FAILURE(4)：推流失败。"
            }
            5 -> {
                "RTMP_STREAM_PUBLISH_STATE_DISCONNECTING(5)：SDK 正在与 Agora 推流服务器和 CDN 服务器断开连接。"
            }
            else -> {
                "no states"
            }
        }
        val errorName = when (err) {
            0 -> {
                "RTMP_STREAM_PUBLISH_ERROR_OK(0)：推流成功"
            }
            1 -> {
                "RTMP_STREAM_PUBLISH_ERROR_INVALID_ARGUMENT(1)：参数无效。"
            }
            2 -> {
                "RTMP_STREAM_PUBLISH_ERROR_ENCRYPTED_STREAM_NOT_ALLOWED(2)：推流已加密不能推流"
            }
            3 -> {
                "RTMP_STREAM_PUBLISH_ERROR_CONNECTION_TIMEOUT(3)：推流超时未成功。可调用 addPublishStreamUrl 重新推流"
            }
            4 -> {
                "RTMP_STREAM_PUBLISH_ERROR_INTERNAL_SERVER_ERROR(4)：推流服务器出现错误。请调用 addPublishStreamUrl 重新推流"
            }
            5 -> {
                "RTMP_STREAM_PUBLISH_ERROR_RTMP_SERVER_ERROR(5)：CDN 服务器出现错误"
            }
            6 -> {
                "RTMP_STREAM_PUBLISH_ERROR_TOO_OFTEN(6)：预留参数"
            }
            7 -> {
                "RTMP_STREAM_PUBLISH_ERROR_REACH_LIMIT(7)：单个主播的推流地址数目达到上限 10。请删掉一些不用的推流地址再增加推流地址"
            }
            8 -> {
                "RTMP_STREAM_PUBLISH_ERROR_NOT_AUTHORIZED(8)：主播操作不主播自己的流，如更新其他主播的流参数、停止其他主播的流。请检查 App 逻辑"
            }
            9 -> {
                "RTMP_STREAM_PUBLISH_ERROR_STREAM_NOT_FOUND(9)：服务器未找到这个流"
            }
            10 -> {
                "RTMP_STREAM_PUBLISH_ERROR_FORMAT_NOT_SUPPORTED(10)：推流地址格式有错误。请检查推流地址格式是否正确。"
            }
            11 -> {
                "RTMP_STREAM_PUBLISH_ERROR_NOT_BROADCASTER(11)：用户角色不是主播，该用户无法使用推流功能。请检查你的应用代码逻辑。"
            }
            13 -> {
                "RTMP_STREAM_PUBLISH_ERROR_TRANSCODING_NO_MIX_STREAM(13)：非转码推流情况下，调用了 updateRtmpTranscoding 或 setLiveTranscoding 方法更新转码属性。请检查你的应用代码逻辑。"
            }
            14 -> {
                "RTMP_STREAM_PUBLISH_ERROR_NET_DOWN(14)：主播的网络出错。"
            }
            15 -> {
                "RTMP_STREAM_PUBLISH_ERROR_INVALID_APPID(15)：你的 App ID 没有使用 Agora 推流服务的权限。请参考前提条件开启推流服务。"
            }
            100 -> {
                "RTMP_STREAM_UNPUBLISH_ERROR_OK(100)：推流已正常结束。当你调用 removePublishStreamUrl 结束推流后，SDK 会返回该值。"
            }
            else -> {
                "no reason"
            }
        }

        return "stats=${state}--${statesName}\n,reason=${err}--${errorName}"
    }

    fun setRemoteVideoStreamType(uid: Int, videoStreamHigh: Int) {
//        logI("DisplayManager.setRemoteVideoStreamType.uid=${uid},videoStreamHigh=${videoStreamHigh}")
        mRtcEngine?.setRemoteVideoStreamType(uid, videoStreamHigh)
    }

    // 开启/关闭本地视频采集。
    fun enableLocalVideo(flag: Boolean = true) {
        mRtcEngine?.enableLocalVideo(flag)
    }

    // 开启/关闭本地音频采集。
    fun enableLocalAudio(flag: Boolean = true) {
        mRtcEngine?.enableLocalAudio(flag)
    }


}