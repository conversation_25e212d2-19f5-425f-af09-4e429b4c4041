package com.czur.cloud.adapter;

/**
 * Created by czur_app001 on 2018/1/19.
 * Email：<EMAIL>
 * (ง •̀_•́)ง
 */

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.czur.cloud.R;
import com.czur.cloud.model.EtEquipmentModel;
import com.czur.cloud.preferences.UserPreferences;

import java.util.List;


public class EtDeviceAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private static final int TYPE_NORMAL = 0;
    private static final int TYPE_EMPTY = 1;
    private static final int TYPE_ADD = 2;
    private List<EtEquipmentModel> datas;
    private Context context;
    private final UserPreferences userPreferences;


    public EtDeviceAdapter(Context context, List<EtEquipmentModel> datas) {
        userPreferences = UserPreferences.getInstance(context);
        this.datas = datas;
        this.context = context;
    }


    public void refreshData(List<EtEquipmentModel> datas) {
        this.datas = datas;
        notifyDataSetChanged();
    }

    public void refreshItem(List<EtEquipmentModel> datas, int position) {
        this.datas = datas;
        notifyItemChanged(position);
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        if (viewType == TYPE_NORMAL) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_et_device, parent, false);
            return new NormalViewHolder(view);
        } else if (viewType == TYPE_EMPTY) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_et_device_empty, parent, false);
            return new EmptyHolder(view);
        } else if (viewType == TYPE_ADD) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_et_device_add, parent, false);
            return new AddHolder(view);
        } else {
            return null;
        }
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, final int position) {
        if (holder instanceof NormalViewHolder) {
            final NormalViewHolder mHolder = (NormalViewHolder) holder;
            mHolder.mItem = datas.get(position);
            mHolder.etDeviceNameTv.setText(mHolder.mItem.getAlias());
            if(mHolder.mItem.isNeedUpdateFw()){
                mHolder.etDeviceToUseBtn.setVisibility(View.VISIBLE);
                mHolder.etDeviceToUseBtn.setText(context.getString(R.string.need_update_fw));
                mHolder.etDeviceToUseBtn.setTextColor(context.getResources().getColor(R.color.white));
                mHolder.etDeviceToUseBtn.setBackground(context.getResources().getDrawable(R.drawable.btn_rec_5_bg_with_red_d4));
            }else {
                mHolder.etDeviceToUseBtn.setBackground(context.getResources().getDrawable(R.drawable.btn_rec_5_bg_with_white));
                if (!mHolder.mItem.isIsShared()) {
                    mHolder.etDeviceToUseBtn.setVisibility(View.GONE);
                } else {
                    mHolder.etDeviceToUseBtn.setVisibility(View.VISIBLE);
                    if (mHolder.mItem.getInUsingUserId() == Integer.parseInt(userPreferences.getUserId())) {
                        mHolder.etDeviceToUseBtn.setText(context.getString(R.string.stop_use));
                        mHolder.etDeviceToUseBtn.setTextColor(context.getResources().getColor(R.color.red_update));
                    } else {
                        mHolder.etDeviceToUseBtn.setText(context.getString(R.string.start_use));
                        mHolder.etDeviceToUseBtn.setTextColor(context.getResources().getColor(R.color.blue_29b0d7));
                    }
                }
            }

            mHolder.etDeviceMoreBtn.setVisibility(View.VISIBLE);
            String deviceBindUserId = mHolder.mItem.getBindUserId() + "";
            if (deviceBindUserId.equals(UserPreferences.getInstance(context).getUserId())) {
                mHolder.etDeviceMoreBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (onItemClickListener != null) {
                            onItemClickListener.onItemClick(position, mHolder.mItem, true);
                        }
                    }
                });
            } else {
                mHolder.etDeviceMoreBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (onItemClickListener != null) {
                            onItemClickListener.onItemClick(position, mHolder.mItem, false);
                        }
                    }
                });
            }


            mHolder.etDeviceToUseBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onItemUseEtClickListener != null) {
                        onItemUseEtClickListener.onUseEtClick(position, mHolder.mItem);
                    }
                }
            });


        } else if (holder instanceof EmptyHolder) {

        } else if (holder instanceof AddHolder) {
            final AddHolder mHolder = (AddHolder) holder;
            mHolder.etDeviceAddBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onAddItemClickListener != null) {
                        onAddItemClickListener.onAddItemClick();
                    }
                }
            });

        }

    }

    @Override
    public int getItemViewType(int position) {
        if (datas.size() == 0) {
            return TYPE_ADD;
        } else {
            if (position >= 0 && position < datas.size()) {
                return TYPE_NORMAL;
            } else {
                return TYPE_EMPTY;
            }
        }

    }

    @Override
    public int getItemCount() {
        if (datas.size() == 0) {
            return 1;
        } else if (datas.size() < 3) {
            return 3;
        } else {
            return datas.size() + 1;
        }

    }


    public class NormalViewHolder extends RecyclerView.ViewHolder {
        public final View mView;
        ConstraintLayout itemView;
        TextView etDeviceNameTv;
        ImageView etDeviceMoreBtn;
        TextView etDeviceToUseBtn;
        EtEquipmentModel mItem;
        TextView etDeviceWifiBtn;


        public NormalViewHolder(View view) {
            super(view);
            mView = view;
            itemView = (ConstraintLayout) view.findViewById(R.id.equipment_rl);
            etDeviceWifiBtn = (TextView) view.findViewById(R.id.et_device_wifi_btn);
            etDeviceNameTv = (TextView) view.findViewById(R.id.et_device_name_tv);
            etDeviceMoreBtn = (ImageView) view.findViewById(R.id.et_device_more_btn);
            etDeviceToUseBtn = (TextView) view.findViewById(R.id.et_device_to_use_btn);

        }

    }

    public static class EmptyHolder extends RecyclerView.ViewHolder {
        public final View mView;

        public EmptyHolder(View view) {
            super(view);
            mView = view;

       }

    }

    public static class AddHolder extends RecyclerView.ViewHolder {
        public final View mView;
        ImageView etDeviceAddBtn;

        public AddHolder(View view) {
            super(view);
            mView = view;
            etDeviceAddBtn = (ImageView) view.findViewById(R.id.et_device_add_btn);
        }

    }

    public OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void onItemClick(int position, EtEquipmentModel etEquipmentModel, boolean isAdmin);
    }

    public OnAddItemClickListner onAddItemClickListener;

    public void setOnAddItemClickListener(OnAddItemClickListner onAddItemClickListner) {
        this.onAddItemClickListener = onAddItemClickListner;
    }

    public interface OnAddItemClickListner {
        void onAddItemClick();
    }


    public OnItemUseEtClickListener onItemUseEtClickListener;

    public void setOnItemUseEtClickListener(OnItemUseEtClickListener onItemUseEtClickListener) {
        this.onItemUseEtClickListener = onItemUseEtClickListener;
    }

    public interface OnItemUseEtClickListener {
        void onUseEtClick(int position, EtEquipmentModel etEquipmentModel);
    }
}