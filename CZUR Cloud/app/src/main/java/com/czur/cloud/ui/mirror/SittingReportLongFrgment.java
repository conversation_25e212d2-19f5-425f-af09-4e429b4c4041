package com.czur.cloud.ui.mirror;

import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.auramate.reportfragment.CustomXAxisRenderer;
import com.czur.cloud.ui.auramate.reportfragment.ReportUtil;
import com.czur.cloud.ui.base.BaseFragment;
import com.czur.cloud.ui.mirror.comm.SittingReportUtil;
import com.czur.cloud.ui.mirror.model.SittingReportModelSub;
import com.czur.cloud.util.validator.Validator;
import com.github.mikephil.charting.charts.BarChart;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.formatter.ValueFormatter;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet;
import com.github.mikephil.charting.listener.ChartTouchListener;
import com.github.mikephil.charting.listener.OnChartGestureListener;
import com.github.mikephil.charting.listener.OnChartValueSelectedListener;
import com.google.gson.reflect.TypeToken;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Jason on 2020/12/29.
 */
public class SittingReportLongFrgment extends BaseFragment {
    private final int BAR_MAX_COUNT = 7;
    private final float BAR_WIDTH = 0.4f;

    private int currentIndex = 0;   //当前选中的X轴数据序号；
    private String typePageSize = "7";   //当前类型下的获取记录总数量；日：365，周：52，月：12
    private String equipmentId, titleName, type, reportId;
    List<String> barChartDataListX = new ArrayList<>();
    List<Integer> barChartDataListY = new ArrayList<>();
    private boolean isReflashFlag = false;  //判断是否需要加载更多后，刷新界面；没有数据，无需刷新；
    private boolean isLoadMore;//是否加载更多
    private boolean canLoadLeft;//向左是否可以加载更多
    private boolean canLoadRight;//向左是否可以加载更多
    private String oldReportID = "";  // 防止重复加载更多

    private List<SittingReportModelSub> reportListDatas = new ArrayList<>();//接口返回的数据
    private List<SittingReportModelSub> reportAllListDatas = new ArrayList<>();//插入空值的数据；用于显示，查找
    //注意，获取到的网络数据是倒排序，reportListDatas也是倒排序；新的日期在前面，早的日期在后面
    //barchart显示的需要时正排序，即早的日期在前面，新的日期在最后

    private RelativeLayout emptyRl, dataRl;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private BarChart chart;
    private int curSelectValueX = 0;    //当前选中的bar，点击空白处，仍然选择当前的。
    private int oldListDatasCount = 0;  //加载数据前的数据总数
    private SmartRefreshLayout refreshLayout;
    private int dataMinNumber=0, dataMaxNumber = 0; // 最小 最大的值

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_sitting_long_report, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        reportId="";
        Bundle bundle = requireArguments();
        equipmentId = bundle.getString("equipmentId");
        titleName = bundle.getString("titleName");
        type = bundle.getString("type");
        if(type.equals(CZURConstants.SITTING_REPORT_STR_DAY)){
            typePageSize="15";
        }
        if(type.equals(CZURConstants.SITTING_REPORT_STR_WEEK)){
            typePageSize="15";
        }
        if(type.equals(CZURConstants.SITTING_REPORT_STR_MONTH)){
            typePageSize="12";
        }

        initComponent();
    }

    private void initComponent(){
        userPreferences = UserPreferences.getInstance();
        httpManager = HttpManager.getInstance();
        emptyRl = requireActivity().findViewById(R.id.empty_rl);
        dataRl = requireActivity().findViewById(R.id.data_rl);

        emptyRl.setVisibility(View.GONE);
        dataRl.setVisibility(View.GONE);

        refreshLayout = requireActivity().findViewById(R.id.refresh_layout);
        refreshLayout.setEnableOverScrollDrag(false);
        refreshLayout.setEnableOverScrollBounce(false);
        refreshLayout.setEnableAutoLoadMore(false);
        refreshLayout.setEnableRefresh(true);
        refreshLayout.setEnableNestedScroll(false);
        refreshLayout.setEnableFooterFollowWhenNoMoreData(true);
        refreshLayout.setEnableLoadMoreWhenContentNotFull(false);
        refreshLayout.setEnableLoadMore(false);
        refreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                getReportListNew(equipmentId);
//                refreshLayout.finishRefresh(true);
            }
        });

        isLoadMore = true;
        canLoadLeft = true;
        canLoadRight = true;
        dataMinNumber = 0;
        dataMaxNumber = 0;

        getReportListNew(equipmentId);

        // 正确率趋势
        chart = requireActivity().findViewById(R.id.chart1);

        chart.setOnChartValueSelectedListener(new OnChartValueSelectedListener() {
            @Override
            public void onValueSelected(Entry e, Highlight h) {
                int curX = (int) e.getX();

                if ((curX > -1) && (curX < barChartDataListX.size())){
                    String selectXName = barChartDataListX.get(curX);
                    int right = barChartDataListY.get(curX);

                    //当日有学习数据，正确率及错误率为0时，条形图点击切换。
                    //当日未学习，不显示数据，条形图不支持点击切换。还是上一次点击的条图形
                    if (right > 0){
                        curSelectValueX = curX;
                    }else{
                        Highlight high = new Highlight(curSelectValueX, 0, 0);
                        high.setDataIndex(curSelectValueX);
                        chart.highlightValue(high, false);
                        return;
                    }

                    HighlightBarChartCurSelected(curSelectValueX);

                    for (SittingReportModelSub entry:reportAllListDatas){
                        String fromEnd = entry.getFromEnd();
                        if (!Validator.isEmpty(fromEnd)){
                            fromEnd = ReportUtil.foramtDateTime(fromEnd, Integer.parseInt(type));
                        }else{
                            fromEnd = "";
                        }

                        if (fromEnd.equals(selectXName)){
                            //使用Activity的runOnUiThread()方法更新UI
                            getActivity().runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    initReportHorizontalView(entry);
                                }
                            });

                            break;
                        }
                    }
                }
            }

            @Override
            public void onNothingSelected() {
                HighlightBarChartCurSelected(curSelectValueX);
            }
        });

        chart.setOnChartGestureListener(new OnChartGestureListener() {

            @Override
            public void onChartLongPressed(MotionEvent me) {            }

            @Override
            public void onChartDoubleTapped(MotionEvent me) {            }

            @Override
            public void onChartSingleTapped(MotionEvent me) {            }

            @Override
            public void onChartFling(MotionEvent me1, MotionEvent me2, float velocityX, float velocityY) {            }

            @Override
            public void onChartScale(MotionEvent me, float scaleX, float scaleY) {            }

            @Override
            public void onChartGestureStart(MotionEvent me, ChartTouchListener.ChartGesture lastPerformedGesture) {
                isLoadMore = false;
            }

            @Override
            public void onChartGestureEnd(MotionEvent me, ChartTouchListener.ChartGesture lastPerformedGesture) {
                int leftX = (int) chart.getLowestVisibleX();//获取可视区域中，显示在x轴最左边的index，最小值
                int rightX = (int) chart.getHighestVisibleX();//最大值，最右边的
                int dataCount = reportAllListDatas.size();
                oldListDatasCount = dataCount;

                if(lastPerformedGesture == ChartTouchListener.ChartGesture.DRAG){
                    isLoadMore = true;
                    if((leftX == 0 || leftX == 1) && (rightX == BAR_MAX_COUNT || rightX == BAR_MAX_COUNT-1 )){
                        isLoadMore = false;
                        if (!canLoadLeft)
                            return;

                        //<==加载更多数据的操作
                        String report_id = String.valueOf(reportAllListDatas.get(0).getId());
                        if (!oldReportID.equals(report_id)){
                            oldReportID = report_id;
                            getReportListBeforeMore(equipmentId, report_id);
                        }
                    }

//                    if((rightX == dataCount-1 || rightX == dataCount) && (leftX == dataCount-BAR_MAX_COUNT || leftX == dataCount-BAR_MAX_COUNT-1 )){
//                        isLoadMore = false;
//                        if (!canLoadRight)
//                            return;
//
//                        //==>加载更多数据的操作
//                        String report_id = String.valueOf(reportAllListDatas.get(dataCount-1).getId());
//                        getReportListAfterMore(equipmentId, report_id);
//                    }
                }
            }

            @Override
            public void onChartTranslate(MotionEvent me, float dX, float dY) {
                if(isLoadMore){
                    int leftX = (int) chart.getLowestVisibleX();//获取可视区域中，显示在x轴最左边的index，最小值
                    int rightX = (int) chart.getHighestVisibleX();//最大值，最右边的
                    int dataCount = reportAllListDatas.size();
                    oldListDatasCount = dataCount;

                    if((leftX == 0 || leftX == 1) && (rightX == BAR_MAX_COUNT || rightX == BAR_MAX_COUNT-1 )){
                        isLoadMore = false;
                        if (!canLoadLeft)
                            return;

                        //<==加载更多数据的操作
                        String report_id = String.valueOf(reportAllListDatas.get(0).getId());
                        if (!oldReportID.equals(report_id)) {
                            oldReportID = report_id;
                            getReportListBeforeMore(equipmentId, report_id);
                        }
                    }

//                    if((rightX == dataCount-1 || rightX == dataCount) && (leftX == dataCount-BAR_MAX_COUNT || leftX == dataCount-BAR_MAX_COUNT-1 )){
//                        isLoadMore = false;
//                        if (!canLoadRight)
//                            return;
//
//                        //==>加载更多数据的操作
//                        String report_id = String.valueOf(reportAllListDatas.get(dataCount-1).getId());
//                        getReportListAfterMore(equipmentId, report_id);
//                    }
                }
            }
        });

    }

    //高亮显示当前选中的条目
    private void HighlightBarChartCurSelected(int curx){
        Highlight high = new Highlight(curx, 0, 0);
        high.setDataIndex(curx);
        chart.highlightValue(high, false);
    }

    /**
     * @des: 刷新接口
     */
    private OnRefreshListener onRefreshListener = refreshLayout -> getReportListNew(equipmentId);

    private List<SittingReportModelSub> getBeforeReportList(String equipmentId, String reportId) {
        try {
            final MiaoHttpEntity<SittingReportModelSub> reportEntity = httpManager.request().
                    getBeforeSittingReports(
                            userPreferences.getUserId(),
                            equipmentId,
                            type,
                            reportId,
                            typePageSize,
                            new TypeToken<List<SittingReportModelSub>>() {
                            }.getType());

            if (reportEntity == null) {
                return null;
            }
            if (reportEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return reportEntity.getBodyList();
            } else {
                return null;
            }

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private List<SittingReportModelSub> getAfterReportList(String equipmentId, String reportId) {
        try {
            final MiaoHttpEntity<SittingReportModelSub> reportEntity = httpManager.request().
                    getAfterSittingReports(
                            userPreferences.getUserId(),
                            equipmentId,
                            type,
                            reportId,
                            typePageSize,
                            new TypeToken<List<SittingReportModelSub>>() {
                            }.getType());

            if (reportEntity == null) {
                return null;
            }
            if (reportEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return reportEntity.getBodyList();
            } else {
                return null;
            }

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * @des: 刷新列表
     */
    public void getReportListNew(String equipment_id) {
        if (!NetworkUtils.isConnected()) {
            showMessage(R.string.toast_no_connection_network);
            showEmpty();
            hideProgressDialog();
            return;
        }

        showProgressDialog();

        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                reportListDatas = getBeforeReportList(equipment_id, "");
                reportListDatas = SittingReportUtil.reportListDatasReversal(reportListDatas, Integer.parseInt(type));

                ReportUtil.printSittingDatasLog(reportListDatas);

                if (Validator.isNotEmpty(reportListDatas)) {
                    if (reportListDatas.size() < BAR_MAX_COUNT) {
                        canLoadLeft = false;
                        canLoadRight = false;
                    }

                    //插值，补到reportAllListDatas里面
//                    reportAllListDatas = reportListDatasAddSpace(reportListDatas);
                    reportAllListDatas = SittingReportUtil.reportListDatasAddSpace(reportListDatas, Integer.parseInt(type));
                    ReportUtil.printSittingDatasLog(reportAllListDatas);

                }else{
                    canLoadRight = false;
                    canLoadLeft = false;
                }
                return null;
            }

            @Override
            public void onSuccess(Void result) {
                refreshLayout.finishRefresh(true);

                refreshFiles();
                showEmpty();
                hideProgressDialog();
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                refreshLayout.finishRefresh(false);

                showEmpty();
                hideProgressDialog();
            }
        });
    }

    public void getReportListBeforeMore(String equipment_id, String report_id) {
        if (!NetworkUtils.isConnected()) {
            showMessage(R.string.toast_no_connection_network);
            showEmpty();
            hideProgressDialog();
            return;
        }

        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                isReflashFlag = false;
                reportListDatas = getBeforeReportList(equipment_id, report_id);
                if (Validator.isNotEmpty(reportListDatas)) {
                    if (reportListDatas.size() < BAR_MAX_COUNT)
                        canLoadLeft = false;

                    isReflashFlag = true;
                    //插值，补到reportAllListDatas里面
                    reportListDatas = SittingReportUtil.reportListDatasReversal(reportListDatas, Integer.parseInt(type));
                    ReportUtil.printSittingDatasLog(reportListDatas);

                    ///////
                    reportAllListDatas.addAll(0,reportListDatas);
//                    reportAllListDatas = reportListDatasAddSpace(reportAllListDatas);
                    reportAllListDatas = SittingReportUtil.reportListDatasAddSpace(reportAllListDatas, Integer.parseInt(type));
                    currentIndex = reportAllListDatas.size()-oldListDatasCount-1;

                    ReportUtil.printSittingDatasLog(reportAllListDatas);
                }else{
                    canLoadLeft = false;
                    isReflashFlag = false;
                }

                return null;
            }

            @Override
            public void onSuccess(Void result) {
                if (isReflashFlag){
                    refreshBarChart();
                    showEmpty();
                }
                hideProgressDialog();
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                hideProgressDialog();
            }
        });
    }

    public void getReportListAfterMore(String equipment_id, String report_id) {
        if (!NetworkUtils.isConnected()) {
            showMessage(R.string.toast_no_connection_network);
            showEmpty();
            hideProgressDialog();
            return;
        }

        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                isReflashFlag = false;
                reportListDatas = getAfterReportList(equipment_id, report_id);
                if (Validator.isNotEmpty(reportListDatas)) {
                    if (reportListDatas.size() < BAR_MAX_COUNT)
                        canLoadRight = false;

                    isReflashFlag = true;
                    //插值，补到reportAllListDatas里面
                    reportListDatas = SittingReportUtil.reportListDatasReversal(reportListDatas, Integer.parseInt(type));
                    ReportUtil.printSittingDatasLog(reportListDatas);

                    ///////
                    reportAllListDatas.addAll(reportListDatas);
//                    reportAllListDatas = reportListDatasAddSpace(reportAllListDatas);
                    reportAllListDatas = SittingReportUtil.reportListDatasAddSpace(reportAllListDatas, Integer.parseInt(type));
                    ReportUtil.printSittingDatasLog(reportAllListDatas);
                }else{
                    canLoadRight = false;
                    isReflashFlag = false;
                }
                return null;
            }

            @Override
            public void onSuccess(Void result) {
                if (isReflashFlag){
                    refreshBarChart();
                    showEmpty();
                }
                hideProgressDialog();
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                hideProgressDialog();
            }
        });
    }

    private void refreshBarChart() {

        //对barChartDataListX等数据初始化
        initBarChartDataListFromServer();

        //刷新柱状图
        reflashChart(1, chart);

        //将当前视口的左侧（边）到指定的 x 值。
        chart.moveViewToX(currentIndex);

    }

    //对要在图表展示的数据进行初始化赋值操作
    private void initBarChartDataListFromServer(){
        barChartDataListX.clear();
        barChartDataListY.clear();

        //获取barChart的数据
        for (SittingReportModelSub entry: reportAllListDatas){
            String reportType = entry.getType()+"";

            if (!reportType.equals(type))
                continue;

            int right = entry.getSedentaryTimeCount();
            if (Validator.isEmpty(right))
                right = 0;

            String fromEnd = entry.getFromEnd();
            barChartDataListX.add(fromEnd);
            barChartDataListY.add(right);
            if (right < dataMinNumber) dataMinNumber=right;
            if (right > dataMaxNumber) dataMaxNumber=right;
        }

        //对于少于7天的数据，补齐缺少的，让柱状图靠右对齐
        if (barChartDataListX.size()<BAR_MAX_COUNT){
            int x = BAR_MAX_COUNT - barChartDataListX.size();
            for (int i=0; i<x; i++){
                barChartDataListX.add(0, "");
                barChartDataListY.add(0,0);
            }
        }

    }

    private void refreshFiles() {

        //初始化绘制统计数据
        if (Validator.isEmpty(reportAllListDatas))
            return;

        initBarChartDataListFromServer();

        curSelectValueX = barChartDataListX.size()-1;
        SittingReportModelSub armSub = reportAllListDatas.get(reportAllListDatas.size()-1);

        initReportHorizontalView(armSub);

        int allCount= reportAllListDatas.size();
        if (allCount<BAR_MAX_COUNT){
            int x = BAR_MAX_COUNT - allCount;
            curSelectValueX = curSelectValueX +x;
        }

        initChart(chart);
    }

    private void initReportHorizontalView(SittingReportModelSub reportData) {
        TextView tv_report_alert_value, tv_report_alert_per;
        tv_report_alert_value = getActivity().findViewById(R.id.tv_report_alert_value);
        int times = reportData.getSedentaryTimeCount();
        tv_report_alert_value.setText(times+"");

        tv_report_alert_per = getActivity().findViewById(R.id.tv_report_alert_per);
        tv_report_alert_per.setVisibility(View.INVISIBLE);

        if (type.equals(CZURConstants.SITTING_REPORT_STR_WEEK) || type.equals(CZURConstants.SITTING_REPORT_STR_MONTH)){
            tv_report_alert_per.setVisibility(View.VISIBLE);

            if (BuildConfig.IS_OVERSEAS) {
                float size = 12;
                ((TextView)getActivity().findViewById(R.id.tv_report_alert_han)).setTextSize(size);
                tv_report_alert_per.setTextSize(size);
            }
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    private void showEmpty() {
        if (Validator.isEmpty(reportAllListDatas)){
            emptyRl.setVisibility(View.VISIBLE);
            dataRl.setVisibility(View.GONE);
            return;
        }
        if (reportAllListDatas.size() > 0) {
            emptyRl.setVisibility(View.GONE);
            dataRl.setVisibility(View.VISIBLE);

        } else {
            emptyRl.setVisibility(View.VISIBLE);
            dataRl.setVisibility(View.GONE);
        }
    }

    private void reflashChart(int typeA, BarChart chart){

        XAxis xAxis = chart.getXAxis();

        xAxis.setValueFormatter(new ValueFormatter() {
            @Override
            public String getFormattedValue(float value) {
                if ((int)value > barChartDataListX.size()-1)
                    return "";
//                return barChartDataListX.get((int)value);
                String ret1 = barChartDataListX.get((int)value);
                String ret = ret1;
                //仅仅针对周的情况，需要旋转显示
                if (type.equals(CZURConstants.SITTING_REPORT_STR_WEEK)) {
                    String[] strArray = ret1.split("-");
                    if (strArray.length > 1) {
                        String strFrom = strArray[0];
                        String strEnd = strArray[1];
                        ret =  strFrom + "-\n" + strEnd;
                    }
                }
                return ret;
            }
        });

        int lenSize = barChartDataListY.size();

        // setting data
        ArrayList<BarEntry> values = new ArrayList<>();
        for (int i = 0; i < lenSize; i++) {
            values.add(new BarEntry(i, barChartDataListY.get(i)));
        }

        BarDataSet set1;
        set1 = new BarDataSet(values, "Data Set");
        set1.setDrawValues(false);
        set1.setColor(Color.argb(255,24,143,142));  //柱子的颜色 8a e5 b1 ///188f8e==24,143,142
        if (typeA == 2){
            set1.setColor(Color.argb(255,240,117,117));  //柱子的颜色 f07575==240,117,117
        }
        set1.setHighLightAlpha(45);

        ArrayList<IBarDataSet> dataSets = new ArrayList<>();
        dataSets.add(set1);

        BarData data = new BarData(dataSets);
        data.setBarWidth(BAR_WIDTH);
        chart.setData(data);
        chart.setFitBars(true);

        //setVisibleXRange是设置可见的x范围，我设置成(0, 5)之后，
        // 就只会显示序号0到5的内容(6项)。往左滑才会显示第7,8,9…最后一项。
        chart.setVisibleXRange(0,BAR_MAX_COUNT);

        chart.notifyDataSetChanged();
        chart.invalidate();
    }

    public void initChart(BarChart chart){

        chart.getDescription().setEnabled(false);

        // if more than 60 entries are displayed in the chart, no values will be drawn
        chart.setMaxVisibleValueCount(60);

        //是否能够缩放
        chart.setPinchZoom(false);
        //允许X轴缩放
        chart.setScaleXEnabled(false);
        //允许Y轴缩放
        chart.setScaleYEnabled(false);

        chart.setDrawBarShadow(false);
        chart.setDrawGridBackground(false);

        XAxis xAxis = chart.getXAxis();
        YAxis leftAxis = chart.getAxisLeft();
        YAxis rightAxis = chart.getAxisRight();

        xAxis.setPosition(XAxis.XAxisPosition.BOTTOM);
        xAxis.setGranularity(1f);
        xAxis.setLabelCount(BAR_MAX_COUNT, false);

        // add a nice and smooth animation
        chart.animateY(1000);

        xAxis.setValueFormatter(new ValueFormatter() {
            @Override
            public String getFormattedValue(float value) {
                if ((int)value > barChartDataListX.size()-1)
                    return "";
//                return barChartDataListX.get((int)value);
                String ret1 = barChartDataListX.get((int)value);
                String ret = ret1;
                //仅仅针对周的情况，需要旋转显示
                if (type.equals(CZURConstants.SITTING_REPORT_STR_WEEK)) {
                    String[] strArray = ret1.split("-");
                    if (strArray.length > 1) {
                        String strFrom = strArray[0];
                        String strEnd = strArray[1];
                        ret =  strFrom + "-\n" + strEnd;
                    }
                }
                return ret;
            }
        });

        leftAxis.setValueFormatter(new ValueFormatter() {
            @Override
            public String getFormattedValue(float value) {
                if (value>0)
                    return (int) value + "";
                else{
                    return "";
                }
            }
        });
        leftAxis.setTextColor(Color.rgb(128,128,128));
        xAxis.setTextColor(Color.rgb(128,128,128));

        chart.getAxisLeft().setDrawAxisLine(false);
        chart.getXAxis().setDrawAxisLine(false);

        chart.getAxisRight().setEnabled(false);
        chart.getLegend().setEnabled(false);

        chart.setDrawBorders(false); ////是否在折线图上添加边框
        // 如果没有数据的时候，会显示这个，类似ListView的EmptyView
        chart.setDrawGridBackground(true); // 是否显示表格颜色
        chart.setGridBackgroundColor(Color.argb(255, 240,240,240)); // 表格的的颜色，在这里是是给颜色设置一个透明度
        //是否展示网格线
//        chart.setDrawGridBackground(false);
        xAxis.setDrawGridLines(false);
        rightAxis.setDrawGridLines(false);
        leftAxis.setDrawGridLines(false);

        chart.setTouchEnabled(true);//设置是否可以触摸，如为false，则不能拖动，缩放等
        chart.setDragEnabled(true); //设置是否可以拖拽，缩放

        int lenSize = barChartDataListY.size();

        leftAxis.setYOffset(10f);//这样会向下偏移 50%。我也不知道为啥，自己试出来的
        float xSize = 0.95f;

        // setting data
        ArrayList<BarEntry> values = new ArrayList<>();

        for (int i = 0; i < lenSize; i++) {
            values.add(new BarEntry(i, barChartDataListY.get(i)*xSize));
        }

        xAxis.setAxisMinimum(-0.5f);    //X轴数据向右偏移一些
//        xAxis.setAxisMinimum(100f);     //

        //////////////采用动态坐标轴数据的逻辑，具体为以下内容。
        //坐标轴数据显示逻辑：
        //提醒最大值在0-5次：坐标轴显示显示0-5
        //最大值在5-10次：坐标轴显示显示2-10
        //最大值为10-25次：坐标轴显示显示5-25
        //最大值为25-50次：坐标轴值显示10-50
        //最大值为50-100次：坐标轴显示20-100
        //最大值为100-125次：坐标轴显示25-125
        float min =0f;
        float max = 100f;
        if (dataMinNumber >= 100)
            min = 25f;
        else if (dataMinNumber >= 50)
            min = 20f;
        else if (dataMinNumber >= 25)
            min = 10f;
        else if (dataMinNumber >= 10)
            min = 5f;
        else if (dataMinNumber >= 5)
            min = 2f;
        else
            min = 0;

        if (dataMaxNumber <= 5)
            max = 5;
        else if (dataMaxNumber <= 10)
            max = 10;
        else if (dataMaxNumber <= 25)
            max = 25;
        else if (dataMaxNumber <= 50)
            max = 50;
        else if (dataMaxNumber <= 100)
            max = 100;
        else if (dataMaxNumber <= 125)
            max = 125;
        else if (dataMaxNumber <= 150)
            max = 150;
        else if (dataMaxNumber <= 200)
            max = 200;
        else if (dataMaxNumber <= 300)
            max = 300;
        else if (dataMaxNumber <= 500)
            max = 500;
        else
            max = 500;
        
        leftAxis.setAxisMinimum(min);    //最小刻度值
        leftAxis.setAxisMaximum(max);  // the axis maximum is 100
        leftAxis.setLabelCount(5);

        chart.setExtraBottomOffset(2.2f * 10f);
        xAxis.setTextSize(10f);
        chart.setXAxisRenderer(new CustomXAxisRenderer(chart.getViewPortHandler(), chart.getXAxis(), chart.getTransformer(YAxis.AxisDependency.LEFT)));

        BarDataSet set1;
        set1 = new BarDataSet(values, "Data Set");
        set1.setDrawValues(false);

        set1.setColor(Color.argb(255,24,143,142));  //柱子的颜色 8a e5 b1 ///188f8e==24,143,142
        set1.setHighLightAlpha(45);

        ArrayList<IBarDataSet> dataSets = new ArrayList<>();
        dataSets.add(set1);

        BarData data = new BarData(dataSets);
        data.setBarWidth(BAR_WIDTH);
        chart.setData(data);
        chart.setFitBars(true);

        //setVisibleXRange是设置可见的x范围，我设置成(0, 5)之后，
        // 就只会显示序号0到5的内容(6项)。往左滑才会显示第7,8,9…最后一项。
        chart.setVisibleXRange(0,BAR_MAX_COUNT);

//        将当前视口的左侧（边）到指定的 x 值。
        chart.moveViewToX(curSelectValueX);

        // 将坐标移动到最新
        // 此代码将刷新图表的绘图
        Highlight high = new Highlight(curSelectValueX, 0, 0);
        high.setDataIndex(curSelectValueX);
        chart.highlightValue(high, false);

        chart.invalidate();
    }

    //30个横坐标时，缩放4f是正好的。
    private float scalePercent = 4f/30f;

}
