package com.czur.cloud.ui.starry.meeting.common

import androidx.lifecycle.MutableLiveData
import com.czur.cloud.ui.starry.meeting.baselib.handler.SPContentHandler

/**
 * Created by 陈丰尧 on 2021/8/4
 */

object MeetingHandler : SPContentHandler() {
    private const val KEY_MEETING_STATUS = "meetingStatus"          // 会议状态
    private const val KEY_MEETING_MEMBER_CHOOSE = "meetingChoose"  // 人员选择中


    override val keyLiveMap: Map<String, LiveTrans<*>> by lazy {
        mapOf(
                // 会议状态
                KEY_MEETING_STATUS to LiveTrans(meetingStatusLive as MutableLiveData) {
                    val value = if (it.isNotEmpty()) it else MeetingStatus.STOPPED.name
                    MeetingStatus.valueOf(value)
                },
                // 人员选择
                KEY_MEETING_MEMBER_CHOOSE to LiveTrans(inMemberChoiceLive as MutableLiveData) {
                    it.toBoolean()
                }
        )
    }


    /**
     * 会议状态
     */
    val meetingStatusLive = createLive { meetingStatus }
    var meetingStatus: MeetingStatus
        set(value) {
            setValue(KEY_MEETING_STATUS, value.name)
        }
        get() {
            return MeetingStatus.valueOf(
                    getValue(
                            KEY_MEETING_STATUS,
                            MeetingStatus.STOPPED.name,
                    )
            )
        }

    /**
     * 是否当前在人员选择中
     * 由Launcher-ContactViewModel 维护
     */
    val inMemberChoiceLive = createLive { inMemberChoice }
    var inMemberChoice: Boolean
        set(value) {
            setValue(KEY_MEETING_MEMBER_CHOOSE, value)
        }
        get() = getValue(KEY_MEETING_MEMBER_CHOOSE, false)

}