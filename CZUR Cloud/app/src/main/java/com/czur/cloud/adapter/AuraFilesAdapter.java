package com.czur.cloud.adapter;

import android.app.Activity;
import android.content.Context;
import android.net.Uri;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.R;
import com.czur.cloud.cache.AuraCustomImageRequest;
import com.czur.cloud.model.AuraHomeFileModel;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.interfaces.DraweeController;
import com.facebook.drawee.view.SimpleDraweeView;
import com.facebook.imagepipeline.request.ImageRequestBuilder;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * Created by Yz on 2018/3/16
 * Email：<EMAIL>
 * 重新改造 by shaojun on 2020/5/20
 */


public class AuraFilesAdapter extends RecyclerView.Adapter<ViewHolder> {

    private Activity mActivity;
    //当前需要显示的所有的图片数据
    private List<AuraHomeFileModel.FilesBean> datas;
    //是否进入选择
    private boolean isSelectItem;
    private LayoutInflater mInflater;
    private LinkedHashMap<String, String> isCheckedMap = new LinkedHashMap<>();


    /**
     * 构造方法
     */
    public AuraFilesAdapter(Activity activity, List<AuraHomeFileModel.FilesBean> datas, boolean isSelectItem) {
        this.mActivity = activity;
        this.isSelectItem = isSelectItem;
        this.datas = datas;
        mInflater = LayoutInflater.from(activity);
    }


    public void refreshData(boolean isSelectItem) {
        this.isSelectItem = isSelectItem;
        notifyDataSetChanged();
    }


    public void refreshData(List<AuraHomeFileModel.FilesBean> datas, boolean isSelectItem, LinkedHashMap<String, String> isCheckedMap) {
        this.datas = datas;
        this.isSelectItem = isSelectItem;
        this.isCheckedMap = isCheckedMap;
        notifyDataSetChanged();
    }

    public void refreshData(List<AuraHomeFileModel.FilesBean> datas) {
        this.datas = datas;
        notifyDataSetChanged();
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return new EtFilesHolder(mInflater.inflate(R.layout.item_et_files, parent, false));
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {
        if (holder instanceof EtFilesHolder) {
            final EtFilesHolder mHolder = (EtFilesHolder) holder;
            mHolder.mItem = datas.get(position);
            Uri lowResUri = Uri.parse(getSmallUrl(mHolder.mItem));
            AuraCustomImageRequest imageRequest = new AuraCustomImageRequest(ImageRequestBuilder.newBuilderWithSource(lowResUri));
            imageRequest.setImageRequestType(1);
            imageRequest.setImageRequestObject("small");
            DraweeController controller = Fresco.newDraweeControllerBuilder()
                    .setImageRequest(imageRequest)
                    .setOldController(mHolder.etFilesImg.getController())
                    .build();
            mHolder.etFilesImg.setController(controller);
            if (isSelectItem) {
                mHolder.checkBox.setVisibility(View.VISIBLE);
                mHolder.checkBox.setTag(mHolder.mItem.getId());
            } else {
                mHolder.checkBox.setVisibility(View.GONE);
            }
            mHolder.checkBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    if (isChecked) {
                        if (!isCheckedMap.containsKey(mHolder.checkBox.getTag())) {
                            //选中时添加
                            isCheckedMap.put(mHolder.mItem.getId(), getOssKey(mHolder.mItem));
                        }
                    } else {
                        if (isCheckedMap.containsKey(mHolder.checkBox.getTag())) {
                            //没选中时移除
                            isCheckedMap.remove(mHolder.mItem.getId());
                        }
                    }
                    if (onItemCheckListener != null) {
                        onItemCheckListener.onItemCheck(position, mHolder.mItem, isCheckedMap, datas.size());
                    }
                }
            });
            if (isCheckedMap != null) {
                mHolder.checkBox.setChecked(isCheckedMap.containsKey(mHolder.mItem.getId()));
            } else {
                mHolder.checkBox.setChecked(false);
            }
            mHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onEtFilesClickListener != null) {
                        onEtFilesClickListener.onEtFilesClick(mHolder.mItem, position, mHolder.checkBox);
                    }
                }
            });
        }
    }

    private String getSmallUrl(AuraHomeFileModel.FilesBean item) {
        return item.getSmallSingle();
    }

    private String getOssKey(AuraHomeFileModel.FilesBean item) {
        return item.getSingleKey();
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return datas.size();
    }

    private static class EtFilesHolder extends ViewHolder {
        public final View mView;
        AuraHomeFileModel.FilesBean mItem;
        SimpleDraweeView etFilesImg;
        CheckBox checkBox;

        EtFilesHolder(View itemView) {
            super(itemView);
            mView = itemView;
            etFilesImg = (SimpleDraweeView) itemView.findViewById(R.id.et_files_img);
            checkBox = (CheckBox) itemView.findViewById(R.id.check);
            ViewGroup.LayoutParams layoutParams = itemView.getLayoutParams();
            layoutParams.width = (ScreenUtils.getScreenWidth() - SizeUtils.dp2px(16)) / 3;
            layoutParams.height = (int) (layoutParams.width * 1.33f);
            itemView.setLayoutParams(layoutParams);
        }
    }

    public int getTotalSize() {
        return datas.size();
    }

    public View inflate(Context context, int layoutId) {
        if (layoutId <= 0) {
            return null;
        }
        return LayoutInflater.from(context).inflate(layoutId, null);
    }


    private OnEtFilesClickListener onEtFilesClickListener;

    public void setOnEtFilesClickListener(OnEtFilesClickListener onEtFilesClickListener) {
        this.onEtFilesClickListener = onEtFilesClickListener;
    }

    public interface OnEtFilesClickListener {
        void onEtFilesClick(AuraHomeFileModel.FilesBean filesBean, int position, CheckBox checkBox);
    }

    private OnItemCheckListener onItemCheckListener;

    public void setOnItemCheckListener(OnItemCheckListener onItemCheckListener) {
        this.onItemCheckListener = onItemCheckListener;
    }

    public interface OnItemCheckListener {
        void onItemCheck(int position, AuraHomeFileModel.FilesBean filesBean, LinkedHashMap<String, String> isCheckedMap, int totalSize);
    }

}
