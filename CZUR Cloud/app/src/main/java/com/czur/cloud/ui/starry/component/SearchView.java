package com.czur.cloud.ui.starry.component;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;

import androidx.appcompat.widget.AppCompatEditText;

import com.czur.cloud.R;

public class SearchView extends AppCompatEditText {

    float searchSize = 0;
    float textSize = 0;
    int textColor = 0xFF000000;
    Drawable mDrawable;
    Paint paint;

    public SearchView(Context context, AttributeSet attrs) {
        super(context, attrs);
        InitResource(context, attrs);
        InitPaint();
        this.setBackground(getContext().getDrawable(R.drawable.starry_rec_10_bg_with_gray));
        int x = (int)getResources().getDimension(R.dimen.starryMargin15);
        this.setPadding(x, 0, x, 0);
        this.setSingleLine(true);
    }

    private void InitResource(Context context, AttributeSet attrs) {
        TypedArray mTypedArray = context.obtainStyledAttributes(attrs, R.styleable.SearchEditView);
        float density = context.getResources().getDisplayMetrics().density;
//        searchSize = mTypedArray.getDimension(R.styleable.SearchEditView_search_imagewidth, getContext().getResources().getDimension(R.dimen.starryMargin8) * density + 0.5F);
        searchSize = mTypedArray.getDimension(R.styleable.SearchEditView_search_imagewidth, getContext().getResources().getDimension(R.dimen.starryMargin15));
        textColor = mTypedArray.getColor(R.styleable.SearchEditView_search_textColor, getContext().getColor(R.color.starry_home_text_gray_color));
        textSize = mTypedArray.getDimension(R.styleable.SearchEditView_search_textSize, getContext().getResources().getDimension(R.dimen.starryMargin15));
        mTypedArray.recycle();
    }

    private void InitPaint() {
        paint = new Paint(Paint.ANTI_ALIAS_FLAG);
        paint.setColor(textColor);
        paint.setTextSize(textSize);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        DrawSearchIcon(canvas);
    }

    private void DrawSearchIcon(Canvas canvas) {
        if (this.getText().toString().length() == 0) {
            String searchWord = getContext().getString(R.string.starry_search_key);
            float textWidth = paint.measureText(searchWord);
            float textHeight = getFontLeading(paint);

            float dx = (getWidth() - searchSize - textWidth - 8) / 2;
            float dy = (getHeight() - searchSize) / 2;

            canvas.save();
            canvas.translate(getScrollX() + dx, getScrollY() + dy);
            if (mDrawable != null) {
                mDrawable.draw(canvas);
            }
            canvas.drawText(searchWord, getScrollX() + searchSize + 8, getScrollY() + (getHeight() - (getHeight() - textHeight) / 2) - paint.getFontMetrics().bottom - dy, paint);
            canvas.restore();
        }
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        if (mDrawable == null) {
            try {
                mDrawable = getContext().getResources().getDrawable(R.mipmap.starry_search);
                mDrawable.setBounds(0, 0, (int) searchSize, (int) searchSize);
            } catch (Exception e) {

            }
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        if (mDrawable != null) {
            mDrawable.setCallback(null);
            mDrawable = null;
        }
        super.onDetachedFromWindow();
    }

    public float getFontLeading(Paint paint) {
        Paint.FontMetrics fm = paint.getFontMetrics();
        return fm.bottom - fm.top;
    }

}
