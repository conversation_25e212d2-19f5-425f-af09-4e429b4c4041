package com.czur.cloud.event.aurahome;


import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.netty.bean.ReceivedMsgBodyBean;

/**
 * 上线通知
 */
public class ATCheckDeviceIsOnlineEvent extends BaseEvent {
    private String deviceUdid;
    private String dataBegin;

    private ReceivedMsgBodyBean.BodyBean.DataBean.DeviceStatusBean statusBean;

    public ATCheckDeviceIsOnlineEvent(EventType eventType, String deviceUdid, ReceivedMsgBodyBean.BodyBean.DataBean.DeviceStatusBean statusBean) {
        super(eventType);
        this.deviceUdid = deviceUdid;
        this.statusBean = statusBean;
    }
    public ATCheckDeviceIsOnlineEvent(EventType eventType, String deviceUdid, String  dataBegin) {
        super(eventType);
        this.deviceUdid = deviceUdid;
        this.dataBegin = dataBegin;
    }

    public ReceivedMsgBodyBean.BodyBean.DataBean.DeviceStatusBean getStatusBean() {
        return statusBean;
    }

    public String getDataBegin() {
        return dataBegin;
    }

    public String getDeviceUdid() {
        return deviceUdid;
    }
    @Override
    public boolean match(Object obj) {
        return true;
    }
}
