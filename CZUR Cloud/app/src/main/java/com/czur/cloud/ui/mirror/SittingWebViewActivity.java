package com.czur.cloud.ui.mirror;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.JavascriptInterface;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.badoo.mobile.util.WeakHandler;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.czur.cloud.R;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.market.MallFragment;

/**
 * Created by Yz on 2018/2/28.
 * Email：<EMAIL>
 */

public class SittingWebViewActivity extends BaseActivity implements View.OnClickListener {

    private FrameLayout webContainer;
    private WebView webView;
    private ImageView webviewBackBtn;
    private TextView webviewTitleTv;
    private String title;
    private String url;
    private RelativeLayout reloadWebviewRl;
    private TextView reloadBtn;
    private boolean isSuccess = false;
    private boolean isError = false;
    private boolean isFirstFinish = true;
    private WeakHandler handler;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        BarUtils.setStatusBarColor(this, 0,true);
        BarUtils.setStatusBarLightMode(getWindow(), true);
        setContentView(R.layout.activity_webview);
        showProgressDialog();
        initComponent();
        registerEvent();
    }

    private void initComponent() {
        title = getIntent().getStringExtra("title");
        url = getIntent().getStringExtra("url");
        handler = new WeakHandler();
        reloadWebviewRl = (RelativeLayout) findViewById(R.id.reload_webview_rl);
        reloadBtn = (TextView) findViewById(R.id.reload_btn);
        webviewBackBtn = (ImageView) findViewById(R.id.webview_back_btn);
        webviewTitleTv = (TextView) findViewById(R.id.webview_title_tv);
        webContainer = (FrameLayout) findViewById(R.id.web_frame);
        webviewTitleTv.setText(title);

        webView = new WebView(this);
        WebSettings settings = webView.getSettings();
        settings.setDomStorageEnabled(true);
        //解决一些图片加载问题
        settings.setJavaScriptEnabled(true);
        settings.setBlockNetworkImage(false);
        webView.setWebViewClient(webClient);
        webView.setWebChromeClient(new WebChromeClient() {

            @Override
            public void onProgressChanged(WebView view, int progress) {
                //当进度走到100的时候做自己的操作，我这边是弹出dialog
                if (progress == 100) {
                  hideProgressDialog();
                }
            }
        });
        webContainer.addView(webView);
        webView.addJavascriptInterface(new JSCallAndroidObject(), "jsCallAndroidObject");
        webView.loadUrl(url);
    }

    private void registerEvent() {
        if (NetworkUtils.isConnected()) {
            reloadWebviewRl.setVisibility(View.GONE);
        } else {
            reloadWebviewRl.setVisibility(View.VISIBLE);
        }

        webviewBackBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (webView.canGoBack()) {
                    webView.goBack(); //goBack()表示返回WebView的上一页面

                } else {
                    ActivityUtils.finishActivity(SittingWebViewActivity.this);
                }
            }
        });
        reloadBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showProgressDialog();
                isFirstFinish = true;
                webView.reload();
            }
        });
    }

    /***
     * 设置Web视图的方法
     */
    private WebViewClient webClient = new WebViewClient() {


        //处理网页加载失败时
        @Override
        public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
            super.onReceivedError(view, request, error);
            reloadProgress();

            isError = true;
            isSuccess = false;
            reloadWebviewRl.setVisibility(View.VISIBLE);
            webContainer.setVisibility(View.GONE);
        }

        @Override
        public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
            super.onReceivedError(view, errorCode, description, failingUrl);
            reloadProgress();
            isError = true;
            isSuccess = false;
            reloadWebviewRl.setVisibility(View.VISIBLE);
            webContainer.setVisibility(View.GONE);
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            reloadProgress();
            if (!isFirstFinish) {
                return;
            }
            isFirstFinish = false;

            if (!isError) {
                isSuccess = true;
                //回调成功后的相关操作
                reloadWebviewRl.setVisibility(View.GONE);
                webContainer.setVisibility(View.VISIBLE);
            } else {
                isError = false;
                reloadWebviewRl.setVisibility(View.VISIBLE);
                webContainer.setVisibility(View.GONE);
            }
        }

        @Override
        public boolean shouldOverrideUrlLoading(WebView view, String url) {
            Log.d("webview", "url: " + url);
            view.loadUrl(url);
            return true;
        }
    };

    private void reloadProgress() {
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                hideProgressDialog();
            }
        }, 600);
    }

    /**
     * @des: js交互
     * @params:
     * @return:
     */

    public class JSCallAndroidObject {
        private String TAG = MallFragment.JSCallAndroidObject.class.getSimpleName();

        @JavascriptInterface
        public String jsCallAndroid(String msg) {
            Uri uri = Uri.parse(msg);
            Intent intent = new Intent(Intent.ACTION_VIEW, uri);
            startActivity(intent);
            return "from Android";
        }

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            case R.id.normal_back_btn:
                ActivityUtils.finishActivity(this);
                break;

            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        if (webView != null) {
            webView.loadDataWithBaseURL(null, "", "text/html", "uft-8", null);
            webView.clearHistory();
            ((ViewGroup) webView.getParent()).removeView(webView);
            webView.destroy();
            webView = null;
        }
        super.onDestroy();
    }
}
