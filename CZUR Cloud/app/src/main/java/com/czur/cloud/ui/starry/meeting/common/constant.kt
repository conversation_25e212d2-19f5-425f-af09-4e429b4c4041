package com.czur.cloud.ui.starry.meeting.common

import okhttp3.MediaType.Companion.toMediaType

val JSON = "application/json; charset=utf-8".toMediaType()

/***** 时间单位 *****/
const val ONE_SECOND = 1000L
const val ONE_MINUTE = 60 * ONE_SECOND
const val ONE_HOUR = 60 * ONE_MINUTE

// 入会超时没有收到memberlist退出会议
const val JOIN_MEETING_TIMEOUT = ONE_SECOND * 15
const val JOIN_MEETING_TIMEOUT_OVERSEA = ONE_MINUTE * 1

val Int.second: Long
    get() = this * ONE_SECOND
val Int.minute: Long
    get() = this * ONE_MINUTE
val Int.hour: Long
    get() = this * ONE_HOUR

enum class StreamType {
    VIDEO,
    AUDIO,
    ALL
}

/**
 * 会议的显示模式
 */
enum class MeetingDisplayMode {
    SINGLE,
    GRID
}

/***** 会议设定 *****/
val DISPLAY_MODE_DEF = MeetingDisplayMode.SINGLE
const val SMART_FOCUS_DEF = true           // 自动检测发言人开关
const val ROOM_ALL_AUDIO_DEF = true           // 房间全部静音，默认非静音，即mic开
const val ROOM_LOCKED_DEF = false           // 房间锁定
const val MEETING_RECORD_DEF = false         // 会议是否录音


/**
 * 会议CMD
 * @param isTargetCMD: 这个命令是否是有指向的
 */
enum class MeetingCMD(val cmd: String, val isTargetCMD: Boolean = false) {
    REJECT("reject"),       // 拒绝加入会议
    JOIN("join"),           // 加入会议室
    EXIT("exit"),           // 自己退出
    STOP("stop"),           // 结束会议
    STEPOUT("stepOut"),           // 暂时离开
    MUTE_AUDIO("muteAudio", true),// 关闭音频
    OPEN_AUDIO("openAudio", true),// 打开音频
    MUTE_VIDEO("muteVideo", true),// 关闭视频
    OPEN_VIDEO("openVideo", true),// 打开视频
    HOST("host", true),               // 设置成员为管理员
    UNHOST("unhost", true),
    SHARE("share"),             // 开始分享
    STOP_SHARE("stopShare"),    // 停止分享
    QUIT_SHARE("quitShare"),    // 停止他人分享
    REMOVE("remove", true),           // 移除会议成员
    SYNC_SELF("audioVideoStatus"),   // 同步音视频状态
    UPDATE_NICKNAME("update_nickname", true),  // 37.会议中更新参会人名称

    START_RECORD("startRecord"),    // 开始会议录制
    STOP_RECORD("stopRecord"),      // 结束会议录制

    OTHER_JOINED("otherJoined"),      // 其他端加入会议时收到的消息

    CHECK_MEETING("check_meeting"), // 获取正在进行会议
    RECENT_MEETING("recent_meeting"), // 获取正在呼叫的会议

    HOLD("hold"),           // 接听页面点击【稍后】时，发送新的cmd指令【hold】

    TO_BACKGROUND("go_to_backend"),           // 放置后台【发送指令】
    TO_FRONT("back_to_the_frontend"),           // 回到前台【发送指令】

    AAA("AAA");

    companion object {
        /**
         * 根据枚举的[MeetingCMD.cmd] 找到对应类型
         */
        fun cmdOf(cmd: String): MeetingCMD {
            return MeetingCMD.values().find {
                it.cmd.equals(cmd, true)
            } ?: throw IllegalArgumentException("cmd:${cmd} 无法找到对应的枚举类型")
        }
    }
}

const val DEVICES_MODULE = "Starry"

const val MEETING_KEY_CMD = "cmd"
const val MEETING_KEY_UDID_TO = "udid_to"
const val MEETING_KEY_UDID_FROM = "udid_from"
const val MEETING_KEY_SUBJECT = "room_name" // 会议主题
const val MEETING_KEY_ROOM = "room"
const val MEETING_KEY_MEETING_NO = "meeting_no"

const val MEETING_KEY_HEAD_IMG = "headImage" // 头像
const val MEETING_KEY_NICK_FROM = "nickname_from"
const val MEETING_KEY_USER_ID_FROM = "userid_from"

const val MEETING_KEY_SHARE_STREAM = "share_stream"

const val MEETING_KEY_AUDIO_STATUS = "audioStatus"
const val MEETING_KEY_VIDEO_STATUS = "videoStatus"

const val MEETING_KEY_USERS = "users"       // 用户列表 users的key
const val MEETING_KEY_LOCKED = "locked"     // 用户列表 房间是否锁定的key
const val MEETING_KEY_IS_RECORD = "isRecoder"// 用户列表 会议是否正在录音
const val MEETING_KEY_META_DATA = "metadata" // 用户列表 额外属性(房间是否锁定)

const val MEETING_KEY_TARGET = "target" // 要控制的目标(打开/关闭音视频流时使用)

const val CAM_AUDIO_STATUS_CLOSE = "0"// 关闭
const val CAM_AUDIO_STATUS_OPEN = "1" // 打开
const val CAM_AUDIO_STATUS_NO = "-1" // -1时保持原状,不需要过多处理
/**
 * 会议的状态
 */
enum class MeetingStatus {
    CALLING,        // 呼叫中
    STARTING,       // 正在进行中
    STOPPED         // 没在开会
}