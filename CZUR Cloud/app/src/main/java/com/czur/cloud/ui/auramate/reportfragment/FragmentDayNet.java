package com.czur.cloud.ui.auramate.reportfragment;

import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Matrix;
import android.os.Bundle;
import android.os.Handler;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.model.AuraMateReportModelSub;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.auramate.AuraMateReportNewActivity;
import com.czur.cloud.ui.auramate.siterror.AuraMateErrorSitActivity;
import com.czur.cloud.ui.base.BaseFragment;
import com.czur.cloud.util.validator.Validator;
import com.github.mikephil.charting.charts.BarChart;
import com.github.mikephil.charting.charts.PieChart;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.PieData;
import com.github.mikephil.charting.data.PieDataSet;
import com.github.mikephil.charting.data.PieEntry;
import com.github.mikephil.charting.formatter.ValueFormatter;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet;
import com.github.mikephil.charting.listener.ChartTouchListener;
import com.github.mikephil.charting.listener.OnChartGestureListener;
import com.github.mikephil.charting.listener.OnChartValueSelectedListener;
import com.google.gson.reflect.TypeToken;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by Jason on 2020/12/12.
 */
public class FragmentDayNet extends BaseFragment {
    private final int BAR_MAX_COUNT = 7;
    private final float BAR_WIDTH = 0.4f;
    private final int SHOW_PIC_BEFORE_DAY = 3;

    private int currentIndex = 0;   //当前选中的X轴数据序号；
    private String typePageSize = "7";   //当前类型下的获取记录总数量；日：365，周：52，月：12
    private String currentReportDate;   //当前制定的记录的日期fromEnd；
    private String equipmentId, titleName, type, reportId;
    List<String> barChartDataListX = new ArrayList<>();
    List<Integer> barChartDataListY = new ArrayList<>();
    List<Integer> barChartDataListYError = new ArrayList<>();
    List<Integer> barChartDataListYError1 = new ArrayList<>();
    List<Integer> barChartDataListYError2 = new ArrayList<>();
    private boolean isReflashFlag = false;  //判断是否需要加载更多后，刷新界面；没有数据，无需刷新；
    private boolean isLoadMore;//是否加载更多
    private boolean canLoadLeft;//向左是否可以加载更多
    private boolean canLoadRight;//向左是否可以加载更多
    private String oldReportID = "";  // 防止重复加载更多

    private List<AuraMateReportModelSub> reportListDatas = new ArrayList<>();//接口返回的数据
    private List<AuraMateReportModelSub> reportAllListDatas = new ArrayList<>();//插入空值的数据；用于显示，查找
    //注意，获取到的网络数据是倒排序，reportListDatas也是倒排序；新的日期在前面，早的日期在后面
    //barchart显示的需要时正排序，即早的日期在前面，新的日期在最后

    private RelativeLayout emptyRl, dataRl;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private BarChart chart,chart2;
    private ImageView shareBtn;  //分享按钮
    private int curSelectValueX = 0;    //当前选中的bar，点击空白处，仍然选择当前的。
    private int oldListDatasCount = 0;  //加载数据前的数据总数

    private LinearLayout reportAlertLlBtn;
    private boolean isShowErrorPicture = false;
    private String showErrorPictureDate = "";
    private String showErrorPictureRelationId = "";

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_report_day, container, false);
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        reportId="";
        Bundle bundle = getArguments();
        if (bundle != null) {
            equipmentId = bundle.getString("equipmentId");
            titleName = bundle.getString("titleName");
            type = bundle.getString("type");
            reportId = bundle.getString("reportId");
        }
        if(type.equals("1")){
            typePageSize="15";
        }
        if(type.equals("2")){
            typePageSize="15";
        }
        if(type.equals("3")){
            typePageSize="12";
        }

        initComponent();
    }

    private void initComponent(){
        userPreferences = UserPreferences.getInstance();
        httpManager = HttpManager.getInstance();
        emptyRl = requireActivity().findViewById(R.id.empty_rl);
        dataRl = requireActivity().findViewById(R.id.data_rl);

        emptyRl.setVisibility(View.GONE);
        dataRl.setVisibility(View.GONE);

        isLoadMore = true;
        canLoadLeft = true;
        canLoadRight = true;

        reportAlertLlBtn = requireActivity().findViewById(R.id.tv_report_alert_ll);
        reportAlertLlBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                isShowErrorPicture = true;
                Intent intent = new Intent(getActivity(), AuraMateErrorSitActivity.class);
                intent.putExtra("isShowErrorPicture", isShowErrorPicture);
                intent.putExtra("showErrorPictureDate", showErrorPictureDate);
                intent.putExtra("showErrorPictureRelationId", showErrorPictureRelationId);
                intent.putExtra("equipmentId", equipmentId);
                ActivityUtils.startActivity(intent);
            }
        });
        reportAlertLlBtn.setClickable(false);

        // 初始化日周月的报告界面
        ininReportTypeView();

        showProgressDialog();

        //Only for Test
//        reportId = "12228";

        getReportListNew(equipmentId, reportId);

        ScrollView fragment_day_sv = (getActivity()).findViewById(R.id.fragment_day_sv);

        shareBtn = (getActivity()).findViewById(R.id.user_share_btn);
        shareBtn.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {

                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        AuraMateReportNewActivity mainActivity= (AuraMateReportNewActivity) getActivity();
                        mainActivity.setShareUrl(getActivity().getExternalFilesDir("apk").getAbsolutePath() + File.separator + "/report_test.png");
                        Bitmap shareBmp = mainActivity.getBitmapByView(fragment_day_sv);
                        mainActivity.setShareBmp(shareBmp);

                        if (BuildConfig.IS_OVERSEAS) {
                            mainActivity.requestCopyToSdPermission();
                        } else {
                            mainActivity.getShareImageUrl();
                        }
                    }
                }, 1000);

            }
        });

        // 正确率趋势
        // 严重错误率趋势
        chart = requireActivity().findViewById(R.id.chart1);
        chart2 = requireActivity().findViewById(R.id.chart2);

        chart.setOnChartValueSelectedListener(new OnChartValueSelectedListener() {
            @Override
            public void onValueSelected(Entry e, Highlight h) {
                int curX = (int) e.getX();

                if ((curX > -1) && (curX < barChartDataListX.size())){
                    String selectXName = barChartDataListX.get(curX);
                    int right = barChartDataListY.get(curX);
                    int error = barChartDataListYError.get(curX);
                    int error1 = barChartDataListYError1.get(curX);
                    int error2 = barChartDataListYError2.get(curX);

                    //当日有学习数据，正确率及错误率为0时，条形图点击切换。
                    //当日未学习，不显示数据，条形图不支持点击切换。还是上一次点击的条图形
                    if ((right > 0) || (error > 0) || (error1 > 0) || (error2 > 0)){
                        curSelectValueX = curX;
                    }else{
                        Highlight high = new Highlight(curSelectValueX, 0, 0);
                        high.setDataIndex(curSelectValueX);
                        chart.highlightValue(high, false);
                        return;
                    }

                    HighlightBarChartCurSelected(curSelectValueX);
                    chart2.moveViewToX(chart.getLowestVisibleX());

                    for (AuraMateReportModelSub entry:reportAllListDatas){
                        String fromEnd = entry.getFromEnd();
                        if (!Validator.isEmpty(fromEnd)){
                            fromEnd = ReportUtil.foramtDateTime(fromEnd, Integer.parseInt(type));
                        }else{
                            fromEnd = "";
                        }

                        if (fromEnd.equals(selectXName)){
                            //使用Activity的runOnUiThread()方法更新UI
                            getActivity().runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    initReportHorizontalView(entry);
                                }
                            });

                            break;
                        }
                    }
                }
            }

            @Override
            public void onNothingSelected() {
                HighlightBarChartCurSelected(curSelectValueX);
            }
        });

        chart2.setOnChartValueSelectedListener(new OnChartValueSelectedListener() {
            @Override
            public void onValueSelected(Entry e, Highlight h) {
                int curX = (int) e.getX();
                int curY = (int) e.getY();
                if ((curX > -1) && (curX < barChartDataListX.size())){
                    String selectXName = barChartDataListX.get(curX);
                    int right = barChartDataListY.get(curX);
                    int error = barChartDataListYError.get(curX);
                    int error1 = barChartDataListYError1.get(curX);
                    int error2 = barChartDataListYError2.get(curX);

                    //当日有学习数据，正确率及错误率为0时，条形图点击切换。
                    //当日未学习，不显示数据，条形图不支持点击切换。还是上一次点击的条图形
                    if ((right > 0) || (error > 0) || (error1 > 0) || (error2 > 0)){
                        curSelectValueX = curX;
                    }else{
                        Highlight high = new Highlight(curSelectValueX, 0, 0);
                        high.setDataIndex(curSelectValueX);
                        chart2.highlightValue(high, false);
                        return;
                    }

                    HighlightBarChartCurSelected(curSelectValueX);
                    chart.moveViewToX(chart2.getLowestVisibleX());

                    for (AuraMateReportModelSub entry:reportAllListDatas){
                        String fromEnd = entry.getFromEnd();
                        if (!Validator.isEmpty(fromEnd)){
                            fromEnd = ReportUtil.foramtDateTime(fromEnd, Integer.parseInt(type));
                        }else{
                            fromEnd = "";
                        }

                        if (fromEnd.equals(selectXName)){
                            //使用Activity的runOnUiThread()方法更新UI
                            getActivity().runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    initReportHorizontalView(entry);
                                }
                            });

                            break;
                        }
                    }
                }
            }

            @Override
            public void onNothingSelected() {
                HighlightBarChartCurSelected(curSelectValueX);
            }
        });

        chart.setOnChartGestureListener(new OnChartGestureListener() {

            @Override
            public void onChartLongPressed(MotionEvent me) {            }

            @Override
            public void onChartDoubleTapped(MotionEvent me) {            }

            @Override
            public void onChartSingleTapped(MotionEvent me) {            }

            @Override
            public void onChartFling(MotionEvent me1, MotionEvent me2, float velocityX, float velocityY) {            }

            @Override
            public void onChartScale(MotionEvent me, float scaleX, float scaleY) {            }

            @Override
            public void onChartGestureStart(MotionEvent me, ChartTouchListener.ChartGesture lastPerformedGesture) {
                isLoadMore = false;
            }

            @Override
            public void onChartGestureEnd(MotionEvent me, ChartTouchListener.ChartGesture lastPerformedGesture) {
                chart2.moveViewToX(chart.getLowestVisibleX());

                int leftX = (int) chart.getLowestVisibleX();//获取可视区域中，显示在x轴最左边的index，最小值
                int rightX = (int) chart.getHighestVisibleX();//最大值，最右边的
                int dataCount = reportAllListDatas.size();
                oldListDatasCount = dataCount;

                if(lastPerformedGesture == ChartTouchListener.ChartGesture.DRAG){
                    isLoadMore = true;
                    if((leftX == 0 || leftX == 1) && (rightX == BAR_MAX_COUNT || rightX == BAR_MAX_COUNT-1 )){
                        isLoadMore = false;
                        if (!canLoadLeft)
                            return;

                        //<==加载更多数据的操作
                        String report_id = String.valueOf(reportAllListDatas.get(0).getId());
                        String title = String.valueOf(reportAllListDatas.get(0).getTitle());
//                        getReportListBeforeMore(equipmentId, report_id);
                        if (!oldReportID.equals(report_id)) {
                            oldReportID = report_id;
                            getReportListBeforeMore(equipmentId, report_id);
                        }
                    }

                }
            }

            @Override
            public void onChartTranslate(MotionEvent me, float dX, float dY) {
                chart2.moveViewToX(chart.getLowestVisibleX());

                if(isLoadMore){
                    int leftX = (int) chart.getLowestVisibleX();//获取可视区域中，显示在x轴最左边的index，最小值
                    int rightX = (int) chart.getHighestVisibleX();//最大值，最右边的
                    int dataCount = reportAllListDatas.size();
                    oldListDatasCount = dataCount;

//                    if(leftX == 0 || leftX == 1){
                    if((leftX == 0 || leftX == 1) && (rightX == BAR_MAX_COUNT || rightX == BAR_MAX_COUNT-1 )){
                        isLoadMore = false;
                        if (!canLoadLeft)
                            return;

                        //<==加载更多数据的操作
                        String report_id = String.valueOf(reportAllListDatas.get(0).getId());
                        String title = String.valueOf(reportAllListDatas.get(0).getTitle());
//                        getReportListBeforeMore(equipmentId, report_id);
                        if (!oldReportID.equals(report_id)) {
                            oldReportID = report_id;
                            getReportListBeforeMore(equipmentId, report_id);
                        }
                    }

                }
            }
        });

        chart2.setOnChartGestureListener(new OnChartGestureListener() {
            @Override
            public void onChartGestureStart(MotionEvent me, ChartTouchListener.ChartGesture lastPerformedGesture) {            }

            @Override
            public void onChartGestureEnd(MotionEvent me, ChartTouchListener.ChartGesture lastPerformedGesture) {
                chart.moveViewToX(chart2.getLowestVisibleX());
            }

            @Override
            public void onChartLongPressed(MotionEvent me) {            }

            @Override
            public void onChartDoubleTapped(MotionEvent me) {            }

            @Override
            public void onChartSingleTapped(MotionEvent me) {            }

            @Override
            public void onChartFling(MotionEvent me1, MotionEvent me2, float velocityX, float velocityY) {            }

            @Override
            public void onChartScale(MotionEvent me, float scaleX, float scaleY) {            }

            @Override
            public void onChartTranslate(MotionEvent me, float dX, float dY) {
                chart.moveViewToX(chart2.getLowestVisibleX());
            }
        });
    }

    //高亮显示当前选中的条目
    private void HighlightBarChartCurSelected(int curx){
        Highlight high = new Highlight(curx, 0, 0);
        high.setDataIndex(curx);
        chart.highlightValue(high, false);
        chart2.highlightValue(high, false);
    }

    private void ininReportTypeView(){
        TextView report_name = requireActivity().findViewById(R.id.report_name);
        TextView tv_report_total_name = requireActivity().findViewById(R.id.tv_report_total_name);
        TextView tv_report_alert_name = requireActivity().findViewById(R.id.tv_report_alert_name);
        TextView tv_report_sit_name = requireActivity().findViewById(R.id.tv_report_sit_name);
        TextView tv_report_rank_name = requireActivity().findViewById(R.id.tv_report_rank_name);
        TextView tv_report_trend_name = requireActivity().findViewById(R.id.tv_report_trend_name);
        ImageView tv_report_alert_arrow_iv = requireActivity().findViewById(R.id.tv_report_alert_arrow_iv);

        tv_report_alert_arrow_iv.setVisibility(View.GONE);

        if (type.equals("1")){
            report_name.setText(R.string.aura_report_title_day);
            tv_report_total_name.setText(R.string.aura_report_day_total);
            tv_report_alert_name.setText(R.string.aura_report_day_alert);
            tv_report_sit_name.setText(R.string.aura_report_day_sit);
            tv_report_rank_name.setText(R.string.aura_report_day_rank);
            tv_report_trend_name.setText(R.string.aura_report_day_trend);
            tv_report_alert_arrow_iv.setVisibility(View.VISIBLE);
            reportAlertLlBtn.setClickable(true);
        }
        if (type.equals("2")){
            report_name.setText(R.string.aura_report_title_week);
            tv_report_total_name.setText(R.string.aura_report_week_total);
            tv_report_alert_name.setText(R.string.aura_report_week_alert);
            tv_report_sit_name.setText(R.string.aura_report_week_sit);
            tv_report_rank_name.setText(R.string.aura_report_week_rank);
            tv_report_trend_name.setText(R.string.aura_report_week_trend);
        }
        if (type.equals("3")){
            report_name.setText(R.string.aura_report_title_month);
            tv_report_total_name.setText(R.string.aura_report_month_total);
            tv_report_alert_name.setText(R.string.aura_report_month_alert);
            tv_report_sit_name.setText(R.string.aura_report_month_sit);
            tv_report_rank_name.setText(R.string.aura_report_month_rank);
            tv_report_trend_name.setText(R.string.aura_report_month_trend);
        }
    }

    /**
     * @des: 刷新接口
     */
    private OnRefreshListener onRefreshListener = refreshLayout -> getReportListNew(equipmentId, "");

    private List<AuraMateReportModelSub> getBeforeReportList(String equipmentId, String reoprt_id) {
        try {
            final MiaoHttpEntity<AuraMateReportModelSub> reportEntity = httpManager.request().
                    beforeUseReportList(
                            userPreferences.getUserId(),
                            equipmentId,
                            type,
                            reoprt_id,
                            typePageSize,
                            new TypeToken<List<AuraMateReportModelSub>>() {
                            }.getType());

            if (reportEntity == null) {
                return null;
            }
            if (reportEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return reportEntity.getBodyList();
            } else {
                return null;
            }

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private List<AuraMateReportModelSub> getAfterReportList(String equipmentId, String reoprt_id) {
        try {
            final MiaoHttpEntity<AuraMateReportModelSub> reportEntity = httpManager.request().
                    afterUseReportList(
                            userPreferences.getUserId(),
                            equipmentId,
                            type,
                            reoprt_id,
                            typePageSize,
                            new TypeToken<List<AuraMateReportModelSub>>() {
                            }.getType());

            if (reportEntity == null) {
                return null;
            }
            if (reportEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return reportEntity.getBodyList();
            } else {
                return null;
            }

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private List<AuraMateReportModelSub> getBeforeAfterReportList(String equipment_id, String reoprt_id) {
        try {
            final MiaoHttpEntity<AuraMateReportModelSub> reportEntity = httpManager.request().
                    beforeAfterUseReports(
                            userPreferences.getUserId(),
                            equipment_id,
                            type,
                            reoprt_id,
                            typePageSize,
                            new TypeToken<List<AuraMateReportModelSub>>() {
                            }.getType());

            if (reportEntity == null) {
                return null;
            }
            if (reportEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return reportEntity.getBodyList();
            } else {
                return null;
            }

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * @des: 刷新列表
     */
    public void getReportListNew(String equipment_id, String report_id) {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                if (report_id.equals("")) {
                    reportListDatas = getBeforeReportList(equipment_id, "");
                }
                else{
                    reportListDatas = getBeforeAfterReportList(equipment_id, report_id);
                }
                reportListDatas = reportListDatasReversal(reportListDatas);

                logI("=====getReportListNew.reportListDatas===");
                ReportUtil.printDatasLog(reportListDatas);

                if (Validator.isNotEmpty(reportListDatas)) {
                    if (reportListDatas.size() < BAR_MAX_COUNT) {
                        canLoadLeft = false;
                        canLoadRight = false;
                    }

                    //插值，补到reportAllListDatas里面
                    reportAllListDatas = reportListDatasAddSpace(reportListDatas);
                    ReportUtil.printDatasLog(reportAllListDatas);

                }else{
                    canLoadRight = false;
                    canLoadLeft = false;
                }
                return null;
            }

            @Override
            public void onSuccess(Void result) {
                refreshFiles();
                showEmpty();
                hideProgressDialog();
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                showEmpty();
                hideProgressDialog();
            }
        });
    }

    public void getReportListBeforeMore(String equipment_id, String report_id) {
        logI("getReportListBeforeMore.equipment_id.report_id=="+equipment_id+";"+ report_id);

        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                isReflashFlag = false;
                reportListDatas = getBeforeReportList(equipment_id, report_id);
                if (Validator.isNotEmpty(reportListDatas)) {
                    if (reportListDatas.size() < BAR_MAX_COUNT)
                        canLoadLeft = false;

                    isReflashFlag = true;
                    //插值，补到reportAllListDatas里面
                    reportListDatas = reportListDatasReversal(reportListDatas);
                    ReportUtil.printDatasLog(reportListDatas);

                    ///////
                    reportAllListDatas.addAll(0,reportListDatas);
                    reportAllListDatas = reportListDatasAddSpace(reportAllListDatas);
                    currentIndex = reportAllListDatas.size()-oldListDatasCount-1;
//                    currentIndex = reportListDatas.size()-1;

                    ReportUtil.printDatasLog(reportAllListDatas);
                }else{
//                        showMessage("没有更多的数据了。");
                    canLoadLeft = false;
                    isReflashFlag = false;
                }

                return null;
            }

            @Override
            public void onSuccess(Void result) {
                if (isReflashFlag){
                    refreshBarChart();
                    showEmpty();
                }
                hideProgressDialog();
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                hideProgressDialog();
            }
        });
    }

    public void getReportListAfterMore(String equipment_id, String report_id) {

        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                isReflashFlag = false;
                reportListDatas = getAfterReportList(equipment_id, report_id);
                if (Validator.isNotEmpty(reportListDatas)) {
                    if (reportListDatas.size() < BAR_MAX_COUNT)
                        canLoadRight = false;

                    isReflashFlag = true;
                    //插值，补到reportAllListDatas里面
                    reportListDatas = reportListDatasReversal(reportListDatas);
                    ReportUtil.printDatasLog(reportListDatas);

                    ///////
                    reportAllListDatas.addAll(reportListDatas);
                    reportAllListDatas = reportListDatasAddSpace(reportAllListDatas);
//                    currentIndex = reportAllListDatas.size()-1;
                    ReportUtil.printDatasLog(reportAllListDatas);
                }else{
//                        showMessage("没有更多的数据了。");
                    canLoadRight = false;
                    isReflashFlag = false;
                }
                return null;
            }

            @Override
            public void onSuccess(Void result) {
                if (isReflashFlag){
                    refreshBarChart();
                    showEmpty();
                }
                hideProgressDialog();
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                hideProgressDialog();
            }
        });
    }

    //反转数据，并格式化日期
    private List<AuraMateReportModelSub> reportListDatasReversal(List<AuraMateReportModelSub> reportDatas) {
        List<AuraMateReportModelSub> retDatas = new ArrayList<>();
        List<AuraMateReportModelSub> tmpDatasRev = new ArrayList<>();

        if (Validator.isEmpty(reportDatas)){
            return null;
        }

        int count = reportDatas.size();
        for (int i = 0;i < count; i++) {
            AuraMateReportModelSub sub = reportDatas.get(i);
            String sEndDate = String.valueOf(sub.getFromEnd());
//            String sEndDateSplit = sEndDate;
            sEndDate = ReportUtil.foramtDateTime(sEndDate, Integer.parseInt(type));
            sub.setFromEnd(sEndDate);

            // 针对 周 的情况，需要折行
//            if (type.equals("2")){
//                sEndDateSplit =  ReportUtil.foramtDateTimeSplit(sEndDateSplit);
//                sub.setFromEnd(sEndDateSplit);
//            }
            tmpDatasRev.add(0,sub);
        }
        return tmpDatasRev;
    }

    //接口获取的数据插值补充
    private List<AuraMateReportModelSub> reportListDatasAddSpace(List<AuraMateReportModelSub> reportDatas) {
        List<AuraMateReportModelSub> retDatas = new ArrayList<>();
        ArrayList<String> retList = new ArrayList<>();
        int count = reportDatas.size();

        if (count > 1) {
            for ( int indx=0; indx<count-1; indx++){
                String sStartDate = String.valueOf(reportDatas.get(indx).getFromEnd());
                String sEndDate = String.valueOf(reportDatas.get(indx + 1).getFromEnd());

                sStartDate = ReportUtil.foramtDateTime(sStartDate, Integer.parseInt(type));
                sEndDate = ReportUtil.foramtDateTime(sEndDate, Integer.parseInt(type));

                if (sStartDate.equals(sEndDate)) {
                    retDatas.add(reportDatas.get(indx));
                }else {
                    retList = ReportUtil.insertDateList(sStartDate, sEndDate, Integer.parseInt(type));
                    retDatas.add(reportDatas.get(indx));
                    for (int i=1;i<retList.size();i++){
                        AuraMateReportModelSub sub = new AuraMateReportModelSub();
                        sub.setId(0);
                        sub.setType(Integer.parseInt(type));
                        sub.setTitle("");
                        sub.setFromEnd(retList.get(i));
                        sub.setRightProportion("0");
                        sub.setSeriousProportion("0");//seriousProportion
                        sub.setModerateProportion("0");//moderateProportion
                        sub.setMildProportion("0");//mildProportion
                        retDatas.add(sub);
                    }
                    //tmpDatas.add(tmpDatasRev.get(indx+1));不添加，给下一个留着
                }
            }
            //最后再加上最后一个数据
            retDatas.add(reportDatas.get(count-1));
        }else {
            retDatas = reportDatas;
        }

        return retDatas;
    }

    private void refreshBarChart() {

        //对barChartDataListX等数据初始化
        initBarChartDataListFromServer();

        //刷新柱状图
        reflashChart(1, chart);
        reflashChart(2, chart2);

        //将当前视口的左侧（边）到指定的 x 值。
        chart.moveViewToX(currentIndex);

    }

    //对要在图表展示的数据进行初始化赋值操作
    private void initBarChartDataListFromServer(){
        barChartDataListX.clear();
        barChartDataListY.clear();
        barChartDataListYError.clear();
        barChartDataListYError1.clear();
        barChartDataListYError2.clear();

        //获取barChart的数据
        for (AuraMateReportModelSub entry: reportAllListDatas){
            String reportType = entry.getType()+"";

            if (!reportType.equals(type))
                continue;

            String right = entry.getRightProportion();
            if (Validator.isEmpty(right))
                right = "0";
            int rightProportion = Integer.parseInt(right);
            String error = entry.getSeriousProportion();
            if (Validator.isEmpty(error))
                error = "0";
            int seriousProportion = Integer.parseInt(error);
            String error1 = entry.getMildProportion();
            if (Validator.isEmpty(error1))
                error1 = "0";
            int mildProportion = Integer.parseInt(error1);
            String error2 = entry.getModerateProportion();
            if (Validator.isEmpty(error2))
                error2 = "0";
            int moderateProportion = Integer.parseInt(error2);

            String fromEnd = entry.getFromEnd();
            barChartDataListX.add(fromEnd);
            barChartDataListY.add(rightProportion);
            barChartDataListYError.add(seriousProportion);
            barChartDataListYError1.add(mildProportion);
            barChartDataListYError2.add(moderateProportion);
        }

        //对于少于7天的数据，补齐缺少的，让柱状图靠右对齐
        if (barChartDataListX.size()<BAR_MAX_COUNT){
            int x = BAR_MAX_COUNT - barChartDataListX.size();
            for (int i=0; i<x; i++){
                barChartDataListX.add(0, "");
                barChartDataListY.add(0,0);
                barChartDataListYError.add(0,0);
                barChartDataListYError1.add(0,0);
                barChartDataListYError2.add(0,0);
            }
        }

    }

    private void refreshFiles() {

        //初始化绘制统计数据
        if (Validator.isEmpty(reportAllListDatas))
            return;

        initBarChartDataListFromServer();

        curSelectValueX = barChartDataListX.size()-1;
        AuraMateReportModelSub armSub = reportAllListDatas.get(reportAllListDatas.size()-1);

        if (!reportId.equals("")){
            for (int indx=0; indx<reportAllListDatas.size()-1; indx++){
                String repID = String.valueOf(reportAllListDatas.get(indx).getId());
                if (repID.equals(reportId)){
                    curSelectValueX = indx;
                    AuraMateReportModelSub armSub1 = reportAllListDatas.get(indx);
                    if (Validator.isNotEmpty(armSub1))
                        armSub = armSub1;
                    break;
                }
            }
        }
        initReportHorizontalView(armSub);

        int allCount= reportAllListDatas.size();
        if (allCount<BAR_MAX_COUNT){
            int x = BAR_MAX_COUNT - allCount;
            curSelectValueX = curSelectValueX +x;
        }

        initChart(1, chart);
        initChart(2, chart2);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    private void drawChart(PieChart pieChart, AuraMateReportModelSub entity) {
        pieChart.setUsePercentValues(true);
        pieChart.getDescription().setEnabled(false);
        pieChart.setDrawHoleEnabled(true);
        pieChart.setHoleColor(Color.WHITE);
        pieChart.setHoleRadius(60);
        pieChart.setDrawCenterText(false);
        pieChart.setDrawEntryLabels(false);
        pieChart.setRotationAngle(-90);
        pieChart.getLegend().setEnabled(false);
        pieChart.setRotationEnabled(false);
        pieChart.setHighlightPerTapEnabled(false);
        pieChart.setExtraOffsets(0, 0, 0, 0);
        ArrayList<PieEntry> entries = new ArrayList<>();

        String rightProportion= entity.getRightProportion();
        String mildProportion=entity.getMildProportion();
        String moderateProportion=entity.getModerateProportion();
        String seriousProportion=entity.getSeriousProportion();
        if (Validator.isEmpty(rightProportion)){
            rightProportion = "";
        }
        if (Validator.isEmpty(moderateProportion)){
            moderateProportion = "";
        }
        if (Validator.isEmpty(mildProportion)){
            mildProportion = "";
        }
        if (Validator.isEmpty(seriousProportion)){
            seriousProportion = "";
        }
        entries.add(new PieEntry(Float.parseFloat(rightProportion)));
        entries.add(new PieEntry(Float.parseFloat(mildProportion)));
        entries.add(new PieEntry(Float.parseFloat(moderateProportion)));
        entries.add(new PieEntry(Float.parseFloat(seriousProportion)));

        PieDataSet dataSet = new PieDataSet(entries, "");
        dataSet.setColors(getActivity().getResources().getColor(R.color.green_8ae5b1),
                getActivity().getResources().getColor(R.color.yellow_faec94),
                getActivity().getResources().getColor(R.color.orange_fbb779),
                getActivity().getResources().getColor(R.color.red_f07575));
        dataSet.setDrawValues(false);
        dataSet.setDrawIcons(false);

        PieData data = new PieData(dataSet);
        data.setDrawValues(false);
        data.setHighlightEnabled(false);
        pieChart.setData(data);
        pieChart.invalidate();
    }

    private void initReportHorizontalView(AuraMateReportModelSub reportData) {

        //初始化标题
        TextView report_title = (requireActivity()).findViewById(R.id.report_title);
        TextView report_title2 = (requireActivity()).findViewById(R.id.report_title2);
        report_title.setText(titleName);

        //"title": "2020-12-16日报",  ==> 日报(2020.12.16)
        String sDateTime = reportData.getTitle();
        //只有日报才计算,可以点击错误坐姿
//        reportAlertLlBtn.setClickable(false);
        if (type.equals("1")) {
            showErrorPictureRelationId = reportData.getRelationId();
            String reportDate = sDateTime.substring(0, sDateTime.length() - 2);
            showErrorPictureDate = reportDate;
            String reportDateFormate = ReportUtil.foramtDateTime(reportDate, "yyyy.MM.dd");
            String dateBeforeThree = ReportUtil.getBeforeDay("", SHOW_PIC_BEFORE_DAY, "yyyy.MM.dd");
            isShowErrorPicture = ReportUtil.isDateOneBigger(reportDateFormate, dateBeforeThree);
        }

        String txtTitle = ReportUtil.getFormateReportTitle(sDateTime, type);
        report_title2.setText(txtTitle);

        TextView tvRight, tvMicroError, tvError, tvSeriousError;
        PieChart pieChart;
        TextView tv_report_total_value,
                tv_report_alert_value,
                tv_report_sit_value,
                tv_report_rank_value,
                tv_report_trend_value;

        tvRight = getActivity().findViewById(R.id.tv_right);
        tvMicroError = getActivity().findViewById(R.id.tv_micro_error);
        tvError = getActivity().findViewById(R.id.tv_error);
        tvSeriousError = getActivity().findViewById(R.id.tv_serious_error);
        pieChart = getActivity().findViewById(R.id.pie_chart);
        tv_report_total_value = getActivity().findViewById(R.id.tv_report_total_value);
        tv_report_alert_value = getActivity().findViewById(R.id.tv_report_alert_value);
        tv_report_sit_value = getActivity().findViewById(R.id.tv_report_sit_value);
        tv_report_rank_value = getActivity().findViewById(R.id.tv_report_rank_value);
        tv_report_trend_value = getActivity().findViewById(R.id.tv_report_trend_value);

        drawChart(pieChart, reportData);

        tvRight.setText(reportData.getRightProportion());
//        tvMicroError.setText(reportData.getModerateProportion());
//        tvError.setText(reportData.getMildProportion());
        tvMicroError.setText(reportData.getMildProportion());//轻微
        tvError.setText(reportData.getModerateProportion());//中度
        tvSeriousError.setText(reportData.getSeriousProportion());

        int t = reportData.getDayUsingDuration();
        String time = ReportUtil.getDayUsingAllTime(t);
        tv_report_total_value.setText(time);
        tv_report_alert_value.setText(reportData.getDayRemindCount() + "" + getString(R.string.aura_report_dayusing_times));
//        tv_report_alert_value.setText(reportData.getDayRemindCount() + "次" + " - id:" + reportData.getId());

        String rightName = reportData.getLevel();
        if (Validator.isEmpty(rightName))
            rightName = getString(R.string.aura_report_sit_default);
        tv_report_sit_value.setText(rightName);

        String aRank =reportData.getRank();
        if (Validator.isEmpty(aRank))
            aRank = "";
        tv_report_rank_value.setText(getString(R.string.aura_report_rank_pre) + "" + aRank);

        tv_report_trend_value.setText(reportData.getTrend());

    }

    private void showEmpty() {
        if (Validator.isEmpty(reportAllListDatas)){
            emptyRl.setVisibility(View.VISIBLE);
            dataRl.setVisibility(View.GONE);
            shareBtn.setImageResource(R.mipmap.btn_share_gray);
            shareBtn.setEnabled(false);
            return;
        }
        if (reportAllListDatas.size() > 0) {
            emptyRl.setVisibility(View.GONE);
            dataRl.setVisibility(View.VISIBLE);
            shareBtn.setImageResource(R.mipmap.btn_share);
            shareBtn.setEnabled(true);

        } else {
            emptyRl.setVisibility(View.VISIBLE);
            dataRl.setVisibility(View.GONE);
            shareBtn.setImageResource(R.mipmap.btn_share_gray);
            shareBtn.setEnabled(false);
        }
    }

    private void reflashChart(int typeA, BarChart chart){

        XAxis xAxis = chart.getXAxis();

        xAxis.setValueFormatter(new ValueFormatter() {
            @Override
            public String getFormattedValue(float value) {
                if ((int)value > barChartDataListX.size()-1)
                    return "";
                return barChartDataListX.get((int)value);
            }
        });

        int lenSize = barChartDataListY.size();
        if (typeA ==2){
            lenSize = barChartDataListYError.size();
        }

        // setting data
        ArrayList<BarEntry> values = new ArrayList<>();
        for (int i = 0; i < lenSize; i++) {
            if (typeA ==2) {
                values.add(new BarEntry(i, barChartDataListYError.get(i)));
            }else{
                values.add(new BarEntry(i, barChartDataListY.get(i)));
            }
        }

        BarDataSet set1;
        set1 = new BarDataSet(values, "Data Set");
        set1.setDrawValues(false);
        set1.setColor(Color.argb(255,138, 229, 177));  //柱子的颜色 8a e5 b1
        if (typeA == 2){
            set1.setColor(Color.argb(255,240, 117, 117));  //柱子的颜色 f07575
        }
        set1.setHighLightAlpha(45);

        ArrayList<IBarDataSet> dataSets = new ArrayList<>();
        dataSets.add(set1);

        BarData data = new BarData(dataSets);
        data.setBarWidth(BAR_WIDTH);
        chart.setData(data);
        chart.setFitBars(true);

        //setVisibleXRange是设置可见的x范围，我设置成(0, 5)之后，
        // 就只会显示序号0到5的内容(6项)。往左滑才会显示第7,8,9…最后一项。
        chart.setVisibleXRange(0,BAR_MAX_COUNT);

        chart.notifyDataSetChanged();
        chart.invalidate();
    }

    public void initChart(int typeA, BarChart chart){

        chart.getDescription().setEnabled(false);

        // if more than 60 entries are displayed in the chart, no values will be drawn
        chart.setMaxVisibleValueCount(60);

        //是否能够缩放
        chart.setPinchZoom(false);
        //允许X轴缩放
        chart.setScaleXEnabled(false);
        //允许Y轴缩放
        chart.setScaleYEnabled(false);

        chart.setDrawBarShadow(false);
        chart.setDrawGridBackground(false);

        XAxis xAxis = chart.getXAxis();
        YAxis leftAxis = chart.getAxisLeft();
        YAxis rightAxis = chart.getAxisRight();

        xAxis.setPosition(XAxis.XAxisPosition.BOTTOM);
        xAxis.setGranularity(1f);
        xAxis.setLabelCount(BAR_MAX_COUNT, false);

        // add a nice and smooth animation
        chart.animateY(1000);

        xAxis.setValueFormatter(new ValueFormatter() {
            @Override
            public String getFormattedValue(float value) {
                if ((int)value > barChartDataListX.size()-1)
                    return "";

                String ret1 = barChartDataListX.get((int)value);
                String ret = ret1;
                //仅仅针对周的情况，需要旋转显示
                if (type.equals("2")) {
                    String[] strArray = ret1.split("-");
                    if (strArray.length > 1) {
                        String strFrom = strArray[0];
                        String strEnd = strArray[1];
                        ret =  strFrom + "-\n" + strEnd;
                    }
                }
                return ret;
            }
        });

        leftAxis.setValueFormatter(new ValueFormatter() {
            @Override
            public String getFormattedValue(float value) {
                if (value>0)
                    return ((int) (value)) + "%";
                else{
                    return "";
                }
            }
        });
        leftAxis.setLabelCount(5, false);
        leftAxis.setTextColor(Color.rgb(128,128,128));
        xAxis.setTextColor(Color.rgb(128,128,128));

        chart.getAxisLeft().setDrawAxisLine(false);
        chart.getXAxis().setDrawAxisLine(false);

        chart.getAxisRight().setEnabled(false);
        chart.getLegend().setEnabled(false);

        chart.setDrawBorders(false); ////是否在折线图上添加边框
        // 如果没有数据的时候，会显示这个，类似ListView的EmptyView
        chart.setDrawGridBackground(true); // 是否显示表格颜色
        chart.setGridBackgroundColor(Color.argb(255, 240,240,240)); // 表格的的颜色，在这里是是给颜色设置一个透明度
        //是否展示网格线
//        chart.setDrawGridBackground(false);
        xAxis.setDrawGridLines(false);
        rightAxis.setDrawGridLines(false);
        leftAxis.setDrawGridLines(false);

        chart.setTouchEnabled(true);//设置是否可以触摸，如为false，则不能拖动，缩放等
        chart.setDragEnabled(true); //设置是否可以拖拽，缩放

        int lenSize = barChartDataListY.size();

        if (typeA ==2){
            lenSize = barChartDataListYError.size();
        }

        leftAxis.setYOffset(10f);//这样会向下偏移 50%。我也不知道为啥，自己试出来的
        float xSize = 0.95f;

        // setting data
        ArrayList<BarEntry> values = new ArrayList<>();

        for (int i = 0; i < lenSize; i++) {
            if (typeA ==2) {
                values.add(new BarEntry(i, barChartDataListYError.get(i)*xSize));
            }else{
                values.add(new BarEntry(i, barChartDataListY.get(i)*xSize));
            }
        }

        xAxis.setAxisMinimum(-0.5f);    //X轴数据向右偏移一些
//        xAxis.setAxisMinimum(100f);     //
        leftAxis.setAxisMinimum(0f);    //最小刻度值
        leftAxis.setAxisMaximum(100f);  // the axis maximum is 100

        chart.setExtraBottomOffset(2.2f * 10f);
        xAxis.setTextSize(10f);
        chart.setXAxisRenderer(new CustomXAxisRenderer(chart.getViewPortHandler(), chart.getXAxis(), chart.getTransformer(YAxis.AxisDependency.LEFT)));

        //仅仅针对周的情况，需要旋转显示
//        if (type.equals("2")) {
//            //X轴文字斜着显示
//            chart.getXAxis().setLabelRotationAngle(45);
//        }

        BarDataSet set1;

        set1 = new BarDataSet(values, "Data Set");
        set1.setDrawValues(false);

        set1.setColor(Color.argb(255,138, 229, 177));  //柱子的颜色 8a e5 b1
        if (typeA == 2){
            set1.setColor(Color.argb(255,240, 117, 117));  //柱子的颜色 f07575
        }
        set1.setHighLightAlpha(45);

        ArrayList<IBarDataSet> dataSets = new ArrayList<>();
        dataSets.add(set1);

        BarData data = new BarData(dataSets);
        data.setBarWidth(BAR_WIDTH);
        chart.setData(data);
        chart.setFitBars(true);

        //setVisibleXRange是设置可见的x范围，我设置成(0, 5)之后，
        // 就只会显示序号0到5的内容(6项)。往左滑才会显示第7,8,9…最后一项。
        chart.setVisibleXRange(0,BAR_MAX_COUNT);

//        将当前视口的左侧（边）到指定的 x 值。
        chart.moveViewToX(curSelectValueX);
//        chart.centerViewTo(curSelectValueX, 0, YAxis.AxisDependency.LEFT );

        // 将坐标移动到最新
        // 此代码将刷新图表的绘图
        Highlight high = new Highlight(curSelectValueX, 0, 0);
        high.setDataIndex(curSelectValueX);
        chart.highlightValue(high, false);
//        setChartData(chart);

        chart.invalidate();
    }

    private void setChartData(BarChart chart){
        Matrix m = new Matrix();
        m.postScale(scaleNum(20), 1f);//两个参数分别是x,y轴的缩放比例。例如：将x轴的数据放大为之前的1.5倍
        chart.getViewPortHandler().refresh(m, chart, false);//将图表动画显示之前进行缩放
    }

    //30个横坐标时，缩放4f是正好的。
    private float scalePercent = 4f/30f;

    private float scaleNum(int xCount){
        return xCount * scalePercent;
    }

}
