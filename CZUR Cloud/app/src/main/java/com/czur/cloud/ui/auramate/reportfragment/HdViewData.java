package com.czur.cloud.ui.auramate.reportfragment;

public class HdViewData {

	private String filename;
	private String full_filename; //全路径文件名
	private String oss_bucket;
	private String oss_key;
	private boolean isFav;	//是否已经收藏,默认false

	public HdViewData(String filename, String oss_key, String oss_bucket) {
		this.filename=filename;
		this.oss_bucket=oss_bucket;
		this.oss_key=oss_key;
		this.isFav = false;
	}

	public HdViewData(String filename, String fullname, String oss_key, String oss_bucket) {
		this.filename=filename;
		this.oss_bucket=oss_bucket;
		this.oss_key=oss_key;
		this.isFav = false;
		this.full_filename = fullname;
	}

	public String getFilename() {
		return filename;
	}

	public String getOss_bucket() {
		return oss_bucket;
	}

	public String getOss_key() {
		return oss_key;
	}

	public boolean isFav() {		return isFav;	}

	public void setFav(boolean fav) {		isFav = fav;	}

	public String getFull_filename() {
		return full_filename;
	}

}
