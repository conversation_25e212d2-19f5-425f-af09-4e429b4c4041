package com.czur.cloud.util

import android.os.Build
import android.text.TextUtils
import android.util.Log
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStreamReader
import java.util.*

object VendorUtils {

    private const val TAG = "Rom"
    private const val ROM_MIUI = "MIUI"
    private const val ROM_EMUI = "EMUI"
    private const val ROM_FLYME = "FLYME"
    private const val ROM_OPPO = "OPPO"

    //    public static final String ROM_SMARTISAN = "SMARTISAN";
    private const val ROM_VIVO = "VIVO"

    //    public static final String ROM_QIKU = "QIKU";
    private const val ROM_ONEPLUS = "ONEPLUS"
    private const val ROM_REALME = "REALME"
    private const val KEY_VERSION_MIUI = "ro.miui.ui.version.name"
    private const val KEY_VERSION_EMUI = "ro.build.version.emui"
    private const val KEY_VERSION_OPPO = "ro.build.version.opporom"

    //    private static final String KEY_VERSION_SMARTISAN = "ro.smartisan.version";
    private const val KEY_VERSION_VIVO = "ro.vivo.os.version"
    private const val KEY_BRAND = "ro.product.brand"

    fun check(): RomEnum {
        return if (!TextUtils.isEmpty(getProp(KEY_VERSION_MIUI))) {
            RomEnum.XIAOMI
        } else if (!TextUtils.isEmpty(getProp(KEY_VERSION_EMUI))) {
            RomEnum.HUAWEI
        } else if (!TextUtils.isEmpty(getProp(KEY_VERSION_OPPO))) {
            RomEnum.OPPO
        } else if (!TextUtils.isEmpty(getProp(KEY_VERSION_VIVO))) {
            RomEnum.VIVO
        } else {
            val sVersion = Build.DISPLAY
            if (sVersion.uppercase(Locale.CHINA).contains(ROM_FLYME)) {
                RomEnum.MEIZU
            } else if (sVersion.uppercase(Locale.CHINA).contains(ROM_ONEPLUS) ||
                !TextUtils.isEmpty(getProp(KEY_BRAND)) && getProp(
                    KEY_BRAND
                )?.uppercase(Locale.CHINA) ?: "" == ROM_ONEPLUS
            ) {
                RomEnum.ONEPLUS
            } else if (sVersion.uppercase(Locale.CHINA).contains(ROM_REALME) ||
                !TextUtils.isEmpty(getProp(KEY_BRAND)) && getProp(
                    KEY_BRAND
                )?.toUpperCase(Locale.CHINA) ?: "" == ROM_REALME
            ) {
                RomEnum.REALME
            } else {
                RomEnum.OTHER
            }
        }
    }

    private fun getProp(name: String): String? {
        val line: String
        var input: BufferedReader? = null
        try {
            val p = Runtime.getRuntime().exec("getprop $name")
            input = BufferedReader(InputStreamReader(p.inputStream), 1024)
            line = input.readLine()
            input.close()
        } catch (ex: IOException) {
            Log.e(TAG, "Unable to read prop $name", ex)
            return null
        } finally {
            if (input != null) {
                try {
                    input.close()
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
        }
        return line
    }

    enum class RomEnum(val rom: String) {
        HUAWEI("huawei"),
        XIAOMI("xiaomi"),
        OPPO("oppo"),
        ONEPLUS("oneplus"),
        REALME("realme"),
        VIVO("vivo"),
        MEIZU("meizu"),
        OTHER("other");

        companion object {
            fun getRomByStr(str: String?): RomEnum? {
                if (str == null) {
                    return null
                }
                for (rom in values()) {
                    if (rom.rom == str) {
                        return rom
                    }
                }
                return null
            }
        }
    }
}