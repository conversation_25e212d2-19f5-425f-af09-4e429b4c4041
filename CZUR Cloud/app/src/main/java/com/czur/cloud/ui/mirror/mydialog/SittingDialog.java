package com.czur.cloud.ui.mirror.mydialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import com.czur.cloud.R;

public class SittingDialog extends Dialog implements View.OnClickListener {

    private TextView mTitle, mContent;
    private TextView mConfirm, mCancel;

    private Context mContext;
    private String content;
    private OncloseListener listener;
    private String positiveName;
    private String negativeName;
    private String title;
    private boolean onlyShowOK = false; //仅显示确定按钮
    private boolean isGrayCancel = false; //取消按钮灰色

    public SittingDialog(Context context) {
        super(context);
        this.mContext = context;
    }

    public SittingDialog(Context context, int themeResId, String content) {
        super(context, themeResId);
        this.mContext = context;
        this.content = content;
    }

    public SittingDialog(Context context, int themeResId, OncloseListener listener) {
        super(context, themeResId);
        this.mContext = context;
        this.listener = listener;
    }

    public SittingDialog(Context context, int themeResId, String content, OncloseListener listener) {
        super(context, themeResId);
        this.mContext = context;
        this.content = content;
        this.listener = listener;
    }

    protected SittingDialog(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
        this.mContext = context;
    }

    /**
     * 设置弹框标题
     * @param title 标题内容
     * @return
     */
    public SittingDialog setTitle(String title) {
        this.title = title;
        return this;
    }

    /**
     * 设置弹框的提示内容
     * @param content 弹框的提示内容
     * @return
     */
    public SittingDialog setContent(String content) {
        this.content = content;
        return this;
    }

    /**
     * 设置弹框确认键的内容
     * @param name 确认键显示内容
     * @return
     */
    public SittingDialog setPositiveButton(String name) {
        this.positiveName = name;
        return this;
    }

    public SittingDialog setOneButton(boolean flag){
        this.onlyShowOK = flag;
        return this;
    }

    public SittingDialog setGrayCancelButton(boolean flag){
        this.isGrayCancel = flag;
        return this;
    }


    /**
     * 设置弹框取消键的内容
     * @param name 取消键显示内容
     * @return
     */
    public SittingDialog setNegativeButton(String name) {
        this.negativeName = name;
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.my_sitting_dialog);
        setCanceledOnTouchOutside(false);
        mTitle = findViewById(R.id.dialog_title);
        mContent = findViewById(R.id.dialog_content);
        mConfirm = findViewById(R.id.confirm);
        mCancel = findViewById(R.id.cancel);

        mConfirm.setOnClickListener(this);
        mCancel.setOnClickListener(this);

        if (onlyShowOK){
            mCancel.setVisibility(View.GONE);
            findViewById(R.id.sep).setVisibility(View.GONE);
        }
        if (isGrayCancel){
            mCancel.setTextColor(this.getContext().getColor(R.color.gray_5e));
        }
        mTitle.setText(this.title);
        mConfirm.setText(this.positiveName);
        mCancel.setText(this.negativeName);
        mContent.setText(this.content);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.confirm:
                if (listener != null) {
                    listener.onClick(true);
                }
                this.dismiss();
                break;
            case R.id.cancel:
                if (listener != null) {
                    listener.onClick(false);
                }
                this.dismiss();
                break;
        }
    }

    public interface OncloseListener {
        void onClick(boolean confirm);
    }
}
