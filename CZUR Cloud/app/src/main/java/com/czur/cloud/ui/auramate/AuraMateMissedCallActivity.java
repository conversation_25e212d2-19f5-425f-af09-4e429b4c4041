package com.czur.cloud.ui.auramate;

import static com.czur.czurutils.log.CZURLogUtilsKt.logE;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.badoo.mobile.util.WeakHandler;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.MissedCallAdapter;
import com.czur.cloud.entity.realm.MissedCallEntity;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.RefreshMissedCallEvent;
import com.czur.cloud.model.AuraDeviceModel;
import com.czur.cloud.model.MissedCallModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.util.validator.Validator;
import com.google.gson.reflect.TypeToken;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.text.SimpleDateFormat;
import java.util.List;

import io.realm.Realm;
import io.realm.RealmQuery;
import io.realm.RealmResults;
import io.realm.Sort;

/**
 * Created by Yz on 2018/6/1.
 * Email：<EMAIL>
 */
public class AuraMateMissedCallActivity extends AuramateBaseActivity implements View.OnClickListener {

    private ImageView normalBackBtn;
    private TextView normalTitle;
    private UserPreferences userPreferences;
    private HttpManager httpManager;
    private RecyclerView missedRecyclerView;
    private MissedCallAdapter missedCallAdapter;
    private SimpleDateFormat formatter;
    private RealmResults<MissedCallEntity> datas;
    private Realm realm;
    private RelativeLayout emptyRl;

    private WeakHandler handler;
    private SmartRefreshLayout smartRefreshLayout;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_aura_mate_missed_video);
        initComponent();
        registerEvent();
        initRecyclerView();
    }

    @Override
    protected boolean PCNeedFinish() {
        return false;
    }


    private void initComponent() {
        realm = Realm.getDefaultInstance();
        userPreferences = UserPreferences.getInstance(this);
        httpManager = HttpManager.getInstance();

        normalBackBtn = (ImageView) findViewById(R.id.normal_back_btn);
        normalTitle = (TextView) findViewById(R.id.normal_title);
        normalTitle.setText(R.string.missed);
        missedRecyclerView = (RecyclerView) findViewById(R.id.missed_recyclerView);
        smartRefreshLayout = findViewById(R.id.refresh_layout);
        smartRefreshLayout.setOnRefreshListener(onRefreshListener);

        handler = new WeakHandler();

        emptyRl = (RelativeLayout) findViewById(R.id.empty_rl);

        formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
    }

    private void registerEvent() {
        normalBackBtn.setOnClickListener(this);
        setNetListener();

    }


    /**
     * @des: 初始化列表
     * @params:
     * @return:
     */

    private void initRecyclerView() {
        datas = realm.where(MissedCallEntity.class).sort("createTime", Sort.DESCENDING).findAll();
        showEmpty();

        missedCallAdapter = new MissedCallAdapter(this, datas, realm);
        missedCallAdapter.setOnClickListener(onClickListener);
        missedRecyclerView.setHasFixedSize(true);
        missedRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        missedRecyclerView.setAdapter(missedCallAdapter);
        getCallAndSetup();
    }

    private void showEmpty() {
        if (datas.size() > 0) {
            emptyRl.setVisibility(View.GONE);
        } else {
            emptyRl.setVisibility(View.VISIBLE);
        }
    }

    private MissedCallAdapter.OnClickListener onClickListener = new MissedCallAdapter.OnClickListener() {
        @Override
        public void onTagDeleteClick(MissedCallEntity missedCallEntity, int position) {

        }
    };

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case AURA_MATE_CHANGED:
                ActivityUtils.finishActivity(this);
                break;
            default:
                break;

        }
    }

    private OnRefreshListener onRefreshListener = new OnRefreshListener() {
        @Override
        public void onRefresh(@NonNull RefreshLayout refreshLayout) {
            getMissedCallListRefresh();
        }

    };

    private void getMissedCallListRefresh() {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<String>() {
            @Override
            public String doInBackground() {
                String time = getServerTimeSync();
                try (Realm realm = Realm.getDefaultInstance()) {
                    List<MissedCallModel> missedCallList = getMissedCallList();
                    if (Validator.isNotEmpty(missedCallList)) {
                        realm.executeTransaction(new Realm.Transaction() {
                            @Override
                            public void execute(Realm realm) {
                                for (MissedCallModel callModel : missedCallList) {
                                    MissedCallEntity sameEntity = realm.where(MissedCallEntity.class).equalTo("id", callModel.getId()).findFirst();
                                    if (sameEntity == null) {
                                        MissedCallEntity object = realm.createObject(MissedCallEntity.class, callModel.getId());
                                        object.setCallId(callModel.getCallId());
                                        object.setCreateTime(callModel.getCreateTime());
                                        object.setDirection(callModel.getDirection());
                                        object.setOwnerType(callModel.getOwnerType());
                                        object.setStatus(callModel.getStatus());
                                        object.setUdid(callModel.getUdid());
                                        object.setEquipmentUuid(callModel.getEquipmentUuid());
                                        object.setDeviceName(callModel.getDeviceName());
                                        object.setUserId(callModel.getUserId());
                                        object.setHaveRead(0);
                                    }

                                }

                            }
                        });

                    }
                    userPreferences.setReportTime(time);
                }
                return time;
            }

            @Override
            public void onSuccess(String result) {
                smartRefreshLayout.finishRefresh();
                refreshFiles(result);
                showEmpty();
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        if (!NetworkUtils.isConnected()) {
                            showMessage(R.string.toast_no_connection_network);
                        }
                        smartRefreshLayout.finishRefresh(false);
                    }
                });
            }
        });
    }

    private void refreshFiles(String time) {
        datas = realm.where(MissedCallEntity.class).sort("createTime", Sort.DESCENDING).findAll();
        missedCallAdapter.refreshData(time, realm);
    }

    private void getCallAndSetup() {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<String>() {
            @Override
            public String doInBackground() throws Throwable {
                String time = getServerTimeSync();
                try (Realm realm = Realm.getDefaultInstance()) {
                    List<MissedCallModel> callList = getMissedCallList();
                    if (Validator.isNotEmpty(callList)) {
                        realm.executeTransaction(new Realm.Transaction() {
                            @Override
                            public void execute(Realm realm) {
                                for (MissedCallModel callModel : callList) {
                                    MissedCallEntity sameEntity = realm.where(MissedCallEntity.class).equalTo("id", callModel.getId()).findFirst();
                                    if (sameEntity == null) {
                                        MissedCallEntity object = realm.createObject(MissedCallEntity.class, callModel.getId());
                                        object.setCallId(callModel.getCallId());
                                        object.setCreateTime(callModel.getCreateTime());
                                        object.setDirection(callModel.getDirection());
                                        object.setOwnerType(callModel.getOwnerType());
                                        object.setStatus(callModel.getStatus());
                                        object.setUdid(callModel.getUdid());
                                        object.setEquipmentUuid(callModel.getEquipmentUuid());
                                        object.setDeviceName(callModel.getDeviceName());
                                        object.setUserId(callModel.getUserId());
                                        object.setHaveRead(0);
                                    }

                                }

                                List<AuraDeviceModel> auraMateList = getAuraMateList();
                                if (Validator.isNotEmpty(auraMateList)) {
                                    RealmQuery<MissedCallEntity> realmQuery = realm.where(MissedCallEntity.class);
                                    for (AuraDeviceModel auraDeviceModel : auraMateList) {
                                        realmQuery.notEqualTo("equipmentUuid", auraDeviceModel.getEquipmentUID());
                                    }
                                    realmQuery.findAll().deleteAllFromRealm();
                                }

                            }
                        });
                    }
                }
                return time;
            }

            @Override
            public void onSuccess(String result) {
                userPreferences.setCallTime(result);
                missedCallAdapter.refreshData(result, realm);
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
            }
        });
    }

    private List<MissedCallModel> getMissedCallList() {
        try {
            final MiaoHttpEntity<MissedCallModel> reportEntity = httpManager.request().getMissedCallSync(userPreferences.getUserId(), userPreferences.getCallTime(), new TypeToken<List<MissedCallModel>>() {
            }.getType());
            if (reportEntity == null) {
                return null;
            }
            if (reportEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return reportEntity.getBodyList();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private List<AuraDeviceModel> getAuraMateList() {
        try {
            final MiaoHttpEntity<AuraDeviceModel> entity = httpManager.request().getAuraDevicesSync(userPreferences.getUserId(), new TypeToken<List<AuraDeviceModel>>() {
            }.getType());
            if (entity == null) {
                return null;
            }
            if (entity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return entity.getBodyList();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private String getServerTimeSync() {
        try {
            MiaoHttpEntity<String> serverTimeEntity = httpManager.request().getServerTime(
                    userPreferences.getUserId(), String.class);
            if (serverTimeEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                String serverTime = serverTimeEntity.getBody();
                return serverTime;
            } else {
                return String.valueOf(System.currentTimeMillis());
            }

        } catch (Exception e) {
            logE(e.toString());
            e.printStackTrace();
        }
        return String.valueOf(System.currentTimeMillis());
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.normal_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        realm.executeTransaction(new Realm.Transaction() {
            @Override
            public void execute(Realm realm) {
                RealmResults<MissedCallEntity> all = realm.where(MissedCallEntity.class).findAll();
                for (MissedCallEntity missedCallEntity : all) {
                    missedCallEntity.setHaveRead(1);
                }
            }
        });
        realm.close();
        EventBus.getDefault().post(new RefreshMissedCallEvent(EventType.REFRESH_MISSED_CALL));
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }
}
