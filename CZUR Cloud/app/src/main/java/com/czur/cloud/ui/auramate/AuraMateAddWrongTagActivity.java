package com.czur.cloud.ui.auramate;

import android.content.DialogInterface;
import android.os.Bundle;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.ManageQuestionTagAdapter;
import com.czur.cloud.entity.AuraMateWrongTagModel;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.WrongQuestionTagEvent;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.component.popup.AddTagPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.util.EtUtils;
import com.czur.cloud.util.validator.Validator;
import com.google.gson.reflect.TypeToken;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Yz on 2018/6/1.
 * Email：<EMAIL>
 */
public class AuraMateAddWrongTagActivity extends AuramateBaseActivity implements View.OnClickListener {
    private ImageView normalBackBtn;
    private TextView normalTitle;

    private List<AuraMateWrongTagModel> auraMateWrongTagModels;
    private RecyclerView tagRecyclerView;

    private ManageQuestionTagAdapter tagAdapter;
    private EditText dialogEdt;

    private UserPreferences userPreferences;
    private String ownerId;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_add_wrong_tag);
        initComponent();
        registerEvent();

    }

    @Override
    public boolean PCNeedFinish() {
        return false;
    }

    private void initComponent() {
        userPreferences = UserPreferences.getInstance(this);
        auraMateWrongTagModels = new ArrayList<>();
        normalBackBtn = (ImageView) findViewById(R.id.normal_back_btn);
        normalTitle = (TextView) findViewById(R.id.normal_title);
        tagRecyclerView = (RecyclerView) findViewById(R.id.tag_recyclerView);
        normalTitle.setText(R.string.manage_object);
        ownerId = getIntent().getStringExtra("ownerId");
        initRecyclerView();
        getTag();
    }

    private void initRecyclerView() {
        tagAdapter = new ManageQuestionTagAdapter(this, auraMateWrongTagModels);
        tagAdapter.setAddTagListener(addTagListener);
        tagAdapter.setOnDeleteClickListener(deleteClickListener);
        tagRecyclerView.setAdapter(tagAdapter);
        tagRecyclerView.setHasFixedSize(true);
        tagRecyclerView.setLayoutManager(new GridLayoutManager(this, 2));
    }

    private void registerEvent() {
        normalBackBtn.setOnClickListener(this);
        setNetListener();

    }

    private void getTag() {
        HttpManager.getInstance().request().getWrongQuestionTag(userPreferences.getUserId(), equipmentId, ownerId, new TypeToken<List<AuraMateWrongTagModel>>() {
        }.getType(), new MiaoHttpManager.Callback<AuraMateWrongTagModel>() {
            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<AuraMateWrongTagModel> entity) {
                hideProgressDialog();
                tagAdapter.refreshData(entity.getBodyList());

            }

            @Override
            public void onFailure(MiaoHttpEntity<AuraMateWrongTagModel> entity) {
                hideProgressDialog();

            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();

            }
        });
    }

    private ManageQuestionTagAdapter.AddTagListener addTagListener = new ManageQuestionTagAdapter.AddTagListener() {
        @Override
        public void onAddTagClickListener(int position) {
            showAddTagDialog();
        }
    };

    private ManageQuestionTagAdapter.OnDeleteClickListener deleteClickListener = new ManageQuestionTagAdapter.OnDeleteClickListener() {
        @Override
        public void onTagDeleteClick(AuraMateWrongTagModel tagEntity, int position) {
            showConfirmDeleteDialog(tagEntity.getId() + "");
        }
    };

    /**
     * @des:确认是否删除Books
     * @params:
     * @return:
     */

    private void showConfirmDeleteDialog(String tagId) {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(AuraMateAddWrongTagActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.delete_object_prompt));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                deleteWrongTag(tagId);
                dialog.dismiss();
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();
    }

    /**
     * @des: 显示添加Dialog
     * @params:
     * @return:
     */

    private void showAddTagDialog() {
        AddTagPopup.Builder builder = new AddTagPopup.Builder(AuraMateAddWrongTagActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getString(R.string.add_object));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (!EtUtils.containsEmoji(dialogEdt.getText().toString())) {
                    if (Validator.isNotEmpty(dialogEdt.getText().toString())) {
                        addWrongQuestionTag();
                    } else {
                        showMessage(R.string.object_should_not_be_empty);
                    }

                } else {
                    showMessage(R.string.nickname_toast_symbol);
                }

                dialog.dismiss();

            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });

        AddTagPopup commonPopup = builder.create();
        dialogEdt = (EditText) commonPopup.getWindow().findViewById(R.id.edt);
        commonPopup.show();
    }

    private void addWrongQuestionTag() {
        HttpManager.getInstance().request().addWrongQuestionTag(userPreferences.getUserId(), equipmentId, ownerId, dialogEdt.getText().toString(), String.class, new MiaoHttpManager.Callback<String>() {
            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                EventBus.getDefault().post(new WrongQuestionTagEvent(EventType.ADD_WRONG_QUESTION_TAG));
                getTag();

            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                if (entity.getCode() == MiaoHttpManager.STATUS_NAME_IS_SAMED) {
                    showMessage(R.string.had_same_name_object);
                }
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();

            }
        });
    }

    private void deleteWrongTag(String tagId) {
        HttpManager.getInstance().request().deleteWrongQuestionTag(userPreferences.getUserId(), equipmentId, ownerId, tagId, String.class, new MiaoHttpManager.Callback<String>() {
            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                getTag();
                EventBus.getDefault().post(new WrongQuestionTagEvent(EventType.DELETE_WRONG_QUESTION_TAG));
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();

            }
        });
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            case R.id.next_step_btn:


                break;
            case R.id.normal_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

    }
}
