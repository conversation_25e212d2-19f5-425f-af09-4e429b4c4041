package com.czur.cloud.ui.market;

import static com.czur.czurutils.log.CZURLogUtilsKt.logD;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.JavascriptInterface;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.badoo.mobile.util.WeakHandler;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.czur.cloud.R;
import com.czur.cloud.ui.base.BaseActivity;

/**
 * Created by Yz on 2018/2/28.
 * Email：<EMAIL>
 */

public class WebViewActivity extends BaseActivity implements View.OnClickListener {


    private FrameLayout webContainer;
    private WebView webView;
    private ImageView webviewBackBtn;
    private TextView webviewTitleTv;
    private String title;
    private String url;
    private RelativeLayout reloadWebviewRl;
    private TextView reloadBtn;
    private boolean isError = false;
    private boolean isFirstFinish = true;
    private WeakHandler handler;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        BarUtils.setStatusBarColor(this, 0,true);
        BarUtils.setStatusBarLightMode(getWindow(), true);
        setContentView(R.layout.activity_webview);
        showProgressDialog();
        initComponent();
        registerEvent();
    }

    private void initComponent() {
        title = getIntent().getStringExtra("title");
        url = getIntent().getStringExtra("url");
        handler = new WeakHandler();
        reloadWebviewRl = (RelativeLayout) findViewById(R.id.reload_webview_rl);
        reloadBtn = (TextView) findViewById(R.id.reload_btn);
        webviewBackBtn = (ImageView) findViewById(R.id.webview_back_btn);
        webviewTitleTv = (TextView) findViewById(R.id.webview_title_tv);
        webContainer = (FrameLayout) findViewById(R.id.web_frame);
        webviewTitleTv.setText(title);
        int titleSize = getIntent().getIntExtra("titleSize", 18);
        webviewTitleTv.setTextSize(titleSize);

        webView = new WebView(this);
        webView.setLayerType(View.LAYER_TYPE_SOFTWARE, null);

        WebSettings settings = webView.getSettings();
        settings.setDomStorageEnabled(true);
        //解决一些图片加载问题
        settings.setJavaScriptEnabled(true);
        settings.setBlockNetworkImage(false);
        webView.setWebViewClient(webClient);
        webView.setWebChromeClient(new WebChromeClient() {

            @Override
            public void onProgressChanged(WebView view, int progress) {
                //当进度走到100的时候做自己的操作，我这边是弹出dialog
//                logI("setWebChromeClient.onProgressChanged.progress->"+progress);

                if (progress == 100) {
                    hideProgressDialog();
                }
            }
        });
        webContainer.addView(webView);
        webView.addJavascriptInterface(new JSCallAndroidObject(), "jsCallAndroidObject");
        webView.loadUrl(url);

        webviewBackBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (webView.canGoBack()) {
                    webView.goBack(); //goBack()表示返回WebView的上一页面

                } else {
                    ActivityUtils.finishActivity(WebViewActivity.this);
                }
            }
        });
        reloadBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (NetworkUtils.isConnected()) {
                    showProgressDialog();
                    isFirstFinish = true;
                    webView.reload();
                    reloadWebviewRl.setVisibility(View.GONE);
                }else{
                    showProgressDialog();
                    reloadProgress();
                }
            }
        });

    }

    // 能否访问网络
    private void onNetWorkOnline() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                if (NetworkUtils.isAvailable()){
                    Message msg = new Message();
                    msg.what = 1;
                    handlerNetwork.sendMessage(msg);
                }else{
                    Message msg = new Message();
                    msg.what = 2;
                    handlerNetwork.sendMessage(msg);
                }
            }
        }).start();
    }

    @SuppressLint("HandlerLeak")
    Handler handlerNetwork = new Handler() {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            if (msg.what == 1){
                isFirstFinish = true;
                webView.reload();
            }
            if (msg.what == 2){
                reloadProgress();
            }
        }
    };

    private boolean isNetWorkOnline123() {
        Runtime runtime = Runtime.getRuntime();
        try {
            Process ipProcess = runtime.exec("ping -c 3 www.baidu.com");
            int exitValue = ipProcess.waitFor();
            logD("WebViewActivity.isNetWorkOnline.exitValue="+exitValue);
            return (exitValue == 0);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private void registerEvent() {
        NetworkUtils.registerNetworkStatusChangedListener(networkStatusChangedListener);
    }

    private NetworkUtils.OnNetworkStatusChangedListener networkStatusChangedListener = new NetworkUtils.OnNetworkStatusChangedListener() {

        @Override
        public void onDisconnected() {
            reloadWebviewRl.setVisibility(View.VISIBLE);
        }

        @Override
        public void onConnected(NetworkUtils.NetworkType networkType) {

            handler.postDelayed(new Runnable() {

                @Override
                public void run() {
                    showProgressDialog();
                    isFirstFinish = true;
                    webView.reload();
                    reloadWebviewRl.setVisibility(View.GONE);
                }
            }, 1000);
        }
    };

    /***
     * 设置Web视图的方法
     */
    private WebViewClient webClient = new WebViewClient() {

        //处理网页加载失败时
        @Override
        public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
            super.onReceivedError(view, request, error);

            if (request.getUrl().toString().equals(url)) {
                reloadProgress();
                isError = true;
                reloadWebviewRl.setVisibility(View.VISIBLE);
                webContainer.setVisibility(View.GONE);
            }
        }

        @Override
        public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
            super.onReceivedError(view, errorCode, description, failingUrl);
            reloadProgress();
            isError = true;
            reloadWebviewRl.setVisibility(View.VISIBLE);
            webContainer.setVisibility(View.GONE);
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            reloadProgress();
            if (!isFirstFinish) {
                return;
            }
            isFirstFinish = false;

            if (!isError) {
                //回调成功后的相关操作
                webContainer.setVisibility(View.VISIBLE);
            } else {
                isError = false;
                reloadWebviewRl.setVisibility(View.VISIBLE);
            }
        }

        @Override
        public boolean shouldOverrideUrlLoading(WebView view, String url) {
            Log.d("webview", "url: " + url);
            if(url.startsWith("mailto:")){
                url = url.replaceFirst("mailto:", "");
                url = url.trim();
                Intent i = new Intent(Intent.ACTION_SEND);
                i.setType("plain/text").putExtra(Intent.EXTRA_EMAIL, new String[]{url});
                startActivity(i);
                return true;
            }
            view.loadUrl(url);
            return true;
        }
    };

    private void reloadProgress() {
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                hideProgressDialog();
            }
        }, 600);
    }

    /**
     * @des: js交互
     * @params:
     * @return:
     */

    public class JSCallAndroidObject {
        private String TAG = MallFragment.JSCallAndroidObject.class.getSimpleName();

        @JavascriptInterface
        public String jsCallAndroid(String msg) {
            Uri uri = Uri.parse(msg);
            Intent intent = new Intent(Intent.ACTION_VIEW, uri);
            startActivity(intent);
            return "from Android";
        }

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            case R.id.normal_back_btn:
                ActivityUtils.finishActivity(this);
                break;

            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        if (webView != null) {
            webView.loadDataWithBaseURL(null, "", "text/html", "uft-8", null);
            webView.clearHistory();
            ((ViewGroup) webView.getParent()).removeView(webView);
            webView.destroy();
            webView = null;
        }

        NetworkUtils.unregisterNetworkStatusChangedListener(networkStatusChangedListener);
        super.onDestroy();
    }
}
