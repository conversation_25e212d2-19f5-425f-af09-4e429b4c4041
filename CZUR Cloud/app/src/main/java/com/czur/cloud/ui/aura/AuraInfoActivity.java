package com.czur.cloud.ui.aura;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.ui.base.BaseActivity;

/**
 * Created by Yz on 2018/2/28.
 * Email：<EMAIL>
 */

public class AuraInfoActivity extends BaseActivity {


    private ImageView accountBackBtn;
    private TextView accountTitle;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this,true);
        setContentView(R.layout.activity_aura_info);
        initComponent();
        registerEvent();
    }

    private void initComponent() {
        accountBackBtn = (ImageView) findViewById(R.id.user_back_btn);
        accountTitle = (TextView) findViewById(R.id.user_title);
        accountTitle.setText(R.string.explain);
    }

    private void registerEvent() {
        accountBackBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ActivityUtils.finishActivity(AuraInfoActivity.this);
            }
        });
    }


}
