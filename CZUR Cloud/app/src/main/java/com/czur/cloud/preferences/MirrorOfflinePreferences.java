package com.czur.cloud.preferences;

import android.app.Application;
import android.content.Context;

import com.czur.cloud.ui.mirror.model.SittingDeviceModel;
import com.czur.cloud.ui.mirror.model.SittingDeviceOfflineModel;
import com.czur.cloud.util.validator.StringUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.util.List;

public class MirrorOfflinePreferences extends BasePreferences {

    private static final String PREF = MirrorOfflinePreferences.class.getSimpleName();

    private static final String SITTING_SETTINGS_LIST = "czur_cloud_sitting_settings_list";
    private static final String SITTING_BIND_LIST = "czur_cloud_sitting_bind_list";
    private static final String SITTING_UNBIND_LIST = "czur_cloud_sitting_unbind_list";
    private static final String SITTING_SIT_PIC = "czur_cloud_sitting_sit_picture";
    private static final String SITTING_SIT_PIC_LIST = "czur_cloud_sitting_sit_picture_list";
    private static final String SITTING_ERROR_PIC_LIST = "czur_cloud_sitting_error_picture_list";
    private static final String SITTING_HAPPY_PIC_LIST = "czur_cloud_sitting_happy_picture_list";

    private static MirrorOfflinePreferences instance;

    public static void init(Application application) {
        instance = new MirrorOfflinePreferences(application, PREF);
    }

    public static MirrorOfflinePreferences getInstance() {
        return instance;
    }

    public static MirrorOfflinePreferences getInstance(Context context) {
        if (instance == null) {
            instance = new MirrorOfflinePreferences(context, PREF);
        }
        return instance;
    }

    public MirrorOfflinePreferences(Context context, String prefsName) {
        super(context, prefsName);
    }

    public void setSittingDevicesSettings(SittingDeviceModel datas) {
        String json = new Gson().toJson(datas);
        put(SITTING_SETTINGS_LIST, json);
    }

    public SittingDeviceModel getSittingDevicesSettings() {
        String json = (String) get(SITTING_SETTINGS_LIST);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return new Gson().fromJson(json, new TypeToken<SittingDeviceModel>() {
        }.getType());
    }

    public void setSittingDevicesBind(List<SittingDeviceOfflineModel> datas) {
        String json = new Gson().toJson(datas);
        put(SITTING_BIND_LIST, json);
    }

    public List<SittingDeviceOfflineModel> getSittingDevicesBind() {
        String json = (String) get(SITTING_BIND_LIST);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return new Gson().fromJson(json, new TypeToken<List<SittingDeviceOfflineModel>>() {
        }.getType());
    }

    public void setSittingDevicesUnbind(List<SittingDeviceOfflineModel> datas) {
        String json = new Gson().toJson(datas);
        put(SITTING_UNBIND_LIST, json);
    }

    public List<SittingDeviceOfflineModel> getSittingDevicesUnbind() {
        String json = (String) get(SITTING_UNBIND_LIST);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return new Gson().fromJson(json, new TypeToken<List<SittingDeviceOfflineModel>>() {
        }.getType());
    }

    public void setSittingSitPic(SittingDeviceOfflineModel data) {
        String json = new Gson().toJson(data);
        put(SITTING_SIT_PIC, json);
    }

    public SittingDeviceOfflineModel getSittingSitPic() {
        String json = (String) get(SITTING_SIT_PIC);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return new Gson().fromJson(json, new TypeToken<SittingDeviceOfflineModel>() {
        }.getType());
    }

    public void setSittingSitPictures(List<SittingDeviceOfflineModel> datas) {
        String json = new Gson().toJson(datas);
        put(SITTING_SIT_PIC_LIST, json);
    }

    public List<SittingDeviceOfflineModel> getSittingSitPictures() {
        String json = (String) get(SITTING_SIT_PIC_LIST);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return new Gson().fromJson(json, new TypeToken<List<SittingDeviceOfflineModel>>() {
        }.getType());
    }

    public void setSittingErrorPictures(List<SittingDeviceOfflineModel> datas) {
        String json = new Gson().toJson(datas);
        put(SITTING_ERROR_PIC_LIST, json);
    }

    public List<SittingDeviceOfflineModel> getSittingErrorPictures() {
        String json = (String) get(SITTING_ERROR_PIC_LIST);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return new Gson().fromJson(json, new TypeToken<List<SittingDeviceOfflineModel>>() {
        }.getType());
    }

    public void setSittingHappyPictures(List<SittingDeviceOfflineModel> datas) {
        String json = new Gson().toJson(datas);
        put(SITTING_HAPPY_PIC_LIST, json);
    }

    public List<SittingDeviceOfflineModel> getSittingHappyPictures() {
        String json = (String) get(SITTING_HAPPY_PIC_LIST);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return new Gson().fromJson(json, new TypeToken<List<SittingDeviceOfflineModel>>() {
        }.getType());
    }
}
