package com.czur.cloud.ui.mirror.model;

import java.io.Serializable;

/**
 * Created by <PERSON> on 2020/12/26.
 */
public class SittingReportModel implements Serializable {


    /**
     * id : 28
     * equipmentUuid : CET15P1907000003
     * beginTime : 1564698612000
     * endTime : 1564698612000
     * errorDuration : 35
     * usingDuration : 60
     * proportion : 0.5833333
     * createTime : 1564727413000
     */

    private int id;
    private String equipmentUuid;

    private String beginTime;
    private String endTime;
    private int errorDuration;
    private int usingDuration;
    private String proportion;
    private String pushTime;
    private String createTime;

    private int rightDuration;
    private int seriousErrorDuration;
    private int mildErrorDuration;
    private int moderateErrorDuration;

    private String rightProportion;
    private String seriousProportion;
    private String mildProportion;
    private String moderateProportion;

    //Jason
    private String title;//标题
    private int type;  //0为普通报告 1为日报 2为周报 3为月报
    private String reportId;

    public String getReportId() {        return reportId;    }
    public void setReportId(String reportId) {        this.reportId = reportId;    }

    public String getTitle() {        return title;    }
    public void setTitle(String title) {        this.title = title;    }
    public int getType() {        return type;    }
    public void setType(int type) {        this.type = type;    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getEquipmentUuid() {
        return equipmentUuid;
    }

    public void setEquipmentUuid(String equipmentUuid) {
        this.equipmentUuid = equipmentUuid;
    }


    public int getErrorDuration() {
        return errorDuration;
    }

    public void setErrorDuration(int errorDuration) {
        this.errorDuration = errorDuration;
    }

    public int getUsingDuration() {
        return usingDuration;
    }

    public void setUsingDuration(int usingDuration) {
        this.usingDuration = usingDuration;
    }

    public String getProportion() {
        return proportion;
    }

    public void setProportion(String proportion) {
        this.proportion = proportion;
    }


    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getPushTime() {
        return pushTime;
    }

    public void setPushTime(String pushTime) {
        this.pushTime = pushTime;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public int getRightDuration() {
        return rightDuration;
    }

    public void setRightDuration(int rightDuration) {
        this.rightDuration = rightDuration;
    }

    public int getSeriousErrorDuration() {
        return seriousErrorDuration;
    }

    public void setSeriousErrorDuration(int seriousErrorDuration) {
        this.seriousErrorDuration = seriousErrorDuration;
    }

    public int getMildErrorDuration() {
        return mildErrorDuration;
    }

    public void setMildErrorDuration(int mildErrorDuration) {
        this.mildErrorDuration = mildErrorDuration;
    }

    public int getModerateErrorDuration() {
        return moderateErrorDuration;
    }

    public void setModerateErrorDuration(int moderateErrorDuration) {
        this.moderateErrorDuration = moderateErrorDuration;
    }

    public String getRightProportion() {
        return rightProportion;
    }

    public void setRightProportion(String rightProportion) {
        this.rightProportion = rightProportion;
    }

    public String getSeriousProportion() {
        return seriousProportion;
    }

    public void setSeriousProportion(String seriousProportion) {
        this.seriousProportion = seriousProportion;
    }

    public String getMildProportion() {
        return mildProportion;
    }

    public void setMildProportion(String mildProportion) {
        this.mildProportion = mildProportion;
    }

    public String getModerateProportion() {
        return moderateProportion;
    }

    public void setModerateProportion(String moderateProportion) {
        this.moderateProportion = moderateProportion;
    }
}
