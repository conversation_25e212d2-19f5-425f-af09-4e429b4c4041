package com.czur.cloud.ui.auramate;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.badoo.mobile.util.WeakHandler;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.czur.cloud.R;
import com.czur.cloud.entity.realm.WifiHistoryEntity;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.TimeZone;

import cn.bingoogolapple.qrcode.zxing.QRCodeEncoder;
import io.realm.Realm;

/**
 * Created by Yz on 2018/6/1.
 * Email：<EMAIL>
 */
public class AuraMateWifiQRcodeActivity extends AuramateBaseActivity implements View.OnClickListener {
    private ImageView normalBackBtn;
    private TextView nextStepBtn;
    private String wifiName;
    private String wifiPsw;
    private ImageView qrcodeImg;
    private String key;
    private UserPreferences userPreferences;
    private boolean noNeedKey;
    private Realm realm;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_aura_wifi_qrcode);
        initComponent();
        registerEvent();
    }

    @Override
    protected boolean PCNeedFinish() {
        return !TextUtils.isEmpty(equipmentId);
    }

    private void initComponent() {
        realm = Realm.getDefaultInstance();
        userPreferences = UserPreferences.getInstance(this);
        wifiName = getIntent().getStringExtra("wifiName");
        wifiPsw = getIntent().getStringExtra("wifiPsw");
        noNeedKey = getIntent().getBooleanExtra("noNeedKey",false);
        normalBackBtn = (ImageView) findViewById(R.id.normal_back_btn);
        nextStepBtn = (TextView) findViewById(R.id.next_step_btn);
        qrcodeImg = (ImageView) findViewById(R.id.qrcode_img);
        getAuraKey();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case AURA_BIND_SUCCESS:
                WifiHistoryEntity wifiHistoryEntity = realm.where(WifiHistoryEntity.class).equalTo("ssid", wifiName).findFirst();
                if (wifiHistoryEntity == null) {
                    realm.executeTransaction(new Realm.Transaction() {
                        @Override
                        public void execute(Realm realm) {
                            WifiHistoryEntity wifiHistoryEntity = realm.createObject(WifiHistoryEntity.class, wifiName);
                            wifiHistoryEntity.setPassword(wifiPsw);
                            wifiHistoryEntity.setCreateTime(System.currentTimeMillis());
                            realm.copyToRealmOrUpdate(wifiHistoryEntity);
                        }
                    });
                } else {
                    realm.executeTransaction(new Realm.Transaction() {
                        @Override
                        public void execute(Realm realm) {
                            wifiHistoryEntity.setPassword(wifiPsw);
                            wifiHistoryEntity.setCreateTime(System.currentTimeMillis());
                            realm.copyToRealmOrUpdate(wifiHistoryEntity);
                        }
                    });
                }
                if (ActivityUtils.getTopActivity() instanceof AuraMateWifiQRcodeActivity) {
                    Intent intent = new Intent(AuraMateWifiQRcodeActivity.this, AuraMateConnectSuccessActivity.class);
                    intent.putExtra("isHide", true);
                    intent.putExtra("ssid", wifiName);
                    intent.putExtra("password", wifiPsw);
                    intent.putExtra("equipmentId", equipmentId);
                    ActivityUtils.startActivity(intent);
                    ActivityUtils.finishActivity(this);
                }
                break;
            default:
                break;
        }
    }

    private void registerEvent() {
        normalBackBtn.setOnClickListener(this);
        nextStepBtn.setOnClickListener(this);
    }


    private void getAuraKey() {
        showProgressDialog();
        findViewById(R.id.loading_tv).setVisibility(View.VISIBLE);

        //Jason 20201109
        Bitmap Bmp = BitmapFactory. decodeResource(
                getResources(), R.mipmap.aura_home_guide_qrcode);
        Bitmap bmp = Bmp.createScaledBitmap(Bmp, 550, 550, true);
        qrcodeImg.setImageBitmap(bmp);

        HttpManager.getInstance().request().getAuraKey(userPreferences.getUserId(), userPreferences.getUdid(), String.class, new MiaoHttpManager.CallbackNetwork<String>() {
            @Override
            public void onNoNetwork() {
                new WeakHandler().post(new Runnable() {
                    @Override
                    public void run() {
                        ProgressDialogHide();

                        showMessage(R.string.request_failed_alert);
                    }
                });

            }

            @Override
            public void onStart() {

            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                key = entity.getBody();
                ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Bitmap>() {
                    @Nullable
                    @Override
                    public Bitmap doInBackground() throws Throwable {
                        float hours = TimeZone.getDefault().getOffset(System.currentTimeMillis()) * 1.0F / (3600 * 1000) * 1.0F;
                        String zone = TimeZone.getDefault().getID();
                        String json = "";
                        if (noNeedKey) {
                            json = zone + "&" + hours + "&" + wifiName + "&" + wifiPsw;
                        } else {
                            json = zone + "&" + hours + "&" + key + "&" + wifiName + "&" + wifiPsw;
                        }
                        return QRCodeEncoder.syncEncodeQRCode(json, SizeUtils.dp2px(200));
                    }

                    @Override
                    public void onFail(Throwable t) {
                        super.onFail(t);
                        ProgressDialogHide();
                    }

                    @Override
                    public void onSuccess(@Nullable Bitmap result) {
                        qrcodeImg.setImageBitmap(result);
                        ProgressDialogHide();
                    }
                });


            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                showMessage(R.string.request_failed_alert);
                ProgressDialogHide();
            }

            @Override
            public void onError(Exception e) {
                showMessage(R.string.request_failed_alert);
                ProgressDialogHide();
            }
        });
    }

    private void ProgressDialogHide(){
        hideProgressDialog();
        findViewById(R.id.loading_tv).setVisibility(View.GONE);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.next_step_btn:
                Intent intent = new Intent(AuraMateWifiQRcodeActivity.this, AuraMateConnectSuccessActivity.class);
                intent.putExtra("ssid", wifiName);
                intent.putExtra("password", wifiPsw);
                ActivityUtils.startActivity(intent);
                ActivityUtils.finishActivity(this);
                break;
            case R.id.normal_back_btn:
//                ActivityUtils.finishToActivity(AuraMateActivity.class, false);
                // 联网失败，返回历史页面
                ActivityUtils.finishToActivity(AuraMateWifiHistoryActivity.class, false);
                break;
            default:
                break;
        }
    }

    @Override
    public void onBackPressed() {
        ActivityUtils.finishActivity(this);
    }

    @Override
    protected void onDestroy() {
        realm.close();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        super.onDestroy();
    }
}
