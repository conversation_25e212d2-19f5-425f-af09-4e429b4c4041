package com.czur.cloud.netty.bean

import com.czur.cloud.ui.starry.model.StarryDoingMeetingModel

data class StarryRecivedMessageBean(
    val body: StarryRecivedBody,
    val nodeId: Int,
    val requestid: String,
    val timestamp: Long,
    val room: String,
    val type: String
)

data class StarryRecivedBody(
    val action: String,
    val group_push: Int,
    val method: String,
    val reply: StarryRecivedReply,

    val data: List<StarryDoingMeetingModel>,

    val module: String,
    val room: String,
    val udid_from: String,
    val userid_from: String
)

data class StarryRecivedReply(
    val metadata: StarryRecivedMetadata,
    val users: List<StarryRecivedUser>,
    val user: StarryRecivedUser,

    val cmd: String,//指令update_phone 更新用户绑定手机号
    val meeting_no: String,
    val share_stream: String,
    var room: String,
)

data class StarryRecivedMetadata(
    val locked: Boolean,
    val isRecoder: Boolean,
    val isLocked: Boolean,
    val meetingCode: String = "",
    val meetingPassword: String = "",
    val someoneRecording: Boolean,
)

data class StarryRecivedUser(
    val admin: Boolean,
    val headImage: String,
    val appId: String,
    val audioStatus: Int,
    val czurId: String,
    val enter: Boolean,
    val enterTime: String,
    val isAdmin: Boolean,
    val isEnter: Boolean,
    val isResponse: Boolean,
    val kind: String,
    val meetingAccout: String,
    val meetingId: String,
    val mobile: String,
    val name: String,
    val response: Boolean,
    val responseTime: String,
    val sharing: Boolean,
    val shareStream: String,
    val status: Int,
    val type: Int,
    val udid: String,
    val updateTime: String,
    val videoStatus: Int
)
