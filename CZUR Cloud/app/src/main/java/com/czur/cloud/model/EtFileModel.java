package com.czur.cloud.model;

import java.util.List;
import java.util.Objects;

public class EtFileModel {


    private List<FoldersBean> folders;
    private List<FilesBean> files;

    public List<FoldersBean> getFolders() {
        return folders;
    }

    public void setFolders(List<FoldersBean> folders) {
        this.folders = folders;
    }

    public List<FilesBean> getFiles() {
        return files;
    }

    public void setFiles(List<FilesBean> files) {
        this.files = files;
    }

    public static class FoldersBean {
        /**
         * id : n71au1ronhpy2te
         * name :
         * fileCounts : 2
         * date : 2018-05-09 15:15:59
         * seqId : 12
         */

        private String id;
        private String name;
        private int fileCounts;
        private String date;

        private int seqId;


        private boolean isAutoCreate;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getFileCounts() {
            return fileCounts;
        }

        public void setFileCounts(int fileCounts) {
            this.fileCounts = fileCounts;
        }

        public String getDate() {
            return date;
        }

        public void setDate(String date) {
            this.date = date;
        }

        public int getSeqId() {
            return seqId;
        }

        public void setSeqId(int seqId) {
            this.seqId = seqId;
        }
        public boolean isAutoCreate() {
            return isAutoCreate;
        }

        public void setAutoCreate(boolean autoCreate) {
            isAutoCreate = autoCreate;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof FoldersBean)) return false;
            FoldersBean that = (FoldersBean) o;
            return Objects.equals(getId(), that.getId());
        }

        @Override
        public int hashCode() {
            return Objects.hash(getId());
        }
    }

    public static class FilesBean {
        /**
         * id : srz9nm6dmtdoj8n
         * seqNum : 357
         * name : 0228160340.jpg
         * userSelectMode : 2
         * ocrLanguage : ChinesePRC+English
         * mode : 1
         * original : https://changer-et.oss-cn-beijing.aliyuncs.com/test/87/20180228/EBYRF7946CARJ5TD111A_20180228160354800_middle.jpg?Expires=1527224056&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=Ouz9KX%2BWFIMRmc3hX7UrS3hSgmU%3D
         * originalOssKey : test/87/20180228/EBYRF7946CARJ5TD111A_20180228160354800_middle.jpg
         * middle : https://changer-et.oss-cn-beijing.aliyuncs.com/test/87/20180228/EBYRF7946CARJ5TD111A_20180228160354800_books_rotate289386_cut085856_middle.jpg?Expires=1527224056&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=Wf6hRi1i8CqPn7XkKpy7nZt4p%2FE%3D
         * middleOssKey : test/87/20180228/EBYRF7946CARJ5TD111A_20180228160354800_books_rotate289386_cut085856_middle.jpg
         * small : https://changer-et.oss-cn-beijing.aliyuncs.com/test/87/20180228/EBYRF7946CARJ5TD111A_20180228160354800_books_rotate289386_cut085856_small.jpg?Expires=1527224056&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=GInmShHfODZ%2FWsdbK5T%2BKny9tsc%3D
         * smallOssKey : test/87/20180228/EBYRF7946CARJ5TD111A_20180228160354800_books_rotate289386_cut085856_small.jpg
         * flatten : test/87/20180228/EBYRF7946CARJ5TD111A_20180228160354800_books_rotate289386_cut085856.jpg
         * whiteboard : null
         * pixel : 4608 pixels x 3456 pixels
         * fileSizeUnit : 3.0 MB
         * make : ET16
         * exposureTime : 0.25 sec
         * isoSpeedRatings : 1600
         * takeOn : 2018-02-28 16:03:40
         * date : 20180228
         * localeDate : 2018-02-28 16:03:40
         * originalSize : null
         * flattenSize : null
         */

        private String id;
        private int seqNum;
        private String name;
        private int userSelectMode;
        private String ocrLanguage;
        private int mode;
        private int colorMode;
        private String original;
        private String originalOssKey;
        private String middle;
        private String middleOssKey;
        private String small;
        private String smallOssKey;
        private String flatten;
        private Object whiteboard;
        private String pixel;
        private String fileSizeUnit;
        private String make;
        private String exposureTime;
        private String isoSpeedRatings;
        private String takeOn;
        private String date;
        private String localeDate;
        private Object originalSize;
        private long flattenSize;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public int getSeqNum() {
            return seqNum;
        }

        public void setSeqNum(int seqNum) {
            this.seqNum = seqNum;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getUserSelectMode() {
            return userSelectMode;
        }

        public void setUserSelectMode(int userSelectMode) {
            this.userSelectMode = userSelectMode;
        }

        public String getOcrLanguage() {
            return ocrLanguage;
        }

        public void setOcrLanguage(String ocrLanguage) {
            this.ocrLanguage = ocrLanguage;
        }

        public int getMode() {
            return mode;
        }

        public void setMode(int mode) {
            this.mode = mode;
        }

        public String getOriginal() {
            return original;
        }

        public void setOriginal(String original) {
            this.original = original;
        }

        public String getOriginalOssKey() {
            return originalOssKey;
        }

        public void setOriginalOssKey(String originalOssKey) {
            this.originalOssKey = originalOssKey;
        }

        public String getMiddle() {
            return middle;
        }

        public void setMiddle(String middle) {
            this.middle = middle;
        }

        public String getMiddleOssKey() {
            return middleOssKey;
        }

        public void setMiddleOssKey(String middleOssKey) {
            this.middleOssKey = middleOssKey;
        }

        public String getSmall() {
            return small;
        }

        public void setSmall(String small) {
            this.small = small;
        }

        public String getSmallOssKey() {
            return smallOssKey;
        }

        public void setSmallOssKey(String smallOssKey) {
            this.smallOssKey = smallOssKey;
        }

        public String getFlatten() {
            return flatten;
        }

        public void setFlatten(String flatten) {
            this.flatten = flatten;
        }

        public Object getWhiteboard() {
            return whiteboard;
        }

        public void setWhiteboard(Object whiteboard) {
            this.whiteboard = whiteboard;
        }

        public String getPixel() {
            return pixel;
        }

        public void setPixel(String pixel) {
            this.pixel = pixel;
        }

        public String getFileSizeUnit() {
            return fileSizeUnit;
        }

        public void setFileSizeUnit(String fileSizeUnit) {
            this.fileSizeUnit = fileSizeUnit;
        }

        public String getMake() {
            return make;
        }

        public void setMake(String make) {
            this.make = make;
        }

        public String getExposureTime() {
            return exposureTime;
        }

        public void setExposureTime(String exposureTime) {
            this.exposureTime = exposureTime;
        }

        public String getIsoSpeedRatings() {
            return isoSpeedRatings;
        }

        public void setIsoSpeedRatings(String isoSpeedRatings) {
            this.isoSpeedRatings = isoSpeedRatings;
        }

        public String getTakeOn() {
            return takeOn;
        }

        public void setTakeOn(String takeOn) {
            this.takeOn = takeOn;
        }

        public String getDate() {
            return date;
        }

        public void setDate(String date) {
            this.date = date;
        }

        public String getLocaleDate() {
            return localeDate;
        }

        public void setLocaleDate(String localeDate) {
            this.localeDate = localeDate;
        }

        public Object getOriginalSize() {
            return originalSize;
        }

        public void setOriginalSize(Object originalSize) {
            this.originalSize = originalSize;
        }

        public long getFlattenSize() {
            return flattenSize;
        }

        public void setFlattenSize(long flattenSize) {
            this.flattenSize = flattenSize;
        }


        public int getColorMode() {
            return colorMode;
        }

        public void setColorMode(int colorMode) {
            this.colorMode = colorMode;
        }

        @Override
        public boolean equals(Object o) {
            if (!(o instanceof FilesBean)) return false;
            FilesBean filesBean = (FilesBean) o;
            return getSeqNum() == filesBean.getSeqNum();
        }

    }
}
