package com.czur.cloud.ui.component;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.os.CountDownTimer;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

import com.czur.cloud.R;

import java.util.ArrayList;

public class WaveView extends View {

    private int mWaveNum;
    private float mRadiusMin;
    private float mRadiusMax;
    private float mWaveInterval;
    private int mWaveColor;
    private int mWaveAlpha;
    private int mBgColor;
    private float mSpeed;

    private Paint mPaint;
    private ArrayList<Float> mWaveList;
    private CountDownTimer waveAnim = new CountDownTimer(100000000, 16) {
        @Override
        public void onTick(long millisUntilFinished) {
            for (int i = 0; i < mWaveList.size(); i++) {
                mWaveList.set(i, mWaveList.get(i) + mSpeed);
                if (mWaveList.get(i) < mRadiusMin + mWaveInterval) {
                    mWaveList.set(i + 1, mRadiusMin);
                    break;
                }
            }
            if (mWaveList.get(0) > mRadiusMax) {
                mWaveList.set(0, 0f);
                ArrayList<Float> tempList = transList(mWaveList);
                mWaveList.clear();
                mWaveList.addAll(tempList);
            }

            invalidate();
        }

        @Override
        public void onFinish() {

        }
    };


    public WaveView(Context context) {
        this(context, null);

    }

    public WaveView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public WaveView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }


    private void init(Context context, AttributeSet attrs) {
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.wave);
        mWaveNum = ta.getInt(R.styleable.wave_waveNumber, 7);
        mRadiusMin = ta.getFloat(R.styleable.wave_minRadius, 90f);
        mRadiusMax = ta.getFloat(R.styleable.wave_maxRadius, 1080f);
        mWaveInterval = ta.getFloat(R.styleable.wave_waveInterval, 240f);
        mSpeed = ta.getFloat(R.styleable.wave_speed, 10f);
        mWaveColor = ta.getColor(R.styleable.wave_waveColor, Color.BLUE);
        mWaveAlpha = ta.getInt(R.styleable.wave_waveAlpha, 128);
        mBgColor = ta.getColor(R.styleable.wave_waveColor, Color.TRANSPARENT);
        ta.recycle();

        mPaint = new Paint();
        mWaveList = new ArrayList<>(mWaveNum);
        mPaint.setColor(mWaveColor);
        initRadius();
    }

    private void initRadius() {
        mWaveList.add(mRadiusMin);
        for (int i = 1; i < mWaveNum; i++) {
            mWaveList.add(0f);
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        float centerX = canvas.getWidth() / 2;
        float centerY = canvas.getHeight() / 2;
        canvas.drawColor(mBgColor);

        for (int i = 0; i < mWaveList.size(); i++) {
            mPaint.setAlpha(calcAlpha(mWaveList.get(i)));
            canvas.drawCircle(centerX, centerY, mWaveList.get(i), mPaint);
        }
    }


    private ArrayList<Float> transList(ArrayList<Float> list) {
        ArrayList<Float> newList = new ArrayList<>(mWaveNum);
        for (int i = 1; i < list.size(); i++) {
            newList.add(list.get(i));
        }
        newList.add(list.get(0));

        return newList;
    }

    private int calcAlpha(float radius) {
        return (int) ((mRadiusMax - radius) / mRadiusMax * mWaveAlpha);
    }

    public void startWave() {
        waveAnim.start();
    }

    public void stopWave() {
        waveAnim.cancel();
    }

    public void resetWave() {
        mWaveList.clear();
        mPaint.setAlpha(mWaveAlpha);
        mPaint.setColor(mWaveColor);
        initRadius();
        invalidate();
    }

    public int getmWaveNum() {
        return mWaveNum;
    }

    public void setmWaveNum(int mWaveNum) {
        this.mWaveNum = mWaveNum;
    }

    public float getmRadiusMin() {
        return mRadiusMin;
    }

    public void setmRadiusMin(float mRadiusMin) {
        this.mRadiusMin = mRadiusMin;
        mWaveList.clear();
        initRadius();
    }

    public float getmRadiusMax() {
        return mRadiusMax;
    }

    public void setmRadiusMax(float mRadiusMax) {
        this.mRadiusMax = mRadiusMax;
    }

    public int getmWaveColor() {
        return mWaveColor;
    }

    public void setmWaveColor(int mWaveColor) {
        this.mWaveColor = mWaveColor;
        mPaint.setColor(mWaveColor);
    }
}