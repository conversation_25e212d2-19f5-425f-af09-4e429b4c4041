package com.czur.cloud.model;

/**
 * Created by Yz on 2018/3/30.
 * Email：<EMAIL>
 */

public class OssModel {

    private String status;
    private String AccessKeyId;
    private String AccessKeySecret;
    private String SecurityToken;
    private String Expiration;
    private long expirationMillisecond;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAccessKeyId() {
        return AccessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        AccessKeyId = accessKeyId;
    }

    public String getAccessKeySecret() {
        return AccessKeySecret;
    }

    public void setAccessKeySecret(String accessKeySecret) {
        AccessKeySecret = accessKeySecret;
    }

    public String getSecurityToken() {
        return SecurityToken;
    }

    public void setSecurityToken(String securityToken) {
        SecurityToken = securityToken;
    }

    public String getExpiration() {
        return Expiration;
    }

    public void setExpiration(String expiration) {
        Expiration = expiration;
    }

    public long getExpirationMillisecond() {
        return expirationMillisecond;
    }

    public void setExpirationMillisecond(long expirationMillisecond) {
        this.expirationMillisecond = expirationMillisecond;
    }
}
