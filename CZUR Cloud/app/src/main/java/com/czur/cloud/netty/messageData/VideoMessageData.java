package com.czur.cloud.netty.messageData;

public class VideoMessageData {



    /**
     * function : Aura  Mate
     * device_status : {"light_level":"【灯光亮度等级】","light_mode":"【灯光模式】","light_switch":"【灯光开关状态】","has_calibrated_sp":0,"wifi_ssid":"","firmware_need_update":0,"firmware_current_version":"","firmware_update_version":"","sp_reminder_switch":"【坐姿提醒开关状态】","sp_reminder_sensitivity_level":"【坐姿提醒灵敏度等级】","sp_reminder_sensitivity_volume":1}
     */
    private String action;
    private String method;
    private String uuid;
    private int extra;
    private String call_id;
    private String userid_to;
    private String appid_to;




    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public int getExtra() {
        return extra;
    }

    public void setExtra(int extra) {
        this.extra = extra;
    }

    public String getCall_id() {
        return call_id;
    }

    public void setCall_id(String call_id) {
        this.call_id = call_id;
    }

    public String getUserid_to() {
        return userid_to;
    }

    public void setUserid_to(String userid_to) {
        this.userid_to = userid_to;
    }

    public String getAppid_to() {
        return appid_to;
    }

    public void setAppid_to(String appid_to) {
        this.appid_to = appid_to;
    }

}
