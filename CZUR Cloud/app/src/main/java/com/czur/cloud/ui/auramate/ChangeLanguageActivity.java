package com.czur.cloud.ui.auramate;

import android.content.DialogInterface;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.LanguageAdapter;
import com.czur.cloud.entity.LanguageEntity;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.ChangeLanguageEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.aurahome.ATCheckDeviceIsOnlineEvent;
import com.czur.cloud.netty.core.CZURTcpClient;
import com.czur.cloud.ui.component.popup.AuraMateLanguagePopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Yz on 2018/6/1.
 * Email：<EMAIL>
 */
public class ChangeLanguageActivity extends AuramateBaseActivity implements View.OnClickListener {

    private ImageView normalBackBtn;
    private TextView normalTitle;

    private RecyclerView recyclerView;
    private LanguageAdapter adapter;

    private List<LanguageEntity> datas;
    private String language;
    private AuraMateLanguagePopup.Builder builder2;
    private AuraMateLanguagePopup languagePopup;
    private Handler handler = new Handler();

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_change_language);
        initComponent();
        registerEvent();
        initRecyclerView();
    }

    @Override
    public boolean PCNeedFinish() {
        return !TextUtils.isEmpty(equipmentId);
    }


    private void initComponent() {
        language = getIntent().getStringExtra("language");

        normalBackBtn = (ImageView) findViewById(R.id.user_back_btn);
        normalTitle = (TextView) findViewById(R.id.user_title);
        normalTitle.setText(getString(R.string.set_aura_mate_language));
        recyclerView = (RecyclerView) findViewById(R.id.language_recyclerView);
    }

    private void registerEvent() {
        normalBackBtn.setOnClickListener(this);
        setNetListener();
    }

    private void updateLanguageDialog(int position) {
        String finalLanguage = "";
        if (position == 0) {
            finalLanguage = getString(R.string.simple_chinese);
        } else if (position == 1) {
            finalLanguage = getString(R.string.tradition_chinese);
        } else {
            finalLanguage = getString(R.string.english_ch);
        }
        builder2 = new AuraMateLanguagePopup.Builder(this, CloudCommonPopupConstants.COMMON_ONE_BUTTON);
        builder2.setMessage(getString(R.string.aura_change_language_to) + finalLanguage + "?");
        builder2.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialogInterface, int i) {
                if (languagePopup != null && languagePopup.isShowing()) {
                    languagePopup.dismiss();
                }
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        sendLanguageMsg(position);
                        showProgressDialog();
                        handler.removeCallbacksAndMessages(null);
                        handler.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                hideProgressDialog();
                            }
                        }, 15000);
                    }
                }).start();
            }
        });
        languagePopup = builder2.create();
        languagePopup.show();
    }

    /**
     * @des: 初始化列表
     * @params:
     * @return:
     */

    private void initRecyclerView() {
        if (language == null) {
            return;
        }
        datas = new ArrayList<>();
        LanguageEntity cn = new LanguageEntity();
        cn.setCnName(getString(R.string.simple_chinese));
        cn.setName(getString(R.string.simple_chinese));
        cn.setSelect(language.equals("CN"));
        LanguageEntity tc = new LanguageEntity();
        tc.setCnName(getString(R.string.tradition_chinese));
        tc.setName(getString(R.string.tradition_chinese));
        tc.setSelect(language.equals("TC"));
        LanguageEntity en = new LanguageEntity();
        en.setCnName(getString(R.string.english_ch));
        en.setName(getString(R.string.english));
        en.setSelect(language.equals("EN"));

        datas.add(cn);
        datas.add(tc);
        datas.add(en);

        adapter = new LanguageAdapter(this, datas);
        adapter.setOnItemClickListener(onClickListener);
        recyclerView.setHasFixedSize(true);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(adapter);

    }


    private LanguageAdapter.onItemClickListener onClickListener = new LanguageAdapter.onItemClickListener() {
        @Override
        public void onItemClick(int position, LanguageEntity languageEntity) {
            updateLanguageDialog(position);

        }
    };

    private void sendLanguageMsg(int position) {
        if (position == 0) {
            CZURTcpClient.getInstance().changeLanguage(ChangeLanguageActivity.this, equipmentId, "CN");
        } else if (position == 1) {
            CZURTcpClient.getInstance().changeLanguage(ChangeLanguageActivity.this, equipmentId, "TC");
        } else if (position == 2) {
            CZURTcpClient.getInstance().changeLanguage(ChangeLanguageActivity.this, equipmentId, "EN");
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case SYSTEM_LANGUAGE:
                ATCheckDeviceIsOnlineEvent event1 = (ATCheckDeviceIsOnlineEvent) event;
                String system_language = event1.getStatusBean().getSystem_language();
                if (system_language.equals("CN")) {
                    datas.get(0).setSelect(true);
                    datas.get(1).setSelect(false);
                    datas.get(2).setSelect(false);
                } else if (system_language.equals("TC")) {
                    datas.get(0).setSelect(false);
                    datas.get(1).setSelect(true);
                    datas.get(2).setSelect(false);
                } else {
                    datas.get(0).setSelect(false);
                    datas.get(1).setSelect(false);
                    datas.get(2).setSelect(true);
                }
                language = system_language;
                adapter.refreshData(datas);
                EventBus.getDefault().post(new ChangeLanguageEvent(EventType.CHANGE_LANGUAGE, system_language));
                hideProgressDialog();
                break;
            default:
                break;

        }
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.user_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        handler.removeCallbacksAndMessages(null);
    }
}
