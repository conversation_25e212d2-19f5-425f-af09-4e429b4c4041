package com.czur.cloud.util

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.AppUtils
import com.czur.cloud.R
import com.czur.cloud.entity.VersionInfoEntity
import com.czur.cloud.preferences.VersionPreferences
import com.czur.cloud.ui.base.CzurCloudApplication
import com.google.gson.Gson
import kotlinx.coroutines.*
import okhttp3.CacheControl.Companion.FORCE_NETWORK
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.IOException

/**
 * Created by 陈丰尧 on 2022/11/30
 */

object VersionUtil {
    // 获取新版的url地址
    private val getVersionJsonUrl: String
        get() {
            val url = if (AppUtils.isAppDebug()) {
                CzurCloudApplication.getApplication().getString(R.string.check_update_url_debug)
            } else {
                CzurCloudApplication.getApplication().getString(R.string.check_update_url)
            }
            return "${url}?${System.currentTimeMillis()}"
        }

    private val httpClient: OkHttpClient by lazy {
        OkHttpClient.Builder().build()
    }

    private val gson: Gson by lazy {
        Gson()
    }

    var remoteVersionInfoEntity: VersionInfoEntity? = null
        private set

    /**
     * 当前的版本号
     */
    val currentVersionCode: Int by lazy {
        AppUtils.getAppVersionCode()
    }

    init {
        CoroutineScope(Job()).launch {
            val savedVersionInfo = VersionPreferences.getInstance().savedRemoteVersionEntity ?: return@launch
            if (savedVersionInfo.versionCode > currentVersionCode && remoteVersionInfoEntity == null) {
                remoteVersionInfoEntity = savedVersionInfo
            }
        }
    }

    /**
     * 是否有新版本
     */
    fun hasNewVersion(serverVersionInfo: VersionInfoEntity? = remoteVersionInfoEntity): Boolean {
        return (serverVersionInfo?.versionCode ?: currentVersionCode) > currentVersionCode
    }


    fun getNewVersionInfoForJava(
        lifecycleOwner: LifecycleOwner,
        successCallBack: (VersionInfoEntity) -> Unit,
        errorCallBack: (Throwable) -> Unit
    ) {
        lifecycleOwner.lifecycleScope.launch {
            try {
                successCallBack(getNewVersionInfo())
            } catch (th: Throwable) {
                errorCallBack(th)
            }
        }
    }

    suspend fun getNewVersionInfo(): VersionInfoEntity {
        val url = getVersionJsonUrl
        val request = Request.Builder()
            .url(url)
            .cacheControl(FORCE_NETWORK)        // 禁止缓存
            .get()
            .build()

        val newCall = httpClient.newCall(request)
        return withContext(Dispatchers.IO) {
            val response = newCall.execute()
            if (response.isSuccessful) {
                val jsonStr = response.body!!.string()
                gson.fromJson(jsonStr, VersionInfoEntity::class.java)
            } else {
                throw IOException("请求新版本数据失败")
            }
        }.also {
            remoteVersionInfoEntity = it
            VersionPreferences.getInstance().saveRemoteVersionEntity(it)    // 记录下来, 没有网也能正确显示NEW的标记
        }
    }
}