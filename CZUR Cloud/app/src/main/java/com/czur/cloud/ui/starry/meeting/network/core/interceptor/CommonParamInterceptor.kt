package com.czur.cloud.ui.starry.meeting.network.core.interceptor

import com.blankj.utilcode.util.AppUtils
import com.czur.cloud.common.CZURConstants
import com.czur.cloud.preferences.UserPreferences
import okhttp3.Interceptor
import okhttp3.Response

/**
 * Created by 陈丰尧 on 2021/7/14
 * 为所有请求添加通用参数 和 请求头
 */
class CommonParamInterceptor : Interceptor {
    companion object {
    }

    private val userPreferences by lazy {
        UserPreferences.getInstance()
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val req = chain.request()
        val builder = req.newBuilder()

        val headerReq =builder
            .addHeader("udid", userPreferences.imei)
            .addHeader("App-Key", CZURConstants.CLOUD_ANDROID)
            .addHeader("Api-Build", AppUtils.getAppVersionCode().toString())
            .addHeader("App-Bundle", AppUtils.getAppPackageName())
            .addHeader("T-ID", userPreferences.token)
            .addHeader("U-ID", userPreferences.userId)
            .addHeader("X-COUNTRY-CODE", userPreferences.countryCode)
            .addHeader("Request-Timestamp", System.currentTimeMillis().toString())
            .build()

        return chain.proceed(headerReq)
    }
}