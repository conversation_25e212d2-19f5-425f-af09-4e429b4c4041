package com.czur.cloud.ui.mirror;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.netty.observer.NettyUtils;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.auramate.AuramateBaseActivity;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.home.IndexActivity;
import com.czur.cloud.ui.user.UserFeedbackActivity;

import java.util.Calendar;

/**
 * Created by Jason on 2020/12/25.
 */
public class SittingMenuActivity extends AuramateBaseActivity implements View.OnClickListener {
    private CloudCommonPopup commonPopup;
    private ImageView userBackBtn;
    private RelativeLayout auraHomeMenuAddAuraHomeRl;
    private RelativeLayout auraHomeMenuDeleteRl;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private TextView userTitle;
    private RelativeLayout auraMateMenuAdviceRl;
    private RelativeLayout auraMateMenuQuestionRl;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_sitting_menu_home);
        initComponent();
        registerEvent();
    }

    @Override
    protected boolean PCNeedFinish() {
        return false;
    }


    private void initComponent() {
        userPreferences = UserPreferences.getInstance(this);
        httpManager = HttpManager.getInstance();
        userBackBtn = (ImageView) findViewById(R.id.user_back_btn);
        auraHomeMenuAddAuraHomeRl = (RelativeLayout) findViewById(R.id.sitting_home_menu_add_aura_home_rl);
        auraHomeMenuDeleteRl = (RelativeLayout) findViewById(R.id.sitting_home_menu_delete_rl);
        userTitle = (TextView) findViewById(R.id.user_title);
        userTitle.setText(R.string.more);
        auraMateMenuAdviceRl = (RelativeLayout) findViewById(R.id.sitting_menu_advice_rl);
        auraMateMenuQuestionRl = (RelativeLayout) findViewById(R.id.sitting_menu_question_rl);
    }


    private void registerEvent() {
        auraMateMenuAdviceRl.setOnClickListener(this);
        auraMateMenuQuestionRl.setOnClickListener(this);
        userBackBtn.setOnClickListener(this);
        auraHomeMenuDeleteRl.setOnClickListener(this);
        auraHomeMenuAddAuraHomeRl.setOnClickListener(this);

        findViewById(R.id.sitting_menu_instructions_rl).setOnClickListener(this);
        findViewById(R.id.sitting_menu_tutorial_rl).setOnClickListener(this);

//        setNetListener();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.user_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.sitting_home_menu_delete_rl:
                showConfirmDeleteDialog();
                break;
            case R.id.sitting_home_menu_add_aura_home_rl:
                Intent intent3 = new Intent(this, SittingConnectActivity.class);
                intent3.putExtra("noNeedKey", false);
                ActivityUtils.startActivity(intent3);
                ActivityUtils.finishActivity(this);
                break;
            case R.id.sitting_menu_advice_rl:
                Intent intent2 = new Intent(SittingMenuActivity.this, UserFeedbackActivity.class);
                intent2.putExtra("isQuestion", false);

                // Jason 20201225 type=5,坐姿仪
//                intent2.putExtra("type", 3);
                intent2.putExtra("type", 5);//要重新定义一个
                intent2.putExtra("isFromAuraMate", true);
                ActivityUtils.startActivity(intent2);
                break;
            case R.id.sitting_menu_question_rl:
                Intent intent1 = new Intent(SittingMenuActivity.this, UserFeedbackActivity.class);
                intent1.putExtra("isQuestion", true);
                // Jason 20201225 type=5,坐姿仪
//                intent1.putExtra("type", 3);
                intent1.putExtra("type", 5);//要重新定义一个
                intent1.putExtra("isFromAuraMate", true);
                ActivityUtils.startActivity(intent1);
                break;

            case R.id.sitting_menu_instructions_rl:
                long cTime = Calendar.getInstance().getTimeInMillis();
                Intent intent = new Intent(this, SittingWebViewActivity.class);
                intent.putExtra("title", getString(R.string.sitting_menu_instructions));
//                intent.putExtra("url", getString(R.string.sitting_menu_instructions_url));
                intent.putExtra("url", String.format(getString(R.string.sitting_menu_instructions_url), cTime+""));
                ActivityUtils.startActivity(intent);
                break;

            case R.id.sitting_menu_tutorial_rl:
                long cTime2 = Calendar.getInstance().getTimeInMillis();
                Intent intent4 = new Intent(this, SittingWebViewActivity.class);
                intent4.putExtra("title", getString(R.string.sitting_menu_tutorial));
//                intent4.putExtra("url", getString(R.string.sitting_menu_tutorial_url));
                intent4.putExtra("url", String.format(getString(R.string.sitting_menu_tutorial_url), cTime2+""));
                ActivityUtils.startActivity(intent4);
                break;
            default:
                break;
        }
    }

    /**
     * @des:确认是否删除ET
     * @params:
     * @return:
     */

    private void showConfirmDeleteDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(SittingMenuActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.remove_sitting_confirm));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (commonPopup != null) {
                    commonPopup.dismiss();
                }
                removeEquipment(getString(R.string.Mirror));
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        commonPopup = builder.create();
        commonPopup.show();
    }

    /**
     * @des: 移除设备
     * @params:[equipName]
     * @return:void
     */
    private void removeEquipment(String equipName) {
        httpManager.request().removeEquipment(
                userPreferences.getUserId(), equipName, String.class, new MiaoHttpManager.Callback<String>() {
                    @Override
                    public void onStart() {
                        showProgressDialog();
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                        hideProgressDialog();
//                        ServiceUtils.stopService(NettyService.class);
                        NettyUtils.getInstance().stopNettyService();
                        userPreferences.setHasAuraMate(false);
                        showMessage(R.string.remove_success);
                        Intent intent = new Intent(SittingMenuActivity.this, IndexActivity.class);
                        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
                        ActivityUtils.startActivity(intent);
//                        ServiceUtils.stopService(NettyService.class);
                        NettyUtils.getInstance().stopNettyService();

                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                        hideProgressDialog();
                        if (entity.getCode() == MiaoHttpManager.STATUS_FAIL) {
                            showMessage(R.string.toast_internal_error);
                        } else {
                            showMessage(R.string.request_failed_alert);
                        }
                    }

                    @Override
                    public void onError(Exception e) {
                        hideProgressDialog();
                        showMessage(R.string.request_failed_alert);
                    }
                });
    }

}
