package com.czur.cloud.ui.account

import android.annotation.SuppressLint
import android.text.Editable
import android.text.TextWatcher
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.View.*
import android.view.ViewGroup
import android.widget.EditText
import android.widget.PopupWindow
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.Utils
import com.czur.cloud.BuildConfig
import com.czur.cloud.R
import com.czur.cloud.model.CountryCode
import com.czur.cloud.ui.starry.meeting.baselib.utils.getString
import com.czur.czurutils.log.logI
import kotlinx.android.synthetic.main.starry_pop_bottom_menu_item.view.title_tv
import kotlinx.android.synthetic.main.starry_pop_chose_country_menu_item.view.*
import java.util.*

class ChoseCountryListPopupWindow(
    layoutInflater: LayoutInflater,
    weight: Int,
    height: Int,
    datas: ArrayList<CountryCode>
) {

    private var mPopWindow: PopupWindow? = null
    private var mView: View? = null
    var bottom_menu_cancel: TextView? = null
    private var mListView: RecyclerView? = null
    private var mSearchEt: EditText? = null
    val mAdapter = MyAdapter()
    private var dataList: List<CountryCode> = listOf()
    private var selectValue: String = ""
    private var popup_window_dialog_rl: RelativeLayout? = null
    private var countryCodeListener: ((CountryCode, Int) -> Unit)? = null
    private var selectedCountry: CountryCode? = null

    init {
        initView(layoutInflater, weight, height, datas)
    }

    fun initView(layoutInflater: LayoutInflater, w: Int, h: Int, datas: List<CountryCode>) {
        mView = layoutInflater.inflate(R.layout.starry_pop_chose_country_menu_window, null)
        mListView = mView?.findViewById<RecyclerView>(R.id.recycleList)
        mSearchEt = mView?.findViewById<EditText>(R.id.search_et)
        popup_window_dialog_rl =
            mView?.findViewById(R.id.popup_window_dialog_rl) as RelativeLayout
        dataList = datas
        mAdapter.setDatas(datas)

        mAdapter.setOnItemClickListener(object : MyAdapter.OnClickListener {
            override fun onclick(position: Int, item: CountryCode) {
                logI("mAdapter.setOnItemClickListener.position=${position},item=${item}")
                countryCodeListener?.invoke(item, position)
                mPopWindow?.dismiss()
            }


        })
        mListView?.apply {
            isVerticalScrollBarEnabled = false //不显示滑动条
            val lm = LinearLayoutManager(context)
            lm.orientation = LinearLayoutManager.VERTICAL
            layoutManager = lm
            adapter = mAdapter
        }

        //获取屏幕宽高
        val weight = w
        val height = h

        mPopWindow = PopupWindow(mView, weight, height)

        mPopWindow?.isFocusable = true
        mPopWindow?.isOutsideTouchable = true   //点击外部popueWindow消失
//        mPopWindow?.animationStyle = R.style.BottomSheetAnimation
        //防止虚拟软键盘被弹出菜单遮住
//        mPopWindow?.softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE
//        mPopWindow?.width = WindowManager.LayoutParams.WRAP_CONTENT
//        mPopWindow?.height = WindowManager.LayoutParams.WRAP_CONTENT

        mSearchEt?.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable?) {
                mAdapter.setDatas(searchCountry(s.toString()))
                mAdapter.notifyDataSetChanged()
            }

        })
    }

    //设置选择的国家.
    fun setSelectedCountry(countryCode: CountryCode?) {
        this.selectedCountry = countryCode
        mAdapter.setSelectedCountry(selectedCountry)
        mAdapter.notifyDataSetChanged()
    }

    fun setChoseListener(countryCodeListener: (CountryCode, Int) -> Unit) {
        this.countryCodeListener = countryCodeListener
    }

    fun dismiss() {
        mPopWindow?.dismiss()

    }

    fun showDropDown(view: View) {
        //确定Window显示的位置，bottom和x=0，y=0这样的位置
        mPopWindow?.showAsDropDown(view, 0, 20, Gravity.CENTER)
    }

    //适配器
    class MyAdapter : RecyclerView.Adapter<MyAdapter.InnerHodler>() {

        private var datasList: List<CountryCode> = listOf()
        private var isLastItemIsRed = false
        private var selectedCountry: CountryCode? = null

        inner class InnerHodler(itemView: View) : RecyclerView.ViewHolder(itemView) {
        }

        fun setDatas(datas: List<CountryCode>) {
            this.datasList = datas
        }

        override fun getItemId(position: Int): Long {
            return position.toLong()
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): InnerHodler {
            val itemView = LayoutInflater.from(parent.context)
                .inflate(R.layout.starry_pop_chose_country_menu_item, parent, false)
            return InnerHodler(itemView)
        }

        override fun getItemCount(): Int {
            return datasList.size
        }

        override fun onBindViewHolder(
            holder: InnerHodler,
            @SuppressLint("RecyclerView") position: Int
        ) {
            val item = datasList[position]
            val packageName = AppUtils.getAppPackageName()
            val locale: Locale = ActivityUtils.getTopActivity().resources.getConfiguration().locale
            if (locale.toString().contains("zh_CN")) {

            } else if (locale.toString().contains("zh_HK") || locale.toString()
                    .contains("zh_TW") || locale.toString().contains("zh_MO")
            ) {

            }
            if (BuildConfig.IS_OVERSEAS) {//海外版
                val language = getString(R.string.countryCode)
                // 繁体中文
                if (language == "countryNameTw"){
                    holder.itemView.title_tv?.text = item.countryNameTw
                }else { // 英文及其他语言
                    holder.itemView.title_tv?.text = item.countryNameUs
                }
            } else { //中国版本
                holder.itemView.title_tv?.text = item.countryName
            }

            if (selectedCountry?.countryCode == item.countryCode) {
                holder.itemView.chosen_iv?.visibility = VISIBLE
            } else {
                holder.itemView.chosen_iv?.visibility = GONE
            }

            if (position == datasList.size - 1 && isLastItemIsRed) {
                holder.itemView.title_tv?.setTextColor(
                    Utils.getApp().getColor(R.color.starry_text_color_red)
                )
            }

            //为listView的每一个子条目设置监听
            holder.itemView.setOnClickListener(View.OnClickListener {
//                notifyDataSetChanged()
                clicklistener?.onclick(position, item)
            })

        }

        //自定义点击事件接口
        interface OnClickListener {
            fun onclick(position: Int, item: CountryCode)
        }

        var clicklistener: OnClickListener? = null

        fun setOnItemClickListener(listener: OnClickListener) {
            clicklistener = listener
        }

        fun setLastItemIsRed(lastItemIsRed: Boolean) {
            isLastItemIsRed = lastItemIsRed
        }

        fun setSelectedCountry(selectedCountry: CountryCode?) {
            this.selectedCountry = selectedCountry
        }

    }


    fun searchCountry(text: String): List<CountryCode> {
        if (text.isEmpty()) {
            return dataList
        }
        val resultDataList = arrayListOf<CountryCode>()
        for (value in dataList) {
            val countryName = getString(R.string.countryCode)
            // 繁体中文
            if (countryName == "countryNameTw") {
                if (value.countryNameTw.indexOf(text,0,true) != -1){
                    resultDataList.add(value)
                }
            } else {
                if (value.countryNameUs.indexOf(text,0,true) != -1){
                    resultDataList.add(value)
                }
            }
        }
        return resultDataList
    }
}