package com.czur.cloud.ui.starry.meeting.bean

import com.czur.cloud.ui.starry.meeting.bean.UserStatus.STATUS_CALLING
import com.czur.cloud.ui.starry.meeting.bean.UserStatus.STATUS_HOLD_ON
import com.czur.cloud.ui.starry.meeting.bean.UserStatus.STATUS_IOS_HOLD
import com.czur.cloud.ui.starry.meeting.bean.UserStatus.STATUS_JOINED
import com.czur.cloud.ui.starry.meeting.bean.UserStatus.STATUS_OFFLINE
import com.czur.cloud.ui.starry.meeting.bean.UserStatus.STATUS_TIMEOUT
import com.google.gson.annotations.SerializedName

/**
 * Created by 陈丰尧 on 4/22/21
 * 从长连接返回的User对象
 */
data class User(
    val meetingId: String,         // 会议室的ID
    val czurId: String,           // 用户对应的czur user id
    @SerializedName("meetingAccout")
    val accountID: String,    // 用户的会议账号
    val name: String,           // 用户姓名
    val headImage: String?,      // 用户头像
    val mobile: String,         // 手机号
    val kind: String,           // 账号类型:  1: 设备, 2 手机
    val type: Int,              // 等级: 1: 普通, 2: 高级, 3: 企业
    val status: Int,            // 0: 呼叫中, 1: 未接听超时, 2: 加入, 3: 拒绝, 4:被移除, 5: 离开
    val videoStatus: Int,        // 视频状态  -1 不可用, 0 关闭, 1 开启
    val audioStatus: Int,        // 音频状态
    val sharing: Boolean,       // 是否再分享
    val shareStream: String?,    // 分享的视频流ID
    val isAdmin: Boolean,       // 是否为管理员
    val isResponse: Boolean,    // 是否已响应
    val isEnter: Boolean,       // 是否已加入
) {
    /**
     * 用户是否再会议中
     * 只处理在会议中的用户
     * 状态:
     * 呼叫中 呼叫超时 已加入 掉线 暂时离开
     * 4种状态的用户
     */
    val isInMeeting: Boolean
        get() = status == STATUS_CALLING
                || status == STATUS_TIMEOUT
                || status == STATUS_JOINED
                || status == STATUS_OFFLINE
                || status == STATUS_IOS_HOLD
                || status == STATUS_HOLD_ON
}

/**
 * 成员状态
 * STATUS_REJECT,STATUS_BEEN_REMOVE,STATUS_LEAVE在实际中都不会使用
 */
object UserStatus {
    /***** 成员状态 *****/
    const val STATUS_CALLING = 0        // 呼叫中
    const val STATUS_TIMEOUT = 1        // 未接听超时
    const val STATUS_JOINED = 2         // 加入
    const val STATUS_REJECT = 3         // 拒绝
    const val STATUS_BEEN_REMOVE = 4    // 被移除
    const val STATUS_LEAVE = 5          // 离开
    const val STATUS_OFFLINE = 6        // 掉线
    const val STATUS_HOLD_ON = 7        // 暂时离开
    const val STATUS_IOS_HOLD = 8        // status=8（iOS进入后台）
    const val STATUS_WAITING = 10        // 等待中
}

/**
 * 用户音频流的状态
 */
object UserStreamStatus {
    const val STREAM_IN_USE = 1       // 数据流正在被使用
    const val STREAM_CLOSE = 0        // 数据流关闭
    const val STREAM_DISABLE = -1      // 数据流不可用
}