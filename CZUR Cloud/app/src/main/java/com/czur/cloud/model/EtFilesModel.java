package com.czur.cloud.model;

public class EtFilesModel {


    /**
     * id : 0ha3mjfppbgxmek
     * seqNum : 376
     * name : 0512092905.jpg
     * userSelectMode : 1
     * ossKeyRotateAngle : 0
     * ossFlattenKeyRotateAngle : null
     * ossManualKeyRotateAngle : 0
     * smallRotateAngle : 0
     * ocrLanguage : null
     * mode : 2
     * original : https://changer-et.oss-cn-beijing.aliyuncs.com/E3KUBX5CY1R49N6GU1Y1_20160512093131559_middle.jpg?Expires=1464151935&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=%2BbyxP5GIJe6rImEWuqP0fCAczHU%3D
     * originalOssKey : E3KUBX5CY1R49N6GU1Y1_20160512093131559_middle.jpg
     * middle : https://changer-et.oss-cn-beijing.aliyuncs.com/E3KUBX5CY1R49N6GU1Y1_20160512093131559_manual_middle.jpg?Expires=1464151935&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=g370w1FkG9/RSmwWzHPs1JCwgKI%3D
     * middleOssKey : E3KUBX5CY1R49N6GU1Y1_20160512093131559_manual_middle.jpg
     * small : https://changer-et.oss-cn-beijing.aliyuncs.com/E3KUBX5CY1R49N6GU1Y1_20160512093131559_manual_small.jpg?Expires=1464151935&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=wCqlfZtRYivcih4UHy7WVSdix1U%3D
     * smallOssKey : E3KUBX5CY1R49N6GU1Y1_20160512093131559_manual_small.jpg
     * flatten : null
     * whiteboard : null
     * pixel : 4608 pixels x 3456 pixels
     * fileSizeUnit : 3.1 MB
     * make : ET16
     * exposureTime : 1/60 sec
     * isoSpeedRatings : 400
     * takeOn : 2016:05:12 09:29:05
     * date : 20160512
     * localeDate : 2016-05-12 09:29:05
     */

    private String id;
    private int seqNum;
    private String name;
    private int userSelectMode;
    private int ossKeyRotateAngle;
    private Object ossFlattenKeyRotateAngle;
    private int ossManualKeyRotateAngle;
    private int smallRotateAngle;
    private Object ocrLanguage;
    private int mode;
    private String original;
    private String originalOssKey;
    private String middle;
    private String middleOssKey;
    private String small;
    private String smallOssKey;
    private Object flatten;
    private Object whiteboard;
    private String pixel;
    private String fileSizeUnit;
    private String make;
    private String exposureTime;
    private String isoSpeedRatings;
    private String takeOn;
    private String date;
    private String localeDate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getSeqNum() {
        return seqNum;
    }

    public void setSeqNum(int seqNum) {
        this.seqNum = seqNum;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getUserSelectMode() {
        return userSelectMode;
    }

    public void setUserSelectMode(int userSelectMode) {
        this.userSelectMode = userSelectMode;
    }

    public int getOssKeyRotateAngle() {
        return ossKeyRotateAngle;
    }

    public void setOssKeyRotateAngle(int ossKeyRotateAngle) {
        this.ossKeyRotateAngle = ossKeyRotateAngle;
    }

    public Object getOssFlattenKeyRotateAngle() {
        return ossFlattenKeyRotateAngle;
    }

    public void setOssFlattenKeyRotateAngle(Object ossFlattenKeyRotateAngle) {
        this.ossFlattenKeyRotateAngle = ossFlattenKeyRotateAngle;
    }

    public int getOssManualKeyRotateAngle() {
        return ossManualKeyRotateAngle;
    }

    public void setOssManualKeyRotateAngle(int ossManualKeyRotateAngle) {
        this.ossManualKeyRotateAngle = ossManualKeyRotateAngle;
    }

    public int getSmallRotateAngle() {
        return smallRotateAngle;
    }

    public void setSmallRotateAngle(int smallRotateAngle) {
        this.smallRotateAngle = smallRotateAngle;
    }

    public Object getOcrLanguage() {
        return ocrLanguage;
    }

    public void setOcrLanguage(Object ocrLanguage) {
        this.ocrLanguage = ocrLanguage;
    }

    public int getMode() {
        return mode;
    }

    public void setMode(int mode) {
        this.mode = mode;
    }

    public String getOriginal() {
        return original;
    }

    public void setOriginal(String original) {
        this.original = original;
    }

    public String getOriginalOssKey() {
        return originalOssKey;
    }

    public void setOriginalOssKey(String originalOssKey) {
        this.originalOssKey = originalOssKey;
    }

    public String getMiddle() {
        return middle;
    }

    public void setMiddle(String middle) {
        this.middle = middle;
    }

    public String getMiddleOssKey() {
        return middleOssKey;
    }

    public void setMiddleOssKey(String middleOssKey) {
        this.middleOssKey = middleOssKey;
    }

    public String getSmall() {
        return small;
    }

    public void setSmall(String small) {
        this.small = small;
    }

    public String getSmallOssKey() {
        return smallOssKey;
    }

    public void setSmallOssKey(String smallOssKey) {
        this.smallOssKey = smallOssKey;
    }

    public Object getFlatten() {
        return flatten;
    }

    public void setFlatten(Object flatten) {
        this.flatten = flatten;
    }

    public Object getWhiteboard() {
        return whiteboard;
    }

    public void setWhiteboard(Object whiteboard) {
        this.whiteboard = whiteboard;
    }

    public String getPixel() {
        return pixel;
    }

    public void setPixel(String pixel) {
        this.pixel = pixel;
    }

    public String getFileSizeUnit() {
        return fileSizeUnit;
    }

    public void setFileSizeUnit(String fileSizeUnit) {
        this.fileSizeUnit = fileSizeUnit;
    }

    public String getMake() {
        return make;
    }

    public void setMake(String make) {
        this.make = make;
    }

    public String getExposureTime() {
        return exposureTime;
    }

    public void setExposureTime(String exposureTime) {
        this.exposureTime = exposureTime;
    }

    public String getIsoSpeedRatings() {
        return isoSpeedRatings;
    }

    public void setIsoSpeedRatings(String isoSpeedRatings) {
        this.isoSpeedRatings = isoSpeedRatings;
    }

    public String getTakeOn() {
        return takeOn;
    }

    public void setTakeOn(String takeOn) {
        this.takeOn = takeOn;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getLocaleDate() {
        return localeDate;
    }

    public void setLocaleDate(String localeDate) {
        this.localeDate = localeDate;
    }
}
