package com.czur.cloud.util;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Point;
import android.net.Uri;
import android.os.Handler;

import com.czur.cloud.cache.CustomCacheKeyFactory;
import com.czur.cloud.ui.base.BaseActivity;
import com.davemorrissey.labs.subscaleview.ImageSource;
import com.davemorrissey.labs.subscaleview.SubsamplingScaleImageView;
import com.facebook.binaryresource.BinaryResource;
import com.facebook.binaryresource.FileBinaryResource;
import com.facebook.cache.common.CacheKey;
import com.facebook.common.executors.CallerThreadExecutor;
import com.facebook.common.references.CloseableReference;
import com.facebook.datasource.DataSource;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.imagepipeline.common.ResizeOptions;
import com.facebook.imagepipeline.core.ImagePipeline;
import com.facebook.imagepipeline.core.ImagePipelineFactory;
import com.facebook.imagepipeline.datasource.BaseBitmapDataSubscriber;
import com.facebook.imagepipeline.image.CloseableImage;
import com.facebook.imagepipeline.request.BasePostprocessor;
import com.facebook.imagepipeline.request.ImageRequest;
import com.facebook.imagepipeline.request.ImageRequestBuilder;
import com.facebook.imagepipeline.request.Postprocessor;
import com.shuyu.frescoutil.listener.LoadFrescoListener;

import java.io.File;

import lib.lhh.fiv.library.FrescoImageView;

public class CzurFrescoHelper {
    public static void loadFrescoImage(FrescoImageView imageView, String uri, int defaultImg, int cornerRadius, boolean isCircle, boolean loadLocalPath, boolean isAnima, Point size, Postprocessor postprocessor) {
        init(imageView, cornerRadius, isCircle, isAnima, size, postprocessor);
        if (loadLocalPath) {
            imageView.loadLocalImage(uri, defaultImg);
        } else {
            imageView.loadView(uri, defaultImg);
        }

    }

    public static void loadBigImage(final BaseActivity context, final SubsamplingScaleImageView imageView, String imageUri, final int defaultId, boolean isShowLoading) {
        if (isShowLoading) {
            context.showProgressDialog(true, false);
        }
        final Uri uri = Uri.parse(imageUri.startsWith("http") ? imageUri : (imageUri.startsWith("file://") ? imageUri : "file://" + imageUri));
        final Handler handler = new Handler();
        if (imageUri.startsWith("http")) {
            File file = getCache(context, uri);
            if (file != null && file.exists()) {
                imageView.setImage(ImageSource.uri(file.getAbsolutePath()));
                if (isShowLoading) {
                    context.hideProgressDialog();
                }
            } else {
                getFrescoImg(context, imageUri, 0, 0, new LoadFrescoListener() {
                    public void onSuccess(Bitmap bitmap) {
                        handler.post(new Runnable() {
                            public void run() {
                                File file = CzurFrescoHelper.getCache(context, uri);
                                if (file != null && file.exists()) {
                                    imageView.setImage(ImageSource.uri(file.getAbsolutePath()));
                                }
                                if (isShowLoading) {
                                    context.hideProgressDialog();
                                }
                            }
                        });
                    }

                    public void onFail() {
                        handler.post(new Runnable() {
                            public void run() {
                                imageView.setImage(ImageSource.resource(defaultId));
                                if (isShowLoading) {
                                    context.hideProgressDialog();
                                }
                            }
                        });
                    }
                });
            }
        } else {
            imageView.setImage(ImageSource.uri(imageUri.replace("file://", "")));
            if (isShowLoading) {
                context.hideProgressDialog();
            }
        }

    }

    private static void init(FrescoImageView imageView, int cornerRadius, boolean isCircle, boolean isAnima, Point size, Postprocessor postprocessor) {
        imageView.setAnim(isAnima);
        imageView.setCornerRadius((float) cornerRadius);
        imageView.setFadeTime(300);
        if (isCircle) {
            imageView.asCircle();
        }

        if (postprocessor != null) {
            imageView.setPostProcessor(postprocessor);
        }

        if (size != null) {
            imageView.setResize(size);
        }

    }

    public static void loadFrescoImage(FrescoImageView imageView, String uri, int defaultImg, boolean loadLocalPath) {
        loadFrescoImage(imageView, uri, defaultImg, 0, false, loadLocalPath, true, (Point) null, (Postprocessor) null);
    }

    public static void loadFrescoImage(FrescoImageView imageView, String uri, int defaultImg, boolean loadLocalPath, Point size) {
        loadFrescoImage(imageView, uri, defaultImg, 0, false, loadLocalPath, true, size, (Postprocessor) null);
    }

    public static void loadFrescoImage(FrescoImageView imageView, String uri, int defaultImg, int cornerRadius, boolean loadLocalPath, Point size) {
        loadFrescoImage(imageView, uri, defaultImg, cornerRadius, false, loadLocalPath, true, size, (Postprocessor) null);
    }

    public static void loadFrescoImage(FrescoImageView imageView, String uri, int defaultImg, boolean loadLocalPath, Postprocessor postprocessor) {
        loadFrescoImage(imageView, uri, defaultImg, 0, false, loadLocalPath, true, (Point) null, postprocessor);
    }

    public static void loadFrescoImage(FrescoImageView imageView, String uri, int defaultImg, boolean loadLocalPath, Point point, Postprocessor postprocessor) {
        loadFrescoImage(imageView, uri, defaultImg, 0, false, loadLocalPath, true, point, postprocessor);
    }

    public static void loadFrescoImage(FrescoImageView imageView, String uri, int defaultImg, int radius, boolean loadLocalPath, Point point, Postprocessor postprocessor) {
        loadFrescoImage(imageView, uri, defaultImg, radius, false, loadLocalPath, true, point, postprocessor);
    }

    public static void loadFrescoImage(FrescoImageView imageView, String uri, int defaultImg, int cornerRadius, boolean loadLocalPath) {
        loadFrescoImage(imageView, uri, defaultImg, cornerRadius, false, loadLocalPath, true, (Point) null, (Postprocessor) null);
    }

    public static void loadFrescoImageCircle(FrescoImageView imageView, String uri, int defaultImg, boolean loadLocalPath) {
        loadFrescoImage(imageView, uri, defaultImg, 0, true, loadLocalPath, true, (Point) null, (Postprocessor) null);
    }

    public static boolean isCached(Context context, Uri uri) {
        ImagePipeline imagePipeline = Fresco.getImagePipeline();
        DataSource<Boolean> dataSource = imagePipeline.isInDiskCache(uri);
        if (dataSource == null) {
            return false;
        } else {
            ImageRequest imageRequest = ImageRequest.fromUri(uri);
            CacheKey cacheKey = CustomCacheKeyFactory.getInstance().getEncodedCacheKey(imageRequest, context);
            BinaryResource resource = ImagePipelineFactory.getInstance().getMainFileCache().getResource(cacheKey);
            return resource != null && dataSource.getResult() != null && (Boolean) dataSource.getResult();
        }
    }

    public static File getCache(Context context, Uri uri) {
        if (!isCached(context, uri)) {
            return null;
        } else {
            ImageRequest imageRequest = ImageRequest.fromUri(uri);
            CacheKey cacheKey = CustomCacheKeyFactory.getInstance().getEncodedCacheKey(imageRequest, context);
            BinaryResource resource = ImagePipelineFactory.getInstance().getMainFileCache().getResource(cacheKey);
            File file = ((FileBinaryResource) resource).getFile();
            return file;
        }
    }

    public static void getFrescoImg(BaseActivity context, String url, int width, int height, LoadFrescoListener listener) {
        getFrescoImgProcessor(context, url, width, height, (BasePostprocessor) null, listener);
    }

    public static void getFrescoImgProcessor(BaseActivity context, String url, int width, int height, BasePostprocessor processor, final LoadFrescoListener listener) {
        ResizeOptions resizeOptions = null;
        if (width != 0 && height != 0) {
            resizeOptions = new ResizeOptions(width, height);
        }

        ImageRequest imageRequest = ImageRequestBuilder.newBuilderWithSource(Uri.parse(url))
                .setProgressiveRenderingEnabled(false)
                .setPostprocessor(processor)
                .setResizeOptions(resizeOptions).build();
        ImagePipeline imagePipeline = Fresco.getImagePipeline();
        DataSource<CloseableReference<CloseableImage>> dataSource = imagePipeline.fetchDecodedImage(imageRequest, context);
        dataSource.subscribe(new BaseBitmapDataSubscriber() {
            protected void onNewResultImpl(Bitmap bitmap) {
                listener.onSuccess(bitmap);
            }

            protected void onFailureImpl(DataSource<CloseableReference<CloseableImage>> dataSource) {
                listener.onFail();
            }
        }, CallerThreadExecutor.getInstance());
    }
}
