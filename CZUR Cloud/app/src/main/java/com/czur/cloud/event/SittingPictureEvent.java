package com.czur.cloud.event;

import android.graphics.Bitmap;

public class SittingPictureEvent extends BaseEvent {

	private Bitmap bmp;
	private String bmp_name;

	public SittingPictureEvent(EventType eventType, Bitmap bmp) {
		super(eventType);
		this.bmp = bmp;
		this.bmp_name = "";
	}

	public SittingPictureEvent(EventType eventType, Bitmap bmp, String filename) {
		super(eventType);
		this.bmp = bmp;
		this.bmp_name = filename;
	}

	public Bitmap getBmp(){
		return bmp;
	}

	public String getBmpName(){
		return bmp_name;
	}

	@Override
	public boolean match(Object obj) {
		return true;
	}
}
