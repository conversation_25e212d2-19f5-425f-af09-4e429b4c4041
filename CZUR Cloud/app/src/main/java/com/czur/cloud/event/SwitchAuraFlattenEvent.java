package com.czur.cloud.event;

import com.czur.cloud.model.AuraResultModel;

public class SwitchAuraFlattenEvent extends BaseEvent {
    private AuraResultModel flattenImageModel;
    private boolean isFolder;
    private int position;
    public int getUserSelectMode() {
        return userSelectMode;
    }

    private int userSelectMode;

    public SwitchAuraFlattenEvent(EventType eventType, int position, boolean isFolder, AuraResultModel flattenImageModel,int userSelectMode) {
        super(eventType);
        this.position = position;
        this.isFolder = isFolder;
        this.flattenImageModel = flattenImageModel;
        this.userSelectMode = userSelectMode;
    }

    public boolean isFolder() {
        return isFolder;
    }

    public AuraResultModel getFlattenImageModel() {
        return flattenImageModel;
    }

    public int getPosition() {
        return position;
    }


    @Override
    public boolean match(Object obj) {
        return true;
    }
}
