package com.czur.cloud.ui.starry.meeting.fragment.newmain

import android.content.res.Configuration
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.viewModels
import com.czur.cloud.R
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.livedatabus.LiveDataBus
import com.czur.cloud.ui.starry.meeting.base.BaseMeetingFragment
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.meeting.common.MeetingDisplayMode
import com.czur.cloud.ui.starry.meeting.common.UserHandler
import com.czur.cloud.ui.starry.meeting.model.MeetingModel
import com.czur.cloud.ui.starry.meeting.viewmodel.MeetingViewModel
import com.czur.czurutils.log.logI
import kotlinx.android.synthetic.main.starry_fragment_meeting_grid2_display.*
import kotlinx.coroutines.delay


class MeetingDisplayGridTwoFragment : BaseMeetingFragment() {

    companion object {
        private const val TAG = "MeetingDisplayGridTwoFragment222"

        val INSTANCE: MeetingDisplayGridTwoFragment by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            MeetingDisplayGridTwoFragment()
        }
    }

    private val model: MeetingViewModel by viewModels({ requireActivity() })

    override fun getLayoutId() = R.layout.starry_fragment_meeting_grid2_display

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val viewGroup = requireView().findViewById<ConstraintLayout>(R.id.displayGrid2CL) as ViewGroup
        (viewGroup as? ViewGroup)?.apply {
            layoutTransition = CustomLayoutTransition()
//            layoutTransition.setAnimateParentHierarchy(false)
        }
    }

    override fun initView() {
        super.initView()
        logI("${TAG}.initView._this")

        displayGrid2Container?.onTopBarDoubleClickListener = {
            logI("${TAG}.onTopBarClickListener.initClick.it=${it}")
            // 点击自己头像，不变化，仅跳转首页
            if (!MeetingModel.shareMode) {
                if (it != UserHandler.czurId.toString()) {
                    model.resetDisplayUidsMain(it)
                } else {
                    // 是自己，则直接跳转主页面，并同时锁定窗口显示，tost提示：画面已锁定
                    if (MeetingModel.smartFocus) {
                        LiveDataBus.get().with(StarryConstants.MEETING_CLOCK).value = ""
                    }
                }
                model.switchSmartFocusDisable()
                model.doubleClickUid.postValue(it)
            }
        }
        displayGrid2Container?.onSingleClickListener = {
            if (attached) {
                controlBarVM.onUserOperation()
            }
        }
    }

    override fun onResume() {
        super.onResume()

        setupView()
    }

    override fun initData() {
        super.initData()
        logI("${TAG}.initData")
        model.displayUidsGrid2Live.observe(this){
            if (MeetingModel.displayModeLive.value == MeetingDisplayMode.GRID) {
                setupView()
            }
        }

        // 当前活动的人员检测
        model.activeSpeaker.observe(this) {
            if (it != null) {
                launch {
                    logI("${TAG}.activeSpeaker.uid=${it}")
                    delay(100)
                    displayGrid2Container?.setActivitySpeekerOuter(it)
                }
            }
        }

        // 屏幕分享取消后，强制刷新一下主屏
        MeetingModel.isForceRefeshMain.observe(this){
            if (it){
                if (MeetingModel.displayModeLive.value == MeetingDisplayMode.GRID) {
                    logI("${TAG}.isForceRefeshMain=${it}")
                    displayGrid2Container?.forceRefresh()
                    MeetingModel.isForceRefeshMain.postValue(false)
                }
            }
        }

    }

    private fun setupView(){
        logI("${TAG}.setupView.displayUidsGrid2Live=${model.displayUidsGrid2Live.value}")

        model.displayUidsGrid2Live.value?.forEach {
            val uid = it.uid
            val view = it.view
            val hasView = it.hasView
            if (hasView){
                model.setupViewForVideo(view!!, uid)
            }
        }

        model.displayUidsGrid2Live.value?.let {
            // 当前的活动人员
            displayGrid2Container?.refreshAndActivitySpeeker(it, model.mCurrentActiveSpeakerUid)
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        model.displayUidsGrid2Live.value?.let { displayGrid2Container?.refresh(it) }
    }
}