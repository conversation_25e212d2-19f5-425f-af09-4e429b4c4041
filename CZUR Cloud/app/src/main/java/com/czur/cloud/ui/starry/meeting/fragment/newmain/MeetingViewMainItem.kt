package com.czur.cloud.ui.starry.meeting.fragment.newmain

import android.graphics.Outline
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.util.Log
import android.view.*
import android.widget.ImageView
import androidx.annotation.DrawableRes
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.LiveData
import com.blankj.utilcode.util.SizeUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import com.czur.cloud.R
import com.czur.cloud.ui.starry.meeting.MeetingMainActivity
import com.czur.cloud.ui.starry.meeting.baselib.utils.*
import com.czur.cloud.ui.starry.meeting.common.MeetingDisplayMode
import com.czur.cloud.ui.starry.meeting.model.MeetingModel
import com.czur.cloud.ui.starry.meeting.widget.NetworkQualityView
import com.czur.czurutils.log.logD
import com.czur.czurutils.log.logE
import com.czur.czurutils.log.logV
import kotlinx.android.synthetic.main.starry_meeting_item_display_main.view.*

private const val TAG = "MeetingViewMainItem"

class MeetingViewMainItem(val itemView: View, private val index: Int) {
    // 列表模式的HUD
    private val smallGroup by lazy { itemView.smallGroup }

    // surfaceView的容器
    private val surfaceContainer by lazy { itemView.displayItemContainer }

    // 显示模式,主要在记录各种设定方法的默认参数上
    var displayMode: MeetingDisplayMode = MeetingDisplayMode.SINGLE

    init {
        (itemView as? ViewGroup)?.apply {
            layoutTransition = CustomLayoutTransition()
        }
    }

    /**
     * 给Item设置点击事件(只针对小窗)
     */
    fun onClick(block: (index: Int) -> Unit) {
        itemView.setOnClickListener {
            block(index)
        }
    }

    fun onDoubleClick(block: (index: Int) -> Unit) {
        block(index)
    }


    fun setDisplayNoCorner(uid: String, displayView: SurfaceView) {
        if (!MeetingModel.shareMode) {
            if (surfaceContainer.currentUid == uid) {
//                setSurfaceViewCorner(displayView, 0f)
                surfaceContainer.addSurface(displayView, uid)
//                logV("${uid}.tag相同, ignore")
                return
            }
        }
        surfaceContainer.removeSurface()
        logD("setDisplayNoCorner.添加Surface：${uid}")
//        setSurfaceViewCorner(displayView, 0f)
        surfaceContainer.addSurface(displayView, uid)
    }

    fun setDisplay(uid: String, displayView: View,isMainDisplayView : Boolean = false) {
        if (surfaceContainer.currentUid == uid) {
            logV("${uid}.tag相同, ignore")
            surfaceContainer.addSurface(displayView, uid,isMainDisplayView)
            return
        }else {
            logD("setDisplay.添加Surface：${uid}")
            // 清除掉之前的surfaceView
            surfaceContainer.removeSurface()
            surfaceContainer.addSurface(displayView, uid, isMainDisplayView)
        }
    }

    fun setDisplayForce(uid: String, displayView: View, isMainDisplayView : Boolean = false){
        logD("setDisplayForce.添加Surface：${uid}")
        surfaceContainer.removeSurface()
        surfaceContainer.addSurface(displayView, uid,isMainDisplayView)
    }

    /**
     * 不显示SurfaceView,只显示HUD和黑色的UI
     * 通常是这个人关闭了视频流
     */
    fun setDisplayNull() {
        surfaceContainer.removeSurface(false)
    }

    fun showBottomLineBg(flag: Boolean = true){
        var res = getDrawable(R.color.starry_meeting_small_video_bottom_bar_bg)
        if (!flag){
            res = getDrawable(R.color.transparent)
        }
        smallGroup.smallVideoButtomLayer.background = res
    }

    // 为SurfaceView设置圆角方式
    private fun setSurfaceViewCorner(mDisplaySurfaceView: SurfaceView, radius: Float = SizeUtils.dp2px(6f).toFloat()) {
        mDisplaySurfaceView.outlineProvider = object : ViewOutlineProvider() {
            override fun getOutline(view: View, outline: Outline) {
                val rect = Rect()
                view.getGlobalVisibleRect(rect)
                val leftMargin = 0
                val topMargin = 0
                val selfRect = Rect(
                    leftMargin, topMargin,
                    rect.right - rect.left - leftMargin,
                    rect.bottom - rect.top - topMargin
                )
                outline.setRoundRect(selfRect, radius)
            }
        }
        mDisplaySurfaceView.clipToOutline = true
    }


    /**
     * 设置昵称
     * @param nickName: 昵称
     * @param showMode: 显示模式
     */
    fun setNickName(nickName: String) {
        smallGroup.itemNickNameTv.text = nickName
    }
    fun setNickName2(nickName: String) {
        smallGroup.itemNickNameTv2.text = nickName
    }


    /**
     * 设置会议号
     * @param accountNo: 会议号, 如果为null, 则不显示会议号
     * @param showMode: 显示模式
     */
    fun setAccountNo(accountNo: String?) {
    }

    /**
     * 设置左下角 头像 昵称 网络状况的隐藏显示
     */
    fun setLeftDownGroupVisible(){
        if (MeetingModel.shareMode){
            Log.d(TAG,"MeetingModel.shareMode${MeetingModel.shareMode}")
            itemView.smallVideoButtomLayer.gone()
        }else{
            itemView.smallVideoButtomLayer.show()
        }
    }
    /**
     * 设置用户头像
     * @param imageUrl:     头像的url
     * @param hasSurface:   是否有会议Surface, 如果没有会议的Surface,才会显示头像
     * @param singleDef:    单视图模式   下的默认图片
     * @param gridDef:      列表模式     下的默认图片
     */
    fun setHeadImg(
        imageUrl: String?,
        hasSurface: Boolean,
        nickName: String,
        displayView :View?
    ) {
        val headIv = smallGroup.smallHeadIv

        if (hasSurface) {
            // 有画面数据,则不显示头像
            headIv.gone()
            surfaceContainer.showSnapImg = true     // 截图与头像不同时显示
            smallGroup.itemNickNameTv.show()
            smallGroup.itemNickNameTv2.gone()
        } else {
            headIv.show()
            surfaceContainer.showSnapImg = false
            smallGroup.itemNickNameTv.gone()
            smallGroup.itemNickNameTv2.show()
            if (imageUrl == null) {
                headIv.text = nickName
                headIv.tag = null
                return
            }
//            if (imageUrl != headIv.tag) {
//                headIv.text = nickName // 默认头像信息,是文字
            if (headIv.context == null){//屏蔽情况,pc接听时,app被挤掉,在被挤掉后会收到刷新成员列表,会在巧合时序下,进入方法,但activity已被销毁,导致崩溃
                return
            }

            // 防止 activity 已经destory了，还绘制UI
            if (MeetingMainActivity.isMeetingMainActivityDestory){
                return
            }

            try {
                Glide.with(headIv)
                    .load(imageUrl)
                    .apply(
                        RequestOptions()
                            .transform(
                                CenterCrop(),
                                RoundedCorners(100)
                            )
                    )
                    .listener(object : RequestListener<Drawable> {
                        override fun onLoadFailed(
                            e: GlideException?,
                            model: Any?,
                            target: Target<Drawable>?,
                            isFirstResource: Boolean
                        ): Boolean {
                            logD("头像加载失败(${imageUrl}),使用昵称:${nickName}")
                            headIv.text = nickName
                            return true
                        }

                        override fun onResourceReady(
                            resource: Drawable?,
                            model: Any?,
                            target: Target<Drawable>?,
                            dataSource: DataSource?,
                            isFirstResource: Boolean
                        ): Boolean {
                            // 头像加载成功
//                            logD("头像加载成功(${imageUrl})")
                            headIv.text = null
                            return false
                        }

                    })
                    .into(headIv)
//                headIv.tag = imageUrl
//            }
            }catch (e: Exception){
                logE("${TAG}.setHeadImg.error=${e}")
            }
        }
    }

    /**
     * 设置图片资源
     * @param imageView:    要设置图片的ImageView
     * @param singleRes:    单视图模式 下要设置的图片资源
     * @param gridRes:      列表模式   下要是设置的图片资源
     * @param showMode:     显示模式
     */
    /**
     * 设置图片资源
     * @param imageView:    要设置图片的ImageView
     * @param singleRes:    单视图模式 下要设置的图片资源
     * @param gridRes:      列表模式   下要是设置的图片资源
     * @param showMode:     显示模式
     */
    private fun setImageResPair(
        imageView: ImageView,
        @DrawableRes singleRes: Int,
        @DrawableRes gridRes: Int,
        showMode: MeetingDisplayMode
    ) {
        when (showMode) {
            MeetingDisplayMode.SINGLE -> imageView.setImageResource(singleRes)
            MeetingDisplayMode.GRID -> imageView.setImageResource(gridRes)
        }
    }


    /**
     * 获取ServiceView的LayoutParam
     */
    private fun getSurfaceLayoutParam(): ViewGroup.LayoutParams {
        return ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
    }

    /**
     * 设置音频大小的监听数据
     * @param volumeData: 音频大小的监听数据
     * @param isVolumeInUse: 是否已经静音了
     * @param singleVolumeRes: 单视图模式 下的mic图片
     * @param singleMuteRes:    单视图模式 下的静音图片
     * @param gridVolumeRes:    列表模式 下的mic图片
     * @param gridMuteRes:      列表模式 下的静音图片
     * @param showMode:         显示模式
     */
    fun setVolumeData(
        volumeData: LiveData<Int>,
        isVolumeInUse: Boolean,
        @DrawableRes singleVolumeRes: Int = R.mipmap.starry_meeting_mic_on,
        @DrawableRes singleMuteRes: Int = R.mipmap.starry_meeting_mic_mute,
        @DrawableRes gridVolumeRes: Int = R.mipmap.starry_meeting_mic_on,
        @DrawableRes gridMuteRes: Int = R.mipmap.starry_meeting_mic_mute,
        showMode: MeetingDisplayMode = displayMode
    ) {
        val volumeView = smallGroup.volumeView
        volumeView.visibility = View.VISIBLE
        if (isVolumeInUse) {
            setImageResPair(volumeView, singleVolumeRes, gridVolumeRes, showMode)
            volumeView.setVolumeData(volumeData)
        } else {
            setImageResPair(volumeView, singleMuteRes, gridMuteRes, showMode)
            volumeView.removeVolumeData()
        }
    }

    fun setActivitySpeekerOuter(flag: Boolean) {
        val activityGrid = getActivityGrid()
        if (flag) {
            activityGrid.setBackgroundResource(R.drawable.starry_meeting_activity_speeker_outer)
        }else{
            activityGrid.setBackgroundResource(R.color.transparent)
        }
    }
    private fun getActivityGrid(): ConstraintLayout {
        return smallGroup
    }

    // 网络信号--通话中每个用户的网络上下行 last mile 质量报告回调。
    // QUALITY_UNKNOWN(0)：质量未知
    //QUALITY_EXCELLENT(1)：质量极好
    //QUALITY_GOOD(2)：用户主观感觉和极好差不多，但码率可能略低于极好
    //===QUALITY_POOR(3)：用户主观感受有瑕疵但不影响沟通
    //===QUALITY_BAD(4)：勉强能沟通但不顺畅
    //===QUALITY_VBAD(5)：网络质量非常差，基本不能沟通
    //QUALITY_DOWN(6)：网络连接断开，完全无法沟通
    //QUALITY_DETECTING(8)：SDK 正在探测网络质量
    private fun getQualityImage(): NetworkQualityView {
        return smallGroup.itemNetworkQualityIV
    }

    fun setNetworkQuality(networkQualityLiveData: LiveData<Int>) {
        val qualityIV = getQualityImage()
        qualityIV.setQualityData(networkQualityLiveData)
    }
    fun setNetworkErrorQuality() {
        val qualityIV = getQualityImage()
        qualityIV.setQualityDataNetworkErrorMain()
    }

    fun setNetworkQualityGone() {
        val qualityIV = getQualityImage()
        qualityIV.setQualityDataNetworkGone()
    }

}