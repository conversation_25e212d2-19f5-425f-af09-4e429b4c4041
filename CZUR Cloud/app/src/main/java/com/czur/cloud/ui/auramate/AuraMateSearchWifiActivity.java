package com.czur.cloud.ui.auramate;

import static com.blankj.utilcode.util.PermissionUtils.isGranted;

import android.Manifest;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.location.LocationManager;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiManager;
import android.os.Bundle;
import android.os.Handler;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.SearchWifiAdapter;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.component.recyclerview.MaxHeightRecyclerView;
import com.czur.cloud.ui.component.recyclerview.RecycleViewDivider;
import com.czur.cloud.util.PermissionUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class AuraMateSearchWifiActivity extends AuramateBaseActivity implements View.OnClickListener {

    private MaxHeightRecyclerView recyclerView;
    private ImageView imgBack;
    private RelativeLayout rlInputByHand;
    private List<ScanResult> wifiList;
    private SearchWifiAdapter adapter;
    private int GPS_REQUEST_CODE = 10;
    private boolean noNeedKey;
    private Handler handler = new Handler();
    private Runnable task;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_aura_mate_search_wifi);
        initView();
        initData();
    }

    @Override
    protected boolean PCNeedFinish() {
        return !TextUtils.isEmpty(equipmentId);
    }

    private void initView() {
        imgBack = findViewById(R.id.img_back);
        imgBack.setOnClickListener(this);
        recyclerView = findViewById(R.id.rv_wifi);
        rlInputByHand = findViewById(R.id.rl_input_by_hand);
        rlInputByHand.setOnClickListener(this);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setHasFixedSize(true);
        //添加自定义分割线：可自定义分割线高度和颜色
        recyclerView.addItemDecoration(new RecycleViewDivider(
                this, LinearLayoutManager.HORIZONTAL, SizeUtils.dp2px(0.5f), getResources().getColor(R.color.gray_d8d8d8)));
    }

    private void initData() {
        task = new Runnable() {
            @Override
            public void run() {
                searchWifi();
                handler.postDelayed(task, 5000);
            }
        };
        noNeedKey = getIntent().getBooleanExtra("noNeedKey", false);
        if (!NetworkUtils.getWifiEnabled()) {
            NetworkUtils.setWifiEnabled(true);
        }
        if (!checkGPSIsOpen()) {
            confirmGpsDialog();
        } else {
            checkLocationPermission();
        }
    }

    public List<ScanResult> getWifiList() {
        WifiManager wifiManager = (WifiManager) getApplicationContext().getSystemService(WIFI_SERVICE);
        wifiManager.startScan();
        List<ScanResult> scanWifiList = wifiManager.getScanResults();
        List<ScanResult> wifiList = new ArrayList<>();
        if (scanWifiList != null && scanWifiList.size() > 0) {
            HashMap<String, Integer> signalStrength = new HashMap<String, Integer>();
            for (int i = 0; i < scanWifiList.size(); i++) {
                ScanResult scanResult = scanWifiList.get(i);
                if (!scanResult.SSID.isEmpty()) {
                    String key = scanResult.SSID + " " + scanResult.capabilities;
                    if (!signalStrength.containsKey(key)) {
                        signalStrength.put(key, i);
                        wifiList.add(scanResult);
                    }
                }
            }
        }
        return wifiList;
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == 100) {
            handler.post(task);
        }
    }

    private void searchWifi() {
        wifiList = getWifiList();
        if (recyclerView.getAdapter() == null) {
            adapter = new SearchWifiAdapter(wifiList, this, noNeedKey, equipmentId);
            recyclerView.setAdapter(adapter);
        } else {
            SearchWifiAdapter searchWifiAdapter = (SearchWifiAdapter) recyclerView.getAdapter();
            searchWifiAdapter.getWifiList().clear();
            searchWifiAdapter.getWifiList().addAll(wifiList);
            searchWifiAdapter.notifyDataSetChanged();
        }
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == GPS_REQUEST_CODE) {
            if (!checkGPSIsOpen()) {
                confirmGpsDialog();
            } else {
                checkLocationPermission();
            }
        }
    }

    /**
     * 检测GPS是否打开
     *
     * @return
     */
    private boolean checkGPSIsOpen() {
        boolean isOpen;
        LocationManager locationManager = (LocationManager) this
                .getSystemService(Context.LOCATION_SERVICE);
        isOpen = locationManager.isProviderEnabled(android.location.LocationManager.GPS_PROVIDER);
        return isOpen;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        handler.removeCallbacksAndMessages(null);
    }

    private void confirmGpsDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(this, CloudCommonPopupConstants.GPS_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.gps_permission));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                Intent intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                startActivityForResult(intent, GPS_REQUEST_CODE);
                dialog.dismiss();

            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                ActivityUtils.finishActivity(AuraMateSearchWifiActivity.class);
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.rl_input_by_hand:
                Intent intent = new Intent(AuraMateSearchWifiActivity.this, AuraMateWifiActivity.class);
                intent.putExtra("noNeedKey", noNeedKey);
                ActivityUtils.startActivity(intent);
                break;
            case R.id.img_back:
                ActivityUtils.finishActivity(this);
                break;
        }
    }

    private void checkLocationPermission() {
        String[] permissions = {Manifest.permission.ACCESS_COARSE_LOCATION, Manifest.permission.ACCESS_FINE_LOCATION};
        boolean flag = isGranted(permissions);
        if (flag) { //权限开启状态
            handler.post(task);
        }else{//权限关闭状态
            PermissionUtil.checkPermissionWithDialog(
                    this,
                    getString(R.string.starry_popupwindow_title),
                    getString(R.string.czur_permission_location),
                    getString(R.string.starry_go_open_permission),
                    getString(R.string.starry_background_start_msg_cancel),
                    v -> {
                        if (v != null){//点击去设置
                            PermissionUtil.useToolsRequestPermission(
                                    permissions,
                                    () -> handler.post(task),
                                    this::cancelAction
                            );
                        }else{
                            cancelAction();
                        }
                    });
        }

    }

    private void cancelAction(){
        ActivityUtils.finishActivity(AuraMateSearchWifiActivity.class);
    }
}
