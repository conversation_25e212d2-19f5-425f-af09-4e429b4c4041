package com.czur.cloud.ui.mirror;


import static com.czur.czurutils.log.CZURLogUtilsKt.logE;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattService;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Bitmap;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.blankj.utilcode.util.Utils;
import com.clj.fastble.BleManager;
import com.clj.fastble.callback.BleGattCallback;
import com.clj.fastble.callback.BleScanCallback;
import com.clj.fastble.data.BleDevice;
import com.clj.fastble.exception.BleException;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.entity.realm.SPReportSittingEntity;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.SittingCommonEvent;
import com.czur.cloud.event.SittingModelChangeEvent;
import com.czur.cloud.event.SittingPictureEvent;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.MirrorOfflinePreferences;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.auramate.reportfragment.ReportUtil;
import com.czur.cloud.ui.base.LazyLoadBaseFragment;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.mirror.adapter.SittingEquipmentAdapter;
import com.czur.cloud.ui.mirror.comm.FastBleConstants;
import com.czur.cloud.ui.mirror.comm.FastBleHexUtils;
import com.czur.cloud.ui.mirror.comm.FastBleHttpUtils;
import com.czur.cloud.ui.mirror.comm.FastBleOperationPictureErrorUtils;
import com.czur.cloud.ui.mirror.comm.FastBleOperationPictureUtils;
import com.czur.cloud.ui.mirror.comm.FastBleOperationUtils;
import com.czur.cloud.ui.mirror.comm.FastBleToolUtils;
import com.czur.cloud.ui.mirror.comm.SittingOssTokenModel;
import com.czur.cloud.ui.mirror.comm.SyncSittingReportEntity;
import com.czur.cloud.ui.mirror.comm.SyncSittingReportServerEntity;
import com.czur.cloud.ui.mirror.model.BaseUpdateModel;
import com.czur.cloud.ui.mirror.model.SittingDeviceModel;
import com.czur.cloud.ui.mirror.model.SittingDeviceOfflineModel;
import com.czur.cloud.ui.mirror.model.SittingReportModelSub;
import com.czur.cloud.ui.mirror.model.SittingUpdateModel;
import com.czur.cloud.ui.mirror.mydialog.SittingSensitivityDialog;
import com.czur.cloud.util.validator.Validator;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lsjwzh.widget.recyclerviewpager.RecyclerViewPager;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.Timer;
import java.util.TimerTask;
import java.util.UUID;

import io.realm.Realm;
import io.realm.RealmResults;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public class SittingFragment extends LazyLoadBaseFragment {

    private RecyclerViewPager recyclerView;
    private SittingEquipmentAdapter adapter;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private SmartSittingActivity activity;
    private List<SittingDeviceModel> deviceModels;
    private Realm realm;
    private LinearLayoutManager linearLayoutManager;
    private TextView tvNoNetWork;
    private RelativeLayout rlNoNetWork;
    private RecyclerViewPager.OnPageChangedListener onPageChangedListener;
    private SittingDeviceModel currentDeviceModel;
    private boolean isFirstBind=false;  //第一次绑定成功后，跳转到首页了。
    private boolean isAutoConnect=false;  //断开ble自动重连。
    private String reportNow, reportHist; // 坐姿报告数据接收-当前报告：0
    private String localCommonPicturePath =""; //图片本地存储的路径，相同的前缀，但是后面需要追加不同的文件夹名称
    private boolean isScanBleDevice = false;    // 是否已经扫到ble设备了？
    private SmartRefreshLayout refreshLayoutSmart;
    private int currentPosition;
    private int retryTimes = 0, retryPicTimes = 0, retryErrorPicTimes = 0 , retryMTUTimes=0, retryTimeZoneTimes=0;
    private int retrySecond = 0, retryPicSecond = 0, retryErrorPicSecond = 0, retryMTUSecond = 0 ;
    private int retryReConnectBleTimes = 0;     // 重连次数；
    private int retryReflashTimes = 0;     // 刷新下拉的次数；
    private int retryReconnectBleSecond = FastBleConstants.RUN_DELAY_TIMES1000;
    private String deviceVersion;   // 设备端固件版本号字符串
    private boolean isReSendUpdate = false;     // 固件升级是否需要断点续传？
    private long lastTime = 0;
    private boolean isUnbindDevice = false;     // 已经解绑了设备，不需要再进行重连了

    private Handler handleAlgo = new Handler();
    private Handler handleApp = new Handler();
    private Runnable runnableAlgo;
    private Runnable runnableApp;

    private MirrorOfflinePreferences offlinePreferences;       // 离线保存的数据
    private List<SittingDeviceOfflineModel> bindModels;     // bind数据列表
    private List<SittingDeviceOfflineModel> unbindModels;   // unbind数据列表
//    private SittingDeviceOfflineModel sitPicModel;          // 标准坐姿图
    private List<SittingDeviceOfflineModel> sitPicModels; // 标准坐姿图
    private List<SittingDeviceOfflineModel> errorPicModels; // 错误坐姿列表
    private List<SittingDeviceOfflineModel> happyPicModels; // 开心图列表
    private SittingDeviceModel settingsModels;     // settings数据列表

    private LinearLayout sittingHomeShareLl; // 分享按钮
    private RelativeLayout sittingHomeCircleRl; //坐姿准确率
    private RelativeLayout sittingHomeSmallCircleLeftRl;    //久坐超时次数
    private RelativeLayout sittingHomeSmallCircleRightRl;   //愉悦指数
    private TextView sitting_home_time;

    private long last_time=0;

    private boolean isBluetoothOpen=true;       // 蓝牙已开启

    // 定时进行离线报告检查发送
    private Timer offlineTimer = new Timer();
    TimerTask offlineTask = new TimerTask() {
        @Override
        public void run() {
            try {
                syncReportJsonDataToServer();
                syncAlgoJsonDataToServer();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    };

    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_sitting_home;
    }

    //每次进入主页，执行流程：
    //1,服务器获取device list
    //2,有相同，更新本地数据；
    //3,如果设备已经连接（省去扫描和自动连接过程！）
    //4,进行device list数据判断，adapter数据设置（是否为空，adapter的事件响应）
    //5,打开opennotify、pic notify 、error notify
    //6,成功，发mtu
    //7,成功/失败，re鉴权；
    //8,  发timezone     （同步）
    //    检查固件版本号    （延迟1s）
    //    检查坐姿模式    （延迟3s）
    //9,  发aglo的config  （延迟60s）60*1
    //    发app的config   （延迟120s）60*2
    //10, 获取happy图片   （延迟180*2s）60*3
    //    获取error图片   （延迟240*4s）60*4

    //11,发送服务器报告成功后，延时服务器获取当日的报告数据，刷新UI；
    //12,完成数据加载！取消loading   （延迟5s）

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        logI("SittingFragment.onEvent="+event.getEventType());

        switch (event.getEventType()) {

            // 静音状态
            case SITTING_APPGET_SILENT:
                if (event instanceof SittingCommonEvent) {
                    SittingCommonEvent commonEventSilent = (SittingCommonEvent) event;
                    String paramsSilent = commonEventSilent.getParams();
                    logI("SittingFragment.onEvent.SITTING_APPGET_SILENT.paramsSilent=" + paramsSilent);
                    if (paramsSilent != null && !paramsSilent.equals("")) {
                        if (paramsSilent.equals(FastBleConstants.SITTING_SILENT_INT_YES+"")){
                            FastBleOperationUtils.setDeviceSilent(FastBleConstants.SITTING_SILENT_INT_YES);
                        }else{
                            FastBleOperationUtils.setDeviceSilent(FastBleConstants.SITTING_SILENT_INT_NO);
                        }
                    }
                }
                break;

            // 算法学习更新json数据
            case SITTING_APPGET_ALGO:
                logI("SittingFragment.onEvent.SITTING_APPGET_ALGO====");
                SittingCommonEvent commonEventAlgo = (SittingCommonEvent) event;
                String paramsAlgo = commonEventAlgo.getParams();
                onDealWithDeviceAlgoData(paramsAlgo);
                break;

            // 日期、时间、时区变化了
            case SITTING_TIMEZONE_CHANGE:
                logI("SittingFragment.onEvent.SITTING_TIMEZONE_CHANGE====");
                if (FastBleOperationUtils.getBleDevice() != null) {
                    if (BleManager.getInstance().isConnected(FastBleOperationUtils.getBleDevice())){

                        if (retryTimeZoneTimes > FastBleConstants.MAX_MTU_RETRY_TIMES){
                            return;
                        }
                        // 设置语言时区
                        String sendStr = getTimeZoneNew();
                        FastBleOperationUtils.SetDeviceParamsStr(sendStr, FastBleConstants.HEAD_SETTING_ZONE);

                        retryTimeZoneTimes++;
                    }
                }
                break;

            // app从后台唤醒到前台了，
            // 首先判断是否连接设备；
            // 如果未连接，自动重连
            case SITTING_APP_RESUME:
                logI("SittingFragment.onEvent.SITTING_APP_RESUME====");
                if (FastBleOperationUtils.getBleDevice() != null) {
                    if (!BleManager.getInstance().isConnected(FastBleOperationUtils.getBleDevice())){
                        retryReConnectBleTimes = 0;
                        retryReconnectBleSecond = FastBleConstants.RUN_DELAY_TIMES1000;
                        onReTryConnectBleDevices("SITTING_APP_RESUME");
                    }
                }
                break;

            // 固件升级取消
            case SITTING_UPDATE_CANCEL:
                // 取消升级后复位操作
                FastBleOperationUtils.onUpdateCancelOperation();

                break;

            // 自己抢自己，走自动连接逻辑
            case SITTING_DEVICE_AUTOCONNECT:
                logI("SittingFragment.onEvent.SITTING_DEVICE_AUTOCONNECT");
                isAutoConnect = true;
                SittingModelChangeEvent onlineEvent = (SittingModelChangeEvent) event;
                currentDeviceModel = onlineEvent.getDeviceModel();
                EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_SCROLL_TOP, currentDeviceModel));

                // 进行connect
                FastBleOperationUtils.threadSleep(100);
                String address = currentDeviceModel.getAddress();
                connect(address);

                break;

            // 断开重连
            case SITTING_DEVICE_RECONNECT:
                logI("SittingFragment.onEvent.SITTING_DEVICE_RECONNECT");
                isAutoConnect = true;
                SittingModelChangeEvent onlineEvent1 = (SittingModelChangeEvent) event;
                currentDeviceModel = onlineEvent1.getDeviceModel();
                EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_SCROLL_TOP, currentDeviceModel));

                // 进行connect
                FastBleOperationUtils.threadSleep(100);

                // 初始化主界面：
                initHomePageFragment();

                break;


            // 上传报告成功后，需要刷新UI
            case SITTING_REPORT_SUCCESS:
                String eqId = currentDeviceModel.getEquipmentUID();
                getReportDataAndUpdateUI(eqId);
                // 删除本地文件
                deleteLocalReportFile();
                break;

            // 上传algo成功后
            case SITTING_ALGO_SUCCESS:
                // 删除本地文件
                deleteLocalAlgoFile();
                break;

            // 断线重连
            case SITTING_RECONNECT:
                retryReConnectBleTimes = 0;
                retryReconnectBleSecond = FastBleConstants.RUN_DELAY_TIMES1000;
                onReTryConnectBleDevices("SITTING_RECONNECT");
                break;

            // 账号登出
            case SITTING_SYS_LOGOUT:
                logI("SittingFragment.onEvent.SITTING_SYS_LOGOUT======账号登出！");
            case LOG_OUT:
                logI("SittingFragment.onEvent.LOG_OUT======账号登出！");
                BleDevice bleDevice = FastBleOperationUtils.getBleDevice();
                FastBleOperationUtils.closeGatt(FastBleOperationUtils.getBluetoothGatt(), bleDevice);
                break;

            // 固件升级，设备升级结果
            case SITTING_UPDATE_RESULT670:
                if (event instanceof SittingCommonEvent) {
                    SittingCommonEvent commonEvent = (SittingCommonEvent) event;
                    String params1 = commonEvent.getParams();
                    if (params1 != null && params1.equals("0")) {
                        if (AppUtils.isAppForeground()) {
                            ToastUtils.showLong(R.string.sitting_update_success);
                        }
                    }
                }

                FastBleOperationUtils.clearUpdateModel();
                // 需要刷新UI，消除小红点
                currentDeviceModel.setReadyForCheckUpdate(false);

                // 升级完成，需要恢复置灰设置项按钮
                currentDeviceModel.setDeviceUpdating(false);
                EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_DEVICE_UPDATING, currentDeviceModel));

                break;

                //固件接收完毕
            case SITTING_UPDATE_RECV_OK650:

                if (event instanceof SittingCommonEvent) {
                    SittingCommonEvent commonEvent = (SittingCommonEvent) event;
                    String params = commonEvent.getParams();
                    logI("SittingFragment.onEvent.SITTING_UPDATE_RECV_OK.params="+params);
                    // 升级完成，需要恢复置灰设置项按钮
                    currentDeviceModel.setDeviceUpdating(false);
                    if (params != null && !params.equals("")) {
                        // 字符串：0，升级成功；1，升级发生错误
                        if (params.equals("0")){
                            FastBleOperationUtils.setSittingUpdateStatus(FastBleConstants.STATE_UPDATE_SUCESS);
                        }
                    }
                }
                break;

            // 固件升级，断点续传的指令
            case SITTING_UPDATE_RESEND:
                if (event instanceof SittingCommonEvent) {
                    SittingCommonEvent commonEvent = (SittingCommonEvent) event;
                    String params = commonEvent.getParams();
                    logI("SittingFragment.onEvent.SITTING_UPDATE_RESEND.params="+params);
                    if (params != null && !params.equals("")) {

                        // 取消升级，直接断开ble，loading，超时1分钟，重连后，收到640，发690；
                        currentDeviceModel = userPreferences.getSittingDeviceModel();
                        boolean isUpdateCancel = currentDeviceModel.isUpdateCancel();
                        logI("SittingFragment.onEvent.SITTING_UPDATE_RESEND.isUpdateCancel="+isUpdateCancel);
                        if (isUpdateCancel){
                            logI("SittingFragment.onEvent.SITTING_UPDATE_RESEND.send 690");
                            // 是取消了固件升级，发送690；
                            FastBleOperationUtils.SetDeviceParamsStr("", FastBleConstants.HEAD_UPDATE_CANCEL);
                            FastBleOperationUtils.threadSleep(200);

                            // 取消升级后复位操作
                            FastBleOperationUtils.onUpdateCancelOperation();

                            return;
                        }

                        long seekFile = Long.parseLong(params);
                        FastBleOperationUtils.setSeekFile(seekFile);

                        isReSendUpdate = true;
                        //private final int STATE_RESEND      = 5;	//续传固件
                        FastBleOperationUtils.setSittingUpdateStatus(FastBleConstants.STATE_RESEND);

                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                // 升级中，需要置灰设置项按钮
                                FastBleOperationUtils.setSettingItemDisable(true);
                                // 需要启动updateActivity页面
                                Intent intent = new Intent(getContext(), SittingUpdateActivity.class);
                                intent.putExtra("deviceModel", currentDeviceModel);
                                intent.putExtra("deviceVersion", deviceVersion);
                                intent.putExtra("seekFile", seekFile);
                                intent.putExtra("activityBackFlag", true);
                                ActivityUtils.startActivity(intent);
                            }
                        }, FastBleConstants.RUN_DELAY_TIMES1000*2);
                    }
                }
                break;

            // 坐姿体验模式结束
            case SITTING_EXPER_EXIT:
                FastBleOperationUtils.setIsExperience(false);
                break;

            // 固件升级
            case SITTING_UPDATE_QUERY:
                //固件版本查询  设备端返回：固件版本号字符串"
                if (event instanceof SittingCommonEvent) {
                    SittingCommonEvent commonEvent = (SittingCommonEvent) event;
                    String params = commonEvent.getParams();
                    logI("SittingFragment.onEvent.SITTING_UPDATE_QUERY.params="+params);
                    if (params != null && !params.equals("")) {

                        // 进行比对，是否需要升级  // 检查是否需要升级
                        // 使用和feedback一样的请求方式

                        // TODO Test版本号
//                        params = "M.C.2111011531";    // 测试环境
//                        params = "M.C.2107011526";      // 生产环境
                        String sn = currentDeviceModel.getEquipmentUID();
                        deviceVersion = params;
                        getSittingServerVersionInfo(params, sn);
                    }
                }
                break;

            // 鉴权:前次绑定设备解绑完成
            case SITTING_AUTH_CLEAR_SUCCESS:
                break;

            // 绑定成功
            case SITTING_BIND_SUCCESS:
                logI("SittingFragment.00.SITTING_BIND_SUCCESS");
                // 从设备首次绑定跳转过来的，再次进入就不走这里了
                isFirstBind = true;
                currentDeviceModel = userPreferences.getSittingDeviceModel();

                if (!NetworkUtils.isConnected()) {
                    // 离线缓存bind=1 / unbind=2 信息数据
                    String uid = userPreferences.getUserId();
                    String equipmentUuid = currentDeviceModel.getEquipmentUID();
                    String mac = currentDeviceModel.getMac();
                    cacheOfflineBindUnbind(1, uid, equipmentUuid, mac);

                    // 需要把新的设备加入到本地设备列表中；
                    deviceModels = userPreferences.getSittingDevices();
                    deviceModels.add(currentDeviceModel);
                    userPreferences.setSittingDevices(deviceModels);

                    // 断网的记录中
                    settingsModels.setDeviceModel(currentDeviceModel);
                }

                // 初始化主界面：
                // 和从app进入一样的init
                initHomePageFragment();

                break;

            // 打开通知成功
            case SITTING_OPEN_NOTIFY_SUCCESS:
                if (!FastBleOperationUtils.isIsChangeConnectActivity()) {
                    long cTime = Calendar.getInstance().getTimeInMillis();
                    int spend = (int)(cTime - last_time);
                    logI("SittingFragment.07.SITTING_OPEN_NOTIFY_SUCCESS,and setAuthentication");
                    FastBleOperationUtils.setMtu(FastBleConstants.PARAMS_SECOND_CONNECT);
                    FastBleOperationUtils.threadSleep(500);
                    // re鉴权
                    setAuthentication();
                }
                break;

            // MTU协商结果：3
            case SITTING_FEEDBACK_MTU:
            // MTU设置返回
            case SITTING_MTU_SUCCESS:
//                // 初始化参数设置
//                onSetupDeviceParams();
                break;

            // MTU设置返回
            case SITTING_MTU_FAIL:
                // 如果当前是连接状态，则重新设置，否则，不用了
                if (BleManager.getInstance().isConnected(FastBleOperationUtils.getBleDevice())) {
                    onReSetMTU();
                }else{
                    FastBleOperationUtils.closeGatt(FastBleOperationUtils.getBluetoothGatt(),
                            FastBleOperationUtils.getBleDevice());
                }
                break;

            // MTU失败,重连
            case SITTING_MTU_FAIL_RETRY:
                // 设置mtu失败，mtu=514了，需要断开ble，重连
                logI("SittingFragment.SITTING_MTU_FAIL_RETRY");
                BleManager.getInstance().disconnect(FastBleOperationUtils.getBleDevice());
                FastBleOperationUtils.threadSleep(FastBleConstants.RUN_DELAY_TIMES1000);
                // 初始化主界面：
                initHomePageFragment();
                break;

            case SITTING_AUTH_SUCCESS:
                if (!FastBleOperationUtils.isIsChangeConnectActivity()) {
                    logI("SittingFragment.08.SITTING_AUTH_SUCCESS, and onSetupDeviceParams");

                    // 连接成功，需要进行初始化设置；
                    setDeviceOnline();

                    if (isAutoConnect) {
                        // 此时进行初始化的操作
                        // 设置语言时区
                        String sendStr = getTimeZoneNew();
                        FastBleOperationUtils.threadSleep();
                        FastBleOperationUtils.SetDeviceParamsStr(sendStr, FastBleConstants.HEAD_SETTING_ZONE);
                        isAutoConnect = false;
                    }else{
                        // 初始化参数设置
                        onSetupDeviceParams();
                    }
                }
                break;

            // TimeZone
            case SITTING_TIMEZONE_FAIL:
                // 设置语言时区
                String sendStr = getTimeZoneNew();
                FastBleOperationUtils.SetDeviceParamsStr(sendStr, FastBleConstants.HEAD_SETTING_ZONE);
                break;

            case SITTING_TIMEZONE_SUCCESS:
            case SITTING_OPEN_NOTIFY_PIC_SUCCESS:
            case SITTING_OPEN_NOTIFY_PICERR_SUCCESS:
                break;

            case SITTING_UNBIND:
                SittingDeviceModel model = new SittingDeviceModel();
                model.initDeviceModel();
                model.setId(0);
                model.setMac("");
                model.setDeviceUUID("");
//                model.setDeviceName("");
                currentDeviceModel = model;
                userPreferences.setSittingDeviceModel(currentDeviceModel);

                if (!NetworkUtils.isConnected()) {
                    // 本地加载列表及数据
                    getMirrorDevicesListFromLocal();
                }else {
                    // 断开设备，重新连接
                    getMirrorDevicesList();
                }
                break;

            case SITTING_IS_ONLINE:
            case SITTING_CHANGE_DEVICENAME:
            case SITTING_CHANGE_LONGSIT:
            case SITTING_CHANGE_SENSITIVITY:
            case SITTING_CHANGE_VOLUME:
            case SITTING_CHANGE_LIGHTLEVEL:
            case SITTING_CHANGE_HAPPYTIME:
            case SITTING_CHANGE_MODEL:
            case SITTING_OFFLINE:
            case SITTING_IS_UPDATE: //// 设备升级
            case SITTING_DEVICE_UPDATING:  //设备升级中
            case SITTING_LOADING:   //loading
            case SITTING_INIT_STATUS:   //初始状态
            case SITTING_CHANGE_HAPPYTIME_SWITCH:// 开心瞬间、错误坐姿开关
            case SITTING_CHANGE_ERRORTIME_SWITCH:
            case SITTING_NETWORK_ONLINE:    // 网络在线、离线
            case SITTING_NETWORK_OFFLINE:
            case SITTING_SCROLL_TOP:    // 使scroll移到顶部；
            case SITTING_CANCEL_UPDATE:     // 固件升级取消

                setRefreshData(event);
                break;

            case SITTING_UNSCAN_BLE:// 没有扫到ble设备，弹出提示框
                setRefreshData(event);
                onReTryConnectBleDevices("SITTING_UNSCAN_BLE");
                break;

            case SITTING_FEEDBACK_SETTING:  //设备反馈:配置结果:00
                SittingCommonEvent commonEvent1 = (SittingCommonEvent)event;
                String params1 = commonEvent1.getParams();
                logI("SittingFragment.onEvent.SITTING_FEEDBACK_SETTING="+ params1);

                break;

            case SITTING_PICTURE_STANDAR_CHANGE:
//                currentDeviceModel.setInputStatus(1);
//                userPreferences.setSittingDeviceModel(currentDeviceModel);
//                break;

            case SITTING_APPGET_STATUS: //坐姿监控模式反馈：0：智能坐姿监控；1：自定义坐姿监控；
                setRefreshUIData(event);
                break;

            case SITTING_FEEDBACK_INPUT://设备主动发出 //坐姿录入结果
                SittingCommonEvent commonEvent = (SittingCommonEvent)event;
                String params = commonEvent.getParams();
                logI("SittingFragment.onEvent.SITTING_FEEDBACK_INPUT.params="+params);

                // 坐姿录入结果反馈处理并上传服务器
                onDealWithDeviceInputStatus(params);
                setRefreshUIData(event);

                // 获取录入的标准坐姿图片
                if (params.equals("0") || params.equals("1")){
                    logI("SittingFragment.onEvent.SITTING_FEEDBACK_INPUT.getPicture.HEAD_APPGET_STANDER=");
                    FastBleOperationUtils.SetDeviceParamsStr("", FastBleConstants.HEAD_APPGET_STANDER);
                    return;
                }else{

                }
                logI("SittingFragment.onEvent.SITTING_FEEDBACK_INPUT.params="+params);

                break;

            // 接收传送回来的各种图片
            case SITTING_PICTURE_STANDAR:
                // 注释掉错误坐姿、愉悦心情图片的处理
//            case SITTING_PICTURE_ERROR:
//            case SITTING_PICTURE_HAPPY:
//            case SITTING_PICTURE_STANDAR_ERROR:
                logI("SittingFragment.onEvent.SITTING_PICTURE_STANDAR="+event.getEventType().toString());
                onDealWithDevicePicture(event);
                break;

            // 历史报告发送状态  0， 发送完成；1，未发送完成
            case SITTING_APPGET_REPORT_HISTORY:
                SittingCommonEvent commonEventHis = (SittingCommonEvent)event;
                String paramsHis = commonEventHis.getParams();
                if (paramsHis.equals("1")) {
                    FastBleOperationUtils.SetDeviceParamsStr("", FastBleConstants.HEAD_APPGET_REPORT1);
                }
                break;

            // 坐姿报告数据接收
            case SITTING_REPORT_NOW:
                SittingCommonEvent reportEvent = (SittingCommonEvent) event;
                reportNow = reportEvent.getParams();
                onDealWithDeviceReport(reportNow, true);
                break;

            case SITTING_REPORT_HISTORY:
                SittingCommonEvent reportEvent1 = (SittingCommonEvent) event;
                reportHist = reportEvent1.getParams();
                onDealWithDeviceReport(reportHist);
                break;

            case SITTING_AUTH_WRITE_FAIL:
                // 写入失败，如何处理？
                // FastBleOperationUtils.write.onWriteFailure=BleException
                // { code=102, description='exception occur while writing: gatt writeCharacteristic fail'}

                break;

            // 打开通知失败
            case SITTING_OPEN_NOTIFY_FAIL:
                BleDevice ble = FastBleOperationUtils.getBleDevice();
                BluetoothGatt bleGatt = BleManager.getInstance().getBluetoothGatt(ble);
                onReOpenNotify(bleGatt);
                break;

            case SITTING_OPEN_NOTIFY_PIC_FAIL:
                BleDevice ble_pic = FastBleOperationUtils.getBleDevice();
                BluetoothGatt bleGatt_pic = BleManager.getInstance().getBluetoothGatt(ble_pic);
                onReOpenNotifyPic(bleGatt_pic);
                break;
            case SITTING_OPEN_NOTIFY_PICERR_FAIL:
//                BleDevice ble_err_pic = FastBleOperationUtils.getBleDevice();
//                BluetoothGatt bleGatt_err_pic = BleManager.getInstance().getBluetoothGatt(ble_err_pic);
//                onReOpenNotifyErrorPic(bleGatt_err_pic);
                break;

            // 各种失败的情况
            case SITTING_AUTH_FAIL:
            case SITTING_AUTH_CLEAR_FAIL:
                //失败的处理???
//                onDealWithSettingFail();
                break;

            default:
                break;
        }
    }

    // 删除本地algo文件
    private void deleteLocalAlgoFile() {
        String fDir =
                CZURConstants.MIRROR_PATH +
                        userPreferences.getUserId() + "/" +
                        currentDeviceModel.getEquipmentUID() +
                        FastBleConstants.SITTING_ALGO_DATA_PATH;
        logI("deleteLocalAlgoFile.fDir="+fDir);
        File file = new File(fDir);
        FastBleToolUtils.deleteAllFile(file);
    }

    // 删除本地报告文件
    private void deleteLocalReportFile() {
        String fDir =
                CZURConstants.MIRROR_PATH +
                        userPreferences.getUserId() + "/" +
                        currentDeviceModel.getEquipmentUID() +
                        FastBleConstants.SITTING_POSE_REPORT_PATH;
        logI("deleteLocalReportFile.fDir="+fDir);
//        File file = new File(fDir);
//        FastBleToolUtils.deleteAllFile(file);
        List<String> jsonList = FastBleOperationUtils.getOfflineReportFileList();
        for (String filename: jsonList) {
            String full_filename = fDir + filename;
            try {
                FastBleToolUtils.deleteOnlyFile(full_filename);
                logI("deleteLocalReportFile.full_filename="+full_filename);
            }catch(Exception e){

            }
        }
        List<String> jsonList1 = new ArrayList<>();
        FastBleOperationUtils.setOfflineReportFileList(jsonList1);
    }

    private void onReflashDataReport(int delay){
        logI("onReflashDataReport.delay==="+delay+",retryReflashTimes="+retryReflashTimes);

        // 此处可以进行数据刷新工作；
        // 自动下拉刷新，获取新数据
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (refreshLayoutSmart != null) {
                    try {
                        refreshLayoutSmart.autoRefresh();
                    }catch (Exception e){
                        //
                    }
                }else{
                    if (retryReflashTimes > 50){
                        return;
                    }
                    retryReflashTimes++;
                    int delay1 = delay + FastBleConstants.RUN_DELAY_TIMES100;
                    onReflashDataReport(delay1);
                }
            }
        }, delay);
    }

    public static SittingFragment newInstance(String device) {
        SittingFragment auraMateFragment = new SittingFragment();
        Bundle bundle = new Bundle();
        bundle.putString("device", device);
        auraMateFragment.setArguments(bundle);
        return auraMateFragment;
    }

    @Override
    public void onFragmentResume() {
        super.onFragmentResume();
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }


    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        refreshLayoutSmart = null;
        last_time = Calendar.getInstance().getTimeInMillis();

        // 蓝牙开关监听
        IntentFilter statusFilter = new IntentFilter(BluetoothAdapter.ACTION_STATE_CHANGED);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            getContext().registerReceiver(mStatusReceive, statusFilter, Context.RECEIVER_NOT_EXPORTED);
        }else {
            getContext().registerReceiver(mStatusReceive, statusFilter);
        }

        retryReconnectBleSecond = FastBleConstants.RUN_DELAY_TIMES1000;
        retrySecond = FastBleConstants.RUN_DELAY_TIMES1000;
        retryPicSecond = FastBleConstants.RUN_DELAY_TIMES1000;
        retryErrorPicSecond = FastBleConstants.RUN_DELAY_TIMES1000;
        retryMTUSecond = FastBleConstants.RUN_DELAY_TIMES500;

        activity = (SmartSittingActivity) getActivity();
        userPreferences = UserPreferences.getInstance(activity);

        currentDeviceModel = userPreferences.getSittingDeviceModel();
        //初始化currentDeviceModel
        if (Validator.isEmpty(currentDeviceModel)
                || currentDeviceModel == null
//                || currentDeviceModel.getId()==0
                || currentDeviceModel.getMac() == null){
            currentDeviceModel = new SittingDeviceModel();
            currentDeviceModel.initDeviceModel();
        }

        // 初始化离线数据
        initOfflineData();

        isFirstBind = false;
        isAutoConnect = false;
        int status = FastBleOperationUtils.getSittingUpdateStatus();
        if (status < FastBleConstants.STATE_UPDATING ||
                status > FastBleConstants.STATE_REBOOT ){
            FastBleOperationUtils.setSittingUpdateStatus(FastBleConstants.STATE_NEW);
        }

        currentDeviceModel.setDeviceUpdating(FastBleOperationUtils.isIsDeviceUpdating());

        initNetListener();
        // 初始化主界面：
        initHomePageFragment();

        // 定时检测离线报告
        logI("SittingFragment.offlineTimer.schedule");
        offlineTimer.schedule(offlineTask,FastBleConstants.RUN_DELAY_TIMES1000 * 60,FastBleConstants.RUN_DELAY_TIMES1000 * 60 * 5 );

    }

    // 初始化主界面：
    // 判断ble是否连接；
    // 从服务器获取device list；
    private void initHomePageFragment(){
        long cTime = Calendar.getInstance().getTimeInMillis();
        int spend = (int)(cTime - last_time);
        logI("SittingFragment.01.initHomePageFragment.初始化主界面,");

        if (!NetworkUtils.isConnected()) {

            // 本地加载列表及数据
            getMirrorDevicesListFromLocal();
            setHomeButtonOnlineOffline(false);

        }else {

            if (isFirstBind){
                //首次绑定成功，无需同步本地数据
            }else {
                // 先同步一下本地数据，然后再继续
                syncOfflineDatasToServer();
                setHomeButtonOnlineOffline(true);
            }

            // 进入Mirror系统时，先获取服务器的设备列表
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    getMirrorDevicesList();
                }
            }, FastBleConstants.RUN_DELAY_TIMES500);
        }
    }

    private void initNetListener() {
        NetworkUtils.registerNetworkStatusChangedListener(new NetworkUtils.OnNetworkStatusChangedListener() {
            @Override
            public void onDisconnected() {
                // 没有网络，需要禁用的按钮功能
                setHomeButtonOnlineOffline(false);
                logI("SittingFragment.initNetListener.onDisconnected.NetworkUtils.isConnected()="+NetworkUtils.isConnected());
            }

            @Override
            public void onConnected(NetworkUtils.NetworkType networkType) {

                setHomeButtonOnlineOffline(true);

                // 重新联网，需要同步绑定缓存数据
                syncOfflineDatasToServer();
                logI("SittingFragment.initNetListener.onConnected.NetworkUtils.isConnected()="+NetworkUtils.isConnected());

                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        getMirrorDevicesList();
                    }
                }, FastBleConstants.RUN_DELAY_TIMES1000*2);

            }
        });
    }

    @Override
    protected void initView(View view) {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
        realm = Realm.getDefaultInstance();
        httpManager = HttpManager.getInstance();
        recyclerView = (RecyclerViewPager) view.findViewById(R.id.recycler_view);
        recyclerView.setPadding(ScreenUtils.getScreenWidth() * 3 / 64, 0, ScreenUtils.getScreenWidth() * 3 / 64, 0);
        linearLayoutManager = new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false);
        recyclerView.setLayoutManager(linearLayoutManager);
        recyclerView.setNestedScrollingEnabled(true);
        rlNoNetWork = view.findViewById(R.id.rl_no_network);
        tvNoNetWork = view.findViewById(R.id.tv_click_refresh);
        tvNoNetWork.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                rlNoNetWork.setVisibility(View.GONE);
                //这块要等待socket断网后重连
                getMirrorDevicesList();
            }
        });
        onPageChangedListener = new RecyclerViewPager.OnPageChangedListener() {
            @Override
            public void OnPageChanged(int before, int current) {
                currentPosition = current;
            }
        };
        recyclerView.addOnPageChangedListener(onPageChangedListener);
    }

    // 本地加载列表及数据
    private void getMirrorDevicesListFromLocal() {
        deviceModels = UserPreferences.getInstance().getSittingDevices();

        // 处理数据并初始化主页面
        initHomePageAndRefreshData();

    }

    public void getMirrorDevicesList() {
        if (!NetworkUtils.isConnected()) {
//            showNoNetWorkUI();
            return;
        }

        HttpManager.getInstance().request().getSittingDevices(
                userPreferences.getUserId(),
                new TypeToken<List<SittingDeviceModel>>() { }.getType(),
                new MiaoHttpManager.Callback<SittingDeviceModel>() {

            @Override
            public void onStart() { }

            @Override
            public void onResponse(MiaoHttpEntity<SittingDeviceModel> entity) {

                if (entity != null && entity.getBodyList() != null) {
                    deviceModels = entity.getBodyList();
                } else {
                    showMessage(R.string.request_failed_alert);
                    deviceModels = new ArrayList<>();
                }
                long cTime = Calendar.getInstance().getTimeInMillis();
                int spend = (int)(cTime - last_time);

                // 缓存服务器的设备列表
                setAllDevicesListData();

                // 处理数据并初始化主页面
                initHomePageAndRefreshData();

            }

            @Override
            public void onFailure(MiaoHttpEntity<SittingDeviceModel> entity) {
                showMessage(R.string.request_failed_alert);
                showNoNetWorkUI();
            }

            @Override
            public void onError(Exception e) {
                showMessage(R.string.request_failed_alert);
                logE("SittingFragment.getAuraDevicesList.onError.e="+e.toString());
                showNoNetWorkUI();
            }
        });
    }

    // 缓存服务器的设备列表
    // 此处需要逐个更新数据，可以保留大圆圈的数据，否则，离线设备没有大圆圈的数据了；
    private void setAllDevicesListData() {

        if (deviceModels == null || deviceModels.size()<1) {
            userPreferences.setSittingDevices(deviceModels);
            UserPreferences.getInstance().getSittingDevices();
            return;
        }

        List<SittingDeviceModel> oldDevicesModels = userPreferences.getSittingDevices();
        if (oldDevicesModels == null ){
            oldDevicesModels = new ArrayList<>();
        }
        List<SittingDeviceModel> newDevicesModels = new ArrayList<>();
        for (SittingDeviceModel model : deviceModels){
            String mac = model.getMac();
            int uid = model.getBindUserId();
            String equipmentUID = model.getEquipmentUID();
            for (SittingDeviceModel oldmodel : oldDevicesModels){
                String mac1 = oldmodel.getMac();
                int uid1 = oldmodel.getBindUserId();
                String equipmentUID1 = oldmodel.getEquipmentUID();
                if (mac1.equals(mac) && (uid1 == uid) && equipmentUID1.equals(equipmentUID)){
                    oldmodel.updateDeviceModel(model);
                    model = oldmodel;
                    break;
                }
            }
            newDevicesModels.add(model);
        }
        userPreferences.setSittingDevices(newDevicesModels);
        UserPreferences.getInstance().getSittingDevices();
        deviceModels = newDevicesModels;

    }

    // 处理数据并初始化主页面
    private void initHomePageAndRefreshData() {

        // 只有有设备，才下拉刷新
        if (deviceModels != null && deviceModels.size() > 0) {
            onReflashDataReport(FastBleConstants.RUN_DELAY_TIMES50);
        }

        // recyclerView 刷新
        if (recyclerView.getAdapter() == null) {
            adapter = new SittingEquipmentAdapter(activity, deviceModels, realm);
            adapter.setOnItemAddClickListener(onItemAddClickListener);
            recyclerView.setAdapter(adapter);

            // 下拉刷新界面
            adapter.setOnRefreshListener(onRefreshListener);

            // 解绑
            adapter.setOnUnbindClickListener(onUnbindClickListener);

            // Update
            adapter.setOnUpdateClickListener(onUpdateClickListener);

            // Sensitivity Panel
            adapter.setOnSensitivityClickListener(onSensitivityClickListener);


        } else {
            adapter.refreshData(deviceModels);
        }

        // 在外层获取Adapter中的控件
        recyclerView.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN)
            @Override
            public void onGlobalLayout() {
                if (deviceModels == null || deviceModels.size()<1){
                    return;
                }

                int pos = getCurrentPostion();
                View view = linearLayoutManager.findViewByPosition(pos);
                refreshLayoutSmart = (SmartRefreshLayout) view;
//                logI("initHomePageAndRefreshData.refreshLayoutSmart=" + refreshLayoutSmart);

                //OnGlobalLayoutListener可能会被多次触发
                //所以完成了需求后需要移除OnGlobalLayoutListener
                recyclerView.getViewTreeObserver().removeOnGlobalLayoutListener(this);
            }
        });

        long cTime = Calendar.getInstance().getTimeInMillis();
        int spend = (int)(cTime - last_time);
        logI("SittingFragment.02.getAuraDevicesList.获取服务器的设备列表");
        //这块要特殊处理 防止页面两次刷新导致动画卡顿
        if (deviceModels == null || deviceModels.size() == 0) {
            // 初始化model，初始化UI的数据
            currentDeviceModel = new SittingDeviceModel();
            currentDeviceModel.initDeviceModel();
            isUnbindDevice = true;

        }else{
            //有数据的处理过程
            // 找到本地存储的对应的项的mac，跳转过去，并把数据同步一下
            FastBleOperationUtils.setIsChangeConnectActivity(false);
            isUnbindDevice = false;

            if (currentDeviceModel != null
                    && currentDeviceModel.getDeviceUUID() != null
                    && !currentDeviceModel.getDeviceUUID().equals("")){
                String oldUUID = currentDeviceModel.getDeviceUUID();
                if (isFirstBind){
                    oldUUID = FastBleOperationUtils.getNewConnectDeviceMac();
                }
                int index = 0;
                boolean isHaveAddress = false;
                for (SittingDeviceModel model : deviceModels) {
                    String mac1 = model.getMac();
                    String mac = FastBleToolUtils.changeMacAddress(mac1);
                    model.setMac(mac);
                    if (oldUUID.equals(mac)) {
                        logI("SittingFragment.02.getAuraDevicesList.updateLocalDeviceInfo");
                        // 更新同步服务器的数据
                        updateLocalDeviceInfo(model);
                        currentPosition = index;
                        isHaveAddress = true;
                    }else {
                        int postureMode = model.getPostureMode();
                        int sittingMode = postureMode == 1 ? 0:1;
                        model.setPostureMode(sittingMode);
                        deviceModels.get(index).setDeviceModel(model);
                        deviceModels.get(index).setOnline(false);
                    }
                    index++;
                }

                // 本地有mac地址，但是不在服务器返回的list中；
                if (!isHaveAddress){
                    updateLocalDeviceInfo(deviceModels.get(0));
                    currentPosition = 0;
                }
            }else{
                // 默认就是第一个吧？
                updateLocalDeviceInfo(deviceModels.get(0));
                currentPosition = 0;
            }

            //新绑定的设备跳转
            if (isFirstBind) {
                currentPosition = getCurrentPostion();
            }
            recyclerView.scrollToPosition(currentPosition);

            // 如果是新添加的设备，则无需重新连接直接刷新界面即可；
            // 因为在扫喵添加时，已经连接并注册通知了；
            // 初始化坐姿仪设备
            setDeviceInitStatus();

            setInitDevicesConnect();
        }

    }

    // 初始化默认的设备进行ble连接，或者是配置文件获取到的设备进行连接
    private void setInitDevicesConnect(){
        long cTime = Calendar.getInstance().getTimeInMillis();
        int spend = (int)(cTime - last_time);
        // 需要重新扫描设备列表，获取该UUID的设备的mac地址
//        String uuid = currentDeviceModel.getDeviceUUID();
        String uuid_mac = currentDeviceModel.getMac();
        logI("SittingFragment.03.setInitDevicesConnect.初始化默认的设备进行ble连接.uuid_mac="+uuid_mac);

        if (Validator.isEmpty(uuid_mac)){
            setDeviceOffline();
            return;
        }

        if (!currentDeviceModel.getEquipmentUID().equals("")) {
            localCommonPicturePath =
                    CZURConstants.MIRROR_PATH +
                    userPreferences.getUserId() + "/" +
                    currentDeviceModel.getEquipmentUID() + "/" ;

            String localStandardImagePath = localCommonPicturePath + FastBleConstants.SITTING_PICTURE_PATH_STANDAR;
            FastBleOperationUtils.setLocalStandarPicturePath(localStandardImagePath);
        }

        // 在此处判断，是否已经连接ble，已连接，则不需要重新扫描设备了；
        BleDevice bleDevice = FastBleOperationUtils.getBleDevice();
        // 如果ble已经连接，则直接进入opennotify环节
        if (Validator.isNotEmpty(bleDevice) && BleManager.getInstance().isConnected(bleDevice)){
            setDeviceOnline();
            // 进入opennotify
            logI("SittingFragment.03-01.setInitDevicesConnect--ble已经连接");

            if (isFirstBind) {
                EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_SCROLL_TOP, currentDeviceModel));
                // 初始化参数设置
                onSetupDeviceParams();

            }else{
                // 初始化参数设置
                onSetupDeviceParamsEvery();
            }
            return;
        }

        if (!BleManager.getInstance().isBlueEnable()){
            setDeviceOffline();

            logI("SittingFragment.请先打开蓝牙;isBluetoothOpen="+isBluetoothOpen);
            if (!isBluetoothOpen) {
                isBluetoothOpen = true;
                Intent enableBtIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
                startActivityForResult(enableBtIntent, FastBleConstants.REQUEST_CODE_OPEN_BLE);
            }
        }else {
            // 重新连接ble
            logI("SittingFragment.03-02.setInitDevicesConnect--重新连接ble.uuid_mac="+uuid_mac);
            onReStartScan(uuid_mac);
        }
    }

    private void onReStartScan(String currentUUID) {
        try {
            BleManager.getInstance().cancelScan();
        }catch (Exception e){
            //
        }
        long cTime = Calendar.getInstance().getTimeInMillis();
        int spend = (int)(cTime - last_time);
        logI("SittingFragment.04.onReConnectBleDevice.扫描ble设备.uuid="+currentUUID);
        BleManager.getInstance().scan(new BleScanCallback() {
            @Override
            public void onScanStarted(boolean success) {
                logI("SittingFragment.onReConnectBleDevice.success="+success);
                if (!success){
                    // 本次扫描动作是否开启成功 success
                    // 会回到主线程，参数表示本次扫描动作是否开启成功。
                    // 由于蓝牙没有打开，上一次扫描没有结束等原因，会造成扫描开启失败。
                    FastBleOperationUtils.threadSleep();
                    BleManager.getInstance().cancelScan();
                    FastBleOperationUtils.closeGatt(FastBleOperationUtils.getBluetoothGatt(), FastBleOperationUtils.getBleDevice());

                    return;
                }
                lastTime = Calendar.getInstance().getTimeInMillis();
                isScanBleDevice = false;
            }

            @Override
            public void onLeScan(BleDevice bleDevice) {
                super.onLeScan(bleDevice);

            }

            @Override
            public void onScanning(BleDevice bleDevice) {
                if (FastBleOperationUtils.isBelDeviceOK(bleDevice)){
                    //设备发现用 // 0：设备未绑定false，1设备已绑定
                    boolean discover = FastBleOperationUtils.isBelDeviceBind(bleDevice);
                    if (discover) {
                        String uuid = FastBleOperationUtils.getBelDeviceUUID(bleDevice);
                        if (currentUUID.equals(uuid)) {
                            logI("SittingFragment.onReConnectBleDevice.onScanning.isBelDeviceOK=",
                                    "currentUUID=" + currentUUID + " ; uuid=" + uuid,
                                    "getKey="+bleDevice.getKey());
                            isScanBleDevice = true;
                            BleManager.getInstance().cancelScan();

                            // 进行connect
                            FastBleOperationUtils.threadSleep(500);
                            connect(bleDevice);
                        }
                    }
                }
            }

            @Override
            public void onScanFinished(List<BleDevice> scanResultList) {
                long cTime = Calendar.getInstance().getTimeInMillis();
                int aaa = (int)(cTime - lastTime) / 1000;
                logI("SittingFragment.onScanFinished.size========结束扫描======"+scanResultList.size()+","+aaa);

                // 没有扫到ble设备
                if (!isScanBleDevice){
                    EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_UNSCAN_BLE, currentDeviceModel));
                }
            }
        });
    }

    //页面刷新 使用局部刷新
    private void setRefreshData(BaseEvent event) {
        SittingModelChangeEvent onlineEvent = (SittingModelChangeEvent) event;
        SittingDeviceModel model = onlineEvent.getDeviceModel();
        String deviceUdid = model.getEquipmentUID();

        if (Validator.isNotEmpty(deviceModels)) {
            int devicePosition = checkRefreshPosition(deviceUdid, deviceModels);
            if (devicePosition == -1) {
                return;
            }

//            deviceModels.get(devicePosition).setShowLoading(false);
//            currentDeviceModel = userPreferences.getSittingDeviceModel();

            if (event.getEventType().equals(EventType.SITTING_IS_ONLINE)) {
                // 设备在线，全量刷新
                deviceModels.get(devicePosition).setDeviceModel(model);
                deviceModels.get(devicePosition).setOnline(true);
                currentDeviceModel.setOnline(true);

            }else if (event.getEventType().equals(EventType.SITTING_INIT_STATUS)) {
                // 设备未连接
                deviceModels.get(devicePosition).setDeviceModel(model);
                deviceModels.get(devicePosition).setOnline(false);
                currentDeviceModel.setOnline(false);

            }else if (event.getEventType().equals(EventType.SITTING_OFFLINE)) {
                // 设备离线
                deviceModels.get(devicePosition).setOnline(false);
                currentDeviceModel.setOnline(false);

            }else if (event.getEventType().equals(EventType.SITTING_UNSCAN_BLE)) {
                // 未扫到设备
                deviceModels.get(devicePosition).setOnline(false);
                currentDeviceModel.setOnline(false);

            }else if (event.getEventType().equals(EventType.SITTING_IS_UPDATE)) {
                boolean update = model.isReadyForCheckUpdate();
                deviceModels.get(devicePosition).setReadyForCheckUpdate(update);
                currentDeviceModel.setReadyForCheckUpdate(update);

            }else if (event.getEventType().equals(EventType.SITTING_DEVICE_UPDATING)) {
                boolean flag = model.isDeviceUpdating();
                deviceModels.get(devicePosition).setDeviceUpdating(flag);
                currentDeviceModel.setDeviceUpdating(flag);

             }else if (event.getEventType().equals(EventType.SITTING_CHANGE_VOLUME)) {
                int vol = model.getSound();

                if (vol<FastBleConstants.MIN_VOLUME_NUMBER || vol > FastBleConstants.MAX_VOLUME_NUMBER) {
                    vol = FastBleConstants.SITTING_DEF_VOLUME_INT;
                }
                deviceModels.get(devicePosition).setSound(vol);
                currentDeviceModel.setSound(vol);
//                FastBleOperationUtils.SetDeviceParams(vol, FastBleConstants.HEAD_SETTING_VOICE_VOLUME);

            }else if (event.getEventType().equals(EventType.SITTING_CHANGE_SENSITIVITY)) {
                int iSens = model.getLevel();
                String Sens = model.getSensitivity();

                if (iSens < 0 || iSens > CZURConstants.SITTING_SENSITIVITY.length-1) {
                    iSens = FastBleConstants.SITTING_DEF_SENSITIVITY_INT;
                    Sens = CZURConstants.SITTING_SENSITIVITY[CZURConstants.SITTING_SENSITIVITY.length -1 -iSens];
                }
                deviceModels.get(devicePosition).setLevel(iSens);
                deviceModels.get(devicePosition).setSensitivity(Sens);
                currentDeviceModel.setLevel(iSens);
                currentDeviceModel.setSensitivity(Sens);
                FastBleOperationUtils.SetDeviceParams(iSens, FastBleConstants.HEAD_SETTING_ALERT_SENSITIVITY);

            }else if (event.getEventType().equals(EventType.SITTING_CHANGE_LONGSIT)) {
                int iLongSit = model.getSedentaryTime();
                String LongSit = model.getLongSit();

                deviceModels.get(devicePosition).setSedentaryTime(iLongSit);
                deviceModels.get(devicePosition).setLongSit(LongSit);
                currentDeviceModel.setSedentaryTime(iLongSit);
                currentDeviceModel.setLongSit(LongSit);
                FastBleOperationUtils.SetDeviceParams(iLongSit, FastBleConstants.HEAD_SETTING_ALERT_LONGSIT);

            }else if (event.getEventType().equals(EventType.SITTING_CHANGE_DEVICENAME)) {
                String deviceName = model.getDeviceName();
                if (Validator.isEmpty(deviceName)) {
                    deviceName = getResources().getString(R.string.sitting_home_default_name);
                }
                deviceModels.get(devicePosition).setDeviceName(deviceName);
                deviceModels.get(devicePosition).setAlias(deviceName);
                currentDeviceModel.setDeviceName(deviceName);
                currentDeviceModel.setAlias(deviceName);
                FastBleOperationUtils.SetDeviceParamsStr(deviceName, FastBleConstants.HEAD_SETTING_NAME);

            }else if (event.getEventType().equals(EventType.SITTING_CHANGE_HAPPYTIME)) {
                int iHappyTime = model.getiHappyTimeSwitch();
                String happyTimeName = model.getHappyTimeName();
                boolean isHappyTimeOpen = model.isHappyTimeOpen();

                if (iHappyTime < 0 || iHappyTime > CZURConstants.SITTING_HAPPYTIME.length) {
                    iHappyTime = FastBleConstants.SITTING_HAPPYTIME_INT;
                    happyTimeName = CZURConstants.SITTING_HAPPYTIME[iHappyTime];
                }
                deviceModels.get(devicePosition).setHappyTimeOpen(isHappyTimeOpen);
                deviceModels.get(devicePosition).setiHappyTimeSwitch(iHappyTime);
                deviceModels.get(devicePosition).setHappyTimeName(happyTimeName);
                currentDeviceModel.setHappyTimeOpen(isHappyTimeOpen);
                currentDeviceModel.setiHappyTimeSwitch(iHappyTime);
                currentDeviceModel.setHappyTimeName(happyTimeName);

            }else if (event.getEventType().equals(EventType.SITTING_CHANGE_LIGHTLEVEL)) {
                int lightLevel = model.getLight();
                int lightSwitch = model.getLightSwitch();
                String lightName = model.getLightName();

                deviceModels.get(devicePosition).setLight(lightLevel);
                deviceModels.get(devicePosition).setLightName(lightName);
                deviceModels.get(devicePosition).setLightSwitch(lightSwitch);
                currentDeviceModel.setLight(lightLevel);
                currentDeviceModel.setLightName(lightName);
                currentDeviceModel.setLightSwitch(lightSwitch);

            }else if (event.getEventType().equals(EventType.SITTING_CHANGE_MODEL)) {
                int iModel = model.getPostureMode();
                String modelName = model.getPostureModeName();

                deviceModels.get(devicePosition).setPostureMode(iModel);
                deviceModels.get(devicePosition).setPostureModeName(modelName);
                currentDeviceModel.setPostureMode(iModel);
                currentDeviceModel.setPostureModeName(modelName);

                // 字符串：0，智能模式；1，自定义模式
//                FastBleOperationUtils.SetDeviceParams(iModel, FastBleConstants.HEAD_SETTING_MONITOR);

            }else if (event.getEventType().equals(EventType.SITTING_LOADING)) {
                boolean isloading = model.isShowLoading();
                deviceModels.get(devicePosition).setShowLoading(isloading);
                currentDeviceModel.setShowLoading(isloading);

            }else if (event.getEventType().equals(EventType.SITTING_CHANGE_HAPPYTIME_SWITCH)) {
                // 开心瞬间、错误坐姿开关
                boolean flag= model.isHappyTimeOpen();
                int happySwitch = model.getiHappyTimeSwitch();
                String happyName = model.getHappyTimeName();

                deviceModels.get(devicePosition).setHappyTimeOpen(flag);
                deviceModels.get(devicePosition).setiHappyTimeSwitch(happySwitch);
                deviceModels.get(devicePosition).setHappyTimeName(happyName);
                currentDeviceModel.setHappyTimeOpen(flag);
                currentDeviceModel.setiHappyTimeSwitch(happySwitch);
                currentDeviceModel.setHappyTimeName(happyName);

            }else if (event.getEventType().equals(EventType.SITTING_CHANGE_ERRORTIME_SWITCH)) {
                // 开心瞬间、错误坐姿开关
                boolean flag= model.isErrorTimeOpen();
                int happySwitch = model.getiErrorTimeSwitch();
                String happyName = model.getErrorTimeName();

                deviceModels.get(devicePosition).setErrorTimeOpen(flag);
                deviceModels.get(devicePosition).setiErrorTimeSwitch(happySwitch);
                deviceModels.get(devicePosition).setErrorTimeName(happyName);
                currentDeviceModel.setErrorTimeOpen(flag);
                currentDeviceModel.setiErrorTimeSwitch(happySwitch);
                currentDeviceModel.setErrorTimeName(happyName);

            }else if(event.getEventType().equals(EventType.SITTING_NETWORK_OFFLINE)){
                //
            }else if(event.getEventType().equals(EventType.SITTING_NETWORK_ONLINE)){
                //
            }else if (event.getEventType().equals(EventType.SITTING_SCROLL_TOP)) {
                deviceModels.get(devicePosition).setScrollTop(true);
//                currentDeviceModel.setScrollTop(true);
            }else if (event.getEventType().equals(EventType.SITTING_CANCEL_UPDATE)){
                // 固件升级取消
                boolean flag= model.isUpdateCancel();
                deviceModels.get(devicePosition).setUpdateCancel(flag);
                currentDeviceModel.setUpdateCancel(flag);
            }

            if (recyclerView.getAdapter() != null) {
                SittingEquipmentAdapter adapter = (SittingEquipmentAdapter) recyclerView.getAdapter();
                adapter.refreshData(deviceModels);
            }

            //保存设备信息Jason
//            if (event.getEventType().equals(EventType.SITTING_SCROLL_TOP)){
//                deviceModels.get(devicePosition).setScrollTop(false);
//                currentDeviceModel.setScrollTop(false);
//            }
            userPreferences.setSittingDeviceModel(currentDeviceModel);
            userPreferences.setSittingDevices(deviceModels);

            // 记录断网数据
            if (!NetworkUtils.isConnected()){
                settingsModels.setDeviceModel(currentDeviceModel);
                offlinePreferences.setSittingDevicesSettings(settingsModels);
            }
        }
    }

    //页面刷新 使用局部刷新 other
    private void setRefreshUIData(BaseEvent event) {
//        currentDeviceModel = userPreferences.getSittingDeviceModel();
        String deviceUdid = currentDeviceModel.getEquipmentUID();

        if (Validator.isNotEmpty(deviceModels)) {
            int devicePosition = checkRefreshPosition(deviceUdid, deviceModels);
            if (devicePosition == -1) {
                return;
            }

            if(event.getEventType().equals(EventType.SITTING_FEEDBACK_INPUT)){
                SittingCommonEvent commonEvent = (SittingCommonEvent)event;
                //字符串：0，初次成功；1，重新录入成功;2，初次录入失败。-，没有录入坐姿
                String params = commonEvent.getParams();
                // //坐姿录入状态 0：未录入；1：已经录入；2：录入失败；3：重新录入；4：正在录入
                int status=0;
                if (Validator.isEmpty(params)){
                    params="-";
                }
                switch (params) {
                    case "0":   //0，初次成功
                    case "1":   //1，重新录入成功
                    case "3":   //3, 重新录入失败
                        status = 1;
                        break;
                    case "2":   //2，初次录入失败
                        status = 2;
                        break;
                }
                deviceModels.get(devicePosition).setInputStatus(status);
                currentDeviceModel.setInputStatus(status);

            }else if (event.getEventType().equals(EventType.SITTING_PICTURE_STANDAR)){
//                SittingPictureEvent pictureEventEvent = (SittingPictureEvent) event;
//                Bitmap bmp = pictureEventEvent.getBmp();
                deviceModels.get(devicePosition).setInputStatus(1);
                currentDeviceModel.setInputStatus(1);
            }else if (event.getEventType().equals(EventType.SITTING_APPGET_STATUS)){//
                SittingCommonEvent commonEvent = (SittingCommonEvent)event;
                //0：智能坐姿监控；//1：自定义坐姿监控；
                String params = commonEvent.getParams();
                int iModel=0;
                if (params.equals("1")){
                    iModel = 1;
                }
                String modelName=CZURConstants.SITTING_MODEL[iModel];

                deviceModels.get(devicePosition).setPostureMode(iModel);
                deviceModels.get(devicePosition).setPostureModeName(modelName);
                currentDeviceModel.setPostureMode(iModel);
                currentDeviceModel.setPostureModeName(modelName);

                //api需要转换一下：1为智能坐姿 2位自定义坐姿
                int postureMode = iModel == 0 ? 1:2;
                FastBleHttpUtils.setSittingPostureMode(userPreferences.getUserId(),
                        deviceUdid,
                        postureMode + "");

            }else if (event.getEventType().equals(EventType.SITTING_PICTURE_STANDAR_CHANGE)){
                deviceModels.get(devicePosition).setInputStatus(1);
                currentDeviceModel.setInputStatus(1);
            }

            if (recyclerView.getAdapter() != null) {
                SittingEquipmentAdapter adapter = (SittingEquipmentAdapter) recyclerView.getAdapter();
                adapter.refreshData(deviceModels);
            }

            //保存设备信息Jason
            userPreferences.setSittingDeviceModel(currentDeviceModel);
            userPreferences.setSittingDevices(deviceModels);

            // 记录断网数据
            if (!NetworkUtils.isConnected()){
                settingsModels.setDeviceModel(currentDeviceModel);
                offlinePreferences.setSittingDevicesSettings(settingsModels);
            }

        }
    }

    private int checkRefreshPosition(String deviceUdid, List<SittingDeviceModel> deviceList) {
        for (int i = 0; i < deviceList.size(); i++) {
            if (deviceList.get(i).getEquipmentUID().equals(deviceUdid)) {
                return i;
            }
        }
        return -1;
    }

    private void connect(final BleDevice bleDevice) {
        long cTime = Calendar.getInstance().getTimeInMillis();
        int spend = (int)(cTime - last_time);
        logI("SittingFragment.05.connect.连接ble设备Connect");

        // 已经解绑了，不需要connect了！
        if (isUnbindDevice){
            return;
        }

        // 已经连接了，就不需要再次连接了；
        if (BleManager.getInstance().isConnected(bleDevice)){
            return;
        }

        BleManager.getInstance().connect(bleDevice, new BleGattCallback() {

            @Override
            public void onStartConnect() {
                logI("SittingFragment.connect.onStartConnect");
            }

            @Override
            public void onConnectFail(BleDevice bleDevice, BleException exception) {
                logE("SittingFragment.connect.onConnectFail.绑定失败！e=" + exception.toString());
                if (FastBleOperationUtils.isIsChangeConnectActivity()){
                    return;
                }
                try {
                    FastBleOperationUtils.closeGatt(BleManager.getInstance().getBluetoothGatt(bleDevice), bleDevice);
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            onReTryConnectBleDevices("onConnectFail");
                        }
                    }, FastBleConstants.RUN_DELAY_TIMES1000 * 2);
                }catch (Exception e){
                    //
                }
                setDeviceOffline();
            }

            @Override
            public void onConnectSuccess(BleDevice bleDevice, BluetoothGatt gatt, int status) {
                logI("======SittingFragment.connect.onConnectSuccess======绑定成功！======");

                // 需要对bleDevice 进行初始化及监听;
                FastBleOperationUtils.setBleDevice(bleDevice);
                FastBleOperationUtils.setBluetoothGatt(gatt);

                initBleDeviceAndNotification(gatt);
            }

            @Override
            public void onDisConnected(boolean isActiveDisConnected, BleDevice bleDevice, BluetoothGatt gatt, int status) {
                logI("SittingFragment.connect.onDisConnected.isActiveDisConnected=" + isActiveDisConnected);

                //如果通过调用disconnect(BleDevice bleDevice)方法，
                // 主动断开蓝牙连接的结果也会在这个方法中回调，
                // 此时isActiveDisConnected将会是true。

                FastBleOperationUtils.closeGatt(gatt, bleDevice);

                if (isActiveDisConnected) {
                    //主动断开，解绑情况，无需重连
                } else {
//                    ObserverManager.getInstance().notifyObserver(bleDevice);
                    // 被动断开，断线重连机制
                    logI("======SittingFragment.被动断开，重连！======");

                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (!BleManager.getInstance().isConnected(bleDevice)){
                                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_RECONNECT,""));
                            }
                        }
                    },FastBleConstants.RUN_DELAY_TIMES1000);
                }

                // 设备离线
                setDeviceOffline();
            }
        });
    }

    private void connect(final String mac) {
        logI("SittingFragment.connect111.连接ble设备Connect");

        FastBleOperationUtils.closeGatt(FastBleOperationUtils.getBluetoothGatt(), FastBleOperationUtils.getBleDevice());

        BleManager.getInstance().connect(mac, new BleGattCallback() {

            @Override
            public void onStartConnect() {
                logI("SittingFragment.connect111.onStartConnect");
            }

            @Override
            public void onConnectFail(BleDevice bleDevice, BleException exception) {
                logE("SittingFragment.connect111.onConnectFail.绑定失败！e=" + exception.toString());
                try {
                    FastBleOperationUtils.closeGatt(BleManager.getInstance().getBluetoothGatt(bleDevice), bleDevice);
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            onReTryConnectBleDevices("onConnectFail111");
                        }
                    }, FastBleConstants.RUN_DELAY_TIMES1000 * 2);
                }catch (Exception e){
                    //
                }
                setDeviceOffline();
            }

            @Override
            public void onConnectSuccess(BleDevice bleDevice, BluetoothGatt gatt, int status) {
                logI("======SittingFragment.connect111.onConnectSuccess======绑定成功！======");

                // 需要对bleDevice 进行初始化及监听;
                FastBleOperationUtils.setBleDevice(bleDevice);
                FastBleOperationUtils.setBluetoothGatt(gatt);

                initBleDeviceAndNotification(gatt);
            }

            @Override
            public void onDisConnected(boolean isActiveDisConnected, BleDevice bleDevice, BluetoothGatt gatt, int status) {
                logI("SittingFragment.connect111.onDisConnected.isActiveDisConnected=" + isActiveDisConnected);

                //如果通过调用disconnect(BleDevice bleDevice)方法，
                // 主动断开蓝牙连接的结果也会在这个方法中回调，
                // 此时isActiveDisConnected将会是true。

                FastBleOperationUtils.closeGatt(gatt, bleDevice);

                if (isActiveDisConnected) {
                    //主动断开，解绑情况，无需重连
                } else {
                    // 被动断开，断线重连机制
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (!BleManager.getInstance().isConnected(bleDevice)){
                                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_RECONNECT,""));
                            }
                        }
                    },FastBleConstants.RUN_DELAY_TIMES1000);
                }

                // 设备离线
                setDeviceOffline();
            }
        });
    }

    private void setDeviceInitStatus(){
        EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_INIT_STATUS, currentDeviceModel));
    }

    private void setDeviceOffline(){
        EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_OFFLINE, currentDeviceModel));
    }

    private void setDeviceOnline(){
        EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_IS_ONLINE, currentDeviceModel));
    }

    // 需要对bleDevice 进行初始化及监听;
    private void initBleDeviceAndNotification(BluetoothGatt bleGatt){
        long cTime = Calendar.getInstance().getTimeInMillis();
        int spend = (int)(cTime - last_time);
        logI("SittingFragment.06.initBleDeviceAndNotification.需要对bleDevice 进行初始化及监听");
        FastBleOperationUtils.threadSleep(1000);

        try {
            FastBleOperationUtils.openNotify(bleGatt, FastBleConstants.PARAMS_SECOND_CONNECT);
            FastBleOperationUtils.threadSleep(300);
            FastBleOperationPictureUtils.openNotifyForPicture(bleGatt);
//            FastBleOperationUtils.threadSleep(300);
//            FastBleOperationPictureErrorUtils.openNotifyForErrorPicture(bleGatt);

        }catch (Exception e){
            logE("initBleDeviceAndNotification.error:"+e.toString());
            // 需要重连,断开ble会自动重连
            FastBleOperationUtils.closeGatt(bleGatt, FastBleOperationUtils.getBleDevice());
//            retryReConnectBleTimes = 0;
//            onReTryConnectBleDevices("initBleDeviceAndNotification.Exception");
        }
    }

    //鉴权
    private void setAuthentication(){
//        FastBleOperationUtils.SetDeviceParamsStr(FastBleOperationUtils.HiCZUR, FastBleConstants.HEAD_AUTH_WRITE);
        setFirstAuthentication();

        FastBleOperationUtils.threadSleep(300);

        // 此处为//已绑定设备鉴权：3
        FastBleOperationUtils.SetDeviceParamsStr("", FastBleConstants.HEAD_AUTH_ALREADY);
    }

    //鉴权
    private void setFirstAuthentication(){
        String sendStr = FastBleConstants.HiCZUR + userPreferences.getUserId();
        FastBleOperationUtils.SetDeviceParamsStr( sendStr, FastBleConstants.HEAD_AUTH_WRITE);
    }

    private SittingEquipmentAdapter.OnItemAddClickListener onItemAddClickListener = new SittingEquipmentAdapter.OnItemAddClickListener() {
        @Override
        public void onItemAddClick(int position) {

            //添加的时候需要key
            Intent intent = new Intent(activity, SittingConnectActivity.class);
            intent.putExtra("noNeedKey", false);
            ActivityUtils.startActivity(intent);

        }
    };

    // 下拉刷新界面
    private SittingEquipmentAdapter.OnRefreshListener onRefreshListener = new SittingEquipmentAdapter.OnRefreshListener() {
        @Override
        public void onRefreshListener(RefreshLayout refreshLayout) {
            logI("SittingFragment.13.onRefreshListener");

//            refreshLayout.autoRefresh(FastBleConstants.RUN_DELAY_TIMES1000);
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    refreshLayout.setPrimaryColorsId(R.color.transparent,R.color.jing_alert_red_color);
                    refreshLayout.finishRefresh(false);
                }
            }, FastBleConstants.RUN_DELAY_TIMES1000*10);

            // 如果未连接，则开始扫描连接
            if (!BleManager.getInstance().isConnected(FastBleOperationUtils.getBleDevice())) {
//                currentDeviceModel.setShowOfflineMessage(false);
                // 初始化坐姿仪设备
                setInitDevicesConnect();
            }else {
//                currentDeviceModel.setShowOfflineMessage(true);
                // 刷新，处理数据
                onReflashDealWith();
            }
        }
    };

    // Update
    private SittingEquipmentAdapter.OnUpdateClickListener onUpdateClickListener = new SittingEquipmentAdapter.OnUpdateClickListener() {
        @Override
        public void onUpdateClickListener() {
            Intent intent = new Intent(getContext(), SittingUpdateActivity.class);
            intent.putExtra("deviceModel", currentDeviceModel);
            intent.putExtra("deviceVersion", deviceVersion);
            intent.putExtra("seekFile", FastBleOperationUtils.getSeekFile());
            ActivityUtils.startActivity(intent);
        }
    };

    // 解绑
    private SittingEquipmentAdapter.OnUnbindClickListener onUnbindClickListener = new SittingEquipmentAdapter.OnUnbindClickListener() {
        @Override
        public void onUnbindClickListener() {
            String userId = userPreferences.getUserId();
            SittingDeviceModel model = deviceModels.get(currentPosition);
            String deviceId = model.getEquipmentUID();
            String mac = model.getMac();
            confirmDeleteDialog(userId, deviceId, mac);
        }
    };

    // Sensitivity Panel
    private SittingEquipmentAdapter.OnSensitivityClickListener onSensitivityClickListener = new SittingEquipmentAdapter.OnSensitivityClickListener() {
        @Override
        public void onSensitivityClickListener(View v) {
            int position = 0;
            int SEN_COUNT = CZURConstants.SITTING_SENSITIVITY.length-1;
            int iSensitivity = 0;
            String sensitivity = "";

            int id = v.getId();
            String msg = "";
            if (id == R.id.jing_main_sensitivity_level1_iv) {
                msg = getString(R.string.sitting_sensitivity_e);
                position = 0;

            } else if (id == R.id.jing_main_sensitivity_level2_iv) {
                msg = getString(R.string.sitting_sensitivity_h);
                position = 1;

            } else if (id == R.id.jing_main_sensitivity_level3_iv) {
                msg = getString(R.string.sitting_sensitivity_m);
                position = 2;

            } else if (id == R.id.jing_main_sensitivity_level4_iv) {
                msg = getString(R.string.sitting_sensitivity_l);
                position = 3;
            }

            iSensitivity = SEN_COUNT - position;
            sensitivity = CZURConstants.SITTING_SENSITIVITY[position];

            SittingDeviceModel deviceModel = userPreferences.getSittingDeviceModel();
            deviceModel.setLevel(iSensitivity);
            deviceModel.setSensitivity(sensitivity);

            EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_CHANGE_SENSITIVITY, deviceModel));

            String equipmentId = deviceModel.getEquipmentUID();
            FastBleHttpUtils.setSittingDeviceLevel(userPreferences.getUserId(),
                    equipmentId,
                    iSensitivity + "");

            // 选择极高时，有2次弹窗确认
            if (id == R.id.jing_main_sensitivity_level1_iv) {
                if (FastBleOperationUtils.getIsShowAlertForSensitivity()) {
                    showSensitivityAlertDialog(requireContext(), iSensitivity, sensitivity);
                    FastBleOperationUtils.setIsShowAlertForSensitivity(false);
                    return;
                }
            }

        }
    };

    private void showSensitivityAlertDialog456(Context context, int iSensitivity, String sensitivity) {
        SittingSensitivityDialog dialog = new SittingSensitivityDialog(context, R.style.sittingDialog,
                new SittingSensitivityDialog.OncloseListener() {
                    @Override
                    public void onClick(boolean confirm) {
                        if (confirm) {
                            SittingDeviceModel deviceModel = userPreferences.getSittingDeviceModel();
                            deviceModel.setLevel(iSensitivity);
                            deviceModel.setSensitivity(sensitivity);

                            EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_CHANGE_SENSITIVITY, deviceModel));

                            String equipmentId = deviceModel.getEquipmentUID();
                            FastBleHttpUtils.setSittingDeviceLevel(userPreferences.getUserId(),
                                    equipmentId,
                                    iSensitivity + "");
                        }
                    }
                });
        dialog.setOneButton(true);
        dialog.setPositiveButton(context.getString(R.string.sitting_sensitivity_h_dialog_yes));
        dialog.setTitle(context.getString(R.string.sitting_sensitivity_h_dialog_title));
        dialog.setContent(context.getString(R.string.sitting_sensitivity_h_note));
        dialog.create();
        dialog.show();
    }

    private void showSensitivityAlertDialog(Context context, int iSensitivity, String sensitivity) {
        SittingSensitivityDialog dialog = new SittingSensitivityDialog(context, R.style.sittingDialog,
                new SittingSensitivityDialog.OncloseListener() {
                    @Override
                    public void onClick(boolean confirm) {
                        if (confirm) {

                        }
                    }
        });
        dialog.setOneButton(true);
        dialog.setPositiveButton(context.getString(R.string.sitting_sensitivity_h_dialog_yes));
        dialog.setTitle(context.getString(R.string.sitting_sensitivity_h_dialog_title));
        dialog.setContent(context.getString(R.string.sitting_sensitivity_h_note));
        dialog.create();
        dialog.show();
    }

    private void confirmDeleteDialog(String userId, String deviceId, String mac) {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(getContext(), CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getContext().getResources().getString(R.string.prompt));
        builder.setMessage(getContext().getResources().getString(R.string.confirm_unbind));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (!NetworkUtils.isConnected()) {
                    // 解绑后，需要做清理工作--解绑设备、清理数据库等
                    unBindClean(deviceId);
                }else {
                    unbind(userId, deviceId, mac);
                }
                dialog.dismiss();
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();
    }

    private void unbind(String userId, String deviceId, String mac) {
        if (!NetworkUtils.isConnected()) {
            return;
        }
        HttpManager.getInstance().request().unbindSitting(
                userId,
                deviceId,
                mac,
                String.class,
                new MiaoHttpManager.Callback<String>() {
            @Override
            public void onStart() {
                try {
                    showProgressDialog();
                }catch (Exception e){
                    //
                }

                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            hideProgressDialog();
                        }catch (Exception e){
                            //
                        }
                    }
                }, FastBleConstants.RUN_DELAY_TIMES1000 * 15);
            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                // 解绑成功后，需要做清理工作--解绑设备、清理数据库等
                unBindClean(deviceId);

                try {
                    hideProgressDialog();
                }catch (Exception e){
                    //
                }
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                try {
                    hideProgressDialog();
                }catch (Exception e){
                    //
                }
            }

            @Override
            public void onError(Exception e) {
                try {
                    hideProgressDialog();
                }catch (Exception e1){
                    //
                }
            }
        });
    }

    // 解绑成功后，需要做清理工作--解绑设备、清理数据库等
    private void unBindClean(String deviceId){

        if (!NetworkUtils.isConnected()) {
            // 离线缓存bind=1 / unbind=2 信息数据
            String uid = userPreferences.getUserId();
            String equipmentUuid = currentDeviceModel.getEquipmentUID();
            String mac = currentDeviceModel.getMac();
            cacheOfflineBindUnbind(2, uid, equipmentUuid, mac);

            // 同时需要清理一下本地缓存的设备列表；
            SittingDeviceModel delModel = new SittingDeviceModel();
            for (SittingDeviceModel model: deviceModels){
                String equipmentUuid1 = model.getEquipmentUID();
                String mac1 = model.getMac();
                if (equipmentUuid.equals(equipmentUuid1) &&
                        mac.equals(mac1)){
                    // 就是ta了
                    delModel = model;
                    break;
                }
            }
            if (delModel != null) {
                deviceModels.remove(delModel);
                userPreferences.setSittingDevices(deviceModels);
            }

        }

        // 取消异步延迟同步算法参数数据
        handleAlgo.removeCallbacks(runnableAlgo);
        // 取消异步延迟同步app参数数据
        handleApp.removeCallbacks(runnableApp);

        if (currentDeviceModel != null) {
            String currentDeviceID = currentDeviceModel.getEquipmentUID();
            // 解绑的是离线的设备，不是当前的设备，无需发送解绑协议消息
            if (!deviceId.equals(currentDeviceID)){
                getMirrorDevicesList();
                return;
            }
        }

        isUnbindDevice = true;

        // 解绑设备
        FastBleOperationUtils.threadSleep(200);
        FastBleOperationUtils.SetDeviceParamsStr("", FastBleConstants.HEAD_SETTING_UNBUND);
        FastBleOperationUtils.threadSleep(200);

        // 清理数据库
        RealmResults<SPReportSittingEntity> spReportEntities = realm.where(SPReportSittingEntity.class).
                equalTo("equipmentUuid", deviceId).findAll();
        if (spReportEntities != null) {
            if (spReportEntities.size() > 0) {
                realm.beginTransaction();
                spReportEntities.deleteAllFromRealm();
                realm.commitTransaction();
            }
        }

        // 断开ble连接
        try {
            FastBleOperationUtils.closeGatt(FastBleOperationUtils.getBluetoothGatt(), FastBleOperationUtils.getBleDevice());
            FastBleOperationUtils.setBluetoothGatt(null);
            FastBleOperationUtils.setBleDevice(null);
        }catch(Exception e){
            //
        }

        // clear本地的数据及目录,
        // 清了，就没有坐姿图片了；
        // 其实，只需要清理标准坐姿即可，其它可以保留
        boolean flag = currentDeviceModel.isOnline();
        if (flag) {
            String delDir =
                    CZURConstants.MIRROR_PATH +
                    userPreferences.getUserId() + "/" +
                    currentDeviceModel.getEquipmentUID()  + "/" ;
//                    FastBleConstants.SITTING_PICTURE_PATH_STANDAR;
            File file = new File(delDir);
            FastBleToolUtils.deleteDirWithFile(file);

            FastBleOperationUtils.threadSleep(300);
            EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_UNBIND, currentDeviceModel));
        }else{
            // 离线就不清除本地图片了；
            // 断开设备，重新连接
            getMirrorDevicesList();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        if (realm != null) {
            realm.close();
        }

        // 蓝牙开关监听解除
        getContext().unregisterReceiver(mStatusReceive);

        offlineTimer.cancel();
        offlineTimer = null;

    }

    // 此处统一进行本地设备数据信息保存更新
    private void updateLocalDeviceInfo(SittingDeviceModel model){

        // 目前没有返回name
        model.setDeviceName(currentDeviceModel.getDeviceName());
        // 默认为离线
        model.setOnline(false);
        model.setDeviceUpdating(FastBleOperationUtils.isIsDeviceUpdating());

        // 1为智能坐姿 2位自定义坐姿(api) ==> 字符串：0，智能模式；1，自定义模式
        // 仅此处需要转换一下
        int postureMode = model.getPostureMode();
        int sittingMode = postureMode == 1 ? 0:1;
        model.setPostureMode(sittingMode);

        currentDeviceModel.setDeviceModel(model);

        userPreferences.setSittingDeviceModel(currentDeviceModel);

    }

    private void showNoNetWorkUI(){
        rlNoNetWork.setVisibility(View.VISIBLE);
        rlNoNetWork.postDelayed(new Runnable() {
            @Override
            public void run() {
                try {
                    hideProgressDialog();
                }catch (Exception e){
                    //
                }
            }
        }, FastBleConstants.RUN_DELAY_TIMES500);
    }

    // 接收传送回来的各种图片
    private void onDealWithDevicePicture(BaseEvent event){
        int inputStatus = 0;
        SittingPictureEvent picEvent = (SittingPictureEvent) event;
        Bitmap bmp = picEvent.getBmp();
        String bmp_name = picEvent.getBmpName();
        String u_id = userPreferences.getUserId();
        String clientId = userPreferences.getIMEI();
        String equipmentId = currentDeviceModel.getEquipmentUID();
        String filename = "";
        String img_file = "";
        String localPicturePath = "";
        int type =1;   //1为错误坐姿 2位愉悦瞬间
        String image_dev_name = "";

        logI("SittingFragment.onDealWithDevicePicture.localStandardImagePath="+ localCommonPicturePath);

        // 标准坐姿图片回传的处理
        if (event.getEventType().equals(EventType.SITTING_PICTURE_STANDAR)){
            inputStatus = 1;
            currentDeviceModel.setInputStatus(inputStatus);
            userPreferences.setSittingDeviceModel(currentDeviceModel);

            //Test  // 上传oss服务器
            localPicturePath = FastBleOperationUtils.getLocalStandarPicturePath();
            filename = FastBleConstants.SITTING_PICTURE_NAME_STANDAR;
            if (bmp != null) {
                FastBleHexUtils.saveBitmap(bmp, localPicturePath, filename);

            }
            FastBleOperationUtils.threadSleep();

            // 获取oss的token信息，并进行图片上传
            img_file = localPicturePath + FastBleConstants.SITTING_PICTURE_NAME_STANDAR;
            logI("SittingFragment.onDealWithDevicePicture.SITTING_PICTURE_STANDAR.上传oss服务器====="+img_file);

            if (!NetworkUtils.isConnected()) {
                // Jason 2021-06-09 保存图片信息到MirrorOfflinePreferences
                // 离线缓存坐姿图片信息数据
                cacheOfflineSitPicture(u_id, clientId, equipmentId, img_file);
            }else {
                // 网络上传图片
                getSittingOssInfo(u_id, clientId, equipmentId, img_file);
            }

            // 通知 标准坐姿页面，更新图片
            FastBleOperationUtils.threadSleep();
            EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_PICTURE_STANDAR_CHANGE, ""));

            return;
        }
        // 错误坐姿图片回传的处理
        else if (event.getEventType().equals(EventType.SITTING_PICTURE_ERROR)) {
            //Test  // 上传oss服务器
            logI("SittingFragment.onDealWithDevicePicture.SITTING_PICTURE_ERROR.上传oss服务器=====");
            filename = FastBleConstants.SITTING_PICTURE_NAME_ERROR;
            type = 1;
//            image_dev_name = FastBleOperationUtils.getBmpErrorName();
            image_dev_name = bmp_name+FastBleConstants.SITTING_PICTURE_NAME_ERROR;
            localPicturePath = localCommonPicturePath + FastBleConstants.SITTING_PICTURE_PATH_ERROR;
            img_file = localPicturePath + image_dev_name;
        }
        // 开心坐姿图片回传的处理
        else if (event.getEventType().equals(EventType.SITTING_PICTURE_HAPPY)) {
            //Test  // 上传oss服务器
            logI("SittingFragment.onDealWithDevicePicture.SITTING_PICTURE_HAPPY.上传oss服务器=====");
            filename = FastBleConstants.SITTING_PICTURE_NAME_HAPPY;
            type = 2;
//            image_dev_name = FastBleOperationUtils.getBmpHappyName();
            image_dev_name = bmp_name+FastBleConstants.SITTING_PICTURE_NAME_HAPPY;
            localPicturePath = localCommonPicturePath + FastBleConstants.SITTING_PICTURE_PATH_HAPPY;
            img_file = localPicturePath + image_dev_name;
        }else{
            return;
        }

        logI("SittingFragment.onDealWithDevicePicture.localPicturePath="+localPicturePath,
                "img_file="+img_file);

        FastBleHexUtils.saveBitmap(bmp, localPicturePath, image_dev_name);

        FastBleOperationUtils.threadSleep();

        // 同时，需要把错误坐姿和开心图片保存到服务器；
        /*
        String userId, String udid, String imgDatas
        * "imgDatas": [
        {
          "dateString": "2021-01-12",//对应图片日期字符串
          "img": "mirror/error/789.jpg",//图片路径
          "type": 1,//1为错误坐姿 2位愉悦瞬间
          "uuid": "20e76216-dc39-4d9c-afe4-22fe071a2321" //唯一标志
        }
        */
        Map<String, String> dict = new HashMap<>();
        String date_filename = FastBleToolUtils.getDateTimeFormate(image_dev_name);
        String localeTime = FastBleToolUtils.getDateTimeFullFormate(image_dev_name);
        dict.put("dateString", date_filename);
        dict.put("localeTime", localeTime);
        dict.put("type", type+"");
        dict.put("uuid", image_dev_name);

        // 无论是否离线，都需要先 缓存开心、错误图片信息数据
        cacheOfflineHappyPicture(u_id, clientId, img_file, equipmentId, dict, type);

        if (NetworkUtils.isConnected()) {
            // 获取oss的token信息，并进行图片上传
//            getSittingHappyOssInfo(u_id, clientId, img_file, dict);

            FastBleHttpUtils.uploadSittingHappyOssInfo(u_id, clientId, equipmentId, img_file, dict);
        }
    }

    // 坐姿报告数据接收
    private void onDealWithDeviceReport123(String report){
        logI("SittingFragment.onDealWithDeviceReport.report");

        if (report.startsWith("error")){
            return;
        }

        SyncSittingReportEntity entity = FastBleOperationUtils.setStringToReportBean(report);

        entity.setHappy(entity.getHappyScore());    //happy-->happyScore
        entity.setSedentaryTimeCount(entity.getMotionlessRemindCnt());    //sedentaryTimeCount-->motionlessRemindCnt
        entity.setRemindCount(entity.getRemindCnt());    //remindCount-->remindCnt

        SyncSittingReportServerEntity serverEntry= new SyncSittingReportServerEntity();
        serverEntry.setSyncSittingReportEntityToServer(entity);

        if (serverEntry.getBeginTime().length()<10)
            serverEntry.setBeginTime(FastBleToolUtils.getCurrentSysTime());
        if (serverEntry.getEndTime().length()<10)
            serverEntry.setEndTime(FastBleToolUtils.getCurrentSysTime());
        if (serverEntry.getPushTime().length()<10)
            serverEntry.setPushTime(FastBleToolUtils.getCurrentSysTime());

        List<SyncSittingReportServerEntity> listEntry = new ArrayList<>();
        listEntry.add(serverEntry);

        // 发送到服务器
        String udid = userPreferences.getIMEI();
        String t_id = userPreferences.getToken();
        String u_id = userPreferences.getUserId();
        String syncJson = new Gson().toJson(listEntry);

        logI("SittingFragment.onDealWithDeviceReport.NetworkUtils.isConnected()="+NetworkUtils.isConnected());
        if (!NetworkUtils.isConnected()) {
            // Jason 2021-06-09
            // 保存到本地json
            long cTime = Calendar.getInstance().getTimeInMillis();
            String fDir =
                    CZURConstants.MIRROR_PATH +
                    userPreferences.getUserId() + "/" +
                    currentDeviceModel.getEquipmentUID() +
                    FastBleConstants.SITTING_POSE_REPORT_PATH;
            String filename = cTime + "";
            FastBleToolUtils.saveLocalReportJson(fDir, filename, syncJson);
        }else {
            FastBleHttpUtils.sendTimeUseReport(syncJson, udid, t_id, u_id);
        }
    }

    private void onDealWithDeviceReport(String report){
        onDealWithDeviceReport(report, false);
    }
    private void onDealWithDeviceReport(String report, Boolean isNewRepost){

        if (report.startsWith("error")){
            return;
        }

        SyncSittingReportEntity entity = FastBleOperationUtils.setStringToReportBean(report);

        entity.setHappy(entity.getHappyScore());    //happy-->happyScore
        entity.setSedentaryTimeCount(entity.getMotionlessRemindCnt());    //sedentaryTimeCount-->motionlessRemindCnt
        entity.setRemindCount(entity.getRemindCnt());    //remindCount-->remindCnt

        SyncSittingReportServerEntity serverEntry= new SyncSittingReportServerEntity();
        serverEntry.setSyncSittingReportEntityToServer(entity);

        if (serverEntry.getBeginTime().length()<10)
            serverEntry.setBeginTime(FastBleToolUtils.getCurrentSysTime());
        if (serverEntry.getEndTime().length()<10)
            serverEntry.setEndTime(FastBleToolUtils.getCurrentSysTime());
        if (serverEntry.getPushTime().length()<10)
            serverEntry.setPushTime(FastBleToolUtils.getCurrentSysTime());

        String syncJson = new Gson().toJson(serverEntry);

        // 保存到本地json
        long cTime = Calendar.getInstance().getTimeInMillis();
        String fDir =
                CZURConstants.MIRROR_PATH +
                        userPreferences.getUserId() + "/" +
                        currentDeviceModel.getEquipmentUID() +
                        FastBleConstants.SITTING_POSE_REPORT_PATH;
        String filename = cTime + "";
        FastBleToolUtils.saveLocalReportJson(fDir, filename, syncJson);
        logI("SittingFragment.onDealWithDeviceReport.isNewRepost="+isNewRepost+";filename="+filename);

        // 最新的报告，立即发送
        if (isNewRepost){
            FastBleOperationUtils.threadSleep(300);
            List<String> jsonList = new ArrayList<>();
            jsonList.add(filename);
            FastBleOperationUtils.setOfflineReportFileList(jsonList);
            logI("SittingFragment.onDealWithDeviceReport.isNewRepost=true;filename="+filename);
            List<SyncSittingReportServerEntity> jsonReportList = new ArrayList<>();
            jsonReportList.add(serverEntry);
            String syncJson1 = new Gson().toJson(jsonReportList);
            onSendOfflineReportNew(syncJson1);
        }
    }

    // 算法学习更新json数据
    private void onDealWithDeviceAlgoData123(String algoJsonData) {
        String udid = userPreferences.getIMEI();
        String t_id = userPreferences.getToken();
        String u_id = userPreferences.getUserId();
        String equipmentId = currentDeviceModel.getEquipmentUID();

        // 上传发送给服务器
        if (!NetworkUtils.isConnected()) {
            // Jason 2021-06-09
            // 保存到本地json
            long cTime = Calendar.getInstance().getTimeInMillis();
            String fDir =
                    CZURConstants.MIRROR_PATH +
                            userPreferences.getUserId() + "/" +
                            currentDeviceModel.getEquipmentUID() +
                            FastBleConstants.SITTING_ALGO_DATA_PATH;
            String filename = cTime + "";
            FastBleToolUtils.saveLocalReportJson(fDir, filename, algoJsonData);
        }else {
            FastBleHttpUtils.uploadAlgoJson(algoJsonData, equipmentId, udid, t_id, u_id);
        }
    }
    private void onDealWithDeviceAlgoData(String algoJsonData) {
        String udid = userPreferences.getIMEI();
        String t_id = userPreferences.getToken();
        String u_id = userPreferences.getUserId();
        String equipmentId = currentDeviceModel.getEquipmentUID();

        // 上传发送给服务器
        // 保存到本地json
        long cTime = Calendar.getInstance().getTimeInMillis();
        String fDir =
                CZURConstants.MIRROR_PATH +
                        userPreferences.getUserId() + "/" +
                        currentDeviceModel.getEquipmentUID() +
                        FastBleConstants.SITTING_ALGO_DATA_PATH;
        String filename = cTime + "";
        FastBleToolUtils.saveLocalReportJson(fDir, filename, algoJsonData);

//        // 设置延迟1分钟开始发送离线报告
//        new Handler().postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                syncAlgoJsonDataToServer();
//            }
//        }, FastBleConstants.RUN_DELAY_TIMES1000*30);
    }


    // 设备主动发出-坐姿录入结果
    private void onDealWithDeviceInputStatus(String params){
        String u_id = userPreferences.getUserId();
        String equipmentId = currentDeviceModel.getEquipmentUID();
        String inputStatus = "0";//坐姿录入状态 0：未录入；1：已经录入
        inputStatus = currentDeviceModel.getInputStatus()+"";
        //坐姿录入状态 0：未录入；1：已经录入；2：录入失败；3：重新录入；4：正在录入
        logI("onDealWithDeviceInputStatus.inputStatus="+inputStatus,"params="+params);

        //device feedback:"字符串：0，初次成功；1，重新录入成功;2，初次录入失败;3,重新录入失败"
        if (params.equals("0") || params.equals("1") || params.equals("3")){
            inputStatus = "1";
        }

        FastBleHttpUtils.setSittingDeviceInputStatus(u_id, equipmentId, inputStatus);
    }

    public void getSittingOssInfo(String u_id, String clientId, String equipmentId, String pic_name) {
        if (!NetworkUtils.isConnected()) {
            return;
        }

        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<SittingOssTokenModel>() {
            @Override
            public SittingOssTokenModel doInBackground() {
                return FastBleHttpUtils.getSittingOssInfo(u_id, clientId);
            }

            @Override
            public void onSuccess(SittingOssTokenModel ossModel) {

                if (ossModel == null){
                    return;
                }
                //  test/3515/544c7f8b-4b73-43df/*
                String prefix = ossModel.getPrefix();
                String ossPath = prefix.substring(0,prefix.length()-2) + FastBleConstants.SITTING_PICTURE_NAME_STANDAR;

                // 图片上传oss
                uploadSittingImage(pic_name, ossPath, u_id, equipmentId);
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                logE("SittingFragment.onFail.t="+t.toString());
            }
        });
    }

    // 图片上传oss
    public void uploadSittingImage(String sourcePath, String ossPath, String u_id, String equipmentId) {
        if (!NetworkUtils.isConnected()) {
            return;
        }

        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Boolean>() {
            @Override
            public Boolean doInBackground() {
                return FastBleHttpUtils.uploadSittingStandarImage(sourcePath, ossPath);
            }

            @Override
            public void onSuccess(Boolean flag) {
                logI("uploadSittingImage.onSuccess.flag="+flag);
                if (flag){
                    // 保存坐姿照片路径到服务器
                    FastBleHttpUtils.setSittingDeviceStandPositionImage(
                            u_id,
                            equipmentId,
                            ossPath);
                    FastBleOperationUtils.threadSleep();
                    FastBleHttpUtils.setSittingDeviceInputStatus(
                            u_id,
                            equipmentId,
                            "1");

                    // 同时保存路径到本地，并需要更新
                    currentDeviceModel.setStandPositionImage(ossPath);
                    int iSittingModel = 1;
                    String sittingModelName = CZURConstants.SITTING_MODEL[1];
                    currentDeviceModel.setPostureMode(iSittingModel);
                    currentDeviceModel.setPostureModeName(sittingModelName);
                    userPreferences.setSittingDeviceModel(currentDeviceModel);
                    EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_PICTURE_STANDAR_CHANGE, ""));
                }
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                logE("uploadSittingImage.onFail.t=" + t.toString());
            }
        });
    }

    // 获取报告，仅取一条，并需要判断日期，更新主界面
    public void getReportDataAndUpdateUI(String equipment_id) {
        if (!NetworkUtils.isConnected()) {
            return;
        }
        long cTime = Calendar.getInstance().getTimeInMillis();
        int spend = (int)(cTime - last_time);
        logI("SittingFragment.15.getReportDataAndUpdateUI");
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Boolean>() {
            @Override
            public Boolean doInBackground() {
                List<SittingReportModelSub> reportList = getBeforeReportList(equipment_id, "");
                if (reportList != null && reportList.size()>0){
                    SittingReportModelSub modelSub = reportList.get(0);

                    int rightProportion = modelSub.getRightProportion();
                    int happy = modelSub.getHappy();
                    int dayUsing = modelSub.getDayUsingDuration();
//                    int totalRemindCount = modelSub.getTotalRemindCount();
                    int longsit = modelSub.getSedentaryTimeCount();
                    int happyImgcount = modelSub.getHappyImgCount();
                    int errorImgcount = modelSub.getErrorImgCount();

                    // 显示当天用户坐姿准确率。
                    String fromEnd1 = modelSub.getFromEnd();     //"fromEnd": "2021-03-24",
                    String fromEnd = ReportUtil.foramtDateTime(fromEnd1, "yyyy.MM.dd");
                    String nowDay = ReportUtil.getNowDay("yyyy.MM.dd");
//                    boolean isNew = true;
                    boolean isNew = ReportUtil.isDateOneBigger(nowDay, fromEnd);
                    if (isNew){
                        rightProportion = 0;
                        happy = 0;
                        longsit = 0;
                        dayUsing = 0;
                        happyImgcount = 0;
                        errorImgcount = 0;
                        currentDeviceModel.setHaveDataToday(false);
                    }else{
                        currentDeviceModel.setHaveDataToday(true);
                    }

                    currentDeviceModel.setPerRight(rightProportion);
                    currentDeviceModel.setPerHappy(happy);
                    currentDeviceModel.setPerLong(longsit);
                    currentDeviceModel.setDayUsing(dayUsing);
                    currentDeviceModel.setHappyImgCount(happyImgcount);
                    currentDeviceModel.setErrorImgCount(errorImgcount);

                    return true;
                }
                return false;
            }

            @Override
            public void onSuccess(Boolean result) {
                // 刷新一下UI
                BleDevice bleDevice = FastBleOperationUtils.getBleDevice();
                // 如果ble已经连接，则直接在线
                if (Validator.isNotEmpty(bleDevice) && BleManager.getInstance().isConnected(bleDevice)) {
                    setDeviceOnline();
                }else{
                    setDeviceOffline();
                }
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
            }
        });
    }

    private List<SittingReportModelSub> getBeforeReportList(String equipmentId, String reportId) {
        if (!NetworkUtils.isConnected()) {
            return null;
        }

        try {
            final MiaoHttpEntity<SittingReportModelSub> reportEntity = httpManager.request().
                    getBeforeSittingReports(
                            userPreferences.getUserId(),
                            equipmentId,
                            "1",
                            reportId,
                            "1",
                            new TypeToken<List<SittingReportModelSub>>() {
                            }.getType());

            if (reportEntity == null) {
                return null;
            }
            if (reportEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return reportEntity.getBodyList();
            } else {
                return null;
            }

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    // 获取当前的postion
    private int getCurrentPostion(){
        int devicePosition = 0;
        String deviceUdid = currentDeviceModel.getEquipmentUID();
        if (Validator.isNotEmpty(deviceModels)) {
            devicePosition = checkRefreshPosition(deviceUdid, deviceModels);
            if (devicePosition < 0) {
                devicePosition = 0;
            }
        }

        return devicePosition;
    }

    // 下拉刷新的处理，从设备重新获取当前报告
    private void onReflashDealWith(){
        long cTime = Calendar.getInstance().getTimeInMillis();
        int spend = (int)(cTime - last_time);
        logI("SittingFragment.14.onReflashDealWith");

        if (!BleManager.getInstance().isConnected(FastBleOperationUtils.getBleDevice())) {
            if (refreshLayoutSmart != null) {
                refreshLayoutSmart.setPrimaryColorsId(R.color.transparent,R.color.jing_alert_red_color);
                refreshLayoutSmart.finishRefresh(false);
            }
            return;
        }

        // 获取当前的报告
        FastBleOperationUtils.SetDeviceParamsStr("", FastBleConstants.HEAD_APPGET_REPORT0);

        String eqId = currentDeviceModel.getEquipmentUID();
        if (Validator.isNotEmpty(eqId)) {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    getReportDataAndUpdateUI(eqId);
                    if (refreshLayoutSmart != null) {
                        refreshLayoutSmart.setPrimaryColorsId(R.color.transparent,R.color.gray_99);
                        refreshLayoutSmart.finishRefresh(true);
                    }
                }
            }, FastBleConstants.RUN_DELAY_TIMES1000);
        }else{
            if (refreshLayoutSmart != null) {
                refreshLayoutSmart.setPrimaryColorsId(R.color.transparent,R.color.jing_alert_red_color);
                refreshLayoutSmart.finishRefresh(false);
            }
        }
    }

    // 断线重连机制
    private void onReTryConnectBleDevices(String typeName) {
        logI("onReTryConnectBleDevices.typeName="+typeName,
                "retryReConnectBleTimes="+retryReConnectBleTimes,
                "retryReconnectBleSecond="+retryReconnectBleSecond);

        if (BleManager.getInstance().isConnected(FastBleOperationUtils.getBleDevice())){
            return;
        }
        // 如果connect页面打开的，那就不要重连了
        if (FastBleOperationUtils.isIsChangeConnectActivity()){
            return;
        }

        if (isUnbindDevice){
            return;
        }

        // 1,超过100次就不要重新连接了；
        if (retryReConnectBleTimes > FastBleConstants.BLE_RECONNECT_TIMES){
            return;
        }

        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (FastBleOperationUtils.isIsChangeConnectActivity()){
                    return;
                }

                // 重连10次，从服务器获取一下设备列表，防止设备抢占；
                if (retryReConnectBleTimes % 10 == 0){
                    getMirrorDevicesList();
                }else {
                    setInitDevicesConnect();
                }

                retryReConnectBleTimes++;
            }
        }, FastBleConstants.RUN_DELAY_TIMES1000 * 2);

    }

    // MTU失败，重连机制
    private void onReSetMTU(){
        logI("SittingFragment.onReMTU.retryMTUTimes="+retryMTUTimes,
                "retryMTUSecond="+retryMTUSecond);
        if (retryMTUTimes > FastBleConstants.MAX_MTU_RETRY_TIMES) {
            return;
        }

        if (retryMTUTimes >= FastBleConstants.MAX_MTU_RETRY_TIMES1){
            FastBleOperationUtils.closeGatt(FastBleOperationUtils.getBluetoothGatt(),
                    FastBleOperationUtils.getBleDevice());
            return;
        }
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                FastBleOperationUtils.setMtu(FastBleConstants.PARAMS_SECOND_CONNECT);
                FastBleOperationUtils.threadSleep();
            }
        }, retryMTUSecond);

        retryMTUTimes++;
        retryMTUSecond += FastBleConstants.RUN_DELAY_TIMES500;
    }


    // 打开通知失败，重连机制
    private void onReOpenNotify(BluetoothGatt bleGatt){
        logI("SittingFragment.onReOpenNotify.retryTimes="+retryTimes,
                "retrySecond="+retrySecond);

        //BleManager.getInstance().stopNotify(uuid_service, uuid_characteristic_notify);

        // 1,超过5次就不要重新连接了；
        // 2，第一次1s重连；
        // 3，以后都是*2的延时；
        if (retryTimes > FastBleConstants.MAX_OPEN_NOTIFY_RETRY_TIMES) {
            return;
        }
        if (bleGatt == null){
            return;
        }
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                // notify 监听特性
                BluetoothGattService bleService = bleGatt.getService(UUID.fromString(FastBleConstants.exportUUID.toLowerCase()));
                BluetoothGattCharacteristic bleCharacteristicRead = bleService.
                        getCharacteristic(UUID.fromString(FastBleConstants.readUUID.toLowerCase()));
                if (bleCharacteristicRead != null) {
                    FastBleOperationUtils.openNotify(bleGatt, FastBleConstants.PARAMS_SECOND_CONNECT);
                }
            }
        }, retrySecond);

        retryTimes++;
        retrySecond += FastBleConstants.RUN_DELAY_TIMES1000;
    }

    private void onReOpenNotifyPic(BluetoothGatt bleGatt){
        if (retryPicTimes > FastBleConstants.MAX_OPEN_NOTIFY_RETRY_TIMES) {
            return;
        }

        if (bleGatt == null){
            return;
        }
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                BluetoothGattService bleService = bleGatt.getService(UUID.fromString(FastBleConstants.exportUUID.toLowerCase()));
                BluetoothGattCharacteristic bleCharacteristicReadPic = bleService.
                        getCharacteristic(UUID.fromString(FastBleConstants.readPicUUID.toLowerCase()));
                if (bleCharacteristicReadPic != null ) {
                    FastBleOperationPictureUtils.openNotifyForPicture(bleGatt);
                }
            }
        }, retryPicSecond);
        retryPicTimes++;
        retryPicSecond += FastBleConstants.RUN_DELAY_TIMES1000;
    }

    private void onReOpenNotifyErrorPic(BluetoothGatt bleGatt){
        if (retryErrorPicTimes > FastBleConstants.MAX_OPEN_NOTIFY_RETRY_TIMES) {
            return;
        }
        if (bleGatt == null){
            return;
        }

        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                BluetoothGattService bleService = bleGatt.getService(UUID.fromString(FastBleConstants.exportUUID.toLowerCase()));
                BluetoothGattCharacteristic bleCharacteristicReadErrPic = bleService.
                        getCharacteristic(UUID.fromString(FastBleConstants.readErrPicUUID.toLowerCase()));
                if (bleCharacteristicReadErrPic != null ) {
                    FastBleOperationPictureErrorUtils.openNotifyForErrorPicture(bleGatt);
                }
            }
        }, retryErrorPicSecond);
        retryErrorPicTimes++;
        retryErrorPicSecond += FastBleConstants.RUN_DELAY_TIMES1000;
    }

    // opentify打开成功， 初始化参数设置
    // 1s后强制取消loading
    // 2s后获取版本号；
    // 3s后获取坐姿模式;
    private void onSetupDeviceParams(){
        long cTime = Calendar.getInstance().getTimeInMillis();
        int spend = (int)(cTime - last_time);
        logI("SittingFragment.09.onSetupDeviceParams");

        // 此时进行初始化的操作
        // 设置语言时区
        String sendStr = getTimeZoneNew();
        FastBleOperationUtils.threadSleep();
        logI("SittingFragment.09-1.HEAD_SETTING_ZONE");
        FastBleOperationUtils.SetDeviceParamsStr(sendStr, FastBleConstants.HEAD_SETTING_ZONE);

        // 请求并设定参数
        logI("SittingFragment.09-2.dealWithParamConfig");
        dealWithParamConfig();

        onSetupDeviceParamsEvery();

    }

    // 每次都需要初始化的几个地方
    // 1s后强制取消loading
    // 2s后获取版本号；
    // 3s后获取坐姿模式;
    // 10s 获取坐姿图片;
    private void onSetupDeviceParamsEvery(){
        long cTime = Calendar.getInstance().getTimeInMillis();
        int spend = (int)(cTime - last_time);
        logI("SittingFragment.10.onSetupDeviceParamsEvery");
        // 先获取设备版本号
        new Handler(Looper.myLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                logI("SittingFragment.10-2.HEAD_UPDATE_QUERY");
                FastBleOperationUtils.SetDeviceParamsStr("", FastBleConstants.HEAD_UPDATE_QUERY);
            }
        }, FastBleConstants.RUN_DELAY_TIMES1000);       // *2

        // 获取一下设备的坐姿模式
        new Handler(Looper.myLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                logI("SittingFragment.10-3.HEAD_APPGET_STATUS");
                if (!FastBleOperationUtils.isBleDeviceConnected()){
                    return;
                }
                FastBleOperationUtils.SetDeviceParamsStr("", FastBleConstants.HEAD_APPGET_STATUS);
            }
        }, FastBleConstants.RUN_DELAY_TIMES1000*2);   // *4

        // 先设置一下错误坐姿、愉悦心情关闭
        new Handler(Looper.myLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                logI("SittingFragment.10-8.HEAD_SETTING_SWITCH_ERROR");
                FastBleOperationUtils.SetDeviceParams(0, FastBleConstants.HEAD_SETTING_SWITCH_ERROR);
                FastBleOperationUtils.threadSleep();
                FastBleOperationUtils.SetDeviceParams(0, FastBleConstants.HEAD_SETTING_SWITCH_HAPPY);
            }
        }, FastBleConstants.RUN_DELAY_TIMES1000*3);       // *2

        // 历史报告发送状态
        new Handler(Looper.myLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                logI("SittingFragment.10-6.HEAD_APPGET_REPORT_HISTORY");
                FastBleOperationUtils.SetDeviceParamsStr("", FastBleConstants.HEAD_APPGET_REPORT_HISTORY);
            }
        }, FastBleConstants.RUN_DELAY_TIMES1000*4);     // *8

        // 获取一下设备的静音模式
        new Handler(Looper.myLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                logI("SittingFragment.10-7.HEAD_APPGET_SILENT");
                FastBleOperationUtils.SetDeviceParamsStr("", FastBleConstants.HEAD_APPGET_SILENT);
            }
        }, FastBleConstants.RUN_DELAY_TIMES1000*5);   // *4

        // 获取坐姿图片-从设备
        new Handler(Looper.myLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                logI("SittingFragment.10-4.HEAD_APPGET_STANDER");
                FastBleOperationUtils.SetDeviceParamsStr("", FastBleConstants.HEAD_APPGET_STANDER);
            }
        }, FastBleConstants.RUN_DELAY_TIMES1000*6);    // *6

        // 获取坐姿图片-从本地或服务器
        new Handler(Looper.myLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                logI("SittingFragment.10-5.checkLocalPicture");
                checkLocalPicture();
            }
        }, FastBleConstants.RUN_DELAY_TIMES1000*7);     // *8


        // 刷新，处理数据
        onReflashDealWith();

        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                syncReportJsonDataToServer();
                syncAlgoJsonDataToServer();
            }
        }, FastBleConstants.RUN_DELAY_TIMES1000*30);

    }

    private void dealWithParamConfig(){
        // 每次App连接成功需要进行的操作==请求并设定参数（算法、app）；
        // 请求并设定参数  //获取类型 1.app_config 2.sp_param_config
        // 0,算法参数：0
        logI("SittingFragment.10.HEAD_SETTING_PARAM0.HEAD_SETTING_PARAM1");
        String type = FastBleConstants.SITTING_SET_PARAMS_ALGO+"";
        String u_id = userPreferences.getUserId();
        String uuid = currentDeviceModel.getEquipmentUID();
        String udid = userPreferences.getIMEI();
        String t_id = userPreferences.getToken();
        String param = FastBleConstants.HEAD_SETTING_PARAM0;
        // 请求并设定参数
        String finalParam = param;
        String finalType = type;
        runnableAlgo = new Runnable() {
            @Override
            public void run() {
                logI("SittingFragment.10-1.getAlgoConfigAndSendDevice");
                if (!FastBleOperationUtils.isBleDeviceConnected()){
                    return;
                }
                if (!NetworkUtils.isConnected()) {
                    return;
                }
                FastBleHttpUtils.getConfigJsonAndSendDevice(finalType, udid, t_id, u_id, uuid, uuid, finalParam);
            }
        };
        handleAlgo.postDelayed(runnableAlgo, FastBleConstants.RUN_DELAY_TIMES_FOR_AGLO);

        // 1,APP参数：1
        type = FastBleConstants.SITTING_SET_PARAMS_APP+"";
        param = FastBleConstants.HEAD_SETTING_PARAM1;
        String finalType1 = type;
        String finalParam1 = param;
        runnableApp = new Runnable() {
            @Override
            public void run() {
                logI("SittingFragment.10-2.getAppConfigAndSendDevice");
                if (!FastBleOperationUtils.isBleDeviceConnected()){
                    return;
                }
                if (!NetworkUtils.isConnected()) {
                    return;
                }
                FastBleHttpUtils.getConfigJsonAndSendDevice(finalType1, udid, t_id, u_id, uuid, uuid, finalParam1);
            }
        };
        handleApp.postDelayed(runnableApp, FastBleConstants.RUN_DELAY_TIMES_FOR_APP);

    }

    // 检查是否需要升级
    // 获取api的升级接口
    private void getSittingServerVersionInfo(String version, String sn) {
        if (!NetworkUtils.isConnected()) {
            return;
        }

        logI("SittingFragment.getSittingServerVersionInfo.version="+version + ";sn="+sn);
        String udid = userPreferences.getIMEI();
        String t_id = userPreferences.getToken();
        String u_id = userPreferences.getUserId();

        OkHttpClient okHttpClient = new OkHttpClient();
        //Form表单格式的参数传递
        FormBody formBody = new FormBody
                .Builder()
                .add("version", version) // 固件的版本信息
                .add("sn", sn) // 设备sn
                .build();

        Request request = new Request
                .Builder()
                .header("udid", udid)
                .header("App-Key", CZURConstants.CLOUD_ANDROID)
                .header("T-ID", t_id)
                .header("U-ID", u_id)
//                .header("Content-Type", "application/json")
                .post(formBody)//Post请求的参数传递
                .url(BuildConfig.CHECK_MIRROR_UPDATE_URL)
                .build();

        okHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                logE(e.toString());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                //此方法运行在子线程中，不能在此方法中进行UI操作。
                // 获取前次的升级续传的版本号，进行当前的比对;
//                SittingUpdateModel oldUpdateModel = userPreferences.getSittingUpdateModel();
//                if (oldUpdateModel != null){
//                    oldVersion = oldUpdateModel.getVersion();
//                }
                // 请求成功
                if (response.isSuccessful()) {
                    String res = response.body().string();
                    if (res == null || res.equals("")){
                        logI("getSittingServerVersionInfo.response.body() == null");
                        return;
                    }
                    BaseUpdateModel baseModel = new Gson().fromJson(res, BaseUpdateModel.class);
                    int code = baseModel.getCode();
                    if (code == MiaoHttpManager.STATUS_SUCCESS) {
                        int status = FastBleOperationUtils.getSittingUpdateStatus();

                        logI("getSittingServerVersionInfo.固件版本检测 成功!status="+status);
                        SittingUpdateModel updateModel = baseModel.getBody();
                        if (status < FastBleConstants.STATE_UPDATING ||
                                status > FastBleConstants.STATE_UPDATE_FAIL ){
                            FastBleOperationUtils.setSittingUpdateStatus(FastBleConstants.STATE_NEW);
                        }
                        userPreferences.setSittingUpdateModel(updateModel);
                        currentDeviceModel.setReadyForCheckUpdate(false);

                        if (updateModel.getUpdate() == 1){
                            // 需要升级
                            // 1,标记小红点；
                            // 2,保存接口返回的升级信息
                            currentDeviceModel.setReadyForCheckUpdate(true);
                            if (status < FastBleConstants.STATE_UPDATING ||
                                    status > FastBleConstants.STATE_UPDATE_FAIL ){
                                FastBleOperationUtils.setSittingUpdateStatus(FastBleConstants.STATE_PREPARE);
                            }
                        }
                        userPreferences.setSittingDeviceModel(currentDeviceModel);
                        EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_IS_UPDATE, currentDeviceModel));
                    } else {
                        logI("getSittingServerVersionInfo.固件版本检测 失败!");
                    }
                }
            }
        });
    }

    private void checkLocalPicture(){
        // 获取本地的坐姿图片
        if (currentDeviceModel.getInputStatus() == FastBleConstants.SITTING_DEF_STANDARY_SUCESS_INT) {
            String img_name = FastBleOperationUtils.getLocalStandarPicturePath() + FastBleConstants.SITTING_PICTURE_NAME_STANDAR;
            boolean flag = FastBleHexUtils.getLocalStandarPicture(img_name);
            logI("SittingFragment.checkLocalPicture.img_name="+img_name + ";flag="+flag);
            if (!flag){
                // 本地没有，则从服务器获取；
                String localStandardImagePath=FastBleOperationUtils.getLocalStandarPicturePath();
                String imageKey = currentDeviceModel.getStandPositionImage();
                new Thread(new Runnable(){
                    @Override
                    public void run() {
//                        boolean flag1 = downloadOSSPicture(localStandardImagePath, imageKey);
                        boolean flag1 = FastBleHttpUtils.downloadOSSPicture(localStandardImagePath,
                                FastBleConstants.SITTING_PICTURE_NAME_STANDAR,
                                imageKey);
                        logI("SittingFragment.checkLocalPicture.flag1="+flag1);
                    }
                }).start();
            }
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == FastBleConstants.REQUEST_CODE_OPEN_BLE
                && resultCode == -1) {
            // 重新连接ble
            String uuid = currentDeviceModel.getDeviceUUID();
            logI("SittingFragment.onActivityResult--重新连接ble.uuid="+uuid);
            onReStartScan(uuid);
        }
    }

    private BroadcastReceiver mStatusReceive = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            int blueState = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, 0);
            switch(blueState){
                case BluetoothAdapter.STATE_TURNING_ON:// 开启中
                    isBluetoothOpen = true;
                    break;
                case BluetoothAdapter.STATE_ON:
                    //开启
                    isBluetoothOpen = true;
                    logI("===蓝牙开关监听.开启===");
                    //重新连接
                    retryReConnectBleTimes = 0;
                    onReTryConnectBleDevices("Bluetooth is STATE_ON");
                    break;
                case BluetoothAdapter.STATE_TURNING_OFF:// 关闭中
                    isBluetoothOpen = false;
                    break;
                case BluetoothAdapter.STATE_OFF: // 关闭
                    logI("===蓝牙开关监听.关闭===");
                    isBluetoothOpen = false;
                    break;
            }
        }
    };

    // 初始化离线数据
    private void initOfflineData() {
        offlinePreferences = MirrorOfflinePreferences.getInstance(activity);

        settingsModels = offlinePreferences.getSittingDevicesSettings();
        if (settingsModels == null){
            settingsModels = new SittingDeviceModel();
            settingsModels.initDeviceModel();
        }

        bindModels = offlinePreferences.getSittingDevicesBind();
        if (bindModels == null){
            bindModels = new ArrayList<>();
        }

        unbindModels = offlinePreferences.getSittingDevicesUnbind();
        if (unbindModels == null){
            unbindModels = new ArrayList<>();
        }

        errorPicModels = offlinePreferences.getSittingErrorPictures();
        if (errorPicModels == null){
            errorPicModels = new ArrayList<>();
        }

        happyPicModels = offlinePreferences.getSittingHappyPictures();
        if (happyPicModels == null){
            happyPicModels = new ArrayList<>();
        }

        sitPicModels = offlinePreferences.getSittingSitPictures();
        if (sitPicModels == null){
            sitPicModels = new ArrayList<>();
//            SittingDeviceOfflineModel model = new SittingDeviceOfflineModel();
//            model.initDeviceOfflineModel();
//            sitPicModels.add(model);
        }

        // 清理日志文件

    }

    // 离线缓存坐姿图片信息数据
    private void cacheOfflineSitPicture(String u_id, String clientId, String equipmentUuid, String img_file){
        // 每个uid只存储一张坐姿图片

        // Jason 2021-06-09 保存图片信息到MirrorOfflinePreferences
        //String u_id, String clientId, String pic_name
        SittingDeviceOfflineModel offlinePictureModel = new SittingDeviceOfflineModel();
        offlinePictureModel.setU_id(u_id);
        offlinePictureModel.setClientId(clientId);
        offlinePictureModel.setEquipmentUuid(equipmentUuid);
        offlinePictureModel.setPic_name(img_file);

        for (SittingDeviceOfflineModel model : sitPicModels){
            String uid = model.getU_id();
            String uuid = model.getEquipmentUuid();
            if (u_id.equals(uid) && equipmentUuid.equals(uuid)){
                sitPicModels.remove(model);
            }
        }
        sitPicModels.add(offlinePictureModel);
        offlinePreferences.setSittingSitPictures(sitPicModels);

    }

    // 离线缓存开心、错误图片信息数据
    private void cacheOfflineHappyPicture(String u_id, String clientId, String img_file, String equipmentUuid, Map<String, String> dict, int type){
        // 开心、错误图片有就存储

        // Jason 2021-06-10 保存图片信息到MirrorOfflinePreferences
        //sitHappy/sitErrorPic:String u_id, String clientId, String pic_name, Map dict
        //dict.put("dateString", date_filename);
        //dict.put("localeTime", localeTime);
        //dict.put("type", type+"");
        //dict.put("uuid", image_dev_name);
        SittingDeviceOfflineModel offlinePictureModel = new SittingDeviceOfflineModel();
        offlinePictureModel.setU_id(u_id);
        offlinePictureModel.setClientId(clientId);
        offlinePictureModel.setEquipmentUuid(equipmentUuid);
        offlinePictureModel.setPic_name(img_file);
        offlinePictureModel.setDateString(dict.get((String)FastBleConstants.DICT_DATE_STRING));
        offlinePictureModel.setLocaleTime(dict.get((String)FastBleConstants.DICT_LOCALE_TIME));
        offlinePictureModel.setType(type+"");
        offlinePictureModel.setUUID(dict.get((String)FastBleConstants.DICT_UUID));
        if (type == 2){
            happyPicModels.add(offlinePictureModel);
            offlinePreferences.setSittingHappyPictures(happyPicModels);
        }else {
            errorPicModels.add(offlinePictureModel);
            offlinePreferences.setSittingErrorPictures(errorPicModels);
        }

    }

    // 离线缓存bind=1 / unbind=2 信息数据
    private void cacheOfflineBindUnbind(int type, String uid, String equipmentUuid, String mac) {
        // 首先检查是否存在重复的；
        // 1，bind，检查bind中是否重复，有则更新；
        // 2，bind，检查unbind是否重复，有则删除（重新绑定了）；
        // 3, unbind,检查bind中是否重复，有则删除（解除绑定了）；
        // 4，unbind，解除unbind中是否重复，有则更新；

        //("u_id") String uid,
        //("equipmentUuid") String equipmentUuid,
        //("mac") String mac

        SittingDeviceOfflineModel subModel = new SittingDeviceOfflineModel();
        subModel.initDeviceOfflineModel();
        subModel.setU_id(uid);
        subModel.setEquipmentUuid(equipmentUuid);
        subModel.setMac(mac);

        if (type == 1){ // bind

            // bind
            List<SittingDeviceOfflineModel> tmpbindModels = bindModels;
            for (SittingDeviceOfflineModel model : tmpbindModels){
                String oldMac = model.getMac();
                if (mac.equals(oldMac)){
                    bindModels.remove(model);
                }
            }

            // unbind
            List<SittingDeviceOfflineModel> tmpunBindModels = unbindModels;
            for (SittingDeviceOfflineModel model : tmpunBindModels){
                String oldMac = model.getMac();
                if (mac.equals(oldMac)){
                    unbindModels.remove(model);
                }
            }

            bindModels.add(subModel);
        }else{
            // unbind
            List<SittingDeviceOfflineModel> tmpunBindModels = unbindModels;
            for (SittingDeviceOfflineModel model : tmpunBindModels){
                String oldMac = model.getMac();
                if (mac.equals(oldMac)){
                    unbindModels.remove(model);
                }
            }

            // bind
            List<SittingDeviceOfflineModel> tmpbindModels = bindModels;
            for (SittingDeviceOfflineModel model : tmpbindModels){
                String oldMac = model.getMac();
                if (mac.equals(oldMac)){
                    bindModels.remove(model);
                }
            }

            unbindModels.add(subModel);

            // unbind 需要清除sitpic,当uid和uuid相同时
            for (SittingDeviceOfflineModel model : sitPicModels) {
                String sitPic_uid = model.getU_id();
                String sitPic_uuid = model.getEquipmentUuid();
                if (uid.equals(sitPic_uid) && equipmentUuid.equals(sitPic_uuid)) {
                    sitPicModels.remove(model);
                }
            }

        }
        offlinePreferences.setSittingSitPictures(sitPicModels);
        offlinePreferences.setSittingDevicesBind(bindModels);
        offlinePreferences.setSittingDevicesUnbind(unbindModels);
    }

    // 循环发送离线报告到服务器
    // 整合一起发送离线报告
    private void syncReportJsonDataToServer123(){
        if (!NetworkUtils.isConnected()) {
            return;
        }

        List<String> jsonList = new ArrayList<>();
        String fDir =
                CZURConstants.MIRROR_PATH +
                userPreferences.getUserId() + "/" +
                currentDeviceModel.getEquipmentUID() +
                FastBleConstants.SITTING_POSE_REPORT_PATH;
        jsonList = FastBleToolUtils.getPathAllFiles(fDir);

        if (jsonList == null || jsonList.size()<1){
            return;
        }

        int index = 0;
        for (String filename: jsonList){
            index ++;
            onSendOfflineReport(filename);

            if (index > FastBleConstants.SEND_REPORT_COUNT){
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        syncReportJsonDataToServer();
                    }
                }, FastBleConstants.RUN_DELAY_TIMES1000 * 30);
                break;
            }
        }
    }
    private void syncReportJsonDataToServer(){
        if (!NetworkUtils.isConnected()) {
            return;
        }

        List<String> jsonList = new ArrayList<>();
        String fDir =
                CZURConstants.MIRROR_PATH +
                        userPreferences.getUserId() + "/" +
                        currentDeviceModel.getEquipmentUID() +
                        FastBleConstants.SITTING_POSE_REPORT_PATH;
        jsonList = FastBleToolUtils.getPathAllFiles(fDir);

        if (jsonList == null || jsonList.size()<1){
            return;
        }
        FastBleOperationUtils.setOfflineReportFileList(jsonList);

        // 1.添加所有数据到list中
        List<SyncSittingReportServerEntity> jsonReportList = new ArrayList<>();
        for (String filename: jsonList){
            String syncJson = FastBleToolUtils.readLocalReportJson(filename);
            if (syncJson.length() > 0) {
                Gson gs = new Gson();
                SyncSittingReportServerEntity entry = gs.fromJson(syncJson, SyncSittingReportServerEntity.class);//把JSON字符串转为对象
                jsonReportList.add(entry);
            }
        }

        // 2.去重复逻辑：useId相同的，endTime最近的一条上传；
        for(int i=0;i<jsonReportList.size(); i++){
            SyncSittingReportServerEntity outer = jsonReportList.get(i);
            String outerUseId=outer.getUseId();
            String outerEndTime = outer.getEndTime();
            for(int j=0;j<jsonReportList.size(); j++){
                SyncSittingReportServerEntity inner = jsonReportList.get(j);
                String innerUseId=inner.getUseId();
                String innerEndTime = inner.getEndTime();
                if (i != j) {
                    if (outerUseId.equals(innerUseId)) {
                        if (outerEndTime.compareTo(innerEndTime) < 0) {
                            jsonReportList.remove(outer);
                            i--;
                            break;
                        } else {
                            jsonReportList.remove(inner);
                            j--;
                        }
                    }
                }
            }
        }

        // 上传数据
        String reportJson = new Gson().toJson(jsonReportList);
        onSendOfflineReportNew(reportJson);
    }

    // 循环发送离线Algo到服务器
    private void syncAlgoJsonDataToServer(){
        if (!NetworkUtils.isConnected()) {
            return;
        }

        List<String> jsonList = new ArrayList<>();
        String fDir =
                CZURConstants.MIRROR_PATH +
                        userPreferences.getUserId() + "/" +
                        currentDeviceModel.getEquipmentUID() +
                        FastBleConstants.SITTING_ALGO_DATA_PATH;
        jsonList = FastBleToolUtils.getPathAllFiles(fDir);

        if (jsonList == null || jsonList.size()<1){
            return;
        }

        int index = 0;
        for (String filename: jsonList){
            index ++;
            onSendOfflineAlgo(filename);
            FastBleOperationUtils.threadSleep();

            if (index > FastBleConstants.SEND_REPORT_COUNT){
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        syncAlgoJsonDataToServer();
                    }
                }, FastBleConstants.RUN_DELAY_TIMES1000 * 30);
                break;
            }
        }
    }

    // 重新联网，需要同步绑定缓存数据

    // 同步设置的数据，优先于获取设备列表
    private void syncOfflineSettingToServer(){
        if (!NetworkUtils.isConnected()) {
            return;
        }

        // 1， 同步bind、unbind数据
        logI("syncOfflineDatasToServer.offlinePreferences=01.同步bind数据");
        for (SittingDeviceOfflineModel bindModel:bindModels){
            if (bindModel == null)
                continue;

            String uid = bindModel.getU_id();
            String deviceSN = bindModel.getEquipmentUuid();
            String mac = bindModel.getMac();
            FastBleHttpUtils.mirrorBindDevice(uid, deviceSN, mac);
            logI("syncOfflineDatasToServer.offlinePreferences=01.uid="+uid+", deviceSN="+deviceSN+", mac="+mac);
        }

        // 1， 同步bind、unbind数据
        logI("syncOfflineDatasToServer.offlinePreferences=02.同步unbind数据");
        for (SittingDeviceOfflineModel unbindModel:unbindModels){
            if (unbindModel == null)
                continue;

            String uid = unbindModel.getU_id();
            String deviceSN = unbindModel.getEquipmentUuid();
            String mac = unbindModel.getMac();
            FastBleHttpUtils.mirrorUnbindDevice(uid, deviceSN, mac);
            logI("syncOfflineDatasToServer.offlinePreferences=02.uid="+uid+", deviceSN="+deviceSN+", mac="+mac);
        }

        // 2.  同步更新本地所有的设置项
        if (settingsModels != null) {
            String equipmentUID_off = settingsModels.getEquipmentUID();
            String equipmentUID = currentDeviceModel.getEquipmentUID();
            if (equipmentUID_off.equals(equipmentUID)) {
                FastBleHttpUtils.updateAllSettingLocal(settingsModels);

                settingsModels = new SittingDeviceModel();
                settingsModels.initDeviceModel();
                offlinePreferences.setSittingDevicesSettings(settingsModels);
            }
        }
        // 同步一下设备名称
        String uid = currentDeviceModel.getBindUserId()+"";
        String equipmentUuid = currentDeviceModel.getEquipmentUID();
        String alias = currentDeviceModel.getAlias();
        FastBleHttpUtils.setDeviceAlias(uid, equipmentUuid, alias);

        // 更新完，删除；
        bindModels.clear();
        offlinePreferences.setSittingDevicesBind(bindModels);
        unbindModels.clear();
        offlinePreferences.setSittingDevicesUnbind(unbindModels);

    }

    // 同步图片和json数据文件，可以滞后一点儿；
    private void syncOfflineFilesToServer(){
        if (!NetworkUtils.isConnected()) {
            return;
        }

        // 3， 同步坐姿图片
        logI("syncOfflineDatasToServer.offlinePreferences=04.同步坐姿图片");
        for (SittingDeviceOfflineModel sitPicModel:sitPicModels) {
            if (sitPicModel == null)
                continue;

            String u_id=sitPicModel.getU_id();
            String clientId=sitPicModel.getClientId();
            String equipmentId=sitPicModel.getEquipmentUuid();
            String pic_name=sitPicModel.getPic_name();
            FastBleHttpUtils.uploadSittingOssInfo(u_id, clientId, equipmentId, pic_name);
            logI("syncOfflineDatasToServer.offlinePreferences=04.u_id="+u_id+", clientId="+clientId+", equipmentId="+equipmentId+", pic_name="+pic_name);
        }
        // 更新完，删除；
        sitPicModels.clear();
        offlinePreferences.setSittingSitPictures(sitPicModels);

    }

    private void syncOfflineDatasToServer() {
        logI("syncOfflineDatasToServer.offlinePreferences=00.重新联网，需要同步绑定缓存数据");
        List<SittingDeviceOfflineModel> tempModels = new ArrayList<>();
        if (!NetworkUtils.isConnected()) {
            return;
        }

        // 1， 同步bind、unbind数据
        // 2.  同步更新本地所有的设置项
        // 2， 同步报告数据
        // 3， 同步坐姿图片
        // 4，同步开心图、错误坐姿图片数据

        // 同步设置的数据，优先于获取设备列表
        syncOfflineSettingToServer();

        // 读取并发送报告，然后删除
        // 2， 同步报告数据
        logI("syncOfflineDatasToServer.offlinePreferences=03.读取并发送报告，然后删除");
        syncReportJsonDataToServer();

        // 2.5， 同步algo数据
        logI("syncAlgoJsonDataToServer.offlinePreferences=03.读取并发送algo，然后删除");
        syncAlgoJsonDataToServer();

        // 同步图片和json数据文件，可以滞后一点儿；
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                syncOfflineFilesToServer();
            }
        }, FastBleConstants.RUN_DELAY_TIMES1000 * 15);
    }

    // 上传单独的一张图片（开心或错误坐姿）
    private void uploadOnlyOnePicture(SittingDeviceOfflineModel happyModel){
        if (happyModel == null)
            return;

        String u_id=happyModel.getU_id();
        String clientId=happyModel.getClientId();
        String equipmentId=happyModel.getEquipmentUuid();
        String pic_name=happyModel.getPic_name();
        String dDataString=happyModel.getDateString();
        String dLocaleTime=happyModel.getLocaleTime();
        String dType=happyModel.getType();
        String dUuid=happyModel.getUUID();

        Map<String, String> dict = new HashMap<>();
        dict.put(FastBleConstants.DICT_DATE_STRING, dDataString);
        dict.put(FastBleConstants.DICT_LOCALE_TIME, dLocaleTime);
        dict.put(FastBleConstants.DICT_TYPE, dType);
        dict.put(FastBleConstants.DICT_UUID, dUuid);

        // 判断文件是否存在
        if (!FileUtils.isFileExists(pic_name)){
            return;
        }

        //String u_id, String clientId, String equipmentId, String pic_name, Map dict
        FastBleHttpUtils.uploadSittingHappyOssInfo(u_id, clientId, equipmentId, pic_name, dict);
    }


    // 坐姿报告数据接收
    private void onSendOfflineReport(String filename){
        logI("SittingFragment.onSendOfflineReport.filename="+filename);

        // 发送到服务器
        String udid = userPreferences.getIMEI();
        String t_id = userPreferences.getToken();
        String u_id = userPreferences.getUserId();
        String syncJson = FastBleToolUtils.readLocalReportJson(filename);
        if (syncJson.length()<1)
            return;

        FastBleHttpUtils.sendLocalReport(syncJson, udid, t_id, u_id, filename);
    }

    // 坐姿报告数据接收
    private void onSendOfflineReportNew(String reportJson){
        logI("SittingFragment.onSendOfflineReportNew");
        // 发送到服务器
        String udid = userPreferences.getIMEI();
        String t_id = userPreferences.getToken();
        String u_id = userPreferences.getUserId();
        FastBleHttpUtils.sendLocalReportNew(reportJson, udid, t_id, u_id);
    }

    // algo数据接收
    private void onSendOfflineAlgo(String filename){
        logI("SittingFragment.onSendOfflineAlgo.filename="+filename);

        // 发送到服务器
        String udid = userPreferences.getIMEI();
        String t_id = userPreferences.getToken();
        String u_id = userPreferences.getUserId();
        String equipmentId=currentDeviceModel.getEquipmentUID();

        String syncJson = FastBleToolUtils.readLocalReportJson(filename);
        if (syncJson.length()<1)
            return;

        FastBleHttpUtils.sendLocalAlog(syncJson, equipmentId, udid, t_id, u_id, filename);
    }

    // 无网络状态，需要按钮置灰
    private void setHomeButtonOnlineOffline(boolean flag){

        if (flag){
            EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_NETWORK_ONLINE, currentDeviceModel));
        }else{
            EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_NETWORK_OFFLINE, currentDeviceModel));
        }

        try {
            // 更多按钮
            RelativeLayout sitting_home_more_btn = (RelativeLayout) requireActivity().findViewById(R.id.sitting_home_more_btn);
            FastBleToolUtils.setViewButtonEnable(sitting_home_more_btn, flag);
        }catch(Exception e){

        }
    }

    // 获取timezone，返回字符串
    private String getTimeZoneNew(){
        TimeZone tz = TimeZone.getDefault();
        int mRawOffset = tz.getRawOffset() / 1000 / 60;
        int mDstSavings = tz.getDSTSavings();
        Context mContext = getContext();
        if (mContext == null){
            mContext = ActivityUtils.getTopActivity().getApplicationContext();
        }
        if (mContext == null){
            mContext = requireActivity().getApplicationContext();
        }
        if (mContext == null){
            mContext = getActivity().getBaseContext();
        }
        if (mContext == null){
            mContext = Utils.getApp().getApplicationContext();
        }
        String lang = FastBleToolUtils.getCurrentLanguage(mContext);
        String dateTime= FastBleToolUtils.getCurrentSysTime();
        JSONObject object =new JSONObject();
        try {
            object.put("dateTime",dateTime);
            object.put("timeZone",mRawOffset);
            object.put("timeDst",mDstSavings);
            object.put("lang",lang);
        }catch (Exception e){
            //
        }
        return object.toString();
    }
}