package com.czur.cloud.ui.starry.meeting.network.core;

import java.util.HashMap;

public class MiaoHttpMethod {

    public enum Method {
        Get, Post;
    }

    private String url;
    private HashMap<MiaoHttpParam,String> params = new HashMap<>();
    private HashMap<String, String> headers = new HashMap<>(), paths = new HashMap<>();
    private Method method;
    private boolean isAsync;
    private int noParamCount;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Method getMethod() {
        return method;
    }

    public void setMethod(Method method) {
        this.method = method;
    }

    public int getNoParamCount() {
        return noParamCount;
    }

    public void setNoParamCount(int noParamCount) {
        this.noParamCount = noParamCount;
    }

    public boolean isAsync() {
        return isAsync;
    }

    public void setIsAsync(boolean isAsync) {
        this.isAsync = isAsync;
    }

    public HashMap<MiaoHttpParam, String> getParams() {
        return params;
    }

    public void setParams(HashMap<MiaoHttpParam, String> params) {
        this.params = params;
    }

    public HashMap<String, String> getHeaders() {
        return headers;
    }

    public void setHeaders(HashMap<String, String> headers) {
        this.headers = headers;
    }

    public HashMap<String, String> getPaths() {
        return paths;
    }

    public void setPaths(HashMap<String, String> paths) {
        this.paths = paths;
    }
}
