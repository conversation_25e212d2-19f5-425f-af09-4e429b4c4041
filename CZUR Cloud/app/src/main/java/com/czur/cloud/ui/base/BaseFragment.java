package com.czur.cloud.ui.base;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.blankj.utilcode.util.ToastUtils;
import com.czur.cloud.R;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.util.validator.Validator;


/**
 * Created by Yz on 2018/2/15
 * Email：<EMAIL>
 */


public abstract class BaseFragment extends Fragment {
    public String TAG = BaseActivity.class.getSimpleName();
    private Context context = CzurCloudApplication.getApplication();

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return super.onCreateView(inflater, container, savedInstanceState);
    }

    @Override
    public void onResume() {
        super.onResume();
//        Log.i(TAG, "onResume:"+this.getClass().getSimpleName());
    }

    public void showProgressDialog() {
        showProgressDialog(true, false, null, false);
    }

    // Jason
    public void showProgressDialogSitting() {
        Activity activity = getActivity();
        if (activity instanceof BaseActivity) {
            ((BaseActivity) activity).showProgressDialogSitting();
        }
    }
    public void hideProgressDialogSitting() {
        Activity activity = getActivity();
        if (activity instanceof BaseActivity) {
            ((BaseActivity) activity).hideProgressDialogSitting();
        }
    }


    public void showProgressDialog(boolean isDark) {
        showProgressDialog(true, false, null, isDark);
    }

    public void showProgressDialog(boolean isDark, boolean isCancelable) {
        showProgressDialog(isCancelable, false, null, isDark);
    }

    public void showProgressDialog(String tag) {
        showProgressDialog(true, false, tag, false);
    }

    public void showProgressDialog(boolean cancelable, boolean touchable, String tag, boolean isDark) {
        Activity activity = getActivity();
        if (activity instanceof BaseActivity) {
            ((BaseActivity) activity).showProgressDialog(cancelable, touchable, tag, isDark,false);
        }
    }

    public void hideProgressDialog() {
        Activity activity = getActivity();
        if (activity instanceof BaseActivity) {
            ((BaseActivity) activity).hideProgressDialog();
        }
    }

    public void hideProgressDialog(boolean immediately) {
        Activity activity = getActivity();
        if (activity instanceof BaseActivity) {
            ((BaseActivity) activity).hideProgressDialog(immediately);
        }
    }

    public void showMessage(int resId) {
        ToastUtils.showShort(resId);
    }

    public void showMessage(String text) {
        ToastUtils.showShort(text);
    }

    public void showMessage(int resId, Object... args) {
        ToastUtils.showShort(resId, args);
    }

    /**
     * @des: 长Toast
     * @params:
     * @return:
     */
    public void showLongMessage(int resId) {
        ToastUtils.showLong(resId);
    }

    public void showLongMessage(String text) {
        ToastUtils.showLong(text);
    }

    public void showLongMessage(int resId, Object... args) {
        ToastUtils.showLong(resId, args);
    }

    public void setServerTime() {
        if (Validator.isEmpty(UserPreferences.getInstance(getActivity()).getReportTime())) {
            getServerTime();
        }

    }

    private void getServerTime() {
        HttpManager.getInstance().request().getReportServerTime(UserPreferences.getInstance(getActivity()).getUserId(), String.class, new MiaoHttpManager.Callback<String>() {
            @Override
            public void onStart() {

            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                String serverTime = entity.getBody();
                UserPreferences.getInstance(getActivity()).setReportTime(serverTime);
                UserPreferences.getInstance(getActivity()).setCallTime(serverTime);
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                showMessage(R.string.toast_server_error);
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
                showMessage(R.string.toast_server_error);

            }
        });

    }

    @Override
    public void onDetach() {
        super.onDetach();
        hideProgressDialog();
    }

}
