package com.czur.cloud.adapter;

import android.app.Activity;
import android.content.Context;
import android.net.Uri;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.RelativeLayout;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.R;
import com.czur.cloud.cache.WrongQuestionImageRequest;
import com.czur.cloud.entity.AuraMateWrongQuestionModel;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.drawable.ScalingUtils;
import com.facebook.drawee.interfaces.DraweeController;
import com.facebook.drawee.view.SimpleDraweeView;
import com.facebook.imagepipeline.request.ImageRequestBuilder;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * Created by Yz on 2018/3/16
 * Email：<EMAIL>
 */


public class WrongQuestionAdapter extends RecyclerView.Adapter<ViewHolder> {

    private Activity mActivity;

    public List<AuraMateWrongQuestionModel> getDatas() {
        return datas;
    }

    //当前需要显示的所有的图片数据
    private List<AuraMateWrongQuestionModel> datas;

    //是否进入选择
    private boolean isSelectItem;
    private LayoutInflater mInflater;

    private float ppiLevel;
    private ScalingUtils.ScaleType customScaleType = (outTransform, parentBounds, childWidth, childHeight, focusX, focusY) -> {
        outTransform.setScale(ppiLevel, ppiLevel);
        outTransform.postTranslate(0, 0);
        return outTransform;
    };

    private LinkedHashMap<String, String> isCheckedMap = new LinkedHashMap<>();

    /**
     * 构造方法
     */
    public WrongQuestionAdapter(Activity activity, List<AuraMateWrongQuestionModel> datas, boolean isSelectItem) {
        this.mActivity = activity;
        this.isSelectItem = isSelectItem;
        this.datas = datas;
        mInflater = LayoutInflater.from(activity);
        ppiLevel = mActivity.getResources().getDisplayMetrics().density;
    }

    public void refreshData(List<AuraMateWrongQuestionModel> books, boolean isSelectItem, LinkedHashMap<String, String> isCheckedMap) {
        this.isSelectItem = isSelectItem;
        this.datas = books;
        this.isCheckedMap = isCheckedMap;
        notifyDataSetChanged();

    }
    public void refreshData(List<AuraMateWrongQuestionModel> books) {
        this.datas = books;
        notifyDataSetChanged();
    }


    public void refreshData(boolean isSelectItem) {
        this.isSelectItem = isSelectItem;
        notifyDataSetChanged();
    }


    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return new WrongQuestionHolder(mInflater.inflate(R.layout.item_wrong_question, parent, false));
    }


    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {
            final WrongQuestionHolder mHolder = (WrongQuestionHolder) holder;
            mHolder.mItem = datas.get(position);
            WrongQuestionImageRequest imageRequest = new WrongQuestionImageRequest(ImageRequestBuilder.newBuilderWithSource(Uri.parse(mHolder.mItem.getSmallOssKeyUrl())));
            imageRequest.setImageRequestType(1);
            imageRequest.setImageRequestObject("small");
            mHolder.wrongQuestionImg.getHierarchy().setActualImageScaleType(customScaleType);
            DraweeController controller = Fresco.newDraweeControllerBuilder()
                    .setImageRequest(imageRequest)
                    .setOldController(mHolder.wrongQuestionImg.getController())
                    .build();
            mHolder.wrongQuestionImg.setController(controller);

            if (isSelectItem) {
                mHolder.imgRl.setX(SizeUtils.dp2px(62));
                mHolder.checkBox.setVisibility(View.VISIBLE);
                mHolder.checkBox.setTag(mHolder.mItem.getId() + "");
            } else {
                mHolder.imgRl.setX(0);
                mHolder.checkBox.setVisibility(View.GONE);
            }
            mHolder.checkBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    if (isChecked) {
                        if (!isCheckedMap.containsKey(mHolder.checkBox.getTag() + "")) {
                            //选中时添加
                            isCheckedMap.put(mHolder.mItem.getId() + "", mHolder.mItem.getSmallOssKeyUrl());
                        }
                    } else {
                        if (isCheckedMap.containsKey(mHolder.checkBox.getTag() + "")) {
                            //没选中时移除
                            isCheckedMap.remove(mHolder.mItem.getId() + "");
                        }
                    }
                    if (onItemCheckListener != null) {
                        onItemCheckListener.onItemCheck(position, mHolder.mItem, isCheckedMap, datas.size());
                    }
                }
            });

            if (isCheckedMap != null) {
                mHolder.checkBox.setChecked(isCheckedMap.containsKey(mHolder.mItem.getId() + "") ? true : false);
            } else {
                mHolder.checkBox.setChecked(false);
            }

            mHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onWrongQuestionClickListener != null) {
                        onWrongQuestionClickListener.onEtFilesClick(mHolder.mItem, position, mHolder.checkBox);
                    }

                }
            });



    }



    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return datas.size();
    }


    private static class WrongQuestionHolder extends ViewHolder {
        public final View mView;
        AuraMateWrongQuestionModel mItem;
        RelativeLayout wrongQuestionItem;
        SimpleDraweeView wrongQuestionImg;
        CheckBox checkBox;
        RelativeLayout imgRl;


        WrongQuestionHolder(View itemView) {
            super(itemView);
            mView = itemView;
            wrongQuestionItem = (RelativeLayout) itemView.findViewById(R.id.wrong_question_item);
            wrongQuestionImg = itemView.findViewById(R.id.wrong_question_img);
            checkBox = (CheckBox) itemView.findViewById(R.id.check);

            imgRl = (RelativeLayout) itemView.findViewById(R.id.img_rl);

        }

    }






    public int getTotalSize() {
        return datas.size();
    }



    public View inflate(Context context, int layoutId) {
        if (layoutId <= 0) {
            return null;
        }
        return LayoutInflater.from(context).inflate(layoutId, null);
    }



    private OnWrongQuestionClickListener onWrongQuestionClickListener;

    public void setOnWrongQuestionClickListener(OnWrongQuestionClickListener onWrongQuestionClickListener) {
        this.onWrongQuestionClickListener = onWrongQuestionClickListener;
    }

    public interface OnWrongQuestionClickListener {
        void onEtFilesClick(AuraMateWrongQuestionModel filesBean, int position, CheckBox checkBox);
    }


    private OnItemCheckListener onItemCheckListener;

    public void setOnItemCheckListener(OnItemCheckListener onItemCheckListener) {
        this.onItemCheckListener = onItemCheckListener;
    }

    public interface OnItemCheckListener {
        void onItemCheck(int position, AuraMateWrongQuestionModel filesBean, LinkedHashMap<String, String> isCheckedMap, int totalSize);

    }

}
