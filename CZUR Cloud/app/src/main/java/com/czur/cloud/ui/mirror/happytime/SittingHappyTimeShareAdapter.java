package com.czur.cloud.ui.mirror.happytime;

import android.app.Activity;
import android.content.Context;
import android.net.Uri;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.blankj.utilcode.util.ScreenUtils;
import com.czur.cloud.R;
import com.czur.cloud.cache.AuraCustomImageRequest;
import com.czur.cloud.ui.mirror.model.SittingHappyTimePictureModel;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.interfaces.DraweeController;
import com.facebook.drawee.view.SimpleDraweeView;
import com.facebook.imagepipeline.request.ImageRequestBuilder;

import java.util.List;

/**
 * Created by <PERSON> on 20210528
 */

public class SittingHappyTimeShareAdapter extends RecyclerView.Adapter<ViewHolder> {

    private Activity mActivity;
    //当前需要显示的所有的图片数据
    private List<SittingHappyTimePictureModel> datas;
    //是否进入选择
    private LayoutInflater mInflater;

    /**
     * 构造方法
     */
    public SittingHappyTimeShareAdapter(Activity activity, List<SittingHappyTimePictureModel> datas) {
        this.mActivity = activity;
        this.datas = datas;
        mInflater = LayoutInflater.from(activity);
    }

    public void refreshData(List<SittingHappyTimePictureModel> datas) {
        this.datas = datas;
        notifyDataSetChanged();
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return new EtFilesHolder(mInflater.inflate(R.layout.item_sitting_happy_picture_share, parent, false));
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {
        if (holder instanceof EtFilesHolder) {
            final EtFilesHolder mHolder = (EtFilesHolder) holder;
            mHolder.mItem = datas.get(position);
            Uri lowResUri = Uri.parse(getSmallUrl(mHolder.mItem));
            AuraCustomImageRequest imageRequest = new AuraCustomImageRequest(ImageRequestBuilder.newBuilderWithSource(lowResUri));
            imageRequest.setImageRequestType(1);
            imageRequest.setImageRequestObject("small");
            DraweeController controller = Fresco.newDraweeControllerBuilder()
                    .setImageRequest(imageRequest)
                    .setOldController(mHolder.etFilesImg.getController())
                    .build();
            mHolder.etFilesImg.setController(controller);
        }
    }

    private String getSmallUrl(SittingHappyTimePictureModel item) {
        return item.getSmallImgUrl();
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return datas.size();
    }

    private static class EtFilesHolder extends ViewHolder {
        public final View mView;
        SittingHappyTimePictureModel mItem;
        SimpleDraweeView etFilesImg;

        EtFilesHolder(View itemView) {
            super(itemView);
            mView = itemView;
            etFilesImg = (SimpleDraweeView) itemView.findViewById(R.id.et_files_img);
            ViewGroup.LayoutParams layoutParams = itemView.getLayoutParams();
//            layoutParams.width = (ScreenUtils.getScreenWidth() - SizeUtils.dp2px(16)) / 6;
            layoutParams.width = (int)((ScreenUtils.getScreenWidth()) / 6 * 1.0f);
            layoutParams.height = (int) (layoutParams.width * 1.0f);
            itemView.setLayoutParams(layoutParams);
        }
    }

    public int getTotalSize() {
        return datas.size();
    }

    public View inflate(Context context, int layoutId) {
        if (layoutId <= 0) {
            return null;
        }
        return LayoutInflater.from(context).inflate(layoutId, null);
    }

}
