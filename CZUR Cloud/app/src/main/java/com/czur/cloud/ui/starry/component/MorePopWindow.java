package com.czur.cloud.ui.starry.component;

import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.czur.cloud.R;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.StarryCommonEvent;

import org.greenrobot.eventbus.EventBus;

public class MorePopWindow extends PopupWindow {
    private View conentView;

    public MorePopWindow(final Activity context) {
        LayoutInflater inflater = (LayoutInflater) context
                .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        conentView = inflater.inflate(R.layout.starry_activity_more_menu, null);
        int h = context.getWindowManager().getDefaultDisplay().getHeight();
        int w = context.getWindowManager().getDefaultDisplay().getWidth();
        // 设置SelectPicPopupWindow的View
        this.setContentView(conentView);
        // 设置SelectPicPopupWindow弹出窗体的宽
        this.setWidth(LayoutParams.WRAP_CONTENT);
        // 设置SelectPicPopupWindow弹出窗体的高
        this.setHeight(LayoutParams.WRAP_CONTENT);
        // 设置SelectPicPopupWindow弹出窗体可点击
        this.setFocusable(true);
        this.setOutsideTouchable(true);
        // 刷新状态
        this.update();
        // 实例化一个ColorDrawable颜色为半透明
        ColorDrawable dw = new ColorDrawable(0000000000);
        // 点back键和其他地方使其消失,设置了这个才能触发OnDismisslistener ，设置其他控件变化等操作
        this.setBackgroundDrawable(dw);
        // mPopupWindow.setAnimationStyle(android.R.style.Animation_Dialog);
        // 设置SelectPicPopupWindow弹出窗体动画效果
//        this.setAnimationStyle(R.style.AnimationPreview);

        TextView menu_select = conentView.findViewById(R.id.msg_menu_select);
        menu_select.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                EventBus.getDefault().post(new StarryCommonEvent(EventType.STARRY_MESSAGE_SELECT, ""));
                dismiss();
            }
        });

        TextView menu_read = conentView.findViewById(R.id.msg_menu_read);
        menu_read.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                EventBus.getDefault().post(new StarryCommonEvent(EventType.STARRY_MESSAGE_READ, ""));
                dismiss();
            }
        });

        TextView menu_delete = conentView.findViewById(R.id.msg_menu_delete);
        menu_delete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                EventBus.getDefault().post(new StarryCommonEvent(EventType.STARRY_MESSAGE_DELETE, ""));
                dismiss();
            }
        });

    }

    public void setMenuReadDisable(Boolean flag){
        TextView menu_read = conentView.findViewById(R.id.msg_menu_read);
        if (flag){
            menu_read.setEnabled(false);
            menu_read.setClickable(false);
            menu_read.setAlpha(0.5f);
        }else{
            menu_read.setEnabled(true);
            menu_read.setClickable(true);
            menu_read.setAlpha(1.0f);
        }
        menu_read.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                EventBus.getDefault().post(new StarryCommonEvent(EventType.STARRY_MESSAGE_READ, ""));
                dismiss();
            }
        });

    }

    public void showPopupWindow(View parent) {
        if (!this.isShowing()) {
            this.showAsDropDown(parent, parent.getLayoutParams().width / 2, 1);
        } else {
            this.dismiss();
        }
    }
}