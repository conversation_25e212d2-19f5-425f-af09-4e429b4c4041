package com.czur.cloud.ui.starry.base

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import com.czur.cloud.R
import com.blankj.utilcode.util.BarUtils
import android.widget.TextView
import com.blankj.utilcode.util.ActivityUtils
import com.czur.cloud.ui.base.BaseActivity

abstract class StarryNewBaseActivity : BaseActivity(), View.OnClickListener {
    protected var imgBack: ImageView? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        intent?.let {
            handlePreIntent(it)
        }

        beforeOnCreate()
        setStatusBarColor(R.color.white)
        BarUtils.setStatusBarLightMode(this, true)
        val resId = getLayout()
        if (resId > 0) {
            setContentView(resId)
        }else {
            setContentView(R.layout.starry_activity_base)
        }

        imgBack = findViewById(R.id.user_back_btn)
        imgBack?.setOnClickListener(this)
        setPageTitle(R.string.starry_title)

        initViews()
        initListener()
        initData(savedInstanceState)

    }

    open fun beforeOnCreate(){}

    open fun handlePreIntent(preIntent: Intent) {}
    abstract fun getLayout(): Int
    open fun initViews() {}
    open fun initListener() {}
    open fun initData(savedInstanceState: Bundle?) {}

    private fun setPageTitle(id: Int) {
        val title: TextView? = findViewById(R.id.user_title) ?: null
        title?.setText(id)
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.user_back_btn -> ActivityUtils.finishActivity(this)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
    }
}