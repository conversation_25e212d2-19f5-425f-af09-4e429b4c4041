package com.czur.cloud.ui.eshare

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.view.View
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.czur.cloud.R
import com.czur.cloud.ui.eshare.engine.HostHearBeat
import com.czur.cloud.ui.eshare.view.PaintController.Companion.isOpenPaint
import com.czur.cloud.ui.starry.base.StarryBaseActivity
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.utils.singleClick
import kotlinx.android.synthetic.main.eshare_open_paint_fragment.*
import kotlinx.android.synthetic.main.layout_user_top_bar.*
import kotlinx.coroutines.flow.collect

/**
 * 画笔开关
 */
class EShareOpenPainActivity : StarryBaseActivity(), View.OnClickListener {
    private var sp: SharedPreferences? =null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.white)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.eshare_open_paint_fragment)

        if (sp == null) {
            sp = this!!.getSharedPreferences("brush", Context.MODE_PRIVATE)
        }
        initComponent()
    }

    private fun initComponent() {

        user_title?.setText(R.string.setting)

        user_back_btn?.singleClick {
            ActivityUtils.finishActivity(this)
        }
        eshare_paint_switch?.setDefaultSwitchState(sp!!.getBoolean("isShow",true))

        eshare_paint_switch?.setOnSwitchChange {isOn, fromClick ->
            launch {
                isOpenPaint.emit(isOn)
            }
            val spedit =   sp!!.edit()
            if (isOn){
                spedit.putBoolean("isShow", true)
                spedit.commit()
            }else{
                spedit.putBoolean("isShow", false)
                spedit.commit()
            }
        }

        launch {
            HostHearBeat.castStatusChanged.collect {
                if (it==5)finish()
            }
        }

    }

}