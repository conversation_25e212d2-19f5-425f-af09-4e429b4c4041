package com.czur.cloud.ui.user;

import static com.czur.czurutils.log.CZURLogUtilsKt.logE;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.app.Activity;
import android.app.DownloadManager;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.constant.PermissionConstants;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.ServiceUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.blankj.utilcode.util.UtilsTransActivity;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.UpdateEvent;
import com.czur.cloud.preferences.VersionPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.mirror.comm.FastBleOperationUtils;
import com.czur.cloud.ui.user.download.AFileDownloadManager;
import com.czur.cloud.ui.user.download.DownloadAFileService;
import com.czur.cloud.util.AppInitUtils;
import com.czur.cloud.util.PermissionUtil;
import com.czur.cloud.util.VersionUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

/**
 * Created by Yz on 2018/2/28.
 * Email：<EMAIL>
 */

public class AboutActivity extends BaseActivity implements View.OnClickListener {


    private ImageView accountBackBtn, czur_icon;
    private TextView accountTitle;
    private TextView appVersion;
    private RelativeLayout userAboutRl;
    private TextView aboutNewVersionTv;
    private AFileDownloadManager downloadManager;
    private String apkPath;
    private TextView aboutUpdateBtn;
    private ImageView userAboutRightArrow;
    private String updateUrl;
    private String notes;
    private CloudCommonPopup commonPopup;
    private String version;
    private String apkName = "";

    private WeakReference<Context> weakReference;
    private DownloadManager mDownloadManager;
    private long mReqId;
    private int clickCount = 0;
    private Timer timer;
    private Boolean isUploading = false;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_about);
        initComponent();
        registerEvent();

        // 海外暂无new提示
        if (BuildConfig.IS_OVERSEAS) {
            findViewById(R.id.user_about_ll).setVisibility(View.GONE);
        }

        if (NetworkUtils.isConnected()) {
            checkUpdate();
        } else {
            showMessage(R.string.toast_no_connection_network);
        }

    }

    private void initComponent() {
        EventBus.getDefault().register(this);

        appVersion = (TextView) findViewById(R.id.app_version);
        accountBackBtn = (ImageView) findViewById(R.id.about_back_btn);
        accountTitle = (TextView) findViewById(R.id.about_title);
        accountTitle.setText(R.string.about);
        userAboutRl = (RelativeLayout) findViewById(R.id.user_about_rl);
        aboutNewVersionTv = (TextView) findViewById(R.id.about_new_version_tv);
        aboutUpdateBtn = (TextView) findViewById(R.id.about_update_btn);
        userAboutRightArrow = (ImageView) findViewById(R.id.user_about_right_arrow);
        czur_icon = (ImageView) findViewById(R.id.czur_icon);

        appVersion.setText(AppUtils.getAppVersionName());

        weakReference = new WeakReference<Context>(this);
        mDownloadManager = (DownloadManager) weakReference.get().getSystemService(Context.DOWNLOAD_SERVICE);
        mReqId = VersionPreferences.getInstance().getDownloadId();

    }

    private void registerEvent() {
        accountBackBtn.setOnClickListener(this);
        userAboutRl.setOnClickListener(this);
        userAboutRl.setEnabled(false);
        userAboutRl.setClickable(false);
        czur_icon.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View view) {
                if (BuildConfig.DEBUG) {
                    ToastUtils.showLong("Debug");
                }
                return false;
            }
        });

        // czur_icon连续点击三次响应
        czur_icon.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                clickCount++;
                if (timer != null) {
                    timer.cancel();
                }
                timer = new Timer();
                timer.schedule(new TimerTask() {
                    @Override
                    public void run() {
                        clickCount = 0;
                    }
                }, 1000);
                if (clickCount == 3) {
                    // 执行事件响应代码
                    if (isUploading == false) {
                        doUploadLog();
                    }
                    // 重置计数器
                    clickCount = 0;
                }
            }
        });

    }

    // 上传日志文件zip包
    private void doUploadLog() {
        isUploading = true;
        logI("doUploadLog start.");
        AppInitUtils.uploadLogFile();

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case IS_LATEST_VERSION:
                updateVersionUpdateUI();
                break;
            case HAS_NEW_VERSION:
                updateVersionUpdateUI();
                if (event instanceof UpdateEvent) {
                    userAboutRl.performClick();
                }
                break;
            case HAS_NEW_VERSION_OK:
                FastBleOperationUtils.setIsAPPUpdateOK(true);
                break;
            default:
                break;
        }
    }

    private void updateVersionUpdateUI() {
        if (VersionUtil.INSTANCE.hasNewVersion(VersionUtil.INSTANCE.getRemoteVersionInfoEntity())) {
            aboutUpdateBtn.setText(R.string.app_update);
            userAboutRightArrow.setVisibility(View.VISIBLE);
            aboutNewVersionTv.setVisibility(View.VISIBLE);
            // 海外版本GooglePlay不需要自动更新
            if (!BuildConfig.IS_OVERSEAS) {
                userAboutRl.setClickable(true);
                userAboutRl.setEnabled(true);
            }
        } else {
            aboutUpdateBtn.setText(R.string.already_latest_version);
            userAboutRightArrow.setVisibility(View.INVISIBLE);
            aboutNewVersionTv.setVisibility(View.GONE);
            userAboutRl.setEnabled(false);
            userAboutRl.setClickable(false);
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.about_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.user_about_rl:
                logI("AboutActivity.user_about_rl.isIsAPPUpdateOK="+FastBleOperationUtils.isIsAPPUpdateOK());
                if (NetworkUtils.isConnected()) {
                    if (!FastBleOperationUtils.isIsAPPUpdateOK()) {
                        showMessage(R.string.no_wifi_download);
                    } else {
                        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.R) {
                            requestPermission();
                        }else{
                            checkNewVersion();
                        }
                    }
                } else {
                    showMessage(R.string.toast_no_connection_network);
                }
                break;

            default:
                break;
        }
    }

    /**
     * @des: 检查新版本后下载或者安装
     * @params:
     * @return:
     */
    private void checkNewVersion() {
        if (BuildConfig.IS_OVERSEAS) {
            return;
        }
        File apkFile = new File(apkPath, apkName);
        if (VersionPreferences.getInstance().getDownloadId() != -1) {//如果存有下载记录并且下载记录是当前版本的,就去验证是否可以使用
            if (VersionPreferences.getInstance().getApkName().contains(version)) {
                apkFile = new File(apkPath, VersionPreferences.getInstance().getApkName());
            }
        }

        //apkUrl=http://osscdn.czur.com/resource/software/android/czur/成者CZUR_V2.3.137.apk
        //apkPath=/storage/emulated/0/Download/
        //apkName = 成者CZUR_V2.3.137.apk;

        logI("AboutActivity.checkNewVersion.apkPath=" + apkPath,
                "apkName=" + apkName);
        //apk是否存在，存在就进行安装
        boolean flag = apkFile.exists() && VersionPreferences.getInstance().getDownloadId() != -1;
        logI("AboutActivity.checkNewVersion.apkFile=" + apkFile, "apkFile.exists()=" + flag);
        if (flag) {
            logI("AboutActivity.checkNewVersion apk is exist!");
            DownloadAFileService.checkHistoryDownloadTask(this, false);
        }else {
            logI("AboutActivity.checkNewVersion apk is NOT exist!");
            if (NetworkUtils.isWifiConnected()) {
                FastBleOperationUtils.setIsAPPUpdateOK(false);
                startDownloadService(true);
            } else {
                downloadInNotWifi();
            }
        }



    }

    /**
     * @des: 开始下载service
     * @params:
     * @return:
     */

    private void startDownloadService(boolean isWifi) {
        boolean flag = ServiceUtils.isServiceRunning(DownloadAFileService.class);

        if (!flag) {
            if (isWifi) {
                showMessage(R.string.wifi_download);
            } else {
                showMessage(R.string.no_wifi_download);
            }
        } else {
            ServiceUtils.stopService(DownloadAFileService.class);
            showMessage(R.string.no_wifi_download);
        }
        Intent intent = new Intent(this, DownloadAFileService.class);
        intent.putExtra("updateUrl", updateUrl);
        intent.putExtra("notes", notes);
        intent.putExtra("apkName", apkName);
        startService(intent);
    }

    /**
     * @des: 请求读写权限
     * @params:
     * @return:
     */

    private void requestPermission() {
        // 海外版本GooglePlay不需要自动更新
        if (BuildConfig.IS_OVERSEAS) {
            return;
        }

        PermissionUtils.permission(PermissionUtil.getStoragePermission())
                .rationale(new PermissionUtils.OnRationaleListener() {
                    @Override
                    public void rationale(UtilsTransActivity activity, ShouldRequest shouldRequest) {
                        showMessage(R.string.denied_update);
                        shouldRequest.again(true);
                    }
                })
                .callback(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(List<String> permissionsGranted) {
                        checkNewVersion();
                    }

                    @Override
                    public void onDenied(List<String> permissionsDeniedForever,
                                         List<String> permissionsDenied) {
                        showMessage(R.string.denied_update);
                    }
                })
                .theme(new PermissionUtils.ThemeCallback() {
                    @Override
                    public void onActivityCreate(Activity activity) {
                        ScreenUtils.setFullScreen(activity);
                    }
                })
                .request();
    }

    /**
     * @des: 检查更新
     * @params:
     * @return:
     */

    private void checkUpdate() {
        // 海外暂无new提示
        if (BuildConfig.IS_OVERSEAS) {
            return;
        }

        showProgressDialog();
        VersionUtil.INSTANCE.getNewVersionInfoForJava(this, versionInfoEntity -> {
            logI("new version : " + versionInfoEntity.toString());
            updateUrl = versionInfoEntity.getPackageUrl();
            notes = versionInfoEntity.getNotes();
            version = versionInfoEntity.getVersionName();
            apkPath = CZURConstants.DOWNLOAD_PATH;

            String randomVersion = String.valueOf(System.currentTimeMillis() / 1000);
            randomVersion = randomVersion.substring(randomVersion.length() - 6, randomVersion.length());
            apkName = getString(R.string.app_name) + "_" + version + "." + randomVersion + ".apk";

            if (VersionUtil.INSTANCE.hasNewVersion(versionInfoEntity)) {
                EventBus.getDefault().post(new UpdateEvent(EventType.HAS_NEW_VERSION));
            } else {
                EventBus.getDefault().post(new UpdateEvent(EventType.IS_LATEST_VERSION));
            }
            hideProgressDialog();
            return null;
        }, throwable -> {
            logE("checkUpdate.throwable="+throwable.toString());
            hideProgressDialog();
            return null;
        });
    }


    /**
     * @des: 显示是否在非wifi网络下载Dialog
     * @params:
     * @return:
     */

    private void downloadInNotWifi() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(AboutActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON_YES_NO);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.download_app_prompt));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {

                if (commonPopup != null) {
                    commonPopup.dismiss();
                }
                FastBleOperationUtils.setIsAPPUpdateOK(false);
                startDownloadService(false);
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                FastBleOperationUtils.setIsAPPUpdateOK(true);
                dialog.dismiss();
            }
        });
        commonPopup = builder.create();
        commonPopup.show();
    }


    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (ServiceUtils.isServiceRunning(DownloadAFileService.class)) {
//            ServiceUtils.stopService(DownloadApkService.class);
        }
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }
}
