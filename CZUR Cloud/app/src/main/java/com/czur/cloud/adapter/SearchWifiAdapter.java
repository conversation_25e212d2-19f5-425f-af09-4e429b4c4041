package com.czur.cloud.adapter;

import android.content.Context;
import android.content.Intent;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.czur.cloud.R;
import com.czur.cloud.ui.auramate.AuraMateWifiActivity;
import com.czur.cloud.ui.base.BaseActivity;

import java.util.List;

public class SearchWifiAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    public List<ScanResult> getWifiList() {
        return wifiList;
    }

    private List<ScanResult> wifiList;
    private BaseActivity activity;
    private boolean noNeedKey;
    private String equipmentId;

    public SearchWifiAdapter(List<ScanResult> wifiList, BaseActivity activity, boolean noNeedKey, String equipmentId) {
        this.wifiList = wifiList;
        this.activity = activity;
        this.noNeedKey = noNeedKey;
        this.equipmentId = equipmentId;
    }


    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(activity.getLayoutInflater().inflate(R.layout.item_scan_wifi, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        ViewHolder viewHolder = (ViewHolder) holder;
        ScanResult scanResult = wifiList.get(position);
        viewHolder.tvWifiName.setText(scanResult.SSID);
        viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(activity, AuraMateWifiActivity.class);
                intent.putExtra("ssid", scanResult.SSID);
                intent.putExtra("noNeedKey", noNeedKey);
                intent.putExtra("equipmentId", equipmentId);
                ActivityUtils.startActivity(intent);
            }
        });
        if (checkIsCurrentWifiHasPassword(scanResult.SSID)) {
            viewHolder.imgLock.setVisibility(View.VISIBLE);
        } else {
            viewHolder.imgLock.setVisibility(View.GONE);
        }
    }

    @Override
    public int getItemCount() {
        return wifiList.size();
    }

    private boolean checkIsCurrentWifiHasPassword(String currentWifiSSID) {
        try {
            WifiManager wifiManager = (WifiManager) activity.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            // 得到当前连接的wifi热点的信息
            WifiInfo wifiInfo = wifiManager.getConnectionInfo();
            // 得到当前WifiConfiguration列表，此列表包含所有已经连过的wifi的热点信息，未连过的热点不包含在此表中
            List<WifiConfiguration> wifiConfiguration = wifiManager.getConfiguredNetworks();
            String currentSSID = wifiInfo.getSSID();
            if (currentSSID != null && currentSSID.length() > 2) {
                if (currentSSID.startsWith("\"") && currentSSID.endsWith("\"")) {
                    currentSSID = currentSSID.substring(1, currentSSID.length() - 1);
                }
                if (wifiConfiguration != null && wifiConfiguration.size() > 0) {
                    for (WifiConfiguration configuration : wifiConfiguration) {
                        if (configuration != null && configuration.status == WifiConfiguration.Status.CURRENT) {
                            String ssid = null;
                            if (!TextUtils.isEmpty(configuration.SSID)) {
                                ssid = configuration.SSID;
                                if (configuration.SSID.startsWith("\"") && configuration.SSID.endsWith("\"")) {
                                    ssid = configuration.SSID.substring(1, configuration.SSID.length() - 1);
                                }
                            }
                            if (TextUtils.isEmpty(currentSSID) || currentSSID.equalsIgnoreCase(ssid)) {
                                //KeyMgmt.NONE表示无需密码
                                return (!configuration.allowedKeyManagement.get(WifiConfiguration.KeyMgmt.NONE));
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            //do nothing
        }
        //默认为需要连接密码
        return true;
    }


    private static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvWifiName;
        ImageView imgLock;

        ViewHolder(View itemView) {
            super(itemView);
            tvWifiName = (TextView) itemView.findViewById(R.id.tv_wifi_name);
            imgLock = itemView.findViewById(R.id.img_lock);
        }
    }


}
