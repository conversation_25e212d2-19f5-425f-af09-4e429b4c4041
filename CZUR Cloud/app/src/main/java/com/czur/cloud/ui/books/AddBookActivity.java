package com.czur.cloud.ui.books;

import android.os.Bundle;
import android.text.Editable;
import android.text.InputFilter;
import android.text.Spanned;
import android.text.TextWatcher;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.badoo.mobile.util.WeakHandler;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.R;
import com.czur.cloud.entity.realm.BookEntity;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.NoHintEditText;
import com.czur.cloud.util.EtUtils;
import com.czur.cloud.util.FixKeyboardUtils;
import com.czur.cloud.util.validator.Validator;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

import io.realm.Realm;

/**
 * Created by Yz on 2018/2/28.
 * Email：<EMAIL>
 */

public class AddBookActivity extends BaseActivity implements View.OnClickListener {

    private ImageView addBookBackBtn;
    private TextView addBookTitle;
    private NoHintEditText addBookEdt;
    private TextView addBookBtn;
    private Realm realm;
    private TextView addBookName;
    private WeakHandler handler;
    private RelativeLayout addBookRl;
    private LinearLayout changeKeyboardLl;
    private int hideHeight;
    private int showHeight;
    private int realHeight;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_add_book);
        initComponent();
        registerEvent();
    }

    private void initComponent() {
        handler = new WeakHandler();
        addBookBackBtn = (ImageView) findViewById(R.id.add_book_back_btn);
        addBookTitle = (TextView) findViewById(R.id.add_book_title);
        addBookName = (TextView) findViewById(R.id.add_book_name_tv);
        addBookEdt = (NoHintEditText) findViewById(R.id.add_book_edt);
        addBookEdt.setFilters(new InputFilter[]{inputFilter, new InputFilter.LengthFilter(20)});
        addBookBtn = (TextView) findViewById(R.id.add_book_btn);
        addBookRl = (RelativeLayout) findViewById(R.id.add_book_rl);
        changeKeyboardLl = (LinearLayout) findViewById(R.id.change_keyboard_ll);

        addBookTitle.setText(R.string.add_book);
        realm = Realm.getDefaultInstance();


    }

    private void registerEvent() {

        addBookBackBtn.setOnClickListener(this);
        addBookBtn.setOnClickListener(this);
        addBookBtn.setSelected(false);
        addBookBtn.setClickable(false);

        addBookEdt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s.length() > 0) {
                    addBookBtn.setSelected(true);
                    addBookBtn.setClickable(true);
                    addBookName.setText(s);
                } else {
                    addBookBtn.setSelected(false);
                    addBookBtn.setClickable(false);
                    addBookName.setText("");
                }
            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s.length() > 0) {
                    addBookBtn.setSelected(true);
                    addBookBtn.setClickable(true);
                    addBookName.setText(s);
                } else {
                    addBookBtn.setSelected(false);
                    addBookBtn.setClickable(false);
                    addBookName.setText("");
                }
            }
        });
        setSoftInputVisibleListener();
    }


    InputFilter inputFilter = new InputFilter() {
        @Override
        public CharSequence filter(CharSequence charSequence, int i, int i1, Spanned spanned, int i2, int i3) {

            if (!EtUtils.containsEmoji(charSequence.toString())) {
                return null;
            } else {
                showMessage(R.string.nickname_toast_symbol);
                return "";
            }

        }
    };


    private void setSoftInputVisibleListener() {
        FixKeyboardUtils.registerSoftInputChangedListener(this,
                new FixKeyboardUtils.OnSoftInputChangedListener() {
                    @Override
                    public void onSoftInputChanged(int height) {
                        int screenHeight = ScreenUtils.getScreenHeight();
//                        boolean softInputVisible = KeyboardUtils.isSoftInputVisible(AddBookActivity.this);
                        boolean softInputVisible = height < (screenHeight / 4) ? false : true;
                        if (softInputVisible) {
                            showHeight = height;
                            int remainHeight = ScreenUtils.getScreenHeight() - height;
                            handler.post(new KeyBoardActionRunnable(View.GONE, height, remainHeight));
                        } else {
                            hideHeight = height;
                            handler.post(new KeyBoardActionRunnable(View.VISIBLE, 0, 0));
                        }
                    }
                });

    }

    private class KeyBoardActionRunnable implements Runnable {
        private int visibility;
        private int height;
        private int remainHeight;

        public KeyBoardActionRunnable(int visibility, int height, int remainHeight) {
            this.visibility = visibility;
            this.height = height;
            this.remainHeight = remainHeight;
        }

        @Override
        public void run() {
            addBookRl.setVisibility(visibility);
            if (visibility == View.GONE) {
                RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) changeKeyboardLl.getLayoutParams();
                layoutParams.setMargins(0, 0, 0, remainHeight / 2 - SizeUtils.dp2px(138) / 2);
                changeKeyboardLl.setLayoutParams(layoutParams);
            } else {
                RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) changeKeyboardLl.getLayoutParams();
                layoutParams.setMargins(0, 0, 0, SizeUtils.dp2px(30));
                changeKeyboardLl.setLayoutParams(layoutParams);
            }
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.add_book_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.add_book_btn:
                //文件名字重复添加括号

                int i = 0;
                String bookName = addBookEdt.getText().toString();
                String finalName = bookName;
                Realm realm = Realm.getDefaultInstance();
                BookEntity sameBook = realm.where(BookEntity.class)
                        .equalTo("bookName", addBookEdt.getText().toString())
                        .equalTo("isDelete", 0)
                        .findFirst();
                while (Validator.isNotEmpty(sameBook)) {
                    long current = System.currentTimeMillis();
                    i++;
                    finalName = bookName + String.format(getString(R.string.repeat_name_format), i + "");
                    sameBook = realm.where(BookEntity.class)
                            .equalTo("bookName", finalName)
                            .equalTo("isDelete", 0)
                            .findFirst();
                }
                final String finalName1 = finalName;

                realm.executeTransaction(new Realm.Transaction() {
                    @Override
                    public void execute(Realm realm) {
                        BookEntity bookEntity = realm.createObject(BookEntity.class, UUID.randomUUID().toString());
                        bookEntity.setIsDirty(1);
                        bookEntity.setBookName(finalName1);
                        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
                        String curDate = formatter.format(new Date(System.currentTimeMillis()));
                        bookEntity.setCreateTime(curDate);
                        bookEntity.setUpdateTime(curDate);
                        startAutoSync();
                    }
                });
                ActivityUtils.finishActivity(AddBookActivity.this);
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        FixKeyboardUtils.unregisterSoftInputChangedListener(this);
        realm.close();
    }
}
