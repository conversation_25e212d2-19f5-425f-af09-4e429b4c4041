package com.czur.cloud.ui.starry.model

//{
//    "meetingName":"测试即时会议",
//    "isRecoder":true,
//    "participants":[
//        {
//            "meetingAccout":666666,
//            "isAdmin":true
//        },
//        {
//            "meetingAccout":888888,
//            "isAdmin":false
//        }
//    ]
//}
data class MeetingRequest(
    val isRecoder: Boolean,
    val meetingName: String,
    val participants: MutableList<Participant>,
    val meetingPassword: String = ""
)

data class Participant(
    val isAdmin: <PERSON>olean,
    val meetingAccout: String
)