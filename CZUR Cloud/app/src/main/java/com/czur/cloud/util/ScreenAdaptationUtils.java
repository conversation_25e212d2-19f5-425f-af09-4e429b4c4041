package com.czur.cloud.util;

import java.lang.reflect.Method;

public class ScreenAdaptationUtils {

    public static boolean hasLiuHaiInVivo() {
        try {
            Class clazz = Class.forName("android.util.FtFeature");
            Method method = clazz.getDeclaredMethod("isFeatureSupport", int.class);
            return (Boolean) method.invoke(clazz, 0x00000020);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}
