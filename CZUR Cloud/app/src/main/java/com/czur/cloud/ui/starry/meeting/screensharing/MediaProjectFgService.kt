package com.czur.cloud.ui.starry.meeting.screensharing

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Intent
import android.graphics.BitmapFactory
import android.graphics.Color
import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi
import androidx.core.app.NotificationCompat
import com.czur.cloud.R

class MediaProjectFgService : Service() {

    override fun onBind(p0: Intent?) = null

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        return START_NOT_STICKY
    }

    override fun onCreate() {
        super.onCreate()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            createNotificationChannel()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        stopForeground(true)
    }

    private fun createNotificationChannel123() {
        val notifyId = 55431
        val notification: Notification
        val eventTitle = getString(R.string.app_name)
        val builder = NotificationCompat.Builder(
            this, NotificationHelper.generateChannelId(
                application, notifyId
            )
        )
            .setCategory(Notification.CATEGORY_CALL)
            .setContentTitle(eventTitle)
            .setContentText(eventTitle)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) builder.color =
            resources.getColor(R.color.black)
        notification = builder.build()
        notification.flags = notification.flags or Notification.FLAG_ONGOING_EVENT

        startForeground(notifyId, notification)

    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun createNotificationChannel() {
        val notifyId = 55431
        Log.i("Jason", "createNotificationChannel")
        val name: CharSequence = getString(R.string.app_name)
        val description = "Notice that we are trying to capture the screen!!"
        val importance = NotificationManager.IMPORTANCE_HIGH
        val channelId = "CZUR"
        val channel = NotificationChannel(channelId, name, importance)
//        channel.description = description
        channel.enableLights(true)
        channel.lightColor = Color.RED
        channel.enableVibration(true)
        channel.vibrationPattern = longArrayOf(100, 200, 300, 400, 500, 400, 300, 200, 400)
        val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.createNotificationChannel(channel)
        // Create a notification and set the notification channel.
        val notification: Notification = NotificationCompat.Builder(this, channelId)
            .setContentText(name.toString())
            .setSmallIcon(R.mipmap.ic_launcher)
            .setLargeIcon(BitmapFactory.decodeResource(resources, R.mipmap.ic_launcher))
            .setChannelId(channelId)
            .setWhen(System.currentTimeMillis())
            .build()
        startForeground(notifyId, notification)
    }

}

/**
 * 用户拒绝屏幕分享权限的回调
 */
interface RefusedPermissionListener {

    fun onRefusedPermission()

    fun onAllowPermission()

}
