package com.czur.cloud.ui.et;

import android.os.Bundle;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.SearchUserAdapter;
import com.czur.cloud.event.AddShareUserEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.model.SearchUserModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Yz on 2018/6/1.
 * Email：<EMAIL>
 */
public class EtAddUserActivity extends BaseActivity implements View.OnClickListener {

    private ImageView normalBackBtn;
    private TextView normalTitle;
    private EditText etAddUserEdt;
    private TextView etAddUserSearchBtn;
    private RecyclerView etAddRecyclerView;
    private List<SearchUserModel> userShareModels;
    private UserPreferences userPreferences;
    private View etSearchDivider;
    private HttpManager httpManager;
    private SearchUserAdapter searchAdapter;
    private String deviceId;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_et_add_user);
        initComponent();
        registerEvent();
        initRecyclerView();
    }


    private void initComponent() {
        userPreferences = UserPreferences.getInstance(this);
        httpManager = HttpManager.getInstance();
        deviceId = getIntent().getStringExtra("deviceId");
        etSearchDivider = (View) findViewById(R.id.et_search_divider);
        normalBackBtn = (ImageView) findViewById(R.id.normal_back_btn);
        normalTitle = (TextView) findViewById(R.id.normal_title);
        etAddUserEdt = (EditText) findViewById(R.id.et_add_user_edt);
        etAddUserSearchBtn = (TextView) findViewById(R.id.et_add_user_search_btn);
        etAddRecyclerView = (RecyclerView) findViewById(R.id.et_add_recyclerView);
        normalTitle.setText(R.string.et_search);

    }

    private void registerEvent() {
        normalBackBtn.setOnClickListener(this);
        etAddUserSearchBtn.setOnClickListener(this);
    }


    /**
     * @des: 初始化列表
     * @params:
     * @return:
     */

    private void initRecyclerView() {
        userShareModels = new ArrayList<>();
        searchAdapter = new SearchUserAdapter(this, userShareModels);
        searchAdapter.setOnItemClickListener(onItemClickListener);
        etAddRecyclerView.setHasFixedSize(true);
        etAddRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        etAddRecyclerView.setAdapter(searchAdapter);

    }

    /**
     * @des: 添加列表点击事件
     * @params:
     * @return:
     */

    private SearchUserAdapter.onItemClickListener onItemClickListener = new SearchUserAdapter.onItemClickListener() {
        @Override
        public void onItemClick(int position, SearchUserModel searchUserModel) {
            if (searchUserModel.getId().equals(userPreferences.getUserId())){
                showMessage(R.string.toast_add_people_not_self);
            }else {
                addShareUser(deviceId, searchUserModel.getId());
            }
        }
    };

    /**
     * @des: 搜索用户
     * @params:
     * @return:
     */


    private void searchUser(final String searchUserId) {
        httpManager.requestPassport().searchUser(userPreferences.getChannel(), searchUserId, SearchUserModel.class, new MiaoHttpManager.Callback<SearchUserModel>() {
            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<SearchUserModel> entity) {
                hideProgressDialog();
                etAddRecyclerView.setVisibility(View.VISIBLE);
                etSearchDivider.setVisibility(View.VISIBLE);
                List<SearchUserModel> userList = new ArrayList<>();
                userList.add(entity.getBody());
                searchAdapter.refreshData(userList);

            }

            @Override
            public void onFailure(MiaoHttpEntity<SearchUserModel> entity) {
                hideProgressDialog();
                if (entity.getCode() == MiaoHttpManager.STATUS_NOT_USER) {
                    showMessage(R.string.toast_user_no_exist);
                }
                etAddRecyclerView.setVisibility(View.GONE);
                etSearchDivider.setVisibility(View.GONE);
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
                etAddRecyclerView.setVisibility(View.GONE);
                etSearchDivider.setVisibility(View.GONE);
            }
        });
    }

    /**
     * @des: 添加共享用户
     * @params:
     * @return:
     */
    private void addShareUser(String deviceId, String searchUserId) {
        httpManager.request().addDeviceShareUser(deviceId, searchUserId, userPreferences.getUserId(), String.class, new MiaoHttpManager.Callback<String>() {
            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                EventBus.getDefault().post(new AddShareUserEvent(EventType.ADD_SHARE_USER));
                ActivityUtils.finishActivity(EtAddUserActivity.class);
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                if (entity.getCode() == MiaoHttpManager.STATUS_NOT_MANAGER) {
                    showMessage(R.string.toast_add_fail);
                }
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
                showMessage(R.string.toast_server_error);

            }
        });
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            case R.id.normal_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.et_add_user_search_btn:
                String searchName = etAddUserEdt.getText().toString();
                if (searchName.length() == 0) {
                    showMessage(R.string.toast_format_wrong);
                } else if (isValidatorLoginName(searchName)) {
                    showMessage(R.string.toast_format_wrong);
                } else {
                    searchUser(searchName);
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }
}
