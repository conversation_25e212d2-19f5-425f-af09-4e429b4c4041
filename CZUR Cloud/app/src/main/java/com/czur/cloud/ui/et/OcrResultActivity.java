package com.czur.cloud.ui.et;

import android.content.ComponentName;
import android.content.Intent;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.common.ShareSDKCallback;
import com.czur.cloud.common.ShareSDKParams;
import com.czur.cloud.common.ShareSDKPlatforms;
import com.czur.cloud.common.ShareSDKType;
import com.czur.cloud.common.ShareSDKUtils;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.OcrAgainEvent;
import com.czur.cloud.ui.auramate.AuraMateOcrActivity;
import com.czur.cloud.ui.auramate.AuraMatePreviewActivity;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.dialog.SocialShareDialog;
import com.czur.cloud.util.ClipboardUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * Created by Yz on 2018/2/28.
 * Email：<EMAIL>
 */
public class OcrResultActivity extends BaseActivity implements View.OnClickListener {

    public static boolean isOcrCancel = false;  // 取消ocr识别

    private ImageView handwritingResultFinishBtn;
    private TextView handwritingResultText;
    private RelativeLayout handwritingResultCopyRl;
    private RelativeLayout handwritingResultShareRl;
    private String resultText;
    private static final int SHARE_SUCCESS_CODE = 666;
    private SocialShareDialog socialShareDialog;
    private TextView btnOk;
    private int position;
    private String fileId;
    private String url;
    private boolean isAuraMate;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.black_2a);
        BarUtils.setNavBarColor(this,getColor(R.color.black_2a));
        BarUtils.setStatusBarLightMode(this, false);
        setContentView(R.layout.activity_ocr_result);
        initComponent();
        registerEvent();
    }

    private void initComponent() {
        EventBus.getDefault().register(this);
        resultText = getIntent().getStringExtra("resultText");
        btnOk = (TextView) findViewById(R.id.btn_ok);
        url = getIntent().getStringExtra("url");
        fileId = getIntent().getStringExtra("fileId");
        position = getIntent().getIntExtra("position", 0);
        isAuraMate = getIntent().getBooleanExtra("isAuraHome", false);
        handwritingResultFinishBtn = (ImageView) findViewById(R.id.handwriting_result_finish_btn);
        handwritingResultText = (TextView) findViewById(R.id.handwriting_result_text);
        handwritingResultCopyRl = (RelativeLayout) findViewById(R.id.handwriting_result_copy_rl);
        handwritingResultShareRl = (RelativeLayout) findViewById(R.id.handwriting_result_share_rl);
        handwritingResultText.setText(resultText);
        socialShareDialog = new SocialShareDialog(this, shareDialogOnClickListener);
    }

    private void registerEvent() {
        btnOk.setOnClickListener(this);
        handwritingResultFinishBtn.setOnClickListener(this);
        handwritingResultCopyRl.setOnClickListener(this);
        handwritingResultShareRl.setOnClickListener(this);
    }

    /**
     * @des: 分享dialog监听
     * @params:
     * @return:
     */

    private SocialShareDialog.ShareDialogOnClickListener shareDialogOnClickListener = new SocialShareDialog.ShareDialogOnClickListener() {
        @Override
        public void onShareItemClick(int viewId) {
            switch (viewId) {
                case R.id.weixin_share:
                case R.id.friend_share:
                case R.id.qq_share:
                case R.id.qq_zone_share:
                case R.id.weibo_share:
                    showShare(viewId);
                    socialShareDialog.dismiss();
                    break;
                case R.id.share_dialog_cancel_btn:
                    socialShareDialog.dismiss();
                default:
                    break;
            }
        }
    };

    public void showShare(int viewId) {
        ShareSDKParams params = new ShareSDKParams();
        params.setSite(getString(R.string.app_name)).setText(resultText);
        switch (viewId) {
            case R.id.weixin_share:
                params.setImageData(BitmapFactory.decodeResource(getResources(), R.mipmap.ic_launcher)).setShareType(ShareSDKType.SHARE_TEXT).setPlatform(ShareSDKPlatforms.WECHAT);
                break;
            case R.id.friend_share:
                params.setImageData(BitmapFactory.decodeResource(getResources(), R.mipmap.ic_launcher)).setShareType(ShareSDKType.SHARE_TEXT).setPlatform(ShareSDKPlatforms.WECHAT_MOMENTS);
                break;
            case R.id.qq_share:
                if (AppUtils.isAppInstalled("com.tencent.mobileqq")) {
                    Intent intent = new Intent("android.intent.action.SEND");
                    intent.setType("text/plain");
                    intent.putExtra(Intent.EXTRA_SUBJECT, "纯文字分享");
                    intent.putExtra(Intent.EXTRA_TEXT, resultText);
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    intent.setComponent(new ComponentName("com.tencent.mobileqq", "com.tencent.mobileqq.activity.JumpActivity"));
                    startActivity(intent);
                } else {
                    showMessage("应用未安装");
                }
                break;
            case R.id.qq_zone_share:
                params.setPlatform(ShareSDKPlatforms.QZONE);
                break;
            case R.id.weibo_share:
                params.setPlatform(ShareSDKPlatforms.WEIBO);
                break;
            default:
                return;
        }
        params.setCallback(new ShareSDKCallback() {
            @Override
            public void onComplete() {
            }

            @Override
            public void onError() {
                showMessage(R.string.share_failed);
            }

            @Override
            public void onCancel() {
            }
        });
        ShareSDKUtils.INSTANCE.share(params);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case AURA_MATE_OCR_AGAIN:

                OcrAgainEvent ocrAgainEvent = (OcrAgainEvent) event;
                handwritingResultText.setText(ocrAgainEvent.getOcrResult());
                break;

            default:
                break;
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.handwriting_result_copy_rl:
                ClipboardUtils.copyText(handwritingResultText.getText().toString());
                showMessage(R.string.copy_success);
                break;
            case R.id.handwriting_result_finish_btn:
                if (isAuraMate) {
                    ActivityUtils.finishToActivity(AuraMatePreviewActivity.class, false);
                } else {
                    ActivityUtils.finishToActivity(EtPreviewActivity.class, false);
                }
                break;
            case R.id.handwriting_result_share_rl:
                socialShareDialog.show();
                break;
            case R.id.btn_ok:
                if (isAuraMate) {
                    Intent intent1 = new Intent(OcrResultActivity.this, AuraMateOcrActivity.class);
                    intent1.putExtra("fileId", fileId);
                    intent1.putExtra("position", position);
                    intent1.putExtra("url", url);
                    intent1.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
                    startActivity(intent1);

                } else {
                    Intent intent1 = new Intent(OcrResultActivity.this, EtOcrActivity.class);
                    intent1.putExtra("fileId", fileId);
                    intent1.putExtra("position", position);
                    intent1.putExtra("url", url);
                    intent1.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
                    startActivity(intent1);
                }

                break;
            default:
                break;
        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        if (isAuraMate) {
            ActivityUtils.finishToActivity(AuraMatePreviewActivity.class, false);
        } else {
            ActivityUtils.finishToActivity(EtPreviewActivity.class, false);
        }

    }

    @Override
    protected void onDestroy() {
//        EventBus.getDefault().post(new AuraMateOperateEvent(EventType.SITTING_POSITION_OPERATE,equipmentUid));
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        super.onDestroy();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, final Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == SHARE_SUCCESS_CODE) {

        }
    }
}
