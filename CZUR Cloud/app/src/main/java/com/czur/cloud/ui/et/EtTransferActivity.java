package com.czur.cloud.ui.et;

import android.content.DialogInterface;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.TransferUserAdapter;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.TransferUserEvent;
import com.czur.cloud.model.BaseModel;
import com.czur.cloud.model.UserShareModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.google.gson.reflect.TypeToken;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Yz on 2018/6/1.
 * Email：<EMAIL>
 */
public class EtTransferActivity extends BaseActivity implements View.OnClickListener {

    private ImageView normalBackBtn;
    private TextView normalTitle;
    private RecyclerView etTransferRecyclerView;
    private String deviceId;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private TransferUserAdapter transferUserAdapter;
    private List<UserShareModel> userShareModels;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_transfer_user);
        initComponent();
        registerEvent();
        initRecyclerView();
        getShareUser();

    }

    /**
     * @des: 初始化列表
     * @params:
     * @return:
     */

    private void initRecyclerView() {
        userShareModels = new ArrayList<>();
        transferUserAdapter = new TransferUserAdapter(this, userShareModels);
        transferUserAdapter.setOnItemClickListener(onItemClickListener);
        etTransferRecyclerView.setHasFixedSize(true);
        etTransferRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        etTransferRecyclerView.setAdapter(transferUserAdapter);

    }

    private void initComponent() {
        httpManager = HttpManager.getInstance();
        userPreferences = UserPreferences.getInstance(this);
        deviceId = getIntent().getStringExtra("deviceId");
        normalBackBtn = (ImageView) findViewById(R.id.normal_back_btn);
        normalTitle = (TextView) findViewById(R.id.normal_title);
        etTransferRecyclerView = (RecyclerView) findViewById(R.id.et_transfer_recyclerView);
        normalTitle.setText(R.string.et_transfer_equipment);

    }

    private void registerEvent() {
        normalBackBtn.setOnClickListener(this);
    }


    /**
     * @des: 转让列表点击事件
     * @params:
     * @return:
     */

    private TransferUserAdapter.onItemClickListener onItemClickListener = new TransferUserAdapter.onItemClickListener() {
        @Override
        public void onItemClick(int position, UserShareModel userShareModel) {

            confirmTransferDialog(userShareModel);


        }
    };

    /**
     * @des: 转让接口
     * @params:
     * @return:
     */

    private void transferToOtherUser(UserShareModel userShareModel) {
        httpManager.request().transferDevice(deviceId, userShareModel.getUserId(), userPreferences.getUserId(), BaseModel.class, new MiaoHttpManager.Callback<BaseModel>() {
            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<BaseModel> entity) {
                hideProgressDialog();
                EventBus.getDefault().post(new TransferUserEvent(EventType.TRANSFER_SHARE_USER));
                ActivityUtils.finishToActivity(EtManageActivity.class, false);
            }

            @Override
            public void onFailure(MiaoHttpEntity<BaseModel> entity) {
                hideProgressDialog();
                showLongMessage(R.string.request_failed_alert);

            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
                showLongMessage(R.string.request_failed_alert);
            }
        });
    }

    /**
     * @des: 显示转让Dialog
     * @params:
     * @return:
     */

    private void confirmTransferDialog(final UserShareModel userShareModel) {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(EtTransferActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(String.format(getResources().getString(R.string.tip_transfer_et_alert), userShareModel.getName()));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                transferToOtherUser(userShareModel);
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();
    }

    /**
     * @des:获取分享用户列表
     * @params:
     * @return:
     */

    private void getShareUser() {
        httpManager.request().getShareUser(deviceId, userPreferences.getUserId(), new TypeToken<List<UserShareModel>>() {
        }.getType(), new MiaoHttpManager.Callback<UserShareModel>() {
            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<UserShareModel> entity) {
                hideProgressDialog();
                transferUserAdapter.refreshData(entity.getBodyList());

            }

            @Override
            public void onFailure(MiaoHttpEntity<UserShareModel> entity) {
                hideProgressDialog();
                if (entity.getCode() == MiaoHttpManager.STATUS_NOT_MANAGER) {
                    showMessage(R.string.toast_not_manager_jurisdiction);

                }
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
                showMessage(R.string.toast_server_error);
            }
        });
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            case R.id.normal_back_btn:
                ActivityUtils.finishActivity(this);
                break;

            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }
}
