package com.czur.cloud.ui.starry.activity

import android.content.Context
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import androidx.annotation.RequiresApi
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.*
import com.czur.cloud.R
import com.czur.cloud.event.BaseEvent
import com.czur.cloud.event.EventType
import com.czur.cloud.event.StarryCommonEvent
import com.czur.cloud.netty.bean.StarryRecivedNoticeData
import com.czur.cloud.preferences.StarryPreferences
import com.czur.cloud.ui.component.popup.CloudCommonPopup
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants
import com.czur.cloud.ui.starry.adapter.NoticesAdapter
import com.czur.cloud.ui.starry.base.StarryBaseActivity
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.component.MorePopWindow
import com.czur.cloud.ui.starry.livedatabus.LiveDataBus
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.meeting.bean.MessageBean
import com.czur.cloud.ui.starry.meeting.dialog.StarryCommonPopup
import com.czur.cloud.ui.starry.meeting.dialog.bottommenu.PopBottomMenuWindow
import com.czur.cloud.ui.starry.utils.Tools
import com.czur.cloud.ui.starry.viewmodel.StarryMessageViewModel
import com.czur.cloud.ui.starry.viewmodel.StarryViewModel
import com.czur.czurutils.log.logI
import com.google.gson.Gson
import com.scwang.smartrefresh.layout.listener.OnLoadMoreListener
import com.scwang.smartrefresh.layout.listener.OnRefreshListener
import kotlinx.android.synthetic.main.starry_activity_company_list_contacts.*
import kotlinx.android.synthetic.main.starry_activity_message.*
import kotlinx.android.synthetic.main.starry_layout_top_bar.*
import kotlinx.android.synthetic.main.starry_layout_top_bar.user_back_btn
import kotlinx.android.synthetic.main.starry_layout_top_bar_sel.*
import kotlinx.android.synthetic.main.starry_layout_top_bar_sel.msg_top_select_cancel
import kotlinx.android.synthetic.main.starry_layout_top_bar_sel.msg_top_select_title
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.*

/**
 *
 */
class StarryMessageActivity : StarryBaseActivity(), View.OnClickListener {

    private val alpha = 0.8f

    private val viewModel by lazy {
        ViewModelProvider(this).get(StarryMessageViewModel::class.java)
    }
    private val starryViewModel by lazy {
        ViewModelProvider(StarryActivity.mainActivity ?: this).get(StarryViewModel::class.java)
    }

    private val mAdapter by lazy {
        NoticesAdapter()
    }

    private var lastClickTime: Long = 0

    init {
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: BaseEvent) {
        when (event.eventType) {
            EventType.STARRY_MESSAGE_SELECT -> {
                onMessageSelect()
            }
            EventType.STARRY_MESSAGE_READ -> {
                showProgressDialog()
                onAllRead()
            }
            EventType.STARRY_MESSAGE_DELETE -> {
                onAllDeleted()
            }

            EventType.STARRY_COMPANY_NOTICE,
            EventType.STARRY_COMPANY_NOTICE_REMOVE -> {
                if (mAdapter.isSelecting()){//选择状态时,新消息不提醒bug#14787
                    return
                }

                // 更新消息状态的返回长连接消息：STARRY_COMPANY_NOTICE，不需要更新列表
                val newEvent = event as StarryCommonEvent
                val data = newEvent.params
                val model = Gson().fromJson(data, StarryRecivedNoticeData::class.java)
                val from_api = model?.from_api ?: ""
                if (from_api == "updateNoticesStatus"){
                    return
                }
                // 获取数据
//                showProgressDialog()
//                viewModel.getNoticesListRefresh()

                launch {
                    // 获取新数据前的顶部数据的位置
                    val pos_top = (messageList?.layoutManager as LinearLayoutManager).findFirstVisibleItemPosition()
                    val position =
                        (messageList?.layoutManager as LinearLayoutManager).findLastVisibleItemPosition()
                    val addCount = viewModel.getNoticesListTotal()
                    logI("StarryMessageActivity.pos_top=${pos_top},position=${position}，addCount=${addCount}")
                    if (addCount > 0){

                        // 当顶部位移小于新增消息数量,显示出来新消息
                        if( pos_top <= addCount ) {
                            // 有数据增加，移动到update前的位置
                            (messageList?.layoutManager as LinearLayoutManager).scrollToPosition(0)

                        }else{
                            (messageList?.layoutManager as LinearLayoutManager).scrollToPosition(
                                position + addCount
                            )
                        }
                        //需要把"已读"可点击
                        viewModel.mUnreadCount.postValue(addCount)
                    }

                    if ((viewModel.mNoticesList.value?.size
                            ?: 0) <= StarryConstants.STARRY_CALL_PAGE_SIZE
                    ){
                        viewModel.getNoticesListRefresh()
                    }
                }

            }
            else -> {
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.white)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.starry_activity_message)
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        // 初始化view
        initView()

        // 观察数据变化
        initObserver()

        registerEvent()

        showProgressDialog()
        viewModel.getNoticesListRefresh()

    }

    // 初始化view
    private fun initView() {

        user_title?.setText(R.string.starry_title_message)
        user_more_btn?.visibility = View.VISIBLE
        starry_msg_nodata?.visibility = View.GONE

        msg_bottom_bar_del_rl?.setOnClickListener(this)

        msg_top_select_title?.text =
            String.format(
                getString(R.string.starry_msg_select_title_num), "", "")

        refreshLayout?.run {
            setEnableOverScrollDrag(false)
            setEnableOverScrollBounce(false)
            setEnableAutoLoadMore(true)
            setEnableRefresh(true)
            setEnableNestedScroll(false)
            setEnableFooterFollowWhenNoMoreData(true)
            setEnableLoadMoreWhenContentNotFull(false)
            setEnableLoadMore(true)
        }

        refreshLayout
            ?.setOnLoadMoreListener(OnLoadMoreListener {
                if (starryViewModel.clickNoNetwork()){
                    it.finishLoadMore(false)
                    return@OnLoadMoreListener
                }
                viewModel.getNoticesListMore()
                if (viewModel.mMoreCount < StarryConstants.STARRY_CALL_PAGE_SIZE) {
                    it.finishLoadMoreWithNoMoreData()
                }else {
                    it.finishLoadMore()
                }
            })
            ?.setOnRefreshListener(OnRefreshListener {
                if (starryViewModel.clickNoNetwork()){
                    it.finishRefresh(false)
                    return@OnRefreshListener
                }
                viewModel.getNoticesListRefresh()
                // 下拉刷新，把选中的取消掉
                viewModel.isCheckedMap.value?.clear()
                it.finishRefresh()
            })

        messageList?.run {
            setHasFixedSize(true)
            layoutManager = LinearLayoutManager(this@StarryMessageActivity)
            adapter = mAdapter
        }

        mAdapter.setOnItemClickListener(object: NoticesAdapter.OnClickListener{
            override fun onclick(isSelected: Boolean, id: String, title: String, position: Int, checkMap: LinkedHashMap<String, String>) {
                if (isSelected){
                    logI("setOnItemClickListener.onclick.position=${position}")
                    selectedItemFromList(position, checkMap)
                }else {
                    logI("setOnItemClickListener.onclick.title=${title}")
                    if (starryViewModel.clickNoNetwork()){
                        return
                    }
                    showConfirmDetailDialog(title)
                    launch {
                        viewModel.updateNoticesStatus(
                            id,
                            StarryConstants.STARRY_MESSAGE_STATUS_READ
                        )
                    }
                }
            }

            override fun onclickSel(position: Int, checkMap: LinkedHashMap<String, String>) {
                logI("setOnItemClickListener.onclickSel.position=${position}")
                selectedItemFromList(position, checkMap)
            }

            override fun onclicklong(holder: NoticesAdapter.InnerHodler, position: Int, id: String) {
                logI("setOnItemClickListener.onclicklong.position=${position},id=${id}")
                if (starryViewModel.clickNoNetwork()){
                    return
                }
                onCreateBottomSheetDialog(id)
            }
        })

        // 双击title，、返回顶部
        user_title?.setOnClickListener {
            val clickTime = System.currentTimeMillis()
            if (clickTime - lastClickTime < 800) {
                logI("StarryMessageActivity.user_title?.doubleClick")
                (messageList?.layoutManager as LinearLayoutManager).scrollToPosition(0)
            }
            lastClickTime = clickTime
        }
    }

    private fun selectedItemFromList(position: Int, checkMap: LinkedHashMap<String, String>){
        viewModel.isCheckedMap.value = checkMap
        val checkCount = viewModel.isCheckedMap.value?.size ?: 0
        val count = viewModel.mNoticesList.value?.size ?: 0
        msg_top_select_title?.text =
            String.format(
                getString(R.string.starry_msg_select_title_num),
                checkCount.toString() + "",
                count.toString() + ""
            )

        if (checkCount == count){
            msg_top_select_all.text = getString(R.string.starry_msg_unselect_all)
        }else {
            msg_top_select_all.text = getString(R.string.starry_msg_select_all)
        }
    }
    // 观察数据变化
    private fun initObserver() {

        viewModel.isRefreshListFlag.observe(this){
            if (it){
                refreshLayout?.run {
                    setEnableLoadMore(true)
                    setEnableRefresh(true)
                    setNoMoreData(false)
                }
            }
        }

        viewModel.apply {
            showProgressDialog()
            mNoticesList.observe(this@StarryMessageActivity, androidx.lifecycle.Observer {
                hideProgressDialog()
                // 数据列表更新
                // 更新适配器
                mAdapter.setDatas(it)

                if (it.isNotEmpty()){
                    changeUIStatus(true)

                    // 如果没有未读消息了，需要通知各处刷新红点儿
                    EventBus.getDefault()
                        .post(StarryCommonEvent(EventType.STARRY_COMPANY_NOTICE_CLEAR, ""))

                    val checkCount = viewModel.isCheckedMap.value?.size ?: 0
                    msg_top_select_title?.text =
                        String.format(
                            getString(R.string.starry_msg_select_title_num),
                            checkCount.toString() + "",
                            viewModel.mNoticesList.value?.size.toString() + ""
                        )
                }else{
                    changeUIStatus(false)
                }
            })
            showProgressDialog()
            mRecycleMode.observe(this@StarryMessageActivity, androidx.lifecycle.Observer {
                hideProgressDialog()
                if (it) {
                    starry_layout_top_bar_sel_rl.visibility = View.VISIBLE
                    starry_layout_top_bar_rl.visibility = View.INVISIBLE
                    msg_bottom_bar_del_outer_rl.visibility = View.VISIBLE
                    Tools.setViewButtonEnable(msg_bottom_bar_del_rl, false, alpha)
                    changeDelBtnDisable(false)
                    refreshLayout.run {
                        setEnableLoadMore(true)
                        setEnableRefresh(true)
                    }

                    val checkCount = viewModel.isCheckedMap.value?.size ?: 0
                    val count = viewModel.mNoticesList.value?.size ?: 0
                    msg_top_select_title?.text =
                        String.format(
                            getString(R.string.starry_msg_select_title_num),
                            checkCount.toString() + "",
                            count.toString() + ""
                        )

                    // 检查delBtn是否可以点击
                    isCheckedMap.observe(this@StarryMessageActivity){ it2 ->
                        if (it2.size < 1){
                            Tools.setViewButtonEnable(msg_bottom_bar_del_rl, false, alpha)
                            changeDelBtnDisable(false)
                        }else{
                            Tools.setViewButtonEnable(msg_bottom_bar_del_rl, true)
                            changeDelBtnDisable()
                        }
                    }
                }else{
                    starry_layout_top_bar_sel_rl.visibility = View.INVISIBLE
                    starry_layout_top_bar_rl.visibility = View.VISIBLE
                    msg_bottom_bar_del_outer_rl.visibility = View.GONE
                    refreshLayout.run {
                        setEnableLoadMore(true)
                        setEnableRefresh(true)
                    }
                }
                mAdapter.reflashUI(it)
            })

        }

        viewModel.flagAllRead.observe(this){
            hideProgressDialog()
        }

    }

    private fun changeUIStatus(b: Boolean) {
        // true:有数据，不显示nodata
        if (b) {
            user_more_btn?.visibility = View.VISIBLE

            starry_msg_nodata?.visibility = View.GONE
//            Tools.setViewButtonEnable(user_more_btn, true)
            refreshLayout.run {
                setEnableLoadMore(true)
                setEnableRefresh(true)
            }
        }else {
            // 无数据界面
            user_more_btn?.visibility = View.GONE

            starry_msg_nodata?.visibility = View.VISIBLE
//            Tools.setViewButtonEnable(user_more_btn, false)
            starry_layout_top_bar_sel_rl.visibility = View.INVISIBLE
            starry_layout_top_bar_rl.visibility = View.VISIBLE
            msg_bottom_bar_del_outer_rl.visibility = View.GONE
            refreshLayout.run {
                setEnableLoadMore(false)
                setEnableRefresh(true)
            }
        }
    }

    private fun showConfirmDetailDialog(title: String) {
        CloudCommonPopup.Builder(
            this,
            CloudCommonPopupConstants.COMMON_ONE_BUTTON
        )
            .setTitle(resources.getString(R.string.starry_message_title))
            .setMessage(title)
            .setOnPositiveListener { dialog, _ ->
                dialog?.dismiss()
                // 删除消息，刷新首页消息小红点
                LiveDataBus.get().with(StarryConstants.MEETING_DELETE_MESSAGE_REDPOINT).value = true
            }
            .setOnNegativeListener { dialog, _ -> dialog.dismiss() }
            .create()
            .show()
    }

    private fun registerEvent() {
        user_back_btn?.setOnClickListener(this)
        user_more_btn?.setOnClickListener(this)
        msg_top_select_all?.setOnClickListener(this)
        msg_top_select_cancel?.setOnClickListener(this)
    }

    @RequiresApi(Build.VERSION_CODES.N)
    override fun onClick(v: View) {
        when (v.id) {

            // 返回
            R.id.user_back_btn -> ActivityUtils.finishActivity(this)

            //更多菜单
            R.id.user_more_btn -> {
                popMenu(v)
            }

            // 取消
            R.id.msg_top_select_cancel -> {
                viewModel.changeRecycleMode(false)
                viewModel.isCheckedMap.value?.clear()
                mAdapter.setAllChecked(false)
                msg_top_select_all.text = getString(R.string.starry_msg_select_all)
            }

            // 全选、反选
            R.id.msg_top_select_all -> {
                // selected all
                if (msg_top_select_all.text == getString(R.string.starry_msg_select_all)){
                    msg_top_select_all.text = getString(R.string.starry_msg_unselect_all)
                    mAdapter.setAllChecked(true)
                }else{
                    msg_top_select_all.text = getString(R.string.starry_msg_select_all)
                    mAdapter.setAllChecked(false)
                }
            }

            // 删除
            R.id.msg_bottom_bar_del_rl -> {
                if (starryViewModel.clickNoNetwork()){
                    return
                }
                onClickDeleteMessages()
            }

            else -> {
            }
        }
    }

    private fun onDeleteMessages() {
        viewModel.isCheckedMap.value = mAdapter.getCheckMap()
        val ids = arrayListOf<Int>()
        viewModel.isCheckedMap.value?.forEach { (t, _) ->
            ids.add(t.toInt())
        }
        Log.i("StarryMessageActivity", ".msg_bottom_bar_del_rl.ids=${ids.toString()}")
        launch {
            val message: MessageBean = MessageBean(
                StarryPreferences.getInstance().accountNo,
                ids
            )
            val synJson = Gson().toJson(message)
            val ret = viewModel.deleteNoticesByIds(synJson)
            Log.i("StarryMessageActivity", ".msg_bottom_bar_del_rl.ret=${ret}")
            // 删除消息，刷新首页消息小红点
            LiveDataBus.get().with(StarryConstants.MEETING_DELETE_MESSAGE_REDPOINT).value = true
            if (ret){
                viewModel.getNoticesListRefresh()
                ToastUtils.showLong(getString(R.string.starry_msg_del_message_success))
                viewModel.isCheckedMap.value?.clear()
                Tools.setViewButtonEnable(msg_bottom_bar_del_rl, false, alpha)
                changeDelBtnDisable(false)
            }else{
                ToastUtils.showLong(getString(R.string.starry_msg_del_message_fail))
            }
        }
    }

    private fun changeDelBtnDisable(flag: Boolean=true){
        if (flag){
            msg_btn_del?.setImageResource(R.mipmap.starry_msg_del)
            msg_btn_del_tv?.setTextColor(getColor(R.color.starry_delete_red))
        }else{
            msg_btn_del?.setImageResource(R.mipmap.starry_msg_del_disable)
            msg_btn_del_tv?.setTextColor(getColor(R.color.starry_title_gray))
        }
    }

    private fun popMenu(v: View) {
        val morePopWindow = MorePopWindow(this@StarryMessageActivity)
        user_more_btn?.let {
            morePopWindow.showPopupWindow(it)

            morePopWindow.setMenuReadDisable(viewModel.mUnreadCount.value == 0)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

    // Select
    private fun onMessageSelect(){
        viewModel.changeRecycleMode(true)
    }

    // all read
    private fun onAllRead() {
        viewModel.updateNoticesStatusAllRead()
    }

    // All deleted
    private fun onAllDeleted() {
        StarryCommonPopup.Builder(this)
            .setTitle(getString(R.string.starry_popupwindow_title))
            .setPositiveTitle(getString(R.string.starry_common_dialog_yes))
            .setNegativeTitle(getString(R.string.starry_common_dialog_not))
            .setMessage(getString(R.string.starry_popupwindow_msg_all_del))
            .setOnPositiveListener { dialog, _ ->
                showProgressDialog()
                viewModel.deleteAllNotices()
                dialog?.dismiss()
                // 删除消息，刷新首页消息小红点
                LiveDataBus.get().with(StarryConstants.MEETING_DELETE_MESSAGE_REDPOINT).value = true
            }
            .setOnNegativeListener { dialog, _ -> dialog.dismiss() }
            .create()
            .show()
    }

    private fun onCreateBottomSheetDialog(id: String) {
        //初始化页面
        val layoutInflater = this.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        // 获取屏幕宽高
        val weight = resources.displayMetrics.widthPixels
        val height = resources.displayMetrics.heightPixels
        //初始化该对象
        val mPopWindow = PopBottomMenuWindow(
            layoutInflater,
            weight,
            height
        )
        val list = listOf<String>(
            getString(R.string.starry_top_bar_delete_btn),
        )
        mPopWindow.setDatasList(list,
            "",
            getString(R.string.starry_meeting_exit_admin_menu_cancel))
        mPopWindow.setLastItemIsRed(true)
        mPopWindow.apply {
            bottom_menu_cancel?.setOnClickListener{
                this.dismiss()
            }

            popup_window_dialog_cl?.setOnClickListener{
                this.dismiss()
            }

            mAdapter.setOnItemClickListener(object: PopBottomMenuWindow.MyAdapter.OnClickListener{
                override fun onclick(position: Int, item: String) {
                    onItemClickBottomSheetDialog(position, item, id)
                    <EMAIL>()
                }

            })

            show()
        }
    }

    private fun onItemClickBottomSheetDialog(position: Int, item: String, id: String){
        when(position){
            // delete
            0 -> {
                onClickDeleteOneMessages(id)
            }
        }
    }

    // delete messages
    private fun onClickDeleteMessages() {
        StarryCommonPopup.Builder(this)
            .setTitle(getString(R.string.starry_popupwindow_title))
            .setPositiveTitle(getString(R.string.starry_common_dialog_yes))
            .setNegativeTitle(getString(R.string.starry_common_dialog_not))
            .setMessage(getString(R.string.starry_popupwindow_msg_sel_del))
            .setOnPositiveListener { dialog, _ ->
                onDeleteMessages()
                dialog?.dismiss()

                // 删除消息，刷新首页消息小红点
                LiveDataBus.get().with(StarryConstants.MEETING_DELETE_MESSAGE_REDPOINT).value = true
                // 退出消息选择
                viewModel.changeRecycleMode(false)
                viewModel.isCheckedMap.value?.clear()
                mAdapter.setAllChecked(false)
                msg_top_select_all.text = getString(R.string.starry_msg_select_all)
            }
            .setOnNegativeListener { dialog, _ -> dialog.dismiss() }
            .create()
            .show()
    }

    // delete one messages
    private fun onClickDeleteOneMessages(id: String) {
        StarryCommonPopup.Builder(this)
            .setTitle(getString(R.string.starry_popupwindow_title))
            .setPositiveTitle(getString(R.string.starry_common_dialog_yes))
            .setNegativeTitle(getString(R.string.starry_common_dialog_not))
            .setMessage(getString(R.string.starry_popupwindow_msg_del))
            .setOnPositiveListener { dialog, _ ->
                viewModel.deleteNotices(id)
                dialog?.dismiss()
                // 删除消息，刷新首页消息小红点
                LiveDataBus.get().with(StarryConstants.MEETING_DELETE_MESSAGE_REDPOINT).value = true
            }
            .setOnNegativeListener { dialog, _ -> dialog.dismiss() }
            .create()
            .show()
    }
}