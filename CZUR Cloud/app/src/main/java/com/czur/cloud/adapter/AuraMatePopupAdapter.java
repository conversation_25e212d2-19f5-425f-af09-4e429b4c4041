package com.czur.cloud.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.czur.cloud.R;
import com.czur.cloud.model.AuraMateDeviceModel;
import com.czur.cloud.model.AuraMateNewFileRemind;

import org.jetbrains.annotations.NotNull;

import java.util.List;

import io.realm.Realm;

/**
 * Created by Yz on 2018/3/16
 * Email：<EMAIL>
 */


public class AuraMatePopupAdapter extends RecyclerView.Adapter<ViewHolder> {

    private static final int ITEM_TYPE_NORMA = 0;
    private Context mActivity;
    //当前需要显示的所有的图片数据
    private List<AuraMateDeviceModel> datas;
    private LayoutInflater mInflater;
    private Realm realm;

    /**
     * 构造方法
     */
    public AuraMatePopupAdapter(Context activity, List<AuraMateDeviceModel> datas,Realm realm) {
        this.mActivity = activity;
        this.datas = datas;
        mInflater = LayoutInflater.from(activity);
        this.realm = realm;
    }

    public void refreshData(List<AuraMateDeviceModel> datas) {
        this.datas = datas;
        notifyDataSetChanged();
    }


    @NotNull
    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return new NormalViewHolder(mInflater.inflate(R.layout.item_aura_home_popup, parent, false));
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {
        if (holder instanceof NormalViewHolder) {
            final NormalViewHolder mHolder = (NormalViewHolder) holder;
            mHolder.mItem = datas.get(position);
            mHolder.auraHomePopupName.setText(mHolder.mItem.getDeviceName());
            if (mHolder.mItem.isSelect()) {
                mHolder.auraHomePopupRight.setVisibility(View.VISIBLE);
                mHolder.auraHomePopupName.setTextColor(mActivity.getResources().getColor(R.color.blue_33c5e4));
            } else {
                mHolder.auraHomePopupRight.setVisibility(View.GONE);
                mHolder.auraHomePopupName.setTextColor(mActivity.getResources().getColor(R.color.normal_blue));
            }

            AuraMateNewFileRemind remind = realm.where(AuraMateNewFileRemind.class).equalTo("releationId", mHolder.mItem.getReleationId()).findFirst();
            if (remind != null) {
                if (remind.isHaveRead()) {
                    mHolder.newFileTip.setVisibility(View.INVISIBLE);
                } else {
                    mHolder.newFileTip.setVisibility(View.VISIBLE);
                }
            } else {
                mHolder.newFileTip.setVisibility(View.INVISIBLE);
            }

            mHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    for (int i = 0; i < datas.size(); i++) {
                        if (i == position) {
                            datas.get(i).setSelect(true);
                        } else {
                            datas.get(i).setSelect(false);
                        }
                    }
                    if (onItemClickListener != null) {
                        onItemClickListener.onItemClick(position, mHolder.mItem);
                        if (remind != null && !remind.isHaveRead()) {
                            realm.executeTransaction(new Realm.Transaction() {
                                @Override
                                public void execute(Realm realm) {
                                    remind.setHaveRead(true);
                                }
                            });
                        }
                    }
                    notifyDataSetChanged();
                }
            });
        }
    }

    @Override
    public int getItemViewType(int position) {
        return ITEM_TYPE_NORMA;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return datas.size();
    }

    private static class NormalViewHolder extends ViewHolder {
        public final View mView;
        RelativeLayout auraHomePopupRl;
        TextView auraHomePopupName;
        ImageView auraHomePopupRight;
        AuraMateDeviceModel mItem;
        View newFileTip;

        NormalViewHolder(View itemView) {
            super(itemView);
            mView = itemView;
            auraHomePopupRl = (RelativeLayout) itemView.findViewById(R.id.aura_home_popup_rl);
            auraHomePopupName = (TextView) itemView.findViewById(R.id.aura_home_popup_name);
            auraHomePopupRight = (ImageView) itemView.findViewById(R.id.aura_home_popup_right);
            newFileTip = itemView.findViewById(R.id.new_file_tip);
        }
    }

    private onItemClickListener onItemClickListener;

    public void setOnItemClickListener(onItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface onItemClickListener {
        void onItemClick(int position, AuraMateDeviceModel foldersBean);
    }


}
