package com.czur.cloud.event;

import com.czur.cloud.netty.bean.ReceivedMsgBodyBean;

public class AuraMateOperateEvent extends BaseEvent {

    private String deviceUdid;
    private String dataBegin;

    private ReceivedMsgBodyBean.BodyBean.DataBean.DeviceStatusBean statusBean;

    public AuraMateOperateEvent(EventType eventType, String deviceUdid, ReceivedMsgBodyBean.BodyBean.DataBean.DeviceStatusBean statusBean) {
        super(eventType);
        this.deviceUdid = deviceUdid;
        this.statusBean = statusBean;
    }

    public AuraMateOperateEvent(EventType eventType, String deviceUdid) {
        super(eventType);
        this.deviceUdid = deviceUdid;
    }

    public ReceivedMsgBodyBean.BodyBean.DataBean.DeviceStatusBean getStatusBean() {
        return statusBean;
    }

    public String getDataBegin() {
        return dataBegin;
    }

    public String getDeviceUdid() {
        return deviceUdid;
    }

    @Override
    public boolean match(Object obj) {
        return true;
    }
}
