package com.czur.cloud.entity;

/**
 * Created by Yz on 2018/4/16.
 * Email：<EMAIL>
 * 通过native层 反射调用构造方法传参
 */

public class JniEntity {

    private float[] keyPoints;
    private int pageNum;
    private int recX;
    private int recY;
    private int recWidth;
    private int recHeight;
    private boolean isStable;
    private boolean isNeedFocus;

    private float[] keyCenterPoints;
    private byte[] data;

    JniEntity(float[] keyPoints, boolean isStable,boolean isNeedFocus,int pageNum, int recX, int recY, int recWidth, int recHeight) {
        this.keyPoints = keyPoints;
        this.isStable=isStable;
        this.isNeedFocus = isNeedFocus;
        this.pageNum = pageNum;
        this.recX = recX;
        this.recY = recY;
        this.recWidth = recWidth;
        this.recHeight = recHeight;
    }

    JniEntity(float[] keyCenterPoints, byte[] data) {
        this.keyCenterPoints =keyCenterPoints;
        this.data = data;
    }

    public float[] getKeyPoints() {
        return keyPoints;
    }

    public void setKeyPoints(float[] keyPoints) {
        this.keyPoints = keyPoints;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getRecX() {
        return recX;
    }

    public void setRecX(int recX) {
        this.recX = recX;
    }

    public int getRecY() {
        return recY;
    }

    public void setRecY(int recY) {
        this.recY = recY;
    }

    public int getRecWidth() {
        return recWidth;
    }

    public void setRecWidth(int recWidth) {
        this.recWidth = recWidth;
    }

    public int getRecHeight() {
        return recHeight;
    }

    public void setRecHeight(int recHeight) {
        this.recHeight = recHeight;
    }
    public boolean isStable() {
        return isStable;
    }

    public void setStable(boolean stable) {
        isStable = stable;
    }

    public boolean isNeedFocus(){return isNeedFocus;}



}