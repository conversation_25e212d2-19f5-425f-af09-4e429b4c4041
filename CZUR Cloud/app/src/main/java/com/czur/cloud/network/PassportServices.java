package com.czur.cloud.network;

import androidx.annotation.StringDef;

import com.czur.cloud.model.CountryList;
import com.czur.cloud.model.RegisterModel;
import com.czur.cloud.model.SearchUserModel;
import com.czur.cloud.model.UserSettingModel;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpGet;
import com.czur.cloud.network.core.MiaoHttpHeader;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.network.core.MiaoHttpParam;
import com.czur.cloud.network.core.MiaoHttpPath;
import com.czur.cloud.network.core.MiaoHttpPost;

public interface PassportServices {

    String CN = "zh_CN", TW = "zh_TW", EN = "en";

    @StringDef({CN, TW, EN})
    @interface MailLanguage {
    }
    /** 通过手机号或邮箱注册第三方登录 */
    @MiaoHttpPost("v2/public/tregister/contact")
    void thirdPartyRegister(@MiaoHttpHeader("Channel") String channel, @MiaoHttpHeader("udid") String headUDID,@MiaoHttpHeader("App-Key") String headAppKey,
                        @MiaoHttpParam("code") String code, @MiaoHttpParam("password") String password, @MiaoHttpParam("contact") String contact,  @MiaoHttpParam("thirdAccountId") String thirdAccountId,Class<RegisterModel> clazz, MiaoHttpManager.Callback<RegisterModel> callback);


    /** 绑定第三方账号 */
    @MiaoHttpPost("v2/public/third/bind")
    void thirdPartyBind(@MiaoHttpHeader("Channel") String channel,@MiaoHttpHeader("udid") String headUDID,@MiaoHttpHeader("App-Key") String headAppKey,
                         @MiaoHttpParam("thirdAccountId") String thirdAccountId, @MiaoHttpParam("contact") String contact, @MiaoHttpParam("code") String code, Class<RegisterModel> clazz, MiaoHttpManager.Callback<RegisterModel> callback);
    /** 登录-同步 */
    @MiaoHttpPost("v2/login")
    MiaoHttpEntity<RegisterModel> thirdPartyLoginSync(@MiaoHttpHeader("Channel") String channel, @MiaoHttpHeader("udid") String headUDID,@MiaoHttpHeader("App-Key") String headAppKey,
                                            @MiaoHttpParam("adapter") String platName, @MiaoHttpParam("accessToken") String accessToken, @MiaoHttpParam("openId") String openId, Class<RegisterModel> clazz);
    /** 第三方登录 */
    @MiaoHttpPost("v2/login")
    void thirdPartyLogin(@MiaoHttpHeader("Channel") String channel, @MiaoHttpHeader("udid") String headUDID,@MiaoHttpHeader("App-Key") String headAppKey,
                         @MiaoHttpParam("adapter") String platName, @MiaoHttpParam("accessToken") String accessToken, @MiaoHttpParam("openId") String openId, Class<RegisterModel> clazz, MiaoHttpManager.Callback<RegisterModel> callback);


    /** 通过邮箱确认用户第三方绑定状态 */
    @MiaoHttpPost("v2/public/check/email")
    void confirmThirdPartyByEmail(@MiaoHttpHeader("Channel") String channel, @MiaoHttpHeader("udid") String headUDID,@MiaoHttpHeader("App-Key") String headAppKey,
                                  @MiaoHttpParam("email") String email, @MiaoHttpParam("platName") String platName, @MiaoHttpParam("code") String code, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    /** 通过手机号确认用户第三方绑定状态 */
    @MiaoHttpPost("v2/public/check/mobile")
    void confirmThirdPartyByMobile(@MiaoHttpHeader("Channel") String channel, @MiaoHttpHeader("udid") String headUDID,@MiaoHttpHeader("App-Key") String headAppKey,
                                   @MiaoHttpParam("mobile") String mobile, @MiaoHttpParam("platName") String platName, @MiaoHttpParam("code") String code, Class<String> clazz, MiaoHttpManager.Callback<String> callback);
    /** 获取邮箱验证码 */
    @MiaoHttpPost("v1/public/code/email")
    void mailCode(@MiaoHttpParam("email") String email, @MailLanguage @MiaoHttpParam("locale") String locale, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    /** 获取手机验证码 */
    @MiaoHttpGet("v1/public/code/mobile/{mobile}")
    void mobileCode(@MiaoHttpPath("mobile") String mobile, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    /** 邮箱注册 */
    @MiaoHttpPost("v1/public/register/email")
    void mailRegister(@MiaoHttpHeader("App-Key") String headAppKey,
                      @MiaoHttpHeader("udid") String headUDID,
                      @MiaoHttpHeader("Channel") String channel,
                      @MiaoHttpParam("email") String mobile,
                      @MiaoHttpParam("password") String password,
                      @MiaoHttpParam("code") String code,
                      @MiaoHttpParam("countryCode") String countryCode,
                      Class<RegisterModel> clazz, MiaoHttpManager.Callback<RegisterModel> callback);

    /** 手机注册 */
    @MiaoHttpPost("v1/public/register/mobile")
    void mobileRegister(@MiaoHttpHeader("App-Key") String headAppKey,
                        @MiaoHttpHeader("udid") String headUDID,
                        @MiaoHttpHeader("Channel") String channel,
                        @MiaoHttpParam("mobile") String mobile,
                        @MiaoHttpParam("password") String password,
                        @MiaoHttpParam("code") String code,
                        @MiaoHttpParam("areaCode") String areaCode,
                        @MiaoHttpParam("countryCode") String countryCode,
                        Class<RegisterModel> clazz, MiaoHttpManager.Callback<RegisterModel> callback);

    /** 登录 */
    @MiaoHttpPost("v1/login")
    void login(
            @MiaoHttpHeader("App-Key") String headAppKey,
            @MiaoHttpHeader("udid") String headUDID,
            @MiaoHttpHeader("Channel") String channel,
            @MiaoHttpParam("username") String username,
            @MiaoHttpParam("password") String password,
            Class<RegisterModel> clazz,
            MiaoHttpManager.Callback<RegisterModel> callback);

    /** 登录-同步 */
    @MiaoHttpPost("v1/login")
    MiaoHttpEntity<RegisterModel> loginSync(
            @MiaoHttpHeader("App-Key") String headAppKey,
            @MiaoHttpHeader("udid") String headUDID,
            @MiaoHttpHeader("Channel") String channel,
            @MiaoHttpParam("username") String username,
            @MiaoHttpParam("password") String password,
            Class<RegisterModel> clazz);

    /** 注销账户之发送验证码 */
    @MiaoHttpPost("v1/account/cancellation/sendCode")
    void cancellationSendCode(
            @MiaoHttpParam("u_id") String u_id,
            @MiaoHttpParam("mobileOrEmail") String mobileOrEmail,
            @MiaoHttpParam("locale") String locale,
            Class<String> clazz,
            MiaoHttpManager.Callback<String> callback);

    /** 注销账户之提交注销 */
    @MiaoHttpPost("v1/account/cancellation/submit")
    void cancellationSubmit(
            @MiaoHttpHeader("Channel") String channel,
            @MiaoHttpParam("u_id") String u_id,
            @MiaoHttpParam("code") String code,
            Class<String> clazz,
            MiaoHttpManager.Callback<String> callback);

    /** 找回密码 获取code */
    @MiaoHttpPost("v1/public/find/pwd/code")
    void findPwdSendCode(@MiaoHttpHeader("App-Key") String headAppKey, @MiaoHttpHeader("udid") String headUDID, @MiaoHttpHeader("Channel") String channel,
                         @MiaoHttpParam("username") String userName, @MiaoHttpParam("locale") String locale, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    /** 找回密码 下一步*/
    @MiaoHttpPost("v1/public/find/pwd/check")
    void findPwdNext(@MiaoHttpHeader("App-Key") String headAppKey, @MiaoHttpHeader("udid") String headUDID, @MiaoHttpHeader("Channel") String channel,
                     @MiaoHttpParam("username") String userName, @MiaoHttpParam("code") String code, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    /** 找回密码 再次输入密码*/
    @MiaoHttpPost("v1/public/find/reset/{resetKey}")
    void againPwd(@MiaoHttpHeader("App-Key") String headAppKey, @MiaoHttpHeader("udid") String headUDID, @MiaoHttpHeader("Channel") String channel,
                  @MiaoHttpPath("resetKey") String resetKey, @MiaoHttpParam("password") String password, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    /** 搜索用户 */
    @MiaoHttpPost("v1/public/search/user")
    void searchUser(@MiaoHttpHeader("Channel") String channel, @MiaoHttpParam("keyword") String keyword,
                    Class<SearchUserModel> searchUserEntityClass, MiaoHttpManager.Callback<SearchUserModel> callback);

    /** (未绑定情况下)邮箱修改 */
    @MiaoHttpPost("v1/account/email/bind")
    void notBindUpdateMail(@MiaoHttpHeader("udid") String headUDID, @MiaoHttpHeader("App-Key") String headAppKey, @MiaoHttpHeader("Channel") String channel, @MiaoHttpHeader("U-ID") String headUid, @MiaoHttpHeader("T-ID") String tId,
                           @MiaoHttpParam("u_id") String uid, @MiaoHttpParam("email") String mobile, @MiaoHttpParam("code") String code, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    /** (绑定情况下)邮箱修改_第一次请求*/
    @MiaoHttpPost("v1/account/email/validate")
    void updateMailFirst(@MiaoHttpHeader("udid") String headUDID, @MiaoHttpHeader("App-Key") String headAppKey, @MiaoHttpHeader("Channel") String channel, @MiaoHttpHeader("U-ID") String headUid, @MiaoHttpHeader("T-ID") String tId,
                         @MiaoHttpParam("u_id") String uid, @MiaoHttpParam("code") String code, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    /** (绑定情况下)邮箱修改_第二次请求*/
    @MiaoHttpPost("v1/account/email/change")
    void updateMailSecond(@MiaoHttpHeader("udid") String headUDID, @MiaoHttpHeader("App-Key") String headAppKey, @MiaoHttpHeader("Channel") String channel, @MiaoHttpHeader("U-ID") String headUid, @MiaoHttpHeader("T-ID") String tId,
                          @MiaoHttpParam("u_id") String uid, @MiaoHttpParam("email") String mobile, @MiaoHttpParam("u_key") String uKey, @MiaoHttpParam("code") String code,
                          Class<String> clazz, MiaoHttpManager.Callback<String> callback);


    /** (未绑定情况下) 手机修改*/
    @MiaoHttpPost("v1/account/mobile/bind")
    void notBindUpdateMobile(@MiaoHttpHeader("udid") String headUDID, @MiaoHttpHeader("App-Key") String headAppKey, @MiaoHttpHeader("Channel") String channel, @MiaoHttpHeader("U-ID") String headUid, @MiaoHttpHeader("T-ID") String tId,
                             @MiaoHttpParam("u_id") String uid, @MiaoHttpParam("mobile") String mobile, @MiaoHttpParam("code") String code, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    /** (绑定情况下) 手机修改_第一次请求*/
    @MiaoHttpPost("v1/account/mobile/validate")
    void updateMobileFirst(@MiaoHttpHeader("udid") String headUDID, @MiaoHttpHeader("App-Key") String headAppKey, @MiaoHttpHeader("Channel") String channel, @MiaoHttpHeader("U-ID") String headUid, @MiaoHttpHeader("T-ID") String tId,
                           @MiaoHttpParam("u_id") String uid, @MiaoHttpParam("code") String code, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    /** (绑定情况下) 手机修改_第二次请求*/
    @MiaoHttpPost("v1/account/mobile/change")
    void updateMobileSecond(@MiaoHttpHeader("udid") String headUDID, @MiaoHttpHeader("App-Key") String headAppKey, @MiaoHttpHeader("Channel") String channel, @MiaoHttpHeader("U-ID") String headUid, @MiaoHttpHeader("T-ID") String tId,
                            @MiaoHttpParam("u_id") String uid, @MiaoHttpParam("mobile") String mobile, @MiaoHttpParam("u_key") String uKey, @MiaoHttpParam("code") String code, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    /** 修改昵称*/
    @MiaoHttpPost("v1/account/name/update")
    void updateNickname(@MiaoHttpHeader("udid") String headUDID, @MiaoHttpHeader("App-Key") String headAppKey, @MiaoHttpHeader("Channel") String channel, @MiaoHttpHeader("U-ID") String headUid, @MiaoHttpHeader("T-ID") String tId,
                        @MiaoHttpParam("u_id") String uid, @MiaoHttpParam("name") String name, Class<UserSettingModel> clazz, MiaoHttpManager.Callback<UserSettingModel> callback);

    /** 修改用户密码*/
    @MiaoHttpPost("v1/account/pwd/reset")
    void updatePwd(@MiaoHttpHeader("udid") String headUDID, @MiaoHttpHeader("App-Key") String headAppKey, @MiaoHttpHeader("Channel") String channel, @MiaoHttpHeader("U-ID") String headUid, @MiaoHttpHeader("T-ID") String tId,
                   @MiaoHttpParam("u_id") String uid, @MiaoHttpParam("o_pwd") String oPwd, @MiaoHttpParam("n_pwd") String nPwd, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    // 设置账号国家区域
    @MiaoHttpPost("v1/account/country/update")
    void updateCountry(@MiaoHttpHeader("udid") String headUDID,
                       @MiaoHttpHeader("App-Key") String headAppKey,
                       @MiaoHttpHeader("Channel") String channel,
                       @MiaoHttpHeader("U-ID") String headUid,
                       @MiaoHttpHeader("T-ID") String tId,
                       @MiaoHttpParam("u_id") String uid,
                       @MiaoHttpParam("areaCode") String areaCode,
                       @MiaoHttpParam("countryCode") String countryCode,
                       Class<String> clazz,
                       MiaoHttpManager.Callback<String> callback);


    // 获取国家区域列表
    @MiaoHttpGet("v1/public/country/code")
    void countryCode(@MiaoHttpParam("type") String type,
                     Class<CountryList> clazz,
                     MiaoHttpManager.Callback<CountryList> callback);

}
