package com.czur.cloud.ui.starry.viewmodel

import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.czur.cloud.preferences.StarryPreferences
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.meeting.bean.UserStatus
import com.czur.cloud.ui.starry.meeting.common.MeetingCMD
import com.czur.cloud.ui.starry.meeting.common.MsgProcessor
import com.czur.cloud.ui.starry.meeting.model.MeetingModel
import com.czur.cloud.ui.starry.model.RecentlyModel
import com.czur.cloud.ui.starry.model.StarryCallRecordModel
import com.czur.cloud.ui.starry.model.StarryDoingMeetingModel

class RecentlyViewModel: ViewModel() {

    var reloadCounts = 0 // 0 没有重启 1重启过 栈里没有indexActivity说明是不正常状态,重启此activity(针对在meetingmainactivity修改过权限后,viewmodel中的协程被取消的情况)可以再次修改
    private val DEFAULT_PAGE_NUM = 1
    // 最近会议列表
    var listRecently = MutableLiveData<List<StarryCallRecordModel>>()
    // 最近会议总数
    var recentlyTotal = 0

    // 正在进行的会议
    var listDoingMeeting = MutableLiveData<List<StarryDoingMeetingModel>>()

    private var currentPageNum = DEFAULT_PAGE_NUM
    private val allListRecently = ArrayList<StarryCallRecordModel>()

    // 本次加载更多的数量，比对是否没有更多了
    var mMoreCount = MutableLiveData<Int>(StarryConstants.STARRY_CALL_PAGE_SIZE)

    // 最近是否显示红点
    var isMissedCallRecordsFlag = MutableLiveData<Boolean>(false)
    var missedFlag = false

    var otherMeeting = false  //  其他端是否有在进行会议？

    // 获取服务器数据 查询最近通话记录列表
    private fun getCallRecords(index: Int) {
        launch {
            val num = currentPageNum.toString()
            val size = StarryConstants.STARRY_CALL_PAGE_SIZE.toString()

            try {
                var isRedPoint = false
                val modelList = RecentlyModel.getCallRecords(num, size)
                // 进行正在进行的会议检查
                val callList = modelList.callRecords
                recentlyTotal = modelList.total ?: 0
                mMoreCount.value = (callList.size ?: 0)
                callList.forEach {  callModel ->
                    if (callModel.meetingStatus == 2) {
                        callModel.meetingStatus = 3
                    }

                    listDoingMeeting.value?.forEach {
                        if (callModel.meetingId == it.meetingId) {
                            callModel.meetingStatus = 2
                            callModel.status = it.status.toString()
                            // 只有返回的记录状态是joined，才需要记录pc为true
                            if (it.status.toString() == UserStatus.STATUS_JOINED.toString()) {
                                callModel.isPCEnter = it.isPCEnter
                            }
                            // 进行中，可参加
                            val userStatus = callModel.status
                            if (userStatus == UserStatus.STATUS_CALLING.toString() ||
                                userStatus == UserStatus.STATUS_TIMEOUT.toString() ||
                                userStatus == UserStatus.STATUS_JOINED.toString() ||
                                userStatus == UserStatus.STATUS_OFFLINE.toString() ||
                                userStatus == UserStatus.STATUS_IOS_HOLD.toString() ||
                                userStatus == UserStatus.STATUS_HOLD_ON.toString()) {
                                isRedPoint = true
                            }
                            return@forEach
                        }
                    }
                }
                if (index == 0){
                    allListRecently.clear()
                }
                allListRecently.addAll(callList)

                // 其他端是否有在进行的会议？
                val isPCCount = allListRecently.count { it1 ->
                    it1.isPCEnter
                }
                otherMeeting = isPCCount > 0
                MeetingModel.isPCEnter = isPCCount>0
                MeetingModel.isPCEnterMeetingCode = ""
                if (otherMeeting) {
                    MeetingModel.isPCEnterMeetingCode = allListRecently.first{
                        it.isPCEnter
                    }.meetingCode
                }

                listRecently.postValue(allListRecently)

                missedFlag = missedFlag || isRedPoint
                isMissedCallRecordsFlag.value = missedFlag

            }catch (e: Exception){
                Log.e("RecentlyViewModel",e.toString())
                currentPageNum --
                if (currentPageNum < DEFAULT_PAGE_NUM)
                    currentPageNum = DEFAULT_PAGE_NUM
            }

        }
    }

    // 加载更多
    fun getCallRecordsMore() {
        currentPageNum ++
        getCallRecords(1)
    }

    // 重新加载
    fun getCallRecordsRefresh(){
        Log.i("RecentlyViewModel","getCallRecordsRefresh")
        currentPageNum = DEFAULT_PAGE_NUM
        allListRecently.clear()
        missedFlag = false
        getCallRecords(0)

    }

    fun getDingMeetingAndCallRecords(){
        launch{
            getDoingMeeting()
//            getCallRecordsRefresh()
        }
    }

    // 长连接,获取正在进行会议
    private fun getDoingMeeting(){
        val meetNo = StarryPreferences.getInstance()?.accountNo ?: "0"
        val cmd = MeetingCMD.CHECK_MEETING.cmd

        MsgProcessor.commonMeetCMD(cmd, meetNo)

    }

    // 清空最近会议
    fun deleteAllRecords() {
        launch {
            try {
                val ret = RecentlyModel.deleteAllRecords()

                if (ret >= 0){
                    getCallRecordsRefresh()
                }

            }catch (e: Exception){
                Log.e("RecentlyViewModel",e.toString())
            }

        }
    }

}