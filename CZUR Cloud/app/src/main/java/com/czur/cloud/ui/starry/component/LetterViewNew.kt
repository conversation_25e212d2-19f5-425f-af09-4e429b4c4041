package com.czur.cloud.ui.starry.component

import android.content.Context
import android.util.AttributeSet
import android.view.Gravity
import android.widget.LinearLayout
import android.widget.TextView
import com.blankj.utilcode.util.SizeUtils
import com.czur.cloud.R
import com.itextpdf.text.Utilities
import java.util.LinkedHashMap

class LetterViewNew(private val mContext: Context, attrs: AttributeSet?) : LinearLayout(
    mContext, attrs
) {
    private var mListener: CharacterClickListener? = null

    private lateinit var mListDatas: List<String>
    private var isCheckedMap = LinkedHashMap<String, TextView>()

    init {
        orientation = VERTICAL
    }

    fun initViewNew(charList: List<String>) {
        mListDatas = charList
        removeAllViews()
        charList.forEach {
            val tv = buildTextLayout(it)
            addView(tv)
            isCheckedMap[it]=tv
        }

        if (charList.isNotEmpty()) {
            setSelectedChar(charList.first())
        }
    }

    fun setSelectedChar(char: String){
//        setAllCheckedCancel()
//        val tv = isCheckedMap[char]
//        // 再设置当前选中的背景
//        tv?.background = mContext.getDrawable(R.drawable.circle_with_blue)
//        tv?.setTextColor(mContext.getColor(R.color.white))
        setAllCheckedCancel(char)
    }

    private fun setAllCheckedCancel(oldChar: String){
        isCheckedMap.forEach{
            if (it.value.text == oldChar){
                it.value.background = mContext.getDrawable(R.drawable.circle_with_blue)
                it.value.setTextColor(mContext.getColor(R.color.white))
            }else {
                it.value.background = mContext.getDrawable(R.color.transparent)
                it.value.setTextColor(mContext.getColor(R.color.starry_text_title_color_black))
            }
        }
    }

    private fun setAllCheckedCancel(){
        isCheckedMap.forEach{
            it.value.background = mContext.getDrawable(R.color.transparent)
            it.value.setTextColor(mContext.getColor(R.color.starry_text_title_color_black))
        }
    }

    private fun initView() {
        var i = 'A'
        while (i <= 'Z') {
            val character = i.toString() + ""
            val tv = buildTextLayout(character)
            addView(tv)
            i++
        }
        addView(buildTextLayout("#"))
    }

    private fun buildTextLayout(character: String): TextView {
        val layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT, 1f)
        val tv = TextView(mContext)
        tv.layoutParams = layoutParams
        val ll = tv.layoutParams as LinearLayout.LayoutParams
        ll.height = SizeUtils.dp2px(20f)
        ll.width = SizeUtils.dp2px(20f)
        tv.layoutParams = ll
        tv.gravity = Gravity.CENTER
        tv.isClickable = true
        tv.text = character
        tv.textSize = 12f
        tv.background = mContext.getDrawable(R.color.transparent)
        tv.setTextColor(mContext.getColor(R.color.starry_text_title_color_black))

        tv.setOnClickListener {
            if (mListener != null) {
                // 先清空背景
                setAllCheckedCancel(tv.text.toString())
//                setAllCheckedCancel()
//                // 再设置当前选中的背景
//                tv.background = mContext.getDrawable(R.drawable.circle_with_blue)
//                tv.setTextColor(mContext.getColor(R.color.white))

                mListener?.clickCharacter(character)
            }
        }
        return tv
    }

    fun setCharacterListener(listener: CharacterClickListener?) {
        mListener = listener
    }

    interface CharacterClickListener {
        fun clickCharacter(character: String?)
        fun clickArrow()
    }

}