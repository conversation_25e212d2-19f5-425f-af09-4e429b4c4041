package com.czur.cloud.ui.eshare

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.provider.Settings
import android.text.Editable
import android.text.SpannableString
import android.text.Spanned
import android.text.SpannedString
import android.text.TextUtils
import android.text.TextWatcher
import android.text.style.AbsoluteSizeSpan
import android.util.Log
import android.view.KeyEvent
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.TextView
import androidx.lifecycle.ViewModelProvider
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.NetworkUtils
import com.blankj.utilcode.util.PermissionUtils
import com.blankj.utilcode.util.PermissionUtils.FullCallback
import com.blankj.utilcode.util.PermissionUtils.isGranted
import com.blankj.utilcode.util.PermissionUtils.permission
import com.blankj.utilcode.util.ToastUtils
import com.czur.cloud.BuildConfig
import com.czur.cloud.R
import com.czur.cloud.preferences.UserPreferences
import com.czur.cloud.ui.account.LoginActivity
import com.czur.cloud.ui.base.CzurCloudApplication
import com.czur.cloud.ui.eshare.engine.HostHearBeat
import com.czur.cloud.ui.eshare.utils.NetConnectListenerUtil
import com.czur.cloud.ui.eshare.viewmodel.EShareViewModel
import com.czur.cloud.ui.starry.activity.StarryActivity
import com.czur.cloud.ui.starry.base.StarryBaseActivity
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.meeting.baselib.utils.addNoMinusFilter
import com.czur.cloud.ui.starry.meeting.baselib.utils.dp2px
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.meeting.dialog.MyInputDialog
import com.czur.cloud.ui.starry.utils.RomUtils
import com.czur.cloud.ui.starry.utils.Tools
import com.czur.cloud.ui.starry.utils.singleClick
import com.czur.cloud.util.PermissionUtil
import com.czur.czurutils.log.logE
import com.czur.czurutils.log.logI
import com.eshare.api.EShareAPI
import com.eshare.api.IDevice
import com.eshare.api.bean.Device
import com.eshare.api.callback.ConnectDeviceCallback
import com.eshare.api.callback.FindDeviceCallback
import com.eshare.api.utils.EShareException
import kotlinx.android.synthetic.main.eshare_activity_find_device.eshare_connect
import kotlinx.android.synthetic.main.eshare_activity_find_device.eshare_pincode
import kotlinx.android.synthetic.main.eshare_activity_find_device.eshare_scaning
import kotlinx.android.synthetic.main.eshare_activity_find_device.eshare_scaning_ll
import kotlinx.android.synthetic.main.eshare_activity_find_device.lv_devices_list
import kotlinx.android.synthetic.main.eshare_activity_find_device.refresh_layout
import kotlinx.android.synthetic.main.eshare_layout_top_bar.scan_btn
import kotlinx.android.synthetic.main.eshare_layout_top_bar.user_back_btn
import kotlinx.android.synthetic.main.eshare_layout_top_bar.user_more_btn
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import java.util.Timer
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import kotlin.concurrent.schedule

private var lastClickTime = 0L
private const val FAST_CLICK_DELAY_TIME = 800 // 快速点击间隔

class FindDeviceActivity : StarryBaseActivity(), NetConnectListenerUtil.NetStateListener {

    companion object {
        private const val MSGCODE_INPUTCODE = 10
        private const val MSGCODE_INPUT_ALERT = 20    // 提示Toast:请输入正确投屏码
        private const val MSGCODE_DISCONNECT = 30
        private const val MSGCODE_INPUT_ERROR = 40    // 提示Toast:输入错误或未在同一网络
        private const val MSGCODE_INPUT_ERROR1 = 41    // 提示Toast:请输入正确投屏码
        private const val MSGCODE_RECONNECT = 50

        // 判断拒绝camera权限的阈值
        private const val CAMERA_REFUSE_TIME_THRESHOLD = 500
    }

    private val mDeviceManager: IDevice by lazy {
        EShareAPI.init(this).device()
    }

    private val mExecutorService: ExecutorService by lazy {
        Executors.newSingleThreadExecutor()
    }

    private val viewModel by lazy {
        (applicationContext as CzurCloudApplication).getEshareViewModel1()
//        ViewModelProvider(this)[EShareViewModel::class.java]
    }

    private var deviceName = "eshare_czur"
    private var currentDevice: Device? = null
    private var currentPinCode = ""

    private val deviceAdapter: DeviceAdapter by lazy {
        DeviceAdapter(this)
    }

    private var contactName: String = ""
    private var contactNameOld: String = ""

    private var pwdDialog: MyInputDialog? = null
    private var selDevice: Device? = null

    private var enterType: String = ""
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.eshare_common_bg)
        BarUtils.setStatusBarLightMode(this, false)
        setContentView(R.layout.eshare_activity_find_device)
        if (!Settings.canDrawOverlays(this)
            || !PermissionUtils.isGranted(Manifest.permission.RECORD_AUDIO)
        ) {// 切出去更改权限,返回来的情况,不做处理,直接返回首页
            finish()
        }
        initView()
        initData()

        if (!NetworkUtils.isConnected()) {
            // 没联网的时候从这里刷新
            // 联网的时候, 从onNetChange里面刷新
            refresh_layout.autoRefresh()
        }

        initNetListener()

    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        logI("FindDeviceActivity.连接失败, 刷新页面")
        selDevice?.let {
            // 消去连接失败的那个设备
            deviceAdapter.removeDevice(it)
        }
        dismissInputDialog()
        findDevices()
    }

    private fun initNetListener() {
        NetConnectListenerUtil.init()
        NetConnectListenerUtil.addNetListener(this@FindDeviceActivity)
    }

    private fun initView() {
        logI("FindDeviceActivity.initView.viewModel.inputCode=${viewModel.getPinCode()}")

        user_more_btn.visibility =
            if (viewModel.esharePreferences.moreBtnVisible) View.VISIBLE else View.GONE
        user_back_btn.visibility =
            if (viewModel.esharePreferences.backBtnVisible) View.VISIBLE else View.GONE
        scan_btn.visibility = View.VISIBLE
        user_back_btn?.singleClick {
            logI("user_back_btn.singleClick")
//            finish()
            //判断栈内没有activity了,说明应该跳转到loginactivity了
            checkAccountState()

            ActivityUtils.finishActivity(this)
        }

        user_more_btn?.singleClick {
            logI("user_more_btn.singleClick")
            ActivityUtils.startActivity(EShareMenuActivity::class.java)
        }

        changeUI(0)

        lv_devices_list?.adapter = deviceAdapter
        lv_devices_list?.dividerHeight = dp2px(this, 10.0f)
        lv_devices_list?.setOnItemClickListener { parent, view, position, id ->
            if (System.currentTimeMillis() - lastClickTime < FAST_CLICK_DELAY_TIME) {
                return@setOnItemClickListener
            }
            showProgressDialogEShare()
            lastClickTime = System.currentTimeMillis()
            logI("lv_devices_list.setOnItemClickListener.position=${position}")

            val device = deviceAdapter.getItem(position)
            eshare_pincode?.clearFocus()
            connectDevice(device)
            hideProgressDialogESheare()
        }

        //下拉刷新
        refresh_layout?.apply {
            setEnableOverScrollDrag(false)
            setEnableOverScrollBounce(false)
            setEnableRefresh(true)
            setEnableNestedScroll(false)
        }?.setOnRefreshListener {
//            changeUI(0)
            findDevices()
            it.finishRefresh(true)
        }

        eshare_connect?.singleClick {

            logI("eshare_connect.singleClick")
            //pinCode 连接
            val pincode = eshare_pincode?.text.toString()
            if (!TextUtils.isEmpty(pincode)) {
                // 收起软键盘
                val manager: InputMethodManager =
                    this.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                manager.hideSoftInputFromWindow(
                    eshare_connect?.windowToken,
                    InputMethodManager.HIDE_NOT_ALWAYS
                )

                if (pincode.isEmpty() || pincode.isBlank()) {
                    ToastUtils.showLong(R.string.eshare_input_address)
                    return@singleClick
                }
                if (pincode.length < 8) {
                    ToastUtils.showLong(R.string.eshare_input_error)
                    return@singleClick
                }

                if (pincode.length > 8 && pincode.indexOf(".") > 0) {
                    Tools.setViewButtonEnable(eshare_connect, false)
                    showProgressDialogEShare()
                    connectDeviceByAddress(pincode)
                } else if (pincode.length == 8) {
                    Tools.setViewButtonEnable(eshare_connect, false)
                    showProgressDialogEShare()
                    pincodeConnectDevice(pincode)
                } else {
                    ToastUtils.showLong(R.string.eshare_input_error)
                }

            }
        }

        eshare_scaning_ll?.singleClick {
            logI("eshare_scaning_ll.singleClick")
            changeUI(0)
            mDeviceManager.stopFindDevice()
            findDevices()
        }

        Tools.setViewButtonEnable(eshare_connect, contactName.isNotEmpty())
        eshare_pincode?.setText(viewModel.getPinCode())
        eshare_pincode?.setOnEditorActionListener(object : TextView.OnEditorActionListener {
            override fun onEditorAction(v: TextView?, actionId: Int, event: KeyEvent?): Boolean {
                if (actionId == EditorInfo.IME_ACTION_GO) {
                    eshare_connect?.performClick()
                    return true
                }
                return false
            }

        })
        eshare_pincode?.addTextChangedListener(object : TextWatcher {
            private var temp: CharSequence? = null
            private var selectionStart = 0
            private var selectionEnd = 0
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                temp = s
            }

            override fun afterTextChanged(s: Editable) {
                selectionStart = eshare_pincode?.selectionStart ?: 0
                selectionEnd = eshare_pincode?.selectionEnd ?: 0
                val str = temp.toString().replace(" ", "")
                if (!TextUtils.isEmpty(str)) {


                    val l = Tools.getTextLength(str)
                    if (l > StarryConstants.MAX_ESHARE_CODE_LENGTH) {
                        s.delete(selectionStart - 1, selectionEnd)
                        val tempSelection = selectionEnd
                        eshare_pincode?.text = s
                        eshare_pincode?.setSelection(tempSelection)
                    }
                    var sr: String = s.toString()
                    if (sr != sr.toUpperCase() || sr.contains(" ")) {
                        sr = sr.replace(" ", "")
                        sr = sr.toUpperCase()
                        eshare_pincode.setText(sr)
                        eshare_pincode?.setSelection(eshare_pincode.text.length)
                    }

                    contactName = sr
                } else {
                    contactName = contactNameOld
                }
                Tools.setViewButtonEnable(eshare_connect, contactName.isNotEmpty())

            }
        })
        eshare_pincode?.addNoMinusFilter()

        scan_btn?.singleClick {
            logI("eshare_scan.singleClick")
            checkCameraPermissionAndOpen()
        }

        if (BuildConfig.IS_OVERSEAS) {
            val ss = SpannableString(getString(R.string.eshare_input_address)) //定义hint的值
            val ass = AbsoluteSizeSpan(12, true) //设置字体大小 true表示单位是sp
            ss.setSpan(ass, 0, ss.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            eshare_pincode?.hint = SpannedString(ss)
        }

        // 从widget进入的,需要判断一下type类型,然后进行不同的操作
        enterType = intent.getStringExtra(StarryConstants.ESHARE_EMPTY_TYPE)
            ?: StarryConstants.ESHARE_EMPTY_TYPE_NORMAL
        logI("FindDeviceActivity.type=${enterType}")
        when (enterType) {
            StarryConstants.ESHARE_EMPTY_TYPE_NORMAL -> {
                //
            }

            StarryConstants.ESHARE_EMPTY_TYPE_FIND -> {
                //
            }

            StarryConstants.ESHARE_EMPTY_TYPE_INPUT -> {
                eshare_pincode?.apply {
                    isFocusable = true
                    isFocusableInTouchMode = true
                    requestFocus()
                    // 弹出软键盘
                    window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
                }
            }

            StarryConstants.ESHARE_EMPTY_TYPE_SCAN -> {
                checkCameraPermissionAndOpen()
            }
        }
    }

    private fun checkCameraPermissionAndOpen() {
        val permissions = arrayOf(
            Manifest.permission.CAMERA,
            Manifest.permission.VIBRATE
        )

        val flag = isGranted(*permissions);
        if (flag) {
            openCamera()
        } else {
            PermissionUtil.checkPermissionWithDialog(this,
                getString(R.string.starry_popupwindow_title),
                getString(R.string.czur_permission_camera,getString(R.string.shortcut_scan)),
                getString(R.string.starry_go_open_permission),
                getString(R.string.starry_background_start_msg_cancel),
                clickListener = View.OnClickListener {
                    if (it != null) { //点击去设置
                        openCamera();
                    } else { //点击取消

                    }
                })

        }
    }

    fun openCamera() {
        val isRefuseSecondPermission = booleanArrayOf(false) //当时点击了永久拒绝
        val requestTime = System.currentTimeMillis()
        val requestPermissionClickTime = System.currentTimeMillis()
        permission(
            Manifest.permission.CAMERA,
            Manifest.permission.VIBRATE
        )
            .rationale { activity, shouldRequest ->
//                showLongMessage(R.string.denied_camera)
                shouldRequest.again(true)
                isRefuseSecondPermission[0] = true
            }
            .callback(object : FullCallback {
                override fun onGranted(permissionsGranted: List<String>) {
                    if (permissionsGranted.size == 2) {
                        val intent = Intent(this@FindDeviceActivity, EShareScanActivity::class.java)
                        startActivityForResult(intent, 30)
                    }
                }

                override fun onDenied(
                    permissionsDeniedForever: List<String>,
                    permissionsDenied: List<String>
                ) {
//                    if (Manifest.permission.CAMERA in permissionsDeniedForever) {
//                        val takeTime = System.currentTimeMillis() - requestTime
//                        // 要求用户手点的拒绝, 不弹出Toast
//                        // 只有系统自己发的拒绝, 才会弹出Toast
//                        // 但是框架没有提供相关api, 所以使用时间来进行区分用户手点, 和系统提示
//                        if (takeTime < CAMERA_REFUSE_TIME_THRESHOLD) {
//                            showMessage(R.string.can_not_open_camera)
//                        }

                    //权限拒绝
                    if (permissionsDeniedForever.isNotEmpty()) { //永久拒绝
                        if (isRefuseSecondPermission[0]) {
                            //当时点击永久拒绝的时候不做处理
                            showMessage(R.string.can_not_open_camera)
                        } else {
                            if (System.currentTimeMillis() - requestPermissionClickTime < 500) { //500ms之内 主观认为是系统返回,而非用户点击
                                RomUtils.PermissionPageManagement.goToSetting(ActivityUtils.getTopActivity())
                            }
                        }
                    }

                    LogUtils.i(permissionsDeniedForever, permissionsDenied)
                }
            })
//            .theme { activity -> ScreenUtils.setFullScreen(activity) }
            .request()
    }

    private fun initData() {
        launch {
            while (isActive) {
                delay(10 * 1000L)
                // 每隔10秒 检测一次是否有需要移除的设备
                deviceAdapter.clearTimeOutDevices()
            }
        }
    }

    private fun findDevices() {
        launch {
            mDeviceManager.clearDevices()
            mDeviceManager.stopFindDevice()
            delay(1000)
            deviceAdapter.clearAllDevices()// 必须要在stopFindDevice清空列表
            //找设备
            mDeviceManager.findDevices(object : FindDeviceCallback {
                override fun onSuccess(list: List<Device>) {
                    if (list.isEmpty()) {
                        changeUI(0)
                        deviceAdapter.clearAllDevices()
                    } else {
                        changeUI(1)
                        deviceAdapter.setDevices(list)
                    }
                }

                override fun onError(e: EShareException) {
                    changeUI(0)
//                changeUI(2)
                    logE("FindDeviceActivity.findDevices.onError.e=${e.message}")
                }
            })

        }

    }

    // 修改界面的布局 0：显示scaning； 1:显示列表； 2：显示未搜索到
    private fun changeUI(flag: Int) {
        when (flag) {
            0 -> {
                eshare_scaning_ll?.visibility = View.VISIBLE
                lv_devices_list?.visibility = View.GONE
                eshare_scaning?.text = getString(R.string.eshare_scaning)

                eshare_scaning_ll?.isClickable = false
                eshare_scaning_ll?.isEnabled = false
            }

            1 -> {
                eshare_scaning_ll?.visibility = View.GONE
                lv_devices_list?.visibility = View.VISIBLE
            }

            2 -> {
                eshare_scaning_ll?.visibility = View.VISIBLE
                lv_devices_list?.visibility = View.GONE
                eshare_scaning?.text = getString(R.string.eshare_scaning_msg2)

                eshare_scaning_ll?.isClickable = true
                eshare_scaning_ll?.isEnabled = true
            }
        }

    }

    // 连接设备
    private fun connectDevice(device: Device?) {
        mExecutorService.execute {
            mDeviceManager.connectDevice(device,
                null, object : ConnectDeviceCallback {
                    override fun onSuccess(succDevice: Device) {
                        deviceName = succDevice?.name ?: ""
                        currentDevice = succDevice
                        launch(Dispatchers.IO) {
                            HostHearBeat.get(mDeviceManager).startHearBeatThread()
                        }
                        // 需要进行pincode判断及验证
                        checkPinCode(device)
//                        // 无视密码,直连投屏
//                        enterEShareMain()

                    }

                    override fun onError(e: EShareException) {
                        logE("FindDeviceActivity.connectDevice.onError.e=${e.message}")
                        ToastUtils.showLong(R.string.eshare_connect_fail)
                        hideProgressDialogESheare()

                        deviceAdapter.removeDevice(device)
                        // 延时重新连接
                        handler.sendEmptyMessage(50)
                    }
                })
        }
    }

    private fun pincodeConnectDevice(pincode: String) {
        mExecutorService.execute {
            mDeviceManager.connectDeviceWithPin(pincode, "", object : ConnectDeviceCallback {
                override fun onSuccess(device: Device) {
                    logI("FindDeviceActivity.connectDeviceWithPin.onSuccess.device=${device}")
                    deviceName = device?.name ?: ""
                    currentDevice = device
                    Thread {
                        try {
                            //Code here
                            val authPwd = mDeviceManager.authPassword(pincode.uppercase())
                            logI("EShareActivity.pincode=${pincode.uppercase()},authPwd=${authPwd}")
                            if (authPwd) {
                                //保存 输入框添加投屏幕码正确
                                val inputCode = pincode.uppercase()
                                viewModel.savePinCode(inputCode)
                                enterEShareMain()
                            } else {
                                val inputCode = ""
                                viewModel.savePinCode(inputCode)
                                handler.sendEmptyMessage(MSGCODE_INPUT_ALERT)

                                // 需要先断开连接
                                mDeviceManager.disconnectDevice(deviceName)
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }

                        Log.e(
                            "Jason",
                            "pincodeConnectDevice.onSuccess.Thread.currentThread().getId() = "
                                    + Thread.currentThread().id
                        )
                    }.start()

                    Tools.setViewButtonEnable(eshare_connect, true)

                }

                override fun onError(e: EShareException) {
                    logE("FindDeviceActivity.connectDeviceWithPin.onError.e=${e.message}")
                    Thread {
                        try {
                            val inputCode = ""
                            viewModel.savePinCode(inputCode)
                            hideProgressDialogESheare()
                            handler.sendEmptyMessage(MSGCODE_INPUT_ERROR)
//                            ToastUtils.showLong(R.string.eshare_input_error)
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                        Log.e(
                            "Jason",
                            "pincodeConnectDevice.onError.Thread.currentThread().getId() = "
                                    + Thread.currentThread().id
                        )
                    }.start()
                    Tools.setViewButtonEnable(eshare_connect, true)
                }
            })

            Log.e(
                "Jason", "pincodeConnectDevice.Thread.currentThread().getId() = "
                        + Thread.currentThread().id
            )
        }
    }

    private fun clearInputCode() {
        val inputCode = ""
        viewModel.savePinCode(inputCode)
        eshare_pincode?.setText("")
        Tools.setViewButtonEnable(eshare_connect, false)
    }

    // 连接设备
    private fun connectDeviceByAddress(adress: String) {
        mExecutorService.execute {
            mDeviceManager.connectDevice(adress, "",
                object : ConnectDeviceCallback {
                    override fun onSuccess(succDevice: Device) {
                        deviceName = succDevice?.name ?: ""
                        viewModel.currentDevice = succDevice
                        currentPinCode = ""
                        currentDevice = succDevice
                        launch(Dispatchers.IO) {
                            HostHearBeat.get(mDeviceManager).startHearBeatThread()
                        }
                        hideProgressDialogESheare()
                        // 连接成功保存输入ip
                        val inputCode = adress
                        viewModel.savePinCode(inputCode)
                        checkPinCode(null)

                        Tools.setViewButtonEnable(eshare_connect, true)
                    }

                    override fun onError(e: EShareException) {
                        logE("FindDeviceActivity.connectDevice.onError.e=${e.message}")
                        ToastUtils.showLong(R.string.eshare_input_error)
                        val inputCode = ""
                        viewModel.savePinCode(inputCode)
                        Tools.setViewButtonEnable(eshare_connect, true)
                        hideProgressDialogESheare()
                    }
                })
        }
    }

    private fun connectDeviceByNameAndAddress(adress: String, name: String) {
        mExecutorService.execute {
            mDeviceManager.connectDevice(adress, "",
                object : ConnectDeviceCallback {
                    override fun onSuccess(device: Device) {
                        deviceName = device?.name ?: ""
                        currentPinCode = ""
                        currentDevice = device
                        enterEShareMain()
                    }

                    override fun onError(e: EShareException) {
                        logE("FindDeviceActivity.connectDevice.onError.e=${e.message}")
                        ToastUtils.showLong(R.string.eshare_connect_fail)
//                        clearInputCode()

                        deviceAdapter.removeDeviceAddress(adress)
                        // 延时重新连接
                        handler.sendEmptyMessage(MSGCODE_RECONNECT)

                    }
                })
        }
    }

    override fun onResume() {
        super.onResume()
        if (eshare_pincode?.text?.isNotEmpty() == true) {
            Tools.setViewButtonEnable(eshare_connect, true)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        logI("onActivityResult.requestCode=${requestCode},resultCode=${resultCode}")

        if (requestCode == 30 && resultCode == 30) {

            val pinCode = data?.getStringExtra("DEVICE_PINCODE") ?: ""
            val device_name = data?.getStringExtra("DEVICE_NAME") ?: ""
            val address = data?.getStringExtra("DEVICE_ADRESS") ?: ""

//            intent.putExtra("", (firstThreeOctetsDevice == firstThreeOctetsMobile))
            val ssid = data?.getStringExtra("DEVICE_SSID") ?: ""
            val password = data?.getStringExtra("DEVICE_PASSWORD") ?: ""

            logI(
                "onActivityResult.pinCode=${pinCode},address=${address}," +
                        "ssid=${ssid},password=${password}"
            )
            connectDeviceByNameAndAddress(address, device_name)
            /*
                设备无网络也可以扫码投屏:
                1. 扫码,获取二维码信息;
                2. 判断首个IP地址,是否和本机IP同网段?
                3. 同网段,原来的流程;
                4. 不同网段,则需要手机连接设备的wifi.并且记录原来的wifi
                5. 获取设备的ssid,pwd,ip
                6. 自动连接wifi,投屏;
                7. 断开时,需要断开wifi,重新连接原来的wifi
                * */
//            if (isOnline == true) {
//                // 同网段,原来的流程;
//                connectDeviceByNameAndAddress(address, device_name)
//            }else{
//
//                // 不同网段,则需要手机连接设备的wifi.并且记录原来的wifi
//                WifiUtils.getInstance(applicationContext).isDeviceOnline = false
//                connectDeviceByDeviceHotspot(address, ssid, password)
//            }

        }
    }

    private fun connectDeviceWifi(address: String, ssid: String, password: String) {
        logI("connectDeviceWifi.")
        launch {
            WifiUtils.getInstance(applicationContext).lastSSID = ""
            WifiUtils.getInstance(applicationContext).getWifiList()
            delay(300)
            WifiUtils.getInstance(applicationContext).connectWiFi(ssid, password) {
                if (it) {
                    logI("connectDeviceWifi.连接成功")
                    launch {
                        delay(300)
                        connectDeviceByNameAndAddress(address, "")
                    }
                } else {
                    logI("connectDeviceWifi.连接失败")
                }
            }
        }
    }

    private fun checkPinCode(device: Device?) {
        launch(Dispatchers.IO) {
            try {
                //Code here
//                    delay(1000)
                var isPwd = mDeviceManager.checkPassword()
                if (!isPwd) {
                    delay(500)
                    isPwd = mDeviceManager.checkPassword()
                }
                if (isPwd) {
                    selDevice = device
                    handler.sendEmptyMessage(MSGCODE_INPUTCODE)
                } else {
                    viewModel.savePinCode(currentDevice?.ipAddress ?: "", "")
                    enterEShareMain()
                }

            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    val handler: Handler = @SuppressLint("HandlerLeak")
    object : Handler() {
        override fun handleMessage(msg: Message) {
            when (msg.what) {
                // 弹出输入投屏码对话框
                MSGCODE_INPUTCODE -> {
                    showInputDialog("")
                    hideProgressDialogESheare()
                }

                // 提示Toast
                MSGCODE_INPUT_ALERT -> {
                    hideProgressDialogESheare()
                    ToastUtils.showLong(R.string.eshare_input_error)
                }

                // 断开连接
                MSGCODE_DISCONNECT -> {
                    mDeviceManager.disconnectDevice(deviceName)
                }

                // 提示Toast
                MSGCODE_INPUT_ERROR -> {
                    ToastUtils.showLong(R.string.eshare_input_error)
                }

                // 提示Toast
                MSGCODE_INPUT_ERROR1 -> {
                    ToastUtils.showLong(R.string.eshare_pincode_error)
                }

                // 延时重新连接
                MSGCODE_RECONNECT -> {
                    launch {
                        delay(1000L)
                        findDevices()
                    }
                }
            }
        }
    }

    // 进入连接成功主页
    private fun enterEShareMain() {
        if (currentPinCode.isNotEmpty()) {
//            viewModel.inputCode = currentPinCode.uppercase()
//            viewModel.savePinCode()

            runOnUiThread {
                eshare_pincode?.setText(currentPinCode.uppercase())
            }
        }
        hideProgressDialogESheare()
        viewModel.currentDevice = currentDevice

        val intent = Intent()
        intent.putExtra("DEVICE_NAME", deviceName)
        setResult(20, intent)
        finish()
    }

    /**
     * 消去密码输入对话框
     */
    private fun dismissInputDialog() {
        try {
            pwdDialog?.dismiss()
        } catch (exp: Exception) {
        }
        pwdDialog = null
        selDevice = null
    }

    private fun showInputDialog(name: String) {

        var localName = name
        if (name.isEmpty()) {
            localName = deviceName
        }

        pwdDialog = MyInputDialog(
            this,
            getString(R.string.eshare_input_title, localName),
            currentDevice?.ipAddress ?: "",
            "",
            object : MyInputDialog.clickCallBack {

                override fun yesClick(dialog: MyInputDialog) {
                    currentPinCode = dialog.editText?.text.toString()
                    // 校验必要信息
                    if (currentPinCode.isEmpty()) {
                        // 校验密码
                        ToastUtils.showLong(R.string.eshare_pincode_null)
                        return
                    }

                    if (!mDeviceManager.isDeviceConnect) {
                        // 设备没有连接上
                        dismissInputDialog()
                        return
                    }
                    //此处写后续逻辑
                    logI("showInputDialog.currentPinCode=${currentPinCode}")
                    <EMAIL> {
                        try {
                            //Code here
                            showProgressDialogEShare(true)
                            val authPwd = eShareNeedAuthPwd()
                            logI("EShareActivity.authPwd=${authPwd}")
                            if (authPwd) {
                                // 校验成功
                                viewModel.savePinCode(
                                    currentDevice?.ipAddress ?: "",
                                    currentPinCode.uppercase()
                                )
                                dismissInputDialog()
                                enterEShareMain()
                            } else {
                                // 校验失败
                                if (!mDeviceManager.isDeviceConnect) {
                                    // 设备没有连接上
                                    dismissInputDialog()
                                } else {
                                    // 投屏码错误
                                    viewModel.savePinCode(currentDevice?.ipAddress ?: "", "")
                                    handler.sendEmptyMessage(MSGCODE_INPUT_ERROR1)
                                }

                            }

                        } catch (e: Exception) {
                            e.printStackTrace()
                        } finally {
                            hideProgressDialogESheare(true)
                        }
                    }
                }


                override fun noClick(dialog: MyInputDialog) {
                    logI("showInputDialog.noClick")
                    HostHearBeat.get(mDeviceManager).stopHeatBeatThread()
                    handler.sendEmptyMessage(MSGCODE_DISCONNECT)
                    dismissInputDialog()
                }
            })
            .apply {
                window?.setBackgroundDrawableResource(android.R.color.transparent)
                show()
            }

    }

    /**
     * 判断EShare是否需要验证密码
     */
    private suspend fun eShareNeedAuthPwd(): Boolean = withContext(Dispatchers.IO) {
        var authPwd = mDeviceManager.authPassword(currentPinCode.uppercase())
        if (!authPwd) {
            // 第一次获取失败, 等待300ms 重新尝试
            delay(300)
            authPwd = mDeviceManager.authPassword(currentPinCode.uppercase())
        }
        authPwd
    }


    override fun onDestroy() {

        if (mExecutorService != null) {
            mExecutorService.shutdown()
        }

        mDeviceManager.clearDevices()
        mDeviceManager.stopFindDevice()
        super.onDestroy()
    }

    private fun hideProgressDialogESheare(immediately: Boolean = false) {
        hideProgressDialog(immediately)
        findDevices()
    }

    private fun showProgressDialogEShare(isDark: Boolean = false) {
        showProgressDialog(isDark)
        mDeviceManager.stopFindDevice()
    }

    override fun dispatchKeyEvent(event: KeyEvent): Boolean {
        if (event.keyCode == KeyEvent.KEYCODE_BACK) {
            if (event.action == KeyEvent.ACTION_UP) {
                checkAccountState()
            }
        }
        return super.dispatchKeyEvent(event)
    }

    private fun checkAccountState() {                //判断栈内没有activity了,说明应该跳转到loginactivity了
        if (ActivityUtils.getActivityList().size == 2 && !UserPreferences.getInstance().isValidUser
            && enterType == StarryConstants.ESHARE_EMPTY_TYPE_NORMAL
        ) {
            // 只有findDeviceActivity和EShareActivity,说明是账号被踢掉了,需要在结束时候跳转loginActivity
            val intent = Intent(this, LoginActivity::class.java)
            startActivity(intent)
        }
    }

    override fun onNetConnectedChange(isConnect: Boolean) {
        if (isConnect) {
            launch(Dispatchers.Main) {
                changeUI(0)
                refresh_layout?.autoRefresh()

            }
        } else {
            launch(Dispatchers.Main) {
                refresh_layout?.autoRefresh()
            }
        }
    }
}