package com.czur.cloud.ui.starry.meeting.baselib.utils

import android.os.Build
import android.text.format.Formatter
import androidx.annotation.RequiresApi
import com.blankj.utilcode.util.Utils
import com.czur.cloud.ui.starry.meeting.base.CZURAtyManager
import java.util.*

/**
 * Created by 陈丰尧 on 3/2/21
 */
fun getColor(colorId: Int) = CZURAtyManager.appContext.getColor(colorId)
fun getDrawable(colorId: Int) = CZURAtyManager.appContext.getDrawable(colorId)

fun getString(strId: Int) = CZURAtyManager.appContext.getString(strId)
fun getString(strId: Int, vararg formatArgs: Any) =
    CZURAtyManager.appContext.getString(strId, *formatArgs)

/**
 * 判断字符占多少长度
 */
@RequiresApi(Build.VERSION_CODES.N)
fun getShowLength(c: Char): Int {
    val codePoint = c.toString().codePointAt(0)
    return when (Character.UnicodeScript.of(codePoint)) {
        Character.UnicodeScript.HAN -> 2 // 一个汉字相当于2个字符
        else -> 1
    }
}

@RequiresApi(Build.VERSION_CODES.N)
fun String.getShowLength(): Int {
    return if (isEmpty()) {
        0
    } else {
        var count = 0
        for (c in toCharArray()) {
            count += getShowLength(c)
        }
        count
    }
}

/**
 * 格式化容量数字
 */
fun Long.toSizeStr(): String = Formatter.formatFileSize(CZURAtyManager.appContext, this)

fun pinyinSort(pinyin: String): String {
    val firstLetter = pinyin.first()
    return if (firstLetter.isLetter()) {
        pinyin.uppercase()
    } else {
        // 首字母不是字母的, 添加一个特殊符号参加排序, 统一放在列表的最后
        "~${pinyin.uppercase()}"
    }
}

/**
 * 创建UUID
 * @param ignoreSeparator:  为 true 时,返回的UUID将删除连接的短线
 */
fun createUUID(ignoreSeparator: Boolean = false): String {
    val uuid = UUID.randomUUID().toString()
    return if (ignoreSeparator) uuid.replace("-", "") else uuid
}

/**
 * 文字是否包含汉字
 */
fun String.containsHan(): Boolean {
    for (i in 0 until length) {
        val codePoint = codePointAt(i)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            if (Character.UnicodeScript.of(codePoint) == Character.UnicodeScript.HAN) {
                return true
            }
        }
    }
    return false
}

fun String.startSub(count: Int): String {
    return substring(0 until count)
}

fun String.endSub(count: Int): String {
    return substring((length - count) until length)
}
