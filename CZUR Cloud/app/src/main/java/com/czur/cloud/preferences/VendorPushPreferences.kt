package com.czur.cloud.preferences

import android.app.Application
import android.content.Context
import android.content.SharedPreferences
import com.czur.cloud.util.VendorUtils

object VendorPushPreferences {

    private const val SP_VENDOR_PUSH = "sp_vendor_push"

    private const val SP_PHONE_ROM = "sp_phone_rom"

//    companion object {
//        val instance: VendorPushPreferences
//            get() = SingletonHolder.instance
//    }
//
//    private object SingletonHolder {
//        internal val instance = VendorPushPreferences()
//    }

    private var application: Application? = null
    private lateinit var sharedPreference: SharedPreferences

    fun init(application: Application) {
        if (this.application == null) {
            this.application = application
            sharedPreference = application.getSharedPreferences(SP_VENDOR_PUSH, Context.MODE_PRIVATE)
        }
    }

    var phoneRom: VendorUtils.RomEnum?
        get() = VendorUtils.RomEnum.getRomByStr(sharedPreference.getString(SP_PHONE_ROM, null))
        set(value) = sharedPreference.edit().putString(SP_PHONE_ROM, value?.rom).apply()
}