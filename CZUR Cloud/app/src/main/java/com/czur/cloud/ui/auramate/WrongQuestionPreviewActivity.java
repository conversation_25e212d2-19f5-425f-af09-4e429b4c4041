package com.czur.cloud.ui.auramate;

import static androidx.annotation.Dimension.DP;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.blankj.utilcode.constant.PermissionConstants;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.cache.WrongQuestionImageRequest;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.common.ShareSDKCallback;
import com.czur.cloud.common.ShareSDKParams;
import com.czur.cloud.common.ShareSDKPlatforms;
import com.czur.cloud.common.ShareSDKType;
import com.czur.cloud.common.ShareSDKUtils;
import com.czur.cloud.entity.AuraMateWrongQuestionModel;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.UserInfoEvent;
import com.czur.cloud.event.WrongQuestionTagEvent;
import com.czur.cloud.model.ShareModel;
import com.czur.cloud.model.WrongQuestionPreviewModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.auramate.reportfragment.ReportUtil;
import com.czur.cloud.ui.camera.gallery.GalleryFrescoViewPager;
import com.czur.cloud.ui.camera.gallery.ZoomFrescoImageView;
import com.czur.cloud.ui.component.dialog.SocialShareDialog;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.util.PermissionUtil;
import com.czur.cloud.util.share.FileUtil;
import com.czur.cloud.util.share.ShareContentType;
import com.czur.cloud.util.share.ShareUtils;
import com.facebook.common.executors.UiThreadImmediateExecutorService;
import com.facebook.common.references.CloseableReference;
import com.facebook.datasource.BaseDataSubscriber;
import com.facebook.datasource.DataSource;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.interfaces.DraweeController;
import com.facebook.imagepipeline.core.ImagePipeline;
import com.facebook.imagepipeline.datasource.BaseBitmapDataSubscriber;
import com.facebook.imagepipeline.image.CloseableImage;
import com.facebook.imagepipeline.request.ImageRequest;
import com.facebook.imagepipeline.request.ImageRequestBuilder;

import org.greenrobot.eventbus.EventBus;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.ResponseBody;

/**
 * Created by Yz on 2018/6/1.
 * Email：<EMAIL>
 */
public class WrongQuestionPreviewActivity extends AuramateBaseActivity implements View.OnClickListener {
    private ImageView etPreviewBackBtn;

    private TextView etPreviewTitle1;
    private TextView etPreviewTitle2;

    private GalleryFrescoViewPager viewPager;
    private ImagePagerAdapter mAdapter;
    private HashMap<Integer, ZoomFrescoImageView> viewMap = new HashMap<>();
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private String seqNum;
    private int currentItem;

    private List<AuraMateWrongQuestionModel> fileList;
    private String date;

    private static final int SHARE_SUCCESS_CODE = 666;
//    private long time;
//    private String size;

    //    private boolean isFlattenRun = true;
    private boolean canBack;
    private String url;
    private String ownerId;
    private String tagId;

    private SocialShareDialog socialShareDialog;

    private View auraHomePreviewSaveBtn;
    private View auraHomePreviewShareBtn;
    private View auraHomePreviewDeleteBtn;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.black_2a);
        BarUtils.setNavBarColor(this, getColor(R.color.black_2a));
        BarUtils.setStatusBarLightMode(this, false);
        setContentView(R.layout.activity_wrong_question_preview);
        initComponent();
        initViewPager();
        registerEvent();
        getPreviewList();
    }

    @Override
    public boolean PCNeedFinish() {
        return false;
    }

    private void initComponent() {
        httpManager = HttpManager.getInstance();
        userPreferences = UserPreferences.getInstance(this);
        //    private int listPosition;
//        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA);
        ownerId = getIntent().getStringExtra("ownerId");
        tagId = getIntent().getStringExtra("tagId");
//        size = getIntent().getStringExtra("size");
        seqNum = getIntent().getStringExtra("seqNum");
        date = getIntent().getStringExtra("date");
        currentItem = getIntent().getIntExtra("listPosition", 0);

        String[] arr = date.split("\\s+");

        etPreviewBackBtn = (ImageView) findViewById(R.id.aura_home_preview_back_btn);

        etPreviewTitle1 = (TextView) findViewById(R.id.aura_home_preview_title_1);
        etPreviewTitle2 = (TextView) findViewById(R.id.aura_home_preview_title_2);

        viewPager = (GalleryFrescoViewPager) findViewById(R.id.aura_home_preview_viewpager);
        etPreviewTitle1.setText(arr[0]);
        etPreviewTitle2.setText(arr[1]);
        fileList = new ArrayList<>();
        socialShareDialog = new SocialShareDialog(this, shareDialogOnClickListener);

        auraHomePreviewSaveBtn = (View) findViewById(R.id.aura_home_preview_save_btn);
        auraHomePreviewShareBtn = (View) findViewById(R.id.aura_home_preview_share_btn);
        auraHomePreviewDeleteBtn = (View) findViewById(R.id.aura_home_preview_delete_btn);
    }

    private void registerEvent() {
        viewPager.addOnPageChangeListener(viewPageListener);
        etPreviewBackBtn.setOnClickListener(this);
        auraHomePreviewSaveBtn.setOnClickListener(this);
        auraHomePreviewShareBtn.setOnClickListener(this);
        auraHomePreviewDeleteBtn.setOnClickListener(this);
        setNetListener();
    }

//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void onEvent(BaseEvent event) {
//        switch (event.getEventType()) {
//            default:
//                break;
//        }
//    }

    /**
     * 通知系统相册更新
     */
    private void noticeAlbumUpdate(String sdPicPath) {
        File sdPicFile = FileUtils.getFileByPath(sdPicPath);
//        Uri contentUri = Uri.fromFile(sdPicFile);
        MediaScannerConnection.scanFile(WrongQuestionPreviewActivity.this,
                new String[]{sdPicFile.getAbsolutePath()}, new String[]{"image/jpeg"},
                (path, uri) -> logI("file " + path + ", scanned seccessfully: " + uri));
    }

    private void initViewPager() {
        mAdapter = new ImagePagerAdapter();
        viewPager.setPageMargin(10 * DP);
        viewPager.setAdapter(mAdapter);
        viewPager.setOffscreenPageLimit(0);

        // 设置网络状态
        if (!NetworkUtils.isConnected()) {
            viewPager.setNoNetwork(true);
        } else {
            viewPager.setNoNetwork(false);
        }

        // 使用post延迟执行，确保ViewPager完全初始化后再设置当前页面
        viewPager.post(() -> {
            if (currentItem >= 0 && currentItem < fileList.size()) {
                viewPager.setCurrentItem(currentItem, false);
            }
        });
    }

    private ViewPager.OnPageChangeListener viewPageListener = new ViewPager.OnPageChangeListener() {
        @Override
        public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
        }

        @Override
        public void onPageSelected(int position) {
            ZoomFrescoImageView view = viewMap.get(currentItem);
            if (view != null) {
                view.reset();
            }
            currentItem = position;
            String[] arr = fileList.get(viewPager.getCurrentItem()).getCreateTime().split("\\s+");
            etPreviewTitle1.setText(arr[0]);

            etPreviewTitle2.setText(arr[1]);
            checkIsCache(fileList.get(viewPager.getCurrentItem()).getOssKeyUrl(), position);
        }

        @Override
        public void onPageScrollStateChanged(int state) {

        }

    };


    public class ImagePagerAdapter extends PagerAdapter {
        @Override
        public int getCount() {
            return fileList.size();
        }

        @Override
        public void setPrimaryItem(@NotNull ViewGroup container, int position, @NotNull Object object) {
            super.setPrimaryItem(container, position, object);
            ZoomFrescoImageView zoomImage = viewMap.get(position);
            ((GalleryFrescoViewPager) container).setZoomView(zoomImage);
        }

        @NotNull
        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            ZoomFrescoImageView zoomImage = initZoomFrescoImageView(position);
            container.addView(zoomImage);
            if (viewPager.getCurrentItem() == position) {
//                size = EtUtils.convertOriginalSize(fileList.get(position).getFileSize());
                checkIsCache(fileList.get(position).getOssKeyUrl(), position);
            }

            return zoomImage;
        }

        @Override
        public void destroyItem(ViewGroup container, int position, @NotNull Object object) {
            container.removeView((View) object);
            ZoomFrescoImageView zoomImage = viewMap.get(position);
            if (zoomImage != null) {
                zoomImage.setImageBitmap(null);
                viewMap.remove(position);
            }
        }


        @Override
        public boolean isViewFromObject(@NotNull View view, @NotNull Object object) {
            return view == object;
        }

        @Override
        public int getItemPosition(@NotNull Object object) {
            return POSITION_NONE;
        }

    }

    public ZoomFrescoImageView initZoomFrescoImageView(int position) {
        ZoomFrescoImageView zoomImage = viewMap.get(position);
        if (zoomImage == null) {
            zoomImage = setImageToIndex(position);
        }
        return zoomImage;
    }


    private ZoomFrescoImageView setImageToIndex(final int index) {
        ZoomFrescoImageView zoomImage = new ZoomFrescoImageView(this);
        viewMap.put(index, zoomImage);
        return zoomImage;
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.aura_home_preview_back_btn:
                finish();
                break;
            case R.id.aura_home_preview_save_btn:
                if (checkNetwork()) break;
                requestCopyToSdPermission(false);
                break;
            case R.id.aura_home_preview_share_btn:
                if (checkNetwork()) break;
                if (BuildConfig.IS_OVERSEAS) {
                    requestCopyToSdPermission(true);
                } else {
                    getShareImageUrl();
                }
                break;

            case R.id.aura_home_preview_delete_btn:
                if (checkNetwork()) break;
                confirmDeleteDialog();
                break;

            default:
                break;
        }
    }

    /**
     * 显示删除Dialog
     */
    private void confirmDeleteDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(WrongQuestionPreviewActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.confirm_delete));
        builder.setOnPositiveListener((dialog, which) -> {
            dialog.dismiss();
            delete();
        });
        builder.setOnNegativeListener((dialog, which) -> dialog.dismiss());
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();
    }

    /**
     * 删除文件或者图片
     */
    private void delete() {

        httpManager.request().deleteWrongQuestionQuestion(userPreferences.getUserId(), ownerId, fileList.get(viewPager.getCurrentItem()).getId() + "", String.class, new MiaoHttpManager.CallbackNetwork<String>() {
            @Override
            public void onNoNetwork() {
                showMessage(R.string.toast_no_connection_network);
            }

            @Override
            public void onStart() {
                showProgressDialog(true);

            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                EventBus.getDefault().post(new WrongQuestionTagEvent(EventType.DELETE_WRONG_QUESTION_TAG));
                getPreviewList();
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                hideProgressDialog();

            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
            }
        });
    }

    /**
     * 申请权限
     */
    private void requestCopyToSdPermission(boolean isShare) {
        PermissionUtils.permission(PermissionUtil.getStoragePermission())
                .rationale((activity, shouldRequest) -> {
                    showMessage(isShare ? R.string.denied_share : R.string.denied_sdcard);
                    shouldRequest.again(true);
                })
                .callback(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(@NotNull List<String> permissionsGranted) {
                        //checkIsCache(fileList.get(viewPager.getCurrentItem()).getOssKeyUrl(), true, viewPager.getCurrentItem());
                        showProgressDialog(true);
                        downloadOriginalPicture(getBigUrl(), isShare);
                    }

                    @Override
                    public void onDenied(@NotNull List<String> permissionsDeniedForever,
                                         @NotNull List<String> permissionsDenied) {
                        showMessage(isShare ? R.string.denied_share : R.string.denied_sdcard);
                    }
                })
                .theme(ScreenUtils::setFullScreen)
                .request();
    }

    private String getBigUrl() {
        return fileList.get(viewPager.getCurrentItem()).getOssKeyUrl();
    }

    public boolean checkNetwork() {
        boolean isNoNetwork = hasNetwork();
        return isNoNetwork || fileList.size() <= 0;
    }

    private void downloadOriginalPicture(String url, boolean isShare) {
        new Thread(() -> {
            try {
                OkHttpClient client = new OkHttpClient();
                Request request = new Request.Builder().url(url).build();
                ResponseBody body = client.newCall(request).execute().body();
                if (body != null) {
                    InputStream in = body.byteStream();
                    //转化为bitmap
                    Bitmap bitmap = BitmapFactory.decodeStream(in);
                    runOnUiThread(() -> saveToAlbum(bitmap, isShare));
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }).start();
    }

    public boolean hasNetwork() {
        if (fileList.size() <= 0 && !NetworkUtils.isConnected()) {
            showMessage(R.string.toast_no_connection_network);
            return true;
        }
        return false;
    }


    public void showShare(int viewId) {
        ShareSDKParams params = new ShareSDKParams();
        params.setImageUrl(url).setSite(getString(R.string.app_name));
        switch (viewId) {
            case R.id.weixin_share:
                params.setShareType(ShareSDKType.SHARE_IMAGE).setUrl(url).setPlatform(ShareSDKPlatforms.WECHAT);
                break;
            case R.id.friend_share:
                params.setShareType(ShareSDKType.SHARE_IMAGE).setUrl(url).setPlatform(ShareSDKPlatforms.WECHAT_MOMENTS);
                break;
            case R.id.qq_share:
                params.setPlatform(ShareSDKPlatforms.QQ);
                break;
            case R.id.qq_zone_share:
                params.setTitleUrl(url).setTitle(getString(R.string.wrong_title)).setPlatform(ShareSDKPlatforms.QZONE);
                break;
            case R.id.weibo_share:
                String text =this.getResources().getString(R.string.share) ;
                params.setText(text );
                params.setPlatform(ShareSDKPlatforms.WEIBO);
                break;
            default:
                return;
        }
        params.setCallback(new ShareSDKCallback() {
            @Override
            public void onComplete() {
//                showMessage(R.string.share_success);
            }

            @Override
            public void onError() {
                showMessage(R.string.share_failed);
            }

            @Override
            public void onCancel() {
            }
        });
        ShareSDKUtils.INSTANCE.share(params);
    }

    private String getBigShareKey(int position) {
        return fileList.get(position).getOssKey();
    }

    /**
     * 得到分享图片接口
     */
    private void getShareImageUrl() {
        httpManager.request().auraShare(userPreferences.getUserId(), getBigShareKey(viewPager.getCurrentItem()), ShareModel.class, new MiaoHttpManager.Callback<ShareModel>() {
            @Override
            public void onStart() {
                showProgressDialog(true);
            }

            @Override
            public void onResponse(MiaoHttpEntity<ShareModel> entity) {
                hideProgressDialog();
                url = entity.getBody().getUrl();
                socialShareDialog.show();
            }

            @Override
            public void onFailure(MiaoHttpEntity<ShareModel> entity) {
                hideProgressDialog();
                showMessage(R.string.share_failed);
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
                showMessage(R.string.share_failed);
            }
        });
    }


    /**
     * 从缓存读取图片
     */
    private void getCacheImage(final int position) {
//        AuraCustomImageRequest imageRequest = new AuraCustomImageRequest(ImageRequestBuilder.newBuilderWithSource(Uri.parse(getBigUrl(position))));
//        imageRequest.setImageRequestType(1);
//        imageRequest.setImageRequestObject(getBigKey(position));
        ImageRequest imageRequest = ImageRequestBuilder.newBuilderWithSource(Uri.parse(fileList.get(position).getOssKeyUrl())).build();
        final ImagePipeline imagePipeline = Fresco.getImagePipeline();
        DataSource<CloseableReference<CloseableImage>> dataSource = imagePipeline.fetchDecodedImage(imageRequest, this);
        dataSource.subscribe(new BaseBitmapDataSubscriber() {
            @Override
            protected void onNewResultImpl(final Bitmap bitmap) {
//                if (true) {
                ZoomFrescoImageView zoomImage = initZoomFrescoImageView(position);
                zoomImage.setImageBitmap(bitmap);
//                } else {
//                    saveToAlbum(bitmap);
//                }
            }

            @Override
            protected void onFailureImpl(DataSource<CloseableReference<CloseableImage>> dataSource) {
            }
        }, UiThreadImmediateExecutorService.getInstance());
    }

    /**
     * 保存至相册
     */
    private void saveToAlbum(final Bitmap bitmap, boolean isShare) {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<String>() {
            @Override
            public String doInBackground() {
//                String path = Environment.getExternalStorageDirectory() + CZURConstants.SD_PATH + CZURConstants.AURA_MATE_PATH;
//                String path = CZURConstants.SD_PATH + CZURConstants.AURA_MATE_PATH;
                String path = CZURConstants.PICTURE_PATH + CZURConstants.AURA_MATE_PATH;
                String etPath = "";
                if (FileUtils.createOrExistsDir(path)) {
                    etPath = path + UUID.randomUUID() + CZURConstants.JPG;
                    ImageUtils.save(bitmap, etPath, Bitmap.CompressFormat.JPEG, false);
                }
                return etPath;
            }

            @Override
            public void onSuccess(String result) {
                hideProgressDialog(true);
                if (isShare) {
                    new ShareUtils.Builder(WrongQuestionPreviewActivity.this)
                            .setOnActivityResult(SHARE_SUCCESS_CODE)
                            // 指定分享的文件类型
                            .setContentType(ShareContentType.IMAGE)
                            // 设置要分享的文件 Uri
                            .setShareFileUri(FileUtil.getFileUri(WrongQuestionPreviewActivity.this, ShareContentType.FILE, new File(result)))
                            // 设置分享选择器的标题
                            .setTitle(getString(R.string.share_to))
                            .build()
                            // 发起分享
                            .shareBySystem();
                } else {
                    noticeAlbumUpdate(result);
                    showMessage(R.string.et_save_success);
                }
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                if (!isShare) {
                    showMessage(R.string.et_save_failed);
                }
            }
        });
    }

    /**
     * 得到展示列表接口
     */
    private void getPreviewList() {
        httpManager.request().getWrongQuestionBeforeAndAfter(userPreferences.getUserId(), ownerId, tagId, seqNum, "15", WrongQuestionPreviewModel.class, new MiaoHttpManager.Callback<WrongQuestionPreviewModel>() {
            @Override
            public void onStart() {
                showProgressDialog(true);
            }

            @Override
            public void onResponse(MiaoHttpEntity<WrongQuestionPreviewModel> entity) {
                hideProgressDialog();
                int offset = entity.getBody().getOffset();
                if (offset != 0) {
                    currentItem = offset - 1;
                }
                fileList = entity.getBody().getFileList();
                int position = currentItem;
                if (currentItem == fileList.size()) {
                    position = currentItem - 1;
                }
                if (fileList.size() > 0) {
                    String[] arr = fileList.get(position).getCreateTime().substring(0, date.length()).split("\\s+");
                    etPreviewTitle1.setText(arr[0]);
                    etPreviewTitle2.setText(arr[1]);
                    refreshViewPager();
                } else {
                    finish();
                }


            }

            @Override
            public void onFailure(MiaoHttpEntity<WrongQuestionPreviewModel> entity) {
                canBack = true;
                hideProgressDialog();
                if (entity.getCode() != MiaoHttpManager.IMG_NO_FOUND) {
                    showMessage(R.string.request_failed_alert);
                }
            }

            @Override
            public void onError(Exception e) {
                canBack = true;
                showMessage(R.string.request_failed_alert);
            }
        });
    }

    /**
     * 检查缓存
     * setType :0 是否显示按钮，1是否从缓存加载图片，2 一起
     */
    private void checkIsCache(final String url, final int position) {
        DataSource<Boolean> dataSource = Fresco.getImagePipeline().isInDiskCache(Uri.parse(fileList.get(viewPager.getCurrentItem()).getOssKeyUrl()));
        dataSource.subscribe(new BaseDataSubscriber<Boolean>() {
            @Override
            protected void onNewResultImpl(DataSource<Boolean> dataSource) {
                if (!dataSource.isFinished()) {
                    return;
                }
                boolean isCache = dataSource.getResult() == null ? false : dataSource.getResult();
//                if (true) {
                if (isCache) {
                    getCacheImage(position);

                } else {
                    ZoomFrescoImageView zoomImage = initZoomFrescoImageView(position);
                    String path = fileList.get(viewPager.getCurrentItem()).getOssKeyUrl();
                    Uri lowResUri = Uri.parse(path);
                    WrongQuestionImageRequest imageRequest = new WrongQuestionImageRequest(ImageRequestBuilder.newBuilderWithSource(lowResUri));
                    imageRequest.setImageRequestType(1);
                    imageRequest.setImageRequestObject(fileList.get(viewPager.getCurrentItem()).getOssKeyUrl());

                    DraweeController controller = Fresco.newDraweeControllerBuilder()
                            .setImageRequest(imageRequest)
                            .build();
                    zoomImage.setController(controller);
                }

            }

            @Override
            protected void onFailureImpl(DataSource<Boolean> dataSource) {

            }
        }, UiThreadImmediateExecutorService.getInstance());
    }


    /**
     * 刷新ViewPager
     */
    private void refreshViewPager() {
        canBack = false;

        // 保存当前位置
        int savedCurrentItem = currentItem;

        // 检查网络状态并设置
        boolean isNoNetwork = hasNetwork();
        if (isNoNetwork || fileList.size() <= 0) {
            viewPager.setNoNetwork(true);
        } else {
            viewPager.setNoNetwork(false);
        }

        // 重新创建并设置Adapter，确保完全刷新
        mAdapter = new ImagePagerAdapter();
        viewPager.setAdapter(mAdapter);

        // 使用post延迟执行，确保Adapter设置完成后再设置当前页面
        viewPager.post(() -> {
            if (savedCurrentItem >= 0 && savedCurrentItem < fileList.size()) {
                viewPager.setCurrentItem(savedCurrentItem, false);
                canBack = true;
            } else {
                canBack = true;
            }
        });
    }

    /**
     * 分享dialog监听
     */
    private SocialShareDialog.ShareDialogOnClickListener shareDialogOnClickListener = new SocialShareDialog.ShareDialogOnClickListener() {
        Context cnt = WrongQuestionPreviewActivity.this;
        boolean isInstall = false;
        @Override
        public void onShareItemClick(int viewId) {
            switch (viewId) {
                case R.id.weixin_share:
                case R.id.friend_share:
                    isInstall = ReportUtil.isWeixinInstalled(cnt);
                    if (isInstall) {
                        showShare(viewId);
                    }else{
                        showMessage(cnt.getResources().getString(R.string.share_not_install_wechat));
                    }
                    socialShareDialog.dismiss();
                    break;
                case R.id.qq_share:
                case R.id.qq_zone_share:
                    isInstall = ReportUtil.isQQClientInstalled(cnt);
                    if (isInstall) {
                        showShare(viewId);
                    }else{
                        showMessage(cnt.getResources().getString(R.string.share_not_install_qq));
                    }
                    socialShareDialog.dismiss();
                    break;
                case R.id.weibo_share:
                    isInstall = ReportUtil.isWeiboInstalled(cnt);
                    if (isInstall) {
                        showShare(viewId);
                    }else{
                        showMessage(cnt.getResources().getString(R.string.share_not_install_sinaweibo));
                    }
                    socialShareDialog.dismiss();
                    break;
                case R.id.share_dialog_cancel_btn:
                    socialShareDialog.dismiss();
                default:
                    break;
            }
        }
    };

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
//        if (requestCode == CropImage.CROP_IMAGE_ACTIVITY_REQUEST_CODE && data != null) {
//
//        } else if (requestCode == SHARE_SUCCESS_CODE) {
//        }
    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {

        if (keyCode == KeyEvent.KEYCODE_BACK && !canBack) { //监控/拦截/屏蔽返回键
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onDestroy() {
        EventBus.getDefault().post(new UserInfoEvent(EventType.UPDATE_CACHE));
        super.onDestroy();
//        isFlattenRun = false;
    }

}
