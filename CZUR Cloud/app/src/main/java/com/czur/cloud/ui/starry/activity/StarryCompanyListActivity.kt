package com.czur.cloud.ui.starry.activity

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.ActivityUtils
import com.czur.cloud.BuildConfig
import com.czur.cloud.R
import com.czur.cloud.preferences.StarryPreferences
import com.czur.cloud.ui.starry.adapter.StarryCompanyAdapter
import com.czur.cloud.ui.starry.base.StarryNewBaseActivity
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.meeting.dialog.StarryMeetingPwdDialog
import com.czur.cloud.ui.starry.model.StarryEnterpriseModel
import com.czur.cloud.ui.starry.utils.Tools
import com.czur.cloud.ui.starry.utils.singleClick
import com.czur.cloud.ui.starry.viewmodel.StarryViewModel
import com.czur.czurutils.log.logI
import kotlinx.android.synthetic.main.starry_activity_company_list.*
import kotlinx.android.synthetic.main.starry_layout_top_bar.*

/**
 * 选择企业列表
 */
class StarryCompanyListActivity : StarryNewBaseActivity(){

    override fun getLayout(): Int = R.layout.starry_activity_company_list

    private val viewModel by lazy {
        ViewModelProvider(StarryActivity.mainActivity ?: this)[StarryViewModel::class.java]
    }
    private val linearLayoutManager by lazy {
        LinearLayoutManager(this)
    }
    private val mAdapter by lazy {
        StarryCompanyAdapter(this)
    }

    override fun initViews() {
        super.initViews()

        logI("StarryCompanyListActivity.initViews")

        // top bar
        user_back_btn.setOnClickListener {
            ActivityUtils.finishActivity(this)
        }

        // Title
        user_title.text = getString(R.string.starry_select_company_list)
        if (BuildConfig.IS_OVERSEAS){
            user_title?.textSize = 16f
            user_title?.maxEms = 14
        }

        // list
        recycler_view_company?.apply{
            setHasFixedSize(true)
            layoutManager = linearLayoutManager
            adapter = mAdapter
        }
        mAdapter.setOnItemPickListener(object: StarryCompanyAdapter.OnItemPickListener{
            override fun onItemPick(position: Int, enterprise: StarryEnterpriseModel) {
                onClickEnterCompany(position, enterprise)
            }
        })

        viewModel.starryMeetingIsShowPwd = StarryPreferences.getInstance().lastShowPwd ?: false
        starry_company_pwd_show?.isSelected = viewModel.starryMeetingIsShowPwd
        onSetShowPwd(viewModel.starryMeetingIsShowPwd)

        starry_company_password_rl?.singleClick {
            logI("StarryCompanyListActivity.starry_company_password_rl")
            openSetMeetingPwdDlg()
        }

        starry_company_pwd_show?.setOnClickListener {
            logI("StarryCompanyListActivity.starry_company_pwd_show")
            starry_company_pwd_show?.isSelected = !it.isSelected
            viewModel.starryMeetingIsShowPwd = !viewModel.starryMeetingIsShowPwd
            onSetShowPwd(viewModel.starryMeetingIsShowPwd)
        }

        starry_company_refresh?.singleClick {
            logI("StarryCompanyListActivity.starry_company_refresh")
            viewModel.onRandMeetingPwd()
            starry_comapny_pwd_title?.text = String.format(
                getString(R.string.starry_company_pwd_title),
                viewModel.starryMeetingPwdAuto)
        }

        // 显示、隐藏部分内容
        starry_company_password_rl?.visibility = View.VISIBLE
        starry_meeting_code_pwd_rl?.visibility = View.GONE
        starry_meeting_code_pwd_rl2?.visibility = View.GONE

    }

    private fun onSetShowPwd(flag: Boolean){
        if (flag){
            viewModel.starryMeetingPwdAuto = StarryPreferences.getInstance().lastPwd
            starry_comapny_pwd_title?.alpha = 1.0f
            starry_company_refresh?.visibility = View.VISIBLE
            starry_comapny_pwd_title?.text = String.format(
                getString(R.string.starry_company_pwd_title),
                viewModel.starryMeetingPwdAuto)
        }else{
            starry_comapny_pwd_title?.alpha = 0.5f
            starry_company_refresh?.visibility = View.INVISIBLE
            viewModel.starryMeetingPwdAuto = ""
            starry_comapny_pwd_title?.text = getString(R.string.starry_company_pwd_title_no)
        }
        StarryPreferences.getInstance().lastShowPwd = viewModel.starryMeetingIsShowPwd

    }

    private fun openSetMeetingPwdDlg(){
        val dialog = StarryMeetingPwdDialog(
            context = this,
            title = getString(R.string.starry_meeting_pwd),
            content = "",
            hint = getString(R.string.starry_meeting_pwd_hint),
            OKTitle = getString(R.string.starry_meeting_pwd_open),
            cancelTitle = getString(R.string.starry_meeting_pwd_close),
            object : StarryMeetingPwdDialog.clickCallBack{
                override fun yesClick(dialog: StarryMeetingPwdDialog) {
                    starry_company_pwd_show?.isSelected = true
                    viewModel.starryMeetingIsShowPwd = true
                    onSetShowPwd(viewModel.starryMeetingIsShowPwd)

//                    viewModel.starryMeetingPwdSet = dialog.editText?.text.toString() ?: "000000"
                    val pwd = dialog.editText?.text.toString() ?: ""
                    if (pwd.isBlank()){
                        viewModel.starryMeetingPwdSet = Tools.onRandMeetingPassword()
                    }else{
                        viewModel.starryMeetingPwdSet = Tools.onMakeUpPwd(pwd)
                    }
                    viewModel.starryMeetingPwdAuto = viewModel.starryMeetingPwdSet
                    starry_comapny_pwd_title?.text = String.format(
                        getString(R.string.starry_company_pwd_title),
                        viewModel.starryMeetingPwdAuto)
                    StarryPreferences.getInstance().lastPwd = viewModel.starryMeetingPwdSet
                    dialog.dismiss()
                }

                override fun noClick(dialog: StarryMeetingPwdDialog) {
                    viewModel.starryMeetingPwdSet = ""
                    viewModel.starryMeetingPwdAuto = viewModel.starryMeetingPwdSet
                    starry_company_pwd_show?.isSelected = false
                    viewModel.starryMeetingIsShowPwd = false
                    onSetShowPwd(viewModel.starryMeetingIsShowPwd)

                    dialog.dismiss()
                }
            }
        )
        dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)
//        dialog.setInputTextLength(6)
        if (StarryPreferences.getInstance().lastPwd == ""){
            viewModel.onRandMeetingPwd()
        }
        dialog.setInitPwd(StarryPreferences.getInstance().lastPwd)
        dialog.show()

        // 弹出带输入框的对话框，弹起软键盘，背景activity不抬起。
        window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING);
    }

    private fun onClickEnterCompany(position: Int, enterprise: StarryEnterpriseModel){
        viewModel.setCurrentUserTypeCompany(enterprise)
        viewModel.starryPrePageName = StarryConstants.STARRY_PRE_PAGE_NAME

        val intent = Intent(this@StarryCompanyListActivity, StarryCompanyListContactsActivity::class.java)
        ActivityUtils.startActivity(intent)
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        viewModel.enterpriseListJoined.observe(this){
            mAdapter.setmDatas(it)
        }
    }

}