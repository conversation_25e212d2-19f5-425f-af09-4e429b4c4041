package com.czur.cloud.ui.auramate;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.badoo.mobile.util.WeakHandler;
import com.baoyz.widget.PullRefreshLayout;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.blankj.utilcode.util.UtilsTransActivity;
import com.czur.cloud.R;
import com.czur.cloud.adapter.EtPdfAdapter;
import com.czur.cloud.model.PdfModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.component.CustomRingDrawable;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.et.EtPdfPreviewActivity;
import com.czur.cloud.util.EtUtils;
import com.czur.cloud.util.PermissionUtil;
import com.czur.cloud.util.validator.Validator;
import com.google.gson.reflect.TypeToken;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

import io.realm.Realm;

/**
 * Created by Yz on 2018/2/28.
 * Email：<EMAIL>
 */

public class AuraMatePdfActivity extends AuramateBaseActivity implements View.OnClickListener {

    private RecyclerView gridFileList;
    private List<PdfModel> datas;
    private ImageView myPdfBackBtn;
    private TextView myPdfTopSelectAllBtn;
    private TextView myPdfTitle;
    private TextView myPdfCancelBtn;
    private RelativeLayout myPdfDeleteRl;
    private RelativeLayout pdfEmptyRl;
    private RelativeLayout myPdfMultiSelectBtn;
    private RecyclerView myPdfRecyclerView;
    private EtPdfAdapter etPdfAdapter;
    private List<PdfModel> pdfModels;
    private LinearLayout myPdfBottomLl;
    private LinkedHashMap<String, Boolean> isCheckedMap;
    private Realm realm;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private List<String> pdfIds;
    private PullRefreshLayout swipeRefreshLayout;
    private CustomRingDrawable customRingDrawable;
    private WeakHandler handler;
    private long requestDeviceApiTime;
    private long needSleepTime;
    private long currentTimeMillis;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_et_pdf);
        initComponent();
        initRecyclerView();
        registerEvent();
        getRefreshPdf(false);
    }

    @Override
    protected boolean PCNeedFinish() {
        return false;
    }

    private void initComponent() {

        httpManager = HttpManager.getInstance();
        userPreferences = UserPreferences.getInstance(this);
        swipeRefreshLayout = (PullRefreshLayout) findViewById(R.id.swipeRefreshLayout);
        handler = new WeakHandler();

        pdfEmptyRl = (RelativeLayout) findViewById(R.id.pdf_empty_rl);
        myPdfDeleteRl = (RelativeLayout) findViewById(R.id.my_pdf_delete_rl);
        myPdfBottomLl = (LinearLayout) findViewById(R.id.my_pdf_bottom_ll);
        myPdfBackBtn = (ImageView) findViewById(R.id.my_pdf_back_btn);
        myPdfTopSelectAllBtn = (TextView) findViewById(R.id.my_pdf_top_select_all_btn);
        myPdfTitle = (TextView) findViewById(R.id.my_pdf_title);
        myPdfCancelBtn = (TextView) findViewById(R.id.my_pdf_cancel_btn);
        myPdfMultiSelectBtn = (RelativeLayout) findViewById(R.id.my_pdf_multi_select_btn);
        myPdfRecyclerView = (RecyclerView) findViewById(R.id.my_pdf_recyclerView);
        myPdfTitle.setText(R.string.my_pdf);


    }

    private PullRefreshLayout.OnRefreshListener onRefreshListener = new PullRefreshLayout.OnRefreshListener() {
        @Override
        public void onRefresh() {
            getRefreshPdf(false);
        }
    };

    private void registerEvent() {
        myPdfTopSelectAllBtn.setOnClickListener(this);
        myPdfCancelBtn.setOnClickListener(this);
        myPdfMultiSelectBtn.setOnClickListener(this);
        myPdfBackBtn.setOnClickListener(this);
        myPdfDeleteRl.setOnClickListener(this);
        swipeRefreshLayout.setOnRefreshListener(onRefreshListener);
        customRingDrawable = new CustomRingDrawable(this, swipeRefreshLayout);
        swipeRefreshLayout.setRefreshDrawable(customRingDrawable);
        setNetListener();


    }

    private void refreshSuccess(final boolean isDelete) {
        if (customRingDrawable.isRunning()) {
            //手动下拉刷新
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    swipeRefreshLayout.setRefreshing(false);
                    isShowEmptyPrompt();
                    if (isDelete){
                        refreshPdf();
                    }else {
                        etPdfAdapter.refreshData(pdfModels);
                    }


                }
            }, needSleepTime);
        } else {
            //不是手动下拉刷新
            handler.post(new Runnable() {
                @Override
                public void run() {
                    swipeRefreshLayout.setRefreshing(false);
                    isShowEmptyPrompt();
                    if (isDelete){
                        refreshPdf();
                    }else {
                        etPdfAdapter.refreshData(pdfModels);
                    }
                }
            });
        }
    }

    /**
     * @des: 图片列表接口
     * @params:
     * @return:
     */
    private List<PdfModel> getPdfList() {
        try {
            final MiaoHttpEntity<PdfModel> pdfModelMiaoHttpEntity = httpManager.request().auraPdfListSync(userPreferences.getUserId(), new TypeToken<List<PdfModel>>() {
            }.getType());
            if (pdfModelMiaoHttpEntity == null) {
                return null;
            } else if (pdfModelMiaoHttpEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return pdfModelMiaoHttpEntity.getBodyList();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    //获取最新文件
    private void getRefreshPdf(final boolean isDelete) {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                currentTimeMillis = System.currentTimeMillis();
                pdfModels = getPdfList();
                return null;
            }

            @Override
            public void onSuccess(Void result) {
                checkTime();
                refreshSuccess(isDelete);
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                checkTime();
                refreshError();
            }
        });
    }
    /**
     * @des: 重置准备刷新
     * @params:
     * @return:
     */

    private void resetToFresh() {
        pdfModels = new ArrayList<>();
        pdfIds = new ArrayList<>();
        isCheckedMap.clear();
        isCheckedMap = new LinkedHashMap<>();

    }

    /**
     * @des: 重置选中状态并且刷新
     * @params:
     * @return:
     */
    private void refreshPdf() {
        isMultiSelect = false;
        isSelectAll = false;
        hideSelectTopBar();
        etPdfAdapter.refreshData(pdfModels, isMultiSelect, isCheckedMap);

    }
    private void checkTime() {
        requestDeviceApiTime = System.currentTimeMillis() - currentTimeMillis;
        if (requestDeviceApiTime >= 1000) {
            needSleepTime = 1;
        } else {
            needSleepTime = 1000 - requestDeviceApiTime;
        }
    }

    /**
     * @des: 刷新接口后设置刷新状态
     * @params:
     * @return:
     */

    private void refreshError() {
        if (customRingDrawable.isRunning()) {
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    isShowEmptyPrompt();
                    swipeRefreshLayout.setRefreshing(false);
                }
            }, needSleepTime);
        }
    }

    /**
     * @des: 是否显示空文件夹提示区域
     * @params:
     * @return:
     */

    private void isShowEmptyPrompt() {
        if (Validator.isEmpty(pdfModels) || pdfModels.size() <= 0) {
            myPdfRecyclerView.setVisibility(View.GONE);
            pdfEmptyRl.setVisibility(View.VISIBLE);
        } else {
            myPdfRecyclerView.setVisibility(View.VISIBLE);
            pdfEmptyRl.setVisibility(View.GONE);
        }
    }

    /**
     * @des: 初始化列表
     * @params:
     * @return:
     */

    private void initRecyclerView() {
        pdfModels = new ArrayList<>();
        isCheckedMap = new LinkedHashMap<>();
        etPdfAdapter = new EtPdfAdapter(this, pdfModels, false);
        etPdfAdapter.setOnItemCheckListener(onItemCheckListener);
        etPdfAdapter.setOnItemClickListener(onItemClickListener);
        etPdfAdapter.setOnItemLongClickListener(onItemLongClickListener);
        myPdfRecyclerView.setAdapter(etPdfAdapter);
        myPdfRecyclerView.setHasFixedSize(true);
        myPdfRecyclerView.setLayoutManager(new LinearLayoutManager(this));

    }


    /**
     * @des: 请求权限
     * @params:[]
     * @return:void
     */
    private void requestPermission(final PdfModel PdfModel) {

        PermissionUtils.permission(PermissionUtil.getStoragePermission())
                .rationale(new PermissionUtils.OnRationaleListener() {
                    @Override
                    public void rationale(UtilsTransActivity activity, ShouldRequest shouldRequest) {
                        showMessage(R.string.denied_PDF_preview);
                        shouldRequest.again(true);
                    }
                })
                .callback(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(List<String> permissionsGranted) {
                        Intent intent = new Intent(AuraMatePdfActivity.this, EtPdfPreviewActivity.class);
                        intent.putExtra("pdfUrl", PdfModel.getUrl());
                        intent.putExtra("pdfName", PdfModel.getFileName());
                        ActivityUtils.startActivity(intent);
                    }

                    @Override
                    public void onDenied(List<String> permissionsDeniedForever,
                                         List<String> permissionsDenied) {
                        showMessage(R.string.denied_PDF_preview);
                    }
                })
                .request();
    }

    /**
     * @des: 长按监听
     * @params:
     * @return:
     */

    private EtPdfAdapter.OnItemLongClickListener onItemLongClickListener = new EtPdfAdapter.OnItemLongClickListener() {
        @Override
        public void onPdfModelLongClick(int position, PdfModel PdfModel, LinkedHashMap<String, Boolean> isCheckedMap, int totalSize) {
            multiSelect();
            checkSize(isCheckedMap, totalSize);
            myPdfTitle.setText(R.string.select_one_pdf);
            myPdfBottomLl.setVisibility(View.VISIBLE);
            etPdfAdapter.refreshData(true);
        }
    };
    /**
     * @des: item点击监听
     * @params:
     * @return:
     */
    private EtPdfAdapter.OnItemClickListener onItemClickListener = new EtPdfAdapter.OnItemClickListener() {
        @Override
        public void onPdfModelClick(PdfModel PdfModel, int position, CheckBox checkBox) {
            if (isMultiSelect) {
                checkBox.setChecked(!checkBox.isChecked());
            } else {
                requestPermission(PdfModel);
            }
        }
    };
    /**
     * @des: item选择监听
     * @params:
     * @return:
     */
    private EtPdfAdapter.OnItemCheckListener onItemCheckListener = new EtPdfAdapter.OnItemCheckListener() {
        @Override
        public void onItemCheck(int position, PdfModel PdfModel, LinkedHashMap<String, Boolean> isCheckedMap, int totalSize) {
            AuraMatePdfActivity.this.isCheckedMap = isCheckedMap;
            //如果选中一个 文案变为已选中1个

            pdfIds = new ArrayList<>();
            for (String id : isCheckedMap.keySet()) {
                pdfIds.add(id);
            }

            if (isCheckedMap.size() == 1) {
                myPdfTitle.setText(R.string.select_one_pdf);
                myPdfBottomLl.setVisibility(View.VISIBLE);
            } else if (isCheckedMap.size() > 1) {
                myPdfTitle.setText(String.format(getString(R.string.select_num_pdf), isCheckedMap.size() + ""));
                myPdfBottomLl.setVisibility(View.VISIBLE);
            } else {
                if (isMultiSelect) {
                    myPdfTitle.setText(String.format(getString(R.string.select_num_pdf), isCheckedMap.size() + ""));
                    myPdfBottomLl.setVisibility(View.GONE);
                }
            }
            //如果选择不是全部Item  text变为取消全选
            checkSize(isCheckedMap, totalSize);
        }
    };

    private void checkSize(LinkedHashMap<String, Boolean> isCheckedMap, int totalSize) {
        if (isCheckedMap.size() < totalSize) {
            myPdfTopSelectAllBtn.setText(R.string.select_all);
            isSelectAll = false;
        } else {
            myPdfTopSelectAllBtn.setText(R.string.not_select_all);
            isSelectAll = true;
        }
    }

    private boolean isMultiSelect = false;
    private boolean isSelectAll = false;

    /**
     * @des: 多选
     * @params:
     * @return:
     */

    private void multiSelect() {
        if (Validator.isNotEmpty(pdfModels)) {
            isMultiSelect = !isMultiSelect;
            etPdfAdapter.refreshData(isMultiSelect);
            if (isMultiSelect) {
                showSelectTopBar();
            } else {
                hideSelectTopBar();
            }
        }
    }

    /**
     * @des: 选中所有
     * @params:
     * @return:
     */

    private void selectAll() {
        if (!isSelectAll) {
            for (int i = 0; i < pdfModels.size(); i++) {

                if (!isCheckedMap.containsKey((pdfModels.get(i).getId()))) {
                    isCheckedMap.put(pdfModels.get(i).getId(), true);
                }
            }
            myPdfBottomLl.setVisibility(View.VISIBLE);
            myPdfTopSelectAllBtn.setText(R.string.not_select_all);
            isSelectAll = true;

        } else {

            myPdfBottomLl.setVisibility(View.GONE);
            isCheckedMap.clear();
            isCheckedMap = new LinkedHashMap<>();
            myPdfTopSelectAllBtn.setText(R.string.select_all);
            isSelectAll = false;
        }
        myPdfTitle.setText(String.format(getString(R.string.select_num_pdf), isCheckedMap.size() + ""));
        etPdfAdapter.refreshData(pdfModels, true, isCheckedMap);
    }

    /**
     * @des: 取消事件
     * @params:
     * @return:
     */

    private void cancelEvent() {
        isMultiSelect = false;
        isSelectAll = false;
        isCheckedMap.clear();
        isCheckedMap = new LinkedHashMap<>();
        etPdfAdapter.refreshData(pdfModels, false, isCheckedMap);
        hideSelectTopBar();
    }

    /**
     * @des: 显示选择TopBar
     * @params:
     * @return:
     */

    private void showSelectTopBar() {
        myPdfMultiSelectBtn.setVisibility(View.GONE);
        myPdfBackBtn.setVisibility(View.GONE);
        myPdfCancelBtn.setVisibility(View.VISIBLE);
        myPdfTopSelectAllBtn.setVisibility(View.VISIBLE);
        myPdfCancelBtn.setText(R.string.cancel);
        myPdfTopSelectAllBtn.setText(R.string.select_all);
        myPdfTitle.setText(String.format(getString(R.string.select_num_pdf), isCheckedMap.size() + ""));
    }

    /**
     * @des: 隐藏选择TopBar
     * @params:
     * @return:
     */

    private void hideSelectTopBar() {
        myPdfBottomLl.setVisibility(View.GONE);
        myPdfMultiSelectBtn.setVisibility(View.VISIBLE);
        myPdfBackBtn.setVisibility(View.VISIBLE);
        myPdfCancelBtn.setVisibility(View.GONE);
        myPdfTopSelectAllBtn.setVisibility(View.GONE);
        myPdfTitle.setText(R.string.my_pdf);
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            case R.id.my_pdf_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.my_pdf_cancel_btn:
                cancelEvent();
                break;
            case R.id.my_pdf_top_select_all_btn:
                selectAll();
                break;
            case R.id.my_pdf_multi_select_btn:
                multiSelect();
                break;
            case R.id.my_pdf_delete_rl:
                confirmDeleteDialog();
                break;
            default:
                break;
        }
    }

    /**
     * @des: 显示删除Dialog
     * @params:
     * @return:
     */

    private void confirmDeleteDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(AuraMatePdfActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.confirm_delete));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {

                dialog.dismiss();
                pdfDelete();
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();
    }

    private void pdfDelete() {
        HttpManager.getInstance().request().auraPdfDelete(userPreferences.getUserId(), EtUtils.transFiles(pdfIds), String.class, new MiaoHttpManager.Callback<String>() {
            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                resetCheckList();
                getRefreshPdf(true);
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                resetCheckList();
                refreshPdf();
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
                resetCheckList();
                refreshPdf();
            }
        });
    }

    private void resetCheckList() {

        pdfIds = new ArrayList<>();
        isCheckedMap.clear();
        isCheckedMap = new LinkedHashMap<>();
    }

}
