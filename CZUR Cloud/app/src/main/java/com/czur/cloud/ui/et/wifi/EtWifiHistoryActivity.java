package com.czur.cloud.ui.et.wifi;

import android.Manifest;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.location.LocationManager;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.EtHistoryNetAdapter;
import com.czur.cloud.entity.realm.EtWifiHistoryEntity;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.component.recyclerview.RecycleViewDivider;

import java.util.List;

import io.realm.Realm;
import io.realm.RealmResults;
import io.realm.Sort;

public class EtWifiHistoryActivity extends BaseActivity implements View.OnClickListener {
    private RecyclerView recyclerView;
    private ImageView imgBack, imgLock;
    private RelativeLayout rlSearchNewNet, rlCurrentNet, rlCurrentNoNet;
    private TextView tvHistoryTitle, tvCurrentName;
    private Realm realm;
    private RealmResults<EtWifiHistoryEntity> wifiHistoryList;
    private String ssid;
    private String deviceId;
    private int GPS_REQUEST_CODE = 10;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_et_wifi_history);
        initView();
        initData();
        initListener();
    }

    private void initView() {
        recyclerView = findViewById(R.id.rv_history_net);
        imgBack = findViewById(R.id.img_back);
        imgLock = findViewById(R.id.img_lock);
        rlSearchNewNet = findViewById(R.id.rl_search_new_net);
        rlCurrentNet = findViewById(R.id.rl_current_net);
        rlCurrentNoNet = findViewById(R.id.rl_current_no_net);
        tvCurrentName = findViewById(R.id.tv_current_name);
        tvHistoryTitle = findViewById(R.id.tv_history_title);

        recyclerView.setHasFixedSize(true);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        //添加自定义分割线：可自定义分割线高度和颜色
        recyclerView.addItemDecoration(new RecycleViewDivider(
                this, LinearLayoutManager.HORIZONTAL, SizeUtils.dp2px(13), getResources().getColor(R.color.white)));
    }

    private void initListener() {
        imgBack.setOnClickListener(this);
        rlCurrentNet.setOnClickListener(this);
        rlSearchNewNet.setOnClickListener(this);
        rlCurrentNoNet.setOnClickListener(this);
        initNetListener();
    }

    private void initData() {
        deviceId = getIntent().getStringExtra("deviceId");
        realm = Realm.getDefaultInstance();
        realm.refresh();
        wifiHistoryList = realm.where(EtWifiHistoryEntity.class).findAll();
        //历史列表
        if (wifiHistoryList == null || wifiHistoryList.size() == 0) {
            tvHistoryTitle.setVisibility(View.GONE);
            recyclerView.setVisibility(View.GONE);
        } else {
            tvHistoryTitle.setVisibility(View.VISIBLE);
            recyclerView.setVisibility(View.VISIBLE);
            //时间降序
            wifiHistoryList = wifiHistoryList.sort("createTime", Sort.DESCENDING);
            EtHistoryNetAdapter adapter = new EtHistoryNetAdapter(wifiHistoryList, this, deviceId);
            recyclerView.setAdapter(adapter);
        }

        if (NetworkUtils.isWifiConnected()) {
            rlCurrentNoNet.setVisibility(View.GONE);
            //获取当前wifi名称 需要适配
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (!checkGPSIsOpen()) {
                    confirmGpsDialog();
                } else {
                    registerPermission();
                }
            } else {
                setCurrentWifi();
            }
        } else {
            rlCurrentNet.setVisibility(View.GONE);
            rlCurrentNoNet.setVisibility(View.VISIBLE);
        }
    }

    private void initNetListener() {
        NetworkUtils.registerNetworkStatusChangedListener(new NetworkUtils.OnNetworkStatusChangedListener() {
            @Override
            public void onDisconnected() {

            }

            @Override
            public void onConnected(NetworkUtils.NetworkType networkType) {
                rlCurrentNoNet.setVisibility(View.GONE);
                //获取当前wifi名称 需要适配
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    if (!checkGPSIsOpen()) {
                        confirmGpsDialog();
                    } else {
                        registerPermission();
                    }
                } else {
                    setCurrentWifi();
                }
            }
        });
    }

    private void registerPermission() {
        //动态获取定位权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && checkSelfPermission(
                Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && checkSelfPermission(
                Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            requestPermissions(new String[]{Manifest.permission.ACCESS_COARSE_LOCATION, Manifest.permission.ACCESS_FINE_LOCATION},
                    100);
        } else {
            setCurrentWifi();
        }
    }

    private void setCurrentWifi() {
        WifiManager wifiManager = (WifiManager) getApplicationContext().getSystemService(WIFI_SERVICE);
        WifiInfo wifiInfo = wifiManager.getConnectionInfo();
        ssid = wifiInfo.getSSID();
        if (!TextUtils.isEmpty(ssid)) {
            if (ssid.startsWith("\"")) {
                ssid = ssid.substring(1, ssid.length());
            }
            if (ssid.endsWith("\"")) {
                ssid = ssid.substring(0, ssid.length() - 1);
            }
            tvCurrentName.setText(ssid);
            rlCurrentNet.setVisibility(View.VISIBLE);
            rlCurrentNet.setBackground(getDrawable(R.drawable.btn_rec_5_bg));
            if (checkIsCurrentWifiHasPassword(ssid)) {
                imgLock.setVisibility(View.VISIBLE);
            } else {
                imgLock.setVisibility(View.GONE);
            }

        } else {
            rlCurrentNet.setVisibility(View.GONE);
            rlCurrentNoNet.setVisibility(View.VISIBLE);
            rlCurrentNoNet.setBackground(getDrawable(R.drawable.btn_rec_5_gray_bg));
        }
    }

    private void confirmGpsDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(this, CloudCommonPopupConstants.GPS_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.gps_permission));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                Intent intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                startActivityForResult(intent, GPS_REQUEST_CODE);
                dialog.dismiss();
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                ActivityUtils.finishActivity(EtWifiHistoryActivity.this);
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();
    }


    private boolean checkIsCurrentWifiHasPassword(String currentWifiSSID) {
        try {
            WifiManager wifiManager = (WifiManager) getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            // 得到当前连接的wifi热点的信息
            WifiInfo wifiInfo = wifiManager.getConnectionInfo();
            // 得到当前WifiConfiguration列表，此列表包含所有已经连过的wifi的热点信息，未连过的热点不包含在此表中
            List<WifiConfiguration> wifiConfiguration = wifiManager.getConfiguredNetworks();
            String currentSSID = wifiInfo.getSSID();
            if (currentSSID != null && currentSSID.length() > 2) {
                if (currentSSID.startsWith("\"") && currentSSID.endsWith("\"")) {
                    currentSSID = currentSSID.substring(1, currentSSID.length() - 1);
                }
                if (wifiConfiguration != null && wifiConfiguration.size() > 0) {
                    for (WifiConfiguration configuration : wifiConfiguration) {
                        if (configuration != null && configuration.status == WifiConfiguration.Status.CURRENT) {
                            String ssid = null;
                            if (!TextUtils.isEmpty(configuration.SSID)) {
                                ssid = configuration.SSID;
                                if (configuration.SSID.startsWith("\"") && configuration.SSID.endsWith("\"")) {
                                    ssid = configuration.SSID.substring(1, configuration.SSID.length() - 1);
                                }
                            }
                            if (TextUtils.isEmpty(currentSSID) || currentSSID.equalsIgnoreCase(ssid)) {
                                //KeyMgmt.NONE表示无需密码
                                return (!configuration.allowedKeyManagement.get(WifiConfiguration.KeyMgmt.NONE));
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            //do nothing
        }
        //默认为需要连接密码
        return true;
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.rl_search_new_net:
            case R.id.rl_current_no_net:
                Intent intent = new Intent(EtWifiHistoryActivity.this, EtWifiListActivity.class);
                intent.putExtra("deviceId", deviceId);
                ActivityUtils.startActivity(intent);
                break;
            case R.id.rl_current_net:
                if (ssid.toUpperCase().contains("5G")) {
                    CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(this, CloudCommonPopupConstants.COMMON_ONE_BUTTON);
                    builder.setTitle(getResources().getString(R.string.prompt));
                    builder.setMessage(getResources().getString(R.string.only_2_4G_not_5G));
                    builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            dialog.dismiss();
                        }
                    });
                    CloudCommonPopup commonPopup = builder.create();
                    commonPopup.show();
                } else {
                    Intent intent2 = new Intent(EtWifiHistoryActivity.this, GenerateMp3Service.class);
                    intent2.putExtra("ssid", ssid);
                    EtWifiHistoryEntity etWifiHistoryEntity = realm.where(EtWifiHistoryEntity.class).equalTo("ssid", ssid).findFirst();
                    if (etWifiHistoryEntity != null) {
                        intent2.putExtra("password", etWifiHistoryEntity.getPassword());
                    }
                    intent2.putExtra("deviceId", deviceId);
                    startService(intent2);

                    Intent intent3 = new Intent(EtWifiHistoryActivity.this, WifiConnectResetActivity.class);
                    intent3.putExtra("ssid", ssid);
                    if (etWifiHistoryEntity != null) {
                        intent3.putExtra("password", etWifiHistoryEntity.getPassword());
                    }
                    intent3.putExtra("deviceId", deviceId);
                    startActivity(intent3);
                }
                break;
            case R.id.img_back:
                ActivityUtils.finishActivity(this);
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        realm.close();
    }

    /**
     * 检测GPS是否打开
     *
     * @return
     */
    private boolean checkGPSIsOpen() {
        boolean isOpen;
        LocationManager locationManager = (LocationManager) this
                .getSystemService(Context.LOCATION_SERVICE);
        isOpen = locationManager.isProviderEnabled(android.location.LocationManager.GPS_PROVIDER);
        return isOpen;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == GPS_REQUEST_CODE) {
            if (!checkGPSIsOpen()) {
                confirmGpsDialog();
            } else {
                registerPermission();
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == 100) {
            setCurrentWifi();
        }
    }

}
