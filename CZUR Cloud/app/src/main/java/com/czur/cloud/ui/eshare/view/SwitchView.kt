package com.czur.cloud.ui.eshare.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.view.View


/**
 * create by wangh 22.0322
 */
class SwitchView : View,View.OnClickListener  {

    private var isOpen = true

    private var paintOutside: Paint?=null  //空心圆
    private var paintInside: Paint?=null  //内圆
    private var paintBack: Paint?=null //轨道画笔
    private var paintCommon: Paint?=null //轨道画笔

    private val colorWhite = Color.parseColor("#FFFFFFFF")
    private val colorBlue = Color.parseColor("#FF5983EC")
    private val colorGray = Color.parseColor("#FFbbbbbb")


    private var xPrev = 0  //圆心x坐标
    private var yPrev = 0  //圆心y坐标
    private var radiusPrev = 0 //园半径


    private var switchListener: ((isOn: <PERSON><PERSON><PERSON>, fromUser: <PERSON>olean) -> Unit)? = null

    constructor(context: Context) : super(context) {
        init(context!!)
    }
    constructor(context: Context,attrs:AttributeSet) : super(context,attrs) {
        init(context!!)
    }
    constructor(context: Context,attrs:AttributeSet, def: Int) : super(context,attrs,def) {
        init(context!!)
    }

     fun init(context: Context){
        paintInside = Paint()
        paintOutside =Paint()
        paintBack = Paint()
        paintCommon = Paint()
        //关闭锯齿
        paintInside!!.isAntiAlias = true
        paintOutside!!.isAntiAlias = true
        paintBack!!.isAntiAlias = true
        paintCommon!!.isAntiAlias = true

         paintInside!!.color = colorWhite
         paintOutside!!.color = colorBlue
         paintBack!!.color = colorBlue
         paintCommon!!.color = colorBlue
        this.setOnClickListener(this)
    }

    private fun insideCir(canvas: Canvas){
        paintInside!!.style = Paint.Style.FILL
        canvas!!.drawCircle(xPrev.toFloat(),yPrev.toFloat(),(measuredHeight / 2-5).toFloat(),paintInside!!)
    }

    private fun  outsideCir(canvas: Canvas){
        paintOutside!!.style = Paint.Style.FILL
        canvas!!.drawCircle(xPrev.toFloat(),yPrev.toFloat(),(measuredHeight / 2.toFloat()),paintOutside!!)
    }

    private fun insideRect(canvas: Canvas){
        val roundHeight = measuredHeight
        val rectF = RectF(15f,15f,(measuredWidth-15).toFloat(),(roundHeight-15).toFloat())
        canvas!!.drawRoundRect(rectF,(roundHeight/2-15).toFloat(),(roundHeight/2-15).toFloat(),paintBack!!)
    }

    private fun outsideRect(canvas: Canvas){
        val roundHeight = measuredHeight
            val rectF = RectF(10f,10f,(measuredWidth-10).toFloat(),(roundHeight)-10.toFloat())
            canvas!!.drawRoundRect(rectF,(roundHeight/2-10).toFloat(),(roundHeight/2-10).toFloat(),paintCommon!!)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        if (isOpen){
            xPrev = measuredWidth - measuredHeight/2
        }else{
            xPrev = measuredHeight /2
        }
        yPrev = measuredHeight / 2
        radiusPrev = measuredHeight / 2
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        outsideRect(canvas!!)
        insideRect(canvas!!)
        //画圆点
        outsideCir(canvas!!)
        insideCir(canvas!!)
    }


     fun setDefaultSwitchState(default: Boolean){
         isOpen = default
        if (default){
            xPrev = measuredWidth - measuredHeight/2
            paintBack!!.color = colorBlue
            paintCommon!!.color = colorBlue
            paintInside!!.color = colorWhite
            paintOutside!!.color = colorBlue
        }else{
            xPrev = measuredHeight /2
            paintBack!!.color = colorWhite
            paintCommon!!.color = colorGray
            paintInside!!.color = colorWhite
            paintOutside!!.color = colorGray
        }
         invalidate()
    }



    fun setOnSwitchChange(l: (isOn: Boolean, fromClick: Boolean) -> Unit) {
        switchListener = l
    }
    override fun onClick(v: View?) {
        if(!isOpen){
            xPrev = measuredWidth - measuredHeight/2
            paintBack!!.color = colorBlue
            paintCommon!!.color = colorBlue
            paintInside!!.color = colorWhite
            paintOutside!!.color = colorBlue

        }else{
            xPrev = measuredHeight /2
            paintBack!!.color = colorWhite
            paintCommon!!.color = colorGray
            paintInside!!.color = colorWhite
            paintOutside!!.color = colorGray
        }
        isOpen = !isOpen
        switchListener?.let {
            it(isOpen, true)
        }
        invalidate()
    }
}