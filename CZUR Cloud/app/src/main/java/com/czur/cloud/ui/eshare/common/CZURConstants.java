package com.czur.cloud.ui.eshare.common;

import android.os.Environment;

import com.blankj.utilcode.util.Utils;

public class CZURConstants {
    private static final String FILE_SEP       = System.getProperty("file.separator");

    // TODO 注意：20210712需要在发布的时候关闭日志功能！！！
    public static boolean IS_DEBUG_FLAG = true;
//    public static boolean IS_DEBUG_FLAG = AppUtils.isAppDebug();

    public static final String CZUR_URL = "https://www.czur.com/cn/app/czur";

    //AuraMate
    public static final String PACKAGE_NAME = "com.czur.cloud";
    public static final String ANDROID = "android";
    public static final String RECEIVED_CONNECTED = "CONNECTED";
    public static final String RECEIVED_DONE = "DONE";
    public static final String BUSSINESS = "BIZ";
    public static final String PUSH = "PUSH";
    public static final String BIND_SUCCESS = "BindSuccess";
    public static final String CONNECT_WIFI_SUCCESS = "ConnectWiFiSuccess";
    public static final String RECEIVED_HEARTBEAT = "HEARTBEAT";


    public static final String CHECK_DEVICE_IS_ONLINE = "CheckDeviceIsOnline";
    public static final String DEVICE_ONLINE = "DeviceOnline";
    public static final String LIGHT_SWITCH = "LightSwitch";
    public static final String LIGHT_LEVEL = "LightLevel";
    public static final String LIGHT_MODE = "LightMode";
    public static final String MODE_CHANGED = "ModeChanged";
    public static final String SP_VOLUME = "SPReminderSensitivityVolume";
    public static final String SP_LEVEL = "SPReminderSensitivityLevel";
    public static final String SP_SWITCH = "SPReminderSwitch";
    public static final String SIT_WRONG_SWITCH = "SPWrongSitSwitch";
    public static final String APP_READY_FOR_VIDEO = "AppReadyForVideo";
    public static final String DEVICE_READY_FOR_VIDEO = "DeviceReadyForVideo";
    public static final String SWITCH_CAMERA = "VideoCameraSwitch";
    public static final String DEVICE_CONNECT_WIFI_SUCCESS = "ConnectWiFiSuccess";
    public static final String DEVICE_CANCEL_VIDEO = "VideoRequestCancel";
    public static final String VIDEO_CANCEL = "VideoCancel";
    public static final String CHANGE_LANGUAGE = "ChangeLanguage";
    public static final String CALIBRATE_VIDEO = "SPCalibrateVideoChat";
    public static final String CALIBRATE_PHOTO = "SPCalibrate";
    public static final String DEVICE_CHANGED_RELATIONSHIP = "DeviceRelationshipChange";
    public static final String UPDATE_FW = "updateFW";
    public static final String READY_FOR_OTA_UPDATE = "readyForOTAUpdate";
    public static final String HD_VIEW = "HDView";
    public static final String HD_VIEW_SAVE = "HDViewSave";
    public static final String HD_VIEW_SAVE_V2 = "HDViewSave_V2";
    public static final String SMART_POWER_SAVING = "SmartPowerSaving";
    public static final String SEDENTARY_REMINDER_SWITCH = "SedentaryReminderSwitch";
    public static final String SEDENTARY_REMINDER_DURATION = "SedentaryReminderDuration";
    public static final String CHECK_VIDEO_REQUEST_ACTIVE = "CheckVideoRequestActive";

    // Starry Meeting Module
    public static final String STARRY_MODULE = "Starry";

    public static final String ID = "id";
    public static final String EMPTY = "";
    public static final String CHANNEL = "CN";
    public static final String ENDPOINT = "oss-cn-beijing.aliyuncs.com";

    public static final String CLOUD_ANDROID = "czur_cloud_android";
    public static final String CLOUD_ANDROID_CN = "成者CZUR";
    public static final String BUCKET = "changer-weilian";
    public static final String MIRROR_BUCKET = "czur-mirror";
    public static final String APP_STYLE = "CZUR";
    public static final String QQ = "腾讯QQ";
    public static final String WECHAT = "微信";
    public static final String WEIBO = "微博";
    public static final String GOOGLE_OCR = "-t-i0-handwrit";
    public static final String[] LANGUAGE = {"English","简体中文","繁體中文","한국어",
            "日本語", "русский язык", "Deutsch",
            "Français", "Español", "Italiano",
            "Português", "Người việt nam"};
    public static final String[] GOOGLE_LANGUAGE = {"en","zh","zh", "ko",
            "ja", "ru", "de",
            "fr", "es", "it",
            "pt", "vi"};
    public static final String CLOUD_VISION_API_KEY = "AIzaSyATzdzzESYjGEI3iWsv6ebL12BSBC3Hqsw";
    public static final String ANDROID_CERT_HEADER = "X-Android-Cert";
    public static final String ANDROID_PACKAGE_HEADER = "X-Android-Package";

    //手写识别API
    public static final int HANDWRITING_APP_ID = 1256326589;
    public static final int HANDWRITING_EXPIRED = 2592000;
    public static final String HANDWRITING_BUCKET_NAME = "tencentyun";
    public static final String HANDWRITING_HOST = "recognition.image.myqcloud.com";
    public static final String HANDWRITING_URL = "http://recognition.image.myqcloud.com/ocr/handwriting";
    public static final String HANDWRITING_SECRET_ID = "AKIDPtDQo9MPTM4e4JvLDuX5Wr5EII9xD9c8";
    public static final String HANDWRITING_SECRET_KEY = "gCfzEMJ5gIoAORIyYxCHBtQl9UffUDmA";
    public static final String BAIDU_TOKEN_URL = "https://aip.baidubce.com/oauth/2.0/token";
    public static final String BAIDU_OCR_HAND_URL = "https://aip.baidubce.com/rest/2.0/ocr/v1/handwriting";
    public static final String BAIDU_OCR_GENERAL_URL = "https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic";
    public static final String BAIDU_OCR_ACCURATE_URL = "https://aip.baidubce.com/rest/2.0/ocr/v1/accurate_basic";

    public static final String BAIDU_CLIENT_ID = "3XLxTYOCoS1O5klE1GYMayUS";
    public static final String BAIDU_GRANT_TYPE = "client_credentials";
    public static final String BAIDU_CLIENT_SECRET = "qgalyzTM2cPDoqskGfvcy0V0q2wDrvAf";

    public static final String JPG = ".jpg";
    public static final String MP3 = ".mp3";
    public static final String TEMP = "/temp";
    public static final String SD_TEMP = "temp/";
    public static final String SMALL_JPG = "small.jpg";
    public static final String PDF = ".pdf";
    public static final String OSS_PDF_PATH = "/PDF/";
    public static final String PAGE_PATH = "/book/";
    public static final String ET_PATH = "et/";
    public static final String AURA_MATE_PATH = "auramate/";
    public static final String PDF_PATH = "pdf/";
    public static final String SYNC_PATH = "sync/";
    public static final String OSS_SERVER_PATH = "/Books/";
    public static final String APK_PATH = "apk/";
    public static final String APK_FULL_PATH = "/CZUR/apk/";

//    public static final String SD_PATH = "/CZUR/";
    public static final String SD_PATH = Utils.getApp().getExternalFilesDir("") + "/CZUR/";
    public static final String FILEPROVIEDER = "com.czur.cloud.fileProvider";
    public static final String TAKE_PHOTO_INIT_TIME = "1994-06-07 08:08:08";
    public static final String SYNC_URL = "v3/books/synchronous";
    public static final String FILE_SIZE_URL = "v3/books/getFileSize";
    public static final String FILE_PDF_SIZE_URL = "v3/books/getPdfSize";
    public static final String FEEDBACK_URL = "czur_scanner/v1/uploadFailureFiles.do";
    public static final String CHANNEL_ID_DEFAULT = "DEFAULT";
    public static final String SYNC_MIRROR_URL = "v3/mirror/addTimeUseReport";
    public static final String SYNC_MIRROR_ALGO_URL = "v3/mirror/addSpContent";

    public static final String SYNC_MIRROR_PIC_URL = "v3/mirror/addImgData";
    public static final String MIRROR_PATH = CZURConstants.SD_PATH + "mirror/";
    public static final String DOWNLOAD_PATH = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).getAbsolutePath() + FILE_SEP;
//    public static final String PICTURE_PATH = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES).getAbsolutePath() + FILE_SEP;
    public static final String PICTURE_PATH = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM).getAbsolutePath() + FILE_SEP;
    public static final String DOCUMENT_PATH = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS).getAbsolutePath() + FILE_SEP;

    public static final int SEGMENTTITLESIZE = 18;
    public static final int PWD_MIN_LENGTH = 7;   // 密码最小长度

    //隐私协议
    public static final String PRIVACY_AGREEMENT = "https://privacy.czur.cc/privacy";
    //成者CZUR用户协议
    public static final String PRIVACY_AGREEMENT1 = "https://passport.czur.cc/terms_CZUR.html?timeStep=";
    //成者CZUR隐私政策：
    public static final String PRIVACY_AGREEMENT2 = "https://passport.czur.cc/privacy_CZUR.html?timeStep=";
    // 个人信息收集清单
    public static final String PRIVACY_AGREEMENT3 = "https://passport.czur.cc/checklist.html?timeStep=";
    // 第三方共享信息清单
    public static final String PRIVACY_AGREEMENT4 = "https://passport.czur.cc/3dsharelist.html?timeStep=";

}