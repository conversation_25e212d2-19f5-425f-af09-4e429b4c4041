package com.czur.cloud.ui.starry.meeting.agora

import android.view.View
import android.view.ViewGroup

/**
 */
class DisplayMainModel : HashMap<String, View?>() {
    companion object {
        private const val TAG = "DisplayMainModel"
    }

    fun getOrPut(uid: String, block: () -> View?): View? {
        val surface = get(uid)
        return if (surface == null && containsKey(uid)) {
            // 有这个ID, 但是还没有初始化
            val answer = block()
            answer?.let {
                put(uid, it)
            }
            answer
        } else {
            surface
        }
    }

    /**
     * 释放对应UID的SurfaceView
     */
    fun release(key: String): View? {
        val result = super.remove(key)
        result?.let {
            val parent = it.parent
            if (parent != null) {
                (parent as ViewGroup).removeView(it)
            }
            it.visibility = ViewGroup.GONE
        }
        return result
    }
}