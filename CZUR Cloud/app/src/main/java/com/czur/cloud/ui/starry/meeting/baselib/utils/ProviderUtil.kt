package com.czur.cloud.ui.starry.meeting.baselib.utils

import android.content.ContentResolver
import android.content.ContentValues
import android.content.UriMatcher
import android.database.Cursor
import android.database.MatrixCursor
import android.net.Uri
import android.os.Parcel
import android.os.Parcelable
import android.util.Log
import java.io.ByteArrayOutputStream
import java.io.ObjectInputStream
import java.io.ObjectOutputStream
import java.io.Serializable

/**
 * Created by 陈丰尧 on 3/31/21
 */
private const val TAG = "ProviderUtil"


// 用于表示结果的path
val PATH_RESULT_FALSE = PathWrapper("resultFalse", 100)
val PATH_RESULT_TRUE = PathWrapper("resultTrue", 101)
const val CONTENT = "content" //协议头

// 指定列名, 为了二进制数据使用的
const val COLUMN_NAME_OBJ_BLOB = "objBlob"

// cursor是String
const val COLUMN_NAME_STRING = "colString"


/**
 * 包装了urlMatcher的path和code, 其实跟Pair类一样,语义化了变量名
 */
class PathWrapper(val path: String, val code: Int, val tag: String = path) : Parcelable {


    constructor(parcel: Parcel) : this(
        parcel.readString() ?: "",
        parcel.readInt(),
        parcel.readString() ?: ""
    )

    override fun toString(): String {
        return "PathWrapper(path='$path', code=$code)"
    }

    override fun equals(other: Any?): Boolean {
        // equals 方法不需要考虑tag
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as PathWrapper

        if (path != other.path) return false
        if (code != other.code) return false

        return true
    }

    override fun hashCode(): Int {
        var result = path.hashCode()
        result = 31 * result + code
        return result
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(path)
        parcel.writeInt(code)
        parcel.writeString(tag)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<PathWrapper> {
        override fun createFromParcel(parcel: Parcel): PathWrapper {
            return PathWrapper(parcel)
        }

        override fun newArray(size: Int): Array<PathWrapper?> {
            return arrayOfNulls(size)
        }
    }


}


/***** 简化ContentResolver的相关方法 *****/
fun ContentResolver.query(
    authority: String,
    pathWrapper: PathWrapper,
    vararg params: Pair<String, String?> = arrayOf()
): Cursor? =
    query(makeUri(authority, pathWrapper, *params), null, null, null, null)

fun ContentResolver.update(
    authority: String,
    pathWrapper: PathWrapper,
    cv: ContentValues
): Boolean {
    Log.d(TAG, "ContentResolver.update: ${pathWrapper.path}")
    val result = update(makeUri(authority, pathWrapper), cv, null, null)
    Log.d(TAG, "ContentResolver.update result: $result")
    return result == PATH_RESULT_TRUE.code
}

fun ContentResolver.delete(uri: Uri) =
    delete(uri, null, null) == PATH_RESULT_TRUE.code


/**
 * 根据Path生成不同的Uri
 */
fun makeUri(
    authority: String,
    pathWrapper: PathWrapper,
    vararg params: Pair<String, String?> = arrayOf()
): Uri {
    val builder = Uri.Builder()
        .scheme(CONTENT)
        .authority(authority)
        .appendPath(pathWrapper.path)
    params.forEach { param ->
        param.second?.let {
            // 添加参数
            builder.appendQueryParameter(param.first, it)
        }
    }

    return builder.build()
}

fun makeUri(authority: String, boolean: Boolean) =
    makeUri(authority, getBooleanPathWrapper(boolean))

fun getBooleanPathWrapper(boolean: Boolean) = if (boolean) {
    PATH_RESULT_TRUE
} else {
    PATH_RESULT_FALSE
}

/**
 * 通过字节数组将entity转换成Cursor对象
 */
fun <T : Serializable> entityToCursorBlob(entity: T): Cursor {
    val cursor = MatrixCursor(arrayOf(COLUMN_NAME_OBJ_BLOB))
    val bos = ByteArrayOutputStream()
    ObjectOutputStream(bos).use { oos ->
        oos.writeObject(entity)
        oos.flush()
    }
    cursor.addRow(arrayOf(bos.toByteArray()))
    return cursor
}

/**
 * 通过字节数组将entityList转换成Cursor对象
 */
inline fun <reified T : Serializable> entityToCursorBlob(entities: List<T>): Cursor {
    if (entities.isEmpty()) {
        return MatrixCursor(arrayOf())
    }

    val cursor = MatrixCursor(arrayOf(COLUMN_NAME_OBJ_BLOB))
    entities.forEach {
        val bos = ByteArrayOutputStream()
        ObjectOutputStream(bos).use { oos ->
            oos.writeObject(it)
            oos.flush()
        }
        cursor.addRow(arrayOf(bos.toByteArray()))
    }
    return cursor
}

/**
 * 将String变成Cursor
 */
fun stringToCursor(string: String): Cursor {
    val cursor = MatrixCursor(arrayOf(COLUMN_NAME_STRING))
    cursor.addRow(arrayOf(string))
    return cursor
}

/**
 * 将Boolean类型变成Cursor
 */
fun booleanToCursor(boolean: Boolean): Cursor {
    val booleanStr = boolean.toString()
    return stringToCursor(booleanStr)
}

/**
 * 从Cursor中获取String数据
 */
fun Cursor.getString(autoToFirst: Boolean = true): String {
    if (autoToFirst) {
        moveToFirst()
    }
    return getString(getColumnIndex(COLUMN_NAME_STRING))
}

/**
 * 从Cursor中获取Boolean类型数据
 */
fun Cursor.getBoolean(autoToFirst: Boolean = true): Boolean {
    val booleanStr = getString()
    return booleanStr.toBoolean()
}

/**
 * Cursor中获取Int类型数据
 */
fun Cursor.getInt(autoToFirst: Boolean = true): Int {
    val intStr = getString(autoToFirst)
    return intStr.toInt()
}

/**
 * Cursor中获取Long类型数据
 */
fun Cursor.getLong(autoToFirst: Boolean = true):Long {
    val longStr = getString(autoToFirst)
    return longStr.toLong()
}

/**
 * Cursor中获取Float类型数据
 */
fun Cursor.getFloat(autoToFirst: Boolean = true):Float{
    val floatStr = getString(autoToFirst)
    return floatStr.toFloat()
}

/**
 * Cursor中获取Double类型数据
 */
fun Cursor.getDouble(autoToFirst: Boolean = true):Double{
    val doubleStr = getString(autoToFirst)
    return doubleStr.toDouble()
}

inline fun <reified T> Cursor.toEntityBlob(): T {
    moveToFirst()
    val objBlob = getBlob(getColumnIndex(COLUMN_NAME_OBJ_BLOB))
    return ObjectInputStream(objBlob.inputStream()).use {
        it.readObject() as T

    }
}

fun <T> Cursor.toListBlob(): MutableList<T> {
    val result = mutableListOf<T>()
    while (moveToNext()) {
        val objBlob = getBlob(getColumnIndex(COLUMN_NAME_OBJ_BLOB))
        ObjectInputStream(objBlob.inputStream()).use {
            val item = it.readObject() as T
            result.add(item)
        }
    }
    return result
}

private const val BOOLEAN_AUTHORITY = "com.czur.starry.device.boolean"
private val booleanMatcher: UriMatcher by lazy {
    UriMatcher(UriMatcher.NO_MATCH).apply {
        addURI(BOOLEAN_AUTHORITY, PATH_RESULT_FALSE.path, PATH_RESULT_FALSE.code)
        addURI(BOOLEAN_AUTHORITY, PATH_RESULT_TRUE.path, PATH_RESULT_TRUE.code)
    }
}

fun Uri?.getBooleanResult(): Boolean = when (booleanMatcher.match(this)) {
    PATH_RESULT_TRUE.code -> true
    PATH_RESULT_FALSE.code -> false
    else -> {
        Log.i(TAG, "无法从uri解析出Boolean结果: ${this},使用[false]作为结果")
        false
    }
}

fun Boolean.toUri(vararg params: Pair<String, String?> = arrayOf()): Uri {
    return makeUri(BOOLEAN_AUTHORITY, getBooleanPathWrapper(this), *params)
}


