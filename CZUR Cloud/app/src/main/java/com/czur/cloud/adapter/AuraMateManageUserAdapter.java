package com.czur.cloud.adapter;

import android.app.Activity;
import android.net.Uri;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.czur.cloud.R;
import com.czur.cloud.model.AuraMateShareUserModel;
import com.czur.cloud.preferences.UserPreferences;
import com.facebook.drawee.view.SimpleDraweeView;

import java.util.List;

/**
 * Created by Yz on 2018/3/16
 * Email：<EMAIL>
 */


public class AuraMateManageUserAdapter extends RecyclerView.Adapter<ViewHolder> {


    private static final int ITEM_TYPE_NORMA = 0;
    private Activity mActivity;
    //当前需要显示的所有的图片数据
    private List<AuraMateShareUserModel> datas;

    private LayoutInflater mInflater;
    private boolean isShare;
    private final UserPreferences userPreferences;

    /**
     * 构造方法
     */
    public AuraMateManageUserAdapter(Activity activity, boolean isShare, List<AuraMateShareUserModel> datas) {
        this.isShare = isShare;
        this.mActivity = activity;
        this.datas = datas;
        userPreferences = UserPreferences.getInstance(activity);
        mInflater = LayoutInflater.from(activity);
    }

    public void refreshData(List<AuraMateShareUserModel> datas) {
        this.datas = datas;
        notifyDataSetChanged();

    }


    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return new NormalViewHolder(mInflater.inflate(R.layout.item_aura_mate_manage_share_user, parent, false));
    }


    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {

        if (holder instanceof NormalViewHolder) {
            final NormalViewHolder mHolder = (NormalViewHolder) holder;
            mHolder.mItem = datas.get(position);
            mHolder.etUserHeadName.setText(mHolder.mItem.getMemberName());
            Uri uri;
            if (TextUtils.isEmpty(mHolder.mItem.getMemberPhoto())) {
                mHolder.etUserHeadImg.setImageResource(R.mipmap.user_default_icon);
            } else {
                mHolder.etUserHeadImg.setImageURI(mHolder.mItem.getMemberPhoto());
            }
            if (mHolder.mItem.getOwnerId() == mHolder.mItem.getMemberId()) {
                mHolder.operateBtn.setVisibility(View.VISIBLE);
                mHolder.operateBtn.setText(mActivity.getString(R.string.administrator));
                mHolder.operateBtn.setTextColor(mActivity.getResources().getColor(R.color.blue_29b0d7));
            } else {
                if (isShare) {
                    mHolder.operateBtn.setText(mActivity.getString(R.string.quit));
                    if (mHolder.mItem.getMemberId() == Integer.valueOf(userPreferences.getUserId())) {
                        mHolder.operateBtn.setVisibility(View.VISIBLE);

                    } else {
                        mHolder.operateBtn.setVisibility(View.GONE);}

                } else {
                    mHolder.operateBtn.setText(mActivity.getString(R.string.remove));
                }


                mHolder.operateBtn.setTextColor(mActivity.getResources().getColor(R.color.red_de4d4d));
                mHolder.operateBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (onItemClickListener != null) {
                            onItemClickListener.onItemClick(position, mHolder.mItem);
                        }
                    }
                });
            }


        }
    }


    @Override
    public int getItemViewType(int position) {
        return ITEM_TYPE_NORMA;
    }


    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return datas.size();
    }


    private static class NormalViewHolder extends ViewHolder {
        public final View mView;
        AuraMateShareUserModel mItem;
        SimpleDraweeView etUserHeadImg;
        TextView etUserHeadName;
        TextView operateBtn;

        NormalViewHolder(View itemView) {
            super(itemView);
            mView = itemView;
            etUserHeadImg = (SimpleDraweeView) itemView.findViewById(R.id.et_user_head_img);
            etUserHeadName = (TextView) itemView.findViewById(R.id.et_user_head_name);


            operateBtn = (TextView) itemView.findViewById(R.id.operate_btn);

        }


    }


    private onItemClickListener onItemClickListener;

    public void setOnItemClickListener(onItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface onItemClickListener {
        void onItemClick(int position, AuraMateShareUserModel AuraMateShareUserModel);
    }


}
