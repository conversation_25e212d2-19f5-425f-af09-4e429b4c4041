package com.czur.cloud.ui.books.drag;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.appcompat.app.AppCompatActivity;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.czur.cloud.R;
import com.czur.cloud.entity.realm.PageEntity;

import java.util.ArrayList;
import java.util.List;

import io.realm.Realm;
import io.realm.RealmResults;
import io.realm.Sort;

public class DragPhotoActivity extends AppCompatActivity {
    private ViewPager mViewPager;
    private List<String> mList;
    private DragPhotoView[] mPhotoViews;

    int mOriginLeft;
    int mOriginTop;
    int mOriginHeight;
    int mOriginWidth;
    int mOriginCenterX;
    int mOriginCenterY;
    private float mTargetHeight;
    private float mTargetWidth;
    private float mScaleX;
    private float mScaleY;
    private float mTranslationX;
    private float mTranslationY;
    private float releasedAlpha;
    private Realm realm;
    private String bookId;
    private int firstPosition;
    private View view1;
    private View view2;
    private int DURATION = 300;
    private int currentPosition = 0;
    private List<Integer> lefts;
    private List<Integer> tops;
    private LinearLayout pagePreviewBottomLl;
    private RelativeLayout pagePreviewTopRl;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_drag_photo);
        realm = Realm.getDefaultInstance();
        bookId = getIntent().getStringExtra("bookId");
        firstPosition = getIntent().getIntExtra("position", 0);
        lefts = getIntent().getIntegerArrayListExtra("lefts");
        tops = getIntent().getIntegerArrayListExtra("tops");



        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            getWindow().setStatusBarColor(getResources().getColor(R.color.black));
        }
        mViewPager = (ViewPager) findViewById(R.id.viewpager);
        mList = new ArrayList<>();
        realm.executeTransaction(new Realm.Transaction() {
            @Override
            public void execute(Realm realms) {
                //先查找后得到BookEntity对象
                RealmResults<PageEntity> entities = realm.where(PageEntity.class).equalTo("bookId", bookId).findAll().sort("pageNum", Sort.ASCENDING);
                for (int i = 0; i < entities.size(); i++) {
                    mList.add(entities.get(i).getPicUrl());
                }

            }
        });


        pagePreviewBottomLl = (LinearLayout) findViewById(R.id.page_preview_bottom_ll);
        pagePreviewTopRl=(RelativeLayout)findViewById(R.id.page_preview_top_bar);

        mPhotoViews = new DragPhotoView[mList.size()];

        for (int i = 0; i < mPhotoViews.length; i++) {
            mPhotoViews[i] = (DragPhotoView) View.inflate(this, R.layout.item_viewpager, null);
            mPhotoViews[i].setImageURI(Uri.parse(mList.get(i)));

            mPhotoViews[i].setOnExitListener(new DragPhotoView.OnExitListener() {
                @Override
                public void onExit(DragPhotoView view, float x, float y, float w, float h, float scale) {
                     pagePreviewBottomLl.setVisibility(View.GONE);
                     pagePreviewTopRl.setVisibility(View.GONE);
                    performExitAnimation(view, x, y, w, h, scale);
                }
            });

            mPhotoViews[i].setOnAlphaChangedListener(new DragPhotoView.OnAlphaChangedListener() {
                @Override
                public void onChanged(float mAlpha) {
                    releasedAlpha=mAlpha;
                    pagePreviewBottomLl.setAlpha(mAlpha);
                    pagePreviewTopRl.setAlpha(mAlpha);
                }
            });
            mPhotoViews[i].setOnCancelAlphaChangedListener(new DragPhotoView.OnCancelAlphaChangedListener() {
                @Override
                public void onChanged(float percent) {
                    pagePreviewBottomLl.setAlpha(releasedAlpha+(1-releasedAlpha)*percent);
                    pagePreviewTopRl.setAlpha(releasedAlpha+(1-releasedAlpha)*percent);
                }
            });
        }

        mViewPager.setAdapter(adapter);
        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                currentPosition = position;
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });


        mViewPager.getViewTreeObserver()
                .addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                    @Override
                    public void onGlobalLayout() {
                        mViewPager.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                        mOriginLeft = getIntent().getIntExtra("left", 0);
                        mOriginTop = getIntent().getIntExtra("top", 0);
                        mOriginHeight = getIntent().getIntExtra("height", 0);
                        mOriginWidth = getIntent().getIntExtra("width", 0);
                        mOriginCenterX = mOriginLeft + mOriginWidth / 2;
                        mOriginCenterY = mOriginTop + mOriginHeight / 2;


                        int[] location = new int[2];

                        final DragPhotoView photoView = mPhotoViews[currentPosition];
                        photoView.getLocationOnScreen(location);

                        mTargetHeight = (float) photoView.getHeight();
                        mTargetWidth = (float) photoView.getWidth();

//                        Log.e("TAG", "mTargetHeight:" + mTargetHeight + "      mTargetWidth:" + mTargetWidth);

                        mScaleX = (float) mOriginWidth / mTargetWidth;
                        mScaleY = (float) mOriginHeight / mTargetHeight;

                        float targetCenterX = location[0] + mTargetWidth / 2;
                        float targetCenterY = location[1] + mTargetHeight / 2;


                        mTranslationX = mOriginCenterX - targetCenterX;
                        mTranslationY = mOriginCenterY - targetCenterY;
                        photoView.setTranslationX(mTranslationX);
                        photoView.setTranslationY(mTranslationY);

                        photoView.setScaleX(mScaleX);
                        photoView.setScaleY(mScaleY);

                        performEnterAnimation();

                        for (int i = 0; i < mPhotoViews.length; i++) {
                            mPhotoViews[i].setMinScale(mScaleX);
                        }
                    }
                });
        mViewPager.setCurrentItem(firstPosition);



    }


    private PagerAdapter adapter = new PagerAdapter() {
        @Override
        public int getCount() {
            return mList.size();
        }

        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            container.addView(mPhotoViews[position]);
            return mPhotoViews[position];
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            container.removeView(mPhotoViews[position]);
        }

        @Override
        public boolean isViewFromObject(View view, Object object) {
            return view == object;
        }
    };



    /**
     * ===================================================================================
     * <p>
     * 底下是低版本"共享元素"实现   不需要过分关心  如有需要 可作为参考.
     * <p>
     * Code  under is shared transitions in all android versions implementation
     */
    private void performExitAnimation(final DragPhotoView view, float mTranslateX, float mTranslateY, float w, float h, float scale) {
        final float viewX1 = view.getX();
        final float viewY1 = view.getY();

        mOriginLeft = lefts.get(currentPosition);
        mOriginTop = tops.get(currentPosition);
        mOriginHeight = getIntent().getIntExtra("height", 0);
        mOriginWidth = getIntent().getIntExtra("width", 0);
        mOriginCenterX = mOriginLeft + mOriginWidth / 2;
        mOriginCenterY = mOriginTop + mOriginHeight / 2;

        Log.e("TAG", "1_view.getX()" + view.getX() + "    1_view.getLeft:" + view.getLeft());
        view.finishAnimationCallBack();
        float viewX = mTargetWidth / 2 + mTranslateX - mTargetWidth * scale / 2;
        float viewY = mTargetHeight / 2 + mTranslateY - mTargetHeight * scale / 2;
        view.setX(viewX);
        view.setY(viewY);

        float centerX = view.getX() + mTargetWidth / 2;
        float centerY = view.getY() + mTargetHeight/2;

        Log.e("TAG", "centerX:" + centerX + "   viewX:" + view.getX() + "  viewWidth:" + view.getWidth());

        float translateX = mOriginCenterX + (mTargetWidth * mScaleX / scale - mOriginWidth) / 2 - centerX;
        float translateY = mOriginCenterY + (mTargetHeight * mScaleY / scale - mOriginHeight) / 2 - centerY-mOriginHeight;

//        Log.e("TAG", "translateX:" + translateX + "  translateY:" + translateY);

//        float translateX = mOriginLeft - view.getX();
//        float translateY = mOriginTop - view.getY();

        Log.e("TAG", "view.getX():" + view.getX() + "    view.getLeft:" + view.getLeft());

        final DragPhotoView photoView = mPhotoViews[0];

        ValueAnimator translateXAnimator = ValueAnimator.ofFloat(view.getX(), view.getX() + translateX);
        translateXAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                view.setX((Float) valueAnimator.getAnimatedValue() + viewX1);
            }
        });
        translateXAnimator.setDuration(DURATION);
        translateXAnimator.start();

        ValueAnimator translateYAnimator = ValueAnimator.ofFloat(view.getY(), view.getY() + translateY);
        translateYAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {

                view.setY((Float) valueAnimator.getAnimatedValue() + viewY1);
            }
        });

        translateYAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animator) {

            }

            @Override
            public void onAnimationEnd(Animator animator) {
                Log.e("TAG:", "view.getX():" + view.getX() + "  viewWidth:" + view.getWidth());
                animator.removeAllListeners();
                finish();
                overridePendingTransition(0, 0);
            }

            @Override
            public void onAnimationCancel(Animator animator) {

            }

            @Override
            public void onAnimationRepeat(Animator animator) {

            }
        });


        translateYAnimator.setDuration(DURATION);
        translateYAnimator.start();

        ValueAnimator scaleYAnimator = ValueAnimator.ofFloat(1, mScaleY / scale);
        scaleYAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                view.setScaleY((Float) valueAnimator.getAnimatedValue());
            }
        });
        scaleYAnimator.setDuration(DURATION);
        scaleYAnimator.start();

        ValueAnimator scaleXAnimator = ValueAnimator.ofFloat(1, mScaleX / scale);
        scaleXAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                view.setScaleX((Float) valueAnimator.getAnimatedValue());
            }
        });

        scaleXAnimator.setDuration(DURATION);
        scaleXAnimator.start();


//


    }


    private void finishWithAnimation(final DragPhotoView photoView) {
        final float photoViewX = photoView.getX();


        ValueAnimator translateXAnimator = ValueAnimator.ofFloat(0, mTranslationX);
        translateXAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
//                Log.e(TAG, "onAnimationUpdate: " + (Float) valueAnimator.getAnimatedValue());
                photoView.setX((Float) valueAnimator.getAnimatedValue() + photoViewX);
            }
        });
        translateXAnimator.setDuration(DURATION);
        translateXAnimator.start();

        ValueAnimator translateYAnimator = ValueAnimator.ofFloat(0, mTranslationY);
        translateYAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                photoView.setY((Float) valueAnimator.getAnimatedValue());
            }
        });
        translateYAnimator.setDuration(DURATION);
        translateYAnimator.start();

        ValueAnimator scaleYAnimator = ValueAnimator.ofFloat(1, mScaleY);
        scaleYAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                photoView.setScaleY((Float) valueAnimator.getAnimatedValue());
            }
        });
        scaleYAnimator.setDuration(DURATION);
        scaleYAnimator.start();

        ValueAnimator scaleXAnimator = ValueAnimator.ofFloat(1, mScaleX);
        scaleXAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                photoView.setScaleX((Float) valueAnimator.getAnimatedValue());
            }
        });

        scaleXAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animator) {

            }

            @Override
            public void onAnimationEnd(Animator animator) {
                animator.removeAllListeners();
                finish();
                overridePendingTransition(0, 0);
            }

            @Override
            public void onAnimationCancel(Animator animator) {

            }

            @Override
            public void onAnimationRepeat(Animator animator) {

            }
        });
        scaleXAnimator.setDuration(DURATION);
        scaleXAnimator.start();
    }

    private void performEnterAnimation() {
        final DragPhotoView photoView = mPhotoViews[currentPosition];
        Log.e("TAG", "photoView.getX():" + photoView.getX() + "    photoView.getY():" + photoView.getY());
        ValueAnimator translateXAnimator = ValueAnimator.ofFloat(photoView.getX(), 0);
        translateXAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                photoView.setX((Float) valueAnimator.getAnimatedValue());
            }
        });
        translateXAnimator.setDuration(300);
        translateXAnimator.start();

        ValueAnimator translateYAnimator = ValueAnimator.ofFloat(photoView.getY(), 0);
        translateYAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                photoView.setY((Float) valueAnimator.getAnimatedValue());
            }
        });
        translateYAnimator.setDuration(300);
        translateYAnimator.start();

        ValueAnimator scaleYAnimator = ValueAnimator.ofFloat(mScaleY, 1);
        scaleYAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                pagePreviewBottomLl.setAlpha((Float) valueAnimator.getAnimatedValue());
                pagePreviewTopRl.setAlpha((Float) valueAnimator.getAnimatedValue());
                photoView.setScaleY((Float) valueAnimator.getAnimatedValue());
            }
        });
        scaleYAnimator.setDuration(300);
        scaleYAnimator.start();

        ValueAnimator scaleXAnimator = ValueAnimator.ofFloat(mScaleX, 1);
        scaleXAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                photoView.setScaleX((Float) valueAnimator.getAnimatedValue());
            }
        });
        scaleXAnimator.setDuration(300);
        scaleXAnimator.start();
    }


    @Override
    public void onBackPressed() {

        finish();
    }
}
