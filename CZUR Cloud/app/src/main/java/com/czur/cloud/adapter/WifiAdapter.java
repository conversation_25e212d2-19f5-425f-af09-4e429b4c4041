package com.czur.cloud.adapter;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.czur.cloud.R;
import com.czur.cloud.entity.WifiEntity;

import java.util.List;

/**
 * Created by Yz on 2018/3/16
 * Email：<EMAIL>
 */


public class WifiAdapter extends RecyclerView.Adapter<ViewHolder> {


    private static final int ITEM_TYPE_NORMA = 0;
    private Activity mActivity;
    //当前需要显示的所有的图片数据
    private List<WifiEntity> datas;

    private LayoutInflater mInflater;

    /**
     * 构造方法
     */
    public WifiAdapter(Activity activity, List<WifiEntity> datas) {

        this.mActivity = activity;
        this.datas = datas;
        mInflater = LayoutInflater.from(activity);
    }

    public void refreshData(List<WifiEntity> datas) {
        this.datas = datas;
        notifyDataSetChanged();

    }


    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return new NormalViewHolder(mInflater.inflate(R.layout.item_wifi, parent, false));
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {

        if (holder instanceof NormalViewHolder) {
            final NormalViewHolder mHolder = (NormalViewHolder) holder;
            mHolder.mItem = datas.get(position);
            mHolder.wifiSsidTv.setText(mHolder.mItem.getSsid());
            mHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onItemClickListener != null) {
                        onItemClickListener.onItemClick(position, mHolder.mItem);
                    }
                }
            });

        }
    }


    @Override
    public int getItemViewType(int position) {

        return ITEM_TYPE_NORMA;

    }


    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return datas.size();
    }


    private class NormalViewHolder extends ViewHolder {
        public final View mView;
        WifiEntity mItem;
        TextView wifiSsidTv;


        NormalViewHolder(View itemView) {
            super(itemView);
            mView = itemView;
            wifiSsidTv = (TextView) itemView.findViewById(R.id.wifi_ssid_tv);

        }


    }


    private onItemClickListener onItemClickListener;

    public void setOnItemClickListener(onItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface onItemClickListener {
        void onItemClick(int position, WifiEntity WifiEntity);
    }


}
