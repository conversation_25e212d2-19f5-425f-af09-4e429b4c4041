package com.czur.cloud.ui.eshare.widget;
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.view.View
import com.czur.cloud.R


class TransmitFileProgressBar : View {
    private var mProgressPaint: Paint? = null
    private var mPaintRoundRect: Paint? = null
    private var mWidth = 0
    private var mHeight = 0
    private var padding = 5
    private var round = 0
    private var process = 0f

    constructor(context: Context?) : super(context) {
        init()
    }

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        init()
    }

    fun setProgress(process: Float) {
        if (process > 100) {
            return
        }
        this.process = process
        invalidate()
    }


    //初始化画笔
    private fun init() {
        mPaintRoundRect = Paint() //圆角矩形
        mPaintRoundRect!!.color = resources.getColor(R.color.eshare_5983EC) //圆角矩形颜色
        mPaintRoundRect!!.isAntiAlias = true // 抗锯齿效果
        mPaintRoundRect!!.style = Paint.Style.FILL //设置画笔样式
        mProgressPaint = Paint()
        mProgressPaint!!.color = resources.getColor(R.color.white)
        mProgressPaint!!.style = Paint.Style.FILL
        mProgressPaint!!.isAntiAlias = true
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val widthSpecMode = MeasureSpec.getMode(widthMeasureSpec)
        val heightSpecMode = MeasureSpec.getMode(heightMeasureSpec)
        val widthSpecSize = MeasureSpec.getSize(widthMeasureSpec)
        val heightSpecSize = MeasureSpec.getSize(heightMeasureSpec)

        //MeasureSpec.EXACTLY，精确尺寸
        mWidth = if (widthSpecMode == MeasureSpec.EXACTLY || widthSpecMode == MeasureSpec.AT_MOST) {
            widthSpecSize
        } else {
            0
        }
        //MeasureSpec.AT_MOST，最大尺寸，只要不超过父控件允许的最大尺寸即可，MeasureSpec.UNSPECIFIED未指定尺寸
        mHeight =
            if (heightSpecMode == MeasureSpec.AT_MOST || heightSpecMode == MeasureSpec.UNSPECIFIED) {
                defaultHeight()
            } else {
                heightSpecSize
            }
        //设置控件实际大小
        round = mHeight / 2 //圆角半径
        setMeasuredDimension(mWidth, mHeight)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        drawBackground(canvas) //绘制背景矩形
        drawProgress(canvas) //绘制进度
    }

    private fun drawBackground(canvas: Canvas) {
        val rectF = RectF(
            0f,
            0f,
            mWidth.toFloat(),
            mHeight.toFloat()
        ) //圆角矩形
        canvas.drawRoundRect(rectF, round.toFloat(), round.toFloat(), mPaintRoundRect!!)
    }

    private fun drawProgress(canvas: Canvas) {
        // progress0
        var process0Width = mHeight.toFloat()
        val progressWidth = ((mWidth.toFloat() - mHeight.toFloat()) / 100f) * process
        val progressWidthAll = progressWidth + process0Width.toFloat()

        val rectProgress = RectF(
            0f,
            0f,
            progressWidthAll,
            mHeight.toFloat()
        ) //内部进度条
        canvas.drawRoundRect(rectProgress, round.toFloat(), round.toFloat(), mProgressPaint!!)

    }

    //进度条默认高度，未设置高度时使用
    private fun defaultHeight(): Int {
        val scale = context.resources.displayMetrics.density
        return (20 * scale + 0.5f * if (20 >= 0) 1 else -1).toInt()
    }
}