package com.czur.cloud.ui.starry.meeting.common

import android.util.Base64
import com.blankj.utilcode.util.Utils
import com.czur.cloud.common.CZURConstants
import com.czur.cloud.entity.realm.MessageEntity
import com.czur.cloud.netty.Config
import com.czur.cloud.netty.bean.CZMessage
import com.czur.cloud.netty.bean.MessageType
import com.czur.cloud.netty.messageData.StarryMeetingSendMessageBean
import com.czur.cloud.netty.messageData.StarryMeetingSendMessageData
import com.czur.cloud.preferences.UserPreferences
import com.czur.czurutils.log.logI
import com.google.gson.Gson
import java.util.*


// 组装 cmd 命令
object MeetCMDMessageFactory {

    // 生成 发送BIZ消息
    fun makeBIZMessageBody(roomNo: String,udidFrom: String, useridFrom: String, msgData: StarryMeetingSendMessageData): CZMessage {
        val context = Utils.getApp().applicationContext
        val message = CZMessage()

        val requestId = UUID.randomUUID().toString()
        val messageEntity = MessageEntity()
        messageEntity.requestId = requestId
        messageEntity.uuid = requestId
        messageEntity.deviceUDID = requestId
        messageEntity.name = "Starry"
        messageEntity.isNeedReceipt = false
        messageEntity.type = 0
        messageEntity.status = 0

        message.messageEntity = messageEntity
        message.type = "BIZ"
        message.appid = CZURConstants.PACKAGE_NAME
        message.os = MessageType.OS.type
        message.udid = UserPreferences.getInstance(context).udid
        message.userid = UserPreferences.getInstance(context).userId
        message.device = MessageType.DEVICE.type
        message.requestid = requestId
        val text = Config.appid + ":" + Config.udid + ":" + requestId
        val token = Base64.encodeToString(text.toByteArray(), Base64.NO_WRAP)
        message.token = token

        val meetingMessageData = StarryMeetingSendMessageBean()
        meetingMessageData.action = MessageType.MEETING.type
        meetingMessageData.module = "Starry"
        meetingMessageData.room = roomNo
        meetingMessageData.udid_from = udidFrom
        meetingMessageData.userid_from = useridFrom
        meetingMessageData.udid_to = ""

        meetingMessageData.data = msgData

        message.body = meetingMessageData

        logI("meetCMDMessageFactory.makeBIZMessageBody.message=", Gson().toJson(message))

        return message
    }

}