package com.czur.cloud.util;

import android.app.Activity;
import android.content.Context;

import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.network.PassportServices;

import java.io.File;
import java.util.List;

public class EtUtils {


    /**
     * 根据屏幕宽度与密度计算GridView显示的列数， 最少为三列，并获取Item宽度
     */
    public static int getImageItemWidth(Activity activity) {
        int screenWidth = activity.getResources().getDisplayMetrics().widthPixels;
        int densityDpi = activity.getResources().getDisplayMetrics().densityDpi;
        int cols = screenWidth / densityDpi;
        cols = cols < 3 ? 3 : cols;
        int columnSpace = (int) (2 * activity.getResources().getDisplayMetrics().density);
        return (screenWidth - columnSpace * (cols - 1)) / cols;
    }

    public static @PassportServices.MailLanguage
    String getLocale(String locale) {
        if (locale.contains("zh_CN")) {
            return PassportServices.CN;
        } else if (locale.contains("zh_HK") || locale.contains("zh_TW") || locale.contains("zh_MO")) {
            return PassportServices.TW;
        } else {
            return PassportServices.EN;
        }
    }

    private static long kb = 1024;
    private static long mb = kb * 1024;
    private static long gb = mb * 1024;

    public static String convertFileSize(long size) {
        if (size >= gb) {
            return String.format("%.1f GB", (float) size / gb);
        } else {
            float f = (float) size / mb;
            return String.format(f > 10 ? "%.2f MB" : "%.1f MB", f);
        }
//        else if (size >= kb) {
//            float f = (float) size / kb;
//            return String.format(f > 100 ? "%.0f KB" : "%.1f KB", f);
//        } else {
//            return String.format("%d B", size);
//        }
    }

    public static String convertOriginalSize(long size) {
        if (size > mb) {
            float f = (float) size / mb;
            return String.format("%.1f MB", f);
        }else {
            float f = (float) size / kb;
            return String.format(f > 100 ? "%.0f KB" : "%.1f KB", f);
        }
//        else if (size >= kb) {
//
//        } else {
//            return String.format("%d B", size);
//        }
    }

    public static String getFileProviderName(Context context) {
        return context.getPackageName() + ".provider";
    }
//    public static String getLocalName(String locale,Context context,boolean isBook) {
//        if (locale.contains("zh_CN")) {
//            return  isBook?context.getString(R.string.book_3):context.getString(R.string.aura_3);
//        } else if (locale.contains("zh_HK") || locale.contains("zh_TW") || locale.contains("zh_MO")) {
//            return  isBook?context.getString(R.string.book_2):context.getString(R.string.aura_2);
//        } else {
//            return  isBook?context.getString(R.string.book_1):context.getString(R.string.aura_1);
//        }
//
//    }

    //计算目录下文件合计大小
    public static long getFolderSize(java.io.File file) {
        long size = 0;
        try {
            java.io.File[] fileList = file.listFiles();
            if (fileList != null) {
                for (File aFileList : fileList) {
                    if (aFileList.isDirectory()) {
                        size = size + getFolderSize(aFileList);
                    } else {
                        size = size + aFileList.length();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return size;
    }

    public static long getFolderPDFSize(File path) {
        long size = 0;
        try {
            File[] fileList = path.listFiles();
            if (fileList != null) {
                for (File file : fileList) {
                    if (file.getName().toLowerCase().endsWith(".pdf") || file.getName().toLowerCase().endsWith(".jpg")) {
                        size += file.length();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return size;
    }

    public static String convertTime(String time) {
        String t = time.substring(0, 10);
        String i = t.replaceFirst("-", "年");
        String m = i.replaceFirst("-", "月");
        String e = m + "日";
        return e;
    }

    public static String cotFourDeviceName(String name) {
        String cut = name.substring(4, name.length());
        return cut;
    }

    public static String transFiles(List<String> files) {
        String addString = CZURConstants.EMPTY;
        for (int i = 0; i < files.size(); i++) {
            if (addString.equals(CZURConstants.EMPTY)) {
                addString = files.get(i);
            } else {
                addString = addString + "," + files.get(i);
            }
        }
        return addString;
    }

    public static int stringToInt(String str) {
        boolean has = false;
        String[] n = str.split("");
        for (int i = 0; i < str.length(); i++) {
            if (n[i].equals(".")) {
                has = true;
            }
        }
        if (has) {
            String s = str.substring(0, str.indexOf("."));
            return Integer.valueOf(s);
        } else {
            return Integer.valueOf(str);
        }
    }


    /**
     * 检测是否有emoji表情
     *
     * @param source
     * @return
     */
    public static boolean containsEmoji(String source) {
        int len = source.length();
        for (int i = 0; i < len; i++) {
            char codePoint = source.charAt(i);
            if (!isEmojiCharacter(codePoint)) { //如果不能匹配,则该字符是Emoji表情
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否是Emoji
     *
     * @param codePoint 比较的单个字符
     * @return
     */
    private static boolean isEmojiCharacter(char codePoint) {
        return (codePoint == 0x0) || (codePoint == 0x9) || (codePoint == 0xA) ||
                (codePoint == 0xD) || ((codePoint >= 0x20) && (codePoint <= 0xD7FF)) ||
                ((codePoint >= 0xE000) && (codePoint <= 0xFFFD)) || ((codePoint >= 0x10000)
                && (codePoint <= 0x10FFFF));
    }

}
