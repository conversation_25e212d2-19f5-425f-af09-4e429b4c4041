package com.czur.cloud.ui.starry.meeting.baselib.utils

import com.czur.cloud.ui.starry.meeting.baselib.utils.IndexUtils.INDEX_WORDS
import com.google.common.collect.HashBiMap

object IndexUtils {
    val INDEX_WORDS by lazy {
        val wordList = mutableListOf<Char>()
        for (c in 'A'..'Z'){
            wordList.add(c)
        }
        wordList.add('#')
        wordList.toList()
    }

    //姓名列表，转化为首字母列表
//    fun toWordIndexList(source: List<String>): List<Char> {
//        val list = mutableListOf<Char>()
//        source.forEach {
//            val firstWord = it[0]
//            if (!list.contains(firstWord)) {
//                list.add(Pinyin.toPinyin(firstWord)[0].toUpperCase())
//            }
//        }
//        return list
//    }

    //姓名列表转化为索引字母及位置
//    fun toIndexMap(source: List<String>): Map<Int, Char> {
//        val map = mutableMapOf<Int, Char>()
//        source.forEachIndexed { index, str ->
//            if (index == 0) {
//                val firstWord = str[0]
//                map[index] = Pinyin.toPinyin(firstWord)[0].toUpperCase()
//            } else {
//                val currentNameLetter = str[0]
//                val preNameLetter = source[index - 1][0]
//
//                val currentFirstPinyin = Pinyin.toPinyin(currentNameLetter)[0].toUpperCase()
//                val preFirstPinyin = Pinyin.toPinyin(preNameLetter)[0].toUpperCase()
//
//                //非字母类型，用'#'符号进行归类
//                if (currentFirstPinyin != preFirstPinyin) {
//                    map[index] = if (currentFirstPinyin.isLetter()) currentFirstPinyin else INDEX_WORDS.last()
//                }
//            }
//        }
//        return map
//    }
}

/**
 * 根据结合生成 包含索引信息的map, 注意传入的集合是需要排序过的
 *
 * @param block: 将其他类型转换为String类型的lambda
 * @return 双向HashMap 方便根据字母找索引,或者根据索引找字母
 */
fun <T> List<T>.toIndexMap(block: (T) -> String): HashBiMap<Char, Int> {
    val map = HashBiMap.create<Char, Int>()
    val list = map(block).map { it.first() }

    // 获取首字母, 如果是字母, 则返回本身, 其他情况返回#
    fun getChar(i: Int): Char = if (list[i].isLetter()) {
        list[i].toUpperCase()
    } else {
        INDEX_WORDS.last()
    }

    list.forEachIndexed { index, _ ->
        if (!map.containsKey(getChar(index))) {
            map[getChar(index)] = index
        }
    }
    return map
}