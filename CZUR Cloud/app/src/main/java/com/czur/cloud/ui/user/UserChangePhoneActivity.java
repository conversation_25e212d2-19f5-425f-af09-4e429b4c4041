package com.czur.cloud.ui.user;

import android.content.Intent;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.Editable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextWatcher;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.KeyboardUtils;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.UserInfoEvent;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.ProgressButton;
import com.czur.cloud.util.validator.Validator;

import org.greenrobot.eventbus.EventBus;

/**
 * Created by Yz on 2018/2/28.
 * Email：<EMAIL>
 */

public class UserChangePhoneActivity extends BaseActivity implements View.OnClickListener {


    private ImageView userBackBtn;
    private TextView userTitle;
    private TextView userChangePhoneInfoTv;
    private EditText userChangePhoneCodeEdt;
    private TextView userChangePhoneSendCodeTv;
    private ProgressButton userChangePhoneBtn;
    private UserPreferences userPreferences;
    private TimeCount timeCount;
    private HttpManager httpManager;
    private boolean codeHasContent = false;
    private long currentTime;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_user_change_phone);
        initComponent();
        registerEvent();
    }

    private void initComponent() {

        userBackBtn = (ImageView) findViewById(R.id.user_back_btn);
        userTitle = (TextView) findViewById(R.id.user_title);
        userChangePhoneInfoTv = (TextView) findViewById(R.id.user_change_phone_info_tv);
        userChangePhoneCodeEdt = (EditText) findViewById(R.id.user_change_phone_code_edt);
        userChangePhoneSendCodeTv = (TextView) findViewById(R.id.user_change_phone_send_code_tv);
        userChangePhoneBtn = (ProgressButton) findViewById(R.id.user_change_phone_btn);
        userTitle.setText(R.string.user_change_phone);
        httpManager = HttpManager.getInstance();
        userPreferences = UserPreferences.getInstance(this);
        formatString();

    }

    private void registerEvent() {
        userChangePhoneSendCodeTv.setOnClickListener(this);
        userChangePhoneBtn.setOnClickListener(this);
        userBackBtn.setOnClickListener(this);
        userChangePhoneBtn.setSelected(false);
        userChangePhoneBtn.setClickable(false);
        userChangePhoneCodeEdt.addTextChangedListener(codeTextWatcher);

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.user_back_btn:
                ActivityUtils.finishActivity(this);

                break;
            case R.id.user_change_phone_send_code_tv:
                getMobileCode(userPreferences.getUserMobile());
                break;
            case R.id.user_change_phone_btn:
                checkCodeToNextStep();
                break;
            default:
                break;
        }
    }

    /**
     * @des: 获取手机验证码
     * @params:[accountStr]
     * @return:void
     */
    private void getMobileCode(String mobile) {

        httpManager.requestPassport().mobileCode(mobile, String.class, new MiaoHttpManager.Callback<String>() {
            @Override
            public void onStart() {

            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {

                timeCountBegin();
                showMessage(R.string.toast_code_send);

            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {

                if (entity.getCode() == MiaoHttpManager.STATUS_CODE_1_MIN) {
                    showMessage(R.string.toast_code_1_min);
                } else if (entity.getCode() == MiaoHttpManager.STATUS_5_MIN_4_TIME) {
                    showMessage(R.string.toast_5_min_4_time);
                } else if (entity.getCode() == MiaoHttpManager.STATUS_5_TIME_IN_ONE_DAY) {
                    showMessage(R.string.toast_5_time_in_one_day);
                } else {
                    showMessage(R.string.request_failed_alert);
                }

            }

            @Override
            public void onError(Exception e) {

                showMessage((R.string.request_failed_alert));
            }
        });
    }

    private void checkCodeToNextStep() {
        String code = userChangePhoneCodeEdt.getText().toString();
        if (code.length() <= 5) {
            showMessage(R.string.edit_text_code_length);
        } else {
            currentTime = System.currentTimeMillis();
            KeyboardUtils.hideSoftInput(this);
            httpManager.requestPassport().updateMobileFirst(userPreferences.getIMEI(), CZURConstants.CLOUD_ANDROID,
                    userPreferences.getChannel(), userPreferences.getUserId(), userPreferences.getToken(), userPreferences.getUserId(), code, String.class,
                    new MiaoHttpManager.Callback<String>() {
                        @Override
                        public void onStart() {
                            userChangePhoneBtn.startDelayLoading(UserChangePhoneActivity.this);
                        }

                        @Override
                        public void onResponse(MiaoHttpEntity<String> entity) {
                            successDelay(entity);
                        }

                        @Override
                        public void onFailure(MiaoHttpEntity<String> entity) {
                            if (entity.getCode() == MiaoHttpManager.STATUS_CODE_EXPIRED) {
                                failedDelay(R.string.mail_code_expired);
                            } else {
                                failedDelay(R.string.mail_toast_testing_fail);
                            }
                        }

                        @Override
                        public void onError(Exception e) {
                            failedDelay(R.string.mail_toast_testing_fail);
                        }
                    });
        }
    }

    public void changeSuccess(MiaoHttpEntity<String> entity) {
        userPreferences.setUkey(entity.getBody());
        EventBus.getDefault().post(new UserInfoEvent(EventType.CHANGE_PHONE));
        Intent intent = new Intent(UserChangePhoneActivity.this, UserBindPhoneActivity.class);
        intent.putExtra("changePhone", true);
        ActivityUtils.startActivity(intent);
    }

    private void failedDelay(final int failedText) {

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    long sleepTime;
                    if (System.currentTimeMillis() - currentTime < 1000) {
                        sleepTime = 1000 - (System.currentTimeMillis() - currentTime);
                    } else {
                        sleepTime = 1;
                    }
                    Thread.sleep(sleepTime);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            showMessage(failedText);
                            userChangePhoneBtn.stopLoading();
                        }
                    });
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }


    private void successDelay(final MiaoHttpEntity<String> entity) {

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    long sleepTime;
                    if (System.currentTimeMillis() - currentTime < 1000) {
                        sleepTime = 1000 - (System.currentTimeMillis() - currentTime);
                    } else {
                        sleepTime = 1;
                    }
                    Thread.sleep(sleepTime);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            userChangePhoneBtn.stopLoading();
                            changeSuccess(entity);
                        }
                    });
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }

    /**
     * @des: 修改手机信息字体部分颜色
     * @params:
     * @return:
     */

    private void formatString() {
        String finalStr = String.format(getString(R.string.user_change_phone_first_text), userPreferences.getUserMobile());
        SpannableString spannableString = new SpannableString(finalStr);
        spannableString.setSpan(new ForegroundColorSpan(this.getResources().getColor(R.color.blue_29b0d7)), 20, 31, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        userChangePhoneInfoTv.setText(spannableString);
    }


    private TextWatcher codeTextWatcher = new TextWatcher() {


        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            // 输入的内容变化的监听
            if (s.length() > 0) {
                codeHasContent = true;
            } else {
                codeHasContent = false;
            }

            checkChangePhoneButtonToClick();
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count,
                                      int after) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            // 输入后的监听
            if (s.length() > 0) {
                codeHasContent = true;
            } else {
                codeHasContent = false;
            }
            checkChangePhoneButtonToClick();
        }
    };


    /**
     * @des: 检查注册按钮是否可以点击
     * @params:
     * @return:
     */

    private void checkChangePhoneButtonToClick() {

        if (codeHasContent) {
            userChangePhoneBtn.setSelected(true);
            userChangePhoneBtn.setClickable(true);
        } else {
            userChangePhoneBtn.setSelected(false);
            userChangePhoneBtn.setClickable(false);
        }
    }

    /**
     * @des: 计时器开始倒计时60S
     * @params:
     * @return:void
     */
    private void timeCountBegin() {
        timeCount = new TimeCount(60000, 1000);
        timeCount.start();

    }

    /**
     * @des: 计时器
     * @params:
     * @return:
     */

    class TimeCount extends CountDownTimer {

        public TimeCount(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @Override
        public void onFinish() {
            userChangePhoneSendCodeTv.setText(R.string.resend_code);
            userChangePhoneSendCodeTv.setClickable(true);


        }

        @Override
        public void onTick(long millisUntilFinished) {
            userChangePhoneSendCodeTv.setClickable(false);
            userChangePhoneSendCodeTv.setText(millisUntilFinished / 1000 + " s");


        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (Validator.isNotEmpty(timeCount)) {
            timeCount.cancel();
        }
    }
}