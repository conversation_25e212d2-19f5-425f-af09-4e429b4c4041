package com.czur.cloud.ui.starry.model

import com.czur.cloud.ui.starry.common.StarryConstants
import java.io.Serializable

/**
 */
data class StarryEnterpriseMemberModel(
    val addressBook: List<StarryAddressBookModel> = listOf(),
    val enterPriseMembers: List<StarryEnterpriseModel> = listOf()
):Serializable

data class StarryAddressBookModel(
    var id: String,
    var name: String="",
    val remark: String = "",    // 新增字段，代替name字段：备注名称
    val pinyin: String="",
    var createTime: String="",
    val updateTime: String="",
    val isDelete: Boolean=false,
    // 其它联系人详情
    val accountId: String="",
    var meetingNo: String="",
    var isMarked: Boolean=false,
    val kind: String="",
    val delete: Boolean=false,

    // 企业成员详情
    val enterpriseId: String="",
    val enterpriseName: String="",
    var meetingAccout: String="",
    val isAdmin: Boolean=false,
    val type: String="0",
    val status: String="",
    val admin: Boolean=false,

    // 最近会议成员详情
    var accountNo: String="",
    var response: String="",
    var isInEnterprise: Boolean=false,
    var isInAddressbook: Boolean=false,   // 默认从通讯录来的；从最近会议来的，会自带isInAddressbook

    var userType:String = StarryConstants.STARRY_USER_TYPE_ADDRESS_BOOK,

    var czurId: String = "0",
    val nickname: String = "",
    var namePinyin :String ="",
    var remarkPinyin: String = "",
    var nicknamePinyin:String = "",
    var remarkNamePersonalAdress :String = "",
    var remarkNamePersonalAdressPinYin :String = "",
):Serializable {
    constructor() : this("0")

    override fun toString(): String {
        return "StarryAddressBookModel(id=$id, name='$name', remark='$remark', pinyin='$pinyin', createTime='$createTime', updateTime='$updateTime', accountId='$accountId', meetingNo='$meetingNo', isMarked=$isMarked, kind='$kind', delete=$delete, enterpriseId='$enterpriseId', enterpriseName='$enterpriseName', meetingAccout='$meetingAccout', isAdmin=$isAdmin, type='$type', status='$status', admin=$admin, accountNo='$accountNo', response='$response', isInEnterprise=$isInEnterprise, isInAddressbook=$isInAddressbook, userType=$userType)"
    }


}

data class StarryEnterpriseModel(
    val enterpriseId: String="",
    val enterpriseName: String="",
    val expired: Boolean=false,
    val expiredTime: String="",
    val joinStatus: String="",
    val membersList: List<StarryAddressBookModel> = listOf(),
    val portLimit: Int=0,
    var pinyin: String = ""
): Serializable, Comparable<StarryEnterpriseModel>{
    override fun toString(): String {
        return "StarryEnterpriseModel(enterpriseId='$enterpriseId', enterpriseName='$enterpriseName', expired=$expired, expiredTime='$expiredTime', joinStatus='$joinStatus', membersList=$membersList, portLimit=$portLimit, pinyin='$pinyin')"
    }

    override fun compareTo(other: StarryEnterpriseModel)
    = compareValuesBy(this, other)

}
