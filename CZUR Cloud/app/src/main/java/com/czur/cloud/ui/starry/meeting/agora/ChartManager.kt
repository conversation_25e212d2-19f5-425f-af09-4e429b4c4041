package com.czur.cloud.ui.starry.meeting.agora

import android.content.Context
import android.util.Log
import com.czur.cloud.BuildConfig
import com.czur.cloud.ui.starry.meeting.agora.AgoraManager.Companion.JOIN_CHANNEL_FAIL
import com.czur.cloud.ui.starry.meeting.agora.AgoraManager.Companion.JOIN_CHANNEL_SUCCESS
import com.czur.cloud.ui.starry.meeting.bean.ChatSendMsgStatus
import com.czur.cloud.ui.starry.meeting.bean.ChatTextMsg
import com.czur.cloud.ui.starry.meeting.model.ModelManager
import com.czur.czurutils.log.logE
import io.agora.rtm.*

/**
 * Created by 陈丰尧 on 4/16/21
 */
private const val TAG = "ChatManager"

class ChatManager(private val mContext: Context) {
    lateinit var mRtmClient: RtmClient
    private var mSendMsgOptions: SendMessageOptions? = null
    private val mListenerList: MutableList<RtmClientListener> = ArrayList()
    private val mMessagePool: RtmMessagePool = RtmMessagePool()
    private var channel: RtmChannel? = null

    // 初始化聊天
    fun init() {
        try {
            mRtmClient = RtmClient.createInstance(mContext, BuildConfig.AGORA_STARRY_APP_ID, object : RtmClientListener {
                override fun onConnectionStateChanged(state: Int, reason: Int) {
                    for (listener in mListenerList) {
                        listener.onConnectionStateChanged(state, reason)
                    }
                }

                override fun onMessageReceived(rtmMessage: RtmMessage, peerId: String) {
                    if (mListenerList.isEmpty()) {
                        // If currently there is no callback to handle this
                        // message, this message is unread yet. Here we also
                        // take it as an offline message.
                        mMessagePool.insertOfflineMessage(rtmMessage, peerId)
                    } else {
                        for (listener in mListenerList) {
                            listener.onMessageReceived(rtmMessage, peerId)
                        }
                    }
                }

                override fun onTokenExpired() {}

                override fun onTokenPrivilegeWillExpire() {
                }

                override fun onPeersOnlineStatusChanged(status: kotlin.collections.Map<String, Int>) {}
            })
        } catch (e: Exception) {
            Log.e(TAG, Log.getStackTraceString(e))
            throw RuntimeException(
                """
                    NEED TO check rtm sdk init fatal error
                    ${Log.getStackTraceString(e)}
                    """.trimIndent()
            )
        }

        // Global option, mainly used to determine whether
        // to support offline messages now.
        mSendMsgOptions = SendMessageOptions()
    }

    /**
     * 加入聊天Channel, 需要 先登录, 再加入
     */
    fun joinChannel(
        rtmToken: String,
        room: String,
        czurID: String,
        joinListener: (joinResult: Int) -> Unit
    ) {
        mRtmClient.login(rtmToken, czurID, object : ResultCallback<Void> {
            override fun onSuccess(responseInfo: Void?) {
                createAndJoinChannel(room, joinListener)
            }

            override fun onFailure(errorInfo: ErrorInfo) {
                Log.d(TAG, "聊天登录失败:${errorInfo}")
                if (errorInfo.errorCode == 8) {
                    // 已经登录过了, 可以继续加入Channel
                    createAndJoinChannel(room, joinListener)
                } else {
                    joinListener(JOIN_CHANNEL_FAIL)
                }
            }
        })

    }

    /**
     * 创建并加入channel
     */
    private fun createAndJoinChannel(room: String, joinListener: (joinResult: Int) -> Unit) {
        Log.d(TAG, "createAndJoinChannel")
        channel = mRtmClient.createChannel(room, object : RtmChannelListener {
            override fun onMemberCountUpdated(i: Int) {
                Log.d(TAG, "onMemberCountUpdated: $i")
            }

            override fun onAttributesUpdated(list: MutableList<RtmChannelAttribute>?) {
            }

            override fun onMessageReceived(message: RtmMessage, fromMember: RtmChannelMember) {
                val account = fromMember.userId
                Log.d(TAG, "onMessageReceived account = $account + msg = $message")
                val messageType = message.messageType
                if (messageType == RtmMessageType.TEXT) {
                    // 文本类型的消息
                    ModelManager.charModel.addMsg(ChatTextMsg(account, message.text))
                }
            }

            override fun onMemberJoined(member: RtmChannelMember?) {
//                Log.d(TAG, "上线:${member?.userId}")
//                member?.let {
//                    val id = it.userId
//                    ChatModel.addMsg(ChatActionMsg(id, ChatMemberMsgAction.ONLINE))
//                }
            }

            override fun onMemberLeft(member: RtmChannelMember?) {
//                Log.d(TAG, "下线:${member?.userId}")
//                member?.let {
//                    val id = it.userId
//                    ChatModel.addMsg(ChatActionMsg(id, ChatMemberMsgAction.OFFLINE))
//                }
            }
        })

        channel?.join(object : ResultCallback<Void> {
            override fun onSuccess(responseInfo: Void?) {
                Log.d(TAG, "加入聊天频道成功")
                joinListener(JOIN_CHANNEL_SUCCESS)
            }

            override fun onFailure(errorInfo: ErrorInfo?) {
                Log.d(TAG, "加入聊天频道失败")
                joinListener(JOIN_CHANNEL_FAIL)
            }

        })
    }

    fun getRtmClient(): RtmClient {
        return mRtmClient
    }

    /**
     * 离开聊天室
     */
    fun leaveChanel() {
        Log.d(TAG, "离开聊天室")
        channel?.let {
            it.leave(null)
            it.release()
            channel = null
        }

    }

    fun registerListener(listener: RtmClientListener) {
        mListenerList.add(listener)
    }

    fun unregisterListener(listener: RtmClientListener) {
        mListenerList.remove(listener)
    }

    fun getSendMessageOptions(): SendMessageOptions? {
        return mSendMsgOptions
    }

    fun getAllOfflineMessages(peerId: String?): List<RtmMessage> {
        return mMessagePool.getAllOfflineMessages(peerId!!)!!
    }

    fun removeAllOfflineMessages(peerId: String?) {
        mMessagePool.removeAllOfflineMessages(peerId!!)
    }

    fun sendMessage(text: String) {
        val rtmMsg = mRtmClient.createMessage()
        rtmMsg.text = text
        val chatMsg = ChatTextMsg.mkSend(text)
        val msgId = chatMsg.id
        ModelManager.charModel.addMsg(chatMsg)
        logE("ChatManager.sendMessage.chatMsg=${chatMsg}")
        channel?.sendMessage(rtmMsg, object : ResultCallback<Void> {
            override fun onSuccess(aVoid: Void?) {
                ModelManager.charModel.changeMsgStatus(msgId, ChatSendMsgStatus.SUCCESS)
            }

            override fun onFailure(errorInfo: ErrorInfo?) {
                logE("ChatManager.sendMessage.消息发送失败:${errorInfo?.errorCode}-${errorInfo?.errorDescription}")
                ModelManager.charModel.changeMsgStatus(msgId, ChatSendMsgStatus.FAIL)
            }
        })
    }

}
