package com.czur.cloud.ui.starry.utils

import android.content.ClipData
import android.content.ClipboardManager
import android.os.Build
import com.czur.cloud.BuildConfig

/**
 * Created by 陈丰尧 on 2022/7/13
 */

/**
 * 清空剪贴板
 */
fun ClipboardManager.clearContent() {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
        clearPrimaryClip()
    } else {
        setPrimaryClip(ClipData.newPlainText(null, null))
    }
}

/**
 * ClipData中获取剪贴板的内容
 */
fun ClipData?.getContent(): String {
    if (this == null || itemCount <= 0) return ""
    return getItemAt(0).getContent()
}

/**
 * Item中获取剪贴板的内容
 */
fun ClipData.Item?.getContent(): String {
    return this?.text?.toString() ?: ""
}

/**
 * 校验剪贴板中的文言是否满足我们定义的格式
 */
fun String.isJoinMeetingText(): Boolean {
    if (this.isBlank()) return false
    val patternURL = BuildConfig.SHARE_STARRY_URL.toRegex()
    val patternCZUR = "CZUR".toRegex()

    return (patternURL.containsMatchIn(this)
            &&
            (patternCZUR.containsMatchIn(this))
            )

}