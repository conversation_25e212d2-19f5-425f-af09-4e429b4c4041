package com.czur.cloud.ui.starry.activity

import com.czur.cloud.ui.starry.base.StarryBaseActivity
import com.czur.cloud.ui.component.popup.CloudCommonPopup
import com.czur.cloud.preferences.UserPreferences
import android.widget.TextView
import android.os.Bundle
import com.czur.cloud.R
import android.content.Intent
import com.czur.cloud.ui.starry.activity.StarryWebViewActivity
import com.czur.cloud.ui.user.UserFeedbackActivity
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants
import android.content.DialogInterface
import android.view.View
import android.widget.ImageView
import com.blankj.utilcode.util.*
import com.czur.cloud.network.core.MiaoHttpManager
import com.czur.cloud.network.core.MiaoHttpEntity
import com.czur.cloud.netty.observer.NettyService
import com.czur.cloud.network.HttpManager
import com.czur.cloud.ui.home.IndexActivity
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.livedatabus.LiveDataBus
import com.czur.cloud.ui.starry.utils.singleClick
import com.czur.czurutils.log.logE
import com.google.gson.Gson
import java.lang.Exception
import java.util.*

/**
 * 更多页面
 */
class StarryMenuActivity : StarryBaseActivity(), View.OnClickListener {
    private var commonPopup: CloudCommonPopup? = null
    private var userBackBtn: ImageView? = null
    private var httpManager: HttpManager? = null
    private var userPreferences: UserPreferences? = null
    private var userTitle: TextView? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.white)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.starry_activity_menu_home)
        initComponent()
        registerEvent()
    }

    private fun initComponent() {
        userPreferences = UserPreferences.getInstance(this)
        httpManager = HttpManager.getInstance()
        userBackBtn = findViewById<View>(R.id.user_back_btn) as ImageView
        userTitle = findViewById<View>(R.id.user_title) as TextView
        userTitle?.setText(R.string.starry_title_more)
    }

    private fun registerEvent() {
        userBackBtn?.setOnClickListener(this)
        findViewById<View>(R.id.starry_menu_advice_rl).singleClick(this)
        findViewById<View>(R.id.starry_menu_question_rl).singleClick(this)
        findViewById<View>(R.id.starry_menu_delete_rl).singleClick(this)
        findViewById<View>(R.id.starry_menu_instructions_rl).singleClick(this)
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.user_back_btn -> ActivityUtils.finishActivity(this)
            R.id.starry_menu_instructions_rl -> {
                val cTime = Calendar.getInstance().timeInMillis
                val intent = Intent(this, StarryWebViewActivity::class.java)
                intent.putExtra("title", getString(R.string.starry_menu_instructions))
                intent.putExtra(
                    "url",
                    String.format(
                        getString(R.string.starry_menu_instructions_url),
                        cTime.toString() + ""
                    )
                )
                ActivityUtils.startActivity(intent)
            }
            R.id.starry_menu_delete_rl -> showConfirmDeleteDialog()
            R.id.starry_menu_advice_rl -> {
                val intent2 = Intent(this@StarryMenuActivity, UserFeedbackActivity::class.java)
                intent2.putExtra("isQuestion", false)
                // Jason 20201225 type=6,投影仪
                intent2.putExtra("type", 6)
                intent2.putExtra("isFromAuraMate", true)
                ActivityUtils.startActivity(intent2)
            }
            R.id.starry_menu_question_rl -> {
                val intent1 = Intent(this@StarryMenuActivity, UserFeedbackActivity::class.java)
                intent1.putExtra("isQuestion", true)
                intent1.putExtra("type", 6)
                intent1.putExtra("isFromAuraMate", true)
                ActivityUtils.startActivity(intent1)
            }
            else -> {
            }
        }
    }

    /**
     * @des:确认是否删除
     */
    private fun showConfirmDeleteDialog() {
        val builder = CloudCommonPopup.Builder(
            this@StarryMenuActivity,
            CloudCommonPopupConstants.COMMON_TWO_BUTTON_YES_NO
        )
        builder.setTitle(resources.getString(R.string.prompt))
        builder.setMessage(resources.getString(R.string.starry_menu_remove_confirm))
        builder.setOnPositiveListener { dialog, which ->
            if (commonPopup != null) {
                commonPopup?.dismiss()
            }
            removeEquipment(getString(R.string.Starry))
        }
        builder.setOnNegativeListener { dialog, which -> dialog.dismiss() }
        commonPopup = builder.create()
        commonPopup?.show()
    }

    /**
     * @des: 移除设备
     * @params:[equipName]
     * @return:void
     */
    private fun removeEquipment(equipName: String) {
        if (!NetworkUtils.isConnected()){
            ToastUtils.showLong(R.string.starry_network_error_msg);
            return;
        }

        httpManager?.request()?.removeEquipment(
            userPreferences?.userId,
            equipName,
            String::class.java,
            object : MiaoHttpManager.Callback<String?> {
                override fun onStart() {
                    showProgressDialog()
                }

                override fun onResponse(entity: MiaoHttpEntity<String?>?) {
                    hideProgressDialog()
//                    ServiceUtils.stopService(NettyService::class.java)
//                    showMessage(R.string.remove_success)
                    val intent = Intent(this@StarryMenuActivity, IndexActivity::class.java)
//                    intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
                    intent.flags = Intent.FLAG_ACTIVITY_SINGLE_TOP or Intent.FLAG_ACTIVITY_NEW_TASK
                    ActivityUtils.startActivity(intent)
                    LiveDataBus.get().with(StarryConstants.STARRY_REMOVE).value = true
//                    ServiceUtils.stopService(NettyService::class.java)
                }

                override fun onFailure(entity: MiaoHttpEntity<String?>?) {
                    hideProgressDialog()
                    logE("removeEquipment.onFailure=${Gson().toJson(entity)}")
                    if (entity?.code == MiaoHttpManager.STATUS_FAIL) {
                        showMessage(R.string.toast_internal_error)
                    } else {
                        showMessage(R.string.request_failed_alert)
                    }
                }

                override fun onError(e: Exception) {
                    hideProgressDialog()
                    showMessage(R.string.request_failed_alert)
                }

            })
    }
}