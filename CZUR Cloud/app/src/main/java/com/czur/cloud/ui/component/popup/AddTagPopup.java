package com.czur.cloud.ui.component.popup;

import android.app.Dialog;
import android.content.Context;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.TextView;

import com.czur.cloud.R;
import com.czur.cloud.ui.component.NoHintEditText;
import com.czur.cloud.util.validator.StringUtils;

/**
 * Created by Yz on 2018/3/20
 * Email：<EMAIL>
 */

public class AddTagPopup extends Dialog {
    public static final float DIMMED_OPACITY = 0.2f;

    private boolean isChecked;

    public AddTagPopup(Context context) {
        super(context);
    }

    public AddTagPopup(Context context, int theme) {
        super(context, theme);
    }

    public boolean isChecked() {
        return this.isChecked;
    }

    public static class Builder {
        private Context context;
        private CloudCommonPopupConstants constants;

        private String title;
        private View contentsView;

        private OnClickListener positiveListener;
        private OnClickListener onNegativeListener;
        private OnDismissListener onDismissListener;

        public Builder(Context context, CloudCommonPopupConstants constants) {
            this.context = context;
            this.constants = constants;
        }


        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setContentsView(View contentsView) {
            this.contentsView = contentsView;
            return this;
        }

        public Builder setContentsView(int resource) {
            this.contentsView = LayoutInflater.from(context).inflate(resource, null);
            return this;
        }

        public Builder setOnPositiveListener(OnClickListener positiveListener) {
            this.positiveListener = positiveListener;
            return this;
        }

        public Builder setOnNegativeListener(OnClickListener onNegativeListener) {
            this.onNegativeListener = onNegativeListener;
            return this;
        }

        public Builder setOnDismissListener(OnDismissListener onDismissListener) {
            this.onDismissListener = onDismissListener;
            return this;
        }


        public AddTagPopup create() {
            LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);

            final AddTagPopup dialog = new AddTagPopup(context, R.style.TransparentProgressDialog);
            View layout = commonCustomPopLayout(inflater, dialog);

            dialog.setContentView(layout);
            dialog.setCanceledOnTouchOutside(true);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            params.dimAmount = DIMMED_OPACITY;

            return dialog;
        }

        private Integer count(String str) {
            int chCharacter = 0;//中文字符
            int enCharacter = 0;//英文字符
            int spaceCharacter = 0;//空格
            int numberCharacter = 0;//数字
            int otherCharacter = 0;//其他字符

            if (null == str || str.equals("")) {
                return 0;
            }

            for (int i = 0; i < str.length(); i++) {
                char tmp = str.charAt(i);
                if ((tmp >= 'A' && tmp <= 'Z') || (tmp >= 'a' && tmp <= 'z')) {
                    enCharacter++;
                } else if ((tmp >= '0') && (tmp <= '9')) {
                    numberCharacter++;
                } else if (tmp == ' ') {
                    spaceCharacter++;
                } else if (isChinese(tmp)) {
                    chCharacter++;
                } else {
                    otherCharacter++;
                }
            }
            return chCharacter * 2 + enCharacter + numberCharacter + otherCharacter;
        }

        private boolean isChinese(char ch) {
            //获取此字符的UniCodeBlock
            Character.UnicodeBlock ub = Character.UnicodeBlock.of(ch);
            //  GENERAL_PUNCTUATION 判断中文的“号
            //  CJK_SYMBOLS_AND_PUNCTUATION 判断中文的。号
            //  HALFWIDTH_AND_FULLWIDTH_FORMS 判断中文的，号
            if (ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                    || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B
                    || ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION || ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS
                    || ub == Character.UnicodeBlock.GENERAL_PUNCTUATION) {
                return true;
            }
            return false;
        }

        private String subSixString(CharSequence s) {
            if (count(s.toString()) > 12) {
                for (int i = 0; i < s.toString().length(); i++) {

                    if (count(s.toString().substring(0, (s.toString().length() - 1 - i))) <= 12) {
                        return s.toString().substring(0, (s.toString().length() - 1 - i));
                    }
                }
            }
            return s.toString();
        }

        private View commonCustomPopLayout(LayoutInflater inflater, final AddTagPopup dialog) {

            WindowManager.LayoutParams lp = dialog.getWindow().getAttributes();
            lp.dimAmount = 0.8f;
            dialog.getWindow().setAttributes(lp);
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

            View layout = inflater.inflate(R.layout.add_tag_popup, null, false);
            ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);
            dialog.addContentView(layout, params);
            TextView title = (TextView) layout.findViewById(R.id.title);
            TextView positiveBtn = (TextView) layout.findViewById(R.id.positive_button);
            TextView negativeBtn = (TextView) layout.findViewById(R.id.negative_button);
            final NoHintEditText editText = (NoHintEditText) layout.findViewById(R.id.edt);

            editText.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {

                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {

                }

                @Override
                public void afterTextChanged(Editable s) {
                    editText.removeTextChangedListener(this);
                    String subSixString = subSixString(s);
                    editText.setText(subSixString);
                    editText.setSelection(subSixString.length());
                    editText.addTextChangedListener(this);

                }


            });


            if (contentsView == null) {


                if (StringUtils.isNotEmpty(this.title)) {
                    title.setText(this.title + StringUtils.EMPTY);
                } else if (constants.getTitle() > 0) {
                    title.setText(context.getResources().getString(constants.getTitle()));
                }
            } else {
                title.setVisibility(View.GONE);
            }

            if (constants.getPositiveBtn() > 0) {
                positiveBtn.setText(context.getResources().getString(constants.getPositiveBtn()));
            } else {
                positiveBtn.setVisibility(View.GONE);
            }

            if (positiveListener != null) {
                positiveBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        positiveListener.onClick(dialog, constants.getPositiveBtn());
                    }
                });
            } else {
                positiveBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialog.dismiss();
                    }
                });
            }

            if (constants.getNegativeBtn() > 0) {
                negativeBtn.setText(context.getResources().getString(constants.getNegativeBtn()));
            } else {
                negativeBtn.setVisibility(View.GONE);
            }


            if (onNegativeListener != null) {
                negativeBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onNegativeListener.onClick(dialog, constants.getNegativeBtn());
                    }
                });
            } else {
                negativeBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialog.dismiss();
                    }
                });
            }

            if (onDismissListener != null) {
                dialog.setOnDismissListener(onDismissListener);
            }

            return layout;
        }
    }
}
