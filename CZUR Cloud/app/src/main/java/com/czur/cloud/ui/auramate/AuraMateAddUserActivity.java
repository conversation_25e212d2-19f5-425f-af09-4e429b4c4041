package com.czur.cloud.ui.auramate;

import android.content.DialogInterface;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.AuraMateSearchUserAdapter;
import com.czur.cloud.event.AddShareUserEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.model.SearchUserModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Yz on 2018/6/1.
 * Email：<EMAIL>
 */
public class AuraMateAddUserActivity extends AuramateBaseActivity implements View.OnClickListener {

    private ImageView normalBackBtn;
    private TextView normalTitle;
    private EditText etAddUserEdt;
    private TextView etAddUserSearchBtn;
    private RecyclerView etAddRecyclerView;
    private List<SearchUserModel> userShareModels;
    private UserPreferences userPreferences;
    private HttpManager httpManager;
    private AuraMateSearchUserAdapter searchAdapter;
    private TextView searchTitle;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_aura_mate_add_user);
        initComponent();
        registerEvent();
        initRecyclerView();
    }

    @Override
    public boolean PCNeedFinish() {
        return !TextUtils.isEmpty(equipmentId);
    }


    private void initComponent() {
        userPreferences = UserPreferences.getInstance(this);
        httpManager = HttpManager.getInstance();
        searchTitle = (TextView) findViewById(R.id.search_title);
        normalBackBtn = (ImageView) findViewById(R.id.user_back_btn);
        normalTitle = (TextView) findViewById(R.id.user_title);
        etAddUserEdt = (EditText) findViewById(R.id.et_add_user_edt);

        etAddUserSearchBtn = (TextView) findViewById(R.id.et_add_user_search_btn);
        etAddRecyclerView = (RecyclerView) findViewById(R.id.et_add_recyclerView);
        normalTitle.setText(R.string.invite_family);
    }

    private void registerEvent() {
        normalBackBtn.setOnClickListener(this);
        etAddUserSearchBtn.setOnClickListener(this);
        setNetListener();
    }

    /**
     * @des: 初始化列表
     * @params:
     * @return:
     */

    private void initRecyclerView() {
        userShareModels = new ArrayList<>();
        searchAdapter = new AuraMateSearchUserAdapter(this, userShareModels);
        searchAdapter.setOnItemClickListener(onItemClickListener);
        etAddRecyclerView.setHasFixedSize(true);
        etAddRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        etAddRecyclerView.setAdapter(searchAdapter);
    }

    /**
     * @des: 添加列表点击事件
     * @params:
     * @return:
     */

    private AuraMateSearchUserAdapter.onItemClickListener onItemClickListener = new AuraMateSearchUserAdapter.onItemClickListener() {
        @Override
        public void onItemClick(int position, SearchUserModel searchUserModel) {
            if (searchUserModel.getId().equals(userPreferences.getUserId())) {
                showMessage(R.string.toast_add_people_not_self);
            } else {
                addShareUser(searchUserModel.getId());
            }
        }
    };

    /**
     * @des: 搜索用户
     * @params:
     * @return:
     */


    private void searchUser(final String searchUserId) {
        httpManager.requestPassport().searchUser(userPreferences.getChannel(), searchUserId, SearchUserModel.class, new MiaoHttpManager.Callback<SearchUserModel>() {
            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<SearchUserModel> entity) {
                hideProgressDialog();
                etAddRecyclerView.setVisibility(View.VISIBLE);
                List<SearchUserModel> userList = new ArrayList<>();
                userList.add(entity.getBody());
                searchAdapter.refreshData(userList);
                searchTitle.setVisibility(View.VISIBLE);
            }

            @Override
            public void onFailure(MiaoHttpEntity<SearchUserModel> entity) {
                hideProgressDialog();
                if (entity.getCode() == MiaoHttpManager.STATUS_NOT_USER) {
                    showUserTip(getString(R.string.toast_user_no_exist));
                }
                etAddRecyclerView.setVisibility(View.GONE);
                searchTitle.setVisibility(View.GONE);
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
                etAddRecyclerView.setVisibility(View.GONE);
                searchTitle.setVisibility(View.GONE);
            }
        });
    }

    private void showUserTip(String message) {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(AuraMateAddUserActivity.this, CloudCommonPopupConstants.COMMON_ONE_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(message);
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.setCanceledOnTouchOutside(false);
        commonPopup.show();
    }

    /**
     * @des: 添加共享用户
     * @params:
     * @return:
     */
    private void addShareUser(String searchUserId) {
        httpManager.request().addAuraMateShareUser(userPreferences.getUserId(), equipmentId, searchUserId, String.class, new MiaoHttpManager.Callback<String>() {
            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                EventBus.getDefault().post(new AddShareUserEvent(EventType.ADD_AURA_SHARE_USER));
                ActivityUtils.finishActivity(AuraMateAddUserActivity.class);
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                if (entity.getCode() == MiaoHttpManager.STATUS_NOT_MANAGER) {
                    showMessage(R.string.toast_add_fail);
                } else if (entity.getCode() == MiaoHttpManager.STATUS_SHARED_MEMBER_ADDED) {
                    ActivityUtils.finishActivity(AuraMateAddUserActivity.class);
                } else if (entity.getCode() == MiaoHttpManager.STATUS_SHARED_MEMBER_MORE_THAN_THREE) {
                    showMessage(R.string.toast_add_more_than_three);
                } else {
                    ActivityUtils.finishActivity(AuraMateAddUserActivity.class);
                }
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
                showMessage(R.string.toast_server_error);
            }
        });
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.user_back_btn:
                ActivityUtils.finishActivity(AuraMateAddUserActivity.this);
                break;
            case R.id.et_add_user_search_btn:
                String searchName = etAddUserEdt.getText().toString();
                if (searchName.length() == 0) {
                    showUserTip(getString(R.string.account_empty));
                } else if (isValidatorLoginName(searchName)) {
                    showUserTip(getString(R.string.toast_format_wrong));
                } else {
                    searchUser(searchName);
                }
                break;
            default:
                break;
        }
    }

}
