package com.czur.cloud.ui.et;

import android.Manifest;
import android.app.Activity;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.UtilsTransActivity;
import com.czur.cloud.R;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.RemoveBookEvent;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.home.IndexActivity;
import com.czur.cloud.ui.user.UserFeedbackActivity;
import com.czur.cloud.util.PermissionUtil;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

/**
 * Created by Yz on 2018/6/1.
 * Email：<EMAIL>
 */
public class EtMenuActivity extends BaseActivity implements View.OnClickListener {

    private CloudCommonPopup commonPopup;
    private ImageView userBackBtn;
    private RelativeLayout bookMenuMyPdfRl;
    private RelativeLayout bookMenuDeleteRl;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private RelativeLayout etMenuAddEtRl;
    private TextView userTitle;

    private RelativeLayout etMenuAdviceRl;
    private RelativeLayout etMenuQuestionRl;
    private String sn;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_et_menu);
        initComponent();
        registerEvent();

    }


    private void initComponent() {
        userPreferences = UserPreferences.getInstance(this);
        httpManager = HttpManager.getInstance();
        userBackBtn = (ImageView) findViewById(R.id.user_back_btn);
        sn = getIntent().getStringExtra("sn");

        etMenuAdviceRl = (RelativeLayout) findViewById(R.id.et_menu_advice_rl);
        etMenuQuestionRl = (RelativeLayout) findViewById(R.id.et_menu_question_rl);

        bookMenuMyPdfRl = (RelativeLayout) findViewById(R.id.book_menu_my_pdf_rl);
        bookMenuDeleteRl = (RelativeLayout) findViewById(R.id.book_menu_delete_rl);
        etMenuAddEtRl = (RelativeLayout) findViewById(R.id.et_menu_add_et_rl);
        userTitle = (TextView) findViewById(R.id.user_title);
        userTitle.setText(R.string.more);
    }

    private void registerEvent() {

        etMenuAdviceRl.setOnClickListener(this);
        etMenuQuestionRl.setOnClickListener(this);
        etMenuAddEtRl.setOnClickListener(this);
        userBackBtn.setOnClickListener(this);
        bookMenuMyPdfRl.setOnClickListener(this);
        bookMenuDeleteRl.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.et_menu_add_et_rl:
                openCamera();
                break;

            case R.id.user_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.book_menu_delete_rl:
                showConfirmDeleteDialog();
                break;
            case R.id.book_menu_my_pdf_rl:
                ActivityUtils.startActivity(EtPdfActivity.class);
                break;
            case R.id.et_menu_advice_rl:
                Intent intent = new Intent(EtMenuActivity.this, UserFeedbackActivity.class);
                intent.putExtra("isQuestion", false);
                intent.putExtra("sn", sn);
                intent.putExtra("type", 2);
                ActivityUtils.startActivity(intent);
                break;
            case R.id.et_menu_question_rl:
                Intent intent1 = new Intent(EtMenuActivity.this, UserFeedbackActivity.class);
                intent1.putExtra("isQuestion", true);
                intent1.putExtra("sn", sn);
                intent1.putExtra("type", 2);
                ActivityUtils.startActivity(intent1);
                break;
            default:
                break;
        }
    }

    /**
     * @des: 申请权限并且打开相机页
     * @params:
     * @return:
     */

    private void openCamera() {
        PermissionUtils.permission(PermissionUtil.getStoragePermission(Manifest.permission.CAMERA, Manifest.permission.VIBRATE))
                .rationale(new PermissionUtils.OnRationaleListener() {
                    @Override
                    public void rationale(UtilsTransActivity activity, ShouldRequest shouldRequest) {
//                        showMessage(R.string.denied_camera);
                        shouldRequest.again(true);
                    }
                })
                .callback(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(List<String> permissionsGranted) {
                        ActivityUtils.startActivity(ScanActivity.class);
//                        ActivityUtils.startActivity(CaptureActivity.class);
                    }

                    @Override
                    public void onDenied(List<String> permissionsDeniedForever,
                                         List<String> permissionsDenied) {
//                        showMessage(R.string.denied_camera);
                    }
                })
                .theme(new PermissionUtils.ThemeCallback() {
                    @Override
                    public void onActivityCreate(Activity activity) {
                        ScreenUtils.setFullScreen(activity);
                    }
                })
                .request();
    }

    /**
     * @des:确认是否删除ET
     * @params:
     * @return:
     */

    private void showConfirmDeleteDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(EtMenuActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.remove_ET_confirm));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {

                if (commonPopup != null) {
                    commonPopup.dismiss();
                }
                EventBus.getDefault().post(new RemoveBookEvent(EventType.REMOVE_ET));

                removeEquipment(getString(R.string.ET));
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        commonPopup = builder.create();
        commonPopup.show();
    }

    /**
     * @des: 移除设备
     * @params:[equipName]
     * @return:void
     */
    private void removeEquipment(String equipName) {
        httpManager.request().removeEquipment(
                userPreferences.getUserId(), equipName, String.class, new MiaoHttpManager.Callback<String>() {
                    @Override
                    public void onStart() {
                        showProgressDialog();
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                        hideProgressDialog();
                        showMessage(R.string.remove_success);
                        Intent intent = new Intent(EtMenuActivity.this, IndexActivity.class);
                        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
                        ActivityUtils.startActivity(intent);

                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                        hideProgressDialog();
                        if (entity.getCode() == MiaoHttpManager.STATUS_FAIL) {
                            showMessage(R.string.toast_internal_error);
                        } else {
                            showMessage(R.string.request_failed_alert);
                        }

                    }

                    @Override
                    public void onError(Exception e) {
                        hideProgressDialog();
                        showMessage(R.string.request_failed_alert);
                    }
                });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }
}
