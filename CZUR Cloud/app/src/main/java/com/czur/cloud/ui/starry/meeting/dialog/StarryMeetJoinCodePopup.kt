package com.czur.cloud.ui.starry.meeting.dialog

import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.view.*
import android.widget.EditText
import android.widget.TextView
import com.blankj.utilcode.util.ScreenUtils
import com.czur.cloud.R
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants
import com.czur.cloud.ui.starry.utils.Tools
import com.czur.cloud.util.validator.StringUtils

/**
 * 通用弹出dialog
 */
class StarryMeetJoinCodePopup : Dialog {

    companion object {
        const val DIMMED_OPACITY = 0.2f
    }

    constructor(context: Context?, theme: Int) : super(context!!, theme) {}

    class Builder(private val context: Context,
                  private val constants: CloudCommonPopupConstants = CloudCommonPopupConstants.COMMON_TWO_BUTTON_YES_NO) {
        private var message: String? = null
        private var title: String? = null
        private var btnPositiveTitle: String? = null
        private var positiveTextColor: Int? = null  // 文字颜色
        private var negativeTextColor: Int? = null // 消极的文字颜色
        private var btnNegativeTitle: String? = null
        private var positiveListener: DialogInterface.OnClickListener? = null
        private var onNegativeListener: DialogInterface.OnClickListener? = null
        private var onDismissListener: DialogInterface.OnDismissListener? = null
        private var onKeyBackListener: DialogInterface.OnKeyListener? = null
        private var canceledOnTouchOutside: Boolean = false
        private var isShowInputText = false

        fun setMessage(message: String?): Builder {
            this.message = message
            return this
        }

        fun setTitle(title: String?): Builder {
            this.title = title
            return this
        }

        fun setInputTextShow(flag: Boolean): Builder {
            this.isShowInputText = flag
            return this
        }

        fun setPositiveTitle(title: String?): Builder {
            this.btnPositiveTitle = title
            return this
        }

        fun setPositiveTextColor(color:Int): Builder{
            this.positiveTextColor = color
            return this
        }

        fun setNegativeTitle(title: String?): Builder {
            this.btnNegativeTitle = title
            return this
        }

        fun setNegativeTextColor(color: Int): Builder {
            negativeTextColor = color
            return this
        }

        fun setOnPositiveListener(positiveListener: DialogInterface.OnClickListener?): Builder {
            this.positiveListener = positiveListener
            return this
        }

        fun setOnNegativeListener(onNegativeListener: DialogInterface.OnClickListener?): Builder {
            this.onNegativeListener = onNegativeListener
            return this
        }

        fun setOnDismissListener(onDismissListener: DialogInterface.OnDismissListener?): Builder {
            this.onDismissListener = onDismissListener
            return this
        }

        fun setCanceledOnTouchOutside(canceledOnTouchOutside : Boolean): Builder{
            this.canceledOnTouchOutside = canceledOnTouchOutside
            return this
        }

        fun setOnKeyBackListener(positiveListener: DialogInterface.OnKeyListener?): Builder {
            this.onKeyBackListener = positiveListener
            return this
        }

        fun create(): StarryMeetJoinCodePopup {
            val inflater =
                context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
            val dialog = StarryMeetJoinCodePopup(context, R.style.TransparentProgressDialog)
            val layout = commonCustomPopLayout(inflater, dialog)
            dialog.setContentView(layout)
            dialog.setCanceledOnTouchOutside(canceledOnTouchOutside)
            val params = dialog.window?.attributes
            params?.dimAmount = DIMMED_OPACITY
            if (ScreenUtils.isLandscape()){//针对高版本(小米12) 横屏状态时,弹出提示窗时,无法拉下状态栏
                dialog.window?.setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE)
            }
            return dialog
        }

        private fun commonCustomPopLayout(
            inflater: LayoutInflater,
            dialog: StarryMeetJoinCodePopup
        ): View {
            val lp = dialog.window?.attributes
            lp?.dimAmount = 0.8f
            dialog.window?.attributes = lp
            dialog.window?.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
            val layout = inflater.inflate(R.layout.starry_meet_join_code_popup, null, false)
            val params = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            dialog.addContentView(layout, params)
            val title = layout.findViewById<View>(R.id.title) as TextView
            val message = layout.findViewById<View>(R.id.message) as TextView
            val positiveBtn = layout.findViewById<View>(R.id.positive_button) as TextView
            val negativeBtn = layout.findViewById<View>(R.id.negative_button) as TextView
            val editText = layout.findViewById<View>(R.id.editText) as EditText

            if (StringUtils.isNotEmpty(this.message)) {
                message.text = this.message + StringUtils.EMPTY
            } else if (constants.message > 0) {
                message.text = context.resources.getString(constants.message)
            }
            if (StringUtils.isNotEmpty(this.title)) {
                title.text = this.title + StringUtils.EMPTY
            } else if (constants.title > 0) {
                title.text = context.resources.getString(constants.title)
            }
            if (StringUtils.isNotEmpty(this.btnPositiveTitle)) {
                positiveBtn.text = this.btnPositiveTitle + StringUtils.EMPTY
            } else if (constants.positiveBtn > 0) {
                positiveBtn.text = context.resources.getString(constants.positiveBtn)
            }
            if (StringUtils.isNotEmpty(this.btnNegativeTitle)) {
                negativeBtn.text = this.btnNegativeTitle + StringUtils.EMPTY
            } else if (constants.negativeBtn > 0) {
                negativeBtn.text = context.resources.getString(constants.negativeBtn)
            }

            editText?.visibility = if (isShowInputText) View.VISIBLE else View.GONE

            // 设置字体颜色
            positiveTextColor?.let {
                positiveBtn.setTextColor(it)
            }

            negativeTextColor?.let {
                negativeBtn.setTextColor(it)
            }

            if (positiveListener != null) {
                positiveBtn.setOnClickListener {
                    positiveListener?.onClick(
                        dialog,
                        constants.positiveBtn
                    )
                    Tools.setViewButtonEnable(it, false)
                    it?.postDelayed(kotlinx.coroutines.Runnable {
                        Tools.setViewButtonEnable(it, true)
                    }, 3000)
                }
            } else {
                positiveBtn.setOnClickListener { dialog.dismiss() }
            }

            if (onNegativeListener != null) {
                negativeBtn.setOnClickListener {
                    onNegativeListener?.onClick(
                        dialog,
                        constants.negativeBtn
                    )
                }
            } else {
                negativeBtn.setOnClickListener { dialog.dismiss() }
            }

            if (onDismissListener != null) {
                dialog.setOnDismissListener(onDismissListener)
            }

            if (onKeyBackListener != null) {
                dialog.setOnKeyListener(onKeyBackListener)
            }

            return layout
        }
    }

}