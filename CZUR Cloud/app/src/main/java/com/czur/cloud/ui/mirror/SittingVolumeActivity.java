package com.czur.cloud.ui.mirror;

import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.czur.cloud.R;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.SittingCommonEvent;
import com.czur.cloud.event.SittingModelChangeEvent;
import com.czur.cloud.ui.component.seekbar.BubbleSeekBarSitting;
import com.czur.cloud.ui.mirror.comm.FastBleConstants;
import com.czur.cloud.ui.mirror.comm.FastBleHttpUtils;
import com.czur.cloud.ui.mirror.comm.FastBleOperationUtils;
import com.czur.cloud.ui.mirror.model.SittingDeviceModel;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

public class SittingVolumeActivity extends SittingBaseActivity {
    private int volume, oldVolume, oldValue;
    private BubbleSeekBarSitting seekBar;
    private TextView volValue;
    private TextView sitting_volume_note_tv;
    private ImageView btnPlus,btnMinus;
    private final int SEEKBARMAX = FastBleConstants.MAX_VOLUME_NUMBER;
    private final int SEEKBARMIN = FastBleConstants.MIN_VOLUME_NUMBER;
    private long lastTime = 0;

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        logI("SittingVolumeActivity.onEvent=" + event.getEventType());

        switch (event.getEventType()) {
            // 静音状态
            case SITTING_APPGET_SILENT:
                if (event instanceof SittingCommonEvent) {
                    SittingCommonEvent commonEventSilent = (SittingCommonEvent) event;
                    String paramsSilent = commonEventSilent.getParams();
                    logI("SittingVolumeActivity.onEvent.SITTING_APPGET_SILENT.paramsSilent=" + paramsSilent);
                    if (paramsSilent != null && !paramsSilent.equals("")) {
                        if (paramsSilent.equals(FastBleConstants.SITTING_SILENT_INT_YES+"")){
                            FastBleOperationUtils.setDeviceSilent(FastBleConstants.SITTING_SILENT_INT_YES);
                        }else{
                            FastBleOperationUtils.setDeviceSilent(FastBleConstants.SITTING_SILENT_INT_NO);
                        }
                        changeUI(paramsSilent);
                    }

                }
                break;

            default:
                break;
        }
    }

    private void changeUI(String volumeSilent) {
        if (volumeSilent.equals(FastBleConstants.SITTING_SILENT_INT_YES+"")){
            sitting_volume_note_tv.setVisibility(View.VISIBLE);
        }else{
            sitting_volume_note_tv.setVisibility(View.GONE);
        }
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_sitting_volume);

        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }

        initView();
        initListener();
    }

    protected void initView() {
        deviceModel = userPreferences.getSittingDeviceModel();

        imgBack = findViewById(R.id.top_bar_back_btn);
        seekBar = (BubbleSeekBarSitting) findViewById(R.id.seekBar);
        volValue = findViewById(R.id.sitting_home_vol_level_tv);
        btnPlus = findViewById(R.id.sitting_home_plus_btn);
        btnMinus = findViewById(R.id.sitting_home_minus_btn);

        volume = deviceModel.getSound();
        if (volume < 1)
            volume = FastBleConstants.SITTING_DEF_VOLUME_INT;
        oldVolume = volume;
        oldValue = volume;

        volValue.setText(volume+"");
        seekBar.setProgress((float) volume);

        setPageTitle(R.string.sitting_home_volume);

        seekBar.setOnProgressChangedListener(new BubbleSeekBarSitting.OnProgressChangedListener(){

            @Override
            public void onProgressActionDown(BubbleSeekBarSitting bubbleSeekBar, int progress, float progressFloat, boolean fromUser) {
            }

            @Override
            public void onProgressChanged(BubbleSeekBarSitting bubbleSeekBar, int progress, float progressFloat, boolean fromUser) {
                if (progress == 0){
                    progress = 1;
                    seekBar.setProgress((float)progress);
                }
                volValue.setText(progress+"");
            }

            @Override
            public void getProgressOnActionUp(BubbleSeekBarSitting bubbleSeekBar, int progress, float progressFloat) {
                if (progress == 0){
                    progress = 1;
                    seekBar.setProgress((float)progress);
                }
                volume = progress;
                volValue.setText(progress+"");
                setVolumeValue();
            }

            @Override
            public void getProgressOnFinally(BubbleSeekBarSitting bubbleSeekBar, int progress, float progressFloat, boolean fromUser) {
            }
        });

        sitting_volume_note_tv = findViewById(R.id.sitting_volume_note_tv);
        int volumeSilent = FastBleOperationUtils.getDeviceSilent();
        changeUI(volumeSilent+"");

    }

    protected void initListener() {
        imgBack.setOnClickListener(this);
        btnPlus.setOnClickListener(this);
        btnMinus.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        int p = volume;

        switch (v.getId()) {
            case R.id.top_bar_back_btn:
//                if (oldVolume != volume) {
//                    SittingDeviceModel deviceModel = userPreferences.getSittingDeviceModel();
//                    deviceModel.setSound(volume);
//                    EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_CHANGE_VOLUME, deviceModel));
//
//                    //String u_id, String equipmentId, String volume
//                    FastBleHttpUtils.setDeviceVolume(userPreferences.getUserId(),
//                            equipmentId,
//                            volume + "");
//                }
                ActivityUtils.finishActivity(this);
                break;

            case R.id.sitting_home_plus_btn:
                p = p + 1;
                if (p > SEEKBARMAX)
                    p = SEEKBARMAX;

                setVolumeProgress(p);
                break;

            case R.id.sitting_home_minus_btn:
                p = p - 1;
                if (p < SEEKBARMIN)
                    p = SEEKBARMIN;

                setVolumeProgress(p);
                break;
        }
    }

    private void setVolumeProgress(int p){
        seekBar.setProgress((float) p);
        volume = p;
        setVolumeValue();
    }

    private void setVolumeValue(){
        FastBleOperationUtils.SetDeviceParams(volume, FastBleConstants.HEAD_SETTING_VOICE_VOLUME);

        SittingDeviceModel deviceModel = userPreferences.getSittingDeviceModel();
        deviceModel.setSound(volume);
        EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_CHANGE_VOLUME, deviceModel));

        //String u_id, String equipmentId, String volume
        FastBleHttpUtils.setDeviceVolume(userPreferences.getUserId(),
                equipmentId,
                volume + "");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }

    }

}
