package com.czur.cloud.ui.starry.model

import java.io.Serializable
/*
{
    "meetingId": 6931,
    "czurId": 3383,
    "meetingAccout": 18500236002,
    "name": "迷尘",
    "headImage": "https://thirdqq.qlogo.cn/g?b=oidb&k=dunYQc7bicTeOfbjy50o7icA&s=100&t=1483282034",
    "mobile": "18500236002",
    "kind": "2",
    "type": 3,
    "isAdmin": false,
    "status": 0,
    "sharing": false,
    "isEnter": false,
    "isResponse": false,
    "response": false,
    "admin": false,
    "enter": false
}
* */

private const val APP_ID_PC = "com.czur.starry.pc"
data class StarryDoingMeetingModel(
    val meetingId: String,
    val czurId: String,
    val meetingAccout: String,
    val name: String,
    val headImage: String,
    val mobile: String,
    val kind: String,
    val type: Int,
    val isAdmin: Boolean,
    val status: Int,
    val sharing: Boolean,
    val isEnter: Boolean,
    val isResponse: Boolean,
    val response: <PERSON>olean,
    val admin: Boolean,
    val enter: Boolean,
    val appId: String,  // 标记是否被PC接听了
    val meetingCode: String,
): Serializable {
    // 是否PC端接入, 如果是, 则appID属性为PC端的APPID
    val isPCEnter: Boolean
        get() = appId == APP_ID_PC
}