package com.czur.cloud.ui.user.download;

import static com.czur.czurutils.log.CZURLogUtilsKt.logE;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.IBinder;

import androidx.annotation.Nullable;

import com.czur.cloud.R;

public class DownloadAFileService extends Service {

    private static final String KEY_TASK_TYPE = "taskType";
    private static final int TASK_TYPE_DEFAULT = -1;  // 兼容之前逻辑
    private static final int TASK_TYPE_CHECK_HISTORY = 1;

    private static final String KEY_CLEAR_PAGE = "clearPage";

    private AFileDownloadManager downloadManager;
    private String updateUrl;
    private String notes;
    private String apkName;

    /**
     * 检查是否有之前未下载完的记录
     *
     * @param context
     */
    public static void checkHistoryDownloadTask(Context context, Boolean clearPage) {
        Intent intent = new Intent(context, DownloadAFileService.class);
        intent.putExtra(KEY_TASK_TYPE, TASK_TYPE_CHECK_HISTORY);
        intent.putExtra(KEY_CLEAR_PAGE, clearPage);
        context.startService(intent);
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        int taskType = TASK_TYPE_DEFAULT;
        boolean needClearPage = true;
        //Bug #20872 [在线升级】测试环境V2.3.377在线升级378时，后台378版本一直处于源源不断的下载中（完成下载后自动重新下载），APP始终识别不到导致无法更新
        if (intent != null) {
            taskType = intent.getIntExtra(KEY_TASK_TYPE, TASK_TYPE_DEFAULT);
            // 按照之前逻辑, 每一次启动这个Service, 都会产生一个新的AFileDownloadManager
            // 然后一个AFileDownloadManager只负责下载1个Apk, 没太明白为什么这样设计.
            needClearPage = intent.getBooleanExtra(KEY_CLEAR_PAGE, true);
        }
        downloadManager = new AFileDownloadManager(this);
        downloadManager.setInstallNeedClearPage(needClearPage);
        switch (taskType) {
            case TASK_TYPE_CHECK_HISTORY:
                processTaskCheckHistory();
                break;
            default:
                processTaskDef(intent);
        }
        downloadManager.resume();

        return super.onStartCommand(intent, flags, startId);
    }

    private void processTaskCheckHistory() {
        downloadManager.checkHistoryDownloadTask();
    }

    private void processTaskDef(Intent intent) {
        updateUrl = intent.getStringExtra("updateUrl");
        notes = intent.getStringExtra("notes");
        apkName = intent.getStringExtra("apkName");
        logI("DownloadApkService.updateUrl=" + updateUrl,
                "notes=" + notes,
                "apkName=" + apkName);
        downloadManager.setApkName(apkName);
        check();
    }

    private void check() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                logI("DownloadApkService.updateUrl=" + updateUrl);
                try {
                    downloadManager.downloadApk(updateUrl, getString(R.string.app_update_title), notes);
                    downloadManager.setUpdateListener(new AFileDownloadManager.OnUpdateListener() {
                        @Override
                        public void update(int currentByte, int totalByte) {
                        }
                    });
                } catch (Exception e) {
                    logE("DownloadApkService.check.e=" + e.toString());
                }
            }
        }).start();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        downloadManager.onPause();
    }
}
