package com.czur.cloud.util

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.graphics.Color
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.core.app.NotificationCompat
import com.czur.cloud.R
import com.czur.czurutils.log.logI
import com.czur.czurutils.log.logE

/**
 * Android 16 通知管理工具类
 * 处理进度通知、高级保护模式等新特性
 */
object Android16NotificationUtils {
    private const val TAG = "Android16NotificationUtils"
    
    // 通知渠道ID
    const val CHANNEL_ID_PROGRESS = "android16_progress_channel"
    const val CHANNEL_ID_DOWNLOAD = "android16_download_channel"
    const val CHANNEL_ID_SYNC = "android16_sync_channel"
    
    /**
     * 初始化Android 16通知渠道
     */
    fun initNotificationChannels(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            
            // 创建进度通知渠道
            createProgressNotificationChannel(notificationManager)
            
            // 创建下载通知渠道
            createDownloadNotificationChannel(notificationManager)
            
            // 创建同步通知渠道
            createSyncNotificationChannel(notificationManager)
            
            logI("$TAG.initNotificationChannels: Android 16 notification channels initialized")
        }
    }
    
    @RequiresApi(Build.VERSION_CODES.O)
    private fun createProgressNotificationChannel(notificationManager: NotificationManager) {
        val channel = NotificationChannel(
            CHANNEL_ID_PROGRESS,
            "进度通知",
            NotificationManager.IMPORTANCE_LOW
        ).apply {
            description = "显示任务进度的通知"
            enableLights(false)
            enableVibration(false)
            setShowBadge(false)
        }
        notificationManager.createNotificationChannel(channel)
    }
    
    @RequiresApi(Build.VERSION_CODES.O)
    private fun createDownloadNotificationChannel(notificationManager: NotificationManager) {
        val channel = NotificationChannel(
            CHANNEL_ID_DOWNLOAD,
            "下载通知",
            NotificationManager.IMPORTANCE_LOW
        ).apply {
            description = "显示下载进度的通知"
            enableLights(false)
            enableVibration(false)
            setShowBadge(false)
        }
        notificationManager.createNotificationChannel(channel)
    }
    
    @RequiresApi(Build.VERSION_CODES.O)
    private fun createSyncNotificationChannel(notificationManager: NotificationManager) {
        val channel = NotificationChannel(
            CHANNEL_ID_SYNC,
            "同步通知",
            NotificationManager.IMPORTANCE_LOW
        ).apply {
            description = "显示同步进度的通知"
            enableLights(false)
            enableVibration(false)
            setShowBadge(false)
        }
        notificationManager.createNotificationChannel(channel)
    }
    
    /**
     * 创建Android 16样式的进度通知
     */
    fun createProgressNotification(
        context: Context,
        title: String,
        content: String,
        progress: Int,
        maxProgress: Int = 100,
        isIndeterminate: Boolean = false
    ): Notification {
        val builder = NotificationCompat.Builder(context, CHANNEL_ID_PROGRESS)
            .setContentTitle(title)
            .setContentText(content)
            .setSmallIcon(R.drawable.ic_czur_logo)
            .setProgress(maxProgress, progress, isIndeterminate)
            .setOngoing(true)
            .setSilent(true)
        
        // 如果是Android 16，尝试使用新的进度样式
        if (Build.VERSION.SDK_INT >= 36) {
            try {
                // 使用Android16ApiCompat创建进度样式通知
                val progressNotification = Android16ApiCompat.createProgressStyleNotification(
                    context = context,
                    channelId = CHANNEL_ID_PROGRESS,
                    title = title,
                    progress = progress,
                    maxProgress = maxProgress
                )

                if (progressNotification != null) {
                    logI("$TAG.createProgressNotification: Using Android 16 progress style")
                    return progressNotification
                }
            } catch (e: Exception) {
                logE("$TAG.createProgressNotification: Failed to create Android 16 style, fallback to standard: ${e.message}")
            }
        }
        
        return builder.build()
    }
    
    /**
     * 创建带分段的进度通知（Android 16新特性）
     */
    fun createSegmentedProgressNotification(
        context: Context,
        title: String,
        progress: Int,
        segments: List<Pair<Int, Int>>, // length, color
        points: List<Pair<Int, Int>>? = null // position, color
    ): Notification {
        if (Build.VERSION.SDK_INT >= 36) {
            try {
                val notification = Android16ApiCompat.createProgressStyleNotification(
                    context = context,
                    channelId = CHANNEL_ID_PROGRESS,
                    title = title,
                    progress = progress,
                    segments = segments,
                    points = points,
                    trackerIconRes = R.drawable.ic_czur_logo
                )

                if (notification != null) {
                    logI("$TAG.createSegmentedProgressNotification: Created segmented progress notification")
                    return notification
                }
            } catch (e: Exception) {
                logE("$TAG.createSegmentedProgressNotification error: ${e.message}")
            }
        }
        
        // 降级到标准进度通知
        return createProgressNotification(context, title, "进度: $progress%", progress)
    }
    
    /**
     * 创建下载进度通知
     */
    fun createDownloadProgressNotification(
        context: Context,
        fileName: String,
        progress: Int,
        downloadSpeed: String? = null
    ): Notification {
        val content = if (downloadSpeed != null) {
            "下载中... $progress% ($downloadSpeed)"
        } else {
            "下载中... $progress%"
        }
        
        // 为下载创建分段显示（已下载部分用绿色，未下载用灰色）
        if (Build.VERSION.SDK_INT >= 36 && progress > 0) {
            val segments = listOf(
                Pair(progress * 10, Color.GREEN), // 已下载部分
                Pair((100 - progress) * 10, Color.GRAY) // 未下载部分
            )
            
            return createSegmentedProgressNotification(
                context = context,
                title = "下载 $fileName",
                progress = progress * 10,
                segments = segments
            )
        }
        
        return NotificationCompat.Builder(context, CHANNEL_ID_DOWNLOAD)
            .setContentTitle("下载 $fileName")
            .setContentText(content)
            .setSmallIcon(android.R.drawable.stat_sys_download)
            .setProgress(100, progress, false)
            .setOngoing(true)
            .setSilent(true)
            .build()
    }
    
    /**
     * 创建同步进度通知
     */
    fun createSyncProgressNotification(
        context: Context,
        syncType: String,
        progress: Int,
        totalItems: Int,
        currentItem: Int
    ): Notification {
        val content = "正在同步... ($currentItem/$totalItems)"
        
        return NotificationCompat.Builder(context, CHANNEL_ID_SYNC)
            .setContentTitle("$syncType 同步")
            .setContentText(content)
            .setSmallIcon(R.drawable.ic_czur_logo)
            .setProgress(100, progress, false)
            .setOngoing(true)
            .setSilent(true)
            .build()
    }
    
    /**
     * 显示通知
     */
    fun showNotification(context: Context, notificationId: Int, notification: Notification) {
        try {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.notify(notificationId, notification)
        } catch (e: Exception) {
            logE("$TAG.showNotification error: ${e.message}")
        }
    }
    
    /**
     * 取消通知
     */
    fun cancelNotification(context: Context, notificationId: Int) {
        try {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.cancel(notificationId)
        } catch (e: Exception) {
            logE("$TAG.cancelNotification error: ${e.message}")
        }
    }
    
    /**
     * 检查通知权限
     */
    fun checkNotificationPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.areNotificationsEnabled()
        } else {
            true
        }
    }
}
