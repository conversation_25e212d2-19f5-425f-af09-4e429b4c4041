package com.czur.cloud.cache;

import com.facebook.imagepipeline.request.ImageRequest;
import com.facebook.imagepipeline.request.ImageRequestBuilder;

/**
 * Created by <PERSON>z on 2018/10/24.
 * Email：<EMAIL>
 */
public class WrongQuestionImageRequest extends ImageRequest {


    private int imageRequestType;
    private String imageRequestObject;
    private ImageRequestBuilder builder;

    public WrongQuestionImageRequest(ImageRequestBuilder builder) {
        super(builder);
        this.builder=builder;
    }

    public WrongQuestionImageRequest build() {
        builder.build();
        return this;
    }

    public int getImageRequestType() {
        return imageRequestType;
    }

    public void setImageRequestType(int imageRequestType) {
        this.imageRequestType = imageRequestType;
    }

    public String getImageRequestObject() {
        return imageRequestObject;
    }

    public void setImageRequestObject(String imageRequestObject) {
        this.imageRequestObject = imageRequestObject;
    }
}
