package com.czur.cloud.ui.et.wifi;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.et.EtManageActivity;

/**
 * Created by Yz on 2018/6/1.
 * Email：<EMAIL>
 */
public class WifiRetryActivity extends BaseActivity implements View.OnClickListener {
    private ImageView normalBackBtn;
    private TextView wifiRetryBtn;
    private TextView wifiRetryQuestionToCallTv;
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_wifi_retry);
        initComponent();
        registerEvent();

    }

    private void initComponent() {

        wifiRetryQuestionToCallTv = (TextView) findViewById(R.id.wifi_retry_question_to_call_tv);
        normalBackBtn = (ImageView) findViewById(R.id.normal_back_btn);
        wifiRetryBtn = (TextView) findViewById(R.id.wifi_retry_btn);
        wifiRetryQuestionToCallTv.setVisibility(BuildConfig.IS_OVERSEAS?View.GONE:View.VISIBLE);

    }

    private void registerEvent() {
        normalBackBtn.setOnClickListener(this);
        wifiRetryBtn.setOnClickListener(this);
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            case R.id.wifi_retry_btn:
                ActivityUtils.finishToActivity(EtWifiListActivity.class, false);
                break;
            case R.id.normal_back_btn:
                ActivityUtils.finishToActivity(EtManageActivity.class,false);
                break;
            default:
                break;
        }
    }
    @Override
    public void onBackPressed() {
        super.onBackPressed();
        ActivityUtils.finishToActivity(EtManageActivity.class,false);
    }
    @Override
    protected void onDestroy() {
        super.onDestroy();
    }
}
