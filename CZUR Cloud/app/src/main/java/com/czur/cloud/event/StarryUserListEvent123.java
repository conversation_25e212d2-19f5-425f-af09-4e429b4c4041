package com.czur.cloud.event;

import com.czur.cloud.netty.bean.StarryRecivedReply;

public class StarryUserListEvent123 extends BaseEvent {
	private StarryRecivedReply params;
	private String room;

	public StarryUserListEvent123(EventType eventType, StarryRecivedReply params, String room) {
		super(eventType);
		this.params=params;
		this.room=room;
	}
	public StarryRecivedReply getParams() {
		return params;
	}
	public String getRoom() {
		return room;
	}

	@Override
	public boolean match(Object obj) {
		return true;
	}
}
