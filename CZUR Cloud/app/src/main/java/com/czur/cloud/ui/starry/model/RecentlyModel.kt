package com.czur.cloud.ui.starry.model

import com.czur.cloud.preferences.UserPreferences
import com.czur.cloud.ui.starry.meeting.model.Model
import com.czur.cloud.ui.starry.meeting.network.HttpManager
import com.czur.cloud.ui.starry.meeting.network.IMeetService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.*

object RecentlyModel: Model() {
    private const val TAG = "RecentlyModel"

    override fun doClear() {

    }

    private val netService: IMeetService by lazy { HttpManager.getService() }

    private val userPreferences by lazy {
        UserPreferences.getInstance()
    }

    suspend fun getCallRecords(pageNum: String, pageSize:String): StarryCallRecordDataModel {
        val model = withContext(Dispatchers.IO) {
            netService.getCallRecords(pageNum, pageSize).body ?: StarryCallRecordDataModel()
        }

        return model
    }


    suspend fun getCallRecordDetail(meetingID: String): StarryCallRecordDetailModel{
        val model = withContext(Dispatchers.IO) {
            netService.getCallRecordDetail(meetingID).body ?: StarryCallRecordDetailModel(id = "0", createTime = Date())
        }
        return model
    }

    suspend fun deleteCallRecords(meetingID: String): String{
        val model = withContext(Dispatchers.IO) {
            netService.deleteCallRecords(meetingID).body ?: "-1"
        }
        return model
    }

    suspend fun deleteAllRecords(): Int {
        val ret = withContext(Dispatchers.IO) {
            netService.deleteAllRecords().data ?: 0
        }

        return ret
    }
}