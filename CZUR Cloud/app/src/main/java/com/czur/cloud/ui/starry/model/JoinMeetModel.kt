package com.czur.cloud.ui.starry.model

import android.util.Log
import com.blankj.utilcode.util.AppUtils
import com.czur.cloud.BuildConfig
import com.czur.cloud.common.CZURConstants
import com.czur.cloud.network.core.MiaoHttpManager
import com.czur.cloud.preferences.UserPreferences
import com.czur.cloud.ui.starry.meeting.model.MeetingModel
import com.czur.cloud.ui.starry.meeting.model.Model
import com.czur.cloud.ui.starry.meeting.model.StarryHttpResData
import com.czur.czurutils.log.logE
import com.czur.czurutils.log.logI
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import okhttp3.MultipartBody
import okhttp3.Request
import okhttp3.RequestBody

object JoinMeetModel: Model() {

    private const val TAG = "JoinMeetModel"

    // 加入会议的正确返回内容
    var joinMeetingReturnData: JoinMeetData = JoinMeetData()

    var returnLeftTimes = "0"

    private val userPreferences by lazy {
        UserPreferences.getInstance()
    }

    override fun doClear() {
    }

    // 加入会议-APP、PC
    //6005	会议密码有误
    //6007	会议已结束
    //6002	会议已锁定
    //6001	会议不存在
    // 6000 其它错误
    // 1000 成功
    const val ERROR_CODE_OTHER      = 6000
    const val ERROR_CODE_NOT_EXIST  = 6001
    const val ERROR_CODE_CLOCK      = 6002
    const val ERROR_CODE_PWD_WRONG  = 6005
    const val ERROR_CODE_OVER       = 6007
    const val ERROR_CODE_PWD_MUCH   = 6008
    const val ERROR_MEMBER_MUCH     = 2031

    fun joinMeeting(meetingCode: String, meetingPassword: String): Int{
        try {
            val requestBody: RequestBody = MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("meetingCode", meetingCode)
                .addFormDataPart("meetingPassword", meetingPassword)
                .build()
            val request: Request = Request.Builder()
                .header("udid", userPreferences.imei)
                .header("App-Key", CZURConstants.CLOUD_ANDROID)
                .header("Api-Build", AppUtils.getAppVersionCode().toString())
                .header("App-Bundle", AppUtils.getAppPackageName())
                .header("T-ID", userPreferences.token)
                .header("U-ID", userPreferences.userId)
                .header("X-COUNTRY-CODE", userPreferences.countryCode)
                .url(BuildConfig.BASE_STARRY_URL + "joinMeeting")
                .post(requestBody)
                .build()

            val response = MiaoHttpManager.getInstance().httpClient.newCall(request).execute()
            val responseString = response.body!!.string()
            if (responseString.trim { it <= ' ' }
                    .startsWith("<html>") || responseString.trim { it <= ' ' }
                    .endsWith("</html>")) {
                return ERROR_CODE_OTHER
            }
            val turnsType = object : TypeToken<StarryHttpResData<JoinMeetData>>() {}.type
            val model = Gson().fromJson<StarryHttpResData<JoinMeetData>>(responseString, turnsType)
            // 请求成功
            if (response.isSuccessful) {
                val code = model.code
                if (code == MiaoHttpManager.STATUS_SUCCESS) {
                    logI("${TAG}.joinMeeting:${responseString}.成功!")
                    // model.data
                    joinMeetingReturnData = model.data
                } else {
                    logI("${TAG}.joinMeeting:${responseString}.失败!")
                    if (code == ERROR_CODE_PWD_MUCH) {
                        returnLeftTimes = model.data.expire
                    }else if(code == ERROR_CODE_NOT_EXIST){

                    }
                }
                return code
            } else {
                return ERROR_CODE_OTHER
            }
        } catch (e: java.lang.Exception) {
            logE("${TAG}.joinMeeting.e=${e.toString()}")

        }

        return ERROR_CODE_OTHER
    }

    suspend fun checkShareMeetingInfo(meetingUUID: String): Int{
        try {
            val request: Request = Request.Builder()
                .header("udid", userPreferences.imei)
                .header("App-Key", CZURConstants.CLOUD_ANDROID)
                .header("Api-Build", AppUtils.getAppVersionCode().toString())
                .header("App-Bundle", AppUtils.getAppPackageName())
                .header("T-ID", userPreferences.token)
                .header("U-ID", userPreferences.userId)
                .header("X-COUNTRY-CODE", userPreferences.countryCode)
                .url(BuildConfig.BASE_STARRY_URL + "starry/share/shareMeetingInfo/${meetingUUID}")
                .build()

            val response = MiaoHttpManager.getInstance().httpClient.newCall(request).execute()
            val responseString = response.body!!.string()
            Log.i(TAG, "checkShareMeetingInfo.responseString=$responseString")
            if (responseString.trim { it <= ' ' }
                    .startsWith("<html>") || responseString.trim { it <= ' ' }
                    .endsWith("</html>")) {
                return ERROR_CODE_OTHER
            }
            val turnsType = object : TypeToken<StarryHttpResData<JoinMeetData>>() {}.type
            val model = Gson().fromJson<StarryHttpResData<JoinMeetData>>(responseString, turnsType)
            // 请求成功
            if (response.isSuccessful) {
                val code = model.code
                joinMeetingReturnData = model.data
                return code
            } else {
                return ERROR_CODE_OTHER
            }
        } catch (e: java.lang.Exception) {
            logE("${TAG}.checkShareMeetingInfo.e=${e.toString()}")

        }
        return ERROR_CODE_OTHER
    }

    fun checkPassword(meetingCode: String, meetingPwd: String): Int{
        try {
            val requestBody: RequestBody = MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("code", meetingCode)
                .addFormDataPart("password", meetingPwd)
                .build()
            val request: Request = Request.Builder()
                .header("udid", userPreferences.imei)
                .header("App-Key", CZURConstants.CLOUD_ANDROID)
                .header("Api-Build", AppUtils.getAppVersionCode().toString())
                .header("App-Bundle", AppUtils.getAppPackageName())
                .header("T-ID", userPreferences.token)
                .header("U-ID", userPreferences.userId)
                .header("X-COUNTRY-CODE", userPreferences.countryCode)
                .url(BuildConfig.BASE_STARRY_URL + "starry/share/checkPassword")
                .post(requestBody)
                .build()
            Log.i(TAG, "checkPassword.request=${request}")

            val response = MiaoHttpManager.getInstance().httpClient.newCall(request).execute()
            val responseString = response.body!!.string()
            Log.i(TAG, "checkPassword.responseString=$responseString")
            if (responseString.trim { it <= ' ' }
                    .startsWith("<html>") || responseString.trim { it <= ' ' }
                    .endsWith("</html>")) {
                return ERROR_CODE_OTHER
            }
            val turnsType = object : TypeToken<StarryHttpResData<Boolean>>() {}.type
            val model = Gson().fromJson<StarryHttpResData<Boolean>>(responseString, turnsType)
            // 请求成功
            if (response.isSuccessful) {
                val code = model.code
                if (code == MiaoHttpManager.STATUS_SUCCESS) {
                    logI("${TAG}.checkPassword:${responseString}.成功!")
                    if (model.data){
                        return MiaoHttpManager.STATUS_SUCCESS
                    }else {
                        return ERROR_CODE_PWD_WRONG
                    }
                } else {
                    logI("${TAG}.checkPassword:${responseString}.失败!")
                    return code
                }

            } else {
                return ERROR_CODE_OTHER
            }
        } catch (e: java.lang.Exception) {
            logE("${TAG}.checkPassword.e=${e.toString()}")

        }
        return ERROR_CODE_OTHER
    }

}