package com.czur.cloud.ui.component.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.czur.cloud.R;

public class SocialShareDialog extends Dialog implements View.OnClickListener {
    private Context mContext;
    private LinearLayout weixinShare;
    private LinearLayout friendShare;
    private LinearLayout qqShare;
    private LinearLayout qqZoneShare;
    private LinearLayout weiboShare;
    private TextView shareDialogCancelBtn;
    public SocialShareDialog(Context context, ShareDialogOnClickListener shareDialogOnClickListener) {
        //重点实现R.style.DialogStyle 动画效果
        this(context, R.style.SocialAccountDialogStyle);
        this.shareDialogOnClickListener = shareDialogOnClickListener;
        mContext = context;
    }

    public SocialShareDialog(Context context, int themeResId) {
        super(context, themeResId);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_share_bottom_sheet);
        WindowManager.LayoutParams params = getWindow().getAttributes();


        weixinShare = (LinearLayout)getWindow().findViewById(R.id.weixin_share);
        friendShare = (LinearLayout) getWindow().findViewById(R.id.friend_share);
        qqShare = (LinearLayout) getWindow().findViewById(R.id.qq_share);
        qqZoneShare = (LinearLayout) getWindow().findViewById(R.id.qq_zone_share);
        weiboShare = (LinearLayout) getWindow().findViewById(R.id.weibo_share);
        shareDialogCancelBtn = (TextView) getWindow().findViewById(R.id.share_dialog_cancel_btn);


        //设置显示的位置
        params.gravity = Gravity.BOTTOM;
        //设置dialog的宽度
        params.width = WindowManager.LayoutParams.MATCH_PARENT;
        params.height = WindowManager.LayoutParams.WRAP_CONTENT;
        getWindow().setAttributes(params);

        weixinShare.setOnClickListener(this);
        friendShare.setOnClickListener(this);
        qqShare.setOnClickListener(this);
        qqZoneShare.setOnClickListener(this);
        weiboShare.setOnClickListener(this);
        shareDialogCancelBtn.setOnClickListener(this);

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.weixin_share:
                if (shareDialogOnClickListener !=null) {
                shareDialogOnClickListener.onShareItemClick(R.id.weixin_share);
                }
                break;
            case R.id.friend_share:
                if (shareDialogOnClickListener !=null) {
                    shareDialogOnClickListener.onShareItemClick(R.id.friend_share);
                }
                break;
            case R.id.qq_share:
                if (shareDialogOnClickListener !=null) {
                    shareDialogOnClickListener.onShareItemClick(R.id.qq_share);
                }
                break;
            case R.id.qq_zone_share:
                if (shareDialogOnClickListener !=null) {
                    shareDialogOnClickListener.onShareItemClick(R.id.qq_zone_share);
                }
                break;
            case R.id.weibo_share:
                if (shareDialogOnClickListener !=null) {
                    shareDialogOnClickListener.onShareItemClick(R.id.weibo_share);
                }
                break;
            case R.id.share_dialog_cancel_btn:
                if (shareDialogOnClickListener !=null) {
                    shareDialogOnClickListener.onShareItemClick(R.id.share_dialog_cancel_btn);
                }
                break;
            default:
                break;
        }
    }


    /**

       点击事件接口

     **/
    public interface ShareDialogOnClickListener {
        /**
         *
         * @param viewId
         */
        void onShareItemClick(int viewId);
    }
    private ShareDialogOnClickListener shareDialogOnClickListener;

    private void setShareDialogOnClickListener(ShareDialogOnClickListener shareDialogOnClickListener) {
        this.shareDialogOnClickListener = shareDialogOnClickListener;

    }

}