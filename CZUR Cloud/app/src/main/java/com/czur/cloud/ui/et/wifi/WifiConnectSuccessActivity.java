package com.czur.cloud.ui.et.wifi;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.WifiConnectSuccesEvent;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.et.EtManageActivity;

import org.greenrobot.eventbus.EventBus;

/**
 * Created by Yz on 2018/6/1.
 * Email：<EMAIL>
 */
public class WifiConnectSuccessActivity extends BaseActivity implements View.OnClickListener {
    private ImageView normalBackBtn;
    private TextView wifiConnectSuccessBtn;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_wifi_connect_success);
        initComponent();
        registerEvent();

    }

    private void initComponent() {
        normalBackBtn = (ImageView) findViewById(R.id.normal_back_btn);
        wifiConnectSuccessBtn = (TextView) findViewById(R.id.wifi_connect_success_btn);
    }

    private void registerEvent() {
        normalBackBtn.setOnClickListener(this);
        wifiConnectSuccessBtn.setOnClickListener(this);
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.normal_back_btn:
            case R.id.wifi_connect_success_btn:
                EventBus.getDefault().post(new WifiConnectSuccesEvent(EventType.WIFI_CONNECT_SUCCESS));
                ActivityUtils.finishToActivity(EtManageActivity.class, false);
                break;
            default:
                break;
        }
    }
    @Override
    public void onBackPressed() {
        super.onBackPressed();
        EventBus.getDefault().post(new WifiConnectSuccesEvent(EventType.WIFI_CONNECT_SUCCESS));
        ActivityUtils.finishToActivity(EtManageActivity.class, false);
    }
    @Override
    protected void onDestroy() {
        super.onDestroy();
    }
}
