package com.czur.cloud.ui.mirror.comm;

import static com.czur.czurutils.log.CZURLogUtilsKt.logE;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.util.Log;

import com.alibaba.sdk.android.oss.ClientException;
import com.alibaba.sdk.android.oss.OSS;
import com.alibaba.sdk.android.oss.ServiceException;
import com.alibaba.sdk.android.oss.model.GetObjectRequest;
import com.alibaba.sdk.android.oss.model.GetObjectResult;
import com.alibaba.sdk.android.oss.model.ObjectMetadata;
import com.alibaba.sdk.android.oss.model.PutObjectRequest;
import com.alibaba.sdk.android.oss.model.PutObjectResult;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.SittingCommonEvent;
import com.czur.cloud.model.BaseModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.MirrorOfflinePreferences;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.mirror.model.SittingDeviceModel;
import com.czur.cloud.ui.mirror.model.SittingDeviceOfflineModel;
import com.czur.cloud.ui.mirror.model.SittingUpdateModel;
import com.google.gson.Gson;

import org.greenrobot.eventbus.EventBus;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import okhttp3.FormBody;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class FastBleHttpUtils {
    public static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    /**
     * 下载文件
     */
    public static boolean downloadOSSPicture(String imagePath, String imgaeName, String imgKey) {
        logI("downloadOSSPicture.imagePath"+imagePath,
                "imgaeName="+imgaeName,
                "imgKey="+imgKey);
        if (!NetworkUtils.isConnected()) {
            return false;
        }

        boolean isSuccess = false;
        if (!FileUtils.createOrExistsDir(imagePath)) {
            return false;
        }
        OSS ossClient = OSSInstanceSitting.Companion.getInstance().oss();

        if (ossClient == null) {
            return false;
        }

        //构造下载文件请求
        GetObjectRequest get = new GetObjectRequest(BuildConfig.MIRROR_BUCKET, imgKey);
        try {
            // 同步执行下载请求，返回结果
            GetObjectResult getResult = ossClient.getObject(get);
            // 获取文件输入流
            InputStream inputStream = getResult.getObjectContent();
            byte[] buffer = new byte[2048];
            int len;
            String sourcePath = imagePath + "/" + imgaeName ;
            FileOutputStream downloadStream = new FileOutputStream(sourcePath);
            while ((len = inputStream.read(buffer)) != -1) {
                // 处理下载的数据，比如图片展示或者写入文件等
                downloadStream.write(buffer, 0, len);
                downloadStream.flush();
            }
            downloadStream.close();
            inputStream.close();
            logI("downloadOSSPicture.download success");
            isSuccess = true;

            // 下载后可以查看文件元信息
            ObjectMetadata metadata = getResult.getMetadata();
            Log.d("ContentType", metadata.getContentType());
        } catch (ClientException e) {
            logE("downloadOSSPicture.download defeat" + e.toString());
            // 本地异常如网络异常等
            e.printStackTrace();
        } catch (ServiceException e) {
            logE("downloadOSSPicture.download defeat:" + e.toString());
            // 服务异常
            logE(e.toString());
            logE("downloadOSSPicture.RequestId:" + e.getRequestId());
            logE("downloadOSSPicture.ErrorCode:" + e.getErrorCode());
            logE("downloadOSSPicture.HostId:" + e.getHostId());
            logE("downloadOSSPicture.RawMessage:" + e.getRawMessage());
        } catch (IOException e) {
            logE("downloadOSSPicture.download defeat:" + e.toString());
            e.printStackTrace();
        } catch (Exception e) {
            logE("downloadOSSPicture.unknown exception:" + e.toString());
            return false;
        }
        return isSuccess;
    }

    // app端固件版本检测（返回增量包）
    // http://{固件检查HOST}/v1/mirror/half/version.do
    // BuildConfig.CHECK_MIRROR_UPDATE_URL
    // app端固件版本检测（返回增量包）
    public static SittingUpdateModel getSittingServerVersionInfo(String version, String udid, String t_id, String u_id) {
        if (!NetworkUtils.isConnected()) {
            return null;
        }
        try {
            RequestBody requestBody = RequestBody.create(JSON, version);
            Request request = new Request
                    .Builder()
                    .header("udid", udid)
                    .header("App-Key", CZURConstants.CLOUD_ANDROID)
                    .header("T-ID", t_id)
                    .header("U-ID", u_id)
                    .header("Content-Type", "application/json")
                    .url(BuildConfig.CHECK_MIRROR_UPDATE_URL)
                    .post(requestBody)
                    .build();

            Response response = MiaoHttpManager.getInstance().getHttpClient().newCall(request).execute();

            String responseString = response.body().string();

            if (responseString.trim().startsWith("<html>") || responseString.trim().endsWith("</html>") ){
                return null;
            }
            BaseModel baseModel = new Gson().fromJson(responseString, BaseModel.class);
            // 请求成功
            if (response.isSuccessful()) {
                int code = baseModel.getCode();
                if (code == MiaoHttpManager.STATUS_SUCCESS) {
                    logI("getSittingServerVersionInfo.固件版本检测 成功!");
                    String body = baseModel.getBody();
                    SittingUpdateModel updateModel = new Gson().fromJson(body, SittingUpdateModel.class);
                    return updateModel;
                } else {
                    logI("getSittingServerVersionInfo.固件版本检测 失败!");
                    // only for test
                    SittingUpdateModel updateModel = new SittingUpdateModel();
                    updateModel.testSittingUpdateModel();
                    return updateModel;
//                    return null;
                }
            } else {
                return null;
            }
        } catch (Exception e) {
            logE("getSittingServerVersionInfo.e=" + e.toString());
            return null;
        }
    }

    // 设置坐姿识别灵敏度
    public static void setSittingDeviceLevel(String u_id, String equipmentId, String level) {
        if (!NetworkUtils.isConnected()) {
            return;
        }
        HttpManager.getInstance().request().setSittingDeviceLevel(
                u_id,
                equipmentId,
                level,
                String.class,
                new MiaoHttpManager.Callback<String>() {

                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onError(Exception e) {
                        logE("FastBleHttpUtils.setSittingDeviceLevel.onError="+e.toString());
                    }
                });
    }

    // 是否开启坐姿提醒
    public static void setSittingDeviceStatus(String u_id, String equipmentId, String status) {
        if (!NetworkUtils.isConnected()) {
            return;
        }
        HttpManager.getInstance().request().setSittingDeviceStatus(
                u_id,
                equipmentId,
                status,
                String.class,
                new MiaoHttpManager.Callback<String>() {

                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onError(Exception e) {
                        logE("FastBleHttpUtils.setSittingDeviceStatus.onError="+e.toString());
                    }
                });
    }

    // 音量大小
    public static void setDeviceVolume(String u_id, String equipmentId, String volume) {
        if (!NetworkUtils.isConnected()) {
            return;
        }
        HttpManager.getInstance().request().setSittingDeviceSound(
                u_id,
                equipmentId,
                volume,
                String.class,
                new MiaoHttpManager.Callback<String>() {

                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onError(Exception e) {
                        logE("FastBleHttpUtils.setDeviceVolume.onError="+e.toString());
                    }
                });
    }

    // 久坐时长
    public static void setSittingDeviceSedentaryTime(String u_id, String equipmentId, String sedentaryTime) {
        if (!NetworkUtils.isConnected()) {
            return;
        }
        HttpManager.getInstance().request().setSittingDeviceSedentaryTime(
                u_id,
                equipmentId,
                sedentaryTime,
                String.class,
                new MiaoHttpManager.Callback<String>() {

                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onError(Exception e) {
                        logE("FastBleHttpUtils.setSittingDeviceSedentaryTime.onError="+e.toString());
                    }
                });
    }

    // 设置提示灯亮度  0-5
    public static void setSittingDeviceLightLevel(String u_id, String equipmentId, String level) {
        if (!NetworkUtils.isConnected()) {
            return;
        }
        HttpManager.getInstance().request().setSittingDeviceLightLevel(
                u_id,
                equipmentId,
                level,
                String.class,
                new MiaoHttpManager.Callback<String>() {

                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onError(Exception e) {
                        logE("FastBleHttpUtils.setSittingDeviceLightLevel.onError="+e.toString());
                    }
                });
    }

    // 录入状态
    public static void setSittingDeviceInputStatus(String u_id, String equipmentId, String inputStatus) {
        if (!NetworkUtils.isConnected()) {
            return;
        }
        HttpManager.getInstance().request().setSittingDeviceInputStatus(
                u_id,
                equipmentId,
                inputStatus,
                String.class,
                new MiaoHttpManager.Callback<String>() {

                    @Override
                    public void onStart() {                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onError(Exception e) {
                        logE("FastBleHttpUtils.setSittingDeviceInputStatus.onError="+e.toString());
                    }
                });
    }

    // 标准坐姿图片
    public static void setSittingDeviceStandPositionImage(String u_id, String equipmentId, String standPositionImage) {
        if (!NetworkUtils.isConnected()) {
            return;
        }
        HttpManager.getInstance().request().setSittingDeviceStandPositionImage(
                u_id,
                equipmentId,
                standPositionImage,
                String.class,
                new MiaoHttpManager.Callback<String>() {

                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onError(Exception e) {
                        logE("FastBleHttpUtils.setSittingDeviceStandPositionImage.onError="+e.toString());
                    }
                });
    }

    // 设置坐姿模式
    public static void setSittingPostureMode(String u_id, String equipmentId, String postureMode) {
        if (!NetworkUtils.isConnected()) {
            return;
        }
        HttpManager.getInstance().request().setSettingPostureMode(
                u_id,
                equipmentId,
                postureMode,
                String.class,
                new MiaoHttpManager.Callback<String>() {

                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onError(Exception e) {
                        logE("FastBleHttpUtils.setSittingPostureMode.onError="+e.toString());
                    }
                });
    }

    // 设置坐姿开关 0为关 1为开
    public static void setSettingPostureSwitch(String u_id, String equipmentId, String postureSwitch) {
        if (!NetworkUtils.isConnected()) {
            return;
        }
        HttpManager.getInstance().request().setSettingPostureSwitch(
                u_id,
                equipmentId,
                postureSwitch,
                String.class,
                new MiaoHttpManager.Callback<String>() {

                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onError(Exception e) {
                        logE("FastBleHttpUtils.setSettingPostureSwitch.onError="+e.toString());
                    }
                });
    }

    // 灯光开关 1为开 0为关
    public static void setSettingLightSwitch(String u_id, String equipmentId, String lightSwitch) {
        if (!NetworkUtils.isConnected()) {
            return;
        }

        HttpManager.getInstance().request().setSettingLightSwitch(
                u_id,
                equipmentId,
                lightSwitch,
                String.class,
                new MiaoHttpManager.Callback<String>() {

                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onError(Exception e) {
                        logE("FastBleHttpUtils.setSettingLightSwitch.onError="+e.toString());
                    }
                });
    }

    // 设置设备名称
    public static void setDeviceAlias(String u_id, String equipmentUuid, String alias) {
        if (!NetworkUtils.isConnected()) {
            return;
        }
        if (equipmentUuid.equals("") || u_id.equals("0")){
            return;
        }

        HttpManager.getInstance().request().setDeviceAlias(
                u_id,
                equipmentUuid,
                alias,
                String.class,
                new MiaoHttpManager.Callback<String>() {

                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onError(Exception e) {
                        logE("FastBleHttpUtils.setDeviceAlias.onError="+e.toString());
                    }
                });
    }

    // 获取设备初始化配置文件app
    // 获取类型 1.app_config 2.sp_param_config 3.sys_config
    // // 获取设备初始化配置文件algo
    public static void getConfigJsonAndSendDevice(String type, String udid, String t_id, String u_id,
                                                  String u_sn, String uuid, String param) {
        logI("getConfigJsonAndSendDevice.type="+type+
                ",udid="+udid+
                ",t_id="+t_id+
                ",u_id="+u_id+
                ",u_sn="+u_sn+
                ",uuid="+uuid+
                ",param="+param);
        if (!NetworkUtils.isConnected()) {
            return;
        }
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Boolean>() {
            @Override
            public Boolean doInBackground() throws Throwable {

                return getAlgoConfigAndSendDevice(type, udid, t_id, u_id, u_sn, uuid, param);
            }

            @Override
            public void onSuccess(Boolean result) {

            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
            }
        });
    }

    private static boolean getAlgoConfigAndSendDevice(String type, String udid, String t_id, String u_id,
                                                      String u_sn, String uuid, String param) {
        if (!NetworkUtils.isConnected()) {
            return false;
        }
        try {
            RequestBody requestBody = new FormBody.Builder()
                    .add("type",type)
                    .add("u_id",u_id)
                    .add("sn",u_sn)
                    .build();

            // udid,App-Key,U-ID,T-ID=xxxx
            Request request = new Request
                    .Builder()
                    .header("udid", udid)
                    .header("App-Key", CZURConstants.CLOUD_ANDROID)
                    .header("T-ID", t_id)
                    .header("U-ID", u_id)
//                    .header("Content-Type", "application/x-www-form-urlencoded")
                    // https://test.czur.cc/api/v3/mirror/getAppConfig
                    .url(BuildConfig.BASE_URL + "v3/mirror/getAppConfig")
                    .post(requestBody)
                    .build();

            Response response = MiaoHttpManager.getInstance().getHttpClient().newCall(request).execute();
            String responseString = response.body().string();
            if (responseString.trim().startsWith("<html>") || responseString.trim().endsWith("</html>") ){
                return false;
            }
            BaseModel baseModel = new Gson().fromJson(responseString, BaseModel.class);

            // 请求成功
            if (response.isSuccessful()) {
                int code = baseModel.getCode();
                if (code == MiaoHttpManager.STATUS_SUCCESS) {
                    // 进行json数据比对；
                    String sendStr = baseModel.getBody();
                    String localCommonPicturePath = CZURConstants.MIRROR_PATH +
                            u_id + "/" + uuid + "/" ;
                    String jsonFileName = type+".json";
                    FastBleToolUtils.setLocalJsonDir(localCommonPicturePath);
                    boolean flag = FastBleToolUtils.isCompTwoJson(sendStr, jsonFileName);
                    logI("FastBleHttpUtils.getAlgoConfig.isCompTwoJson="+flag);

                    if (!flag) {
                        ///// 获取成功，需要发给设备端；
                        FastBleOperationUtils.SetDeviceParamsStr(sendStr, param);
                    }

                    logI("FastBleHttpUtils.getAlgoConfig.成功!");
                    return true;
                } else {
                    logI("FastBleHttpUtils.getAlgoConfig.失败!");
                    return false;
                }
            } else {
                return false;
            }
        } catch (Exception e) {
            logE("FastBleHttpUtils.getAlgoConfig.e=" + e.toString());
            return false;
        }
    }

    // 获取设备初始化配置文件sys
    public static void getSysConfigAndSendDeviceNew(String type, String udid, String t_id, String u_id, String param) {
        if (!NetworkUtils.isConnected()) {
            return;
        }
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Boolean>() {
            @Override
            public Boolean doInBackground() throws Throwable {

                return getSysConfigAndSendDevice(type, udid, t_id, u_id, param);
            }

            @Override
            public void onSuccess(Boolean result) {

            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
            }
        });
    }

    private static boolean getSysConfigAndSendDevice(String type, String udid, String t_id, String u_id, String param) {
        if (!NetworkUtils.isConnected()) {
            return false;
        }
        try {
            RequestBody requestBody = new FormBody.Builder()
                    .add("type",type)
                    .add("u_id",u_id)
                    .build();

            // udid,App-Key,U-ID,T-ID=xxxx
            Request request = new Request
                    .Builder()
                    .header("udid", udid)
                    .header("App-Key", CZURConstants.CLOUD_ANDROID)
                    .header("T-ID", t_id)
                    .header("U-ID", u_id)
//                    .header("Content-Type", "application/x-www-form-urlencoded")
                    // https://test.czur.cc/api/v3/mirror/getAppConfig
                    .url(BuildConfig.BASE_URL + "v3/mirror/getAppConfig")
                    .post(requestBody)
                    .build();

            Response response = MiaoHttpManager.getInstance().getHttpClient().newCall(request).execute();
            String responseString = response.body().string();
            if (responseString.trim().startsWith("<html>") || responseString.trim().endsWith("</html>") ){
                return false;
            }
            BaseModel baseModel = new Gson().fromJson(responseString, BaseModel.class);

            // 请求成功
            if (response.isSuccessful()) {
                int code = baseModel.getCode();
                if (code == MiaoHttpManager.STATUS_SUCCESS) {
                    ///// 获取成功，需要发给设备端；
                    String sendStr = baseModel.getBody();
                    FastBleOperationUtils.SetDeviceParamsStr( sendStr, param);
                    logI("FastBleHttpUtils.getSysConfig.成功!");
                    return true;
                } else {
                    logI("FastBleHttpUtils.getSysConfig.失败!");
                    return false;
                }
            } else {
                return false;
            }
        } catch (Exception e) {
            logE("FastBleHttpUtils.getSysConfig.e=" + e.toString());
            return false;
        }
    }

    // 坐姿仪上传报告信息接口
    public static void sendTimeUseReport(String syncJson, String udid, String t_id, String u_id) {
        if (!NetworkUtils.isConnected()) {
            return;
        }

        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Boolean>() {
            @Override
            public Boolean doInBackground() throws Throwable {

                return addTimeUseReport(syncJson, udid, t_id, u_id);
            }

            @Override
            public void onSuccess(Boolean result) {

            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
            }
        });
    }

    private static boolean addTimeUseReport(String syncJson, String udid, String t_id, String u_id) {
        if (!NetworkUtils.isConnected()) {
            return false;
        }
        try {
            RequestBody requestBody = RequestBody.create(JSON, syncJson);

            //请求超时设置
//            MiaoHttpManager.getInstance().getHttpClient().newBuilder()
//                    .connectTimeout(60000, TimeUnit.SECONDS)
//                    .readTimeout(60000, TimeUnit.SECONDS);

            Request request = new Request
                    .Builder()
                    .header("udid", udid)
                    .header("App-Key", CZURConstants.CLOUD_ANDROID)
                    .header("T-ID", t_id)
                    .header("U-ID", u_id)
                    .header("Content-Type", "application/json")
                    .url(BuildConfig.BASE_URL + CZURConstants.SYNC_MIRROR_URL)
                    .post(requestBody)
                    .build();

            Response response = MiaoHttpManager.getInstance().getHttpClient().newCall(request).execute();

            String responseString = response.body().string();

            if (responseString.trim().startsWith("<html>") || responseString.trim().endsWith("</html>") ){
                return false;
            }
            BaseModel baseModel = new Gson().fromJson(responseString, BaseModel.class);
            // 请求成功
            if (response.isSuccessful()) {
                int code = baseModel.getCode();
                if (code == MiaoHttpManager.STATUS_SUCCESS) {
                    logI("addTimeUseReport.上传数据至服务器 成功!");
                    EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_REPORT_SUCCESS, ""));
                    return true;
                } else {
                    logI("addTimeUseReport.上传数据至服务器 失败!");
                    return false;
                }
            } else {
                return false;
            }
        } catch (Exception e) {
            logE("addTimeUseReport.e="+e.toString());
            return false;
        }
    }

    private static boolean addTimeUseReportNoFresh(String syncJson, String udid, String t_id, String u_id) {
        if (!NetworkUtils.isConnected()) {
            return false;
        }
        try {
            RequestBody requestBody = RequestBody.create(JSON, syncJson);

            Request request = new Request
                    .Builder()
                    .header("udid", udid)
                    .header("App-Key", CZURConstants.CLOUD_ANDROID)
                    .header("T-ID", t_id)
                    .header("U-ID", u_id)
                    .header("Content-Type", "application/json")
                    .url(BuildConfig.BASE_URL + CZURConstants.SYNC_MIRROR_URL)
                    .post(requestBody)
                    .build();

            Response response = MiaoHttpManager.getInstance().getHttpClient().newCall(request).execute();

            String responseString = response.body().string();

            if (responseString.trim().startsWith("<html>") || responseString.trim().endsWith("</html>") ){
                return false;
            }
            BaseModel baseModel = new Gson().fromJson(responseString, BaseModel.class);
            // 请求成功
            if (response.isSuccessful()) {
                int code = baseModel.getCode();
                if (code == MiaoHttpManager.STATUS_SUCCESS) {
                    logI("addTimeUseReportNoFresh.上传数据至服务器 成功!");

                    return true;
                } else {
                    logI("addTimeUseReportNoFresh.上传数据至服务器 失败!");
                    return false;
                }
            } else {
                return false;
            }
        } catch (Exception e) {
            logE("addTimeUseReportNoFresh.e="+e.toString());
            return false;
        }
    }

    public static SittingOssTokenModel getSittingOssInfo(String u_id, String clientId) {
        if (!NetworkUtils.isConnected()) {
            return null;
        }
        try {
            MiaoHttpEntity<SittingOssTokenModel> sitOssInfo = HttpManager.getInstance().request()
                    .getSittingOssInfo(
                            u_id,
                            clientId,
                            SittingOssTokenModel.class);
            if (sitOssInfo.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return sitOssInfo.getBody();
            }else{
                return null;
            }
        } catch (Exception e) {
            logE(e.toString());
            e.printStackTrace();
        }
        return null;
    }

    // 提交坐姿图片信息接口
    /*
    * "imgDatas": [
    {
      "dateString": "2021-01-12",//对应图片日期字符串
      "img": "mirror/error/789.jpg",//图片路径
      "type": 1,//1为错误坐姿 2位愉悦瞬间
      "uuid": "20e76216-dc39-4d9c-afe4-22fe071a2321" //唯一标志
    }
    */
    // 坐姿仪上传报告信息接口
    public static void submitHappyPicture(String syncJson, String udid, String t_id, String u_id) {
        if (!NetworkUtils.isConnected()) {
            return;
        }
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Boolean>() {
            @Override
            public Boolean doInBackground() throws Throwable {

                return submitSittingHappyPicture(syncJson, udid, t_id, u_id);
            }

            @Override
            public void onSuccess(Boolean result) {

            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
            }
        });
    }

    private static boolean submitSittingHappyPicture(String syncJson, String udid, String t_id, String u_id) {
        if (!NetworkUtils.isConnected()) {
            return false;
        }
        try {
            RequestBody requestBody = RequestBody.create(JSON, syncJson);
            Request request = new Request
                    .Builder()
                    .header("udid", udid)
                    .header("App-Key", CZURConstants.CLOUD_ANDROID)
                    .header("T-ID", t_id)
                    .header("U-ID", u_id)
                    .header("Content-Type", "application/json")
                    .url(BuildConfig.BASE_URL + CZURConstants.SYNC_MIRROR_PIC_URL)
                    .post(requestBody)
                    .build();

            Response response = MiaoHttpManager.getInstance().getHttpClient().newCall(request).execute();

            String responseString = response.body().string();
            logI("submitSittingHappyPicture.syncJson="+syncJson);
            logI("submitSittingHappyPicture.responseString="+responseString);

            if (responseString.trim().startsWith("<html>") || responseString.trim().endsWith("</html>") ){
                return false;
            }
            BaseModel baseModel = new Gson().fromJson(responseString, BaseModel.class);
            // 请求成功
            if (response.isSuccessful()) {
                int code = baseModel.getCode();
                if (code == MiaoHttpManager.STATUS_SUCCESS) {
                    logI("submitSittingHappyPicture.上传数据至服务器 成功!");
                    return true;
                } else {
                    logI("submitSittingHappyPicture.上传数据至服务器 失败!");
                    return false;
                }
            } else {
                return false;
            }
        } catch (Exception e) {
            logE("submitSittingHappyPicture.e="+e.toString());
            return false;
        }
    }

    // 上传文件 // 图片上传oss
    public static boolean uploadSittingStandarImage(String sourcePath, String ossPath) {
        if (!NetworkUtils.isConnected()) {
            return false;
        }
        boolean isUploadSuccess = false;
        OSS ossClient = OSSInstanceSitting.Companion.getInstance().oss();

        if (ossClient == null) {
            return false;
        }
        // 构造上传请求
//        PutObjectRequest put = new PutObjectRequest(CZURConstants.MIRROR_BUCKET, ossPath, sourcePath);
        PutObjectRequest put = new PutObjectRequest(BuildConfig.MIRROR_BUCKET, ossPath, sourcePath);

        try {
            PutObjectResult putResult = ossClient.putObject(put);
            isUploadSuccess = true;
        } catch (ClientException e) {
            // 本地异常如网络异常等
            e.printStackTrace();
        } catch (ServiceException e) {
            // 服务异常
            logE("uploadSittingStandarImage.RequestId:" + e.getRequestId());
            logE("uploadSittingStandarImage.ErrorCode:" + e.getErrorCode());
            logE("uploadSittingStandarImage.HostId:" + e.getHostId());
            logE("uploadSittingStandarImage.RawMessage:" + e.getRawMessage());
        }
        return isUploadSuccess;
    }

//    // 重新联网，需要同步绑定缓存数据
//    public static void syncOfflineDatasToServer() {
//        MirrorOfflinePreferences offlinePreferences = MirrorOfflinePreferences.getInstance();
//        SittingDeviceOfflineModel
//        // 1， 同步bind、unbind数据
//        // 2， 同步报告数据
//        // 3， 同步坐姿图片
//        // 4，同步开心图、错误坐姿图片数据
//
//        for (SittingDeviceOfflineModel bindModel:bindModels){
//
//        }
//    }

    public static void mirrorBindDevice(String uid, String deviceSN, String mac){
        if (!NetworkUtils.isConnected()) {
            return;
        }

        HttpManager.getInstance().request().bindSittingDevice(
            uid,  deviceSN,  mac, String.class,
            new MiaoHttpManager.CallbackNetwork<String>() {
                @Override
                public void onNoNetwork() { }

                @Override
                public void onStart() {  }

                @Override
                public void onResponse(MiaoHttpEntity<String> entity) {
//                    logI("FastBleHttpUtils.mirrorBindDevice.onResponse="+entity.getBody());
                }

                @Override
                public void onFailure(MiaoHttpEntity<String> entity) {
                }

                @Override
                public void onError(Exception e) {
                    logE("FastBleHttpUtils.mirrorBindDevice.onError="+e.toString());
                }
            });
    }

    public static void mirrorUnbindDevice(String userId, String deviceId, String mac) {
        if (!NetworkUtils.isConnected()) {
            return;
        }
        HttpManager.getInstance().request().unbindSitting(
                userId, deviceId, mac, String.class,
                new MiaoHttpManager.Callback<String>() {
                    @Override
                    public void onStart() {  }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onError(Exception e) {
                        logE("FastBleHttpUtils.mirrorUnbindDevice.onError="+e.toString());
                    }
                });
    }

    // 网络上传标准坐姿图片
    public static void uploadSittingOssInfo(String u_id, String clientId, String equipmentId, String pic_name) {
        if (!NetworkUtils.isConnected()) {
            return;
        }
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<SittingOssTokenModel>() {
            @Override
            public SittingOssTokenModel doInBackground() {
                return FastBleHttpUtils.getSittingOssInfo(u_id, clientId);
            }

            @Override
            public void onSuccess(SittingOssTokenModel ossModel) {

                if (ossModel == null){
                    return;
                }
                //  test/3515/544c7f8b-4b73-43df/*
                String prefix = ossModel.getPrefix();
                String ossPath = prefix.substring(0,prefix.length()-2) + FastBleConstants.SITTING_PICTURE_NAME_STANDAR;

                // 图片上传oss
                uploadSittingImage(pic_name, ossPath, u_id, equipmentId);
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                logE("FastBleHttpUtils.uploadSittingOssInfo.onFail.t="+t.toString());
            }
        });
    }

    // 图片上传oss
    private static void uploadSittingImage(String sourcePath, String ossPath, String u_id, String equipmentId) {
        if (!NetworkUtils.isConnected()) {
            return;
        }
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Boolean>() {
            @Override
            public Boolean doInBackground() {
                return FastBleHttpUtils.uploadSittingStandarImage(sourcePath, ossPath);
            }

            @Override
            public void onSuccess(Boolean flag) {
                logI("FastBleHttpUtils.uploadSittingImage.onSuccess.flag="+flag);
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                logE("FastBleHttpUtils.uploadSittingImage.onFail.t="+t.toString());
            }
        });
    }

    public static void uploadSittingHappyOssInfo(String u_id, String clientId,
                                                 String equipmentId, String pic_name, Map<String, String> dict) {
        if (!NetworkUtils.isConnected()) {
            return;
        }
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<SittingOssTokenModel>() {
            @Override
            public SittingOssTokenModel doInBackground() {
                return FastBleHttpUtils.getSittingOssInfo(u_id, clientId);
            }

            @Override
            public void onSuccess(SittingOssTokenModel ossModel) {
                if (ossModel == null){
                    return;
                }

                String prefix = ossModel.getPrefix();
                String name = dict.get(FastBleConstants.DICT_UUID)+"";
                if (FastBleToolUtils.isMessyCode(name)){
                    name = FastBleToolUtils.getCurrentSysTimeLong();
                    // 需要重新赋值dataString
                    String date_filename = FastBleToolUtils.getDateTimeFormate(name);
                    String localeTime = FastBleToolUtils.getDateTimeFullFormate(name);
                    dict.put(FastBleConstants.DICT_DATE_STRING, date_filename);
                    dict.put(FastBleConstants.DICT_LOCALE_TIME, localeTime);
                }
                String ossPath = prefix.substring(0,prefix.length()-1) + name;
                List<Map<String, String>> listEntry = new ArrayList<>();
                dict.put(FastBleConstants.DICT_IMG, ossPath);
                listEntry.add(dict);

                Map<String, Object> mParams = new HashMap<>();
                mParams.put(FastBleConstants.OSS_PARAMS_UID, u_id);
                mParams.put(FastBleConstants.OSS_PARAMS_UDID, equipmentId);
                mParams.put(FastBleConstants.OSS_PARAMS_IMG_DATA, listEntry);
                String imgDatas = new Gson().toJson(mParams);
                // 图片上传oss
                String type = dict.get(FastBleConstants.DICT_TYPE);
                uploadSittingHappyImage(u_id, pic_name, ossPath, imgDatas, equipmentId, type);
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                logE("getSittingHappyOssInfo.onFail.t="+t.toString());
            }
        });
    }

    // 图片上传oss
    private static void uploadSittingHappyImage(String u_id, String sourcePath,
                                                String ossPath, String imgDatas,
                                                String equipmentId, String type) {
        if (!NetworkUtils.isConnected()) {
            return;
        }
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Boolean>() {
            @Override
            public Boolean doInBackground() {
                return FastBleHttpUtils.uploadSittingStandarImage(sourcePath, ossPath);
            }

            @Override
            public void onSuccess(Boolean flag) {
                if (flag){
                    // 保存坐姿照片路径到服务器
                    // 发送到服务器
                    String udid = UserPreferences.getInstance().getIMEI();
                    String t_id = UserPreferences.getInstance().getToken();
                    FastBleHttpUtils.submitHappyPicture(imgDatas, udid, t_id, u_id);

                    // 需要同步删除图片；
                    FastBleToolUtils.deleteOnlyFile(sourcePath);
                    logI("FastBleHttpUtils.uploadSittingHappyImage.deleteFile="+sourcePath);

                    // 上传成功，删除该条记录 1:错误坐姿；2：开心图片
                    if (type.equals("2")){
                        removeOneRecordFromOfflineHappy(u_id, equipmentId, sourcePath);
                    }else{
                        removeOneRecordFromOfflineError(u_id, equipmentId, sourcePath);
                    }
                }
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                logE("uploadSittingImage.onFail.t="+t.toString());
            }
        });
    }

    // 从离线记录中删除已经上传完成的记录
    public static void removeOneRecordFromOfflineHappy(String u_id, String equipmentId, String pic_name){
        MirrorOfflinePreferences offlinePreferences = MirrorOfflinePreferences.getInstance();
        List<SittingDeviceOfflineModel> picModels = offlinePreferences.getSittingHappyPictures();
        if (picModels == null){
            picModels = new ArrayList<>();
        }

        List<SittingDeviceOfflineModel> tmpModels = picModels;
        for (SittingDeviceOfflineModel model : tmpModels){
            String old_u_id = model.getU_id();
            String old_pic_name = model.getPic_name();
            String old_uuid = model.getEquipmentUuid();
            if ((u_id.equals(old_u_id)) &&
                    (pic_name.equals(old_pic_name)) &&
                    (equipmentId.equals(old_uuid))){
                picModels.remove(model);
                break;
            }
        }
        offlinePreferences.setSittingHappyPictures(picModels);
    }

    public static void removeOneRecordFromOfflineError(String u_id, String equipmentId, String pic_name){
        MirrorOfflinePreferences offlinePreferences = MirrorOfflinePreferences.getInstance();
        List<SittingDeviceOfflineModel> picModels = offlinePreferences.getSittingErrorPictures();
        if (picModels == null){
            picModels = new ArrayList<>();
        }

        List<SittingDeviceOfflineModel> tmpModels = picModels;
        for (SittingDeviceOfflineModel model : tmpModels){
            String old_u_id = model.getU_id();
            String old_pic_name = model.getPic_name();
            String old_uuid = model.getEquipmentUuid();
            if ((u_id.equals(old_u_id)) &&
                    (pic_name.equals(old_pic_name)) &&
                    (equipmentId.equals(old_uuid))){
                picModels.remove(model);
                break;
            }
        }
        offlinePreferences.setSittingErrorPictures(picModels);
    }


    // 同步更新本地所有的设置项
    public static void updateAllSettingLocal(SittingDeviceModel currentDeviceModel) {
        if (!NetworkUtils.isConnected()) {
            return;
        }
        if (currentDeviceModel == null){
            return;
        }

        String u_id=currentDeviceModel.getBindUserId()+"";   //true		用户名ID
        String equipmentUID=currentDeviceModel.getEquipmentUID()+"";    //true	String	设备流水号
        String level=currentDeviceModel.getLevel()+"";   //integer 坐姿识别灵敏度 0 - 慢 1 - 适中 2 - 快
        String status="true";  //boolean	是否开启坐姿提醒
        String sound=currentDeviceModel.getSound()+"";   //integer	音量大小
        String sedentaryTime=currentDeviceModel.getSedentaryTime()+"";   //integer	久坐时长
        String light=currentDeviceModel.getLight()+"";   //提示灯亮度 0-5
        String inputStatus=currentDeviceModel.getInputStatus()+""; //录入状态
        String standPositionImage=currentDeviceModel.getStandPositionImage()+"";  //标准坐姿图片
        String postureMode=currentDeviceModel.getPostureMode()== 0 ? "1" : "2"; //1为智能坐姿 2位自定义坐姿
//        String postureSwitch=currentDeviceModel.getPostureSwitch()+"";   //坐姿开关 0为关 1为开
        String postureSwitch=currentDeviceModel.getiErrorTimeSwitch()+"";
        String lightSwitch=currentDeviceModel.getLightSwitch()+""; //灯光开关 1为开 0为关
        String happySwitch=currentDeviceModel.getiHappyTimeSwitch()+""; //happySwitch	false	integer	开心坐姿开关 1为开 0为关
        if (equipmentUID.equals("") || u_id.equals("0")){
            return;
        }

        HttpManager.getInstance().request().setSettingAll(
                u_id,
                equipmentUID,
                level,
                status,
                sound,
                sedentaryTime,
                light,
                inputStatus,
                standPositionImage,
                postureMode,
                postureSwitch,
                lightSwitch,
                happySwitch,
                String.class,
                new MiaoHttpManager.Callback<String>() {

                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onError(Exception e) {
                        logE("FastBleHttpUtils.updateAllSettingLocal.onError="+e.toString());
                    }
                });
    }

    // 坐姿仪上传报告信息接口,local json file
    public static void sendLocalReport(String syncJson, String udid, String t_id, String u_id, String filename) {
        if (!NetworkUtils.isConnected()) {
            return;
        }

        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Boolean>() {
            @Override
            public Boolean doInBackground() throws Throwable {
                return addTimeUseReportNoFresh(syncJson, udid, t_id, u_id);
            }

            @Override
            public void onSuccess(Boolean result) {
                // 删除本地json文件
                if (result){
                    FastBleToolUtils.deleteOnlyFile(filename);
                }
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
            }
        });
    }

    // 坐姿仪上传报告信息接口
    public static void sendLocalReportNew(String syncJson, String udid, String t_id, String u_id) {
        if (!NetworkUtils.isConnected()) {
            return;
        }

        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Boolean>() {
            @Override
            public Boolean doInBackground() throws Throwable {
                return addTimeUseReport(syncJson, udid, t_id, u_id);
            }

            @Override
            public void onSuccess(Boolean result) {
                // 删除本地json文件
                if (result){
//                    FastBleToolUtils.deleteOnlyFile(filename);
                }
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
            }
        });
    }

    // 坐姿仪上传algo信息接口,local json file
    public static void sendLocalAlog(String syncJson, String equipmentId, String udid, String t_id, String u_id, String filename) {
        if (!NetworkUtils.isConnected()) {
            return;
        }

        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Boolean>() {
            @Override
            public Boolean doInBackground() throws Throwable {
                return uploadAlgoJsonData(syncJson, equipmentId, udid, t_id, u_id);
            }

            @Override
            public void onSuccess(Boolean result) {
                // 删除本地json文件
                if (result){
                    FastBleToolUtils.deleteOnlyFile(filename);
                }
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
            }
        });
    }
    public static void sendLocalAlogNew(String syncJson, String equipmentId, String udid, String t_id, String u_id) {
        if (!NetworkUtils.isConnected()) {
            return;
        }

        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Boolean>() {
            @Override
            public Boolean doInBackground() throws Throwable {
                return uploadAlgoJsonData(syncJson, equipmentId, udid, t_id, u_id);
            }

            @Override
            public void onSuccess(Boolean result) {
                // 删除本地json文件
                if (result){
//                    FastBleToolUtils.deleteOnlyFile(filename);
                }
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
            }
        });
    }

    // 坐姿仪上传algo接口
    public static void uploadAlgoJson(String syncJson, String equipmentId, String udid, String t_id, String u_id) {
        if (!NetworkUtils.isConnected()) {
            return;
        }

        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Boolean>() {
            @Override
            public Boolean doInBackground() throws Throwable {
                return uploadAlgoJsonData(syncJson, equipmentId, udid, t_id, u_id);
            }

            @Override
            public void onSuccess(Boolean result) { }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
            }
        });
    }

    private static boolean uploadAlgoJsonData(String syncJson, String equipmentId, String udid, String t_id, String u_id) {
        if (!NetworkUtils.isConnected()) {
            return false;
        }

        // 请求地址:http://localhost:8081/api/v3/mirror/addSpContent
        // 请求头: udid,App-Key,U-ID,T-ID=xxxx

        // 组合发送的body
        // u_id	true	long	user id
        // sn	true	String	设备SN
        // content	true	String	算法参数字符串

        try {
//            RequestBody requestBody = RequestBody.create(JSON, syncJson);
            RequestBody requestBody = new FormBody.Builder()
                    .add("u_id",u_id)
                    .add("sn",equipmentId)
                    .add("content",syncJson)
                    .build();
            //请求超时设置
            MiaoHttpManager.getInstance().getHttpClient().newBuilder()
                    .connectTimeout(60000, TimeUnit.SECONDS)
                    .readTimeout(60000, TimeUnit.SECONDS);

            Request request = new Request
                    .Builder()
                    .header("udid", udid)
                    .header("App-Key", CZURConstants.CLOUD_ANDROID)
                    .header("T-ID", t_id)
                    .header("U-ID", u_id)
                    .header("Content-Type", "application/json")
                    .url(BuildConfig.BASE_URL + CZURConstants.SYNC_MIRROR_ALGO_URL)
                    .post(requestBody)
                    .build();

            Response response = MiaoHttpManager.getInstance().getHttpClient().newCall(request).execute();

            String responseString = response.body().string();

            if (responseString.trim().startsWith("<html>") || responseString.trim().endsWith("</html>") ){
                return false;
            }
            BaseModel baseModel = new Gson().fromJson(responseString, BaseModel.class);
            // 请求成功
            if (response.isSuccessful()) {
                int code = baseModel.getCode();
                if (code == MiaoHttpManager.STATUS_SUCCESS) {
                    logI("uploadAlgoJsonData.上传数据至服务器 成功!");
                    EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_ALGO_SUCCESS, ""));
                    return true;
                } else {
                    logI("uploadAlgoJsonData.上传数据至服务器 失败!");
                    return false;
                }
            } else {
                return false;
            }
        } catch (Exception e) {
            logE("uploadAlgoJsonData.e="+e.toString());
            return false;
        }
    }

}
