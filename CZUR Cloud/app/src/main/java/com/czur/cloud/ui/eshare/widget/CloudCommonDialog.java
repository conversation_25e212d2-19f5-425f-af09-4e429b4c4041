package com.czur.cloud.ui.eshare.widget;

import android.app.Dialog;
import android.content.Context;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.blankj.utilcode.util.StringUtils;
import com.czur.cloud.R;


/**
 * Created by Yz on 2018/3/20
 * Email：<EMAIL>
 */

public class CloudCommonDialog extends Dialog {
    public static final float DIMMED_OPACITY = 0.2f;

    private boolean isChecked;

    public CloudCommonDialog(Context context) {
        super(context);
    }

    public CloudCommonDialog(Context context, int theme) {
        super(context, theme);

    }

    public boolean isChecked() {
        return this.isChecked;
    }

    public static class Builder {
        private Context context;
        private CloudCommonPopupConstants constants;

        private String message;
        private String title;

        private boolean isShowNoMoreTips;
        private boolean noMoreTipsDefaultChecked = false;
        private View contentsView;

        private OnClickListener positiveListener;
        private OnClickListener onNegativeListener;
        private OnDismissListener onDismissListener;
        private NoMoreTipsListener noMoreTipsListener;

        public Builder(Context context, CloudCommonPopupConstants constants) {
            this.context = context;
            this.constants = constants;
        }

        public Builder setMessage(String message) {
            this.message = message;
            return this;
        }

        public Builder setTitle(int resource) {
            this.title = context.getResources().getString(resource);
            return this;
        }
        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setNoMoreTips(boolean isShow, boolean defaultChecked,NoMoreTipsListener noMoreTipsListener) {
            this.isShowNoMoreTips = isShow;
            this.noMoreTipsDefaultChecked = defaultChecked;
            this.noMoreTipsListener = noMoreTipsListener;
            return this;
        }

        public Builder setContentsView(View contentsView) {
            this.contentsView = contentsView;
            return this;
        }

        public Builder setContentsView(int resource) {
            this.contentsView = LayoutInflater.from(context).inflate(resource, null);
            return this;
        }

        public Builder setOnPositiveListener(OnClickListener positiveListener) {
            this.positiveListener = positiveListener;
            return this;
        }

        public Builder setOnNegativeListener(OnClickListener onNegativeListener) {
            this.onNegativeListener = onNegativeListener;
            return this;
        }

        public Builder setOnDismissListener(OnDismissListener onDismissListener) {
            this.onDismissListener = onDismissListener;
            return this;
        }


        public CloudCommonDialog create() {
            LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);

            final CloudCommonDialog dialog = new CloudCommonDialog(context, R.style.TransparentProgressDialog);
            View layout = commonCustomPopLayout(inflater, dialog);

            dialog.setContentView(layout);
            dialog.setCanceledOnTouchOutside(false);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            params.dimAmount = DIMMED_OPACITY;

            return dialog;
        }

        private View commonCustomPopLayout(LayoutInflater inflater, final CloudCommonDialog dialog) {
            WindowManager.LayoutParams lp = dialog.getWindow().getAttributes();
            lp.dimAmount = 0.8f;
            dialog.getWindow().setAttributes(lp);
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

            View layout = inflater.inflate(R.layout.eshare_common_custom_popup, null, false);
            ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);
            dialog.addContentView(layout, params);

            TextView title = (TextView) layout.findViewById(R.id.title);
            TextView message = (TextView) layout.findViewById(R.id.message);
            TextView positiveBtn = (TextView) layout.findViewById(R.id.positive_button);
            TextView negativeBtn = (TextView) layout.findViewById(R.id.negative_button);
            EditText editText = (EditText) layout.findViewById(R.id.edt);
            ConstraintLayout noRemindCl = (ConstraintLayout) layout.findViewById(R.id.no_remind_cl);
            ESCheckBox noRemindCb = (ESCheckBox) layout.findViewById(R.id.no_remind_cb);
            if (contentsView == null) {
                if (!StringUtils.isEmpty(this.message)) {
                    message.setText(this.message + "");
                } else if (constants.getMessage() > 0) {
                    message.setText(context.getResources().getString(constants.getMessage()));

                }

                if (!StringUtils.isEmpty(this.title)) {
                    title.setText(this.title + "");
                } else if (constants.getTitle() > 0) {
                    title.setText(context.getResources().getString(constants.getTitle()));
                }

                if (isShowNoMoreTips){
                    noRemindCl.setVisibility(View.VISIBLE);
                    noRemindCb.setChecked(noMoreTipsDefaultChecked);
                    noRemindCb.setOnCheckedChangeListener(new ESCheckBox.OnCheckedChangeListener() {
                        @Override
                        public void onCheckedChanged(ESCheckBox checkBox, boolean isChecked) {
                            if (noMoreTipsListener != null){
                                noMoreTipsListener.onNoMoreTips(isChecked);
                            }
                        }
                    });
                }else {
                    noRemindCl.setVisibility(View.GONE);
                }
            } else {
                message.setVisibility(View.GONE);
                title.setVisibility(View.GONE);
            }

            if (constants.getPositiveBtn() > 0) {
                positiveBtn.setText(context.getResources().getString(constants.getPositiveBtn()));
            } else {
                positiveBtn.setVisibility(View.GONE);
            }

            if (positiveListener != null) {
                positiveBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        positiveListener.onClick(dialog, constants.getPositiveBtn());
                    }
                });
            } else {
                positiveBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialog.dismiss();
                    }
                });
            }

            if (constants.getNegativeBtn() > 0) {
                negativeBtn.setText(context.getResources().getString(constants.getNegativeBtn()));
            } else {
                negativeBtn.setVisibility(View.GONE);
            }

            if (constants.getEditText() > 0) {
                editText.setVisibility(View.VISIBLE);
            } else {
                editText.setVisibility(View.GONE);
            }


            if (onNegativeListener != null) {
                negativeBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onNegativeListener.onClick(dialog, constants.getNegativeBtn());
                    }
                });
            } else {
                negativeBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialog.dismiss();
                    }
                });
            }

            if (onDismissListener != null) {
                dialog.setOnDismissListener(onDismissListener);
            }

            return layout;
        }
    }

    interface NoMoreTipsListener {
        void onNoMoreTips(boolean isChecked);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            // 屏蔽返回键
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }
}
