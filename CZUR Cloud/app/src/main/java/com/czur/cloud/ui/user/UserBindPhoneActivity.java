package com.czur.cloud.ui.user;

import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.KeyboardUtils;
import com.blankj.utilcode.util.RegexUtils;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.UserInfoEvent;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.StarryPreferences;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.ProgressButton;
import com.czur.cloud.ui.home.IndexActivity;
import com.czur.cloud.ui.starry.activity.StarryActivity;
import com.czur.cloud.util.AppClearUtils;
import com.czur.cloud.util.validator.Validator;

import org.greenrobot.eventbus.EventBus;

/**
 * Created by Yz on 2018/2/28.
 * Email：<EMAIL>
 */

public class UserBindPhoneActivity extends BaseActivity implements View.OnClickListener {

    private ImageView userBackBtn;
    private TextView userTitle;
    private EditText userBindPhoneMobileEdt;
    private EditText userBindPhoneCodeEdt;
    private TextView userBindPhoneSendCodeTv;
    private ProgressButton userBindPhoneBtn;
    private boolean codeHasContent = false;
    private TimeCount timeCount;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private boolean isChangePhone;
    private boolean isStarry;
    private long currentTime;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this,true);
        setContentView(R.layout.activity_user_bind_phone);
        initComponent();
        registerEvent();
    }

    private void initComponent() {
        isChangePhone = getIntent().getBooleanExtra("changePhone", false);
        isStarry = getIntent().getBooleanExtra("STARRY", false);
        userBackBtn = (ImageView) findViewById(R.id.user_back_btn);
        userTitle = (TextView) findViewById(R.id.user_title);
        userBindPhoneMobileEdt = (EditText) findViewById(R.id.user_bind_phone_edt);
        userBindPhoneCodeEdt = (EditText) findViewById(R.id.user_bind_phone_code_edt);
        userBindPhoneSendCodeTv = (TextView) findViewById(R.id.user_bind_phone_send_code_tv);
        userBindPhoneBtn = (ProgressButton) findViewById(R.id.user_bind_phone_btn);
        httpManager = HttpManager.getInstance();
        userPreferences = UserPreferences.getInstance(this);

//        userTitle.setText(R.string.user_change_phone);
        userTitle.setText(R.string.user_first_bind_phone);

//        if (isStarry){
//            userTitle.setText(R.string.user_first_bind_phone);
//        }
//        if (isChangePhone){
//            userTitle.setText(R.string.user_change_phone);
//        }else {
//            userTitle.setText(R.string.bind_phone);
//        }
    }

    private void registerEvent() {
        userBackBtn.setOnClickListener(this);
        userBindPhoneCodeEdt.addTextChangedListener(codeTextWatcher);
        userBindPhoneMobileEdt.addTextChangedListener(codeTextWatcher);
        userBindPhoneSendCodeTv.setOnClickListener(this);
        userBindPhoneBtn.setOnClickListener(this);
        userBindPhoneBtn.setSelected(false);
        userBindPhoneBtn.setClickable(false);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.user_back_btn:
                ActivityUtils.finishActivity(this);

                break;
            case R.id.user_bind_phone_send_code_tv:
                checkMobile();
                break;
            case R.id.user_bind_phone_btn:
                checkIsHasMobileToBind();
                break;
            default:
                break;
        }
    }

    /**
     * @des:校验是否有手机号
     * @params:
     * @return:
     */
    private void checkIsHasMobileToBind() {
        if (isChangePhone) {
            hasMobileToBind();
        } else {
            noMobileToBind();
        }
    }

    /**
     * @des:没有手机号绑定
     * @params:
     * @return:
     */
    private void noMobileToBind() {

        final String mobile = userBindPhoneMobileEdt.getText().toString();
        final String mobileCode = userBindPhoneCodeEdt.getText().toString();
        if (mobile.length() == 0) {
            showMessage(R.string.login_alert_phone_empty);
        } else if (!RegexUtils.isMobileExact(mobile)) {
            showMessage(R.string.toast_mobile_format_wrong);
        } else if (mobileCode.length() <= 5) {
            showMessage(R.string.edit_text_code_length);
        } else if (mobile.equals(userPreferences.getUserMobile())) {
            showMessage(R.string.mobile_toast_put_new_mobile_number);
        } else {
            currentTime = System.currentTimeMillis();
            KeyboardUtils.hideSoftInput(this);
            httpManager.requestPassport().notBindUpdateMobile(userPreferences.getIMEI(), CZURConstants.CLOUD_ANDROID,
                    userPreferences.getChannel(), userPreferences.getUserId(), userPreferences.getToken(), userPreferences.getUserId(),
                    mobile, mobileCode, String.class, new MiaoHttpManager.Callback<String>() {
                        @Override
                        public void onStart() {
                            userBindPhoneBtn.startDelayLoading(UserBindPhoneActivity.this);
                        }

                        @Override
                        public void onResponse(MiaoHttpEntity<String> entity) {
                            successDelay(entity,mobile,false);

                        }

                        @Override
                        public void onFailure(MiaoHttpEntity<String> entity) {

                            if (entity.getCode() == MiaoHttpManager.STATUS_MOBILE_BIND_OTHER_USER) {
                                failedDelay(R.string.mobile_toast_bind_other_user);

                            } else if (entity.getCode() == MiaoHttpManager.STATUS_CODE_EXPIRED) {
                                failedDelay(R.string.mail_code_expired);
                            } else if (entity.getCode() == MiaoHttpManager.STATUS_INVALID_MOBILE) {
                                failedDelay(R.string.invalid_mobile);
                            }

                        }

                        @Override
                        public void onError(Exception e) {
                            failedDelay(R.string.request_failed_alert);
                        }
                    });
        }
    }

    public void noMobileSuccess(String mobile) {
        userPreferences.setUserMobile(mobile);
        showMessage(R.string.user_mobile_bind_success);
        setMobileForStarry(mobile);
        EventBus.getDefault().post(new UserInfoEvent(EventType.BIND_PHONE));

        // 同步一下starry的用户信息
        AppClearUtils.getStarryUserInfo();

        try {
            Thread.sleep(300);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        if (isStarry){
            ActivityUtils.startActivity(StarryActivity.class);
        }else {
            ActivityUtils.startActivity(IndexActivity.class);
        }
    }

    private void setMobileForStarry(String mobile){
        // Starry,需要保存一下手机号
        if (isStarry){
            StarryPreferences.getInstance().setAccountNo(mobile);
//"body": {
//    │         "id": 4073,
//    │         "name": "<EMAIL>",
//    │         "mobile": null,
//    │         "email": "<EMAIL>",
//    │         "photo": null,
//    │         "photoOssKey": null,
//    │         "isActive": true,
//    │         "token": "B69943B099EE6838058D58ACB0697CD5",
//    │         "service": null,
//    │         "refreshToken": null
//    │     }
// (id=2781, accountNo=***********, czurId=4072,
// headImage=, kind=2, mobile=***********,
// name=<EMAIL>, type=0,
// createTime=2021-12-17 08:53:04,
// updateTime=2021-12-17 08:53:04, userType=COMPANY)

        }
    }

    /**
     * @des:有手机号绑定
     * @params:
     * @return:
     */
    private void hasMobileToBind() {

        final String mobile = userBindPhoneMobileEdt.getText().toString();
        final String mobileCode = userBindPhoneCodeEdt.getText().toString();
        if (mobile.length() == 0) {
            showMessage(R.string.login_alert_phone_empty);
        } else if (!RegexUtils.isMobileExact(mobile)) {
            showMessage(R.string.toast_mobile_format_wrong);
        } else if (mobileCode.length() <= 5) {
            showMessage(R.string.edit_text_code_length);

        } else if (mobile.equals(userPreferences.getUserMobile())) {
            showMessage(R.string.mobile_toast_put_new_mobile_number);

        } else {
            currentTime = System.currentTimeMillis();
            KeyboardUtils.hideSoftInput(this);
            HttpManager.getInstance().requestPassport().updateMobileSecond(userPreferences.getIMEI(), CZURConstants.CLOUD_ANDROID,
                    userPreferences.getChannel(), userPreferences.getUserId(), userPreferences.getToken(), userPreferences.getUserId(),
                    mobile, userPreferences.getUkey(), mobileCode, String.class, new MiaoHttpManager.Callback<String>() {
                        @Override
                        public void onStart() {
                            userBindPhoneBtn.startDelayLoading(UserBindPhoneActivity.this);
                        }

                        @Override
                        public void onResponse(MiaoHttpEntity<String> entity) {

                            successDelay(entity,mobile,true);
                        }

                        @Override
                        public void onFailure(MiaoHttpEntity<String> entity) {

                            if (entity.getCode() == MiaoHttpManager.STATUS_MOBILE_BIND_OTHER_USER) {
                                failedDelay(R.string.mobile_toast_bind_other_user);
                            } else if (entity.getCode() == MiaoHttpManager.STATUS_INVALID_MOBILE) {
                                failedDelay(R.string.invalid_mobile);
                            } else if (entity.getCode() == MiaoHttpManager.STATUS_CODE_EXPIRED) {
                                failedDelay(R.string.mail_code_expired);
                            }else {
                                failedDelay(R.string.request_failed_alert);
                            }
                        }

                        @Override
                        public void onError(Exception e) {
                            failedDelay(R.string.request_failed_alert);
                        }
                    });
        }
    }

    public void hasMobileSuccess(String mobile) {
        userPreferences.setUserMobile(mobile);
        setMobileForStarry(mobile);
        showMessage(R.string.mobile_toast_mobile_update_success);
        EventBus.getDefault().post(new UserInfoEvent(EventType.CHANGE_PHONE));
        ActivityUtils.startActivity(IndexActivity.class);
    }

    private void failedDelay(final int failedText) {

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    long sleepTime;
                    if (System.currentTimeMillis() - currentTime < 1000) {
                        sleepTime = 1000 - (System.currentTimeMillis() - currentTime);
                    } else {
                        sleepTime = 1;
                    }
                    Thread.sleep(sleepTime);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            showMessage(failedText);
                            userBindPhoneBtn.stopLoading();
                            userBindPhoneCodeEdt.setText(CZURConstants.EMPTY);
                        }
                    });
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }


    private void successDelay(final MiaoHttpEntity<String> entity, final String mobile, final boolean hasMoblie) {

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    long sleepTime;
                    if (System.currentTimeMillis() - currentTime < 1000) {
                        sleepTime = 1000 - (System.currentTimeMillis() - currentTime);
                    } else {
                        sleepTime = 1;
                    }
                    Thread.sleep(sleepTime);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (hasMoblie) {
                                hasMobileSuccess(mobile);
                            } else {
                                noMobileSuccess(mobile);
                            }

                        }
                    });
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }
    /**
     * @des:校验手机
     * @params:
     * @return:
     */

    private void checkMobile() {
        if (Validator.isNotEmpty(userBindPhoneMobileEdt.getText().toString())){
            if (RegexUtils.isMobileExact(userBindPhoneMobileEdt.getText().toString())) {
                getMobileCode(userBindPhoneMobileEdt.getText().toString());

            } else {
                showMessage(R.string.toast_mobile_format_wrong);
            }
        }else {
            showMessage(R.string.login_alert_phone_empty);
        }

    }

    /**
     * @des: 获取手机验证码
     * @params:[accountStr]
     * @return:void
     */
    private void getMobileCode(String mobile) {

        httpManager.requestPassport().mobileCode(mobile, String.class, new MiaoHttpManager.Callback<String>() {
            @Override
            public void onStart() {

            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                timeCountBegin();
                showMessage(R.string.toast_code_send);

            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {

                if (entity.getCode() == MiaoHttpManager.STATUS_CODE_1_MIN) {
                    showMessage(R.string.toast_code_1_min);
                } else if (entity.getCode() == MiaoHttpManager.STATUS_5_MIN_4_TIME) {
                    showMessage(R.string.toast_5_min_4_time);
                } else if (entity.getCode() == MiaoHttpManager.STATUS_5_TIME_IN_ONE_DAY) {
                    showMessage(R.string.toast_5_time_in_one_day);
                } else {
                    showMessage(R.string.request_failed_alert);
                }

            }

            @Override
            public void onError(Exception e) {

                showMessage((R.string.request_failed_alert));
            }
        });
    }


    private TextWatcher codeTextWatcher = new TextWatcher() {


        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            // 输入的内容变化的监听
            if (s.length() > 0) {
                codeHasContent = true;
            } else {
                codeHasContent = false;
            }

            checkChangePhoneButtonToClick();
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count,
                                      int after) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            // 输入后的监听
            if (s.length() > 0) {
                codeHasContent = true;
            } else {
                codeHasContent = false;
            }
            checkChangePhoneButtonToClick();
        }
    };

    /**
     * @des: 检查注册按钮是否可以点击
     * @params:
     * @return:
     */

    private void checkChangePhoneButtonToClick() {

        boolean mobileIsNotEmpty = Validator.isNotEmpty(userBindPhoneMobileEdt.getText().toString());
        boolean codeIsNotEmpty = Validator.isNotEmpty(userBindPhoneCodeEdt.getText().toString());

        if (mobileIsNotEmpty && codeIsNotEmpty && codeHasContent) {
            userBindPhoneBtn.setSelected(true);
            userBindPhoneBtn.setClickable(true);
        } else {
            userBindPhoneBtn.setSelected(false);
            userBindPhoneBtn.setClickable(false);
        }
    }


    /**
     * @des: 计时器开始倒计时60S
     * @params:
     * @return:void
     */
    private void timeCountBegin() {
        timeCount = new TimeCount(60000, 1000);
        timeCount.start();

    }

    /**
     * @des: 计时器
     * @params:
     * @return:
     */

    class TimeCount extends CountDownTimer {

        public TimeCount(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @Override
        public void onFinish() {
            userBindPhoneSendCodeTv.setText(R.string.resend_code);
            userBindPhoneSendCodeTv.setClickable(true);


        }

        @Override
        public void onTick(long millisUntilFinished) {
            userBindPhoneSendCodeTv.setClickable(false);
            userBindPhoneSendCodeTv.setText(millisUntilFinished / 1000 + " s");


        }
    }
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (Validator.isNotEmpty(timeCount)) {
            timeCount.cancel();
        }
    }
}
