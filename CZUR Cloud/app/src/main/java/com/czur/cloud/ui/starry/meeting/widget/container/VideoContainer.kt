package com.czur.cloud.ui.starry.meeting.widget.container

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Configuration
import android.graphics.Bitmap
import android.util.AttributeSet
import android.view.*
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.core.view.children
import com.blankj.utilcode.util.TimeUtils
import com.czur.cloud.R
import com.czur.cloud.event.EventType
import com.czur.cloud.event.StarryCommonEvent
import com.czur.cloud.ui.starry.meeting.agora.SurfaceViewSnapCache
import com.czur.cloud.ui.starry.meeting.common.UserHandler
import com.czur.cloud.ui.starry.meeting.fragment.newmain.CustomLayoutTransition
import com.czur.cloud.ui.starry.meeting.model.MeetingModel
import com.czur.cloud.ui.starry.meeting.model.ModelManager
import kotlinx.coroutines.*
import org.greenrobot.eventbus.EventBus


/**
 * Created by 陈丰尧 on 2022/3/3
 * 用来存放SurfaceView的容器
 * 当SurfaceView被移除的时候, 会截取SurfaceView当前的图像, 放在里面
 */
class VideoContainer @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : FrameLayout(context, attrs, defStyleAttr) {
    val SCALE = 3.0f //最大最小之间的差距 不能超过3倍, 可能是因为缩太小了以后手指的小量位移波动, 就会引起大幅度形变


    val SCALE_MAX = 1.0f //最大的缩放比例

    private val SCALE_MIN_AND_NORMAL = SCALE_MAX / SCALE //缩放的最小值， 也是用户看到的比例为1的值

    private var oldDistance = 0.0
    private var moveDist = 0.0
    private var downX1 = 0f
    private var downX2 = 0f
    private var downY1 = 0f
    private var downY2 = 0f

    private var singleMoveDownX = 0f
    private var singleMoveDownY = 0f
    private var lastFingerUpTime: Long = 0
    private var doubleFingerEvent = false //进行过双指事件后 手指没有完全松开，时候不走单指移动事件
    var singleClick = true

    private var baseWidth = 0 //放大后控件的宽高

    private var baseHeight = 0
    private var smallWidth = 0f //中间小窗的宽高

    private var smallHeight = 0f


    var videoView: View? = null
    var currentVideoView: View? = null


    private var bitmap: Bitmap? = null

    // 记录上一个加入的SurfaceView, 当SurfaceView被移除后, 使用该uid来显示截图
    private var lastUid: String = ""

    // 当前显示的uid, 与SurfaceView保持一致
    var currentUid: String = ""
        private set

    // 初始的两个手指按下的触摸点的距离
    private var oriDis = 1f

    var isMainDisplayBigView = false // 是否是主页面的大屏
    var saveScreenOrientation = -1; // 保存的屏幕当前横竖屏状态

    var canScale = false // 是否可以缩放

    companion object {
        private const val TAG = "SurfaceContainer"
    }

    var showSnapImg: Boolean = true
        set(value) {
            field = value
            surfaceScreenshotIv.visibility = if (value) VISIBLE else GONE
        }

    private val surfaceScreenshotIv: ImageView by lazy {
        findViewById(R.id.surfaceScreenshotIv)
    }

    init {
        inflate(context, R.layout.widget_surface_container, this)
        layoutTransition = CustomLayoutTransition()
    }

    override fun addOnLayoutChangeListener(listener: OnLayoutChangeListener?) {
        super.addOnLayoutChangeListener(listener)
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        if (isShareDisplayView()) {
            if (saveScreenOrientation != resources.configuration.orientation) {
                resetTextTureViewOnScreenSizeChanged()
            } else {
                invalidate()
            }

            //横屏时候 有texttureview 阻止父布局重新layout
        } else {
            super.onLayout(changed, left, top, right, bottom)
        }
    }

    // 横竖屏切换时,重置分享窗口尺寸
    fun resetTextTureViewOnScreenSizeChanged() {
        if (videoView != null) {
            if (videoView is TextureView || canScale) {
                videoView?.post {
                    shareScreenInit()
                }
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        if (resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE
            || resources.configuration.orientation == Configuration.ORIENTATION_PORTRAIT
            && isShareDisplayView()
        ) {
            saveScreenOrientation = resources.configuration.orientation
            resetTextTureViewOnScreenSizeChanged()
        }
    }

    fun getSurfaceView(){

    }
    /**
     * 向容器中添加SurfaceView
     */
    fun addSurface(surfaceView: View, uid: String, isMainDisplayView: Boolean = false) {
        if (lastUid != uid) {
            lastUid = ""
            // 清空之前的截图
            surfaceScreenshotIv.setImageBitmap(null)
            bitmap?.recycle()
            bitmap = null
        }
        if (childCount > 1) {
            return
        }
        lastUid = uid
        stripView(surfaceView)
        currentVideoView = surfaceView
        currentUid = uid
        this.isMainDisplayBigView = isMainDisplayView
        saveScreenOrientation != resources.configuration.orientation

        if (isShareDisplayView()) {
            videoView = surfaceView
//            post {
            surfaceView.post {
                shareScreenInit()
            }
            handleTouchEvent(videoView)
            addView(videoView, getTextTureLayoutParam())

//            }
        } else {
            addView(surfaceView, getSurfaceLayoutParam())
        }

        showSnapImg = true

    }

    var layoutLeft: Int = 0
    var layoutTop: Int = 0
    var layoutRight: Int = 0
    var layoutBottom: Int = 0


    fun shareScreenInit() {
        this.post {
            baseWidth = (width / SCALE_MIN_AND_NORMAL).toInt()
            baseHeight = (height / SCALE_MIN_AND_NORMAL).toInt()

            smallWidth = baseWidth * SCALE_MIN_AND_NORMAL
            smallHeight = baseHeight * SCALE_MIN_AND_NORMAL

            layoutLeft = (-(baseWidth - baseWidth / SCALE) / 2).toInt()
            layoutTop = (-(baseHeight - baseHeight / SCALE) / 2).toInt()
            layoutRight =
                ((baseWidth - (baseWidth / SCALE)) / 2 + baseWidth / SCALE).toInt()
            layoutBottom =
                ((baseHeight - (baseHeight / SCALE)) / 2 + baseHeight / SCALE).toInt()
            videoView?.post {
                videoView?.layout(layoutLeft, layoutTop, layoutRight, layoutBottom)
                setInitScale()
            }
        }
    }

    var firstClickTime = 0L
    var secondClickTime = 0L
    var clickJob: Job? = null

    var moveCount = 0

    @SuppressLint("ClickableViewAccessibility")
    fun handleTouchEvent(surfaceView: View?) {

        surfaceView?.setOnTouchListener { v, event ->

            val pointerCount = event.pointerCount
            when (event.action and MotionEvent.ACTION_MASK) {
                MotionEvent.ACTION_DOWN -> {
                    moveCount = 0
                    if (videoView?.scaleX!! <= SCALE_MIN_AND_NORMAL) {
                        getParent().requestDisallowInterceptTouchEvent(false);
                    } else {
                        getParent().requestDisallowInterceptTouchEvent(true);
                    }
                    singleClick = true
                    singleMoveDownX = event.x
                    singleMoveDownY = event.y
                }
                MotionEvent.ACTION_UP -> {
                    if (singleClick) {

                        if (firstClickTime == 0L) {
                            firstClickTime = TimeUtils.getNowMills()
                        } else {
                            secondClickTime = TimeUtils.getNowMills()
                        }

                        if (secondClickTime == 0L) {
                            clickJob = GlobalScope.launch {

                                delay(400)
                                firstClickTime = 0L
                                secondClickTime = 0L
                                EventBus.getDefault()
                                    .post(StarryCommonEvent(EventType.STARRY_USER_TOUCH_SCREEN, ""))
                            }
                        }
                        val x1 = event.getX(0) //控件的XY坐标
                        val y1 = event.getY(0)
//                        Log.d("ContainerLayout", "x1:" + x1 + "y1:" + y1)
                        val clickInterval = secondClickTime - firstClickTime
                        if (clickInterval in 1..300) {
                            // 双击
                            if (clickJob != null && clickJob?.isActive == true) {
                                clickJob?.cancel()
                            }
                            firstClickTime = 0L
                            secondClickTime = 0L
//                            ToastUtils.showShort("双击pointerCount == 2x")

                            if (videoView?.scaleX == SCALE_MIN_AND_NORMAL) {
                                //当前最小 放大
                                setScale(SCALE_MAX - 0.1f)

                                // 点击的点在屏幕的百分比处
                                val percentX = x1.toFloat() / baseWidth.toFloat()
                                val percentY = y1.toFloat() / baseHeight.toFloat()

                                //用百分比计算出 缩小时,所在的具体像素点
                                val smallPxx = percentX * width.toFloat()
                                val smallPxy = percentY * height.toFloat()


                                val scaleXNow: Float = videoView?.getScaleX() ?: 0f //获取当前缩放值
                                //计算放大后, 所在的像素点

                                //放大后的像素宽高
                                val bigWidth: Float = baseWidth * scaleXNow
                                val bigHeight: Float = baseHeight * scaleXNow

                                //先确定 点 在放大后控件上的位置
                                val percentPxx: Float = bigWidth * percentX
                                val percentPxy: Float = bigHeight * percentY

                                //中心点相差距离
                                val distancex: Float = bigWidth / 2 - width.toFloat() / 2
                                val distancey: Float = bigHeight / 2 - height.toFloat() / 2
                                //移动控件达到真实位置,获取真实big的点坐标
                                val resultx: Float = percentPxx - distancex
                                val resulty: Float = percentPxy - distancey

                                //计算出新控件宽高
                                val newWidth = scaleXNow * baseWidth
                                val newHeight = scaleXNow * baseHeight
                                //以移动到底边为例,计算移动距离与pivot与距离比例
                                val disY = (baseHeight - newHeight) / 2 //distance 控件与大imgview 的单侧边距
                                val pivotYCenterToBottomDis =
                                    (baseHeight / 2).toFloat() //pivot 需要移动到最底端的距离
                                val biliTopY = pivotYCenterToBottomDis / disY //比例 pivot/dis

                                //以移动到底边为例,计算移动距离与pivot与距离比例
                                val disX = (baseWidth - newWidth) / 2 //distance 控件与大控件 的单侧边距
                                val pivotXCenterToLeft =
                                    (baseWidth / 2).toFloat() //pivot 需要移动到最底端的距离
                                val biliTopX = pivotXCenterToLeft / disX //比例 pivot/dis

                                val resultPivotX: Float = -(resultx - smallPxx) * biliTopX
                                val resultPivotY: Float = -(resulty - smallPxy) * biliTopY
                                val pivotx: Float = videoView?.getPivotX()?.plus(resultPivotX) ?: 0f
                                val pivoty: Float = videoView?.getPivotY()?.plus(resultPivotY) ?: 0f
                                setPivot(pivotx, pivoty)

                            } else {
                                // 缩小
                                setInitScale()
                            }

                        }
                    }
                    doubleFingerEvent = false
                    lastFingerUpTime = System.currentTimeMillis()
                    if (pointerCount == 2) {
                        downX1 = 0f
                        downY1 = 0f
                        downX2 = 0f
                        downY2 = 0f
                    }
                }
                MotionEvent.ACTION_MOVE -> {
                    moveCount++
                    if (videoView?.scaleX!! <= SCALE_MIN_AND_NORMAL) {
                        getParent().requestDisallowInterceptTouchEvent(false);
                    } else {
                        getParent().requestDisallowInterceptTouchEvent(true);
                    }
                    if (pointerCount == 1) {
                        if (doubleFingerEvent == false) {//非双指移开时候的遗留单指误操作
                            val doublePointSpace: Double =
                                doublePointSpace(
                                    event.getX(0),
                                    event.getY(0),
                                    singleMoveDownX,
                                    singleMoveDownY
                                )
                            if (doublePointSpace > 10) { //防止单指触摸时候的抖动

                                singleClick = false
                                val pianyiX = event.getX(0) - singleMoveDownX
                                val pianyiY = event.getY(0) - singleMoveDownY
//                                Log.d(
//                                    "ContainerLayout",
//                                    "pointerCount == 1x" + pianyiX
//                                            + "  y" + pianyiY
//                                            + "  singleMoveDownX" + singleMoveDownX
//                                            + "  singleMoveDownY" + singleMoveDownY
//                                            + "  event.getX(0)" + event.getX(0)
//                                            + "  event.getY(0)" + event.getY(0)
//                                )
                                setFormatPivot(pianyiX, pianyiY, pointerCount)
                            }
                        }
                    } else if (pointerCount == 2) {
                        singleClick = false
                        //双指缩放处理
                        moveDist = spacing(event)
                        val space = moveDist - oldDistance
                        val scale = (v.scaleX + space / v.width).toFloat()
                        if (scale < SCALE_MIN_AND_NORMAL) {
                            setScale(SCALE_MIN_AND_NORMAL)
                        } else if (scale > SCALE_MAX - 0.1f) {
                            setScale(SCALE_MAX - 0.1f)
                        } else {
                            setScale(scale)
                        }
                        val x1 = event.getX(0) //控件的XY坐标
                        val x2 = event.getX(1)
                        val y1 = event.getY(0)
                        val y2 = event.getY(1)
                        val changeX1 = (x1 - downX1).toDouble() //偏移量
                        val changeX2 = (x2 - downX2).toDouble()
                        val changeY1 = (y1 - downY1).toDouble()
                        val changeY2 = (y2 - downY2).toDouble()
                        if (scaleX > SCALE_MIN_AND_NORMAL) { //双指滑动
                            val pianyiX = (changeX1 / 2 + changeX2 / 2).toFloat()
                            val pianyiY = (changeY1 / 2 + changeY2 / 2).toFloat()
                            setFormatPivot(pianyiX, pianyiY, pointerCount)
                        }
                    }
                }
                MotionEvent.ACTION_POINTER_DOWN -> if (pointerCount == 2) {
                    singleClick = false
                    doubleFingerEvent = true
                    downX1 = event.getX(0)
                    downX2 = event.getX(1)
                    downY1 = event.getY(0)
                    downY2 = event.getY(1)
                    oldDistance = spacing(event) //两点按下时的距离
                }
                MotionEvent.ACTION_POINTER_UP -> {
                }
                else -> {
                }
            }
            true
        }


    }

    override fun removeAllViews() {
        // 防止将ImageView也销毁掉
        removeSurface()
    }

    /**
     * 从容器中移除SurfaceView
     */
    fun removeSurface(showSnap: Boolean = true) {

        if (removeSur() || removeTextT()) {
            currentUid = ""
        }

        if (showSnap) {
            SurfaceViewSnapCache.getBitmap(lastUid)?.let {
                showSnapImg = true
                surfaceScreenshotIv.setImageBitmap(it)
            }
        } else {
            surfaceScreenshotIv.setImageBitmap(null)
            showSnapImg = false
        }
    }

    private fun removeSur(): Boolean {
        val surfaceView = children.find {
            it is SurfaceView
        } as? SurfaceView ?: return false
        removeView(surfaceView)
        return true
    }

    private fun removeTextT(): Boolean {
        val textureView = children.find {
            it is TextureView
        } as? TextureView ?: return false
        removeView(textureView)
        return true
    }

    private fun hasTextTureViewChild(): Boolean {
        val textureView = children.find {
            it is TextureView
        } as? TextureView ?: return false
        return true
    }

    /**
     * 查看surfaceView是否被添加到别的View中
     * 如果有, 则将其从之前的parent中移除
     */
    private fun stripView(view: View) {
        when (val parent = view.parent) {
            is VideoContainer -> parent.removeSurface()
            is FrameLayout -> parent.removeView(view)
            else -> {
            }
        }
    }

    private fun setFormatPivot(px: Float, py: Float, pointerCount: Int) {
//        Log.d("ContainerLayout", "setFormatPivot:px:" + px + " setFormatPivot:py:" + py)
        var pivotx: Float = videoView?.getPivotX()?.plus(px) ?: 0f
        var pivoty: Float = videoView?.getPivotY()?.plus(py) ?: 0f
        val scaleXNow: Float = videoView?.getScaleX() ?: 0f //获取当前缩放值
        //计算出新控件宽高
        val newWidth = scaleXNow * baseWidth
        val newHeight = scaleXNow * baseHeight
        //以移动到底边为例,计算移动距离与pivot与距离比例
        val disY = (baseHeight - newHeight) / 2 //distance 控件与大imgview 的单侧边距
        val pivotYCenterToBottomDis = (baseHeight / 2).toFloat() //pivot 需要移动到最底端的距离
        val biliTopY = pivotYCenterToBottomDis / disY //比例 pivot/dis
        val needMoveY = (baseHeight * scaleXNow - baseHeight * SCALE_MIN_AND_NORMAL) / 2
        val resultPivotTop = needMoveY * biliTopY + pivotYCenterToBottomDis
        val resultPivotBottom = pivotYCenterToBottomDis - needMoveY * biliTopY
//        Log.d("ContainerLayout", "pivoty$pivoty  resultPivot$resultPivotTop")

        //以移动到底边为例,计算移动距离与pivot与距离比例
        val disX = (baseWidth - newWidth) / 2 //distance 控件与大控件 的单侧边距
        val pivotXCenterToLeft = (baseWidth / 2).toFloat() //pivot 需要移动到最底端的距离
        val biliTopX = pivotXCenterToLeft / disX //比例 pivot/dis
        val needMoveX = (baseWidth * scaleXNow - baseWidth * SCALE_MIN_AND_NORMAL) / 2
        val resultPivotLeft = needMoveX * biliTopX + pivotXCenterToLeft
        val resultPivotRight = pivotXCenterToLeft - needMoveX * biliTopX

        //移动后的新坐标
        if (pivoty > resultPivotTop) { //上方超标
//            Log.d("resultPivotTop", "resultPivotTopTop" + resultPivotTop + " pivoty" + pivoty)
            pivoty = resultPivotTop
        } else
            if (pivoty < resultPivotBottom) { // 下方超标
//                Log.d(
//                    "resultPivotTop",
//                    "resultPivotTopBottom" + resultPivotBottom + " pivoty" + pivoty
//                )
                pivoty = resultPivotBottom
            }
        //移动后的新坐标
        if (pivotx > resultPivotLeft) { //左方超标
//            Log.d("resultPivotTop", "resultPivotTopLeft" + resultPivotLeft + " pivotx" + pivotx)
            pivotx = resultPivotLeft
        } else
            if (pivotx < resultPivotRight) { // 右方超标
//                Log.d(
//                    "resultPivotTop",
//                    "resultPivotTopRight" + resultPivotRight + " pivotx" + pivotx
//                )
                pivotx = resultPivotRight
                if (pointerCount == 1) {
                    getParent().requestDisallowInterceptTouchEvent(false);
                }
            }
//        Log.d("ContainerLayout", "setFormatPivot:pivoty$pivoty  pivotx$pivotx")
        setPivot(pivotx, pivoty)
    }//3534 2288

    /**
     * 计算两个点的距离
     *
     * @param event
     * @return
     */
    private fun spacing(event: MotionEvent): Double {
        return if (event.pointerCount == 2) {
            val x = event.getX(0) - event.getX(1)
            val y = event.getY(0) - event.getY(1)
            Math.sqrt((x * x + y * y).toDouble())
        } else {
            0.0
        }
    }

    //两点之间距离
    private fun doublePointSpace(x1: Float, y1: Float, x2: Float, y2: Float): Double {
        val x = x1 - x2
        val y = y1 - y2
        return Math.sqrt((x * x + y * y).toDouble())
    }

    /**
     * 平移画面，当画面的宽或高大于屏幕宽高时，调用此方法进行平移
     *
     * @param x
     * @param y
     */
    fun setPivot(x: Float, y: Float) {
        videoView?.setPivotX(x)
        videoView?.setPivotY(y)
        videoView?.invalidate()
    }


    /**
     * 设置放大缩小
     *
     * @param scale
     */
    fun setScale(scale: Float) {
        videoView?.setScaleX(scale)
        videoView?.setScaleY(scale)
    }

    /**
     * 初始化比例，用户手机屏幕
     */
    fun setInitScale() {
        videoView?.setScaleX(SCALE_MIN_AND_NORMAL)
        videoView?.setScaleY(SCALE_MIN_AND_NORMAL)
        setPivot(
            ((width.toFloat() / SCALE_MIN_AND_NORMAL) / 2).toFloat(),
            ((height.toFloat() / SCALE_MIN_AND_NORMAL) / 2).toFloat()
        )
    }


    /**
     * 获取ServiceView的LayoutParam
     */
    private fun getTextTureLayoutParam(): ViewGroup.LayoutParams? {
        return LayoutParams(
            (width.toFloat() / SCALE_MIN_AND_NORMAL).toInt(),
            (height.toFloat() / SCALE_MIN_AND_NORMAL).toInt()
        )
    }

    /**
     * 获取ServiceView的LayoutParam
     */
    private fun getSurfaceLayoutParam(): ViewGroup.LayoutParams {
        return LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
    }

    private fun isShareDisplayView(): Boolean {
        val shareMember = ModelManager.membersModel.getShareMemberCanNull()

        return (MeetingModel.shareMode &&
                shareMember != null &&
                (shareMember.czurID != UserHandler.czurId.toString())
                && isMainDisplayBigView)
                || canScale
    }
}