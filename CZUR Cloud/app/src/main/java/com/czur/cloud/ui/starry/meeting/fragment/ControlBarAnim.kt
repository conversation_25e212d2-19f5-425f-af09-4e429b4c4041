package com.czur.cloud.ui.starry.meeting.fragment

import android.view.View
import com.czur.cloud.ui.starry.meeting.baselib.utils.gone
import com.czur.cloud.ui.starry.meeting.baselib.utils.show
import com.czur.cloud.ui.starry.meeting.utils.anim.*
import com.czur.cloud.ui.starry.meeting.widget.BulletLayout

/**
 * Created by 陈丰尧 on 2021/6/17
 * 用来控制主画面底部控制条中各个元素的动画
 * - 控制条是平移动画
 * - 底色是渐变动画
 * 动画遵循以下逻辑:
 * 1. 如果用户长时间没有操作, 则自动隐藏控制按钮
 * 2. 如果页面上弹出了悬浮窗, 则不隐藏控制按钮   20220310 sqj 不判断此条件
 * 3. 如果隐藏控制按钮时, 页面上还有正在显示的消息弹幕, 则弹幕View跟随控制条一起使用向下移动的动画
 * 4. 如果控制条和弹幕View都不显示了, 则同时隐藏底纹
 * 5. 如果底纹已经消失,此时想要显示控制条/弹幕View 则先显示底纹,再显示控制条/弹幕View
 */

private const val TAG = "ControlBarAnim"

class ControlBarAnim(
    private val controlActionBar: View,
    private val bulletLayout: BulletLayout,
    private val controlBarBg: View,
    controlBarFragment: ControlBarFragment
) {
    // 操作栏的动画属性
    private val controlBarAnim: AnimParam by lazy {
        AnimParam.flyFromBottom(controlActionBar)
    }

    // 弹幕View的动画
    private val bulletBarAnim: AnimParam by lazy {
        AnimParam(
            view = bulletLayout,
            hideY = 42F,
            hideZ = -bulletLayout.elevation,
            hideAlpha = 1F
        )
    }

    init {
        // 弹幕是否有消息的监听
        bulletLayout.isEmptyLive.observe(controlBarFragment) { bulletIsEmpty ->
//            logV(TAG, "bulletIsEmpty:${bulletIsEmpty}")
            if (bulletIsEmpty) {
                bulletLayout.gone()
            } else {
                bulletLayout.show()
            }
        }
    }

    /**
     * 更新动画状态
     * @param userIdle: 用户操作是否空闲
     * @param floatShow: 是否有悬浮窗正在显示
     */
    fun updateAnim(userIdle: Boolean, floatShow: Boolean) {
        if (userIdle && !floatShow) {
            controlBarAnim.hide ()
            bulletBarAnim.moveToEnd()
        } else {
            // 显示的时候, 先显示背景,再显示操作栏
            controlBarAnim.show()
            bulletBarAnim.moveToStart()
        }
    }

}