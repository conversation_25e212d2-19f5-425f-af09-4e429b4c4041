package com.czur.cloud.entity.realm;

import io.realm.RealmObject;

/**
 * Created by Yz on 2018/4/27.
 * Email：<EMAIL>
 */
public class DownloadEntity extends RealmObject {


    private String fileId;
    private String uuid;
    private boolean hasDownloadImage;
    private boolean hasMakeSmallImage;

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public boolean isHasDownloadImage() {
        return hasDownloadImage;
    }

    public void setHasDownloadImage(boolean hasDownloadImage) {
        this.hasDownloadImage = hasDownloadImage;
    }

    public boolean isHasMakeSmallImage() {
        return hasMakeSmallImage;
    }

    public void setHasMakeSmallImage(boolean hasMakeSmallImage) {
        this.hasMakeSmallImage = hasMakeSmallImage;
    }






}
