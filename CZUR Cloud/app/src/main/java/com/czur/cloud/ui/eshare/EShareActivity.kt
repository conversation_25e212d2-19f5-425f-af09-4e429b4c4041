package com.czur.cloud.ui.eshare


import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.app.NotificationManager
import android.bluetooth.BluetoothManager
import android.content.*
import android.content.pm.PackageManager
import android.graphics.PorterDuff
import android.net.Uri
import android.net.nsd.NsdManager
import android.net.nsd.NsdServiceInfo
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.Parcelable
import android.provider.Settings
import android.util.Log
import android.view.KeyEvent
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Group
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.*
import com.czur.cloud.BuildConfig
import com.czur.cloud.R
import com.czur.cloud.preferences.ESharePreferences
import com.czur.cloud.ui.base.CzurCloudApplication
import com.czur.cloud.ui.eshare.common.EshareConstants
import com.czur.cloud.ui.eshare.engine.Constants.CAST
import com.czur.cloud.ui.eshare.engine.Constants.FULLCAST
import com.czur.cloud.ui.eshare.receiver.CastStateListener
import com.czur.cloud.ui.eshare.service.EShareHeartBeatService
import com.czur.cloud.ui.eshare.transmitfile.TransmitFileUpLoadActivity
import com.czur.cloud.ui.eshare.utils.NetConnectListenerUtil
import com.czur.cloud.ui.eshare.view.PaintController
import com.czur.cloud.ui.eshare.view.PlaceHolderLayerController
import com.czur.cloud.ui.eshare.viewmodel.EShareViewModel
import com.czur.cloud.ui.eshare.widget.TransmitFileDialogManager
import com.czur.cloud.ui.starry.base.StarryBaseActivity
import com.czur.cloud.ui.starry.livedatabus.LiveDataBus
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.utils.RomUtils.isOppo
import com.czur.cloud.ui.starry.utils.singleClick
import com.czur.cloud.util.PermissionCallBack
import com.czur.cloud.util.PermissionUtil
import com.czur.czurutils.log.logD
import com.czur.czurutils.log.logI
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.eshare.api.EShareAPI
import com.eshare.api.IDevice
import com.eshare.api.IMedia
import com.eshare.api.IScreen
import com.eshare.api.callback.MediaStateCallback
import kotlinx.android.synthetic.main.eshare_activity_main.*
import kotlinx.android.synthetic.main.eshare_layout_top_bar.*
import kotlinx.coroutines.*
import java.net.InetAddress

/**
 * EShare页面
 */
class EShareActivity : StarryBaseActivity(), CastStateListener.Callback,
    NetConnectListenerUtil.NetStateListener {
    companion object {
        private const val TAG = "EShareActivity"
        private const val REQ_CODE_START_SCREEN_MIRROR = 100
        private const val MY_PERMISSIONS_REQUEST_BLUETOOTH_CONNECT = 1001
        var eshareIsRunning = false
        val ACTION_CAST_START = "com.eshare.action.mirror.connected"
        val ACTION_CAST_STOP = "com.eshare.action.mirror.disconnected"

        //画笔
        var mPaintController: PaintController? = null

        var needIntentToFindDeviceAty = false // 如果应用在后台,不跳转,等待用户返回时候再跳转
            set(value) {
                field = value
                logTagD(TAG, "set needIntentToFindDeviceAty=${value}")
            }
    }


    private var mCastReceiver: CastReceiver? = null

    private var mCastStateListener: CastStateListener? = null

    private var needIntentToFindDevice = false

    private val mScreenManager: IScreen by lazy {
        EShareAPI.init(this).screen()
    }
    private val mDeviceManager: IDevice by lazy {
        EShareAPI.init(this).device()
    }
    private val viewModel by lazy {
        ViewModelProvider(this)[EShareViewModel::class.java]
    }
    private val applicationViewModel by lazy {
        (applicationContext as CzurCloudApplication).getEshareViewModel1()
    }

    private val preference by lazy {
        ESharePreferences.getInstance()
    }

    private val user_back_btn by lazy {
        findViewById<ImageView>(R.id.user_back_btn)
    }

    private val btn_mirror by lazy {
        findViewById<Button>(R.id.btn_mirror)
    }
    private val eshare_mid_btn_play by lazy {
        findViewById<ImageView>(R.id.eshare_mid_btn_play)
    }
    private val eshare_disconnect_ll by lazy {
        findViewById<ConstraintLayout>(R.id.eshare_disconnect_cl)
    }

    private val eshare_mid_play_name by lazy {
        findViewById<TextView>(R.id.eshare_mid_play_name)
    }

    private val eshare_fullscreen_ll by lazy {
        findViewById<Group>(R.id.eshare_fullscreen_ll)
    }
    private val eshare_paint_cl by lazy {
        findViewById<Group>(R.id.eshare_paint_cl)
    }
    private val eshare_paint_iv by lazy {
        findViewById<ImageView>(R.id.eshare_paint_iv)
    }

    private val eshare_paint_tv by lazy {
        findViewById<TextView>(R.id.eshare_paint_tv)
    }
    private val eshare_main_bottom_doc_ll by lazy {
        findViewById<LinearLayout>(R.id.eshare_main_bottom_doc_ll)
    }
    private val eshare_main_bottom_pic_ll by lazy {
        findViewById<LinearLayout>(R.id.eshare_main_bottom_pic_ll)
    }
    private val eshare_main_bottom_vdo_ll by lazy {
        findViewById<LinearLayout>(R.id.eshare_main_bottom_vdo_ll)
    }
    private val eshare_main_bottom_cam_ll by lazy {
        findViewById<LinearLayout>(R.id.eshare_main_bottom_cam_ll)
    }

    private val eshare_fullscreen_iv by lazy {
        findViewById<ImageView>(R.id.eshare_fullscreen_iv)
    }

    private val eshare_fullscreen_iv_cancel by lazy {
        findViewById<ImageView>(R.id.eshare_fullscreen_iv_cancel)
    }

    private val eshare_fullscreen_tv by lazy {
        findViewById<TextView>(R.id.eshare_fullscreen_tv)
    }

    private val eshare_device_name_tv by lazy {
        findViewById<TextView>(R.id.eshare_device_name_tv)
    }
    private val eshare_transmit_upload_file_cl by lazy {
        findViewById<ConstraintLayout>(R.id.eshare_transmit_upload_file_cl)
    }
    private val eshare_transmit_download_file_cl by lazy {
        findViewById<ConstraintLayout>(R.id.eshare_transmit_download_file_cl)
    }
    private val download_title_tv by lazy {
        findViewById<TextView>(R.id.download_title_tv)
    }
    private val upload_title_tv by lazy {
        findViewById<TextView>(R.id.upload_title_tv)
    }
    private val mMediaManager: IMedia by lazy {
        EShareAPI.init(this).media()
    }

    private var btnStatusFlag = false
    private var isConnectView = false
    private var exitTime: Long = 0

    //首次投屏按钮标记
    private var isFistStart = true

    //独占全屏状态标记
    private var isFullScreenState = false

    //是否显示独占全屏
    private var ScreenFullVisible = false

    //    //画笔
//    private var mPaintController: PaintController? = null
    //投屏或停止 timeout
    private var Timecount = 0
    private val TIME_OUT_COUNT = 8
    private var job: Job? = null
    private var jobCastState: Job? = null
    private var jobreCastState: Job? = null
    private var callBack = false

    private var mobileName: String = Build.MODEL
    //接收断开广播不处理
//    private var  isDoNothing = false
    /***断开广播处理
     * 1.点击断开；2.双击back；3.后台kill；
     * 4.长链接断开；5.账号其他手机登陆
     */


    // 后台当时检测任务
    private val bgServiceChecker: BackgroundServiceChecker by lazy { BackgroundServiceChecker(this) }

    private val resolveListener = object : NsdManager.ResolveListener {

        override fun onResolveFailed(serviceInfo: NsdServiceInfo, errorCode: Int) {
            // Called when the resolve fails. Use the error code to debug.
            Log.e(TAG, "Resolve failed: $errorCode")
        }

        override fun onServiceResolved(serviceInfo: NsdServiceInfo) {
            Log.e(TAG, "Resolve Succeeded. $serviceInfo")

//                if (serviceInfo.serviceName == mServiceName) {
//                    logTagI(TAG, "Same IP.")
//                    return
//                }
//                mService = serviceInfo
            val port: Int = serviceInfo.port
            val host: InetAddress = serviceInfo.host
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setStatusBarColor(R.color.eshare_common_bg)
        BarUtils.setStatusBarLightMode(this, false)
        setContentView(R.layout.eshare_activity_main)

        initView()


//        if (!Settings.canDrawOverlays(this)
//            || !PermissionUtils.isGranted(Manifest.permission.RECORD_AUDIO)
//        ) {// 切出去更改权限,返回来的情况,不做处理,直接返回首页
//            finish()
//        }
        initNetListener()

        initComponent()
        initBeatListener()

        // 注册监听
        initBroadCastListener()

        showPlayBtnStatus(applicationViewModel.esharePlayStatus)

//        startOneTimeWork()
        // 检查前台服务是否存活
//        AppClearUtils.startScreenNotify()
//
//        bgServiceChecker.startChecking()
    }

    private fun initView() {
        if (BuildConfig.IS_OVERSEAS) {
            download_title_tv.textSize = 14f
            upload_title_tv.textSize = 14f
        }
    }

    private fun getDeviceName() {
        if (ActivityCompat.checkSelfPermission(
                this,
                Manifest.permission.BLUETOOTH_CONNECT
            ) == PackageManager.PERMISSION_GRANTED
        ) {
            val bluetoothManager = getSystemService(BLUETOOTH_SERVICE) as BluetoothManager
            val bluetoothAdapter = bluetoothManager.adapter
            mobileName = bluetoothAdapter.name
        }
    }

    private fun initNetListener() {
        NetConnectListenerUtil.init()
        NetConnectListenerUtil.addNetListener(this)
    }

    private fun initBroadCastListener() {
        //判断投屏状态  主要用这个广播判断是否在投屏
        mCastStateListener = CastStateListener.getSingleton()
        mCastStateListener?.initListener(this)
        mCastStateListener?.registerCallback(this)
    }

    private fun initBeatListener() {
        jobCastState = launch {
            //castStatus   0 代表没投屏   1代表投屏中  2  代表独占全屏
            EShareHeartBeatService.castStatusChanged.collect {
                logI("${TAG}.initBeatListener.castStatusChanged=${it}")
                if (it != 5) {
                    callBack = true
                    job?.cancel()
                    EShareHeartBeatService.startService(this@EShareActivity)
                } else {
                    EShareHeartBeatService.stopService(this@EShareActivity)
                }

                when (it) {
                    0 -> {
                        launch {
                            applicationViewModel.esharePlayStatus = false
                            eshareIsRunning = false
                            showPlayBtnStatus(false)
//                            stopPaintController()
                            stopPaintController()
                            countDowndTimer()
                            stopMirror()
                        }
                    }

                    1 -> {
                        launch {//设备端取消独占全屏 (正常状态)
                            applicationViewModel.esharePlayStatus = true
                            eshareIsRunning = true
                            showPlayBtnStatus(true)
                            if (!isFullScreenState) {//规避设备端从独占改回正常模式时,画笔会被取消
                                startPaintController()
                            }
                            isFullScreenState = false
                        }
                    }

                    2 -> {
                        //独占全屏成功
                        isFullScreenState = true
                        updateFullUI(true)
                    }

                    3 -> {
                        //1分屏不显示独占
                        ScreenFullVisible = false
                        isVisibleAllScreen(ScreenFullVisible)
                    }

                    4 -> {
                        ScreenFullVisible = true
                        isVisibleAllScreen(ScreenFullVisible)
                    }

                    5 -> {
                        launch {
                            if (ActivityUtils.getTopActivity().localClassName != localClassName
                                || !AppUtils.isAppForeground()
                            ) {//暂缓处理
                                needIntentToFindDeviceAty = true
                            } else {
                                disConnectDevice()
                                ToastUtils.showLong(R.string.eshare_connect_fail)
                                intentToFindDeviceActivity()
                            }
                        }
                    }

                }
            }

        }

        jobreCastState = launch {
            EShareHeartBeatService.replyCastState.collect {
                //replyCastStatus 1 代表同意投屏  0 拒绝投屏
                when (it) {
                    1 -> {
                        //投屏或者独占全屏
                        launch {
                            startMirror()
                        }

                    }

                    else -> {
                        if (applicationViewModel.esharePlayStatus) {
                            launch {
                                //独占全屏失败
                                ToastUtils.showLong(R.string.eshare_fullscreen_refuse)
                            }
                        } else {
                            launch {
                                ToastUtils.showLong(R.string.eshare_mid_refuse)
                                showPlayBtnStatus(false)
                            }
                        }
                    }
                }
            }
        }
    }

    private fun initComponent() {
        user_back_btn.visibility = View.VISIBLE
        user_back_btn?.singleClick {
            logI("user_back_btn.singleClick")
            finish()
        }

        eshare_transmit_upload_file_cl?.singleClick {
            viewModel.wantToTransmitFile(
                this,
                selectFileResultLauncher,
                applicationViewModel.currentDevice?.ipAddress ?: "",
                EShareViewModel.TransmitType.UPLOAD_TYPE,
                uploadSupport = viewModel.uploadSupport,
                downloadSupport = viewModel.downloadSupport
            )
        }
        eshare_transmit_download_file_cl?.singleClick {
            viewModel.wantToTransmitFile(
                this,
                selectFileResultLauncher,
                applicationViewModel.currentDevice?.ipAddress ?: "",
                EShareViewModel.TransmitType.DOWNLOAD_TYPE,
                uploadSupport = viewModel.uploadSupport,
                downloadSupport = viewModel.downloadSupport
            )
        }

        btn_mirror?.singleClick {
            logD("btn_mirror.singleClick")
            // mScreenManager.startTVMirror(this)
        }

        eshare_mid_btn_play?.singleClick {
            logD("eshare_mid_btn_play.singleClick${applicationViewModel.esharePlayStatus}")
            // startMirror()
            if (applicationViewModel.esharePlayStatus) {
                stopPaintController()
                countDowndTimer()
                stopMirror()
            } else {
                eshare_mid_btn_play?.background = getDrawable(R.drawable.eshare_circle_with_7ss)
                eshare_mid_btn_play?.setImageResource(R.mipmap.ic_connectting)
                eshare_transmit_upload_file_cl?.background =
                    getDrawable(R.drawable.eshare_circle_color_transparent_white_5dp)
                eshare_transmit_upload_file_cl.isEnabled = false
                eshare_transmit_download_file_cl?.background =
                    getDrawable(R.drawable.eshare_circle_color_transparent_white_5dp)
                eshare_transmit_download_file_cl.isEnabled = false
                eshare_mid_btn_play.isEnabled = false
                setViewDisable(eshare_disconnect_ll, false)
                eshare_mid_play_name?.text = getString(R.string.eshare_mid_starting_request_text)
                launch(Dispatchers.IO) {
                    countDowndTimer(true)
                    mDeviceManager.sendCastRequest(CAST)
                }
            }

        }

        val fullscreenViews = arrayOf(eshare_fullscreen_iv, eshare_fullscreen_tv)
        for (view in fullscreenViews) {
            view.singleClick {
                launch(Dispatchers.IO) {
                    if (isFullScreenState) {
                        mDeviceManager.exitFullScreen()
                        updateFullUI(false)
                        isFullScreenState = false
                    } else {
                        ToastUtils.showShort(R.string.eshare_fullscreen_requst)
                        mDeviceManager.sendCastRequest(FULLCAST)
                    }

                }
            }
        }

        eshare_disconnect_ll?.singleClick {
            logI("eshare_disconnect_ll.singleClick")
            disConnectDevice()
            // 重新连接
            intentToFindDeviceActivity()
            showPlayBtnStatus(false)
        }

        // document
        eshare_main_bottom_doc_ll?.singleClick {
            logI("eshare_main_bottom_doc_ll.singleClick")
            val name1 = "/test.docx"
            val filename =
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).absolutePath +
                        name1
            val name = FileUtils.getFileByPath(filename)
            logI("EShareActivity.eshare_main_bottom_doc_ll.name=${name}")
            Thread {
                try {
                    mMediaManager?.openFile(name)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }.start()
        }

        // picture
        eshare_main_bottom_pic_ll?.singleClick {
            logI("eshare_main_bottom_pic_ll.singleClick")
            val filename =
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).absolutePath +
                        "/ic_launcher.png"
            val name = FileUtils.getFileByPath(filename)
            logI("EShareActivity.eshare_main_bottom_pic_ll.name=${name}")
            Thread {
                try {
                    btnStatusFlag = if (!btnStatusFlag) {
                        mMediaManager?.openImage(name)
                        true
                    } else {
                        mMediaManager.closeImage()
                        false
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }.start()
        }

        // video audio
        eshare_main_bottom_vdo_ll?.singleClick {
            logI("eshare_main_bottom_vdo_ll.singleClick")
            val filename =
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).absolutePath +
                        "/test.mp4"
            val name = FileUtils.getFileByPath(filename)
            logI("EShareActivity.eshare_main_bottom_vdo_ll.name=${name}")
            Thread {
                try {
                    btnStatusFlag = if (!btnStatusFlag) {
                        mMediaManager?.openMedia(name)
                        true
                    } else {
                        mMediaManager.stopMedia()
                        false
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }.start()
        }

        // camera
        eshare_main_bottom_cam_ll?.singleClick {
            logI("EShareActivity.eshare_main_bottom_cam_ll")
            //【可在主线程调用】开始摄像头直播

            Thread {
                try {
                    btnStatusFlag = if (!btnStatusFlag) {
                        mDeviceManager?.startCameraLive()
                        true
                    } else {
                        mDeviceManager?.stopCameraLive()
                        false
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }.start()
        }


        val paintViews = arrayOf(eshare_paint_iv, eshare_paint_tv)
        for (view in paintViews) {
            view.singleClick {
                logI("EShareActivity.switchPaint")
                if (mPaintController == null) {
                    startPaintController()
                } else {
                    stopPaintController()
                }
            }
        }

        // 悬浮窗权限判断
//        if (checkPermission()) {
        val type = intent.getStringExtra(EshareConstants.ESHARE_EMPTY_TYPE)
            ?: EshareConstants.ESHARE_EMPTY_TYPE_NORMAL
        onStatuesRun(type)
//        }

//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) { //recode audio permission
//            val flag = ESPermissionUtils.requestSpeakerPermissions(this)
//            logI("${TAG}.requestSpeakerPermissions=${flag}")
//        }

        LiveDataBus.get()
            .with(EshareConstants.ESHARE_MEETING_CALLIN, Boolean::class.java)
            .observe(this) {
                logI("EShareActivity.ESHARE_MEETING_CALLIN.stopMirror()")

                launch {
                    applicationViewModel.esharePlayStatus = false
                    eshare_mid_btn_play.isEnabled = false
                    try {
                        mScreenManager.pauseScreenMirror(this@EShareActivity)
                        Thread.sleep(100)
                        mScreenManager.stopScreenMirror(this@EShareActivity)
                        Thread.sleep(100)
                        stopPaintController()
                        eshareIsRunning = false
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }

                    setViewDisable(eshare_disconnect_ll, false)
//                    eshare_mid_btn_play?.setImageResource(R.mipmap.eshare_btn_play_request)
                    eshare_transmit_upload_file_cl?.background =
                        getDrawable(R.drawable.eshare_circle_color_transparent_white_5dp)
                    eshare_transmit_upload_file_cl.isEnabled = false
                    eshare_transmit_download_file_cl?.background =
                        getDrawable(R.drawable.eshare_circle_color_transparent_white_5dp)
                    eshare_transmit_download_file_cl.isEnabled = false
                    eshare_mid_play_name?.text = getString(R.string.eshare_mid_start_pause)
//                    eshare_mid_play_subname?.visibility = View.GONE
                    eshare_fullscreen_ll.visibility = View.GONE
                    eshare_paint_cl.visibility = View.GONE
                }

            }

    }

    private fun onStatuesRun(type: String = "") {
        if (!applicationViewModel.esharePlayStatus) {
            intentToFindDeviceActivity(
                mutableList =
                mutableListOf(
                    Pair(EshareConstants.ESHARE_EMPTY_TYPE, type)
                )
            )

        }

        mMediaManager.getMediaState(object : MediaStateCallback {
            override fun onSuccess(p0: Int, p1: Int, p2: Int) {
                logI("EShareActivity.getMediaState.onSuccess.p0=${p0},p1=${p1},p2=${p2}")
                btnStatusFlag = false
            }

            override fun onError(p0: java.lang.Exception?) {
                logI("EShareActivity.getMediaState.onError.p0=${p0}")
                btnStatusFlag = false
            }

        })

    }

    private fun startPaintController() {
        stopPaintController()
        mPaintController = PaintController(this)
        eshare_paint_iv.setImageResource(R.mipmap.icon_pen_def)
        val color: Int =
            ContextCompat.getColor(this, R.color.eshare_common_bg) // 替换 "your_color" 为你想要的颜色资源
        eshare_paint_iv.setColorFilter(color, PorterDuff.Mode.SRC_IN)
        eshare_paint_iv.background = getDrawable(R.drawable.eshare_circle_with_white)
    }

    private fun stopPaintController() {
        if (mPaintController != null) {
            mPaintController?.close()
            mPaintController = null
        }
        eshare_paint_iv.setImageResource(R.mipmap.icon_pen_def)
        val color: Int = ContextCompat.getColor(this, R.color.white) // 替换 "your_color" 为你想要的颜色资源
        eshare_paint_iv.setColorFilter(color, PorterDuff.Mode.SRC_IN)
        eshare_paint_iv.background = getDrawable(R.drawable.eshare_circle_with_dark)
    }

    private fun startMirror() {
        logI("EShareActivity.startMirror")
//        if (!ESPermissionUtils.checkDrawOverlays(this)) {
//            return
//        }
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) { //recode audio permission
//            if (!ESPermissionUtils.requestSpeakerPermissions(this)) {
//                return
//            }
//        }
        try {
//            // @param isPaint 是否打开手写涂鸦功能
            val isSuccess = mScreenManager.startScreenMirror(this, true)
            if (!isSuccess) {
                showPlayBtnStatus(false)
                //pc端明宇定义投屏失败为连接超时
                ToastUtils.showLong(R.string.eshare_mid_timeout_text)
            }

        } catch (e: Exception) {
            showPlayBtnStatus(false)
            ToastUtils.showLong(R.string.eshare_mid_timeout_text)
        }

        logI("EShareActivity.startMirror222.viewModel.esharePlayStatus=${applicationViewModel.esharePlayStatus}")

    }

    private fun stopMirror() {

        applicationViewModel.esharePlayStatus = false
        eshareIsRunning = false
        eshare_mid_btn_play.isEnabled = false
        setViewDisable(eshare_disconnect_ll, false)
//        eshare_mid_btn_play?.setImageResource(R.mipmap.eshare_btn_play_request)
        eshare_transmit_upload_file_cl?.background =
            getDrawable(R.drawable.eshare_circle_color_transparent_white_5dp)
        eshare_transmit_upload_file_cl.isEnabled = false
        eshare_transmit_download_file_cl?.background =
            getDrawable(R.drawable.eshare_circle_color_transparent_white_5dp)
        eshare_transmit_download_file_cl.isEnabled = false
        eshare_mid_play_name?.text = getString(R.string.eshare_mid_start_pause)
//        eshare_mid_play_subname?.visibility = View.GONE
        eshare_fullscreen_ll.visibility = View.GONE
        eshare_paint_cl.visibility = View.GONE
        try {
//            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.Q) {
//                mScreenManager.stopScreenMirror(this)
//            }else{
//                mScreenManager.pauseScreenMirror(this)
//            }
            mScreenManager.pauseScreenMirror(this@EShareActivity)
            Thread.sleep(100)
            mScreenManager.stopScreenMirror(this@EShareActivity)
            Thread.sleep(100)
            stopPaintController()
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }


    //投屏或结束添加timeout计时
    fun countDowndTimer(needToast: Boolean = false) {
        Timecount = TIME_OUT_COUNT
        callBack = false
        if (job != null) {
            job?.cancel()
            job = null
        }
        job = launch(Dispatchers.IO) {
            while (Timecount > 0) {
                if (callBack) break
                delay(1000)
                Timecount--
            }
            if (Timecount <= 0) {
                try {
                    mScreenManager.pauseScreenMirror(this@EShareActivity)
                    Thread.sleep(100)
                    mScreenManager.stopScreenMirror(this@EShareActivity)
                    Thread.sleep(100)
                    stopPaintController()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                applicationViewModel.esharePlayStatus = false
                eshareIsRunning = false
                showPlayBtnStatus(false)
                if (needToast) {
                    withContext(Dispatchers.Main) {
                        ToastUtils.showLong(R.string.eshare_connect_mirror_fail)
                    }
                }
            }
        }
    }

    private fun showPlayBtnStatus(flag: Boolean) {
        launch {
            // 无线投屏主页面的play状态 true:已投屏，显示stop；false：未投屏显示play
            if (flag) {
                eshare_mid_btn_play?.setImageResource(R.mipmap.eshare_btn_stop_with_bg)
                eshare_transmit_upload_file_cl?.background =
                    getDrawable(R.drawable.eshare_circle_color_transparent_white_5dp)
                eshare_transmit_upload_file_cl.isEnabled = false
                eshare_transmit_download_file_cl?.background =
                    getDrawable(R.drawable.eshare_circle_color_transparent_white_5dp)
                eshare_transmit_download_file_cl.isEnabled = false
                eshare_mid_play_name?.text = getString(R.string.eshare_mid_starting_text)
//                eshare_mid_play_subname?.visibility = View.GONE
                if (ScreenFullVisible) {
                    eshare_fullscreen_ll.visibility = View.VISIBLE
                    eshare_paint_cl.visibility = View.VISIBLE
                } else {
                    eshare_fullscreen_ll.visibility = View.GONE
                    eshare_paint_cl.visibility = View.GONE
                }
                updateFullUI(false)
            } else if (!eshareIsRunning) {
//                eshare_mid_btn_play?.setImageResource(R.mipmap.eshare_btn_play)
                eshare_mid_btn_play?.setImageResource(R.mipmap.eshare_btn_play_with_bg)
                eshare_transmit_upload_file_cl?.background =
                    getDrawable(R.drawable.eshare_circle_color_white_5dp)
                eshare_transmit_upload_file_cl.isEnabled = true
                eshare_transmit_download_file_cl?.background =
                    getDrawable(R.drawable.eshare_circle_color_white_5dp)
                eshare_transmit_download_file_cl.isEnabled = true
                eshare_mid_play_name?.text = getString(R.string.eshare_mid_start_text)
//                eshare_mid_play_subname?.visibility = View.VISIBLE
                eshare_fullscreen_ll.visibility = View.GONE
                eshare_paint_cl.visibility = View.GONE
            }
            setViewDisable(eshare_mid_btn_play, true)
            setViewDisable(eshare_disconnect_ll, true)
        }
    }

    private fun isVisibleAllScreen(isVisible: Boolean) {
        launch {
            if (applicationViewModel.esharePlayStatus && isVisible) {
                eshare_fullscreen_ll.visibility = View.VISIBLE
                eshare_paint_cl.visibility = View.VISIBLE
            } else {
                eshare_fullscreen_ll.visibility = View.GONE
                eshare_paint_cl.visibility = View.GONE
            }
        }
    }

    private fun updateFullUI(flag: Boolean) {
        launch {
            if (flag) {
                eshare_fullscreen_iv.setImageResource(R.mipmap.eshare_fullscreen_no)
                eshare_fullscreen_iv.background = getDrawable(R.drawable.eshare_circle_with_gray)

//                eshare_fullscreen_iv.visibility = View.GONE
//                eshare_fullscreen_iv_cancel.visibility = View.VISIBLE
                eshare_fullscreen_tv.setText(R.string.eshare_fullscreen_no_text)
            } else {
                eshare_fullscreen_iv.setImageResource(R.mipmap.eshare_fullscreen)
                eshare_fullscreen_iv.background = getDrawable(R.drawable.eshare_circle_with_7ss)


//                eshare_fullscreen_iv.visibility = View.VISIBLE
//                eshare_fullscreen_iv_cancel.visibility = View.GONE
                eshare_fullscreen_tv.setText(R.string.eshare_fullscreen_text)
            }
        }
    }

    override fun onResume() {
        super.onResume()

        logI(
            "needIntentToFindDeviceAty = ${needIntentToFindDeviceAty}"
        )
//        showPlayBtnStatus(viewModel.esharePlayStatus)
        if (!eshare_mid_btn_play.isEnabled) {
            countDowndTimer()
        }

        // 判断一下画笔是否还在
        hasPaint()

        if (needIntentToFindDeviceAty
//            || !NetworkUtils.isWifiConnected()
        ) {
            disConnectDevice()
            ToastUtils.showLong(R.string.eshare_connect_fail)
            intentToFindDeviceActivity()
        }

        needIntentToFindDeviceAty = false

        viewModel.downloadSupport = false
        viewModel.uploadSupport = false
        viewModel.getIntentSwitch()

        logI(
            "EShareActivity.onResume.viewModel.esharePlayStatus=${applicationViewModel.esharePlayStatus}",
            "viewModel.currentDevice=${applicationViewModel.currentDevice}"
        )

    }

    @SuppressLint("StringFormatMatches")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        logI("onActivityResult.requestCode=${requestCode},resultCode=${resultCode}")

        if (requestCode == REQ_CODE_START_SCREEN_MIRROR) {
//            viewModel.esharePlayStatus = resultCode == RESULT_OK
//            showPlayBtnStatus(viewModel.esharePlayStatus)
            when (resultCode) {
                RESULT_OK -> {
                    logD("获取到投屏权限")
                    mScreenManager.setScreenMirrorData(
                        this,
                        requestCode,
                        resultCode,
                        data,
                        null
                    )
                }

                RESULT_CANCELED -> {
                    isFistStart = true  // 因为没有获取权限, 所以还应当认为是firstStart
                    logD("没有获取到投屏权限")
                    callBack = true
                    job?.cancel()
                    showPlayBtnStatus(false)
                }
            }
        } else if (requestCode == EshareConstants.RESULT_FIND_DEVICES_CODE && resultCode == 20) {
            val placeHolderLayerController = PlaceHolderLayerController(this)
            placeHolderLayerController.initCanvas()

            isFistStart = true
            checkAndShowNotification()

            val name = data?.getStringExtra("DEVICE_NAME") ?: "Starry"

            eshare_device_name_tv?.text = name
//            eshare_mid_play_subname?.text = getString(R.string.eshare_mid_start_submag, name)
            eshare_fullscreen_ll?.visibility = View.GONE
            user_back_btn.visibility = View.GONE
//            user_more_btn.visibility = View.GONE
            setViewDisable(eshare_disconnect_ll, true)
            setViewDisable(eshare_mid_btn_play, true)
            isConnectView = true
            showPlayBtnStatus(false)
            launch(Dispatchers.IO) {
                getDeviceName()

                mDeviceManager.setClientName(mobileName)

                EShareHeartBeatService.startService(this@EShareActivity)
            }
        } else if (requestCode == EshareConstants.RESULT_CHECK_OVERLAYS_CODE) {
            if (!Settings.canDrawOverlays(this)) {
                logI("StarryActivity.onActivityResult.checkPermission.授权失败")
                finish()
            } else {
                onStatuesRun()
            }
        } else if (requestCode == EshareConstants.RESULT_TRANSFER_FILE_CODE) {
//            if (needIntentToFindDevice) {
//                intentToFindDeviceActivity(true)
//            }
        } else if (requestCode == EshareConstants.RESULT_FIND_DEVICES_CODE){
            if (packageName.contains("cloud")){
                finish()
            }
        }
    }

    private fun setViewDisable(view: View, flag: Boolean) {
        var alpha = 0.6f
        if (flag) {
            alpha = 1.0f
        }

        view.isEnabled = flag
        view.isClickable = flag
        view.alpha = alpha

    }


    private fun checkPermissions() {
        if (ContextCompat.checkSelfPermission(
                this,
                PERMISSIONS_ESHARE[0]
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            logI("申请权限${PERMISSIONS_ESHARE[0]}...")
            requestPermissions(
                PERMISSIONS_ESHARE,
                REQUEST_EXTERNAL_ESHARE
            )
        } else {
            ToastUtils.showLong("已申请权限")
            logI("已申请权限${PERMISSIONS_ESHARE[0]}")
        }
    }

    private val REQUEST_EXTERNAL_ESHARE = 1
    private val PERMISSIONS_ESHARE = arrayOf(
        Manifest.permission.FOREGROUND_SERVICE,
        Manifest.permission.RECORD_AUDIO

    )

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            REQUEST_EXTERNAL_ESHARE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    ToastUtils.showLong("已申请权限")
                    logI("已申请权限${PERMISSIONS_ESHARE[0]}")
                } else {
                    ToastUtils.showLong("申请权限失败")
                    logI("申请权限失败${PERMISSIONS_ESHARE[0]}")
                }
            }
        }
    }


    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK && isConnectView) {
            backToHome()
            return false
        }
        return super.onKeyDown(keyCode, event)
    }

    override fun onPause() {
        super.onPause()
        callBack = true
        job?.cancel()
        TransmitFileDialogManager.dismissAllDialog()
        hasPaint()

    }


    private fun hasPaint() {
        // 判断一下画笔是否还在
        // 主要针对 oppo手机
        if (applicationViewModel.esharePlayStatus) {
            if (isOppo()) {
                lifecycleScope.launch {
                    startPaintController()
                    val sp: SharedPreferences =
                        <EMAIL>("brush", Context.MODE_PRIVATE)
                    val isShow = sp.getBoolean("isShow", true)
                    PaintController.isOpenPaint.emit(isShow)
                }
            }
        }
    }

    override fun onDestroy() {
        disConnectDevice()
        jobCastState?.cancel()
        jobreCastState?.cancel()

//        this.unregisterReceiver(mCastReceiver)

        mCastStateListener?.unregisterCallback(this)

        // 停止前台服务检测
//        bgServiceChecker.stopChecking()

        super.onDestroy()
    }


    private fun disConnectDevice() {

        eshare_fullscreen_ll?.visibility = View.GONE
        setViewDisable(eshare_disconnect_ll, false)
        setViewDisable(eshare_mid_btn_play, false)
        try {

            mScreenManager.pauseScreenMirror(this@EShareActivity)
            Thread.sleep(200)
            mScreenManager.stopScreenMirror(this@EShareActivity)
            Thread.sleep(200)
            if (mDeviceManager.isDeviceConnect) {
                mDeviceManager.disconnectDevice(mobileName)
            }
            stopHostBeat()
        } catch (e: Exception) {
            logTagI(TAG, "===调用接口异常==${e.toString()}")
            e.printStackTrace()
        }
        stopMirror()
        applicationViewModel.currentDevice = null
        applicationViewModel.esharePlayStatus = false
        eshareIsRunning = false
        stopPaintController()
        job?.cancel()

    }


    private fun stopHostBeat() {
        logTagI(TAG, "stopHostBeat")
        EShareHeartBeatService.stopService(this@EShareActivity)

    }

    private class CastReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            if (ACTION_CAST_START == action) {
            } else if (ACTION_CAST_STOP == action) {
            }
        }
    }

    private fun checkPermission(): Boolean {
        if (!Settings.canDrawOverlays(this)) {
            logI("StarryActivity.checkPermission.当前无权限，请授权")
            this.startActivityForResult(
                Intent(
                    Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    Uri.parse("package:" + this.packageName)
                ), EshareConstants.RESULT_CHECK_OVERLAYS_CODE
            )
            return false
        }
        return true
    }

    override fun onCastStateChanged(castStaus: Int) {
        /**
        //投屏失败
        castStaus=-1
        //开始投屏
        castStaus=1;
        //结束投屏
        castStaus=0;
        //继续投屏
        castStaus=2;
        //暂停投屏
        castStaus=3;
         */
        // 检查前台服务是否存活
//        AppClearUtils.startScreenNotify()

        if (castStaus == 0) {
            stopMirror()
            return
        }

        if (castStaus == -1) {
            return
        }

        if (castStaus == 1 || castStaus == 2) {
            startPaintController()
            return
        }
        stopPaintController()
    }

    private val selectFileResultLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                if (needIntentToFindDeviceAty) {
                    return@registerForActivityResult
                }
                val fileUriList = mutableListOf<Uri>()
                val clipData: ClipData? = result.data?.clipData
                if (clipData != null) {
                    for (i in 0 until clipData.itemCount) {
                        val uri = clipData.getItemAt(i).uri
                        fileUriList.add(uri)
                    }

                    val intent1 = Intent(this, TransmitFileUpLoadActivity::class.java)
                    intent1.putParcelableArrayListExtra(
                        "fileList",
                        ArrayList<Parcelable>(fileUriList)
                    )
                    startActivityForResult(intent1, EshareConstants.RESULT_TRANSFER_FILE_CODE)
                } else {
                    val uri: Uri? = result.data?.data
                    uri?.let { fileUriList.add(it) }
                    val intent1 = Intent(this, TransmitFileUpLoadActivity::class.java)
                    intent1.putParcelableArrayListExtra(
                        "fileList",
                        ArrayList<Parcelable>(fileUriList)
                    )
                    startActivityForResult(intent1, EshareConstants.RESULT_TRANSFER_FILE_CODE)
                }

            }
        }


    /**
     * 如果传文件页面存在,先不跳转
     * 不存在时候跳转
     * isTransmitFileFinished 是否是从传文件页面返回,返回后,处理情况,
     * 1如果连接失败,直接返回列表,2还是连接成功的状态,就保持在这个页面
     */
    private fun intentToFindDeviceActivity(
        mutableList: MutableList<Pair<String, String>> = mutableListOf()
    ) {
        EShareHeartBeatService.stopService(this)


        if (mutableList.isNotEmpty()) {
            val intent = Intent(this, FindDeviceActivity::class.java)
            mutableList.forEach {
                intent.putExtra(it.first, it.second)
            }
            startActivityForResult(intent, EshareConstants.RESULT_FIND_DEVICES_CODE)
        } else {
            val intent = Intent(this, FindDeviceActivity::class.java)
            startActivityForResult(intent, EshareConstants.RESULT_FIND_DEVICES_CODE)

        }
        hideProgressDialog(true)
    }

    private fun checkAndShowNotification() {
        val context = applicationContext
        val notificationPermission = arrayOf(Manifest.permission.POST_NOTIFICATIONS)

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            val manager = getSystemService(NotificationManager::class.java)
            val b = manager.areNotificationsEnabled()
            logI("IndexActivity.checkAndShowPermission.areNotificationsEnabled=$b")

            if (!b) {
                PermissionUtil.checkPermissionWithDialog(
                    context,
                    context.getString(R.string.starry_popupwindow_title),
                    context.getString(R.string.eshare_notification_permission_explain),
                    context.getString(R.string.go_setting),
                    context.getString(R.string.starry_background_start_msg_cancel)
                ) {
                    if (it != null) { //点击去设置
                        PermissionUtil.useToolsRequestPermission(
                            notificationPermission,
                            object : PermissionCallBack {
                                override fun execute() {

                                }
                            }
                        )
                    } else {//点击取消

                    }
                }
            }
        }
    }

    override fun onNetConnectedChange(isConnect: Boolean) {
        if (isConnect) {
            launch(Dispatchers.Main) {
                eshare_transmit_upload_file_cl?.background =
                    getDrawable(R.drawable.eshare_circle_color_white_5dp)
                eshare_transmit_upload_file_cl.isEnabled = true
                eshare_transmit_download_file_cl?.background =
                    getDrawable(R.drawable.eshare_circle_color_white_5dp)
                eshare_transmit_download_file_cl.isEnabled = true
            }
        } else {
            launch(Dispatchers.Main) {
                eshare_transmit_upload_file_cl?.background =
                    getDrawable(R.drawable.eshare_circle_color_transparent_white_5dp)
                eshare_transmit_upload_file_cl.isEnabled = false
                eshare_transmit_download_file_cl?.background =
                    getDrawable(R.drawable.eshare_circle_color_transparent_white_5dp)
                eshare_transmit_download_file_cl.isEnabled = false
            }
        }
    }
}