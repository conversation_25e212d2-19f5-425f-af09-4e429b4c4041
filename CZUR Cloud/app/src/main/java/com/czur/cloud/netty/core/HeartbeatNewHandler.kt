package com.czur.cloud.netty.core

import android.content.Context
import android.util.Log
import com.blankj.utilcode.util.NetworkUtils
import com.blankj.utilcode.util.Utils
import com.czur.cloud.netty.observer.NettyUtils
import com.czur.cloud.preferences.UserPreferences
import com.czur.czurutils.log.logI
import io.netty.channel.ChannelDuplexHandler
import io.netty.channel.ChannelHandlerContext
import io.netty.util.concurrent.Future
import io.netty.util.concurrent.ScheduledFuture
import java.util.concurrent.TimeUnit

/**
 * Created by 陈丰尧 on 4/6/21
 * 发送心跳包的处理
 *
 * 服务器的心跳包并不监听读写空闲,为了与之匹配, 不能使用IdleStateHandler进行监控
 * 仿照 IdleStateHandler.java 重写HeartbeatHandler
 */
private const val TAG = "HeartbeatNewHandler"

// 心跳包空闲时间(单位:秒)
const val HEART_INTERVAL_SECOND = 20L

/**
 * @param intervalSecond: 各个心跳的间隔时间 单位是秒
 */
class HeartbeatNewHandler(private val intervalSecond: Long) : ChannelDuplexHandler() {
    // 0 - none, 1 - initialized, 2 - destroyed
    private var state: Byte = 0
    private var heartTask: ScheduledFuture<*>? = null

    override fun handlerAdded(ctx: ChannelHandlerContext) {
        if (ctx.channel().isActive && ctx.channel().isRegistered) {
            initialize(ctx)
        }
    }

    override fun channelRegistered(ctx: ChannelHandlerContext) {
        if (ctx.channel().isActive) {
            initialize(ctx)
        }
        super.channelRegistered(ctx)
    }

    override fun channelActive(ctx: ChannelHandlerContext) {
        initialize(ctx)
        super.channelActive(ctx)
    }

    /**
     * 初始化
     */
    private fun initialize(ctx: ChannelHandlerContext) {
        Log.i(TAG, "心跳Handler initialize:state=${state}")
        when (state) {
            1.toByte(), 2.toByte() -> return
        }

        state = 1
        heartTask = schedule(ctx, HeartTask(ctx))

    }

    private fun schedule(ctx: ChannelHandlerContext, task: Runnable): ScheduledFuture<*> {
        return ctx.executor().schedule(task, intervalSecond, TimeUnit.SECONDS)
    }


    override fun handlerRemoved(ctx: ChannelHandlerContext?) {
        super.handlerRemoved(ctx)
        destroy()
    }

    override fun channelInactive(ctx: ChannelHandlerContext?) {
        destroy()
        super.channelInactive(ctx)
    }

    private fun destroy() {
        Log.i(TAG, "心跳Handler destroy")
        state = 2
        heartTask?.let {
            it.cancel(false)
            heartTask = null
        }
    }

    inner class HeartTask(private val ctx: ChannelHandlerContext) : Runnable {
        override fun run() {
            if (!ctx.channel().isOpen) {
                return
            }
            if (!ctx.channel().isActive) {
                return
            }
            sendPingMsg(ctx)
            heartTask = schedule(ctx, this)
        }
    }

    /**
     * 发送客户端心跳包
     */
    /**
     * 发送客户端心跳包
     */
    private fun sendPingMsg123(ctx: ChannelHandlerContext) {
//        Log.i(TAG,"发送心跳包")
        val pingMsg = makePingMsg()
        ctx.writeAndFlush(pingMsg).addListener(::checkSendResult)
    }
    private fun sendPingMsg(ctx: ChannelHandlerContext) {
//        Log.i(TAG,"发送心跳包")
        val context = Utils.getApp().applicationContext
        val instance = UserPreferences.getInstance(context)
//        if (instance.isHasAuraMate && instance.isUserLogin) {
        if (instance.isUserLogin) {
            if (ctx?.channel() != null && ctx.channel().isActive) {
                instance.heartBeatTime = System.currentTimeMillis()
                MessageProcess.sendHeartbeat(ctx)
            } else {
                if (ctx != null) {
                    ctx.close()
                }
                CZURTcpClient.getInstance().closeChannel()
//                if (instance.isHasAuraMate && instance.isUserLogin) {
                if (instance.isUserLogin) {
                    Log.i(TAG,"sendPingMsg.startNettyService")
                    startNettyService(context)
                }
            }
        }
    }

    private fun checkSendResult(future: Future<in Void>) {
        if (!future.isSuccess) {
            Log.i(TAG, "心跳包发送失败",future.cause())
        }
    }

    /**
     * 生成心跳消息
     */
    private fun makePingMsg(): CZMessageNew {
        return PingMsg()
    }

    private fun startNettyService(context: Context) {
        if (NetworkUtils.isConnected()) {
            NettyUtils.getInstance().stopNettyService();
            logI("${TAG}.startNettyStarryService.NettyService.class")
        }
    }
}