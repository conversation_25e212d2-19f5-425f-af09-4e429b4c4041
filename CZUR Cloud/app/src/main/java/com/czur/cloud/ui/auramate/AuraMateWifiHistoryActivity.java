package com.czur.cloud.ui.auramate;

import static com.blankj.utilcode.util.PermissionUtils.isGranted;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.location.LocationManager;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.adapter.AuraMateHistoryNetAdapter;
import com.czur.cloud.entity.realm.WifiHistoryEntity;
import com.czur.cloud.preferences.FirstPreferences;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.component.recyclerview.RecycleViewDivider;
import com.czur.cloud.ui.mirror.mydialog.SittingDialog;
import com.czur.cloud.util.PermissionUtil;

import java.util.List;

import io.realm.Realm;
import io.realm.RealmResults;
import io.realm.Sort;

public class AuraMateWifiHistoryActivity extends AuramateBaseActivity implements View.OnClickListener {
    private RecyclerView recyclerView;
    private ImageView imgBack, imgLock;
    private RelativeLayout rlSearchNewNet, rlCurrentNet, rlCurrentNoNet;
    private TextView tvHistoryTitle, tvCurrentName;
    private Realm realm;
    private RealmResults<WifiHistoryEntity> wifiHistoryList;
    private String ssid;
    private int GPS_REQUEST_CODE = 10;
    private boolean noNeedKey;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_aura_mate_wifi_history);
        initView();
        initData();
        initListener();
    }

    @Override
    protected boolean PCNeedFinish() {
        return  !TextUtils.isEmpty(equipmentId);
    }

    private void initView() {
        recyclerView = findViewById(R.id.rv_history_net);
        imgBack = findViewById(R.id.img_back);
        imgLock = findViewById(R.id.img_lock);
        rlSearchNewNet = findViewById(R.id.rl_search_new_net);
        rlCurrentNet = findViewById(R.id.rl_current_net);
        rlCurrentNoNet = findViewById(R.id.rl_current_no_net);
        tvCurrentName = findViewById(R.id.tv_current_name);
        tvHistoryTitle = findViewById(R.id.tv_history_title);

        recyclerView.setHasFixedSize(true);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        //添加自定义分割线：可自定义分割线高度和颜色
        recyclerView.addItemDecoration(new RecycleViewDivider(
                this, LinearLayoutManager.HORIZONTAL, SizeUtils.dp2px(13), getResources().getColor(R.color.white)));
    }

    private void initListener() {
        imgBack.setOnClickListener(this);
        rlCurrentNet.setOnClickListener(this);
        rlSearchNewNet.setOnClickListener(this);
        rlCurrentNoNet.setOnClickListener(this);
        initNetListener();
    }

    private void initData() {
        noNeedKey = getIntent().getBooleanExtra("noNeedKey", false);
        realm = Realm.getDefaultInstance();
        realm.refresh();
        wifiHistoryList = realm.where(WifiHistoryEntity.class).findAll();
        //历史列表
        if (wifiHistoryList == null || wifiHistoryList.size() == 0) {
            tvHistoryTitle.setVisibility(View.GONE);
            recyclerView.setVisibility(View.GONE);
        } else {
            tvHistoryTitle.setVisibility(View.VISIBLE);
            recyclerView.setVisibility(View.VISIBLE);
            //时间降序
            wifiHistoryList = wifiHistoryList.sort("createTime", Sort.DESCENDING);
            AuraMateHistoryNetAdapter adapter = new AuraMateHistoryNetAdapter(wifiHistoryList, this, noNeedKey);
            recyclerView.setAdapter(adapter);
        }

        if (NetworkUtils.isWifiConnected()) {
            rlCurrentNoNet.setVisibility(View.GONE);
            //获取当前wifi名称 需要适配
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (!checkGPSIsOpen()) {
                    confirmGpsDialog();
                } else {
                    checkLocationPermission();
                }
            } else {
                setCurrentWifi();
            }
        } else {
            rlCurrentNet.setVisibility(View.GONE);
            rlCurrentNoNet.setVisibility(View.VISIBLE);
        }
    }

    private void initNetListener() {
        NetworkUtils.registerNetworkStatusChangedListener(new NetworkUtils.OnNetworkStatusChangedListener() {
            @Override
            public void onDisconnected() {

            }

            @Override
            public void onConnected(NetworkUtils.NetworkType networkType) {
                rlCurrentNoNet.setVisibility(View.GONE);
                //获取当前wifi名称 需要适配
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    if (!checkGPSIsOpen()) {
                        confirmGpsDialog();
                    } else {
                        checkLocationPermission();
                    }
                } else {
                    setCurrentWifi();
                }

            }
        });
    }

    private void setCurrentWifi() {
        WifiManager wifiManager = (WifiManager) getApplicationContext().getSystemService(WIFI_SERVICE);
        WifiInfo wifiInfo = wifiManager.getConnectionInfo();
        ssid = wifiInfo.getSSID();
        if (!TextUtils.isEmpty(ssid)) {
            if (ssid.startsWith("\"")) {
                ssid = ssid.substring(1, ssid.length());
            }
            if (ssid.endsWith("\"")) {
                ssid = ssid.substring(0, ssid.length() - 1);
            }
            tvCurrentName.setText(ssid);
            rlCurrentNet.setVisibility(View.VISIBLE);
            rlCurrentNet.setBackground(getDrawable(R.drawable.btn_rec_5_bg));

            if (checkIsCurrentWifiHasPassword(ssid)) {
                imgLock.setVisibility(View.VISIBLE);
            } else {
                imgLock.setVisibility(View.GONE);
            }

        } else {
            rlCurrentNet.setVisibility(View.GONE);
            rlCurrentNoNet.setVisibility(View.VISIBLE);
            rlCurrentNoNet.setBackground(getDrawable(R.drawable.btn_rec_5_gray_bg));
        }
    }

    private void checkLocationPermission() {

        String[] permissions = {Manifest.permission.ACCESS_COARSE_LOCATION, Manifest.permission.ACCESS_FINE_LOCATION};
        boolean flag = isGranted(permissions);
        if (flag) { //权限开启状态
            setCurrentWifi();
        }else{//权限关闭状态
            PermissionUtil.checkPermissionWithDialog(
                this,
                getString(R.string.starry_popupwindow_title),
                getString(R.string.czur_permission_location),
                getString(R.string.starry_go_open_permission),
                getString(R.string.starry_background_start_msg_cancel),
                v -> {
                    if (v != null){//点击去设置
                        PermissionUtil.useToolsRequestPermission(
                                permissions,
                                this::setCurrentWifi,
                                this::cancelAction
                        );
                    }else{ // 点击取消
                        cancelAction();
                    }
                });
        }

        if (BuildConfig.IS_OVERSEAS) {
            if (FirstPreferences.getInstance(getApplicationContext()).isFirstAuraMateLocation()) {
                // googleplay for location
                showLocationAlertDailog(AuraMateWifiHistoryActivity.this);
            }
        }

    }

    private void cancelAction(){
        ActivityUtils.finishActivity(AuraMateWifiHistoryActivity.class);
    }

    private void confirmGpsDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(this, CloudCommonPopupConstants.GPS_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.gps_permission));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                Intent intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                startActivityForResult(intent, GPS_REQUEST_CODE);
                dialog.dismiss();
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                ActivityUtils.finishActivity(AuraMateWifiHistoryActivity.this);
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();
    }


    private boolean checkIsCurrentWifiHasPassword(String currentWifiSSID) {
        try {
            WifiManager wifiManager = (WifiManager) getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            // 得到当前连接的wifi热点的信息
            WifiInfo wifiInfo = wifiManager.getConnectionInfo();
            // 得到当前WifiConfiguration列表，此列表包含所有已经连过的wifi的热点信息，未连过的热点不包含在此表中
            List<WifiConfiguration> wifiConfiguration = wifiManager.getConfiguredNetworks();
            String currentSSID = wifiInfo.getSSID();
            if (currentSSID != null && currentSSID.length() > 2) {
                if (currentSSID.startsWith("\"") && currentSSID.endsWith("\"")) {
                    currentSSID = currentSSID.substring(1, currentSSID.length() - 1);
                }
                if (wifiConfiguration != null && wifiConfiguration.size() > 0) {
                    for (WifiConfiguration configuration : wifiConfiguration) {
                        if (configuration != null && configuration.status == WifiConfiguration.Status.CURRENT) {
                            String ssid = null;
                            if (!TextUtils.isEmpty(configuration.SSID)) {
                                ssid = configuration.SSID;
                                if (configuration.SSID.startsWith("\"") && configuration.SSID.endsWith("\"")) {
                                    ssid = configuration.SSID.substring(1, configuration.SSID.length() - 1);
                                }
                            }
                            if (TextUtils.isEmpty(currentSSID) || currentSSID.equalsIgnoreCase(ssid)) {
                                //KeyMgmt.NONE表示无需密码
                                return (!configuration.allowedKeyManagement.get(WifiConfiguration.KeyMgmt.NONE));
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            //do nothing
        }
        //默认为需要连接密码
        return true;
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.rl_search_new_net:
            case R.id.rl_current_no_net:
                Intent intent = new Intent(AuraMateWifiHistoryActivity.this, AuraMateSearchWifiActivity.class);
                intent.putExtra("noNeedKey", noNeedKey);
                intent.putExtra("equipmentId",equipmentId);
                ActivityUtils.startActivity(intent);
                break;
            case R.id.rl_current_net:
                Intent intent2 = new Intent(AuraMateWifiHistoryActivity.this, AuraMateWifiActivity.class);
                intent2.putExtra("ssid", ssid);
                intent2.putExtra("noNeedKey", noNeedKey);
                intent2.putExtra("equipmentId",equipmentId);
                WifiHistoryEntity wifiHistoryEntity = realm.where(WifiHistoryEntity.class).equalTo("ssid", ssid).findFirst();
                if (wifiHistoryEntity != null) {
                    intent2.putExtra("password", wifiHistoryEntity.getPassword());
                }
                ActivityUtils.startActivity(intent2);
                break;
            case R.id.img_back:
                ActivityUtils.finishActivity(this);
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        realm.close();
    }

    /**
     * 检测GPS是否打开
     *
     * @return
     */
    private boolean checkGPSIsOpen() {
        boolean isOpen;
        LocationManager locationManager = (LocationManager) this
                .getSystemService(Context.LOCATION_SERVICE);
        isOpen = locationManager.isProviderEnabled(android.location.LocationManager.GPS_PROVIDER);
        return isOpen;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == GPS_REQUEST_CODE) {
            if (!checkGPSIsOpen()) {
                confirmGpsDialog();
            } else {
                checkLocationPermission();
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == 100) {
            setCurrentWifi();
        }
    }

    // 弹出对话框，告知用户
    private void showLocationAlertDailog(Activity activity){
        SittingDialog dialog = new SittingDialog(activity, R.style.sittingDialog,
                confirm -> {
                    FirstPreferences.getInstance(getApplicationContext()).setIsFirstAuraMateLocation(false);
                });
        dialog.setOneButton(true);
        dialog.setPositiveButton(getString(R.string.czur_location_dialog_ok));
        dialog.setTitle(getString(R.string.czur_dialog_title));
        dialog.setContent(getString(R.string.czur_auramate_location_alert_msg));
        dialog.create();
        dialog.setCanceledOnTouchOutside(false);//加上这个，点击空白处不消失
        dialog.show();

    }

    @Override
    protected void onResume() {
        super.onResume();
        setCurrentWifi();
    }
}
