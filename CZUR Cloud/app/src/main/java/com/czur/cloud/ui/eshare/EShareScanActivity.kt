package com.czur.cloud.ui.eshare

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.net.wifi.WifiManager
import android.os.Bundle
import android.os.Vibrator
import android.text.format.Formatter
import android.view.View
import androidx.core.app.ActivityCompat
import cn.bingoogolapple.qrcode.core.QRCodeView
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.ToastUtils
import com.czur.cloud.R
import com.czur.cloud.ui.starry.base.StarryBaseActivity
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.czurutils.log.logI
import kotlinx.android.synthetic.main.activity_scan_zxing.zxingview
import kotlinx.android.synthetic.main.eshare_layout_top_bar.user_back_btn
import kotlinx.android.synthetic.main.eshare_layout_top_bar.user_more_btn
import kotlinx.coroutines.delay
import java.util.Timer
import kotlin.concurrent.schedule

class EShareScanActivity : StarryBaseActivity(), QRCodeView.Delegate {

    companion object {
        private const val ERROR_CODE = "errorStr"
        private const val SUCCESS_CODE = "successStr"
    }

    public override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.eshare_common_bg)
        BarUtils.setStatusBarLightMode(this, false)
        setContentView(R.layout.activity_scan_zxing)

        zxingview?.setDelegate(this)
//        zxingview?.setType(BarcodeType.ONLY_QR_CODE, null)

        user_back_btn?.setOnClickListener {
            finish()
        }
        user_more_btn?.visibility = View.GONE

        //相机权限处理
        verifyCameraPermissions()

    }

    private val PERMISSIONS_CAMERA = arrayOf(Manifest.permission.CAMERA)
    private fun verifyCameraPermissions() {
        // Check if we have write permission
        val permission = ActivityCompat.checkSelfPermission(this, Manifest.permission.CAMERA)

        if (permission != PackageManager.PERMISSION_GRANTED) {
            // We don't have permission so prompt the user
            ActivityCompat.requestPermissions(
                this, PERMISSIONS_CAMERA, 1
            )
        }
    }

    override fun onStart() {
        super.onStart()
        // 打开后置摄像头开始预览，但是并未开始识别
        zxingview?.startCamera()
        // 显示扫描框，并且延迟0.5秒后开始识别
        zxingview?.startSpotAndShowRect()

    }

    override fun onStop() {
        // 关闭摄像头预览，并且隐藏扫描框
        zxingview?.stopCamera()
        super.onStop()
    }

    override fun onResume() {
        super.onResume()
        // 打开后置摄像头开始预览，但是并未开始识别
        zxingview?.startCamera()
        // 显示扫描框，并且延迟0.5秒后开始识别
        zxingview?.startSpotAndShowRect()
    }
    
    override fun onDestroy() {
        // 销毁二维码扫描控件
        zxingview?.onDestroy()
        super.onDestroy()
    }

    private fun vibrate() {
        val vibrator = getSystemService(VIBRATOR_SERVICE) as Vibrator
        vibrator.vibrate(200)
    }

    override fun onScanQRCodeSuccess(result: String) {
        //https://czur.com/cn/app/czur?************:8000&sn=0x103001bf&ssid=CPH22A2109000003&devicename=StarryHub%E6%98%AF%E6%98%AF%E6%98%AF&pincode=JOLITSOG&port=8121&iplist=************:************&password=changer007
        logI("onScanQRCodeSuccess.result:$result")
        vibrate()
//        if (result.contains("https://czur.com/app/czur?")) {
        if (result.contains("pincode") && result.contains("iplist")) {
            //{pincode=, password=, port=8121, devicename=EShare-3ph2,
            // sn=0x3001bf, iplist=************, ssid=CZUR}
            val res = Utils.getUrlParams(result)
            val pincode = res["pincode"] ?: ""
            val device_name = res["devicename"] ?: ""
            val devicesIp  = result.split("?")[1].split(":")[0]// 设备ip

            val ssid = res["ssid"] ?: ""
            val password = res["password"] ?: ""

            logI(
                "pincode=${pincode},device_name=${device_name}," +
                        "ssid=${ssid},password=${password},")

            vibrate()

            val intent = Intent()
            intent.putExtra("DEVICE_ADRESS", devicesIp)
            intent.putExtra("DEVICE_NAME", device_name)
            intent.putExtra("DEVICE_PINCODE", pincode)
            // 设备是否在线(ip地址和手机在同一网段)
            intent.putExtra("DEVICE_SSID", ssid)
            intent.putExtra("DEVICE_PASSWORD", password)

            setResult(30, intent)

        } else {
            ToastUtils.showLong(R.string.eshare_scan_error)

        }

        // 延迟调用
        Timer().schedule(300) {
            finish()
        }
    }

    override fun onCameraAmbientBrightnessChanged(isDark: Boolean) {}

    var showTime = 0L
    var tryCount = 0
    override fun onScanQRCodeOpenCameraError() {

        //短时间内只弹出一次
        val currentTime = System.currentTimeMillis()
        if (currentTime - showTime > 500) {
            showTime = currentTime
            // 鸿蒙有可能失败,多尝试几次
            tryCount++
            if (tryCount > 30) {
                ToastUtils.showLong(R.string.can_not_open_camera)
                return
            } else {
                launch {
                    delay(3000)
                    // 打开后置摄像头开始预览，但是并未开始识别
                    zxingview?.startCamera()
                    // 显示扫描框，并且延迟0.5秒后开始识别
                    zxingview?.startSpotAndShowRect()

                    ToastUtils.showShort("打开摄像头失败,请重试")
                }

            }

//            ToastUtils.showLong(R.string.can_not_open_camera)
        }

    }

}