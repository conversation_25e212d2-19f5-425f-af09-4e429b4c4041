package com.czur.cloud.ui.starry.meeting.fragment.newmain

import android.content.pm.ActivityInfo
import android.content.res.Configuration
import androidx.fragment.app.viewModels
import com.blankj.utilcode.util.ToastUtils
import com.czur.cloud.R
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.livedatabus.LiveDataBus
import com.czur.cloud.ui.starry.meeting.base.BaseMeetingFragment
import com.czur.cloud.ui.starry.meeting.bean.vo.DisplayData
import com.czur.cloud.ui.starry.meeting.common.MeetingDisplayMode
import com.czur.cloud.ui.starry.meeting.common.UserHandler
import com.czur.cloud.ui.starry.meeting.model.MeetingModel
import com.czur.cloud.ui.starry.meeting.model.ModelManager
import com.czur.cloud.ui.starry.meeting.viewmodel.ControlBarViewModel
import com.czur.cloud.ui.starry.meeting.viewmodel.MeetingViewModel
import com.czur.czurutils.log.logI
import kotlinx.android.synthetic.main.starry_fragment_meeting_main_display.*

/**
 * 显示会议的画面
 */

class MeetingDisplayMainFragment : BaseMeetingFragment() {

    companion object {
        private const val TAG = "MeetingDisplayMainFragment"

        val instance: MeetingDisplayMainFragment by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            MeetingDisplayMainFragment()
        }
    }

    var displayMainUids = listOf<DisplayData>()

    override fun getLayoutId(): Int = R.layout.starry_fragment_meeting_main_display

    private val model: MeetingViewModel by viewModels({ requireActivity() })
    private val controlBarViewModel: ControlBarViewModel by viewModels({ requireActivity() })

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
//        logI("${TAG}.onConfigurationChanged.newConfig=${newConfig}")
        val mCurrentOrientation = resources.configuration.orientation
        if ( mCurrentOrientation == Configuration.ORIENTATION_LANDSCAPE ) {
            requireActivity().requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        }else{
            requireActivity().requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }

    }

    override fun initView() {
        super.initView()
        logI("${TAG}.initView=${MeetingDisplayMainFragment}")
        displayMainContainer?.displayMode = MeetingDisplayMode.SINGLE
        displayMainContainer?.onTopBarClickListener = {
            logI("${TAG}.onTopBarClickListener.initClick.it=${it}")
            val count = model.displayJoinedUids.value?.size ?: 0
            if (count == 2) {
                displayMainContainer?.setMainUid(it, MeetingModel.curMainId)
            }
        }

        controlBarViewModel.userIdle.observeForever {
            displayMainContainer?.setTopBarVisStatus(!it)
        }

        LiveDataBus.get()
            .with(StarryConstants.MEETING_CLOCK, String::class.java)
            .observe(this) {
                ToastUtils.showLong(R.string.starry_main_locked)
            }
    }

    override fun onResume() {
        super.onResume()
//        logI("${TAG}.onResume")
        displayMainContainer?.displayMode = MeetingDisplayMode.SINGLE

        setupView()

    }

    override fun initData() {
        super.initData()
        logI("${TAG}.initData")
        model.displayUidsMainLive.observe(this){

            if (MeetingModel.displayModeLive.value == MeetingDisplayMode.GRID) {
                return@observe
            }

            logI("${TAG}.initData.model.displayUidsMainLive.=${it}")

            if (displayMainUids != it) {
                displayMainUids = it
                logI("${TAG}.initData.model.displayMainUids=${displayMainUids}")

                setupView()

                if (MeetingModel.displayModeLive.value == MeetingDisplayMode.SINGLE) {
                    // 就剩下自己了,主屏显示
                    if (it.size < 2) {
                        displayMainContainer?.setOnlyMainUid()
                    } else {
                        displayMainContainer?.setMainUid(
                            MeetingModel.curMainId,
                            MeetingModel.curSmallId
                        )
                    }
                }
            }

            // 两人会议中-摄像头全关闭，自己显示在主窗口，屏幕分享后，结束分享后主窗和副窗口都显示自己--bug17999
            if (it.size >= 2 && !ModelManager.membersModel.selfVideoInUse){
                setupView()
                displayMainContainer?.setMainUid(
                    MeetingModel.curMainId,
                    MeetingModel.curSmallId
                )
            }

        }

        // 当前活动的人员检测
        model.activeSpeaker.observeForever {
            val memberSize = model.displayJoinedUids.value?.size ?: 0
            if (it != null &&
//                model.displayMode.value == MeetingDisplayMode.SINGLE && // 是否只有在main页面才自动跳转？
                memberSize > 2 &&
                MeetingModel.smartFocus) {
                    model.resetDisplayUidsMain(it)
            }
        }

        // 屏幕分享取消后，强制刷新一下主屏
//        MeetingModel.isForceRefeshMain.observe(this){
//            if (it){
//                if (MeetingModel.displayModeLive.value == MeetingDisplayMode.SINGLE) {
//                    logI("$TAG.isForceRefeshMain=${it}")
//                    displayMainContainer?.forceRefresh()
//                    MeetingModel.isForceRefeshMain.postValue(false)
//                    // 强制主屏横竖屏归位
//                    displayMainContainer?.forceConfigurationChanged()
//                }
//            }
//        }
    }

    private fun setupView(){
        logI("$TAG.setupView()")
        val surfaceMain = model.displayDataJoinedMapMain[MeetingModel.curMainId ?: UserHandler.czurId]?.view
        if (surfaceMain != null) {
            model.setupViewForVideo(surfaceMain, MeetingModel.curMainId)
        }

        val surfaceSmall = model.displayDataJoinedMapMain[MeetingModel.curSmallId ?: 0]?.view
        MeetingModel.curSmallId?.let {
            if (surfaceSmall != null) {
                model.setupViewForVideo(surfaceSmall, it)
            }
        }

        displayMainContainer?.refreshMap(model.displayDataJoinedMapMain)

    }

    fun mainForceRefresh() {
        setupView()
        displayMainContainer?.setMainUid(
            MeetingModel.curMainId,
            MeetingModel.curSmallId
        )
    }
}

