package com.czur.cloud.ui.auramate;

import static com.blankj.utilcode.util.PermissionUtils.isGranted;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.blankj.utilcode.constant.PermissionConstants;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.UtilsTransActivity;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.common.ShareSDKPlatforms;
import com.czur.cloud.common.ShareSDKUtils;
import com.czur.cloud.model.AuraDeviceModel;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.auramate.reportfragment.FragmentAll;
import com.czur.cloud.ui.auramate.reportfragment.FragmentDayNet;
import com.czur.cloud.ui.auramate.reportfragment.FragmentMonth;
import com.czur.cloud.ui.auramate.reportfragment.FragmentWeek;
import com.czur.cloud.ui.auramate.reportfragment.ReportUtil;
import com.czur.cloud.ui.component.dialog.SocialShareDialog;
import com.czur.cloud.ui.component.segmentview.SegmentView;
import com.czur.cloud.util.PermissionUtil;
import com.czur.cloud.util.share.FileUtil;
import com.czur.cloud.util.share.ShareContentType;
import com.czur.cloud.util.share.ShareUtils;
import com.czur.cloud.util.validator.Validator;

import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * Created by Yz on 2018/6/1.
 * Email：<EMAIL>
 */
public class AuraMateReportNewActivity extends AuramateBaseActivity implements View.OnClickListener{
    private String equipmentId1, titleName;

    private ImageView userBackBtn;
    private ImageView userShareBtn;

    private FragmentAll mFragmentAll;
    private FragmentDayNet mFragmentDay;
    private FragmentWeek mFragmentWeek;
    private FragmentMonth mFragmentMonth;

    private Fragment mFragment;
    SegmentView mSegmentView;
    private UserPreferences userPreferences;
    private SocialShareDialog socialShareDialog;

    private String shareUrl;

    public Bitmap getShareBmp() {        return shareBmp;    }

    public void setShareBmp(Bitmap shareBmp) {        this.shareBmp = shareBmp;    }

    private Bitmap shareBmp;

    private  final int REQUEST_EXTERNAL_STORAGE = 1;

    public  void verifyStoragePermissions(Activity activity) {
        // Check if we have write permission
        String[] permissions = PermissionUtil.getStoragePermission();

        boolean flag = isGranted(permissions);
        if (!flag) {
            // We don't have permission so prompt the user
            ActivityCompat.requestPermissions(activity, PermissionUtil.getStoragePermission(),
                    REQUEST_EXTERNAL_STORAGE);
        }
    }

    /**
     * 申请权限
     */
    private void requestCopyToSdPermission(boolean isShare) {
        PermissionUtils.permission(PermissionUtil.getStoragePermission())
                .rationale((activity, shouldRequest) -> {
                    showMessage(isShare ? R.string.denied_share : R.string.denied_sdcard);
                    shouldRequest.again(true);
                })
                .callback(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(@NotNull List<String> permissionsGranted) {
                    }

                    @Override
                    public void onDenied(@NotNull List<String> permissionsDeniedForever,
                                         @NotNull List<String> permissionsDenied) {
                        showMessage(isShare ? R.string.denied_share : R.string.denied_sdcard);
                    }
                })
                .theme(ScreenUtils::setFullScreen)
                .request();
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_aura_mate_report_new);

        verifyStoragePermissions(this);
        requestCopyToSdPermission(true);

        socialShareDialog = new SocialShareDialog(this, shareDialogOnClickListener);

        initComponent();
        registerEvent();

        UserPreferences userPreferences = UserPreferences.getInstance(getApplicationContext());
        titleName = getString(R.string.aura_home_text);
        List<AuraDeviceModel> devicesList = userPreferences.getAuraMateDevices();
        if (Validator.isNotEmpty(devicesList)) {
            for (AuraDeviceModel model : devicesList) {
                String aliasName = model.getShowName();
                String equipmentId1 = model.getEquipmentUID();
                if (equipmentId1.equals(equipmentId)) {
                    titleName = aliasName;
                    break;
                }
            }
        }
        titleName = (titleName == null) ? "" : titleName;

    }

    @Override
    public boolean PCNeedFinish() {
        return false;
    }

    private void initComponent() {
        mSegmentView = findViewById(R.id.segmentview);

        userBackBtn = (ImageView) findViewById(R.id.user_back_btn);

        userShareBtn = (ImageView) findViewById(R.id.user_share_btn);
        TextView userTitle = (TextView) findViewById(R.id.user_title);

        userTitle = (TextView) findViewById(R.id.user_title);
        userTitle.setText(R.string.aura_mate_report);

        //取得启动该Activity的Intent对象
        //取出Intent中附加的数据
        equipmentId1 = getIntent().getStringExtra("equipmentId");
        String report_id = getIntent().getStringExtra("reportId");
        String type = getIntent().getStringExtra("type");

        /////// 分段选择器  ///////
        mSegmentView = findViewById(R.id.segmentview);

        if (Validator.isNotEmpty(report_id) && Validator.isNotEmpty(type)){
            setFragment(Integer.parseInt(type), Integer.parseInt(report_id));
        }else {
            setDefaultFragment();
        }
        mSegmentView.setOnSegmentViewClickListener((view, position) -> {
            setFragment(position);
        });
    }

    private void setDefaultFragment() {
        setFragment(0);
    }

    public void setFragment(int index) {        setFragment(index, -1);    }

    //type：0为普通报告 1为日报 2为周报 3为月报
    //dateStr:日报-2020.11.26;周报-2020.11.23-11.29;月报-2020.11
    public void setFragment(int index, int reportId) {
        Bundle bundle = new Bundle();
        bundle.putString("equipmentId",equipmentId1);
        bundle.putString("titleName",titleName);
        String sReportId="";
        if (reportId >= 0){
            sReportId = reportId + "";
        }
        bundle.putString("reportId",sReportId);
        FragmentManager fm = getSupportFragmentManager();
        FragmentTransaction ft = fm.beginTransaction();
        userShareBtn.setVisibility(View.VISIBLE);
        if(mFragment != null){
            getSupportFragmentManager().beginTransaction().remove(mFragment).commit();
        }
        switch (index) {
            case 0: //"全部"
                if (mFragmentAll == null) {
                    mFragmentAll = new FragmentAll();
                }
                bundle.putString("title","全部");//这里的values就是我们要传的值
                bundle.putString("type","0");//0为普通报告 1为日报 2为周报 3为月报
                mFragment = mFragmentAll;
                userShareBtn.setVisibility(View.GONE);
                break;
            case 1: //"日"
                if (mFragmentDay == null) {
                    mFragmentDay = new FragmentDayNet();
                }
                bundle.putString("title","日报");//这里的values就是我们要传的值
                bundle.putString("type","1");//0为普通报告 1为日报 2为周报 3为月报
                mFragment = mFragmentDay;
                break;
            case 2: //"周"
                if (mFragmentWeek == null) {
                    mFragmentWeek = new FragmentWeek();
                }
                bundle.putString("title","周报");//这里的values就是我们要传的值
                bundle.putString("type","2");//0为普通报告 1为日报 2为周报 3为月报
                mFragment = mFragmentWeek;
                break;
            case 3: //"月"
                if (mFragmentMonth == null) {
                    mFragmentMonth = new FragmentMonth();
                }
                bundle.putString("title","月报");//这里的values就是我们要传的值
                bundle.putString("type","3");//0为普通报告 1为日报 2为周报 3为月报
                mFragment = mFragmentMonth;
                break;
        }
        mFragment.setArguments(bundle);
        ft.replace(R.id.layFragme, mFragment).commitAllowingStateLoss();
        mSegmentView.setSelect(index);
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.user_back_btn) {
            finish();
        }else if (v.getId() == R.id.user_share_btn) {
            //
        }
    }

    private void registerEvent() {
        userBackBtn.setOnClickListener(this);
        userShareBtn.setOnClickListener(this);
        setNetListener();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    /**
     * 截取scrollview的屏幕
     * @param scrollView
     * @return
     */
    public Bitmap getBitmapByView(ScrollView scrollView) {
        int h = 0;
        Bitmap bitmap = null;
        // 获取listView实际高度
        for (int i = 0; i < scrollView.getChildCount(); i++) {
            h += scrollView.getChildAt(i).getHeight();
            scrollView.getChildAt(i).setBackgroundResource(R.color.white);
        }

        // 创建对应大小的bitmap
        bitmap = Bitmap.createBitmap(scrollView.getWidth(), h,
                Bitmap.Config.ARGB_8888);
        final Canvas canvas = new Canvas(bitmap);
        scrollView.draw(canvas);
        // 测试输出
        FileOutputStream out = null;
        try {
            String name = shareUrl;
            out = new FileOutputStream(name);
            Log.i("Jason", name);

        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        try {
            if (null != out) {
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, out);
                out.flush();
                out.close();
            }
        } catch (IOException e) {
        }
        return bitmap;
    }

    /**
     * @des: 申请权限
     * @params:
     * @return:
     */

    public void requestCopyToSdPermission() {
//        String sdPicPath = this.getExternalFilesDir("apk").getAbsolutePath() + File.separator + "/report_test.png";;
        String sdPicPath = CZURConstants.SD_PATH + "/report_test.png";;
        PermissionUtils.permission(PermissionUtil.getStoragePermission())
                .rationale(new PermissionUtils.OnRationaleListener() {
                    @Override
                    public void rationale(UtilsTransActivity activity, ShouldRequest shouldRequest) {
                        showMessage(R.string.denied_sdcard);
                        shouldRequest.again(true);
                    }
                })
                .callback(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(List<String> permissionsGranted) {
//                        socialShareDialog.show();
                        share(sdPicPath);
                    }

                    @Override
                    public void onDenied(List<String> permissionsDeniedForever,
                                         List<String> permissionsDenied) {
                        showMessage(R.string.denied_sdcard);
                    }
                })
                .theme(new PermissionUtils.ThemeCallback() {
                    @Override
                    public void onActivityCreate(Activity activity) {
                        com.blankj.utilcode.util.ScreenUtils.setFullScreen(activity);
                    }
                })
                .request();
    }

    /**
     * @des: 分享
     * @params:
     * @return:
     */
    private static final int SHARE_SUCCESS_CODE = 666;
    private void share(String sdPicPath) {
        new ShareUtils.Builder(this)
                .setOnActivityResult(SHARE_SUCCESS_CODE)
                // 指定分享的文件类型
                .setContentType(ShareContentType.IMAGE)
                // 设置要分享的文件 Uri
                .setShareFileUri(FileUtil.getFileUri(this, ShareContentType.FILE, new File(sdPicPath)))
                // 设置分享选择器的标题
                .setTitle(getString(R.string.share_to))
                .build()
                // 发起分享
                .shareBySystem();
    }

    /**
     * 分享dialog监听
     */
    private SocialShareDialog.ShareDialogOnClickListener shareDialogOnClickListener = new SocialShareDialog.ShareDialogOnClickListener() {
        @Override
        public void onShareItemClick(int viewId) {
            Context cnt = AuraMateReportNewActivity.this;
            boolean isInstall = false;
            switch (viewId) {
                case R.id.weixin_share:
                    isInstall = ReportUtil.isWeixinInstalled(cnt);
                    if (isInstall) {
                        showShare(cnt, ShareSDKPlatforms.WECHAT.name(), shareUrl, shareBmp);
                    }else{
                        showMessage(R.string.share_not_install_wechat);
                    }
                    socialShareDialog.dismiss();
                    break;

                case R.id.friend_share:
                    isInstall = ReportUtil.isWeixinInstalled(cnt);
                    if (isInstall) {
                        showShare(cnt, ShareSDKPlatforms.WECHAT_MOMENTS.name(), shareUrl, shareBmp);
                    }else{
                        showMessage(R.string.share_not_install_wechat);
                    }
                    socialShareDialog.dismiss();
                    break;

                case R.id.qq_share:
                    isInstall = ReportUtil.isQQClientInstalled(cnt);
                    if (isInstall) {
                        showShare(cnt, ShareSDKPlatforms.QQ.name(), shareUrl, shareBmp);
                    }else{
                        showMessage(R.string.share_not_install_qq);
                    }
                    socialShareDialog.dismiss();
                    break;

                case R.id.qq_zone_share:
                    isInstall = ReportUtil.isQQClientInstalled(cnt);
                    if (isInstall) {
                        showShare(cnt, ShareSDKPlatforms.QZONE.name(), shareUrl, shareBmp);
                    }else{
                        showMessage(R.string.share_not_install_qq);
                    }
                    socialShareDialog.dismiss();
                    break;

                case R.id.weibo_share:
                    isInstall = ReportUtil.isWeiboInstalled(cnt);
                    if (isInstall) {
                        showShare(cnt, ShareSDKPlatforms.WEIBO.name(), shareUrl, shareBmp);
                    }else{
                        showMessage(R.string.share_not_install_sinaweibo);
                    }
                    socialShareDialog.dismiss();
                    break;
                case R.id.share_dialog_cancel_btn:
                    socialShareDialog.dismiss();
                default:
                    break;
            }
        }
    };

    /**
     * 得到分享图片接口
     */
    public void getShareImageUrl() {
        shareUrl = this.getExternalFilesDir("apk").getAbsolutePath() + File.separator + "/report_test.png";
        socialShareDialog.show();
    }

    public String getShareUrl() {        return shareUrl;    }

    public void setShareUrl(String shareUrl) {        this.shareUrl = shareUrl;    }

    /**
     * 调用ShareSDK执行分享
     * @param context
     * @param platformToShare 指定直接分享平台名称（一旦设置了平台名称，则九宫格将不会显示）
     */
    private void showShare(Context context, String platformToShare, String shareUrl, Bitmap shareBmp){
        ShareSDKUtils.INSTANCE.showShare(context, platformToShare, shareUrl, shareBmp);
    }
}
