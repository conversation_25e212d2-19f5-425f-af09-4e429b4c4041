package com.czur.cloud.ui.starry.meeting.baselib.adapter

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.os.Build
import android.text.Html
import android.text.Html.FROM_HTML_MODE_LEGACY
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.collection.SparseArrayCompat
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import com.czur.cloud.R
import com.czur.cloud.ui.starry.meeting.widget.HeadImageView

/**
 * Created by 陈丰尧 on 12/31/20
 * RecyclerView的ViewHolder
 */
class BaseVH(resLayout: Int, parent: ViewGroup) :
    RecyclerView.ViewHolder(
        LayoutInflater.from(parent.context)
            .inflate(resLayout, parent, false)
    ) {
    val context: Context = parent.context
    private val views = SparseArrayCompat<View>()
    private var lastViewId = View.NO_ID

    // item 中有几个child
    val childCount: Int
        get() = when (itemView) {
            is ViewGroup -> (itemView as ViewGroup).childCount
            else -> 0
        }

    fun <T : View> getView(viewId: Int): T {
        if (viewId == View.NO_ID) {
            throw IllegalArgumentException("请先调用getView方法,或其他set方法指定id")
        }
        lastViewId = viewId
        var view = views[viewId]
        view?.let {
            return view as T
        } ?: run {
            view = itemView.findViewById(viewId)
            views.put(viewId, view!!)
            return view as T
        }
    }

    fun setText(text: String, viewId: Int = lastViewId): BaseVH {
        val textView: TextView = getView(viewId)
        textView.text = text
        return this
    }

    fun setHtmlText(htmlText: String, viewId: Int = lastViewId): BaseVH {
        val textView: TextView = getView(viewId)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            textView.text = Html.fromHtml(htmlText, FROM_HTML_MODE_LEGACY)
        }
        return this
    }

    fun setTextColor(colorStr: String, viewId: Int = lastViewId): BaseVH {
        val textView: TextView = getView(viewId)
        textView.setTextColor(Color.parseColor(colorStr))
        return this
    }

    fun setTextSize(unit: Int, size: Float, viewId: Int = lastViewId): BaseVH {
        val textView: TextView = getView(viewId)
        textView.setTextSize(unit, size)
        return this
    }

    fun setImgResource(imgResource: Int, viewId: Int = lastViewId): BaseVH {
        val imageView: ImageView = getView(viewId)
        imageView.setImageResource(imgResource)
        return this
    }

    fun setImgResourceCondition(
        trueRes: Int,
        falseRes: Int,
        condition: Boolean,
        viewId: Int = lastViewId
    ): BaseVH {
        val imageView: ImageView = getView(viewId)
        if (condition) {
            imageView.setImageResource(trueRes)
        } else {
            imageView.setImageResource(falseRes)
        }
        return this
    }

    /**
     * 通过url设置图片,通过Glide进行图片请求
     * @param url       要请求图片的网址
     * @param errorRes  请求错误时显示的图片
     * @param viewId:   imageView的ID
     */
    fun setImgUrlOrText(
        url: String?,
        text: String,
        viewId: Int = lastViewId
    ): BaseVH {
        val imageView = getView(viewId) as HeadImageView
        // 1. 先设置默认文字
        if (url == null || url == "" || url == "null") {
            imageView.text = text
            // 没有图片URL 就直接return了
            return this
        }
        // 2. 使用Glide加载图片
        Glide.with(imageView)
            .load(url)
            .placeholder(R.mipmap.starry_home_user_def)
            .apply(RequestOptions()
                .transform(CenterCrop(),
                    RoundedCorners(100)))
            .listener(object : RequestListener<Drawable> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Drawable>?,
                    isFirstResource: Boolean
                ): Boolean {
                    // 加载失败
                    return true
                }

                override fun onResourceReady(
                    resource: Drawable?,
                    model: Any?,
                    target: Target<Drawable>?,
                    dataSource: DataSource?,
                    isFirstResource: Boolean
                ): Boolean {
                    return false
                }
            })
            .into(imageView)

        return this
    }

    fun setImgUrl(
        url: String?,
        defRes: Int,
        errorRes: Int = defRes,
        viewId: Int = lastViewId
    ): BaseVH {
        val imageView: ImageView = getView(viewId)
        imageView.setImageResource(defRes)

        Glide.with(imageView)
            .load(url)
            .apply(RequestOptions()
                .transform(CenterCrop(),
                    RoundedCorners(100)))
            .error(errorRes)
            .into(imageView)


        return this
    }


    fun setImgDrawable(drawable: Drawable?, viewId: Int = lastViewId): BaseVH {
        val imageView: ImageView = getView(viewId)
        imageView.setImageDrawable(drawable)
        return this
    }

    fun setImgLevel(level: Int, viewId: Int = lastViewId): BaseVH {
        val imageView: ImageView = getView(viewId)
        val drawable = imageView.drawable
        if (drawable != null) {
            drawable.level = level
        }
        return this
    }

    fun visible(visible: Boolean, viewId: Int = lastViewId): BaseVH {
        val view: View = getView(viewId)
        view.visibility = if (visible) View.VISIBLE else View.GONE
        return this
    }

    fun visibleNotGone(visible: Boolean, viewId: Int = lastViewId): BaseVH {
        val view: View = getView(viewId)
        view.visibility = if (visible) View.VISIBLE else View.INVISIBLE
        return this
    }

    fun enable(enable: Boolean, viewId: Int = lastViewId): BaseVH {
        val view: View = getView(viewId)
        view.isEnabled = enable
        return this
    }

    /**
     * 设置背景颜色
     */
    fun setBgColor(color: Int, viewId: Int = lastViewId): BaseVH {
        val view: View = getView(viewId)
        view.setBackgroundColor(color)
        return this
    }

    /**
     * 设置通明度
     */
    fun setAlpha(alpha: Float, viewId: Int = lastViewId): BaseVH {
        val view: View = getView(viewId)
        view.alpha = alpha
        return this
    }

    /**
     * 批量设置通明度
     */
    fun setAlphaBatch(alpha: Float, vararg viewIds: Int): BaseVH {
        viewIds.forEach {
            setAlpha(alpha, it)
        }
        return this
    }

    fun setCornersBg(radius: Float, color: Int, viewId: Int = lastViewId): BaseVH {
        val view: View = getView(viewId)
        val shapeDrawable = getShapeDrawable(color, radius)
        view.background = shapeDrawable
        return this
    }

    fun setTintColor(color: Int, viewId: Int = lastViewId): BaseVH {
        val imageView: ImageView = getView(viewId)
        imageView.imageTintList = ColorStateList.valueOf(color)
        return this
    }


    /**
     * 画边框方法
     * @param color      背景色
     * @param radius     圆角
     * @param stockWidth 边框宽度
     * @param stockColor 边框颜色
     * @param dashWidth  边框线间隔
     * @param dashGap    边框线长度
     * @return
     */
    fun getShapeDrawable(
        color: Int,
        radius: Float,
        stockWidth: Int = 0,
        stockColor: Int = Color.parseColor("#00000000"),
        dashWidth: Float = 0F,
        dashGap: Float = 0F
    ): GradientDrawable {
        val gradientDrawable = GradientDrawable()
        gradientDrawable.cornerRadius = radius
        gradientDrawable.setColor(color)
        gradientDrawable.setStroke(stockWidth, stockColor, dashWidth, dashGap)
        return gradientDrawable

    }
}