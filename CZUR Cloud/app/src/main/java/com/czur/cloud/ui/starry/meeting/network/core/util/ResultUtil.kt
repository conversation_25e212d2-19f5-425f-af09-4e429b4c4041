package com.czur.cloud.ui.starry.meeting.network.core.util

import com.czur.cloud.common.ChangerTimestampTypeAdapter
import com.czur.cloud.ui.starry.meeting.baselib.utils.*
import com.czur.cloud.ui.starry.meeting.network.core.MiaoHttpEntity
import com.czur.cloud.ui.starry.meeting.network.core.common.ResCode.RESULT_CODE_NO_INTERNET
import com.czur.cloud.ui.starry.meeting.network.core.common.ResCode.RESULT_CODE_NO_NET_CONNECT
import com.czur.cloud.ui.starry.meeting.network.core.common.ResCode.RESULT_CODE_SERVER_INTERNAL_ERROR
import com.czur.cloud.ui.starry.meeting.network.core.common.ResCode.RESULT_CODE_SUCCESS
import com.czur.czurutils.log.logD
import com.czur.czurutils.log.logE
import com.czur.czurutils.log.logI
import com.czur.czurutils.log.logV
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.JsonSyntaxException
import org.json.JSONObject
import java.io.IOException
import java.lang.reflect.Type
import java.sql.Timestamp

/**
 * Created by 陈丰尧 on 2021/7/16
 */
private const val TAG = "NetResultUtil"

private const val FORMATTER = "yyyy-MM-dd HH:mm:ss"
private const val FORMATTER_WITHOUT_SECOND = "yyyy-MM-dd HH:mm"
private const val DATA_CODE = "code"
private const val DATA_MSG = "msg"
private const val DATA_BODY = "data" //key 为data

private const val DAYA_BODY_BODY = "body" // key 为body(获取osstoken时)

private val gson by lazy { Gson() }

private val gsonWithDate = GsonBuilder().setDateFormat(FORMATTER)
    .registerTypeAdapter(Timestamp::class.java, ChangerTimestampTypeAdapter()).create()

private val gsonWithoutSecond =
    GsonBuilder().setDateFormat(FORMATTER_WITHOUT_SECOND).registerTypeAdapter(
        Timestamp::class.java, ChangerTimestampTypeAdapter()
    ).create()


fun <T> makeNetWorkErrorEntity(ioException: IOException): MiaoHttpEntity<T?> {
    logE("网络错误${ioException.toString()}")
    val result = MiaoHttpEntity<T?>()
    // 检查网络连接状态
    if (isNetworkConnected()) {
        // 网络连接但是没有网, 有可能是网络波动
        logV("连接无网")
        result.code = RESULT_CODE_NO_INTERNET
        result.msg = "无法访问互联网"
    } else {
        logV("没有网络连接")
        result.code = RESULT_CODE_NO_NET_CONNECT
        result.msg = "没有连接任何网络"
    }
    return result
}

fun <T> makeServerErrorEntity(msg: String): MiaoHttpEntity<T?> {
    logD("服务器错误:${msg}")
    val result = MiaoHttpEntity<T?>()

    result.code = RESULT_CODE_SERVER_INTERNAL_ERROR
    result.msg = msg
    return result
}

/**
 * 请求成功, 但是没有 response body
 */
@JvmOverloads
fun <T> makeEmptyEntity(
    code: Int = RESULT_CODE_SUCCESS,
    msg: String = "Success"
): MiaoHttpEntity<T?> {
    // 请求成功, 但是没有body
    val result = MiaoHttpEntity<T?>()
    result.code = code
    result.msg = msg
    return result
}

fun <T> makeEntity(json: String, type: Type): MiaoHttpEntity<T?> {
//    logV("makeEntity type:$type")

    val jsonObject = JSONObject(json)
    val code = jsonObject.getInt(DATA_CODE)
    val msg = jsonObject.getString(DATA_MSG, "")

    val entity = MiaoHttpEntity<T?>()
    entity.code = code
    entity.msg = msg

    if (type == Unit::class.java || type == Void::class.java){
//        logV("不需要解析")
        return entity
    }


    val keyData: String = when {
        jsonObject.has(DATA_BODY) -> {
            DATA_BODY
        }
        jsonObject.has(DAYA_BODY_BODY) -> {
            // 给获取oss token使用的, 它的key和其他的不一样
            DAYA_BODY_BODY
        }
        else -> {
            logI(json, "Server返回的数据格式是新的")
            return makeEmptyEntity(code, msg)
        }
    }

    val bodyJson = jsonObject.getString(keyData)

    if (bodyJson.isBlank() || bodyJson.equals("null", true)) {
        return makeEmptyEntity(code, msg)
    } else if (bodyJson.isJson()) {
        val t: T = try {
            gson.fromJson(bodyJson, type)
        } catch (e: JsonSyntaxException) {
            logV("应该使用 DateJsonDeserializer 进行解析:${type}")
            try {
                gsonWithDate.fromJson(bodyJson, type)
            } catch (e: JsonSyntaxException) {
                gsonWithoutSecond.fromJson(bodyJson, type)
            }
        }
        entity.setBody(t)
    } else if (bodyJson.isJsonArr()) {
        val list: List<T?> = gsonWithDate.fromJson(bodyJson, type)
        entity.setBodyList(list)
    } else {
        // 基本数据类型
        when (type) {
            Int::class.java, java.lang.Integer::class.java -> entity.body = bodyJson.toInt() as T
            Boolean::class.java, java.lang.Boolean::class.java -> entity.body =
                bodyJson.toBoolean() as T
            Long::class.java, java.lang.Long::class.java -> entity.body = bodyJson.toLong() as T
            Float::class.java, java.lang.Float::class.java -> entity.body = bodyJson.toFloat() as T
            Double::class.java, java.lang.Double::class.java -> entity.body =
                bodyJson.toDouble() as T
            String::class.java -> entity.body = bodyJson as T
            else -> {
                logV("未知的数据类型:${type}")
                entity.body = bodyJson as T
            }
        }
    }
    return entity
}

private fun JSONObject.getString(key: String, def: String): String {
    return if (has(key)) getString(key) else def
}

private fun String.isJson(): Boolean = startsWith("{")
private fun String.isJsonArr(): Boolean = startsWith("[")


