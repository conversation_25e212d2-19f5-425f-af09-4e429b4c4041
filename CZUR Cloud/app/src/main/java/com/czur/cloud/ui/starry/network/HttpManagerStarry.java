package com.czur.cloud.ui.starry.network;

import com.czur.cloud.BuildConfig;
import com.czur.cloud.network.HttpServices;

public class HttpManagerStarry {

    private HttpManagerStarry() {
    }

    public static HttpManagerStarry getInstance() {
        return SingletonHolder.instance;
    }

    private static class SingletonHolder {
        protected static final HttpManagerStarry instance = new HttpManagerStarry();
    }

    public StarryHttpServices request() {
        return StarryHttpManager.getInstance().create(StarryHttpServices.class, BuildConfig.BASE_STARRY_URL, true);
    }

}
