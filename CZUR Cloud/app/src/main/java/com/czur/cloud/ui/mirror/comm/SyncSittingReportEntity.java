package com.czur.cloud.ui.mirror.comm;

/**
 * Created by <PERSON> on 2021-03-05.
 */
public class SyncSittingReportEntity extends SyncSittingReportServerEntity {

    private long happyScore;      //
    private long motionlessRemindCnt;     //
    private int remindCnt;    //
    // "remindCnt": 0,
    //"happyScore": 48,
    //"motionlessRemindCnt": 3,

    public long getHappyScore() {
        return happyScore;
    }

    public void setHappyScore(long happyScore) {
        this.happyScore = happyScore;
    }

    public long getMotionlessRemindCnt() {
        return motionlessRemindCnt;
    }

    public void setMotionlessRemindCnt(long motionlessRemindCnt) {
        this.motionlessRemindCnt = motionlessRemindCnt;
    }

    public int getRemindCnt() {
        return remindCnt;
    }

    public void setRemindCnt(int remindCnt) {
        this.remindCnt = remindCnt;
    }

}
