package com.czur.cloud.netty.bean;

import com.google.gson.Gson;


public class CZTcpRequestMessage extends CZMessage{

    public static CZTcpRequestMessage parseMessage(String message){
//        ObjectMapper mapper = new ObjectMapper();
//        CZTcpRequestMessage obj = null;
//        try {
//            obj = mapper.readValue(message,CZTcpRequestMessage.class);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        return obj;
        return new Gson().fromJson(message, CZTcpRequestMessage.class);
    }

    @Override
    public String toString() {
        return "CZTcpRequestMessage{" +
                "token='" + token + '\'' +
                ", appid='" + appid + '\'' +
                ", os='" + os + '\'' +
                ", udid='" + udid + '\'' +
                ", userid='" + userid + '\'' +
                ", device='" + device + '\'' +
                ", type='" + type + '\'' +
                ", body=" + body +
                '}';
    }
}
