package com.czur.cloud.model;

public class AuraMateColorModel {
    private String id;
    private String url;
    private String ossKey;
    private Long orgFileSize;
    private Long fileSize;
    private String ossOrgKey;
    private String ossOrgKeyUrl;
    private String ossSmallKey;
    private String ossSmallKeyUrl;
    private String ossMiddleKeyUrl;

    private Long seqNum;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getOssKey() {
        return ossKey;
    }

    public void setOssKey(String ossKey) {
        this.ossKey = ossKey;
    }

    public Long getOrgFileSize() {
        return orgFileSize;
    }

    public void setOrgFileSize(Long orgFileSize) {
        this.orgFileSize = orgFileSize;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getOssOrgKey() {
        return ossOrgKey;
    }

    public void setOssOrgKey(String ossOrgKey) {
        this.ossOrgKey = ossOrgKey;
    }

    public String getOssOrgKeyUrl() {
        return ossOrgKeyUrl;
    }

    public void setOssOrgKeyUrl(String ossOrgKeyUrl) {
        this.ossOrgKeyUrl = ossOrgKeyUrl;
    }

    public String getOssSmallKey() {
        return ossSmallKey;
    }

    public void setOssSmallKey(String ossSmallKey) {
        this.ossSmallKey = ossSmallKey;
    }

    public String getOssSmallKeyUrl() {
        return ossSmallKeyUrl;
    }

    public void setOssSmallKeyUrl(String ossSmallKeyUrl) {
        this.ossSmallKeyUrl = ossSmallKeyUrl;
    }

    public String getOssMiddleKeyUrl() {
        return ossMiddleKeyUrl;
    }

    public void setOssMiddleKeyUrl(String ossMiddleKeyUrl) {
        this.ossMiddleKeyUrl = ossMiddleKeyUrl;
    }

    public Long getSeqNum() {
        return seqNum;
    }

    public void setSeqNum(Long seqNum) {
        this.seqNum = seqNum;
    }







}
