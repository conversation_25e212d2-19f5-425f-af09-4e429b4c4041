package com.czur.cloud.ui.starry.frgment

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.NetworkUtils
import com.blankj.utilcode.util.ToastUtils
import com.czur.cloud.R
import com.czur.cloud.event.BaseEvent
import com.czur.cloud.event.EventType
import com.czur.cloud.event.StarryCheckMeetingListEvent
import com.czur.cloud.preferences.StarryPreferences
import com.czur.cloud.ui.eshare.ESPermissionUtils
import com.czur.cloud.ui.starry.activity.StarryActivity
import com.czur.cloud.ui.starry.activity.StarryMeetingDetailActivity
import com.czur.cloud.ui.starry.adapter.StarryRecentlyAdapter
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.livedatabus.LiveDataBus
import com.czur.cloud.ui.starry.meeting.MeetingMainActivity
import com.czur.cloud.ui.starry.meeting.base.BaseFragment
import com.czur.cloud.ui.starry.meeting.baselib.utils.getColor
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.meeting.common.CAM_AUDIO_STATUS_CLOSE
import com.czur.cloud.ui.starry.meeting.common.CAM_AUDIO_STATUS_OPEN
import com.czur.cloud.ui.starry.meeting.dialog.StarryCommonPopup
import com.czur.cloud.ui.starry.meeting.model.MeetingModel
import com.czur.cloud.ui.starry.meeting.model.ModelManager
import com.czur.cloud.ui.starry.model.StarryCallInModel
import com.czur.cloud.ui.starry.model.StarryCallRecordModel
import com.czur.cloud.ui.starry.utils.singleClick
import com.czur.cloud.ui.starry.viewmodel.RecentlyViewModel
import com.czur.cloud.ui.starry.viewmodel.StarryViewModel
import com.czur.cloud.util.AppClearUtils
import com.czur.czurutils.log.logI
import com.scwang.smartrefresh.layout.listener.OnLoadMoreListener
import com.scwang.smartrefresh.layout.listener.OnRefreshListener
import kotlinx.android.synthetic.main.layout_no_network.tv_click_refresh
import kotlinx.android.synthetic.main.starry_fragment_recently.*
import kotlinx.android.synthetic.main.starry_no_network_layout.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.*
import kotlin.concurrent.schedule

class RecentlyFragment : BaseFragment() {

    override fun getLayoutId(): Int = R.layout.starry_fragment_recently

    private val viewModel by lazy {
        ViewModelProvider(
            StarryActivity.mainActivity ?: requireActivity()
        )[RecentlyViewModel::class.java]
    }
    private val starryViewModel by lazy {
        ViewModelProvider(StarryActivity.mainActivity ?: this).get(StarryViewModel::class.java)
    }

    private val mAdapter: StarryRecentlyAdapter? by lazy {
        StarryRecentlyAdapter()
    }

    private var clearHistoryPopup: StarryCommonPopup? = null

    companion object {
        @JvmStatic
        fun newInstance(device: String?): RecentlyFragment {
            val auraMateFragment = RecentlyFragment()
            val bundle = Bundle()
            bundle.putString("device", device)
            auraMateFragment.arguments = bundle
            return auraMateFragment
        }

    }

    init {
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: BaseEvent) {
        when (event.eventType) {

            EventType.STARRY_MEETING_CMD_CHECK_MEETING_LIST -> {
                val newMeetingListEvent = event as StarryCheckMeetingListEvent
                val dataBean = newMeetingListEvent.params
//                logI(
//                    "RecentlyFragment.STARRY_MEETING_CMD_CHECK_MEETING_LIST",
//                    "dataBean=${dataBean}"
//                )
                viewModel.listDoingMeeting.postValue(dataBean)

                viewModel.getCallRecordsRefresh()
                starryViewModel.showMeetingRemindDialog()
            }

            EventType.STARRY_RECENTLY_DEL -> {
                // 获取服务器数据 查询最近通话记录列表
                viewModel.getCallRecordsRefresh()
                starryViewModel.showMeetingRemindDialog()
            }

//            // PC接听，发送该指令
//            EventType.STARRY_MEETING_CMD_STOP_CALL,
//            EventType.STARRY_CLOSE_MEETING,
            EventType.STARRY_MEETING_CMD_REMOVE -> {
                // 从服务器获取最新数据
                viewModel.getDingMeetingAndCallRecords()
            }

            EventType.STARRY_DEVICE_ONLINE -> {
                viewModel.getDingMeetingAndCallRecords()
                starryViewModel.redpointRead()
            }
            else -> {
            }
        }
    }

    override fun initView() {
        Log.i("RecentlyFragment", "initView")

        // 本来就在最近tab，自然结束会议，也不显示小红点
        MeetingModel.isCancelCallRecordsFlag = true

        showProgressDialog()
        Timer().schedule(2000) {
            hideProgressDialog()
        }

        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }

//        starry_msg_nodata?.visibility = View.GONE
        recently_bottom_msg_coount?.visibility = View.GONE

        //线性布局
        val linearLayoutManager = LinearLayoutManager(activity)
        linearLayoutManager.orientation = LinearLayoutManager.VERTICAL

        mAdapter?.setOnItemPickListener(object : StarryRecentlyAdapter.OnItemPickListener {
            override fun onItemPick(position: Int, model: StarryCallRecordModel) {
                logI("RecentlyFragment.onItemPick.position=${position}")
                if (starryViewModel.clickNoNetwork()) {
                    return
                }

                if (position > viewModel.listRecently.value?.size ?: 0) {
                    return
                }

                val title = model.meetingName
                val id = model.id
                val isPCEnter = model.isPCEnter
                model.status.toInt()

                val intent = Intent(activity, StarryMeetingDetailActivity::class.java)
                intent.putExtra(StarryConstants.STARRY_MEETING_TITLE, title)
                intent.putExtra(StarryConstants.STARRY_MEETING_ID, id)
                intent.putExtra(StarryConstants.STARRY_MEETING_ISPCENTER, isPCEnter)
                if (MeetingModel.meetingActivityIsRunning) {
                    logI("RecentlyFragment.MeetingAty没有结束")
                    launch {
                        // 手机性能较差时, 会有页面没能及时销毁的情况
                        // 此时稍等一下, 再启动
                        delay(500L)
                        ActivityUtils.startActivity(intent)
                    }
                } else {
                    ActivityUtils.startActivity(intent)
                }
            }

            override fun onItemButtonClick(position: Int, model: StarryCallRecordModel) {
                logI("RecentlyFragment.onItemButtonClick.position=${position}")

                // 未接-0 //已接-1 //拒接-2 //转接-3 //占线-4 //退出-5 //被移除-6
                val memberStatus = model.status.toString()
                // 当被移除会议，且会议正在进行时，再次加入弹出提示
                if (memberStatus == StarryConstants.CALL_STATUS_REMOVED.toString()) {
                    ToastUtils.showLong(R.string.starry_meeting_removed_msg)
                } else {
                    // 加入视频会议
                    onMeetingJoined(position, model)
                }
            }
        })

        recycler_view_recently?.apply {
            setHasFixedSize(true)
            layoutManager = linearLayoutManager
            adapter = mAdapter
        }

        refresh_layout?.run {
            setEnableOverScrollDrag(false)
            setEnableOverScrollBounce(false)
            setEnableAutoLoadMore(true)
            setEnableRefresh(true)
            setEnableNestedScroll(false)
            setEnableLoadMoreWhenContentNotFull(false)
            setEnableLoadMore(true)
            setEnableFooterFollowWhenNoMoreData(false)
        }
            ?.setOnLoadMoreListener(OnLoadMoreListener {
                viewModel.getCallRecordsMore()
            })
            ?.setOnRefreshListener(OnRefreshListener {
                if (noNetworkUI()) {
                    it.finishRefresh(false)
                    return@OnRefreshListener
                }

//                startNettyStarry()
                logI("RecentlyFragment.initView.startNettyStarryService");
                AppClearUtils.startNettyStarryService()

                // 获取服务器数据 查询最近通话记录列表
                viewModel.getDingMeetingAndCallRecords()
//              starryViewModel.missedCallRecords()
                it.finishRefresh(true)
                refresh_layout.resetNoMoreData()
            })

        recently_bottom_msg_coount?.text = getString(R.string.starry_recently_bottom_msg_count, 0)

        // 先显示，然后获取最新数据
        viewModel.listRecently.value?.apply {
            mAdapter?.setmDatas(this)
            mAdapter?.setTotal(viewModel.recentlyTotal)

            logI("RecentlyFragment.listRecently.apply.size=${this.size}")
            if (this.isNotEmpty()) {
                logI("RecentlyFragment.listRecently.starry_msg_nodata1")
                starry_msg_nodata?.visibility = View.GONE
                refresh_layout?.visibility = View.VISIBLE
                recently_bottom_msg_coount?.visibility = View.GONE
            }

            setFooterText(this.size ?: 0)
            setFooterView(recycler_view_recently)
        }

        launch {
            AppClearUtils.startNettyStarryService()
            starryViewModel.redpointRead()
            delay(100)
            viewModel.getCallRecordsRefresh()
            delay(300)
            viewModel.getDingMeetingAndCallRecords()
        }

        // 断网、弱网环境下
        tv_click_refresh?.singleClick {
            if (noNetworkUI()) {
                (activity as StarryActivity).showProgressDialogMoment()
                return@singleClick
            }
            //这块要等待socket断网后重连
            // 从服务器获取最新数据
            Timer().schedule(500) {
                viewModel.getDingMeetingAndCallRecords()
            }
        }

        noNetworkUI()
        initNetListener()

        // 清空最近会议
        LiveDataBus.get().with(StarryConstants.MEETING_CLEAR, Boolean::class.java)
            .observe(this) {
                if (it) {
                    onClearMeeting()
                }
            }
    }

    // 清空最近会议
    private fun onClearMeeting() {
        clearHistoryPopup = StarryCommonPopup.Builder(requireActivity())
            .setTitle(getString(R.string.starry_popupwindow_title))
            .setMessage(getString(R.string.starry_recently_msg_clear))
            .setPositiveTitle(getString(R.string.starry_common_dialog_yes))
            .setNegativeTextColor(getColor(R.color.starry_text_title_color_black))
            .setNegativeTitle(getString(R.string.starry_common_dialog_not))
            .setOnPositiveListener { dialog, _ ->
                viewModel.deleteAllRecords()
                dialog?.dismiss()
            }
            .setOnDismissListener {
                clearHistoryPopup = null
            }
            .setOnNegativeListener { dialog, _ -> dialog.dismiss() }
            .create().apply {
                show()
            }

    }

    private fun onMeetingJoined(position: Int, model: StarryCallRecordModel) {
        logI("RecentlyFragment.onMeetingJoined.position=${position}")

        if (!NetworkUtils.isConnected()){
            ToastUtils.showLong(R.string.starry_network_error_msg)
            return
        }

        val meetingName = model.meetingName
        val roomNo = model.meetingId

        val isCam = StarryPreferences.getInstance()?.lastCam ?: false
        val isMic = StarryPreferences.getInstance()?.lastMic ?: true
        ModelManager.membersModel.selfVideoInUseLive.value = isCam
        ModelManager.membersModel.selfAudioInUseLive.value = isMic
        if (!ESPermissionUtils.checkVideoAndAudioPermission()) {
            StarryPreferences.getInstance().callInTargetCamStatus = if (isCam) CAM_AUDIO_STATUS_OPEN else CAM_AUDIO_STATUS_CLOSE
            StarryPreferences.getInstance().callInTargetMicStatus = if (isMic) CAM_AUDIO_STATUS_OPEN else CAM_AUDIO_STATUS_CLOSE
        }

        val callInModel = StarryCallInModel()
        callInModel.apply {
            room_name = meetingName
            room = roomNo
            udid_from = ""
            callId = ""
            userid_from = ""
            nickname_from = ""
            headImage = ""
            isCamOpen = isCam
            isMicOpen = isMic
            userid = StarryPreferences.getInstance().userId
            czurId = StarryPreferences.getInstance().czurId
            accountNo = StarryPreferences.getInstance().accountNo
            agoraId = StarryPreferences.getInstance().starryUserinfoModel.id.toString()
        }

        // 用来标识自己是否在其他会议中，可用此字段来判断是否需要进行弹窗
        // 并且当前会议不是 同账号,其他端接听的情况, 才弹窗
        if (viewModel.otherMeeting && !model.isPCEnter) {
            StarryCommonPopup.Builder(requireContext())
                .setTitle(getString(R.string.starry_popupwindow_title))
                .setMessage(getString(R.string.starry_callin_dialog_msg))
                .setPositiveTitle(getString(R.string.starry_callin_dialog_join))
                .setNegativeTitle(getString(R.string.starry_callin_dialog_cancel))
                .setOnPositiveListener { dialog, _ ->
                    if (!NetworkUtils.isConnected()){
                        ToastUtils.showLong(R.string.starry_network_error_msg)
                        dialog?.dismiss()
                        return@setOnPositiveListener
                    }
                    val i = Intent(requireContext(), MeetingMainActivity::class.java)
                    i.putExtra(MeetingMainActivity.KEY_BOOT_MODEL, callInModel)
                    i.putExtra(MeetingMainActivity.KEY_ROOM, roomNo)
                    i.putExtra(MeetingMainActivity.KEY_BOOT_TYPE, MeetingMainActivity.BOOT_TPE_JOIN)
                    startActivity(i)

                    dialog?.dismiss()
                }
                .setOnNegativeListener { dialog, _ ->
                    dialog.dismiss()
                }
                .create()
                .show()
        } else {
            if (!NetworkUtils.isConnected()){
                ToastUtils.showLong(R.string.starry_network_error_msg)
                return
            }
            val i = Intent(requireContext(), MeetingMainActivity::class.java)
            i.putExtra(MeetingMainActivity.KEY_BOOT_MODEL, callInModel)
            i.putExtra(MeetingMainActivity.KEY_ROOM, roomNo)
            i.putExtra(MeetingMainActivity.KEY_BOOT_TYPE, MeetingMainActivity.BOOT_TPE_JOIN)
            startActivity(i)
        }
    }

    private fun initNetListener() {
        NetworkUtils.registerNetworkStatusChangedListener(object :
            NetworkUtils.OnNetworkStatusChangedListener {
            override fun onDisconnected() {
                clearHistoryPopup?.dismiss()
            }

            override fun onConnected(networkType: NetworkUtils.NetworkType?) {
                logI("RecentlyFragment.onConnected.startNettyStarryService");
                AppClearUtils.startNettyStarryService()

                Timer().schedule(1500) {
                    launch(Dispatchers.Main) {
                        Log.i("RecentlyFragment", "onConnected.schedule1")
                        starry_no_network?.visibility = View.GONE
                        // 获取服务器数据 查询最近通话记录列表
                        viewModel.getDingMeetingAndCallRecords()
                    }
                }
            }
        })
    }

    // 无网络下，点击按钮刷新，仍旧无网络页面
    private fun noNetworkUI(): Boolean {
        return if (!NetworkUtils.isConnected()) {
            starry_no_network?.visibility = View.VISIBLE
            true
        } else {
            starry_no_network?.visibility = View.GONE
            false
        }
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)

//        Log.i("Jason", "RecentlyFragment.onHiddenChanged.hidden=${hidden}")

        // 本来就在最近tab，自然结束会议，也不显示小红点
        MeetingModel.isCancelCallRecordsFlag = !hidden

        if (noNetworkUI()) {
            return
        }

        if (!hidden) {
//            starry_msg_nodata?.visibility = View.GONE
            recycler_view_recently?.scrollToPosition(0)

            // 从服务器获取最新数据
            launch {
//                startNettyStarry()
                logI("RecentlyFragment.onHiddenChanged.startNettyStarryService");
                AppClearUtils.startNettyStarryService()
                starryViewModel.redpointRead()
                starryViewModel.showMeetingRemindDialog()
                delay(100)
                viewModel.getCallRecordsRefresh()
                delay(300)
                viewModel.getDingMeetingAndCallRecords()
            }
        }
    }

    override fun initData() {
        super.initData()
        viewModel.apply {
            viewModel.mMoreCount.observe(this@RecentlyFragment) {
                if (it < StarryConstants.STARRY_CALL_PAGE_SIZE) {
                    refresh_layout.finishLoadMoreWithNoMoreData()
                } else {
                    refresh_layout.resetNoMoreData()
                    refresh_layout.finishLoadMore()
                }
            }
            logI("RecentlyFragment.listRecently.viewModel.listRecently" + viewModel.listRecently.value?.size)
            if (viewModel == null || viewModel.listRecently.value == null) {// viewmodel的变量为空了, 没有进行初始化, 暂不知道原因
                listRecentlyObserve(listOf())
            }

            viewModel.listRecently.observe(this@RecentlyFragment) {
                listRecentlyObserve(it)
            }

            starryViewModel.isMissedCallRecordsFlag.observe(this@RecentlyFragment) {
                if (it) {
                    // 从服务器获取最新数据
                    viewModel.getDingMeetingAndCallRecords()
                }
            }
        }

    }

    private fun listRecentlyObserve(it: List<StarryCallRecordModel>) {

//            Log.i("RecentlyFragment", "viewModel.listDoingMeeting=${viewModel.listDoingMeeting.value}")
        refresh_layout.setEnableLoadMore(false)
        mAdapter?.setTotal(viewModel.recentlyTotal)
        if (viewModel.mMoreCount.value!! < StarryConstants.STARRY_CALL_PAGE_SIZE) {
            mAdapter?.setFooterVis(View.VISIBLE)
        } else {
            mAdapter?.setFooterVis(View.GONE)
        }
        mAdapter?.setmDatas(it)
        recycler_view_recently?.postDelayed({//为了防止刷新以后refresh_layout 会弹出上划加载的footer
            refresh_layout?.setEnableLoadMore(true)
        }, 200)
//        logI("RecentlyFragment.listRecently.size=${it.size}")
        if (it.isEmpty()) {
            starry_msg_nodata?.visibility = View.VISIBLE
            refresh_layout?.visibility = View.GONE
            recently_bottom_msg_coount?.visibility = View.GONE
        } else {
            starry_msg_nodata?.visibility = View.GONE
            refresh_layout?.visibility = View.VISIBLE
            recently_bottom_msg_coount?.visibility = View.GONE
        }

        setFooterText(it.size ?: 0)
        setFooterView(recycler_view_recently)

        hideProgressDialog()
    }

    private fun setFooterView(view: RecyclerView) {
        val footer = layoutInflater.inflate(R.layout.contact_footer, view, false)
        mAdapter?.setFooterFivew(footer)
    }

    private fun setFooterText(count: Int) {
        mAdapter?.setFootertext(count)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

}