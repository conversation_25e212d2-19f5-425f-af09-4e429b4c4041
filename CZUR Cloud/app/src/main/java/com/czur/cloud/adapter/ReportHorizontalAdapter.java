package com.czur.cloud.adapter;

import android.app.Activity;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.czur.cloud.R;
import com.czur.cloud.entity.realm.SPReportEntitySub;
import com.czur.cloud.util.validator.Validator;
import com.github.mikephil.charting.charts.PieChart;
import com.github.mikephil.charting.data.PieData;
import com.github.mikephil.charting.data.PieDataSet;
import com.github.mikephil.charting.data.PieEntry;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

import io.realm.Realm;
import io.realm.Sort;

/**
 * Created by shaojun on 2020/4/1
 * Email：<EMAIL>
 */

public class ReportHorizontalAdapter extends RecyclerView.Adapter<ViewHolder> {
    private static final int ITEM_TYPE_NORMA = 0;
    private Activity mActivity;
    //当前需要显示的所有的图片数据
    private List<SPReportEntitySub> datas;
    private LayoutInflater mInflater;

    /**
     * 构造方法
     */
    public ReportHorizontalAdapter(Activity activity, List<SPReportEntitySub> datas) {
        this.mActivity = activity;
        this.datas = datas;
        mInflater = LayoutInflater.from(activity);
    }

    public void refreshData(List<SPReportEntitySub> datas) {
        this.datas = datas;
        notifyDataSetChanged();
    }

    public void refreshData(Realm realm) {
        this.datas = realm.where(SPReportEntitySub.class).sort("createTime", Sort.DESCENDING).findAll();
        notifyDataSetChanged();
    }


    @NotNull
    @Override
    public ViewHolder onCreateViewHolder(@NotNull ViewGroup parent, int viewType) {
        return new NormalViewHolder(mInflater.inflate(R.layout.item_report_horizontal, parent, false));
    }

    @Override
    public void onBindViewHolder(@NotNull ViewHolder holder, final int position) {

        if (holder instanceof NormalViewHolder) {
            final NormalViewHolder mHolder = (NormalViewHolder) holder;
            mHolder.mItem = datas.get(position);
            String fromEnd = mHolder.mItem.getFromEnd();
            if (Validator.isNotEmpty(fromEnd)) {
                drawChart(mHolder.pieChart, mHolder.mItem);

                mHolder.tvRight.setText(mHolder.mItem.getRightProportion() + "%");
                mHolder.tvMicroError.setText(mHolder.mItem.getModerateProportion() + "%");
                mHolder.tvError.setText(mHolder.mItem.getMildProportion() + "%");
                mHolder.tvSeriousError.setText(mHolder.mItem.getSeriousProportion() + "%");
            }
        }
    }


    @Override
    public int getItemViewType(int position) {
        return ITEM_TYPE_NORMA;
    }


    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return datas.size();
    }

    private static class NormalViewHolder extends ViewHolder {
        public final View mView;
        SPReportEntitySub mItem;
        TextView tvRight, tvMicroError, tvError, tvSeriousError;
        PieChart pieChart;

        NormalViewHolder(View itemView) {
            super(itemView);
            mView = itemView;

            tvRight = itemView.findViewById(R.id.tv_right);
            tvMicroError = itemView.findViewById(R.id.tv_micro_error);
            tvError = itemView.findViewById(R.id.tv_error);
            tvSeriousError = itemView.findViewById(R.id.tv_serious_error);
            pieChart = itemView.findViewById(R.id.pie_chart);

        }
    }


    private onItemClickListener onItemClickListener;

    public void setOnItemClickListener(onItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface onItemClickListener {
        void onItemClick(int position, SPReportEntitySub SPReportEntity);
    }


    private void drawChart(PieChart pieChart, SPReportEntitySub entity) {
        pieChart.setUsePercentValues(true);
        pieChart.getDescription().setEnabled(false);
        pieChart.setDrawHoleEnabled(true);
        pieChart.setHoleColor(Color.WHITE);
        pieChart.setHoleRadius(60);
        pieChart.setDrawCenterText(false);
        pieChart.setDrawEntryLabels(false);
        pieChart.setRotationAngle(-90);
        pieChart.getLegend().setEnabled(false);
        pieChart.setRotationEnabled(false);
        pieChart.setHighlightPerTapEnabled(false);
        pieChart.setExtraOffsets(0, 0, 0, 0);
        ArrayList<PieEntry> entries = new ArrayList<>();
        entries.add(new PieEntry(Float.parseFloat(entity.getRightProportion())));
        entries.add(new PieEntry(Float.parseFloat(entity.getModerateProportion())));
        entries.add(new PieEntry(Float.parseFloat(entity.getMildProportion())));
        entries.add(new PieEntry(Float.parseFloat(entity.getSeriousProportion())));

        PieDataSet dataSet = new PieDataSet(entries, "");
        dataSet.setColors(mActivity.getResources().getColor(R.color.green_8ae5b1),
                mActivity.getResources().getColor(R.color.yellow_faec94),
                mActivity.getResources().getColor(R.color.orange_fbb779),
                mActivity.getResources().getColor(R.color.red_f07575));
        dataSet.setDrawValues(false);
        dataSet.setDrawIcons(false);

        PieData data = new PieData(dataSet);
        data.setDrawValues(false);
        data.setHighlightEnabled(false);
        pieChart.setData(data);
        pieChart.invalidate();
    }

}
