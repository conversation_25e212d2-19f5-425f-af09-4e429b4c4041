package com.czur.cloud.ui.auramate;

import static com.czur.czurutils.log.CZURLogUtilsKt.logE;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;
import static com.czur.czurutils.log.CZURLogUtilsKt.logTagD;
import static com.czur.czurutils.log.CZURLogUtilsKt.logTagE;
import static io.agora.rtc2.Constants.CHANNEL_PROFILE_LIVE_BROADCASTING;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.KeyguardManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.PowerManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.SurfaceView;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.alibaba.sdk.android.oss.ClientException;
import com.alibaba.sdk.android.oss.ServiceException;
import com.alibaba.sdk.android.oss.callback.OSSProgressCallback;
import com.alibaba.sdk.android.oss.common.OSSLog;
import com.alibaba.sdk.android.oss.model.GetObjectRequest;
import com.alibaba.sdk.android.oss.model.GetObjectResult;
import com.alibaba.sdk.android.oss.model.ObjectMetadata;
import com.badoo.mobile.util.WeakHandler;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.ConvertUtils;
import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.blankj.utilcode.util.UtilsTransActivity;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.common.OSSInstance;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.GetRoomChannelEvent;
import com.czur.cloud.event.HdVideoSwitchEvent;
import com.czur.cloud.event.HdViewEvent;
import com.czur.cloud.event.HdViewSaveEvent;
import com.czur.cloud.event.VideoCameraEvent;
import com.czur.cloud.event.aurahome.VideoEvent;
import com.czur.cloud.model.VideoTokenModel;
import com.czur.cloud.netty.CZURMessageConstants;
import com.czur.cloud.netty.core.CZURTcpClient;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.auramate.reportfragment.AuraMateHDViewAlbumActivity;
import com.czur.cloud.ui.auramate.reportfragment.HdViewData;
import com.czur.cloud.ui.auramate.reportfragment.HdViewFileUtils;
import com.czur.cloud.ui.component.CustomClickListener;
import com.czur.cloud.ui.component.popup.SaveHdViewPopupDialog;
import com.czur.cloud.ui.starry.meeting.widget.container.VideoContainer;
import com.czur.cloud.util.PermissionUtil;
import com.czur.cloud.util.validator.Validator;
import com.davemorrissey.labs.subscaleview.ImageSource;
import com.davemorrissey.labs.subscaleview.SubsamplingScaleImageView;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicBoolean;

import io.agora.rtc2.ChannelMediaOptions;
import io.agora.rtc2.Constants;
import io.agora.rtc2.IRtcEngineEventHandler;
import io.agora.rtc2.RtcEngine;
import io.agora.rtc2.video.BeautyOptions;
import io.agora.rtc2.video.VideoCanvas;
import io.agora.rtc2.video.VideoEncoderConfiguration;

public class AuraMateRemoteVideoActivity extends AuramateBaseActivity implements View.OnClickListener {

    public static final int WARN_ADM_PLAYOUT_ABNORMAL_FREQUENCY = 1020;
    public static final int STREAM_FALLBACK_OPTION_AUDIO_ONLY = 2;

    private static final String LOG_TAG = AuraMateRemoteVideoActivity.class.getSimpleName();
    private static final int PERMISSION_REQ_ID_RECORD_AUDIO = 22;
    private static final int PERMISSION_REQ_ID_CAMERA = PERMISSION_REQ_ID_RECORD_AUDIO + 1;
    private ImageView changeCameraBtn;
    private TextView changeCameraTv;
    private TextView hdImageTv;
    private TextView hdVideoTv;
    private ImageView dialogOutBtn;
    private ImageView hdImageBtn;
    private ImageView hdVideoBtn;
    private ImageView remoteVideoBackBtn;
    private String channel;

    private AtomicBoolean isInVideo;
    private RelativeLayout videoLoadingRl;
    private ImageView loadingImg;
    private RelativeLayout hdViewLoadingRl;
    private ImageView hdViewLoadingImg;
    private TextView hdViewLoadingTv;

    private TextView lastCountDownTimeTv;
    private RelativeLayout saveLoadingRl;
    private ImageView saveLoadingImg;
    private boolean isCallIn;
    private String callId;
    private String userIdTo;
    private String token;
    private Animation imgAnim;
    private ImageView saveCloseImg;
    private ImageView saveImg, saveImgAblum;

    private RelativeLayout failedToast;
    private TextView tvNetToast;
    private SubsamplingScaleImageView frescoImageView;
    private WeakHandler handler;
    private SaveHdViewPopupDialog successPopup;
    private RelativeLayout hdViewLoadingBg;
    private LinearLayout hdViewLl;
    private LinearLayout hdVideoLl;
    private LinearLayout changeCameraLl;
    private FrameLayout smallContainer,bigContainer;

    private TextView loadingTv;
    private CountDownTimer timer;
    private int meCount, otherCount, unKnownCount;
    private TextView tvMeTxQuality, tvMeRxQuality;
    private TextView tvOtherTxQuality, tvOtherRxQuality;
    private SurfaceView localSv, remoteSv;
    private AtomicBoolean isCameraSwitching;
    private AtomicBoolean isCamareVideo;
    private AtomicBoolean isJoinChannel;

    private ImageView btnVideoZan;  //赞按钮
    private ImageView btnVideoMic;  //mic按钮
    private ImageView btnVideoSub;  //小窗按钮
    private ImageView btnVideoAlbum;  //相册
    private ImageView btnVideoRecord;  //录制
    private TextView countDownTimeTv;  //录制倒计时
    private AtomicBoolean isVideoMic;   //是否静音
    private AtomicBoolean isVideoSub;   //是否开启小窗
    private AtomicBoolean isVideoZan;   //是否已经赞了
    private AtomicBoolean isVideoAlbum;   //相册是否有图

    private int albumCount = 0;//相册中的照片数量
    private TextView btn_video_album_count;
    private String saveHdViewPath;
    private HdViewData locHdViewData;  //当前保存的图片数据

    private boolean isHDVideo = false;

    private boolean isRecording = false;

    private boolean isSupportHdVideo = false;

    VideoContainer videoContainer = null;

    WeakHandler weakHandler = new WeakHandler();
    CountDownTimer countDownTimer = new CountDownTimer(600000, 1000) {
        @Override
        public void onTick(long millisUntilFinished) {
            long time = 600000 - millisUntilFinished;
            long minutes = time / 1000 / 60;
            long seconds = (time / 1000) % 60;
            String text = String.format(Locale.getDefault(), "%02d:%02d", minutes, seconds);
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    countDownTimeTv.setText(text);
                }
            });
            if (minutes == 9 && seconds == 0) {//剩余1分钟提示
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        ToastUtils.showLong(R.string.aura_record_time_warn);
                    }
                });
            }
            if (minutes == 9 && seconds >= 50){
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        lastCountDownTimeTv.setVisibility(View.VISIBLE);
                        lastCountDownTimeTv.setText(String.valueOf(Math.toIntExact(60 - seconds)));
                    }
                });
            }
        }

        @Override
        public void onFinish() {
            // 这里是倒计时结束时需要执行的操作
            btnVideoRecord.performClick();
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        requestPhonePermission(this);

        PowerManager pm = ((PowerManager) getSystemService(POWER_SERVICE));
        @SuppressLint("InvalidWakeLockTag")
        PowerManager.WakeLock screenLock = pm.newWakeLock(PowerManager.SCREEN_DIM_WAKE_LOCK | PowerManager.ACQUIRE_CAUSES_WAKEUP, "TAG");
        screenLock.acquire(100 * 60 * 1000L /*100 minutes*/);
        KeyguardManager km = (KeyguardManager) getSystemService(Context.KEYGUARD_SERVICE);
        final KeyguardManager.KeyguardLock kl = km.newKeyguardLock("unLock");
        kl.disableKeyguard();
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
                | WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED
                | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
                | WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD);
        WindowManager.LayoutParams params = getWindow().getAttributes();
        params.systemUiVisibility = View.SYSTEM_UI_FLAG_LOW_PROFILE;
        setStatusBarColor(R.color.transparent);
        BarUtils.setNavBarVisibility(this, false);
        ScreenUtils.setFullScreen(this);
        setContentView(R.layout.activity_remote_video);

        saveHdViewPath = this.getExternalFilesDir("hdview") + File.separator + "/";

        initComponent();
        registerEvent();

        initDataImageCount();

        hideHDViewAblum(true);
        HdViewFileUtils.videoActivity = this;
        if (BuildConfig.IS_OVERSEAS){//海外暂时不上
            isSupportHdVideo = false;
        }else{
            checkIsSupportHdVideo();
        }
    }

    private void hideHDViewAblum(boolean flag) {
        int hidden = View.VISIBLE;
        if (flag) {
            hidden = View.INVISIBLE;
        }
        findViewById(R.id.save_close_img).setVisibility(hidden);
        findViewById(R.id.save_img).setVisibility(hidden);
        findViewById(R.id.save_tv).setVisibility(hidden);
        findViewById(R.id.save_img_ablum).setVisibility(hidden);
        findViewById(R.id.save_tv_ablum).setVisibility(hidden);

    }

    private void initDataImageCount() {
        //退出时，清除缓存图片
        deleteFolderFile(saveHdViewPath);
        HdViewFileUtils.hdViewDataList.clear();

        File folder = new File(saveHdViewPath);
        //将文件夹下所有文件名存入数组
        String[] allFiles = folder.list();
        albumCount = allFiles.length;
    }

    private void initComponent() {
        isInVideo = new AtomicBoolean(false);
        isJoinChannel = new AtomicBoolean(false);
        isCameraSwitching = new AtomicBoolean(false);
        isCamareVideo = new AtomicBoolean(true);
        handler = new WeakHandler();
        isCallIn = getIntent().getBooleanExtra("isCallIn", false);
        callId = getIntent().getStringExtra("callId");
        userIdTo = getIntent().getStringExtra("userIdTo");

        if (isCallIn) {
            CZURTcpClient.getInstance().videoRequestSecond(this, equipmentId, 1, userIdTo, callId);
        } else {
            CZURTcpClient.getInstance().videoRequest(this, equipmentId);
        }
        remoteVideoBackBtn = (ImageView) findViewById(R.id.remote_video_back_btn);
        changeCameraBtn = (ImageView) findViewById(R.id.change_camera_btn);
        changeCameraTv = (TextView) findViewById(R.id.change_camera_tv);
        loadingTv = (TextView) findViewById(R.id.loading_tv);
        failedToast = (RelativeLayout) findViewById(R.id.failed_toast);
        tvNetToast = findViewById(R.id.tv_net_toast);
        hdViewLoadingBg = (RelativeLayout) findViewById(R.id.hd_view_loading_bg);
        hdImageBtn = (ImageView) findViewById(R.id.hd_imageview_btn);
        hdVideoBtn = (ImageView) findViewById(R.id.hd_video_btn);
        hdImageTv = (TextView) findViewById(R.id.hd_imageview_tv);
        hdVideoTv = (TextView) findViewById(R.id.hd_video_tv);
        dialogOutBtn = (ImageView) findViewById(R.id.dialog_out_btn);
        saveCloseImg = (ImageView) findViewById(R.id.save_close_img);
        saveImg = (ImageView) findViewById(R.id.save_img);
        saveImgAblum = (ImageView) findViewById(R.id.save_img_ablum);
        frescoImageView = findViewById(R.id.save_show_img);
        videoLoadingRl = (RelativeLayout) findViewById(R.id.video_loading_rl);
        loadingImg = (ImageView) findViewById(R.id.loading_img);
        saveLoadingRl = (RelativeLayout) findViewById(R.id.save_loading_rl);
        saveLoadingImg = (ImageView) findViewById(R.id.save_loading_img);
        hdViewLl = (LinearLayout) findViewById(R.id.hd_imageview_ll);
        hdVideoLl = (LinearLayout) findViewById(R.id.hd_video_ll);
        changeCameraLl = (LinearLayout) findViewById(R.id.change_camera_ll);
        hdViewLoadingRl = (RelativeLayout) findViewById(R.id.hd_view_loading_rl);
        hdViewLoadingImg = (ImageView) findViewById(R.id.hd_view_loading_img);
        hdViewLoadingTv = (TextView) findViewById(R.id.hd_view_loading_tv);
        lastCountDownTimeTv = (TextView) findViewById(R.id.last_count_down_time_tv);

        imgAnim = AnimationUtils.loadAnimation(this, R.anim.dialog_anim);
        loadingImg.startAnimation(imgAnim);
        hdViewLoadingImg.startAnimation(imgAnim);
        saveLoadingImg.startAnimation(imgAnim);
        bigContainer = (FrameLayout) findViewById(R.id.remote_video_view_container);
        smallContainer = (FrameLayout) findViewById(R.id.local_video_view_container);
        SaveHdViewPopupDialog.Builder builder = new SaveHdViewPopupDialog.Builder(AuraMateRemoteVideoActivity.this);
        successPopup = builder.create();
        tvMeTxQuality = findViewById(R.id.tv_me_tx_quality);
        tvMeRxQuality = findViewById(R.id.tv_me_rx_quality);
        tvOtherTxQuality = findViewById(R.id.tv_other_tx_quality);
        tvOtherRxQuality = findViewById(R.id.tv_other_rx_quality);
        setCameraDisable();

        //Jason 20201107
        isVideoZan = new AtomicBoolean(false);
        isVideoMic = new AtomicBoolean(false);
        isVideoSub = new AtomicBoolean(true);
        isVideoAlbum = new AtomicBoolean(true);
        btnVideoZan = (ImageView) findViewById(R.id.btn_video_zan);
        btnVideoMic = (ImageView) findViewById(R.id.btn_video_mic);
        btnVideoSub = (ImageView) findViewById(R.id.btn_video_sub);
        btnVideoAlbum = (ImageView) findViewById(R.id.btn_video_album);
        btnVideoRecord = (ImageView) findViewById(R.id.btn_video_record);
        countDownTimeTv = (TextView) findViewById(R.id.countdown_time_tv);

        if (BuildConfig.IS_OVERSEAS){// 海外暂时不上这个功能
            btnVideoRecord.setVisibility(View.GONE);
        }
        btn_video_album_count = findViewById(R.id.btn_video_album_count);
        btn_video_album_count.setVisibility(View.GONE);
        btnVideoAlbum.setImageResource(R.mipmap.btn_video_album);
        if (albumCount > 0) {
            btn_video_album_count.setVisibility(View.VISIBLE);
            btn_video_album_count.setText(albumCount + "");
        }
        btnVideoRecord.setImageResource(R.mipmap.ic_aura_record);
        btnVideoZan.setVisibility(View.GONE);
        setLocalAudioMuteDisable();
    }

    private void setSaveGroupUI(boolean visible) {
        if (visible) {
            findViewById(R.id.save_tv).setVisibility(View.VISIBLE);
            findViewById(R.id.save_img).setVisibility(View.VISIBLE);
            findViewById(R.id.save_tv_ablum).setVisibility(View.VISIBLE);
            findViewById(R.id.save_img_ablum).setVisibility(View.VISIBLE);
        } else {
            findViewById(R.id.save_tv).setVisibility(View.INVISIBLE);
            findViewById(R.id.save_img).setVisibility(View.INVISIBLE);
        }
    }

    private void setShowGroupUI(boolean visible) {
        if (visible) {
            findViewById(R.id.save_bg).setVisibility(View.VISIBLE);
            findViewById(R.id.save_show_img).setVisibility(View.VISIBLE);
            findViewById(R.id.save_close_img).setVisibility(View.VISIBLE);
        } else {
            findViewById(R.id.save_bg).setVisibility(View.INVISIBLE);
            findViewById(R.id.save_show_img).setVisibility(View.INVISIBLE);
            findViewById(R.id.save_close_img).setVisibility(View.INVISIBLE);
        }
    }

    private void setCameraDisable() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                changeCameraBtn.setAlpha(0.5F);
                changeCameraTv.setAlpha(0.5F);
                changeCameraBtn.setEnabled(false);
                changeCameraBtn.setClickable(false);
            }
        });

    }

    private int count = 5;

    private void countDown() {
        timer = new CountDownTimer(9000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                if (isCallIn) {
                    loadingTv.setText(String.format(getString(R.string.calling)));
                } else {
                    if (count > 0) {
                        loadingTv.setText(String.format(getString(R.string.calling_second), ((count)) + ""));
                    } else {
                        loadingTv.setText(String.format(getString(R.string.calling_second), (1 + "")));
                    }
                }
                if (count < 0 && firstView) {
                    changeCameraLl.setVisibility(View.VISIBLE);
                    videoLoadingRl.setVisibility(View.INVISIBLE);
                    setCameraEnable();
                    setLocalAudioMuteEnable();
                    firstView = false;
                }
                count--;
            }

            @Override
            public void onFinish() {
                changeCameraLl.setVisibility(View.VISIBLE);
                videoLoadingRl.setVisibility(View.INVISIBLE);
                setCameraEnable();
                setLocalAudioMuteEnable();
            }
        }.start();

    }

    private void setCameraEnable() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                changeCameraBtn.setAlpha(1F);
                changeCameraTv.setAlpha(1F);
                changeCameraBtn.setEnabled(true);
                changeCameraBtn.setClickable(true);

                if (isCamareVideo.get()) {
                    changeCameraTv.setText(R.string.scan_camera);
                    hdViewLl.setVisibility(View.INVISIBLE);
                    changeHdVideoLayoutVis(false);
                } else {
                    changeCameraTv.setText(R.string.video_camera);
                    hdViewLl.setVisibility(View.VISIBLE);
                    changeHdVideoLayoutVis(true);
                }

            }
        });

    }

    private void setHDDisable() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                hdImageBtn.setAlpha(0.5F);
                hdImageTv.setAlpha(0.5F);
                hdImageBtn.setEnabled(false);
                hdImageBtn.setClickable(false);

                hdVideoBtn.setAlpha(0.5F);
                hdVideoTv.setAlpha(0.5F);
                hdVideoBtn.setEnabled(false);
                hdVideoBtn.setClickable(false);
            }
        });
    }

    private void setHDEnable() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                hdImageBtn.setAlpha(1F);
                hdImageTv.setAlpha(1F);
                hdImageBtn.setEnabled(true);
                hdImageBtn.setClickable(true);
                hdVideoBtn.setAlpha(1F);
                hdVideoTv.setAlpha(1F);
                hdVideoBtn.setEnabled(true);
                hdVideoBtn.setClickable(true);
            }
        });
    }

    private void showImage(String bucket, String ossKey) {
        long start = System.currentTimeMillis();

        GetObjectRequest get = new GetObjectRequest(bucket, ossKey);
        //设置下载进度回调
        get.setProgressListener(new OSSProgressCallback<GetObjectRequest>() {
            @Override
            public void onProgress(GetObjectRequest request, long currentSize, long totalSize) {
                OSSLog.logDebug("getobj_progress: " + currentSize + "  total_size: " + totalSize, false);
            }
        });
        try {
            // 同步执行下载请求，返回结果
            GetObjectResult getResult = OSSInstance.Companion.getInstance().oss().getObject(get);
            Log.d("Content-Length", "" + getResult.getContentLength());
            // 获取文件输入流
            InputStream inputStream = getResult.getObjectContent();
            ByteArrayOutputStream byteArrayOutputStream = ConvertUtils.input2OutputStream(inputStream);
            if (byteArrayOutputStream != null && byteArrayOutputStream.toByteArray() != null) {
                Bitmap bitmap = ConvertUtils.bytes2Bitmap(byteArrayOutputStream.toByteArray());
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        setHDEnable();
                        hdViewLoadingRl.setVisibility(View.INVISIBLE);
                        hdViewLoadingBg.setVisibility(View.INVISIBLE);
                        dialogOutBtn.setVisibility(View.INVISIBLE);
                        changeCameraLl.setVisibility(View.INVISIBLE);
                        hdViewLl.setVisibility(View.INVISIBLE);
                        changeHdVideoLayoutVis(false);
                        setShowGroupUI(true);
                        setSaveGroupUI(true);
                        //压到2048以下就可以
                        frescoImageView.setImage(ImageSource.bitmap(ImageUtils.compressByScale(bitmap, 2000, bitmap.getHeight() * 2000 / bitmap.getWidth())));

                    }
                });

                //Jason 2021-01-08
                // 同步存储图片到高清查看的临时图库
                saveHDViewSave(bitmap, bucket, ossKey);

                // 下载后可以查看文件元信息
                ObjectMetadata metadata = getResult.getMetadata();
                Log.d("ContentType", metadata.getContentType());
            }

        } catch (ClientException e) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    setHDEnable();
                    hdViewLoadingRl.setVisibility(View.INVISIBLE);
                    hdViewLoadingBg.setVisibility(View.INVISIBLE);
                    dialogOutBtn.setVisibility(View.INVISIBLE);
                    changeCameraLl.setVisibility(View.INVISIBLE);
                    hdViewLl.setVisibility(View.INVISIBLE);
                    changeHdVideoLayoutVis(false);
                    setShowGroupUI(true);
                    setSaveGroupUI(true);
                }
            });
            e.printStackTrace();
        } catch (ServiceException e) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    setHDEnable();
                    hdViewLoadingRl.setVisibility(View.INVISIBLE);
                    hdViewLoadingBg.setVisibility(View.INVISIBLE);
                    dialogOutBtn.setVisibility(View.INVISIBLE);
                    changeCameraLl.setVisibility(View.INVISIBLE);
                    hdViewLl.setVisibility(View.INVISIBLE);
                    changeHdVideoLayoutVis(false);
                    setShowGroupUI(true);
                    setSaveGroupUI(true);
                }
            });
        }

        long end = System.currentTimeMillis();
    }


    private void registerEvent() {
        changeCameraBtn.setOnClickListener(this);
        remoteVideoBackBtn.setOnClickListener(this);
        dialogOutBtn.postDelayed(new Runnable() {
            @Override
            public void run() {
                dialogOutBtn.setOnClickListener(new CustomClickListener() {
                    @Override
                    protected void onSingleClick() {
                        finishActivity();
                    }

                    @Override
                    protected void onFastClick() {

                    }
                });
            }
        }, 2000);
        hdImageBtn.setOnClickListener(this);
        hdVideoBtn.setOnClickListener(this);
        saveCloseImg.setOnClickListener(this);
        saveImg.setOnClickListener(this);
        saveImgAblum.setOnClickListener(this);
        changeCameraBtn.setEnabled(false);
        changeCameraBtn.setClickable(false);

        //Jason
        btnVideoSub.setOnClickListener(this);
        btnVideoZan.setOnClickListener(this);
        btnVideoMic.setOnClickListener(this);
        btnVideoAlbum.setOnClickListener(this);
        btnVideoRecord.setOnClickListener(this);

    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_video_record:
                UserPreferences.getInstance().setAuraMoviesRedPoint(true);
                btnVideoRecord.setEnabled(false);
                btnVideoRecord.setAlpha(0.5F);
                int delayTime = 1000;
                if (isRecording) {
                    delayTime = 1000;
                    countDownTimer.cancel();
                    countDownTimeTv.setVisibility(View.GONE);
                    lastCountDownTimeTv.setVisibility(View.GONE);
                    countDownTimer.cancel();
                    CZURTcpClient.getInstance().stopRecordVideo(AuraMateRemoteVideoActivity.this, equipmentId, channel);
                    btnVideoRecord.setImageResource(R.mipmap.ic_aura_record);
                    ToastUtils.showShort(R.string.aura_record_success);
                } else {
                    delayTime = 3000;
                    countDownTimeTv.setVisibility(View.VISIBLE);
                    countDownTimer.start();
                    CZURTcpClient.getInstance().startRecordVideo(AuraMateRemoteVideoActivity.this, equipmentId, channel);
                    btnVideoRecord.setImageResource(R.mipmap.ic_aura_recording);
                }
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        btnVideoRecord.setEnabled(true);
                        btnVideoRecord.setAlpha(1F);
                    }
                }, delayTime);  // 3000ms的延迟（3s）
                isRecording = !isRecording;
                break;
            case R.id.save_close_img:
                setCameraEnable();
                dialogOutBtn.setVisibility(View.VISIBLE);
                changeCameraLl.setVisibility(View.VISIBLE);
                if (changeCameraTv.getText().equals(getResources().getString(R.string.video_camera))) {
                    hdViewLl.setVisibility(View.VISIBLE);
                    changeHdVideoLayoutVis(true);
                } else {
                    hdViewLl.setVisibility(View.INVISIBLE);
                    changeHdVideoLayoutVis(false);
                }
                setShowGroupUI(false);
                setSaveGroupUI(false);
                hideHDViewAblum(true);
                break;
            case R.id.save_img:
                saveLoadingRl.setVisibility(View.VISIBLE);
                CZURTcpClient.getInstance().hdViewSave(AuraMateRemoteVideoActivity.this, equipmentId);
                break;

            case R.id.save_img_ablum:

                openLocalAlbum();//相册按钮
                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        setCameraEnable();
                        dialogOutBtn.setVisibility(View.VISIBLE);
                        changeCameraLl.setVisibility(View.VISIBLE);
                        if (changeCameraTv.getText().equals(getResources().getString(R.string.video_camera))) {
                            hdViewLl.setVisibility(View.VISIBLE);
                            changeHdVideoLayoutVis(true);
                        } else {
                            hdViewLl.setVisibility(View.INVISIBLE);
                            changeHdVideoLayoutVis(false);
                        }
                        setShowGroupUI(false);
                        setSaveGroupUI(false);
                        hideHDViewAblum(true);
                    }
                }, 500);

                break;

            case R.id.hd_imageview_btn:
                setCameraDisable();
                setHDDisable();
                CZURTcpClient.getInstance().hdView(this, equipmentId);
                break;
            case R.id.hd_video_btn://高清视频
                checkIsAuraHDMore180Days();
                break;
            case R.id.change_camera_btn:
            case R.id.change_camera_tv:
                isCamareVideo.set(!isCamareVideo.get());
                isCameraSwitching.set(true);
                setCameraDisable();
                setHDDisable();
                CZURTcpClient.getInstance().switchCamera(this, equipmentId);
                if (isCamareVideo.get()) {
//                    changeCameraTv.setText(R.string.scan_camera);
                    hdViewLl.setVisibility(View.INVISIBLE);
                    changeHdVideoLayoutVis(false);
                } else {
//                    changeCameraTv.setText(R.string.video_camera);
                    hdViewLl.setVisibility(View.VISIBLE);
                    changeHdVideoLayoutVis(true);
                }
                break;
            case R.id.remote_video_back_btn:
            case R.id.dialog_out_btn:
                ActivityUtils.finishActivity(this);
                break;

            //赞按钮
            case R.id.btn_video_zan:
                break;

            //mic按钮
            case R.id.btn_video_mic:
                onLocalAudioMuteClicked();
                break;

            //小窗按钮
            case R.id.btn_video_sub:
                switchLocalVideoSub();
                break;

            //相册按钮
            case R.id.btn_video_album:
                openLocalAlbum();
                break;

            default:
                break;
        }
    }

    private void initShowLoading() {
        hdViewLoadingRl.setVisibility(View.VISIBLE);
        hdViewLoadingBg.setVisibility(View.VISIBLE);
        hdViewLoadingImg.startAnimation(imgAnim);
        hdViewLoadingImg.setBackgroundResource(R.mipmap.video_loading);
    }

    private void showFailed(int id) {
        timer = new CountDownTimer(5000, 1000) {
            @Override
            public void onTick(long l) {
                if (l == 4000) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            hdViewLoadingTv.setText(id);
                        }
                    });
                }
            }

            @Override
            public void onFinish() {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        setCameraEnable();
                        setHDEnable();
                        hdViewLoadingBg.setVisibility(View.INVISIBLE);
                        hdViewLoadingTv.setVisibility(View.INVISIBLE);
                    }
                });
            }
        }.start();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        logI(LOG_TAG + ".EventType=" + event.getEventType());
        switch (event.getEventType()) {
            case SWITCH_HD_RESULT:
                HdVideoSwitchEvent hdVideoSwitchEvent = (HdVideoSwitchEvent) event;
                hdImageBtn.setAlpha(1F);
                hdImageTv.setAlpha(1F);
                hdImageBtn.setEnabled(true);
                hdImageBtn.setClickable(true);

                hdVideoBtn.setAlpha(1F);
                hdVideoTv.setAlpha(1F);
                hdVideoBtn.setEnabled(true);
                hdVideoBtn.setClickable(true);
                switch (hdVideoSwitchEvent.switchResult()) {
                    case CZURConstants.AURA_HD_VIDEO_SWITCH_SUCCESS:
                        isHDVideo = !isHDVideo;
                        if (isHDVideo) {
                            hdVideoBtn.setImageResource(R.mipmap.ic_aura_is_hd_video);
                        } else {
                            hdVideoBtn.setImageResource(R.mipmap.ic_hd_video);
                        }
                        break;
                    case CZURConstants.AURA_HD_VIDEO_SWITCH_FAULT:
                        break;
                    case CZURConstants.AURA_HD_VIDEO_NO_TIME:
                        ToastUtils.showLong(getString(R.string.aura_hd_video_time_not_enough));
                        break;
                }
                break;
            case HD_SAVE_VIEW:
                HdViewSaveEvent hdViewSaveEvent = (HdViewSaveEvent) event;

                saveLoadingRl.setVisibility(View.INVISIBLE);
                if (hdViewSaveEvent.getStatus() == 0) {
                    setSaveGroupUI(true);
                    successPopup.show(getString(R.string.hdview_save_failed1), getString(R.string.hdview_save_failed2), 0);
                    handler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            successPopup.dismiss();
                        }
                    }, 2000);
                } else {
                    setSaveGroupUI(false);
                    successPopup.show(getString(R.string.hdview_save_success1), getString(R.string.hdview_save_success2), 1);

                    if (!HdViewFileUtils.isAblumAcitivityOpen) {
                        locHdViewData.setFav(true);
                    }

                    handler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            successPopup.dismiss();
                        }
                    }, 2000);
                }
                break;
            case HD_VIEW:
                HdViewEvent hdViewEvent = (HdViewEvent) event;
                String hdViewType = hdViewEvent.getHdViewType();
                if (hdViewType.equals(CZURMessageConstants.HdView.UPLOADING.getStatus())) {
                    cancelTimer();
                    initShowLoading();
                    hdViewLoadingTv.setText(R.string.uploading);
                    hdViewLoadingTv.setVisibility(View.VISIBLE);
//                    showFailed(R.string.uploading);
                    setHDDisable();
                } else if (hdViewType.equals(CZURMessageConstants.HdView.TAKING_PICTURES.getStatus())) {
                    cancelTimer();
                    initShowLoading();
                    hdViewLoadingTv.setText(R.string.taking_photo);
                    hdViewLoadingTv.setVisibility(View.VISIBLE);
//                    showFailed(R.string.taking_photo);
                    setHDDisable();
                } else if (hdViewType.equals(CZURMessageConstants.HdView.TAKING_PICTURES_FAILURE.getStatus())) {
                    cancelTimer();
                    initShowLoading();
                    hdViewLoadingRl.setVisibility(View.VISIBLE);
                    hdViewLoadingBg.setVisibility(View.VISIBLE);
                    hdViewLoadingTv.setVisibility(View.VISIBLE);
                    hdViewLoadingTv.setText(R.string.taking_photo_failed);
                    showFailed(R.string.taking_photo_failed);
                    setHDDisable();
                    hdViewLoadingImg.clearAnimation();
                    hdViewLoadingImg.setBackgroundResource(R.mipmap.hd_failed_icon);
                } else if (hdViewType.equals(CZURMessageConstants.HdView.UPLOADING_FAILURE.getStatus())) {
                    cancelTimer();
                    initShowLoading();
                    hdViewLoadingRl.setVisibility(View.VISIBLE);
                    hdViewLoadingBg.setVisibility(View.VISIBLE);
                    hdViewLoadingTv.setVisibility(View.VISIBLE);
                    hdViewLoadingTv.setText(R.string.upload_failed);
                    showFailed(R.string.upload_failed);
                    setHDDisable();
                    hdViewLoadingImg.clearAnimation();
                    hdViewLoadingImg.setBackgroundResource(R.mipmap.hd_failed_icon);
                } else if (hdViewType.equals(CZURMessageConstants.HdView.UPLOAD_COMPLETED.getStatus())) {
                    cancelTimer();
                    initShowLoading();
                    hdViewLoadingTv.setVisibility(View.VISIBLE);
                    hdViewLoadingTv.setText(R.string.getting);
//                    showFailed(R.string.getting);
                    setHDDisable();
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            showImage(hdViewEvent.getOss_bucket(), hdViewEvent.getOss_key());
                        }
                    }).start();
                }
                break;
            case GET_VIDEO_ROOM_CHANNEL:
                GetRoomChannelEvent roomChannelEvent = (GetRoomChannelEvent) event;
                channel = roomChannelEvent.getChannel();
                String udid_from = roomChannelEvent.getUdid_from();
                CZURTcpClient.getInstance().deviceReadyForVideo(this, udid_from, channel);
                HttpManager.getInstance().request().getVideoToken(UserPreferences.getInstance().getUserId(), channel, VideoTokenModel.class, new MiaoHttpManager.CallbackNetwork<VideoTokenModel>() {
                    @Override
                    public void onNoNetwork() {

                    }

                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<VideoTokenModel> entity) {
                        if (!isFinishing()) {
                            token = entity.getBody().getRtc_token();
                            videoLoadingRl.setVisibility(View.VISIBLE);
                            initAgoraEngineAndJoinChannel();
                            if (!isCallIn) {
                                countDown();
                            }
                        }
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<VideoTokenModel> entity) {
                    }

                    @Override
                    public void onError(Exception e) {
                    }
                });
                break;
            case LOG_OUT:
            case DEVICE_CANCEL_VIDEO:
                finishActivity();
                break;
            case APP_IS_READY_FOR_VIDEO:
                VideoEvent videoEvent = (VideoEvent) event;
                channel = videoEvent.getVideo_chat_room_no();
                CZURTcpClient.getInstance().appReadyForVideo(AuraMateRemoteVideoActivity.this, equipmentId, CZURMessageConstants.CallIn.YES.getValue());
                HttpManager.getInstance().request().getVideoToken(UserPreferences.getInstance().getUserId(), channel, VideoTokenModel.class, new MiaoHttpManager.CallbackNetwork<VideoTokenModel>() {
                    @Override
                    public void onNoNetwork() {
                    }

                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<VideoTokenModel> entity) {
                        if (!isFinishing()) {
                            token = entity.getBody().getRtc_token();
                            videoLoadingRl.setVisibility(View.VISIBLE);
                            initAgoraEngineAndJoinChannel();
                            if (!isCallIn) {
                                countDown();
                            } else if (isCallIn) {
                                countDown();
                            }
                        }
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<VideoTokenModel> entity) {
                    }

                    @Override
                    public void onError(Exception e) {
                    }
                });
                break;
            case DEVICE_IS_READY_FOR_VIDEO:
                VideoEvent event1 = (VideoEvent) event;
                if (event1.getIsReadyForCalibrate().equals(CZURMessageConstants.CallIn.NO.getValue())) {
                    showMessage(R.string.msg_busy);
                    finishActivity();
                }
                break;
            case VIDEO_CANCEL:
            case DEVICE_IS_READY_FOR_VIDEO_TIME_OUT:
                if (hasShowPcPopup) {
                    return;
                }
                VideoEvent videoEvent1 = (VideoEvent) event;
                if (videoEvent1.getDeviceUdid().equals(equipmentId)) {
                    finishActivity();
                }
                break;
            case VIDEO_CAMERA_SWITCH:
                VideoCameraEvent cameraEvent = (VideoCameraEvent) event;
                if (cameraEvent.getCamera() == null) {
                    //超时重置
                    if (isCameraSwitching.get()) {
                        setCameraEnable();
                        setHDEnable();
                        isCameraSwitching.set(false);
                    }
                } else {
                    if (cameraEvent.getCamera().equals(CZURMessageConstants.CameraSwitch.CAMERA_ABOVE.getCamera())) {
//                        changeCameraTv.setText(R.string.video_camera);
                        hdViewLl.setVisibility(View.VISIBLE);
                        changeHdVideoLayoutVis(true);
                    } else {
//                        changeCameraTv.setText(R.string.scan_camera);
                        hdViewLl.setVisibility(View.INVISIBLE);
                        changeHdVideoLayoutVis(false);
                    }
                }
                break;
            default:
                break;
        }
    }

    private void cancelTimer() {
        if (timer != null) {
            timer.cancel();
        }
    }

    private void initAgoraEngineAndJoinChannel() {
        initializeAgoraEngine();     // Tutorial Step 1
        setupVideoProfile();         // Tutorial Step 2
        setupLocalVideo();           // Tutorial Step 3
        joinChannel();               // Tutorial Step 4
    }

    private boolean firstView = false;
    private RtcEngine mRtcEngine;// Tutorial Step 1
    private final IRtcEngineEventHandler mRtcEventHandler = new IRtcEngineEventHandler() { // Tutorial Step 1
        @Override
        public void onFirstRemoteVideoDecoded(final int uid, int width, int height, int elapsed) { // Tutorial Step 5
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    logTagD(LOG_TAG,".5.onFirstRemoteVideoDecodedonFirstRemoteVideoDecodedonFirstRemoteVideoDecoded");

                    setupRemoteVideo(uid);
                }
            });
        }

        @Override
        public void onRemoteVideoStateChanged(int uid, int state, int reason, int elapsed) {
            super.onRemoteVideoStateChanged(uid, state, reason, elapsed);
            if (state == 2) {
                firstView = true;
            }
            runOnUiThread(new Runnable() {
                @Override
                public void run() {

                    if (isCameraSwitching.get()) {
                        if (state == 2 && reason == 2) {
                            setCameraEnable();
                            setHDEnable();
                            isCameraSwitching.set(false);
                        }
                    }
                }
            });

        }


        @Override
        public void onUserOffline(int uid, int reason) { // Tutorial Step 7
            logI(LOG_TAG + ".7.onUserOffline().uid=" + uid + ",reason=" + reason);
            if (hasShowPcPopup) {
                return;
            }
            finishActivity();
        }

        @Override
        public void onUserMuteVideo(final int uid, final boolean muted) { // Tutorial Step 10
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    logTagD(LOG_TAG,".5.onUserMuteVideoonUserMuteVideoonUserMuteVideo");
                    onRemoteUserVideoMuted(uid, muted);
                }
            });
        }

        @Override
        public void onJoinChannelSuccess(String channel, int uid, int elapsed) {
            super.onJoinChannelSuccess(channel, uid, elapsed);
            logTagD(LOG_TAG,".5.onJoinChannelSuccessonJoinChannelSuccessonJoinChannelSuccess");
            isJoinChannel.set(true);
        }

        @Override
        public void onUserJoined(int uid, int elapsed) {
            super.onUserJoined(uid, elapsed);

            isInVideo.set(true);
         /*   runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (isCallIn) {
                        changeCameraLl.setVisibility(View.VISIBLE);
                        videoLoadingRl.setVisibility(View.INVISIBLE);
                        setCameraEnable();
                    }
                }
            });*/

        }

        @Override
        public void onNetworkQuality(int uid, int txQuality, int rxQuality) {
            logI(LOG_TAG + ".onNetworkQuality.uid=" + uid);
            //tx 是本地用户 上行； rx 下行
            super.onNetworkQuality(uid, txQuality, rxQuality);
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    showNetToast(uid, txQuality, rxQuality);
                }
            });

        }


        @Override
        public void onConnectionStateChanged(int state, int reason) {
            super.onConnectionStateChanged(state, reason);
        }
    };


    private void showNetToast(int uid, int txQuality, int rxQuality) {
        if (uid == 0) {
            tvMeTxQuality.setText("本机上行：" + txQuality);
            tvMeRxQuality.setText("本机下行：" + rxQuality);
        } else {
            tvOtherRxQuality.setText("对方上行：" + txQuality);
            tvOtherTxQuality.setText("对方下行：" + rxQuality);
        }

        if (uid == 0) {
            if (txQuality >= 4 && txQuality <= 6) {
                meCount++;
            } else {
                meCount--;
                if (meCount < 0) {
                    meCount = 0;
                }
            }
        } else {
            if (txQuality >= 4 && txQuality <= 6) {
                otherCount++;
            } else {
                otherCount--;
                if (otherCount < 0) {
                    otherCount = 0;
                }
            }
        }

        //双方网络同时都差
        if (meCount == 2 && otherCount == 2) {
            meCount = 0;
            otherCount = 0;
            tvNetToast.setText(getResources().getString(R.string.network_bad));
            failedToast.setVisibility(View.VISIBLE);
            failedToast.postDelayed(new Runnable() {
                @Override
                public void run() {
                    failedToast.setVisibility(View.INVISIBLE);
                }
            }, 3000);
        } else if (meCount == 2) {
            //我的网络差
            meCount = 0;
            tvNetToast.setText(getResources().getString(R.string.me_network_bad));
            failedToast.setVisibility(View.VISIBLE);
            failedToast.postDelayed(new Runnable() {
                @Override
                public void run() {
                    failedToast.setVisibility(View.INVISIBLE);
                }
            }, 3000);
        } else if (otherCount == 2) {
            //对方网络差
            otherCount = 0;
            tvNetToast.setText(getResources().getString(R.string.other_network_bad));
            failedToast.setVisibility(View.VISIBLE);
            failedToast.postDelayed(new Runnable() {
                @Override
                public void run() {
                    failedToast.setVisibility(View.INVISIBLE);
                }
            }, 3000);
        }
        if (uid == 0) {
            if (txQuality == 0 || txQuality == 6) {
                unKnownCount++;
            } else {
                unKnownCount--;
                if (unKnownCount < 0) {
                    unKnownCount = 0;
                }
            }

//            if (unKnownCount == 5) {
//                unKnownCount = 0;
//                ActivityUtils.finishActivity(this);
//            }
        }
    }

    private void closeAgora() {
        if (Validator.isNotEmpty(channel)) {
            CZURTcpClient.getInstance().videoCancel(AuraMateRemoteVideoActivity.this, equipmentId, channel);
            leaveChannel();
            if (mRtcEngine != null) {
                RtcEngine.destroy();
            }
        }
    }

    // Tutorial Step 1
    private void initializeAgoraEngine() {
        try {
            logI(LOG_TAG + ".1.initializeAgoraEngine()");
            mRtcEngine = RtcEngine.create(getApplicationContext(), BuildConfig.AGORA_APP_ID, mRtcEventHandler);
        } catch (Exception e) {
            Log.e(LOG_TAG, Log.getStackTraceString(e));
            throw new RuntimeException("NEED TO check rtc sdk init fatal error\n" + Log.getStackTraceString(e));
        }
    }

    // Tutorial Step 2
    private void setupVideoProfile() {
        logI(LOG_TAG + ".20000.setupVid eoProfile1()");
        if (mRtcEngine != null) {
            mRtcEngine.setChannelProfile(CHANNEL_PROFILE_LIVE_BROADCASTING);
            mRtcEngine.setDefaultAudioRoutetoSpeakerphone(true);
            mRtcEngine.setClientRole(Constants.CLIENT_ROLE_BROADCASTER);
            mRtcEngine.enableVideo();
            // 设置美颜效果选项
            mRtcEngine.setBeautyEffectOptions(true, new BeautyOptions(1, 1f, 1f, 1f, 0.3f));
            mRtcEngine.setVideoEncoderConfiguration(
                    new VideoEncoderConfiguration(
                            VideoEncoderConfiguration.VD_320x240,
                            VideoEncoderConfiguration.FRAME_RATE.FRAME_RATE_FPS_15,
                            VideoEncoderConfiguration.STANDARD_BITRATE,
                            VideoEncoderConfiguration.ORIENTATION_MODE.ORIENTATION_MODE_FIXED_LANDSCAPE
                    )
            );
            // 开启双流模式
            mRtcEngine.enableDualStreamMode(true);
            // 发送端的配置。网络较差时，只发送音频流。
            mRtcEngine.setLocalPublishFallbackOption(STREAM_FALLBACK_OPTION_AUDIO_ONLY);
            // 接收端的配置。弱网环境下先尝试接收小流；若当前网络环境无法显示视频，则只接收音频流。
            mRtcEngine.setRemoteSubscribeFallbackOption(STREAM_FALLBACK_OPTION_AUDIO_ONLY);
        }

    }

    // Tutorial Step 3
    private void setupLocalVideo() {
        logI(LOG_TAG + ".3.setupLocalVideo()");
        if (mRtcEngine != null) {
            localSv = RtcEngine.CreateRendererView(this);
            localSv.setZOrderMediaOverlay(true);
            smallContainer.addView(localSv);
            mRtcEngine.setupLocalVideo(new VideoCanvas(localSv, VideoCanvas.RENDER_MODE_HIDDEN, 0));
        }
    }

    // Tutorial Step 4
    private void joinChannel() {
        logI(LOG_TAG + ".4.joinChannel()");
        if (mRtcEngine != null) {
            ChannelMediaOptions option = new ChannelMediaOptions();
            option.autoSubscribeAudio = true;
            option.autoSubscribeVideo = true;
            option.publishCameraTrack = true;
            option.publishMicrophoneTrack = true;
            option.publishScreenCaptureVideo = false;
            mRtcEngine.joinChannel(
                    token,
                    channel,
                    Integer.parseInt(UserPreferences.getInstance().getUserId()),
                    option);
//            mRtcEngine.joinChannel(token, channel, "AuraMate", Integer.parseInt(UserPreferences.getInstance().getUserId()));// if you do not specify the uid, we will generate the uid for you
            mRtcEngine.startPreview();
        }
    }

    private int uid = 0;

    // Tutorial Step 5
    private void setupRemoteVideo(int uid) {
        logI(LOG_TAG + ".5.setupRemoteVideo(),uid=" + uid);
        //因为远端只有一个设备 点对点视频
        if (bigContainer.getChildCount() > 0) {
            bigContainer.removeAllViews();
        }
        remoteSv = RtcEngine.CreateRendererView(this);
        videoContainer = new VideoContainer(this);
        if (BuildConfig.IS_OVERSEAS){
            videoContainer.setCanScale(false);
        }else{
            videoContainer.setCanScale(true);
        }
        videoContainer.addSurface(remoteSv, String.valueOf(uid),true);
        bigContainer.addView(videoContainer);
//        bigContainer.addView(remoteSv);
        this.uid = uid;
        if (mRtcEngine != null) {
            mRtcEngine.setupRemoteVideo(new VideoCanvas(remoteSv, VideoCanvas.RENDER_MODE_FIT, uid));
        }
        remoteSv.setTag(uid); // for mark purpose

    }

    // Tutorial Step 6
    private void leaveChannel() {
        logI(LOG_TAG + ".6.leaveChannel()");
        if (mRtcEngine != null && isJoinChannel.get()) {
            mRtcEngine.leaveChannel();
        }
    }

    // Tutorial Step 7
    private void onRemoteUserLeft() {
        bigContainer.removeAllViews();
    }

    // Tutorial Step 10
    private void onRemoteUserVideoMuted(int uid, boolean muted) {
        logI(LOG_TAG + ".10.onRemoteUserVideoMuted(),uid=" + uid + ", muted=" + muted);

        try {
            if(bigContainer == null){
                logE(LOG_TAG + ".10.onRemoteUserVideoMuted()--Exception--bigContainer == null");
                return;
            }
            VideoContainer videoContainer1 = (VideoContainer) bigContainer.getChildAt(0);

            if (videoContainer1 == null){
                logE(LOG_TAG + ".10.onRemoteUserVideoMuted()--Exception--videoContainer1 == null");
                return;
            }
            View surfaceView = videoContainer1.getCurrentVideoView();
            if (surfaceView != null) {
                logE(LOG_TAG + ".10.onRemoteUserVideoMuted()--Exception--surfaceView == null");
                Object tag = surfaceView.getTag();
                if (tag != null && (Integer) tag == uid) {
                    surfaceView.setVisibility(muted ? View.INVISIBLE : View.VISIBLE);
                }
            }
        }catch (Exception e){
            logE(LOG_TAG + ".10.onRemoteUserVideoMuted()--Exception--" +e.getMessage());
        }


    }

    public boolean checkSelfPermission(String permission, int requestCode) {
        Log.i(LOG_TAG, "checkSelfPermission " + permission + " " + requestCode);
        if (ContextCompat.checkSelfPermission(this,
                permission)
                != PackageManager.PERMISSION_GRANTED) {

            ActivityCompat.requestPermissions(this,
                    new String[]{permission},
                    requestCode);
            return false;
        }
        return true;
    }


    @Override
    public void onRequestPermissionsResult(int requestCode,
                                           @NonNull String permissions[], @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        Log.i(LOG_TAG, "onRequestPermissionsResult " + grantResults[0] + " " + requestCode);

        switch (requestCode) {
            case PERMISSION_REQ_ID_RECORD_AUDIO: {
                if (grantResults.length > 0
                        && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    checkSelfPermission(Manifest.permission.CAMERA, PERMISSION_REQ_ID_CAMERA);
                } else {
                    showMessage("No permission for " + Manifest.permission.RECORD_AUDIO);
                    finishActivity();
                }
                break;
            }
            case PERMISSION_REQ_ID_CAMERA: {
                if (grantResults.length > 0
                        && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    initAgoraEngineAndJoinChannel();
                } else {
                    showMessage("No permission for " + Manifest.permission.CAMERA);
                    finishActivity();
                }
                break;
            }
        }
    }

    //Jason
    private void switchLocalVideoSub() {
        boolean type = isVideoSub.get();

        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (localSv != null) {
                    if (type) {
                        smallContainer.removeView(localSv);
                        btnVideoSub.setImageResource(R.mipmap.btn_video_sub);
                    } else {
                        smallContainer.addView(localSv);
                        btnVideoSub.setImageResource(R.mipmap.btn_video_sub_gray);
                    }
                    isVideoSub.set(!isVideoSub.get());
                }
            }
        });
    }

    /**
     * Tutorial Step 8
     * 将自己静音
     */
    public void onLocalAudioMuteClicked() {
        logI(LOG_TAG + ".8.onLocalAudioMuteClicked()");
        if (mRtcEngine != null) {
            int res = isVideoMic.get() ? R.mipmap.btn_video_mic_gray : R.mipmap.btn_video_mic;
            btnVideoMic.setImageResource(res);

            isVideoMic.set(!isVideoMic.get());
            mRtcEngine.muteLocalAudioStream(isVideoMic.get());
        }
    }

    private void setLocalAudioMuteDisable() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                btnVideoMic.setAlpha(0.5F);
                btnVideoSub.setAlpha(0.5F);
                btnVideoAlbum.setAlpha(0.5F);
                btnVideoRecord.setAlpha(0.5F);
//                btnVideoZan.setAlpha(0.5F);
                btnVideoMic.setEnabled(false);
                btnVideoMic.setClickable(false);
                btnVideoSub.setEnabled(false);
                btnVideoSub.setClickable(false);
                btnVideoAlbum.setEnabled(false);
                btnVideoAlbum.setClickable(false);
                btnVideoRecord.setEnabled(false);
                btnVideoRecord.setClickable(false);
            }
        });
    }

    private void setLocalAudioMuteEnable() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                btnVideoMic.setAlpha(1F);
                btnVideoSub.setAlpha(1F);
                btnVideoAlbum.setAlpha(1F);
                btnVideoRecord.setAlpha(1F);
//                btnVideoZan.setAlpha(1F);
                btnVideoMic.setEnabled(true);
                btnVideoMic.setClickable(true);
                btnVideoSub.setEnabled(true);
                btnVideoSub.setClickable(true);
                btnVideoAlbum.setEnabled(true);
                btnVideoAlbum.setClickable(true);
                btnVideoRecord.setEnabled(true);
                btnVideoRecord.setClickable(true);
            }
        });
    }

    public void openLocalAlbum() {
        if (albumCount < 1) {
            ToastUtils.showLong(getResources().getString(R.string.hdview_no_picture));
            return;
        }

        HdViewFileUtils.isAblumAcitivityOpen = true;

        Intent intent = new Intent(this, AuraMateHDViewAlbumActivity.class);
        intent.putExtra("saveHdViewPath", saveHdViewPath);
        intent.putExtra("ownerId", getIntent().getStringExtra("ownerId"));
        intent.putExtra("equipmentId", equipmentId);
        ActivityUtils.startActivity(intent);
        overridePendingTransition(0, 0);
    }

    private void saveHDViewSave(Bitmap bmp, String bucket, String ossKey) {

        long start = System.currentTimeMillis();

        try {

            long name_time = System.currentTimeMillis();
            String img_filename = name_time + ".png";
            String img_name = saveHdViewPath + img_filename;

            boolean flag = ImageUtils.save(bmp, img_name, Bitmap.CompressFormat.JPEG, false);

            albumCount += 1;

            String fullname = "file://" + saveHdViewPath + "/" + img_filename;
            locHdViewData = new HdViewData(img_filename, fullname, ossKey, bucket);
            HdViewFileUtils.hdViewDataList.add(0, locHdViewData);

            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    //需要同步更新相册中的照片数量
                    if (albumCount > 0) {
                        btn_video_album_count.setVisibility(View.VISIBLE);
                        btn_video_album_count.setText(albumCount + "");
                    }
                }
            });
        } catch (Exception e) {
        }
    }

    //删除制定目录的图片文件
    public void deleteFolderFile(String filePath) {
        try {
            File file = new File(filePath);//获取SD卡指定路径
            File[] files = file.listFiles();//获取SD卡指定路径下的文件或者文件夹
            for (int i = 0; i < files.length; i++) {
                if (files[i].isFile()) {//如果是文件直接删除
                    File photoFile = new File(files[i].getPath());
                    photoFile.delete();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    public boolean PCNeedFinish() {
        return !TextUtils.isEmpty(equipmentId);
    }

    @Override
    protected void onPause() {
        super.onPause();

        //判断是否需要进入后台就挂断视频
        if (HdViewFileUtils.isAblumAcitivityOpen) {
            //
        } else {
            finishActivity();
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
//        //进入后台就挂断视频
//        ActivityUtils.finishActivity(this);
    }

    @Override
    protected void onResume() {
        super.onResume();
        HdViewFileUtils.isAblumAcitivityOpen = false;
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        finishActivity();
    }

    boolean hasRequestFinish =false;
    private void finishActivity(){
        if (hasRequestFinish){
            return;
        }
        hasRequestFinish = true;

//        logStackTrace();
        logI(LOG_TAG + ".finish()");
        weakHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                // 如果卡住了 2s关闭
                ActivityUtils.finishActivity(AuraMateRemoteVideoActivity.this);
            }
        },2000);
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (videoContainer != null){
                    videoContainer.removeAllViews();
                }
                closeAgora();
                mRtcEngine = null;
                ActivityUtils.finishActivity(AuraMateRemoteVideoActivity.this);
            }
        });
    }
    boolean hasFinish = false;
    @Override
    public void finish() {
//        if (hasFinish){
//            return;
//        }
//        hasFinish = true;
//        logStackTrace();
        logI(LOG_TAG + ".finish()");
        super.finish();
//        videoContainer.removeAllViews();
//        closeAgora();
//        mRtcEngine = null;
    }

    @Override
    protected void onDestroy() {
        if (isRecording) {
            CZURTcpClient.getInstance().stopRecordVideo(this, equipmentId, channel);
        }
        if (remoteSv != null) {
            if (videoContainer != null){
                videoContainer.removeAllViews();
            }
            if (bigContainer != null){
                bigContainer.removeAllViews();
            }
        }
        remoteSv = null;
        if (localSv != null) {
            smallContainer.removeView(localSv);
        }
        localSv = null;

        //退出时，清除缓存图片
        deleteFolderFile(saveHdViewPath);
        HdViewFileUtils.hdViewDataList.clear();

        HdViewFileUtils.videoActivity = null;
        countDownTimer.cancel();
        super.onDestroy();
    }

    private void checkIsSupportHdVideo() {
        HttpManager.getInstance().request().isSupportAuraHD(equipmentId,
                String.class,
                new MiaoHttpManager.CallbackNetwork<String>() {
                    @Override
                    public void onNoNetwork() {
                        showMessage(R.string.toast_no_connection_network);
                    }

                    @Override
                    public void onStart() {
//                        showProgressDialog();
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> response) {
                        hideProgressDialog();
                        try {
                            String responseString = response.getBody();
                            if (response.getCode() == 1000) {
                                isSupportHdVideo = "true".equals(responseString);
                                // 处理code为1000的情况
                            } else {
                                isSupportHdVideo = false;
                                // 处理code不为1000的情况
                            }
                        } catch (Exception e) {
                            logE(e.getMessage());
                            isSupportHdVideo = false;
                        }
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                        hideProgressDialog();
                        isSupportHdVideo = false;
                    }

                    @Override
                    public void onError(Exception e) {
                        hideProgressDialog();
                        isSupportHdVideo = false;
                    }
                });
    }

    private void checkIsAuraHDMore180Days() {
        HttpManager.getInstance().request().isAuraHDMore180Days(equipmentId,
                String.class,
                new MiaoHttpManager.CallbackNetwork<String>() {
                    @Override
                    public void onNoNetwork() {
                        showMessage(R.string.toast_no_connection_network);
                    }

                    @Override
                    public void onStart() {
//                        showProgressDialog();
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> response) {
                        try {
                            if (response.getCode() == 1000) {
                                if ("false".equals(response.getBody())) {
                                    hdImageBtn.setAlpha(0.5F);
                                    hdImageTv.setAlpha(0.5F);
                                    hdImageBtn.setEnabled(false);
                                    hdImageBtn.setClickable(false);
                                    hdVideoBtn.setAlpha(0.5F);
                                    hdVideoTv.setAlpha(0.5F);
                                    hdVideoBtn.setEnabled(false);
                                    hdVideoBtn.setClickable(false);

                                    String isHd = "1";
                                    if (isHDVideo) {
                                        // 如果当前是高清,发送标清0
                                        isHd = "0";
                                    } else {
                                        // 如果当前是标清,发送高清1
                                        isHd = "1";
                                    }
                                    CZURTcpClient.getInstance().hdVideo(AuraMateRemoteVideoActivity.this, equipmentId, isHd);
                                } else {
                                    new Handler().postDelayed(new Runnable() {//为了显示看起来舒服一些
                                        @Override
                                        public void run() {
                                            ToastUtils.showLong(R.string.aura_not_support_hd_video);
                                        }
                                    },800);

                                }
                            } else {

                            }
                        } catch (Exception e) {
                            logE(e.getMessage());
                        }

                        hideProgressDialog();


                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                        hideProgressDialog();

                    }

                    @Override
                    public void onError(Exception e) {
                        hideProgressDialog();

                    }
                });
    }

    private void changeHdVideoLayoutVis(boolean vis) {
        if (!isSupportHdVideo) {
            hdVideoLl.setVisibility(View.GONE);
        } else {
            int viss = vis ? View.VISIBLE : View.GONE;
            hdVideoLl.setVisibility(viss);
        }
    }

    private void requestPhonePermission(Context context) {
        PermissionUtils.permission(PermissionUtil.getPhonePermission())
                .rationale(new PermissionUtils.OnRationaleListener() {
                    @Override
                    public void rationale(UtilsTransActivity activity, ShouldRequest shouldRequest) {
                        ToastUtils.showShort(R.string.denied_camera);
                        shouldRequest.again(true);
                    }
                })
                .callback(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(List<String> permissionsGranted) {

                    }

                    @Override
                    public void onDenied(List<String> permissionsDeniedForever,
                                         List<String> permissionsDenied) {
                        ToastUtils.showShort(R.string.denied_camera);
                    }
                })
                .request();
    }
}
