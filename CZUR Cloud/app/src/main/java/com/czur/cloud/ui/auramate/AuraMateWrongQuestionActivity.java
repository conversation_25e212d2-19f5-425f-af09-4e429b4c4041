package com.czur.cloud.ui.auramate;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.badoo.mobile.util.WeakHandler;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.WrongQuestionAdapter;
import com.czur.cloud.adapter.WrongQuestionTagAdapter;
import com.czur.cloud.entity.AuraMateWrongQuestionModel;
import com.czur.cloud.entity.AuraMateWrongTagModel;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.DeleteFilesEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.util.EtUtils;
import com.czur.cloud.util.validator.Validator;
import com.google.gson.reflect.TypeToken;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnLoadMoreListener;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Yz on 2018/2/28.
 * Email：<EMAIL>
 */

@SuppressWarnings("AlibabaAvoidManuallyCreateThread")
public class AuraMateWrongQuestionActivity extends AuramateBaseActivity implements View.OnClickListener {


    private WrongQuestionAdapter wrongQuestionAdapter;
    private RelativeLayout objectRl;
    private RecyclerView objectRecyclerView;

    private LinkedHashMap<String, String> isCheckedMap;
    private EditText dialogEdt;
    private UserPreferences userPreferences;
    private HttpManager httpManager;
    private SimpleDateFormat formatter;
    private SmartRefreshLayout refreshLayout;
    private WeakHandler handler;
    private List<AuraMateWrongTagModel> objectDatas;
    private List<AuraMateWrongQuestionModel> filesBeans;
    private List<AuraMateWrongQuestionModel> addFilesBeans;
    private String seqNum;
    private List<String> fileIds;
    private List<String> pdfIds;
    private boolean isPdfRun = true;


    private ImageView wrongQuestionBackBtn;
    private TextView wrongQuestionSelectAllBtn;
    private TextView wrongQuestionTitleTv;
    private TextView wrongQuestionCancelBtn;
    private TextView wrongQuestionMultiSelectBtn;
    private RecyclerView wrongQuestionRecyclerView;
    private RelativeLayout wrongQuestionEmptyRl;
    private LinearLayout wrongQuestionBottomLl;
    private RelativeLayout wrongQuestionSaveRl;
    private ImageView wrongQuestionSaveImg;
    private TextView wrongQuestionSaveTv;
    private RelativeLayout wrongQuestionDeleteRl;
    private ImageView wrongQuestionDeleteImg;
    private TextView wrongQuestionDeleteTv;
    private RelativeLayout wrongQuestionShareRl;
    private ImageView wrongQuestionShareImg;
    private TextView wrongQuestionShareTv;
    private WrongQuestionTagAdapter objectAdapter;
    private String ownerId;
    private String tagId;
    private List<AuraMateWrongTagModel> auraMateWrongTagModels;
    private int selectPosition = 0;
    private ImageView manageObjectBtn;
    private boolean isNormal;
    private String selectedTagId;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_wrong_question);
        initComponent();
        initEtFilesRecyclerView();
        registerEvent();
        getObjectAndWrongQuestion("",true);

    }

    @Override
    public boolean PCNeedFinish() {
        return false;
    }


    private void initComponent() {
        userPreferences = UserPreferences.getInstance(this);
        httpManager = HttpManager.getInstance();
        formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        handler = new WeakHandler();
        selectedTagId = getIntent().getStringExtra("tagId");
        if (Validator.isEmpty(selectedTagId)) {
            selectedTagId = "0";
        }
        ownerId = getIntent().getStringExtra("ownerId");
        isNormal = getIntent().getBooleanExtra("isNormal", false);

        refreshLayout = (SmartRefreshLayout) findViewById(R.id.refresh_layout);
        refreshLayout.setEnableOverScrollDrag(false);
        refreshLayout.setEnableOverScrollBounce(false);
        refreshLayout.setEnableAutoLoadMore(true);
        refreshLayout.setEnableRefresh(true);
        refreshLayout.setEnableNestedScroll(false);
        refreshLayout.setEnableFooterFollowWhenNoMoreData(true);
        refreshLayout.setEnableLoadMoreWhenContentNotFull(false);
        refreshLayout.setEnableLoadMore(true);
        wrongQuestionBackBtn = (ImageView) findViewById(R.id.wrong_question_back_btn);
        wrongQuestionSelectAllBtn = (TextView) findViewById(R.id.wrong_question_select_all_btn);
        wrongQuestionTitleTv = (TextView) findViewById(R.id.wrong_question_title_tv);
        wrongQuestionCancelBtn = (TextView) findViewById(R.id.wrong_question_cancel_btn);
        wrongQuestionMultiSelectBtn = (TextView) findViewById(R.id.wrong_question_multi_select_btn);
        wrongQuestionRecyclerView = (RecyclerView) findViewById(R.id.wrong_question_recyclerView);
        wrongQuestionEmptyRl = (RelativeLayout) findViewById(R.id.wrong_question_empty_rl);
        wrongQuestionBottomLl = (LinearLayout) findViewById(R.id.wrong_question_bottom_ll);
        wrongQuestionSaveRl = (RelativeLayout) findViewById(R.id.wrong_question_save_rl);
        wrongQuestionSaveImg = (ImageView) findViewById(R.id.wrong_question_save_img);
        wrongQuestionSaveTv = (TextView) findViewById(R.id.wrong_question_save_tv);
        wrongQuestionDeleteRl = (RelativeLayout) findViewById(R.id.wrong_question_delete_rl);
        wrongQuestionDeleteImg = (ImageView) findViewById(R.id.wrong_question_delete_img);
        wrongQuestionDeleteTv = (TextView) findViewById(R.id.wrong_question_delete_tv);
        wrongQuestionShareRl = (RelativeLayout) findViewById(R.id.wrong_question_share_rl);
        wrongQuestionShareImg = (ImageView) findViewById(R.id.wrong_question_share_img);
        wrongQuestionShareTv = (TextView) findViewById(R.id.wrong_question_share_tv);

        objectRl = (RelativeLayout) findViewById(R.id.object_rl);
        objectRecyclerView = (RecyclerView) findViewById(R.id.object_recyclerView);
        manageObjectBtn = (ImageView) findViewById(R.id.manage_object_btn);
        wrongQuestionTitleTv.setText(R.string.wrong_question);
        wrongQuestionTitleTv.setVisibility(View.VISIBLE);

        refreshLayout.setOnLoadMoreListener(new OnLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                wrongQuestionRecyclerView.stopScroll();
                loadMore();
            }
        });
        refreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                resetToFresh();
                getObjectAndWrongQuestion("",true);
            }
        });


    }

    /**
     * @des: 初始化列表
     * @params:
     * @return:
     */

    private void initEtFilesRecyclerView() {
        fileIds = new ArrayList<>();
        pdfIds = new ArrayList<>();
        filesBeans = new ArrayList<>();
        addFilesBeans = new ArrayList<>();
        isCheckedMap = new LinkedHashMap<>();
        wrongQuestionAdapter = new WrongQuestionAdapter(this, filesBeans, false);
        wrongQuestionAdapter.setOnItemCheckListener(onItemCheckListener);
        wrongQuestionAdapter.setOnWrongQuestionClickListener(onItemClickListener);
        wrongQuestionRecyclerView.setHasFixedSize(true);
        wrongQuestionRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        wrongQuestionRecyclerView.setAdapter(wrongQuestionAdapter);


        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false);
        objectRecyclerView.setLayoutManager(linearLayoutManager);
        objectDatas = new ArrayList<>();
        objectAdapter = new WrongQuestionTagAdapter(this, objectDatas);
        objectAdapter.setOnTagItemClickListener(onTagClickListener);
        objectRecyclerView.setAdapter(objectAdapter);
    }

    private WrongQuestionTagAdapter.OnTagItemClickListener onTagClickListener = new WrongQuestionTagAdapter.OnTagItemClickListener() {
        @Override
        public void onTagClick(AuraMateWrongTagModel auraMateWrongTagModel, int position) {
            selectPosition = position;
            resetToFresh();
            getObjectAndWrongQuestion("",true);
        }
    };



    /**
     * @des: 是否显示空文件夹提示区域
     * @params:
     * @return:
     */

    private void isShowEmptyPrompt() {
        if (filesBeans != null && filesBeans.size() <= 0) {
            wrongQuestionRecyclerView.setVisibility(View.GONE);
            wrongQuestionEmptyRl.setVisibility(View.VISIBLE);
        } else {
            wrongQuestionRecyclerView.setVisibility(View.VISIBLE);
            wrongQuestionEmptyRl.setVisibility(View.GONE);
        }

        if (Validator.isEmpty(objectDatas) || objectDatas.size() < 1){
            wrongQuestionMultiSelectBtn.setEnabled(false);
        }else{
            wrongQuestionMultiSelectBtn.setEnabled(true);
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case DELETE_WRONG_QUESTION_TAG:
                selectPosition = 0;

            case ADD_WRONG_QUESTION_TAG:
                objectDatas = new ArrayList<>();
                objectAdapter = new WrongQuestionTagAdapter(this, objectDatas);
                objectAdapter.setOnTagItemClickListener(onTagClickListener);
                objectRecyclerView.setAdapter(objectAdapter);
                resetToFresh();
                getObjectAndWrongQuestion("",true);
                break;
            default:
                break;
        }
    }


    private void registerEvent() {
        manageObjectBtn.setOnClickListener(this);
        wrongQuestionSelectAllBtn.setOnClickListener(this);
        wrongQuestionCancelBtn.setOnClickListener(this);
        wrongQuestionMultiSelectBtn.setOnClickListener(this);
        wrongQuestionDeleteRl.setOnClickListener(this);
        wrongQuestionBackBtn.setOnClickListener(this);
        wrongQuestionShareRl.setOnClickListener(this);
        wrongQuestionSaveRl.setOnClickListener(this);
        setNetListener();

    }

    /**
     * @des: 下拉加载
     * params:
     * @return:
     */

    private void loadMore() {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                addFilesBeans = getWrongQuestionList(seqNum);
                if (addFilesBeans != null && !filesBeans.containsAll(addFilesBeans)) {
                    if (isSelectAll) {
                        for (int i = 0; i < addFilesBeans.size(); i++) {
                            if (!isCheckedMap.containsKey((addFilesBeans.get(i).getId() + ""))) {
                                isCheckedMap.put(addFilesBeans.get(i).getId() + "", addFilesBeans.get(i).getSmallOssKeyUrl());
                            }
                        }
                    }
                    filesBeans.addAll(addFilesBeans);
                    getSeqNum(addFilesBeans);
                }
                return null;
            }

            @Override
            public void onSuccess(Void result) {
                checkSize(isCheckedMap, wrongQuestionAdapter.getTotalSize());
                if (addFilesBeans == null) {
                    refreshLayout.finishLoadMore(false);
                } else if (Validator.isEmpty(addFilesBeans)) {
                    refreshLayout.finishLoadMoreWithNoMoreData();
                } else {
                    wrongQuestionAdapter.refreshData(filesBeans);
                    refreshLayout.finishLoadMore(true);
                }
            }
        });

    }


    private void refreshObject() {
        objectAdapter.refreshData(objectDatas, selectPosition);
    }

    /**
     * @des: 刷新列表
     * @params:
     * @return:
     */
    public void getObjectAndWrongQuestion(String seqNum,boolean showLoading) {
        if (showLoading){
            showProgressDialog();
        }
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                objectDatas = getObjects();
                if (Validator.isNotEmpty(objectDatas)) {
                    for (int i = 0; i < objectDatas.size(); i++) {
                        if (objectDatas.get(i).getId() == Integer.parseInt(selectedTagId)) {
                            selectPosition = i;
                        }
                    }

                    tagId = objectDatas.get(selectPosition).getId() + "";
                    List<AuraMateWrongQuestionModel> refreshBeans = getWrongQuestionList(seqNum);
                    if (Validator.isNotEmpty(refreshBeans)) {
                        filesBeans.addAll(refreshBeans);
                    }
                    getSeqNum(refreshBeans);
                }

                return null;
            }

            @Override
            public void onSuccess(Void result) {
                selectedTagId = "0";
                if (objectDatas == null) {
                    refreshLayout.finishRefresh(false);
                } else if (!Validator.isNotEmpty(objectDatas)) {
                    refreshLayout.finishRefresh();
                } else {
                    refreshLayout.finishRefresh();
                }
                isShowEmptyPrompt();
                refreshObject();
                refreshFiles();
                hideProgressDialog();

            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                if (!NetworkUtils.isConnected()) {
                    showMessage(R.string.toast_no_connection_network);
                }
                refreshLayout.finishRefresh(false);
                hideProgressDialog();
            }
        });
    }

    /**
     * @des: 重置选中状态并且刷新
     * @params:
     * @return:
     */
    private void refreshFiles() {
        isMultiSelect = false;
        isSelectAll = false;
        hideSelectTopBar();
        wrongQuestionAdapter.refreshData(filesBeans, isMultiSelect, isCheckedMap);
        loadMore();
    }

    /**
     * @des: 重置准备刷新
     * @params:
     * @return:
     */

    private void resetToFresh() {
        refreshLayout.resetNoMoreData();
        refreshLayout.closeHeaderOrFooter();
        filesBeans = new ArrayList<>();
        fileIds = new ArrayList<>();
        pdfIds = new ArrayList<>();
        isCheckedMap = new LinkedHashMap<>();
    }

    /**
     * @des: 图片列表接口
     * @params:
     * @return:
     */
    private List<AuraMateWrongQuestionModel> getWrongQuestionList(String seqNum) {
        try {
            final MiaoHttpEntity<AuraMateWrongQuestionModel> entity = httpManager.request().getWrongQuestionSync(userPreferences.getUserId(), ownerId, tagId, seqNum, "5", new TypeToken<List<AuraMateWrongQuestionModel>>() {
            }.getType());
            if (entity == null) {
                return null;
            }
            if (entity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return entity.getBodyList();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * @des: 图片列表接口
     * @params:
     * @return:
     */
    private List<AuraMateWrongTagModel> getObjects() {
        try {
            final MiaoHttpEntity<AuraMateWrongTagModel> entity = httpManager.request().getWrongQuestionTagSync(userPreferences.getUserId(), equipmentId, ownerId, new TypeToken<List<AuraMateWrongTagModel>>() {
            }.getType());
            if (entity == null) {
                return null;
            }
            if (entity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return entity.getBodyList();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * @des: 获得下拉加载ID
     * @params:
     * @return:
     */
    private void getSeqNum(List<AuraMateWrongQuestionModel> seqNumBeans) {
        if (seqNumBeans.size() > 0) {
            seqNum = seqNumBeans.get(seqNumBeans.size() - 1).getId() + "";
        }
    }


    /**
     * @des: Item选中监听
     * @params:
     * @return:
     */

    private WrongQuestionAdapter.OnItemCheckListener onItemCheckListener = new WrongQuestionAdapter.OnItemCheckListener() {
        @Override
        public void onItemCheck(int position, AuraMateWrongQuestionModel filesBean, LinkedHashMap<String, String> isCheckedMap, int totalSize) {
            AuraMateWrongQuestionActivity.this.isCheckedMap = isCheckedMap;
            checkSize(isCheckedMap, totalSize);
        }
    };
    /**
     * @des: Item点击监听
     * @params:
     * @return:
     */

    private WrongQuestionAdapter.OnWrongQuestionClickListener onItemClickListener = new WrongQuestionAdapter.OnWrongQuestionClickListener() {


        @Override
        public void onEtFilesClick(AuraMateWrongQuestionModel filesBean, int position, CheckBox checkBox) {
            if (isMultiSelect) {
                checkBox.setChecked(!checkBox.isChecked());
            } else {
                Intent intent = new Intent(AuraMateWrongQuestionActivity.this, WrongQuestionPreviewActivity.class);
                intent.putExtra("ownerId", ownerId);
                intent.putExtra("tagId", tagId);
                intent.putExtra("seqNum", filesBean.getId() + "");
                intent.putExtra("equipmentId", equipmentId);
                intent.putExtra("listPosition", position);
                intent.putExtra("date", filesBean.getCreateTime());
                ActivityUtils.startActivity(intent);
            }


        }
    };


    /**
     * @des: 检查选中个数
     * @params:
     * @return:
     */

    private void checkSize(LinkedHashMap<String, String> isCheckedMap, int totalSize) {
        judgeToShowBottom(isCheckedMap);
        if (isCheckedMap.size() == 1) {
            wrongQuestionTitleTv.setText(R.string.select_one_et);
        } else if (isCheckedMap.size() > 1) {
            wrongQuestionTitleTv.setText(String.format(getString(R.string.select_num_et), isCheckedMap.size() + ""));
        } else {
            if (isMultiSelect) {
                wrongQuestionTitleTv.setText(String.format(getString(R.string.select_num_et), isCheckedMap.size() + ""));
            }
        }
        checkSelectAll(isCheckedMap);
    }

    /**
     * @des: 根据数量显示bottom
     * @params:
     * @return:
     */

    private void judgeToShowBottom(LinkedHashMap<String, String> isCheckedMap) {
        int fileSize = 0;
        pdfIds = new ArrayList<>();
        fileIds = new ArrayList<>();
        for (Map.Entry<String, String> stringStringEntry : isCheckedMap.entrySet()) {
            fileSize++;
            fileIds.add(stringStringEntry.getKey());
            pdfIds.add(stringStringEntry.getValue());
        }
        if (fileSize > 0) {
            showAll();
        } else {
            darkAll();
        }

    }

    private void checkSelectAll(LinkedHashMap<String, String> isCheckedMap) {
        //如果选择不是全部Item  text变为取消全选
        if (isCheckedMap.size() < wrongQuestionAdapter.getTotalSize()) {
            wrongQuestionSelectAllBtn.setText(R.string.select_all);
            isSelectAll = false;
        } else {
            wrongQuestionSelectAllBtn.setText(R.string.not_select_all);
            isSelectAll = true;
        }
    }


    private boolean isMultiSelect = false;
    private boolean isSelectAll = false;

    /**
     * @des: 多选
     * @params:
     * @return:
     */

    private void multiSelect() {
        if (Validator.isNotEmpty(filesBeans)) {
            isMultiSelect = !isMultiSelect;
            wrongQuestionAdapter.refreshData(isMultiSelect);
            if (isMultiSelect) {
                showSelectTopBar();
            } else {
                hideSelectTopBar();
            }
        }
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.manage_object_btn:
                Intent intent = new Intent(AuraMateWrongQuestionActivity.this, AuraMateAddWrongTagActivity.class);
                intent.putExtra("equipmentId", equipmentId);
                intent.putExtra("ownerId", ownerId);
                ActivityUtils.startActivity(intent);
                break;
            case R.id.wrong_question_cancel_btn:
                cancelEvent();
                break;
            case R.id.wrong_question_select_all_btn:
                selectAll();
                break;
            case R.id.wrong_question_back_btn:
                if (isNormal) {
                    ActivityUtils.finishActivity(this);
                } else {
                    ActivityUtils.finishToActivity(AuraMateActivity.class, false);
                }

                break;
            case R.id.wrong_question_multi_select_btn:
                multiSelect();
                break;
            case R.id.wrong_question_delete_rl:
                confirmDeleteDialog();
                break;
            default:
                break;
        }
    }


    /**
     * @des: 显示选择TopBar
     * @params:
     * @return:
     */

    private void showSelectTopBar() {
        objectRl.setVisibility(View.GONE);
        wrongQuestionBottomLl.setVisibility(View.VISIBLE);
        wrongQuestionBackBtn.setVisibility(View.GONE);
        wrongQuestionMultiSelectBtn.setVisibility(View.GONE);
        wrongQuestionCancelBtn.setVisibility(View.VISIBLE);
        wrongQuestionSelectAllBtn.setVisibility(View.VISIBLE);
        wrongQuestionCancelBtn.setText(R.string.cancel);
        wrongQuestionTitleTv.setVisibility(View.VISIBLE);
        wrongQuestionTitleTv.setText(String.format(getString(R.string.select_num_et), isCheckedMap.size() + ""));
        wrongQuestionSelectAllBtn.setText(R.string.select_all);
    }

    /**
     * @des: 隐藏选择TopBar
     * @params:
     * @return:
     */

    private void hideSelectTopBar() {
        objectRl.setVisibility(View.VISIBLE);
        wrongQuestionBottomLl.setVisibility(View.GONE);
        wrongQuestionMultiSelectBtn.setVisibility(View.VISIBLE);
        wrongQuestionBackBtn.setVisibility(View.VISIBLE);
        wrongQuestionCancelBtn.setVisibility(View.GONE);
        wrongQuestionSelectAllBtn.setVisibility(View.GONE);
        wrongQuestionTitleTv.setVisibility(View.VISIBLE);
        wrongQuestionTitleTv.setText(R.string.wrong_question);
    }

    /**
     * @des: 显示删除Dialog
     * @params:
     * @return:
     */

    private void confirmDeleteDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(AuraMateWrongQuestionActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.confirm_delete));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                delete();
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();
    }


    /**
     * @des: 选中所有
     * @params:
     * @return:
     */

    private void selectAll() {
        if (!isSelectAll) {
            for (int i = 0; i < filesBeans.size(); i++) {
                if (!isCheckedMap.containsKey((filesBeans.get(i).getId() + ""))) {
                    isCheckedMap.put(filesBeans.get(i).getId() + "", filesBeans.get(i).getSmallOssKeyUrl());
                }
            }
            wrongQuestionSelectAllBtn.setText(R.string.not_select_all);
            isSelectAll = true;
        } else {
            isCheckedMap.clear();
            isCheckedMap = new LinkedHashMap<>();
            wrongQuestionSelectAllBtn.setText(R.string.select_all);
            isSelectAll = false;
        }
        wrongQuestionTitleTv.setText(String.format(getString(R.string.select_num_et), isCheckedMap.size() + ""));
        wrongQuestionAdapter.refreshData(filesBeans, true, isCheckedMap);
    }

    /**
     * @des: 取消事件
     * @params:
     * @return:
     */

    private void cancelEvent() {
        darkAll();
        wrongQuestionBottomLl.setVisibility(View.GONE);
        fileIds = new ArrayList<>();
        pdfIds = new ArrayList<>();
        isMultiSelect = false;
        isSelectAll = false;
        isCheckedMap.clear();
        isCheckedMap = new LinkedHashMap<>();
        wrongQuestionAdapter.refreshData(filesBeans, false, isCheckedMap);
        hideSelectTopBar();
    }

    /**
     * @des: 删除文件或者图片
     * @params:
     * @return:
     */

    private void delete() {
        httpManager.request().deleteWrongQuestionQuestion(userPreferences.getUserId(), ownerId, EtUtils.transFiles(fileIds), String.class, new MiaoHttpManager.CallbackNetwork<String>() {
            @Override
            public void onNoNetwork() {
                showMessage(R.string.toast_no_connection_network);
            }

            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                resetToFresh();
                getObjectAndWrongQuestion("",true);
                EventBus.getDefault().post(new DeleteFilesEvent(EventType.DELETE_WRONG_QUESTION, EtUtils.transFiles(fileIds)));
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                resetToFresh();
                getObjectAndWrongQuestion("",true);
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
                resetToFresh();
                getObjectAndWrongQuestion("",true);
            }
        });
    }



    private void darkAll() {
        darkSave();
        darkShare();
        darkDelete();

    }


    private void showAll() {
        showDelete();
        showSave();
        showShare();
    }

    private void showShare() {
        wrongQuestionShareRl.setClickable(true);
        wrongQuestionShareRl.setEnabled(true);

        wrongQuestionShareImg.setSelected(true);
        wrongQuestionShareTv.setTextColor(getResources().getColor(R.color.white));
    }

    private void darkShare() {
        wrongQuestionShareRl.setClickable(false);
        wrongQuestionShareRl.setEnabled(false);

        wrongQuestionShareImg.setSelected(false);
        wrongQuestionShareTv.setTextColor(getResources().getColor(R.color.dark_text));
    }


    private void showDelete() {
        wrongQuestionDeleteRl.setClickable(true);
        wrongQuestionDeleteRl.setEnabled(true);


        wrongQuestionDeleteImg.setSelected(true);
        wrongQuestionDeleteTv.setTextColor(getResources().getColor(R.color.white));
    }

    private void darkDelete() {
        wrongQuestionDeleteRl.setClickable(false);
        wrongQuestionDeleteRl.setEnabled(false);

        wrongQuestionDeleteImg.setSelected(false);
        wrongQuestionDeleteTv.setTextColor(getResources().getColor(R.color.dark_text));
    }

    private void showSave() {
        wrongQuestionSaveRl.setClickable(true);
        wrongQuestionSaveRl.setEnabled(true);

        wrongQuestionSaveImg.setSelected(true);
        wrongQuestionSaveTv.setTextColor(getResources().getColor(R.color.white));
    }

    private void darkSave() {
        wrongQuestionSaveRl.setClickable(false);
        wrongQuestionSaveRl.setEnabled(false);

        wrongQuestionSaveImg.setSelected(false);
        wrongQuestionSaveTv.setTextColor(getResources().getColor(R.color.dark_text));
    }

}

