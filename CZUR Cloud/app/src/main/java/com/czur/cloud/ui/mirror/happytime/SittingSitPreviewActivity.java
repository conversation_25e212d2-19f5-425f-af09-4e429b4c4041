package com.czur.cloud.ui.mirror.happytime;

import static androidx.annotation.Dimension.DP;
import static com.czur.czurutils.log.CZURLogUtilsKt.logE;

import android.content.Intent;
import android.graphics.Bitmap;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.blankj.utilcode.constant.PermissionConstants;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.UserInfoEvent;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.auramate.siterror.AuraMateStandarSitPicActivity;
import com.czur.cloud.ui.auramate.siterror.MorePopWindow;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.mirror.comm.SittingReportUtil;
import com.czur.cloud.ui.mirror.model.SittingHappyTimePictureModel;
import com.czur.cloud.ui.mirror.model.SittingSitPictureListModel;
import com.czur.cloud.util.CzurFrescoHelper;
import com.czur.cloud.util.PermissionUtil;
import com.czur.cloud.util.share.FileUtil;
import com.czur.cloud.util.share.ShareContentType;
import com.czur.cloud.util.share.ShareUtils;
import com.czur.cloud.util.validator.Validator;
import com.davemorrissey.labs.subscaleview.SubsamplingScaleImageView;
import com.google.gson.reflect.TypeToken;
import com.shuyu.frescoutil.listener.LoadFrescoListener;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.UUID;

public class SittingSitPreviewActivity extends BaseActivity implements View.OnClickListener {
    private ImageView etPreviewBackBtn;
    private ImageView etPreviewMoreBtn;
    private TextView etPreviewTitle1;
    private TextView etPreviewTitle2;
    private View etPreviewSaveBtn;
    private ViewPager viewPager;
    private ImagePagerAdapter mAdapter;
    private HashMap<Integer, SubsamplingScaleImageView> viewMap = new HashMap<>();
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private String seqNum;
    private String typeClass;
    private int currentItem;
    private List<SittingHappyTimePictureModel> fileList;

    private static final int SHARE_SUCCESS_CODE = 666;
    private boolean isCacheBtn;
    private boolean canBack;
    private String url;

    private SimpleDateFormat formatter;

    private String showErrorPictureRelationId="";     //relationId
    private String showErrorPictureDate="";     //获取该日的错误坐姿图片
    private String equipmentId;

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.black_2a);
        BarUtils.setNavBarColor(this, getColor(R.color.black_2a));
        BarUtils.setStatusBarLightMode(this, false);
        setContentView(R.layout.activity_sitting_sit_preview);
        initComponent();
        initViewPager();
        registerEvent();
        getPreviewList();
    }

    private void initComponent() {
        httpManager = HttpManager.getInstance();
        userPreferences = UserPreferences.getInstance(this);
        formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA);

        seqNum = getIntent().getStringExtra("seqNum");
        typeClass = getIntent().getStringExtra("typeClass");
        showErrorPictureRelationId = getIntent().getStringExtra("showErrorPictureRelationId");
        showErrorPictureDate = getIntent().getStringExtra("showErrorPictureDate");
        equipmentId = getIntent().getStringExtra("equipmentId");

        String date = getIntent().getStringExtra("date");
        String[] arr = date.split("\\s+");
        etPreviewBackBtn = (ImageView) findViewById(R.id.aura_home_preview_back_btn);
        etPreviewMoreBtn = (ImageView) findViewById(R.id.aura_home_preview_more_btn);
        etPreviewMoreBtn.setVisibility(View.GONE);

        etPreviewTitle1 = (TextView) findViewById(R.id.aura_home_preview_title_1);
        etPreviewTitle2 = (TextView) findViewById(R.id.aura_home_preview_title_2);
        etPreviewSaveBtn = (View) findViewById(R.id.aura_home_preview_save_btn);
        viewPager = findViewById(R.id.aura_home_preview_viewpager);
        if (arr.length > 0) {
            etPreviewTitle1.setText(arr[0]);
        }
        if (arr.length > 1) {
            etPreviewTitle2.setText(arr[1]);
        }
        fileList = new ArrayList<>();
    }

    private void registerEvent() {
        viewPager.addOnPageChangeListener(viewPageListener);
        etPreviewBackBtn.setOnClickListener(this);
//        etPreviewMoreBtn.setOnClickListener(this);
        etPreviewSaveBtn.setOnClickListener(this);

//        setNetListener();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case AURA_MATE_SIT_MENU_FEEDBACK:
                feedBack();
                break;
            case AURA_MATE_SIT_MENU_STANDARPIC:
                getStandarSitPicture();
                break;
            default:
                break;
        }
    }

    /**
     * 通知系统相册更新
     */
    private void noticeAlbumUpdate(String sdPicPath) {
        MediaScannerConnection.scanFile(SittingSitPreviewActivity.this,
                new String[]{sdPicPath},
                new String[]{"image/jpeg"},
                (path,url)->{});
    }

    private void initViewPager() {
        mAdapter = new ImagePagerAdapter();
        viewPager.setPageMargin(10 * DP);
        viewPager.setAdapter(mAdapter);
        viewPager.setOffscreenPageLimit(3);

        // 使用post延迟执行，确保ViewPager完全初始化后再设置当前页面
        viewPager.post(() -> {
            if (currentItem >= 0 && currentItem < fileList.size()) {
                viewPager.setCurrentItem(currentItem, false);
            }
        });
    }

    private ViewPager.OnPageChangeListener viewPageListener = new ViewPager.OnPageChangeListener() {
        @Override
        public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
        }

        @Override
        public void onPageSelected(int position) {
            currentItem = position;
//            Date date = new Date(Long.parseLong(fileList.get(position).getCreateTime()));
//            String localeDate = formatter.format(date);
            String localeDate = SittingReportUtil.getLocalDateTime(fileList.get(position).getLocaleTime()+"");
            String[] arr = localeDate.split("\\s+");
            if (arr.length > 0) {
                etPreviewTitle1.setText(arr[0]);
            }
            if (arr.length > 1) {
                etPreviewTitle2.setText(arr[1]);
            }
            if (CzurFrescoHelper.isCached(SittingSitPreviewActivity.this, Uri.parse(getBigUrl(position)))) {
                isCacheBtn = true;
                setOriginalBtnGone();
            } else {
                isCacheBtn = false;
                setOriginalBtnVisible();
            }
        }

        @Override
        public void onPageScrollStateChanged(int state) {
        }
    };

    private String getBigUrl(int position) {
        return fileList.get(position).getImgUrl();
    }

    private String getMiddleUrl(int position) {
        return fileList.get(position).getMiddleImgUrl();
    }


    public class ImagePagerAdapter extends PagerAdapter {
        @Override
        public int getCount() {
            return fileList.size();
        }

        @NotNull
        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            SubsamplingScaleImageView zoomImage = initZoomFrescoImageView(position);
            container.addView(zoomImage);
            setBigImage(getBigUrl(position), position);
            if (viewPager.getCurrentItem() == position) {
//                Date date = new Date(Long.parseLong(fileList.get(position).getCreateTime()));
//                String localeDate = formatter.format(date);
                String localeDate = SittingReportUtil.getLocalDateTime(fileList.get(position).getLocaleTime()+"");
                String[] arr = localeDate.split("\\s+");
                if (arr.length > 0) {
                    etPreviewTitle1.setText(arr[0]);
                }
                if (arr.length > 1) {
                    etPreviewTitle2.setText(arr[1]);
                }
                if (CzurFrescoHelper.isCached(SittingSitPreviewActivity.this, Uri.parse(getBigUrl(position)))) {
                    isCacheBtn = true;
                    setOriginalBtnGone();
                } else {
                    isCacheBtn = false;
                    setOriginalBtnVisible();
                }
            }
            return zoomImage;
        }

        @Override
        public void destroyItem(ViewGroup container, int position, @NotNull Object object) {
            container.removeView((View) object);
            SubsamplingScaleImageView zoomImage = viewMap.get(position);
            if (zoomImage != null) {
                viewMap.remove(position);
            }
        }

        @Override
        public boolean isViewFromObject(@NotNull View view, @NotNull Object object) {
            return view == object;
        }

        @Override
        public int getItemPosition(@NotNull Object object) {
            return POSITION_NONE;
        }

    }

    public SubsamplingScaleImageView initZoomFrescoImageView(int position) {
        SubsamplingScaleImageView zoomImage = viewMap.get(position);
        if (zoomImage == null) {
            zoomImage = setImageToIndex(position);
        }
        return zoomImage;
    }

    private SubsamplingScaleImageView setImageToIndex(final int index) {
        SubsamplingScaleImageView zoomImage = new SubsamplingScaleImageView(this);
        viewMap.put(index, zoomImage);
        return zoomImage;
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.aura_home_preview_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.aura_home_preview_more_btn:
                //显示菜单
                popMenu(v);
                break;

            case R.id.aura_home_preview_save_btn:
                if (checkNetwork()) break;
                requestCopyToSdPermission(false);
                break;
            default:
                break;
        }
    }

    private void popMenu(View v){
        MorePopWindow morePopWindow = new MorePopWindow(SittingSitPreviewActivity.this);
        morePopWindow.showPopupWindow(etPreviewMoreBtn);

    }

    public boolean checkNetwork() {
        boolean isNoNetwork = hasNetwork();
        return isNoNetwork || fileList.size() <= 0;
    }

    public boolean hasNetwork() {
        if (fileList.size() <= 0 && !NetworkUtils.isConnected()) {
            showMessage(R.string.toast_no_connection_network);
            return true;
        }
        return false;
    }

    /**
     * 保存至相册
     */
    private void saveToAlbum(final Bitmap bitmap, boolean isShare) {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<String>() {
            @Override
            public String doInBackground() {
//                String path = Environment.getExternalStorageDirectory() + CZURConstants.SD_PATH + CZURConstants.AURA_MATE_PATH;
//                String path = CZURConstants.SD_PATH + CZURConstants.AURA_MATE_PATH;
                String path = CZURConstants.PICTURE_PATH + CZURConstants.AURA_MATE_PATH;
                String etPath = "";
                if (FileUtils.createOrExistsDir(path)) {
                    etPath = path + UUID.randomUUID() + CZURConstants.JPG;
                    ImageUtils.save(bitmap, etPath, Bitmap.CompressFormat.JPEG, false);
                }
                return etPath;
            }

            @Override
            public void onSuccess(String result) {
                hideProgressDialog();
                if (isShare) {
                    new ShareUtils.Builder(SittingSitPreviewActivity.this)
                            .setOnActivityResult(SHARE_SUCCESS_CODE)
                            // 指定分享的文件类型
                            .setContentType(ShareContentType.IMAGE)
                            // 设置要分享的文件 Uri
                            .setShareFileUri(FileUtil.getFileUri(SittingSitPreviewActivity.this, ShareContentType.FILE, new File(result)))
                            // 设置分享选择器的标题
                            .setTitle(getString(R.string.share_to))
                            .build()
                            // 发起分享
                            .shareBySystem();
                } else {
                    noticeAlbumUpdate(result);
                    showMessage(R.string.et_save_success);
                }
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                hideProgressDialog();
                if (!isShare) {
                    showMessage(R.string.et_save_failed);
                }
            }
        });
    }

    /**
     * 得到展示列表接口
     */
    private void getPreviewList() {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                showProgressDialog(true);
                List<SittingHappyTimePictureModel> tempList = getEtFiles();
                if (Validator.isNotEmpty(tempList)) {
                    fileList.addAll(tempList);
                }
                return null;
            }

            @Override
            public void onSuccess(Void result) {
                hideProgressDialog();
                refreshViewPager();
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                canBack = true;
                hideProgressDialog();
                if (!NetworkUtils.isConnected()) {
                    showMessage(R.string.toast_no_connection_network);
                }
            }
        });
    }

    /**
     * @des: 图片列表接口
     * @params:
     * @return:
     */
    private List<SittingHappyTimePictureModel> getEtFiles() {
        try {
            //@MiaoHttpParam("u_id") String u_id,
            //            @MiaoHttpParam("udid") String udid,
            //            @MiaoHttpParam("type") String typeClass,        //请求类型1为错误瞬间 2为愉悦瞬间
            //            @MiaoHttpParam("offsetId") String offsetId,     //指定id位置
            //            @MiaoHttpParam("dateString") String dateString, /
            final MiaoHttpEntity<SittingSitPictureListModel> etFileEntity =
                    httpManager.request().getSittingHappyTimePictureBeforeAfter(
                            userPreferences.getUserId(),
                            equipmentId,
                            typeClass,
                            seqNum,
                            showErrorPictureDate,
                            new TypeToken<SittingSitPictureListModel>() {}.getType());
            if (etFileEntity == null) {
                return null;
            } else if (etFileEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                int offset = etFileEntity.getBody().getOffset();
                currentItem = offset;

                return etFileEntity.getBody().getList();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private void downloadOriginalPicture(String url, boolean isShare) {
        File file = CzurFrescoHelper.getCache(SittingSitPreviewActivity.this, Uri.parse(url));
        if (file != null && file.exists()) {
            Bitmap fileBitmap = ImageUtils.getBitmap(file);
            saveToAlbum(fileBitmap, isShare);
        } else {
            CzurFrescoHelper.getFrescoImg(SittingSitPreviewActivity.this, url, 0, 0, new LoadFrescoListener() {
                @Override
                public void onSuccess(Bitmap bitmap) {
                    File file = CzurFrescoHelper.getCache(SittingSitPreviewActivity.this, Uri.parse(url));
                    if (file != null && file.exists()) {
                        Bitmap fileBitmap = ImageUtils.getBitmap(file);
                        saveToAlbum(fileBitmap, isShare);
                    }
                }

                @Override
                public void onFail() {
                    hideProgressDialog();
                    showMessage(isShare ? R.string.share_failed : R.string.et_save_failed);
                }
            });
        }
    }

    /**
     * 先从缓存拿大图 拿不到直接加载中图
     */
    private void setBigImage(String bigUrl, final int position) {
        SubsamplingScaleImageView zoomImage = initZoomFrescoImageView(position);
        if (CzurFrescoHelper.isCached(this, Uri.parse(bigUrl))) {
            CzurFrescoHelper.loadBigImage(SittingSitPreviewActivity.this, zoomImage, bigUrl, R.mipmap.default_gallery_img, false);
        } else {
            String middleUrl = getMiddleUrl(position);
            CzurFrescoHelper.loadBigImage(SittingSitPreviewActivity.this, zoomImage, middleUrl, R.mipmap.default_gallery_img, false);
        }
    }

    private void setOriginalBtnVisible() {
    }

    private void setOriginalBtnGone() {
    }

    /**
     * 申请权限
     */
    private void requestCopyToSdPermission(boolean isShare) {
        PermissionUtils.permission(PermissionUtil.getStoragePermission())
                .rationale((activity, shouldRequest) -> {
                    showMessage(isShare ? R.string.denied_share : R.string.denied_sdcard);
                    shouldRequest.again(true);
                })
                .callback(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(@NotNull List<String> permissionsGranted) {
                        showProgressDialog(true);
                        downloadOriginalPicture(getBigUrl(viewPager.getCurrentItem()), isShare);
                    }

                    @Override
                    public void onDenied(@NotNull List<String> permissionsDeniedForever,
                                         @NotNull List<String> permissionsDenied) {
                        showMessage(isShare ? R.string.denied_share : R.string.denied_sdcard);
                    }
                })
                .theme(ScreenUtils::setFullScreen)
                .request();
    }

    /**
     * 刷新ViewPager
     */
    private void refreshViewPager() {
        canBack = false;

        // 保存当前位置
        int savedCurrentItem = currentItem;

        // 重新创建并设置Adapter，确保完全刷新
        mAdapter = new ImagePagerAdapter();
        viewPager.setAdapter(mAdapter);

        // 使用post延迟执行，确保Adapter设置完成后再设置当前页面
        viewPager.post(() -> {
            if (savedCurrentItem >= 0 && savedCurrentItem < fileList.size()) {
                viewPager.setCurrentItem(savedCurrentItem, false);
                canBack = true;
            } else {
                canBack = true;
            }
        });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && !canBack) { //监控/拦截/屏蔽返回键
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onDestroy() {
        EventBus.getDefault().post(new UserInfoEvent(EventType.UPDATE_CACHE));
        super.onDestroy();
    }

    /**
     * @des: 反馈错误坐姿识别错误
     */
    private void feedBack() {
        String dataId = fileList.get(currentItem).getId();
        String relationId = fileList.get(currentItem).getRelationId();

        httpManager.request().feedbackAuraErrorSit(
                userPreferences.getUserId(),
                dataId,
                relationId,
                String.class,
                new MiaoHttpManager.CallbackNetwork<String>() {
                    @Override
                    public void onNoNetwork() {
                        showMessage(R.string.toast_no_connection_network);
                    }

                    @Override
                    public void onStart() {
                        showProgressDialog();
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                        hideProgressDialog();
                        ToastUtils.showLong(R.string.error_sit_feedback);
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                        hideProgressDialog();
                        ToastUtils.showLong(R.string.error_sit_feedback_err);
                    }

                    @Override
                    public void onError(Exception e) {
                        hideProgressDialog();
                    }
                });
    }

    public void getStandarSitPicture() {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<String>() {
            @Override
            public String doInBackground() {
                return getStandarSitPicturePath();
            }

            @Override
            public void onSuccess(String url) {
                hideProgressDialog();
                if (Validator.isNotEmpty(url)) {
                    // 显示图片
                    Intent intent = new Intent(SittingSitPreviewActivity.this, AuraMateStandarSitPicActivity.class);
                    intent.putExtra("imgPath", url);
                    ActivityUtils.startActivity(intent);
                }
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                hideProgressDialog();
            }
        });
    }

    private String getStandarSitPicturePath() {
        String dataId = fileList.get(currentItem).getId();
        String relationId = fileList.get(currentItem).getRelationId();
        try {
            MiaoHttpEntity<String> standarSitPicPath = HttpManager.getInstance().request().getStandarSitPicture(
                    userPreferences.getUserId(),
                    dataId,
                    relationId,
                    String.class);
            if (standarSitPicPath.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return standarSitPicPath.getBody();
            } else if (standarSitPicPath.getCode() == MiaoHttpManager.IMG_NO_FOUND) {
                // 提示图片不存在
                ToastUtils.showLong(R.string.error_sit_no_image);
            }else{
                return null;
            }

        } catch (Exception e) {
            logE(e.toString());
            e.printStackTrace();
        }

        return null;
    }


}
