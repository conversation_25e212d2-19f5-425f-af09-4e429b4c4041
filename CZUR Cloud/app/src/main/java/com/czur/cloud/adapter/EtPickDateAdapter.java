package com.czur.cloud.adapter;

import android.content.Context;
import android.graphics.Typeface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.czur.cloud.R;
import com.czur.cloud.entity.EtSummaryDayEntity;
import com.czur.cloud.ui.component.dialog.EtPickDateDialog;

import java.util.List;

public class EtPickDateAdapter extends RecyclerView.Adapter<EtPickDateAdapter.DateHolder> {

    public List<EtSummaryDayEntity> getData() {
        return etSummaryDayEntityList;
    }

    private List<EtSummaryDayEntity> etSummaryDayEntityList;
    private int selectIndex;
    private Context context;
    private EtPickDateDialog.OnItemClickListener onItemClickListener;

    public EtPickDateAdapter(Context context, List<EtSummaryDayEntity> etSummaryDayEntityList, int selectIndex, EtPickDateDialog.OnItemClickListener onItemClickListener) {
        this.context = context;
        this.etSummaryDayEntityList = etSummaryDayEntityList;
        this.selectIndex = selectIndex;
        this.onItemClickListener = onItemClickListener;
    }

    @NonNull
    @Override
    public DateHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new DateHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_et_pick_date, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull DateHolder holder, int position) {
        EtSummaryDayEntity etSummaryDayEntity = etSummaryDayEntityList.get(position);
        holder.etDateTv.setText(String.format("%s  (%d)", etSummaryDayEntity.getDay(), etSummaryDayEntity.getCount()));
        if (selectIndex == position) {
            holder.etDateImg.setVisibility(View.VISIBLE);
            holder.etDateTv.setTextColor(context.getResources().getColor(R.color.blue_29b0d7));
            holder.etDateTv.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        } else {
            holder.etDateImg.setVisibility(View.INVISIBLE);
            holder.etDateTv.setTextColor(context.getResources().getColor(R.color.black_22));
            holder.etDateTv.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
        }

        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String day =etSummaryDayEntity.getDay().replace(".","-");
                onItemClickListener.onClick(day);
            }
        });

    }

    @Override
    public int getItemCount() {
        return this.etSummaryDayEntityList.size();
    }

    static class DateHolder extends RecyclerView.ViewHolder {
        ImageView etDateImg;
        TextView etDateTv;

        DateHolder(View itemView) {
            super(itemView);
            etDateImg = itemView.findViewById(R.id.et_date_img);
            etDateTv = itemView.findViewById(R.id.et_date_tv);
        }
    }
}
