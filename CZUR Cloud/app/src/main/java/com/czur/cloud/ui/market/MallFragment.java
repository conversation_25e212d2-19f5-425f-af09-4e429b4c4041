package com.czur.cloud.ui.market;


import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.JavascriptInterface;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.FrameLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.badoo.mobile.util.WeakHandler;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.czur.cloud.R;
import com.czur.cloud.ui.base.LazyLoadBaseFragment;


public class MallFragment extends LazyLoadBaseFragment implements View.OnClickListener {


    private FrameLayout webContainer;
    private WebView webView;
    private RelativeLayout reloadWebviewRl;
    private TextView reloadBtn;
    private boolean isSuccess = false;
    private boolean isError = false;
    private boolean isFirstFinish=true;
    private WeakHandler handler;

    public static MallFragment newInstance() {
        MallFragment mallFragment = new MallFragment();
        return mallFragment;
    }


    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_mall;
    }


    @Override
    protected void initView(View view) {
        showProgressDialog();
        handler = new WeakHandler();
        webContainer = (FrameLayout) view.findViewById(R.id.web_frame);
        reloadWebviewRl = (RelativeLayout) view.findViewById(R.id.reload_webview_rl);
        reloadBtn = (TextView) view.findViewById(R.id.reload_btn);

    }

    /**
     * @des: js交互
     * @params:
     * @return:
     */
    public class JSCallAndroidObject {
        private String TAG = JSCallAndroidObject.class.getSimpleName();


        @JavascriptInterface
        public String jsCallAndroidTitle(String title, String url) {
            Intent intent = new Intent(getActivity(), WebViewActivity.class);
            intent.putExtra("title", title);
            intent.putExtra("url", url);
            ActivityUtils.startActivity(intent);

            return "from Android";
        }
    }


    private void registerEvent() {
        if (NetworkUtils.isConnected()) {
            reloadWebviewRl.setVisibility(View.GONE);
        } else {
            reloadWebviewRl.setVisibility(View.VISIBLE);
        }
        webView = new WebView(getActivity());
        WebSettings settings = webView.getSettings();
        settings.setDomStorageEnabled(true);
        //解决一些图片加载问题
        settings.setJavaScriptEnabled(true);
        settings.setBlockNetworkImage(false);
        webView.setWebViewClient(webClient);
        webView.setWebChromeClient(new WebChromeClient() {

            @Override
            public void onProgressChanged(WebView view, int progress) {
                //当进度走到100的时候做自己的操作，我这边是弹出dialog
                if (progress == 100) {
                    hideProgressDialog();
                }
            }
        });
        webContainer.addView(webView);
        webView.addJavascriptInterface(new JSCallAndroidObject(), "jsCallAndroidObject");
        webView.loadUrl("https://www.czur.com/cn/shop");
        reloadBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                isFirstFinish=true;
                showProgressDialog();
                webView.reload();
            }
        });
    }

    /***
     * 设置Web视图的方法
     */
    private WebViewClient webClient = new WebViewClient() {
        //处理网页加载失败时
        @Override
        public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
            super.onReceivedError(view, request, error);
            reloadProgress();
            isError = true;
            isSuccess = false;
            reloadWebviewRl.setVisibility(View.VISIBLE);
            webContainer.setVisibility(View.GONE);
        }

        @Override
        public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
            super.onReceivedError(view, errorCode, description, failingUrl);
            reloadProgress();
            isError = true;
            isSuccess = false;
            reloadWebviewRl.setVisibility(View.VISIBLE);
            webContainer.setVisibility(View.GONE);
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            reloadProgress();
            if(!isFirstFinish) {
                return;
            }
            isFirstFinish = false;

            if (!isError) {
                isSuccess = true;
                //回调成功后的相关操作
                reloadWebviewRl.setVisibility(View.GONE);
                webContainer.setVisibility(View.VISIBLE);
            } else {
                isError = false;
                reloadWebviewRl.setVisibility(View.VISIBLE);
                webContainer.setVisibility(View.GONE);
            }
        }

        @Override
        public boolean shouldOverrideUrlLoading(WebView view, String url) {
            Log.d("webview", "url: " + url);
            view.loadUrl(url);
            return true;
        }
    };

    private void reloadProgress() {
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                hideProgressDialog();
            }
        }, 600);
    }


    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        registerEvent();
    }


    public boolean onKeyDown(int keyCode, KeyEvent event) {

        if (webView.canGoBack()) {
            if ((keyCode == KeyEvent.KEYCODE_BACK)) {
                webView.goBack(); //goBack()表示返回WebView的上一页面
            }
            return true;
        } else {
            return false;
        }


    }

    @Override
    public void onPause() {
        super.onPause();

    }

    @Override
    public void onResume() {
        super.onResume();


    }


    @Override
    public void onClick(View v) {
        final int id = v.getId();
        switch (id) {


            default:
                break;
        }
    }


    @Override
    public void onDestroy() {
        if (webView != null) {
            webView.loadDataWithBaseURL(null, "", "text/html", "utf-8", null);
            webView.setTag(null);
            webView.clearHistory();

            ((ViewGroup) webView.getParent()).removeView(webView);
            webView.destroy();
            webView = null;
        }
        super.onDestroy();

    }
}








