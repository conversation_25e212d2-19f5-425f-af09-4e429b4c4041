package com.czur.cloud.util

import android.app.Notification
import android.app.job.JobScheduler
import android.content.Context
import android.os.Build
import androidx.annotation.RequiresApi
import com.czur.czurutils.log.logI
import com.czur.czurutils.log.logE

/**
 * Android 16 适配工具类
 * 处理Android 16新特性和行为变更
 */
object Android16Utils {
    private const val TAG = "Android16Utils"

    /**
     * 检查当前是否运行在Android 16或更高版本
     */
    fun isAndroid16OrHigher(): Boolean {
        return Build.VERSION.SDK_INT >= 36 // Android 16 API Level 36
    }

    /**
     * 检查是否支持Android 16的新版本判断API
     */
    fun isSupportNewVersionApi(): Boolean {
        return isAndroid16OrHigher()
    }

    /**
     * 获取精确的SDK版本信息（Android 16新增）
     */
    @RequiresApi(36)
    fun getFullSdkVersion(): Int {
        return try {
            if (isAndroid16OrHigher()) {
                Android16ApiCompat.getSdkIntFull()
            } else {
                Build.VERSION.SDK_INT
            }
        } catch (e: Exception) {
            logE("$TAG.getFullSdkVersion error: ${e.message}")
            Build.VERSION.SDK_INT
        }
    }

    /**
     * 获取Minor Release版本号（Android 16新增）
     */
    @RequiresApi(36)
    fun getMinorSdkVersion(): Int {
        return try {
            if (isAndroid16OrHigher()) {
                Android16ApiCompat.getMinorSdkVersion(36)
            } else {
                0
            }
        } catch (e: Exception) {
            logE("$TAG.getMinorSdkVersion error: ${e.message}")
            0
        }
    }

    /**
     * 检查设备是否启用了高级保护模式（Android 16新增）
     */
    fun isAdvancedProtectionEnabled(context: Context): Boolean {
        return try {
            if (isAndroid16OrHigher()) {
                Android16ApiCompat.isAdvancedProtectionEnabled(context)
            } else {
                false
            }
        } catch (e: Exception) {
            logE("$TAG.isAdvancedProtectionEnabled error: ${e.message}")
            false
        }
    }

    /**
     * 检查设备是否支持动态刷新率（Android 16新增）
     */
    fun hasAdaptiveRefreshRateSupport(context: Context): Boolean {
        return try {
            if (isAndroid16OrHigher()) {
                Android16ApiCompat.hasAdaptiveRefreshRateSupport(context)
            } else {
                false
            }
        } catch (e: Exception) {
            logE("$TAG.hasAdaptiveRefreshRateSupport error: ${e.message}")
            false
        }
    }

    /**
     * 获取建议的刷新率（Android 16新增）
     */
    fun getSuggestedFrameRate(context: Context, category: Int): Float {
        return try {
            if (isAndroid16OrHigher()) {
                Android16ApiCompat.getSuggestedFrameRate(context, category)
            } else {
                60.0f
            }
        } catch (e: Exception) {
            logE("$TAG.getSuggestedFrameRate error: ${e.message}")
            60.0f
        }
    }

    /**
     * 检查是否需要16KB页大小适配
     */
    fun shouldAdapt16KBPageSize(): Boolean {
        return isAndroid16OrHigher()
    }

    /**
     * 创建进度样式通知（Android 16新增）
     * 注意：由于Android 16的ProgressStyle API可能还未公开，此方法返回标准进度通知
     */
    @RequiresApi(36)
    fun createProgressStyleNotification(
        context: Context,
        channelId: String,
        title: String,
        progress: Int,
        maxProgress: Int = 1000,
        segments: List<Pair<Int, Int>>? = null, // length, color
        points: List<Pair<Int, Int>>? = null, // position, color
        trackerIconRes: Int? = null
    ): Notification? {
        return try {
            if (!isAndroid16OrHigher()) return null

            Android16ApiCompat.createProgressStyleNotification(
                context, channelId, title, progress, maxProgress, segments, points, trackerIconRes
            )
        } catch (e: Exception) {
            logE("$TAG.createProgressStyleNotification error: ${e.message}")
            null
        }
    }

    /**
     * 检查JobScheduler任务失败的所有原因（Android 16新增）
     */
    @RequiresApi(36)
    fun getPendingJobReasons(context: Context, jobId: Int): IntArray {
        return try {
            if (!isAndroid16OrHigher()) return intArrayOf()

            Android16ApiCompat.getPendingJobReasons(context, jobId)
        } catch (e: Exception) {
            logE("$TAG.getPendingJobReasons error: ${e.message}")
            intArrayOf()
        }
    }

    /**
     * 检查是否需要适配自适应布局
     */
    fun shouldAdaptAdaptiveLayout(): Boolean {
        return isAndroid16OrHigher()
    }

    /**
     * 检查是否需要适配预测性返回手势
     */
    fun shouldAdaptPredictiveBack(): Boolean {
        return isAndroid16OrHigher()
    }

    /**
     * 记录Android 16适配信息
     */
    fun logAndroid16Info(context: Context) {
        if (isAndroid16OrHigher()) {
            logI("$TAG.Android16Info:")
            logI("$TAG.SDK_INT: ${Build.VERSION.SDK_INT}")
            logI("$TAG.FullSdkVersion: ${getFullSdkVersion()}")
            logI("$TAG.MinorSdkVersion: ${getMinorSdkVersion()}")
            logI("$TAG.AdvancedProtectionEnabled: ${isAdvancedProtectionEnabled(context)}")
            logI("$TAG.AdaptiveRefreshRateSupport: ${hasAdaptiveRefreshRateSupport(context)}")
            logI("$TAG.SuggestedFrameRate: ${getSuggestedFrameRate(context, 0)}")
            logI("$TAG.ShouldAdapt16KBPageSize: ${shouldAdapt16KBPageSize()}")
            logI("$TAG.ShouldAdaptAdaptiveLayout: ${shouldAdaptAdaptiveLayout()}")
            logI("$TAG.ShouldAdaptPredictiveBack: ${shouldAdaptPredictiveBack()}")
        } else {
            logI("$TAG.Current Android version: ${Build.VERSION.SDK_INT}, Android 16 features not available")
        }
    }
}
