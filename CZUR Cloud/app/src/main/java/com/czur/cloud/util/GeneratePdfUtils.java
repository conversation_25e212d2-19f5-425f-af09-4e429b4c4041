package com.czur.cloud.util;

import android.app.Activity;
import android.util.Log;

import com.blankj.utilcode.util.FileUtils;
import com.czur.cloud.common.CZURConstants;
import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfPageEventHelper;
import com.itextpdf.text.pdf.PdfWriter;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * Created by Yz on 2018/4/8.
 * Email：<EMAIL>
 */
public class GeneratePdfUtils {
    private String path;
    private List<String> paths;
    private String pdfName;
    private Activity activity;
    private long beginTime;

    public GeneratePdfUtils(Activity activity, String path, List<String> paths, String pdfName) {
        this.activity = activity;
        this.path = path;
        this.paths = paths;
        this.pdfName = pdfName;

    }

    public void createPdf() {
        long totalTime = 0;
        String filePath = "";
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (onGeneratePdfListener != null) {
                    onGeneratePdfListener.onStart();
                }
            }
        });

        try {
            beginTime = System.currentTimeMillis();
            Document document = new Document();
            if (FileUtils.createOrExistsDir(path)) {
                filePath = path + pdfName + CZURConstants.PDF;
            }
            PdfWriter writer = PdfWriter.getInstance(document, new FileOutputStream(filePath));
            Background event = new Background();
            writer.setPageEvent(event);
            document.open();
            for (int i = 0; i < paths.size(); i++) {
                document.newPage();
                Image img = Image.getInstance(paths.get(i));
                img.scaleToFit(PageSize.A4.getWidth() - 10, PageSize.A4.getHeight() - 10);
                img.setAbsolutePosition((PageSize.A4.getWidth() - img.getScaledWidth()) / 2, (PageSize.A4.getHeight() - img.getScaledHeight()) / 2);
                document.add(img);
                final long l = System.currentTimeMillis() - beginTime;
                totalTime += l;
                final int finalI = i;
                activity.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (onGeneratePdfListener != null) {
                            onGeneratePdfListener.onGenerate(finalI + 1, l);
                        }

                    }
                });


            }
            document.close();
            final long finalTotalTime = totalTime;
            final String finalFilePath = filePath;
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (onGeneratePdfListener != null) {
                        onGeneratePdfListener.onFinish(finalTotalTime, finalFilePath,pdfName);
                    }
                }
            });

        } catch (IOException e) {
            e.printStackTrace();
            Log.i("xxx", "IOException error：" + e);
        } catch (DocumentException e) {
            e.printStackTrace();
            Log.i("xxx", "DocumentException error：" + e);
        }
    }

    private class Background extends PdfPageEventHelper {
        @Override
        public void onEndPage(PdfWriter writer, Document document) {
            //color
            PdfContentByte canvas = writer.getDirectContentUnder();
            Rectangle rect = document.getPageSize();
            canvas.setColorFill(BaseColor.WHITE);
            canvas.rectangle(rect.getLeft(), rect.getBottom(), rect.getWidth(), rect.getHeight());
            canvas.fill();

            //border
            PdfContentByte canvasBorder = writer.getDirectContent();
            Rectangle rectBorder = document.getPageSize();
            rectBorder.setBorder(Rectangle.BOX);
            rectBorder.setBorderWidth(15);
            rectBorder.setBorderColor(BaseColor.WHITE);
            rectBorder.setUseVariableBorders(true);
            canvasBorder.rectangle(rectBorder);
        }
    }

    private OnGeneratePdfListener onGeneratePdfListener;

    public void setOnGeneratePdfListener(OnGeneratePdfListener onGeneratePdfListener) {
        this.onGeneratePdfListener = onGeneratePdfListener;
    }

    public interface OnGeneratePdfListener {
        void onStart();

        void onGenerate(int progress, long time);

        void onFinish(long totalTime, String pdfPath,String pdfName);
    }
}
