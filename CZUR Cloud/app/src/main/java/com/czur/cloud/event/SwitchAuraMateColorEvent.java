package com.czur.cloud.event;

import com.czur.cloud.model.AuraMateColorModel;

public class SwitchAuraMateColorEvent extends BaseEvent {
    private AuraMateColorModel auraMateColorModel;
    private boolean isFolder;

    private int position;

    public SwitchAuraMateColorEvent(EventType eventType, int position, boolean isFolder,  AuraMateColorModel auraMateColorModel) {
        super(eventType);
        this.position = position;
        this.isFolder = isFolder;
        this.auraMateColorModel = auraMateColorModel;
    }
    public boolean isFolder() {
        return isFolder;
    }

    public AuraMateColorModel getAuraMateColorModel() {
        return auraMateColorModel;
    }

    public int getPosition() {
        return position;
    }


    @Override
    public boolean match(Object obj) {
        return true;
    }
}
