package com.czur.cloud.ui.user;

import static com.czur.cloud.common.CZURConstants.PWD_MIN_LENGTH;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.KeyboardUtils;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.common.MD5Utils;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.ProgressButton;
import com.czur.cloud.util.StringToolsUtils;

/**
 * Created by Yz on 2018/3/7.
 * Email：<EMAIL>
 */

public class UserChangePasswordActivity extends BaseActivity implements View.OnClickListener {

    private ImageView userBackBtn;
    private TextView userTitle;
    private EditText userOldPasswordEdt;
    private EditText userFirstPasswordEdt;
    private ProgressButton userChangePasswordBtn;

    private boolean currentPasswordHasContent = false;
    private boolean firstPasswordHasContent = false;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private long currentTime;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_user_change_password);
        initComponent();
        registerEvent();
    }

    private void initComponent() {


        userBackBtn = (ImageView) findViewById(R.id.user_back_btn);
        userTitle = (TextView) findViewById(R.id.user_title);
        userOldPasswordEdt = (EditText) findViewById(R.id.user_old_password_edt);
        userFirstPasswordEdt = (EditText) findViewById(R.id.user_first_password_edt);
        userChangePasswordBtn = (ProgressButton) findViewById(R.id.user_change_password_btn);
        httpManager = HttpManager.getInstance();
        userPreferences = UserPreferences.getInstance(this);
        userTitle.setText(R.string.user_change_password);


    }

    private void registerEvent() {
        userOldPasswordEdt.addTextChangedListener(currentPswTextWatcher);
        userFirstPasswordEdt.addTextChangedListener(firstPswTextWatcher);
        userBackBtn.setOnClickListener(this);
        userChangePasswordBtn.setOnClickListener(this);
        userChangePasswordBtn.setSelected(false);
        userChangePasswordBtn.setClickable(false);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.user_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.user_change_password_btn:
                updatePassword();
                break;
            default:
                break;
        }
    }

    /**
     * @des: 修改密码
     * @params:
     * @return:
     */

    private void updatePassword() {
        String current = userOldPasswordEdt.getText().toString();
        final String nPwd = userFirstPasswordEdt.getText().toString();

        if (current.length() <= 5 ){
            showMessage(R.string.toast_old_pwe_fail);
        }else if ((nPwd.length() <= PWD_MIN_LENGTH) ||
                (!StringToolsUtils.isLetterDigit(nPwd))) {
            showMessage(R.string.toast_pwd_length);
        } else if (current.equals(nPwd)) {
            showMessage(R.string.toast_input_new_pwd);
        } else {
            currentTime = System.currentTimeMillis();
            KeyboardUtils.hideSoftInput(this);
            httpManager.requestPassport().updatePwd(userPreferences.getIMEI(), CZURConstants.CLOUD_ANDROID,
                    userPreferences.getChannel(), userPreferences.getUserId(), userPreferences.getToken(),
                    userPreferences.getUserId(), MD5Utils.md5(current), MD5Utils.md5(nPwd), String.class, new MiaoHttpManager.Callback<String>() {
                        @Override
                        public void onStart() {
                            userChangePasswordBtn.startDelayLoading(UserChangePasswordActivity.this);
                        }

                        @Override
                        public void onResponse(MiaoHttpEntity<String> entity) {
                            successDelay(entity, nPwd);
                        }

                        @Override
                        public void onFailure(MiaoHttpEntity<String> entity) {
                            if (entity.getCode() == MiaoHttpManager.STATUS_OLD_PWD_FAIL) {
                                failedDelay(R.string.toast_old_pwe_fail);
                            } else {
                                failedDelay(R.string.request_failed_alert);
                            }

                        }

                        @Override
                        public void onError(Exception e) {
                            failedDelay(R.string.request_failed_alert);
                        }
                    });
        }
    }

    public void changeSuccess(MiaoHttpEntity<String> entity, String nPwd) {
        userPreferences.setLoginPassword(nPwd);
        showMessage(R.string.toast_update_pwd_success);
        ActivityUtils.finishActivity(UserChangePasswordActivity.this);
    }

    private void failedDelay(final int failedText) {

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    long sleepTime;
                    if (System.currentTimeMillis() - currentTime < 1000) {
                        sleepTime = 1000 - (System.currentTimeMillis() - currentTime);
                    } else {
                        sleepTime = 1;
                    }
                    Thread.sleep(sleepTime);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            showMessage(failedText);
                            userChangePasswordBtn.stopLoading();
                        }
                    });
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }


    private void successDelay(final MiaoHttpEntity<String> entity, final String nPwd) {

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    long sleepTime;
                    if (System.currentTimeMillis() - currentTime < 1000) {
                        sleepTime = 1000 - (System.currentTimeMillis() - currentTime);
                    } else {
                        sleepTime = 1;
                    }
                    Thread.sleep(sleepTime);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            changeSuccess(entity, nPwd);
                        }
                    });
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }

    private TextWatcher currentPswTextWatcher = new TextWatcher() {


        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            // 输入的内容变化的监听
            if (s.length() > 0) {
                currentPasswordHasContent = true;
            } else {
                currentPasswordHasContent = false;
            }

            checkUpdatePasswordToClick();
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count,
                                      int after) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            // 输入后的监听
            if (s.length() > 0) {
                currentPasswordHasContent = true;
            } else {
                currentPasswordHasContent = false;
            }

            checkUpdatePasswordToClick();
        }
    };
    private TextWatcher firstPswTextWatcher = new TextWatcher() {


        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            // 输入的内容变化的监听
            if (s.length() > 0) {
                firstPasswordHasContent = true;
            } else {
                firstPasswordHasContent = false;
            }

            checkUpdatePasswordToClick();
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count,
                                      int after) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            // 输入后的监听
            if (s.length() > 0) {
                firstPasswordHasContent = true;
            } else {
                firstPasswordHasContent = false;
            }
            checkUpdatePasswordToClick();
        }
    };


    /**
     * @des: 检查注册按钮是否可以点击
     * @params:
     * @return:
     */

    private void checkUpdatePasswordToClick() {

        if (firstPasswordHasContent && currentPasswordHasContent) {
            userChangePasswordBtn.setSelected(true);
            userChangePasswordBtn.setClickable(true);
        } else {
            userChangePasswordBtn.setSelected(false);
            userChangePasswordBtn.setClickable(false);
        }
    }
}
