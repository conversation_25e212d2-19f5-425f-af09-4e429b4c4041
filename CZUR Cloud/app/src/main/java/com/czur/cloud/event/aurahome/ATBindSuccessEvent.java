package com.czur.cloud.event.aurahome;


import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.netty.bean.ReceivedMsgBodyBean;

/**
 * 上线通知
 */
public class ATBindSuccessEvent extends BaseEvent {

    private String deviceUdid;

    private ReceivedMsgBodyBean.BodyBean.DataBean.DeviceStatusBean statusBean;

    public ATBindSuccessEvent(EventType eventType, String deviceUdid, ReceivedMsgBodyBean.BodyBean.DataBean.DeviceStatusBean statusBean) {
        super(eventType);
        this.deviceUdid = deviceUdid;
        this.statusBean = statusBean;
    }

    public ReceivedMsgBodyBean.BodyBean.DataBean.DeviceStatusBean getStatusBean() {
        return statusBean;
    }

    public String getDeviceUdid() {
        return deviceUdid;
    }
    @Override
    public boolean match(Object obj) {
        return true;
    }
}
