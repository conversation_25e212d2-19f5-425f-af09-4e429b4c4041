package com.czur.cloud.ui.books;

import android.content.DialogInterface;
import android.os.Bundle;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.EditTagAdapter;
import com.czur.cloud.entity.realm.PageEntity;
import com.czur.cloud.entity.realm.TagEntity;
import com.czur.cloud.event.ChangeTagEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.popup.AddTagPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.util.EtUtils;
import com.czur.cloud.util.validator.Validator;

import org.greenrobot.eventbus.EventBus;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.UUID;

import io.realm.Realm;
import io.realm.RealmResults;
import io.realm.Sort;

/**
 * Created by Yz on 2018/2/26.
 * Email：<EMAIL>
 */

public class EditTagActivity extends BaseActivity implements View.OnClickListener {


    private ImageView normalBackBtn;
    private TextView normalTitle;
    private RecyclerView tagRecyclerView;
    private TextView cancelTagBtn;
    private Realm realm;
    private EditTagAdapter tagAdapter;
    private EditText dialogEdt;
    private boolean isPreview;
    private String pageId;
    private RealmResults<TagEntity> tagEntities;
    private ArrayList<String> pageIds;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_edit_tag);
        initComponent();
        registerEvent();
    }


    private void initComponent() {
        realm = Realm.getDefaultInstance();
        normalBackBtn = (ImageView) findViewById(R.id.normal_back_btn);
        normalTitle = (TextView) findViewById(R.id.normal_title);
        tagRecyclerView = (RecyclerView) findViewById(R.id.tag_recyclerView);
        cancelTagBtn = (TextView) findViewById(R.id.cancel_tag_btn);
        normalTitle.setText(getString(R.string.choose_tag));
        isPreview = getIntent().getBooleanExtra("isPreview", false);
        pageId = getIntent().getStringExtra("pageId");
        pageIds = getIntent().getStringArrayListExtra("pageIds");

        tagEntities = realm.where(TagEntity.class).equalTo("isDelete", 0).distinct("tagName").sort("createTime", Sort.ASCENDING).findAll();


        realm.executeTransaction(new Realm.Transaction() {
            @Override
            public void execute(Realm realm) {
                if (tagEntities.size() > 0) {
                    if (isPreview) {
                        PageEntity pageEntity = realm.where(PageEntity.class).equalTo("pageId", pageId).findFirst();
                        String tagId = pageEntity.getTagId();
                        for (int i = 0; i < tagEntities.size(); i++) {
                            if (tagEntities.get(i).getTagId().equals(tagId)) {
                                tagEntities.get(i).setIsSelf(1);
                                cancelTagBtn.setVisibility(View.VISIBLE);
                            } else {
                                tagEntities.get(i).setIsSelf(0);
                            }
                        }
                    } else {
                        cancelTagBtn.setVisibility(View.VISIBLE);
                        for (int i = 0; i < tagEntities.size(); i++) {
                            tagEntities.get(i).setIsSelf(0);
                        }
                    }
                }


            }
        });


        tagAdapter = new EditTagAdapter(this, tagEntities, false, realm);
        tagAdapter.setAddTagListener(addTagListener);
        tagAdapter.setOnTagItemClickListener(onTagItemClickListener);
        tagRecyclerView.setAdapter(tagAdapter);
        tagRecyclerView.setHasFixedSize(true);
        tagRecyclerView.setLayoutManager(new GridLayoutManager(this, 2));

    }

    private void registerEvent() {
        normalBackBtn.setOnClickListener(this);
        cancelTagBtn.setOnClickListener(this);
    }

    private EditTagAdapter.AddTagListener addTagListener = new EditTagAdapter.AddTagListener() {
        @Override
        public void onAddTagClickListener(int position) {
            showAddTagDialog(true);
        }
    };


    private EditTagAdapter.OnTagItemClickListener onTagItemClickListener = new EditTagAdapter.OnTagItemClickListener() {
        @Override
        public void onTagEntityClick(final TagEntity tagEntity, int position) {
            realm.executeTransaction(new Realm.Transaction() {
                @Override
                public void execute(Realm realm) {
                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
                    String curDate = formatter.format(new Date(System.currentTimeMillis()));
                    if (isPreview) {
                        PageEntity pageEntity = realm.where(PageEntity.class).equalTo("pageId", EditTagActivity.this.pageId).findFirst();
                        pageEntity.setIsDirty(pageEntity.getIsDirty() == 1 ? 1 : 2);
                        pageEntity.setUpdateTime(curDate);
                        pageEntity.setTagId(tagEntity.getTagId());
                        pageEntity.setTagName(tagEntity.getTagName());

                        EventBus.getDefault().post(new ChangeTagEvent(EventType.ADD_TAG));
                        ActivityUtils.finishActivity(EditTagActivity.this);

                    } else {
                        //给多选的书页添加标签
                        for (String pageId : pageIds) {
                            PageEntity pageEntity = realm.where(PageEntity.class).equalTo("pageId", pageId).findFirst();
                            pageEntity.setIsDirty(pageEntity.getIsDirty() == 1 ? 1 : 2);
                            pageEntity.setUpdateTime(curDate);
                            pageEntity.setTagId(tagEntity.getTagId());
                            pageEntity.setTagName(tagEntity.getTagName());
                        }
                        EventBus.getDefault().post(new ChangeTagEvent(EventType.ADD_TAGS));
                        ActivityUtils.finishActivity(EditTagActivity.this);
                    }
                    startAutoSync();

                }
            });


        }
    };

    /**
     * @des: 显示添加Dialog
     * @params:
     * @return:
     */

    private void showAddTagDialog(boolean isAdd) {
        AddTagPopup.Builder builder = new AddTagPopup.Builder(EditTagActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getString(R.string.add_tag));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                realm.executeTransaction(new Realm.Transaction() {
                    @Override
                    public void execute(Realm realm) {
                        if (!EtUtils.containsEmoji(dialogEdt.getText().toString())) {
                            if (Validator.isNotEmpty(dialogEdt.getText().toString())) {
                                TagEntity tagEntity = new TagEntity();
                                tagEntity.setIsSelf(0);
                                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
                                String curDate = formatter.format(new Date(System.currentTimeMillis()));
                                tagEntity.setCreateTime(curDate);
                                tagEntity.setUpdateTime(curDate);
                                tagEntity.setIsDirty(1);
                                tagEntity.setTagId(UUID.randomUUID().toString());
                                tagEntity.setTagName(dialogEdt.getText().toString());
                                realm.copyToRealmOrUpdate(tagEntity);
                                startAutoSync();
                            } else {
                                showMessage(R.string.tag_should_not_be_empty);
                            }

                        } else {
                            showMessage(R.string.nickname_toast_symbol);
                        }

                    }
                });

                dialog.dismiss();

            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });

        AddTagPopup commonPopup = builder.create();
        dialogEdt = (EditText) commonPopup.getWindow().findViewById(R.id.edt);
        commonPopup.show();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.normal_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.cancel_tag_btn:
                realm.executeTransaction(new Realm.Transaction() {
                    @Override
                    public void execute(Realm realm) {
                        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
                        String curDate = formatter.format(new Date(System.currentTimeMillis()));

                        if (isPreview) {
                            PageEntity pageEntity = realm.where(PageEntity.class).equalTo("pageId", EditTagActivity.this.pageId).findFirst();
                            pageEntity.setUpdateTime(curDate);
                            pageEntity.setIsDirty(pageEntity.getIsDirty() == 1 ? 1 : 2);
                            pageEntity.setTagId("");
                            pageEntity.setTagName("");

                            EventBus.getDefault().post(new ChangeTagEvent(EventType.DELETE_TAG));
                        } else {
                            for (String pageId : pageIds) {
                                PageEntity pageEntity = realm.where(PageEntity.class).equalTo("pageId", pageId).equalTo("isDelete", 0).findFirst();
                                if (pageEntity != null) {
                                    pageEntity.setIsDirty(pageEntity.getIsDirty() == 1 ? 1 : 2);
                                    pageEntity.setUpdateTime(curDate);
                                    pageEntity.setTagId("");
                                    pageEntity.setTagName("");
                                }

                            }
                            EventBus.getDefault().post(new ChangeTagEvent(EventType.DELETE_TAGS));
                        }
                        startAutoSync();
                        ActivityUtils.finishActivity(EditTagActivity.this);
                    }
                });

                break;
            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (realm != null) {
            realm.close();
        }
    }
}
