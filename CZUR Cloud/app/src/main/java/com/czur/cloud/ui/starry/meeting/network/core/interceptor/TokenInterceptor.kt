package com.czur.cloud.ui.starry.meeting.network.core.interceptor

import android.util.Log
import com.czur.cloud.BuildConfig
import com.czur.cloud.common.CZURConstants
import com.czur.cloud.common.MD5Utils
import com.czur.cloud.event.EventType
import com.czur.cloud.event.StarryCommonEvent
import com.czur.cloud.preferences.UserPreferences
import com.czur.cloud.ui.starry.meeting.common.UserHandler
import com.czur.cloud.ui.starry.meeting.network.core.common.ResCode.RESULT_CODE_INVALID_TOKEN
import com.czur.cloud.ui.starry.meeting.network.core.common.ResCode.RESULT_CODE_OTHER_DEVICE_LOGIN
import com.czur.czurutils.log.logI
import kotlinx.coroutines.runBlocking
import okhttp3.FormBody
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject

/**
 * Created by 陈丰尧 on 2021/7/15
 * 如果Token过期,自动刷新Token
 */
class TokenInterceptor : Interceptor {

    companion object {
        private const val TAG = "TokenInterceptor"
        private const val KEY_CODE = "code"
        private const val LOGIN_URL = "v1/login"
        private const val TOKEN_URL = "v1/refresh/token"
        private const val LOGIN_HOST = BuildConfig.PASSPORT_URL
    }


    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val response = chain.proceed(request)
        val responseStr = response.peekBody(1024L * 1024L * 1024L).string()

        // 是否被其他设备顶掉
        // Jason
        return when (getResCode(responseStr)) {
            RESULT_CODE_OTHER_DEVICE_LOGIN -> {
                Log.i("JasonTest", "被其他用户顶掉")
//                NoticeHandler.sendMessage(MsgType(USER_INFO, USER_INFO_OTHER_DEVICE_LOGIN))
//                LiveDataBus.get().with(StarryConstants.STARRY_OTHER_DEVICE_LOGIN).value = true
                EventBus.getDefault()
                    .post(StarryCommonEvent(EventType.STARRY_NOTICE_OTHER_DEVICE_LOGIN, ""))

                response
            }
            RESULT_CODE_INVALID_TOKEN -> {
                EventBus.getDefault()
                    .post(StarryCommonEvent(EventType.STARRY_RESULT_CODE_INVALID_TOKEN, ""))
                refreshToken(response, request, chain)
            }
            else -> response // 不需要更新Token, 返回之前的结果
        }

    }

    /**
     * 刷新Token, 刷新的方式是利用保存的用户名和密码,进行重新登陆
     */
    private fun refreshToken(
        response: Response,
        request: Request,
        chain: Interceptor.Chain
    ): Response {
        // 需要刷新Token
        val userName = UserPreferences.getInstance().loginUserName ?: ""
        val pwd = UserPreferences.getInstance().loginPassword ?: ""
        if (userName.length < 6 || pwd.isBlank()) {
            // 没有用户名/ 密码 登录不了
            // 正常情况下, 不会存在有token 但是账号密码没有的情况
            logI("${TAG}.没有用户名/密码")
            return response
        }

        // 可以重新登录,刷新Token
        val loginRequest = mkReLoginRequest(userName, pwd)
        val loginResponse = chain.proceed(loginRequest)

        // 检查Token是否刷新成功
        val token = loginResponse.body.use {
            getTokenFromJson(it?.string() ?: "{}")
        }

        if (token.isNotEmpty()) {
            // token刷新成功,更新Token
            logI("新Token:${token}")
            UserHandler.token = token
            UserPreferences.getInstance().token = token
        } else {
            // token刷新失败, 将之前结果直接返回
            // 如果是网络问题, 则会抛出异常
            // 没有抛出异常,并且token刷新失败, 只能是本地保存的账号无法登录
            // 所以清理本地的登录信息
            runBlocking {
                logI("更新Token失败, 自动退出登录")
                UserHandler.logout()
            }
            return response
        }

//        logI("Token已更新, 重新请求")
        // 重新请求一遍
        return chain.proceed(request)
    }

    private fun getTokenFromJson(json: String): String {
        return try {
            val jsonObj = JSONObject(json)
            jsonObj.getJSONObject("body").getString("token")
        } catch (e: Exception) {
            ""
        }
    }

    /**
     * 构造激活 Request
     */
    private fun mkReLoginRequest(userName: String, pwd: String): Request {
        val userPreferences = UserPreferences.getInstance()
        val url = LOGIN_HOST + LOGIN_URL

        val formBody = FormBody.Builder()
            .add("username", userName)
            .add("password", MD5Utils.md5(pwd))
            .build()

        val requestBuilder = Request.Builder()

        return requestBuilder
            .header("udid", userPreferences.imei)
            .header("App-Key", CZURConstants.CLOUD_ANDROID)
            .header("Channel", userPreferences.channel)
            .post(formBody)
            .url(url)
            .build()
    }


    /**
     * 获取网络接口的返回code
     * @return 网络接口的Code @see ResCode
     */
    private fun getResCode(responseStr: String): Int {
        if (responseStr == null){
            return -1
        }
        if (responseStr.isBlank()) {
            return -1
        }
        if (responseStr.startsWith("<html>")) {
            return -1
        }
        val json = JSONObject(responseStr)
        return json?.getInt(KEY_CODE) ?: -1
    }

}