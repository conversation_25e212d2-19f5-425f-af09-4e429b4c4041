package com.czur.cloud.ui.auramate;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;

import java.util.Timer;
import java.util.TimerTask;

/**
 * Created by Yz on 2018/6/1.
 * Email：<EMAIL>
 */
public class AuraMateWifiConnectActivity extends AuramateBaseActivity implements View.OnClickListener {
    private ImageView normalBackBtn;
    private TextView nextStepBtn;
    private String wifiName;
    private String wifiPsw;
    private boolean noNeedKey;
    private boolean isChanged;
    private ImageView auraHomeGif3;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_aura_wifi_connect);
        initComponent();
        registerEvent();
    }

    @Override
    protected boolean PCNeedFinish() {
      return  !TextUtils.isEmpty(equipmentId);
    }

    private void initComponent() {
        wifiName = getIntent().getStringExtra("wifiName");
        wifiPsw = getIntent().getStringExtra("wifiPsw");
        noNeedKey = getIntent().getBooleanExtra("noNeedKey", false);
        normalBackBtn = (ImageView) findViewById(R.id.normal_back_btn);
        auraHomeGif3 = (ImageView) findViewById(R.id.aura_home_gif3);
        nextStepBtn = (TextView) findViewById(R.id.next_step_btn);
        Timer mTimer = new Timer();
        isChanged = false;
        mTimer.schedule(timerTask, 0, 200);
        timerTask.run();
        startNetty();
    }

    private void registerEvent() {
        normalBackBtn.setOnClickListener(this);
        nextStepBtn.setOnClickListener(this);
    }

    TimerTask timerTask = new TimerTask() {
        @Override
        public void run() {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    auraHomeGif3.setVisibility(isChanged ? View.VISIBLE : View.GONE);
                    isChanged = !isChanged;
                }
            });
        }
    };

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.next_step_btn:
                Intent intent = new Intent(AuraMateWifiConnectActivity.this, AuraMateWifiQRcodeActivity.class);
                intent.putExtra("wifiName", wifiName);
                intent.putExtra("wifiPsw", wifiPsw);
                intent.putExtra("noNeedKey", noNeedKey);
                intent.putExtra("equipmentId", equipmentId);
                ActivityUtils.startActivity(intent);
                ActivityUtils.finishActivity(this);
                break;
            case R.id.normal_back_btn:
                ActivityUtils.finishToActivity(AuraMateActivity.class, false);
                break;
            default:
                break;
        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        ActivityUtils.finishToActivity(AuraMateWifiHistoryActivity.class, false);
    }

    @Override
    protected void onDestroy() {
        if (timerTask != null) {
            timerTask.cancel();
        }
        super.onDestroy();
    }
}
