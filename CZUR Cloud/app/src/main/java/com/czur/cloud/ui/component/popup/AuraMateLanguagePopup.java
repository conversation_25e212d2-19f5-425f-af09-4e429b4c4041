package com.czur.cloud.ui.component.popup;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.czur.cloud.R;
import com.czur.cloud.util.validator.StringUtils;

/**
 * Created by Yz on 2018/3/20
 * Email：<EMAIL>
 */

public class AuraMateLanguagePopup extends Dialog {
    public static final float DIMMED_OPACITY = 0.2f;

    private boolean isChecked;

    public AuraMateLanguagePopup(Context context) {
        super(context);
    }

    public AuraMateLanguagePopup(Context context, int theme) {
        super(context, theme);
    }

    public boolean isChecked() {
        return this.isChecked;
    }

    public static class Builder {
        private Context context;
        private CloudCommonPopupConstants constants;

        private String message;
        private String title;
        private View contentsView;

        private OnClickListener positiveListener;
        private OnDismissListener onDismissListener;

        public Builder(Context context, CloudCommonPopupConstants constants) {
            this.context = context;
            this.constants = constants;
        }

        public Builder setMessage(String message) {
            this.message = message;
            return this;
        }
        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setContentsView(View contentsView) {
            this.contentsView = contentsView;
            return this;
        }

        public Builder setContentsView(int resource) {
            this.contentsView = LayoutInflater.from(context).inflate(resource, null);
            return this;
        }

        public Builder setOnPositiveListener(OnClickListener positiveListener) {
            this.positiveListener = positiveListener;
            return this;
        }



        public Builder setOnDismissListener(OnDismissListener onDismissListener) {
            this.onDismissListener = onDismissListener;
            return this;
        }


        public AuraMateLanguagePopup create() {
            LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);

            final AuraMateLanguagePopup dialog = new AuraMateLanguagePopup(context, R.style.TransparentProgressDialog);
            View layout = commonCustomPopLayout(inflater, dialog);

            dialog.setContentView(layout);
            dialog.setCanceledOnTouchOutside(true);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            params.dimAmount = DIMMED_OPACITY;

            return dialog;
        }

        private View commonCustomPopLayout(LayoutInflater inflater, final AuraMateLanguagePopup dialog) {
            WindowManager.LayoutParams lp = dialog.getWindow().getAttributes();
            lp.dimAmount = 0.8f;
            dialog.getWindow().setAttributes(lp);
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

            View layout = inflater.inflate(R.layout.dialog_aura_mate_language, null, false);
            ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);
            dialog.addContentView(layout, params);

            TextView title = (TextView) layout.findViewById(R.id.title);
            TextView message = (TextView) layout.findViewById(R.id.message);
            ImageView backBtn = (ImageView) layout.findViewById(R.id.back_btn);
            TextView updateBtn = (TextView) layout.findViewById(R.id.confirm_btn);
            if (StringUtils.isNotEmpty(this.message)) {
                message.setText(this.message);
            }

            if (positiveListener != null) {
                updateBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        positiveListener.onClick(dialog, constants.getPositiveBtn());
                    }
                });
            } else {
                updateBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialog.dismiss();
                    }
                });
            }

            backBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    dialog.dismiss();
                }
            });

            if (onDismissListener != null) {
                dialog.setOnDismissListener(onDismissListener);
            }

            return layout;
        }
    }
}
