package com.czur.cloud.ui.component;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.RectF;
import android.os.Handler;
import android.util.TypedValue;

import com.baoyz.widget.PullRefreshLayout;
import com.baoyz.widget.RefreshDrawable;

/**
 * Created by baoyz on 14/11/2.
 */
public class ETRingDrawable extends RefreshDrawable {

    private static final int MAX_LEVEL = 200;

    private boolean isRunning;
    private RectF mBounds;
    private int mWidth;
    private int mHeight;
    private Paint mPaint;
    private Path mPath;
    private float mAngle;
    private int[] mColorSchemeColors;
    private Handler mHandler = new Handler();
    private int mLevel;
    private float mDegress;
    private Context context;

    public ETRingDrawable(Context context, PullRefreshLayout layout) {
        super(context, layout);
        this.context=context;
        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(dp2px(3));
        mPaint.setStrokeCap(Paint.Cap.ROUND);
        mPath = new Path();
    }

    @Override
    public void setPercent(float percent) {
        mPaint.setColor(Color.WHITE);
        mAngle = 275 * percent;
    }

    @Override
    public void setColorSchemeColors(int[] colorSchemeColors) {
        mColorSchemeColors = colorSchemeColors;
    }

    @Override
    public void offsetTopAndBottom(int offset) {
        invalidateSelf();
    }

    @Override
    public void start() {
        mLevel = 50;
        isRunning = true;
        invalidateSelf();
    }

    private void updateLevel(int level) {
        float percent = level % 50 / 50f;
        mPaint.setColor(Color.WHITE);
        mDegress = 360 * percent;
    }

    @Override
    public void stop() {
        isRunning = false;
        mDegress = 0;
    }

    @Override
    public boolean isRunning() {
        return isRunning;
    }

    @Override
    protected void onBoundsChange(Rect bounds) {
        super.onBoundsChange(bounds);
        mWidth = getRefreshLayout().getFinalOffset();
        mHeight = mWidth;
        mBounds = new RectF(bounds.width() / 2 - mWidth / 2, bounds.top, bounds.width() / 2 + mWidth / 2, bounds.top + mHeight);
        mBounds.inset(dp2px(20), dp2px(20));
    }

    @Override
    public void draw(Canvas canvas) {
        canvas.save();
//        canvas.translate(0, mTop);
        canvas.rotate(mDegress, mBounds.centerX(), mBounds.centerY());
        drawRing(canvas);
        canvas.restore();
        if (isRunning) {
            mLevel = mLevel >= MAX_LEVEL ? 0 : mLevel + 1;
            updateLevel(mLevel);
            invalidateSelf();
        }
    }

    private void drawRing(Canvas canvas) {
        mPath.reset();
        mPath.arcTo(mBounds, 0, mAngle, true);
        canvas.drawPath(mPath, mPaint);
//        canvas.drawArc(mBounds, 270, mAngle, true, mPaint);
    }

    private int dp2px(int dp) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, getContext().getResources().getDisplayMetrics());
    }

}
