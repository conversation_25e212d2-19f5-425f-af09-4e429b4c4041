package com.czur.cloud.ui.starry.meeting.baselib.utils

import android.content.Intent
import android.util.Log
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject

/**
 * Created by 陈丰尧 on 2021/5/14
 */
private const val JSON_HEADER_DIVIDE = "->"
private const val JSON_LINE_FIRST = "║ "

/**
 * 打印Log
 */
fun logcat(
    tag: String,
    msg: String?,
    logLevel: LogLevel = LogLevel.DEBUG,
    tr: Throwable? = null
) {
    printLog(tag, msg, logLevel, tr)
}

//fun logV(
//    tag: String,
//    msg: String?,
//    tr: Throwable? = null
//) = logcat(tag, msg, logLevel = LogLevel.VERBOSE, tr = tr)

//fun logD(
//    tag: String,
//    msg: String?,
//    tr: Throwable? = null
//) = logcat(tag, msg, tr = tr)

//fun logI(
//    tag: String,
//    msg: String?,
//    tr: Throwable? = null
//) = logcat(tag, msg, logLevel = LogLevel.INFO, tr = tr)

//fun logW(
//    tag: String,
//    msg: String?,
//    tr: Throwable? = null
//) = logcat(tag, msg, logLevel = LogLevel.WARN, tr = tr)

//fun logE(
//    tag: String, msg: String?,
//    tr: Throwable? = null
//) = logcat(tag, msg, logLevel = LogLevel.ERROR, tr = tr)

//fun logWTF(
//    tag: String, msg: String?,
//    tr: Throwable? = null
//) = logcat(tag, msg, logLevel = LogLevel.ASSERT, tr = tr)

/**
 * 打印JsonLog信息
 */
//fun logJson(
//    tag: String,
//    jsonStr: String,
//    headerMsg: String? = null,
//    logLevel: LogLevel = LogLevel.DEBUG
//) {
//    printLine(tag, true, logLevel)
//    // 打印headerMsg
//    headerMsg?.let {
//        val headerLines = it.lines()
//        headerLines.forEachIndexed { index, headerLine ->
//            if (index == headerLines.size - 1) {
//
//                printLog(tag, "$JSON_LINE_FIRST$headerLine $JSON_HEADER_DIVIDE", logLevel)
//            } else {
//                printLog(tag, "$JSON_LINE_FIRST$headerLine", logLevel)
//            }
//        }
//    }
//
//    val formatJson = formatJsonStr(jsonStr)
//    formatJson.lines().forEach { line ->
//        if (line.isNotBlank()) {
//            val formatLine = line.replace("\\/", "/")
//            printLog(tag, JSON_LINE_FIRST + formatLine, logLevel)
//        }
//    }
//
//    printLine(tag, false, logLevel)
//}

/**
 * 输出Intent
 */
//fun logIntent(
//    tag: String,
//    intent: Intent
//) {
//    logD(tag, "-----logIntent----")
//    logD(tag, "intent:${intent}")
//    logD(tag, "action:${intent.action}")
//    logD(tag, "data:${intent.dataString}")
//    val extras = intent.extras
//    if (extras == null) {
//        logD(tag, "intent不含extras")
//    } else {
//        logD(tag, "extras->")
//        extras.keySet().forEach {
//            logD(tag, "    ${it}-${extras[it]}")
//        }
//    }
//    logD(tag, "-----logIntent----")
//}

//fun logStackTrace(
//    tag: String
//) = logD(tag, Log.getStackTraceString(Throwable()))

/**
 * 打印多行日志
 */
fun logLines(
    tag: String,
    headerMsg: String? = null,
    logLevel: LogLevel = LogLevel.DEBUG,
    vararg lines: String
) {
    printLine(tag, true, logLevel)
    // 打印headerMsg
    headerMsg?.let {
        printLog(tag, "$JSON_LINE_FIRST$it $JSON_HEADER_DIVIDE", logLevel)
    }
    // 打印body
    lines.forEach { line ->
        line.lines().filter { it.isNotEmpty() }.forEach { single ->

            printLog(tag, JSON_LINE_FIRST + single, logLevel)
        }
    }
    printLine(tag, false, logLevel)
}

private fun formatJsonStr(jsonStr: String): String {
    if (jsonStr.isEmpty()) {
        return "json是空字符串"
    }
    return try {
        if (jsonStr.startsWith("{")) {
            JSONObject(jsonStr).toString(4)
        } else {
            JSONArray(jsonStr).toString(4)
        }
    } catch (e: JSONException) {
        val result = jsonStr.trim().chunked(90)
        val linesJson = buildString {
            result.forEach { line ->
                appendLine(line)
            }
        }
        "json 格式错误:\n$linesJson"
    }
}

private fun printLine(tag: String, isTop: Boolean, logLevel: LogLevel) {
    val msg = if (isTop) {
        "╔═══════════════════════════════════════════════════════════════════════════════════════"
    } else {
        "╚═══════════════════════════════════════════════════════════════════════════════════════"
    }
    printLog(tag, msg, logLevel)
}

private fun printLog(tag: String, msg: String?, logLevel: LogLevel, tr: Throwable? = null) {
    val priority = logLevel.priority
    var printMsg = msg.orEmpty()
    tr?.let {
        printMsg += "\n${Log.getStackTraceString(tr)}"
    }
    Log.println(priority, tag, printMsg)
}

enum class LogLevel(val priority: Int) {
    VERBOSE(2),
    DEBUG(3),
    INFO(4),
    WARN(5),
    ERROR(6),
    ASSERT(7)
}