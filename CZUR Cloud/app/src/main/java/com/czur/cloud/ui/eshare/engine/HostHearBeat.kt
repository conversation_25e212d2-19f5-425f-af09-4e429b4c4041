package com.czur.cloud.ui.eshare.engine

import android.annotation.SuppressLint
import android.content.Context
import android.text.TextUtils
import com.czur.czurutils.log.logD
import com.czur.czurutils.log.logStackTrace
import com.eshare.api.IDevice
import com.google.gson.JsonParser
import com.google.gson.JsonSyntaxException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.UnsupportedEncodingException
import java.nio.charset.StandardCharsets

class HostHearBeat {
    companion object {
        private var mDeviceManager: IDevice? = null
        private var mContext: Context? = null
        private var hostHeartbeatCount = 0
        private var lastCastStatus = -2
        val castStatusChanged = MutableSharedFlow<Int>()
        var LastcastStatusChanged = 0
        val replyCastState = MutableSharedFlow<Int>()
        var isRunning = false
        private var hearBeat: HostHearBeat? = null
        private val instance by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            HostHearBeat()
        }

        fun get(iDevice: IDevice): HostHearBeat {
            mDeviceManager = iDevice
            if (hearBeat == null) {
                hearBeat = HostHearBeat()
            }
            return hearBeat!!

        }
    }

    suspend fun startHearBeatThread() = withContext(Dispatchers.IO) {
        if (isRunning) return@withContext
//        mContext = context
//        mDeviceManager = iDevice
        isRunning = true
        start()
    }

    fun stopHeatBeatThread() {
        logStackTrace()
        isRunning = false
        hearBeat = null
    }

    var lastCheckTime: Long = 0
    var timeoutCount = 0
    suspend private fun start() = withContext(Dispatchers.IO) {
        lastCheckTime = 0
        timeoutCount = 0
        hostHeartbeatCount = 0
        lastCastStatus = -2
        LastcastStatusChanged = -1
        check()

//        while (isRunning) {
//            if (System.currentTimeMillis() - lastCheckTime >= Constants.HEART_BEAT_PERIOD) {
//                logD("hostHeartBeat=====result--->来了")
//                lastCheckTime = System.currentTimeMillis()
//                if (hostHeartbeatCount < Int.MAX_VALUE) {
//                    hostHeartbeatCount++
//                } else {
//                    hostHeartbeatCount = 0
//                }
//                val result = mDeviceManager!!.hostHeartBeat(hostHeartbeatCount)
//                if (result == null)
//                    logD("hostHeartBeat=====result====null")
//                if (!TextUtils.isEmpty(result)) {
//                    timeoutCount = 0
//                    try {
//                        dealResult(result)
//                        logD("hostHeartBeat=====result--->$result")
//                    } catch (e: UnsupportedEncodingException) {
//                        e.printStackTrace()
//                    } catch (e1: JsonSyntaxException) {
//                        e1.printStackTrace()
//                    }
//                } else {
//                    timeoutCount++
//                    if (timeoutCount == Constants.OFFLINE_COUNT) {
//                        logD("hostHeartBeat=====result--->断开设备连接")
//                        //断开设备连接
//                        setCastState(5)
////                        stopHeatBeatThread()
//                        break
//                    }
//                }
//            } else {
//                try {
//                    Thread.sleep(100)
//                } catch (e: InterruptedException) {
//                    e.printStackTrace()
//                }
//            }
//        }
    }

    suspend fun check() {
        if (System.currentTimeMillis() - lastCheckTime >= Constants.HEART_BEAT_PERIOD) {
            logD("hostHeartBeat=====result--->来了")
            lastCheckTime = System.currentTimeMillis()
            if (hostHeartbeatCount < Int.MAX_VALUE) {
                hostHeartbeatCount++
            } else {
                hostHeartbeatCount = 0
            }
            val result = mDeviceManager!!.hostHeartBeat(hostHeartbeatCount)
            if (result == null) {
                isRunning = false
                logD("hostHeartBeat=====result====null")
            } else

                if (!TextUtils.isEmpty(result)) {
                    timeoutCount = 0
                    try {
                        dealResult(result)
                        logD("hostHeartBeat=====result--->$result")
                    } catch (e: UnsupportedEncodingException) {
                        e.printStackTrace()
                    } catch (e1: JsonSyntaxException) {
                        e1.printStackTrace()
                    }
                    check()
                } else {
                    timeoutCount++
                    if (timeoutCount == Constants.OFFLINE_COUNT) {
                        logD("hostHeartBeat=====result--->断开设备连接")
                        //断开设备连接
                        setCastState(5)
//                        stopHeatBeatThread()
                        isRunning = false
                    }
                }
        } else {
            try {
                delay(100)
                check()
            } catch (e: InterruptedException) {
                e.printStackTrace()
            }
        }
    }

    @SuppressLint("StaticFieldLeak")
    private fun dealResult(str: String) {
        var results = String(str.toByteArray(StandardCharsets.UTF_8), StandardCharsets.UTF_8)
        val allRet = results.split(System.lineSeparator()).toTypedArray()
        allRet.forEach {
            val result = it.trim()
            if (null != result && result.isNotEmpty()) {
                val obj = JsonParser().parse(result).asJsonObject
                if (obj.has(Constants.KEY_CAST_STATE)) {
                    val castState = obj[Constants.KEY_CAST_STATE].asInt
                    if (lastCastStatus != castState) {
                        lastCastStatus = castState
                        setCastState(castState)
                    }
                }
                if (obj.has(Constants.REPLYCASTREQUEST)) {
                    val replyCastRequest = obj[Constants.REPLYCASTREQUEST].asInt
                    setreplayCastState(replyCastRequest)
                }
                if (obj.has(Constants.MULTISCREEN)) {
                    val screencount = obj[Constants.MULTISCREEN].asInt
                    if (screencount > 1) {
                        if (LastcastStatusChanged != screencount) {
                            setCastState(4)
                            LastcastStatusChanged = screencount
                        }
                    } else {
                        if (LastcastStatusChanged != screencount) {
                            setCastState(3)
                            LastcastStatusChanged = screencount
                        }
                    }
                }
            }
        }
    }

    private fun setCastState(state: Int) {
        MainScope().launch {
            castStatusChanged.emit(state)
        }
    }

    private fun setreplayCastState(state: Int) {
        MainScope().launch {
            replyCastState.emit(state)
        }
    }
}