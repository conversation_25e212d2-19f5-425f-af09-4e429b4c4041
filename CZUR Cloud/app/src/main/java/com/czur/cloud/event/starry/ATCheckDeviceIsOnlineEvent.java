package com.czur.cloud.event.starry;


import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;

/**
 * 上线通知
 */
public class ATCheckDeviceIsOnlineEvent extends BaseEvent {
    private String deviceUdid;
    private String dataBegin;

    private String statusBean;

    public ATCheckDeviceIsOnlineEvent(EventType eventType, String deviceUdid, String statusBean) {
        super(eventType);
        this.deviceUdid = deviceUdid;
        this.statusBean = statusBean;
    }

    public String getStatusBean() {
        return statusBean;
    }

    public String getDataBegin() {
        return dataBegin;
    }

    public String getDeviceUdid() {
        return deviceUdid;
    }
    @Override
    public boolean match(Object obj) {
        return true;
    }
}
