package com.czur.cloud.ui.starry.meeting.dialog

import android.content.Context
import android.os.CountDownTimer
import android.text.Editable
import android.text.InputFilter
import android.text.TextUtils
import android.text.TextWatcher
import android.text.method.DigitsKeyListener
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import com.czur.cloud.R
import com.czur.cloud.ui.account.RegisterActivity
import com.czur.cloud.ui.starry.utils.Tools

/**
 * 自定义输入弹框
 */
class StarryMeetingPwdDialog(context: Context) : AlertDialog(context), View.OnClickListener {

    var call: clickCallBack? = null
    private var TextView_title: TextView? = null        // title
    private var TextView_content: TextView? = null      // content
    var editText: EditText? = null                      // input
    var yesButton: TextView? = null                 // ok
    private var noButton: TextView? = null                          // cancel
    private var delButton: ImageView? = null            // del
    private var starry_pwd_show: ImageView? = null
    private var max_input_len = DLG_MAX_INPUT_PWD
    private var inputType = DLG_TYPE_DEFALT
    private var btnCloseIv: ImageView? = null
    private var meet_red_point: ImageView? = null
    private var starry_meet_del_iv: ImageView? = null

    companion object {
        const val DIMMED_OPACITY = 0.2f
        const val DLG_TYPE_DEFALT = 0
        const val DLG_TYPE_PASSWORD = 1

        const val DLG_MAX_INPUT_PWD = 6

    }

    constructor(
        context: Context,
        title: String,
        content: String="",
        hint: String="",
        OKTitle: String="",
        cancelTitle: String="",
        yesCallBack: clickCallBack
    ) : this(context) {
        call = yesCallBack
        TextView_title?.text = title
        TextView_content?.text = content

        editText?.hint = hint
        editText?.isFocusable = true
        editText?.isFocusableInTouchMode = true
        editText?.requestFocus()
        this.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)

        if (content == ""){
            TextView_content?.visibility = View.GONE
        }else{
            TextView_content?.visibility = View.VISIBLE
        }

        if (OKTitle != ""){
            yesButton?.text = OKTitle
        }

        if (cancelTitle != ""){
            noButton?.text = cancelTitle
        }

        max_input_len = DLG_MAX_INPUT_PWD

        inputType = DLG_TYPE_DEFALT
        editText?.filters = arrayOf(InputFilter.LengthFilter(max_input_len))

    }

    init {
        val inflate =
            LayoutInflater.from(context).inflate(R.layout.starry_common_input_dialog, null);
        setView(inflate)
        //设置点击别的区域不关闭页面
        setCancelable(false)

        TextView_title = inflate.findViewById<TextView>(R.id.title)
        TextView_content = inflate.findViewById<TextView>(R.id.message)
        editText = inflate.findViewById<EditText>(R.id.starry_meet_edit_et)
        delButton = inflate.findViewById<ImageView>(R.id.starry_meet_del_iv)
        yesButton = inflate.findViewById<TextView>(R.id.positive_button)
        noButton = inflate.findViewById<TextView>(R.id.negative_button)
        starry_pwd_show = inflate.findViewById<ImageView>(R.id.starry_pwd_show)
        btnCloseIv = inflate.findViewById<ImageView>(R.id.btnCloseIv)
        meet_red_point = inflate.findViewById<ImageView>(R.id.meet_red_point)
        starry_meet_del_iv = inflate.findViewById<ImageView>(R.id.starry_meet_del_iv)

        yesButton?.setOnClickListener {
            call?.yesClick(this)
        }
        noButton?.setOnClickListener {
            call?.noClick(this)
        }

        yesButton?.alpha = 1.0f
        yesButton?.setOnClickListener {
            call?.yesClick(this)
        }

        editText?.addTextChangedListener(object : TextWatcher {
            private var temp: CharSequence? = null
            private var selectionStart = 0
            private var selectionEnd = 0
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                temp = s
            }

            override fun afterTextChanged(s: Editable?) {
                selectionStart = editText?.selectionStart ?: 0
                selectionEnd = editText?.selectionEnd ?: 0
                val str = temp.toString().replace(" ", "")
                if (!TextUtils.isEmpty(str)) {
                    val l = Tools.getTextLength(str)
                    if (l > max_input_len) {
                        s?.delete(selectionStart - 1, selectionEnd)
                        val tempSelection = selectionEnd
                        editText?.text = s
                        editText?.setSelection(tempSelection)
                    }
                    starry_meet_del_iv?.visibility = View.VISIBLE
                }else{
                    starry_meet_del_iv?.visibility = View.GONE
                }
            }
        })

        // 清空输入内容
        delButton?.setOnClickListener {
            editText?.setText("")
        }

        starry_pwd_show?.isSelected = false
        if (inputType == DLG_TYPE_PASSWORD) {
            editText?.transformationMethod = PasswordTransformationMethod.getInstance()
        }else{
            editText?.transformationMethod = HideReturnsTransformationMethod.getInstance()             
        }

        editText?.keyListener = DigitsKeyListener.getInstance("0123456789")

    }

    fun setInitPwd(pwd: String = ""){
        editText?.setText(pwd)
        editText?.setSelection(editText?.text?.length ?: 0)
    }

    override fun onClick(p0: View?) {
        call?.yesClick(this)

        call?.noClick(this)
    }

    interface clickCallBack {

        fun yesClick(dialog: StarryMeetingPwdDialog)

        fun noClick(dialog: StarryMeetingPwdDialog)
    }

}
