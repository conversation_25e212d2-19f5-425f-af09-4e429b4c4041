package com.czur.cloud.ui.starry.meeting.widget

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.MotionEvent
import android.widget.TextView
import androidx.core.content.res.ResourcesCompat
import androidx.lifecycle.LiveData
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.czur.cloud.R
import com.czur.cloud.ui.starry.meeting.baselib.adapter.BaseVH
import com.czur.cloud.ui.starry.meeting.baselib.adapter.SimpleAdapter
import com.czur.cloud.ui.starry.meeting.baselib.utils.getString
import com.czur.cloud.ui.starry.meeting.baselib.widget.CircleView
import com.czur.cloud.ui.starry.meeting.bean.ChatActionMsg
import com.czur.cloud.ui.starry.meeting.bean.ChatMemberInfoMsg
import com.czur.cloud.ui.starry.meeting.bean.ChatMemberMsgAction
import com.czur.cloud.ui.starry.meeting.bean.ChatTextMsg
import com.czur.cloud.ui.starry.meeting.common.ONE_SECOND
import kotlinx.coroutines.*
import java.util.*
import java.util.concurrent.ConcurrentHashMap

/**
 * Created by 陈丰尧 on 2021/5/17
 * 弹幕View
 */

/**
 * 每条弹幕最多显示10s
 */
private const val SHOW_DURATION = 10 * ONE_SECOND

class BulletLayout @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr), CoroutineScope by MainScope() {
    private val bulletAdapter =
        BulletLayoutAdapter(R.layout.starry_meeting_item_bullet) { holder, _, data: ChatMemberInfoMsg ->
            when (data) {
                is ChatTextMsg -> {
                    val view = holder.getView<TextView>(R.id.itemBulletTv)
                    view.ellipsize = TextUtils.TruncateAt.valueOf("END")
                    holder.setText("${data.nickName}: ${data.content}", R.id.itemBulletTv)
                    holder.visible(false, R.id.statusPoint)
                }
                is ChatActionMsg -> {
                    holder.visible(true, R.id.statusPoint)
                    val statusPoint: CircleView = holder.getView(R.id.statusPoint)

                    val txt = when(data.action){
                        ChatMemberMsgAction.ONLINE -> {
                            statusPoint.circleColor = 0xFF08bef8.toInt()
                            getString(R.string.bullet_join_meeting, data.nickName)
                        }
                        ChatMemberMsgAction.OFFLINE -> {
                            statusPoint.circleColor = 0xFFe44e4e.toInt()
                            getString(R.string.bullet_leave_meeting, data.nickName)
                        }
                        ChatMemberMsgAction.ADMIN -> {
                            statusPoint.circleColor = 0xFF08bef8.toInt()
                            getString(R.string.bullet_admin_meeting, data.nickName)
                        }
                    }

                    val view = holder.getView<TextView>(R.id.itemBulletTv)
                    holder.setText("$txt   ", R.id.itemBulletTv)
                    view.ellipsize = TextUtils.TruncateAt.valueOf("MIDDLE")

                }
                else ->{
                    val view = holder.getView<TextView>(R.id.itemBulletTv)
                    view.ellipsize = TextUtils.TruncateAt.valueOf("END")
                    holder.setText("", R.id.itemBulletTv)
                }

            }

        }

    val isEmptyLive: LiveData<Boolean> = bulletAdapter.emptyLiveData
    val isEmpty: Boolean
        get() = bulletAdapter.isEmpty

    private val jobMap = ConcurrentHashMap<String, Job>()

    init {
        layoutManager = LinearLayoutManager(context, VERTICAL, true)
        adapter = bulletAdapter
        addItemDecoration(DividerItemDecoration(context, VERTICAL).apply {
            setDrawable(
                ResourcesCompat.getDrawable(
                    resources,
                    R.drawable.starry_decoration_bullet,
                    null
                )!!
            )
        })
        itemAnimator = DefaultItemAnimator()
    }

    /**
     * 清空所有弹幕消息
     */
    fun clearAllMsg() {
        bulletAdapter.dataList = emptyList()
        // 清理所有Job
        val iterator = jobMap.iterator()
        while (iterator.hasNext()) {
            val entry = iterator.next()
            entry.value.cancel()
            iterator.remove()
        }
    }

    /**
     * 添加一条消息
     */
    fun addMessage(msg: ChatMemberInfoMsg) {
        bulletAdapter.addData(msg, 0)
        smoothScrollToPosition(0)
        val key = UUID.randomUUID().toString()
        val job = launch {
            delay(SHOW_DURATION)
            bulletAdapter.remove(msg)
            jobMap.remove(key)

        }
        jobMap[key] = job

    }


    override fun setVisibility(visibility: Int) {
        super.setVisibility(visibility)
        if (visibility != VISIBLE) {
            // 不可见时,清空所有msg
            clearAllMsg()
        }
    }

    override fun onTouchEvent(e: MotionEvent?): Boolean {
        return false
    }
}

class BulletLayoutAdapter(
    itemId: Int,
    onBind: (BaseVH, Int, ChatMemberInfoMsg) -> Unit
) : SimpleAdapter<ChatMemberInfoMsg>(itemId, onBind) {
    fun remove(data: ChatMemberInfoMsg) {
        val pos = dataList.indexOfFirst {
            it.id == data.id
        }
        if (pos >= 0) {
            removeData(pos)
        }
    }
}