package com.czur.cloud.ui.eshare

import android.annotation.SuppressLint
import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.widget.RemoteViews
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.livedatabus.ShortCutTrampolineActivity
import com.czur.czurutils.log.logI
import com.czur.czurutils.log.logTagD


const val TAG = "EShareWidgetProvider"

/**
 * 小组件
 */
class EShareWidgetProvider : AppWidgetProvider() {
    @SuppressLint("RemoteViewLayout")
    override fun onUpdate(context: Context, appWidgetManager: AppWidgetManager, appWidgetIds: IntArray) {
        //在这里可以定义更新Widget的逻辑
        logI("${TAG}.onUpdate")
        // Perform this loop procedure for each App Widget that belongs to this provider
        appWidgetIds.forEach { appWidgetId ->
            logI("${TAG}.onUpdate-appWidgetId=${appWidgetId}")
            // Create an Intent to launch ExampleActivity
            val pendingIntentScan: PendingIntent = Intent(context, ShortCutTrampolineActivity::class.java)
                .apply {
                    putExtra(StarryConstants.ESHARE_EMPTY_TYPE, StarryConstants.ESHARE_EMPTY_TYPE_SCAN)
                }
                .let { intent ->
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                        PendingIntent.getActivity(context, 10, intent, PendingIntent.FLAG_IMMUTABLE)
                    } else {
                        PendingIntent.getActivity(context, 10, intent, PendingIntent.FLAG_UPDATE_CURRENT)
                    }
                }
            val pendingIntentFind: PendingIntent = Intent(context, ShortCutTrampolineActivity::class.java)
                .apply {
                    putExtra(StarryConstants.ESHARE_EMPTY_TYPE, StarryConstants.ESHARE_EMPTY_TYPE_FIND)
                }
                .let { intent ->
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                        PendingIntent.getActivity(context, 20, intent, PendingIntent.FLAG_IMMUTABLE)
                    } else {
                        PendingIntent.getActivity(context, 20, intent, PendingIntent.FLAG_UPDATE_CURRENT)
                    }
                }
            val pendingIntentInput: PendingIntent = Intent(context, ShortCutTrampolineActivity::class.java)
                .apply {
                    putExtra(StarryConstants.ESHARE_EMPTY_TYPE, StarryConstants.ESHARE_EMPTY_TYPE_INPUT)
                }
                .let { intent ->
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                        PendingIntent.getActivity(context, 30, intent, PendingIntent.FLAG_IMMUTABLE)
                    } else {
                        PendingIntent.getActivity(context, 30, intent, PendingIntent.FLAG_UPDATE_CURRENT)
                    }
                }
            // Get the layout for the App Widget and attach an on-click listener
            // to the button
            val views: RemoteViews = RemoteViews(context.packageName, com.czur.cloud.R.layout.eshare_widget_layout)
                .apply {
                    setOnClickPendingIntent(com.czur.cloud.R.id.eshare_search_ll, pendingIntentScan)//扫码投屏
                    setOnClickPendingIntent(com.czur.cloud.R.id.eshare_connect, pendingIntentFind)
                    setOnClickPendingIntent(com.czur.cloud.R.id.eshare_pincode, pendingIntentInput)

//                    // 加载原始图片
//                    val originalBitmap =
//                        BitmapFactory.decodeResource(context.resources, R.mipmap.eshare_widget_logo)
//
//                    // 创建圆角 Drawable
//                    val roundedDrawable: RoundedBitmapDrawable =
//                        RoundedBitmapDrawableFactory.create(context.resources, originalBitmap)
//                    roundedDrawable.cornerRadius = 100f // 设置圆角半径，单位为像素
//
//                    roundedDrawable.setAntiAlias(true) // 开启抗锯齿
//
//                    setImageViewBitmap(R.id.eshare_scan, roundedDrawable.bitmap)

                }

            // Tell the AppWidgetManager to perform an update on the current app widget
            appWidgetManager.updateAppWidget(appWidgetId, views)
        }
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        super.onReceive(context, intent)
        val action = intent?.action
        logI("${TAG}.onReceive.action=${action}")
    }

    override fun onAppWidgetOptionsChanged(
        context: Context?,
        appWidgetManager: AppWidgetManager?,
        appWidgetId: Int,
        newOptions: Bundle?
    ) {
        super.onAppWidgetOptionsChanged(context, appWidgetManager, appWidgetId, newOptions)
        logI("${TAG}.onAppWidgetOptionsChanged")
    }

    override fun onDeleted(context: Context?, appWidgetIds: IntArray?) {
        super.onDeleted(context, appWidgetIds)
        logI("${TAG}.onDeleted")
    }

    override fun onEnabled(context: Context?) {
        super.onEnabled(context)
        logI("${TAG}.onEnabled")
    }

    override fun onDisabled(context: Context?) {
        super.onDisabled(context)
        logI("${TAG}.onDisabled")
    }

    override fun onRestored(context: Context?, oldWidgetIds: IntArray?, newWidgetIds: IntArray?) {
        super.onRestored(context, oldWidgetIds, newWidgetIds)
        logI("${TAG}.onRestored")
    }

}