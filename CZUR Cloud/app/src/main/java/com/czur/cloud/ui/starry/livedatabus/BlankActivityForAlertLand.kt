package com.czur.cloud.ui.starry.livedatabus

import com.czur.cloud.ui.starry.meeting.dialog.StarryCommonPopupAlert

class BlankActivityForAlertLand: BlankActivityForAlert() {
    companion object{
        var commonPopup: StarryCommonPopupAlert? = null
        val instance: BlankActivityForAlertLand by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            BlankActivityForAlertLand()
        }
    }

}