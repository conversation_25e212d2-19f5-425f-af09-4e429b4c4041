package com.czur.cloud.ui.starry.activity

import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.SystemClock
import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.NetworkUtils
import com.blankj.utilcode.util.ToastUtils
import com.czur.cloud.BuildConfig
import com.czur.cloud.R
import com.czur.cloud.event.BaseEvent
import com.czur.cloud.event.EventType
import com.czur.cloud.event.StarryCommonEvent
import com.czur.cloud.preferences.StarryPreferences
import com.czur.cloud.ui.component.popup.CloudCommonPopup
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants
import com.czur.cloud.ui.starry.activity.StarryContactDetailActivity.Companion.DEL_ACCOUNT_000
import com.czur.cloud.ui.starry.activity.StarryContactDetailActivity.Companion.DEL_ACCOUNT_PRE
import com.czur.cloud.ui.starry.adapter.MeetingDetailMemberAdapter
import com.czur.cloud.ui.starry.base.StarryBaseActivity
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.meeting.MeetingMainActivity
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.meeting.dialog.StarryCommonPopup
import com.czur.cloud.ui.starry.meeting.model.MeetingModel
import com.czur.cloud.ui.starry.meeting.model.ModelManager
import com.czur.cloud.ui.starry.model.*
import com.czur.cloud.ui.starry.utils.Tools
import com.czur.cloud.ui.starry.utils.singleClick
import com.czur.cloud.ui.starry.viewmodel.MeetingDetailViewModel
import com.czur.cloud.ui.starry.viewmodel.RecentlyViewModel
import com.czur.cloud.ui.starry.viewmodel.StarryContactViewModel
import com.czur.cloud.ui.starry.viewmodel.StarryViewModel
import com.czur.czurutils.log.logI
import kotlinx.android.synthetic.main.starry_activity_meeting_detail.*
import kotlinx.android.synthetic.main.starry_layout_top_bar.*
import kotlinx.coroutines.delay
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * 会议详情
 */
class StarryMeetingDetailActivity : StarryBaseActivity(), View.OnClickListener {

    private var mLastClickTime: Long = 0

    private val mAdapter by lazy {
        MeetingDetailMemberAdapter()
    }

    private val contactViewModel by lazy {
        ViewModelProvider(
            StarryActivity.mainActivity ?: this
        ).get(StarryContactViewModel::class.java)
    }

    private val viewModel by lazy {
        ViewModelProvider(
            StarryActivity.mainActivity ?: this
        ).get(MeetingDetailViewModel::class.java)
    }

    private val recentlyViewModel by lazy {
        ViewModelProvider(StarryActivity.mainActivity ?: this).get(RecentlyViewModel::class.java)
    }

    private val starryViewModel by lazy {
        ViewModelProvider(StarryActivity.mainActivity ?: this).get(StarryViewModel::class.java)
    }

    private var meetingID = ""
    private var isPCEnter = false

    private var memberStatus = "0"

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: BaseEvent) {

        when (event.eventType) {
//            EventType.STARRY_CONTACT_ADDTO,
            EventType.STARRY_MEETING_CMD_STOP,
            EventType.STARRY_CLOSE_MEETING,
//            EventType.STARRY_CONTACT_DEL
            -> {
                // 查询最近通话记录详情
//                showProgressDialog()
                viewModel.getCallRecordDetail(meetingID)
            }

            else -> {
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.white)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.starry_activity_meeting_detail)

        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }

        initComponent()
        registerEvent()
//        initObserver()
    }

    @SuppressLint("SetTextI18n")
    private fun initComponent() {
        user_title?.setText(R.string.starry_title_meeting_detail)

        contactViewModel.currentFromType = StarryConstants.INTENT_FROM_TYPE_MEETING

        detailView.visibility = View.GONE
        user_delete_btn?.visibility = View.GONE
        meeting_longtime?.visibility = View.GONE
        meeting_doing?.visibility = View.GONE
        meeting_join_btn?.visibility = View.GONE
        meeting_restart_btn?.visibility = View.GONE
        starry_meeting_info_rl?.visibility = View.GONE

        meetingID = intent.getStringExtra(StarryConstants.STARRY_MEETING_ID) ?: "0"
        isPCEnter = intent.getBooleanExtra(StarryConstants.STARRY_MEETING_ISPCENTER, false) ?: false

        recycler_view_member_list?.setHasFixedSize(true)
        //线性布局
        val linearLayoutManager = LinearLayoutManager(this)
        linearLayoutManager.orientation = LinearLayoutManager.VERTICAL
        recycler_view_member_list?.apply {
            layoutManager = linearLayoutManager
            adapter = mAdapter
        }
        mAdapter.setOnItemPickListener(object : MeetingDetailMemberAdapter.OnItemPickListener {
            override fun onItemPick(position: Int, model: StarryCallRecordDetailMemberModel) {
                logI("StarryMeetingDetailActivity.setOnItemPickListener")
                if (starryViewModel.clickNoNetwork()) {
                    return
                }

                // 双击太快过滤
                if (SystemClock.elapsedRealtime() - mLastClickTime < 800) {
                    return
                }
                mLastClickTime = SystemClock.elapsedRealtime()

                val userType = StarryConstants.STARRY_USER_TYPE_CC
                val addressBookModel = StarryAddressBookModel()
                addressBookModel.apply {
                    accountNo = model.accountNo
                    meetingAccout = model.accountNo
                    meetingNo = model.accountNo
                    isInAddressbook = model.isInAddressbook
                    isInEnterprise = model.isInEnterprise
                    isMarked = model.isMarked
                    name = model.name
                    response = model.response
                    id = model.id
//                    createTime = model.createTime
//                    isMyself = model.isMyself
                }

                val intent = Intent(
                    this@StarryMeetingDetailActivity,
                    StarryContactDetailActivity::class.java
                )
                intent.putExtra(StarryConstants.STARRY_USER_MODEL, addressBookModel)
                intent.putExtra(StarryConstants.STARRY_USER_TYPE, userType)
                intent.putExtra(
                    StarryConstants.STARRY_MEETING_FROM,
                    StarryConstants.STARRY_MEETING_FROM_DETAIL
                )
                ActivityUtils.startActivity(intent)
            }

        })

        val count = starryViewModel.enterpriseList.value.size ?: 0
        if (count < 1) {
            meeting_restart_btn?.visibility = View.GONE
        }

        starry_meetingcode_copy?.singleClick {
            // 1, user_name
            // 2, meeting_title
            // 3, meeting_code
            // 4, meeting_pwd
            val shareUrl = "${BuildConfig.SHARE_STARRY_URL}${viewModel.detailModel.meetingId}/${viewModel.detailModel.accountNo}"
            val paste_str = if (viewModel.detailModel.meetingPassword == "") {
                String.format(
                    getString(R.string.starry_meeting_paste_title_no_pwd),
                    StarryPreferences.getInstance().name,
                    viewModel.detailModel.meetingName,
                    shareUrl,
                    viewModel.detailModel.meetingCode
                )
            }else{
                String.format(
                    getString(R.string.starry_meeting_paste_title),
                    StarryPreferences.getInstance().name,
                    viewModel.detailModel.meetingName,
                    shareUrl,
                    viewModel.detailModel.meetingCode,
                    viewModel.detailModel.meetingPassword
                )
            }

            val mClipboardManager: ClipboardManager = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val mClipData: ClipData = ClipData.newPlainText("czur", paste_str)
            mClipboardManager.setPrimaryClip(mClipData)
            ToastUtils.showLong(R.string.starry_meetingcode_copy_ok)
        }

    }

    private fun registerEvent() {
        user_back_btn?.singleClick(this)
        meeting_join_btn?.singleClick(this)
        meeting_restart_btn?.singleClick(this)
        user_delete_btn?.singleClick(this)
    }

    private fun initObserver() {
        viewModel.memberList.observe(this) {

            onRefreshUI(viewModel.detailModel)

            // 数据列表更新
//            mAdapter.setmDatas(list)
            mAdapter.setmDatas(it)
            detailView.visibility = View.VISIBLE
            hideProgressDialog()

        }
        launch {
            viewModel.isDelete.collect {
                when {
                    it > 0 -> {
                        EventBus.getDefault()
                            .post(StarryCommonEvent(EventType.STARRY_RECENTLY_DEL, ""))
                        ActivityUtils.finishActivity(StarryMeetingDetailActivity::class.java)

                    }
                }
            }
        }

    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.user_back_btn -> ActivityUtils.finishActivity(this)
            R.id.meeting_join_btn -> {
                if (starryViewModel.clickNoNetwork()) {
                    return
                }
                onMeetingJoin()
            }
            R.id.meeting_restart_btn -> {
                logI("StarryMeetingDetailActivity.meeting_restart_btn")
                if (starryViewModel.clickNoNetwork()) {
                    return
                }

//                newStartCallMeeting()
                onCheckPCEnter()

            }
            R.id.user_delete_btn -> {
                logI("StarryMeetingDetailActivity.user_delete_btn")
                if (starryViewModel.clickNoNetwork()) {
                    return
                }
                showConfirmDeleteDialog()
            }
            else -> {
            }
        }
    }

    // 加入会议
    private fun onMeetingJoin() {
        logI("StarryMeetingDetailActivity.meeting_join_btn")

        // 加入会议
        // 当被移除会议，且会议正在进行时，再次加入弹出提示
        if (memberStatus == StarryConstants.CALL_STATUS_REMOVED.toString()) {
            ToastUtils.showLong(R.string.starry_meeting_removed_msg)
        } else {
            // 加入视频会议
            onJoinMeeting(viewModel.detailModel)
        }
    }
    var firstIn = true;

    override fun onResume() {
        super.onResume()
        // 查询最近通话记录详情
        if (!NetworkUtils.isConnected()){
            return
        }
        launch {
            showProgressDialog()
            viewModel.getCallRecordDetail(meetingID){
                if (firstIn){
                    launch {
                        firstIn = false
                        delay(200)
                        initObserver()
                    }
                }
            }

        }

    }

    override fun onDestroy() {
        super.onDestroy()

        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }


    private fun showConfirmRemovedDialog() {
        CloudCommonPopup.Builder(
            this,
            CloudCommonPopupConstants.KNOWN_ONE_BUTTON
        )
            .setTitle(resources.getString(R.string.prompt))
            .setMessage(resources.getString(R.string.starry_meeting_removed_confirm))
            .setOnNegativeListener { dialog, which -> dialog.dismiss() }
            .create()
            .show()
    }

    private fun showConfirmDeleteDialog() {
        CloudCommonPopup.Builder(
            this,
            CloudCommonPopupConstants.COMMON_TWO_BUTTON_YES_NO
        )
            .setTitle(resources.getString(R.string.prompt))
            .setMessage(resources.getString(R.string.starry_meeting_remove_confirm))
            .setOnPositiveListener { dialog, _ ->
                viewModel.deleteCallRecords(meetingID)
                dialog?.dismiss()
            }
            .setOnNegativeListener { dialog, _ -> dialog.dismiss() }
            .create()
            .show()
    }

    private fun onRefreshUI(currentMeetingDetailModel: StarryCallRecordDetailModel) {
        meeting_title?.text = currentMeetingDetailModel.meetingName

        // 未接-0 //已接-1 //拒接-2 //转接-3 //占线-4 //退出-5 //被移除-6
        memberStatus = currentMeetingDetailModel.status.toString()

        val count = currentMeetingDetailModel.num
//        val counmeeting_titletReal = currentMeetingDetailModel.details.count { it.response == "1" }
        val countReal = currentMeetingDetailModel.details.size
        val meeting_long = currentMeetingDetailModel.meetingDuration ?: 0
        val timeSpace = Tools.meetingTimeLongFormat(meeting_long.toInt())
        meeting_longtime?.text = getString(R.string.starry_recently_timespace, countReal, timeSpace)
//        val time = currentMeetingDetailModel.createTime
        val time = currentMeetingDetailModel.getTimeStr()
        val time2 = Tools.getFormatDateTimeForMeetDetail(time)
        meeting_starttime?.text = time2

        var meeting_code = currentMeetingDetailModel.meetingCode ?: ""
        meeting_code = Tools.formateMeetCode(meeting_code)
        if (meeting_code == ""){
            meeting_code = "null"
        }
        starry_meeting_code_title?.text = String.format(
            getString(R.string.starry_meeting_code_title),meeting_code)
//        if (meeting_code == ""){
//            starry_meeting_code_title?.visibility = View.GONE
//        }else{
//            starry_meeting_code_title?.visibility = View.VISIBLE
//        }

        starry_meeting_info_rl?.visibility = View.VISIBLE

        val meetingStatus = currentMeetingDetailModel.meetingStatus
        if (meetingStatus == StarryConstants.MEETING_STATUS_PCOCESSING) {
            // 会议中，不显示删除按钮，显示加入会议按钮,xia
            user_delete_btn?.visibility = View.GONE
//            user_delete_btn?.visibility = View.VISIBLE
            meeting_doing?.visibility = View.VISIBLE
            meeting_longtime?.visibility = View.GONE

            meeting_join_btn?.visibility = View.VISIBLE
            // 被移除的人员，加入会议按钮是灰色的，但是可以点击
            if (memberStatus == StarryConstants.CALL_STATUS_REMOVED.toString()) {
//                meeting_join_btn.background = getDrawable(R.drawable.starry_btn_rec_5_bg_with_gray)
//                meeting_join_btn?.alpha = 0.5f
                meeting_join_btn?.visibility = View.GONE
            }
            // 自己退出的，不显示加入会议
            if (memberStatus == StarryConstants.CALL_STATUS_LEAVE.toString()) {
                meeting_join_btn?.visibility = View.GONE
            }

            meeting_restart_btn?.visibility = View.GONE
        } else {
            // 结束的会议，显示删除按钮，显示发起会议（条件判断），不显示加入会议按钮
            meeting_doing?.visibility = View.GONE
            meeting_longtime?.visibility = View.VISIBLE

            meeting_join_btn?.visibility = View.GONE
            user_delete_btn?.visibility = View.VISIBLE
            // ?? 发起会议 show whatever.
            //if (isInCompany) {
            meeting_restart_btn?.visibility = View.VISIBLE
            //}
        }
    }

    // 加入视频会议
    private fun onJoinMeeting(currentMeetingDetailModel: StarryCallRecordDetailModel) {
        val roomNo = currentMeetingDetailModel.meetingId
        val callInModel = StarryCallInModel()

        val isMic = StarryPreferences.getInstance()?.lastMic ?: true
        val isCam = StarryPreferences.getInstance()?.lastCam ?: false
        ModelManager.membersModel.selfVideoInUseLive.value = isCam
        ModelManager.membersModel.selfAudioInUseLive.value = isMic

        callInModel.apply {
            room_name = currentMeetingDetailModel.meetingName
            room = roomNo
            udid_from = ""
            callId = ""
            userid_from = ""
            nickname_from = ""
            headImage = ""
            isCamOpen = isCam
            isMicOpen = isMic
            userid = StarryPreferences.getInstance().userId
            czurId = StarryPreferences.getInstance().czurId
            accountNo = currentMeetingDetailModel.accountNo
            agoraId = StarryPreferences.getInstance().starryUserinfoModel.id.toString()
        }

        logI("StarryMeetingDetailActivity.onJoinMeeting.roomNo=${roomNo},callInModel=${callInModel}")

        if (recentlyViewModel.otherMeeting && !isPCEnter) {
            StarryCommonPopup.Builder(this)
                .setTitle(getString(R.string.starry_popupwindow_title))
                .setMessage(getString(R.string.starry_callin_dialog_msg))
                .setPositiveTitle(getString(R.string.starry_callin_dialog_join))
                .setNegativeTitle(getString(R.string.starry_callin_dialog_cancel))
                .setOnPositiveListener { dialog, _ ->
                    val i = Intent(this, MeetingMainActivity::class.java)
                    i.putExtra(MeetingMainActivity.KEY_BOOT_MODEL, callInModel)
                    i.putExtra(MeetingMainActivity.KEY_ROOM, roomNo)
                    i.putExtra(MeetingMainActivity.KEY_BOOT_TYPE, MeetingMainActivity.BOOT_TPE_JOIN)
                    startActivity(i)
                    ActivityUtils.finishActivity(this)

                    dialog?.dismiss()
                }
                .setOnNegativeListener { dialog, _ ->
                    dialog.dismiss()
                }
                .create()
                .show()
        } else {
            val i = Intent(this, MeetingMainActivity::class.java)
            i.putExtra(MeetingMainActivity.KEY_BOOT_MODEL, callInModel)
            i.putExtra(MeetingMainActivity.KEY_ROOM, roomNo)
            i.putExtra(MeetingMainActivity.KEY_BOOT_TYPE, MeetingMainActivity.BOOT_TPE_JOIN)
            startActivity(i)
            ActivityUtils.finishActivity(this)
        }
    }

    // 同一账号如果pc端已在会议中
    private fun onCheckPCEnter() {
        if (MeetingModel.isPCEnter) {
            StarryCommonPopup.Builder(this)
                .setTitle(getString(R.string.starry_popupwindow_title))
                .setMessage(getString(R.string.starry_callout_dialog_msg))
                .setPositiveTitle(getString(R.string.starry_callout_dialog_goon))
                .setNegativeTitle(getString(R.string.starry_callout_dialog_cancel))
                .setOnPositiveListener { dialog, _ ->
                    newStartCallMeeting()
                    dialog?.dismiss()
                }
                .setOnNegativeListener { dialog, _ ->
                    dialog.dismiss()
                }
                .create()
                .show()
        } else {
            newStartCallMeeting()
        }
    }

    // 发起会议
    private fun newStartCallMeeting() {
        // 呼叫中，不能发起会议
        if (!MeetingModel.checkIsMeetingCall()) {
            return
        }
        showProgressDialog()
        viewModel.getCallRecordDetail(meetingID)
        // 标记开始呼叫
        MeetingModel.isCallingOutMeeting = true

        // 发起会议
        // 根据发起方的企业ID判断，当前会议列表是否全部为发起会议企业内成员，
        // 1.如果是则可进行发起会议功能。
        // 2.如果不全部是发起方企业成员，按钮为灰色，点击后toast提示：“您仅可对自己企业内成员发起会议！”
        //最近会议详情逻辑补充：
        //1.最近会议详情显示参会人员（包含已参加/未参加人员），人数计算也如此
        //2.从最近会议详情发起会议，检测到人超过10人时，toast提示：仅可发起10人会议

        // 已跟产品确认：
        //1.最近列表发起会议，超出人数时，toast提示：参会人员已达上限
        //2.最近列表发起会议，有非共同企业用户时（包含自己没有企业情况），
        // toast提示：仅可对企业成员发起会议 （同时标红没有相同企业的联系人，自己不用标红）

        //
//        logI("StarryMeetingDetailActivity.viewModel.memberList.value=${viewModel.memberList.value}")
        launch {
            val detailModel = RecentlyModel.getCallRecordDetail(meetingID)
            val memberList = viewModel.findMyselfInMemberList(detailModel)
            if (!memberList.isNullOrEmpty()) {
                val no_enterprise = memberList.count {
                    !it.isInEnterprise
                }
                if (no_enterprise > 0) {
                    mAdapter.setIsInEnterpriseFlag(false)
                    ToastUtils.showLong(R.string.starry_meeting_no_enterprise)
                    MeetingModel.isCallingOutMeeting = false
                    return@launch
                }

                // 是否注销判断
                val delete_no = memberList.count {
                    it.accountNo.startsWith(DEL_ACCOUNT_PRE)
                            || it.accountNo.startsWith(DEL_ACCOUNT_000)
                }
                if (delete_no > 0) {
                    mAdapter.setIsDeleteFlag(true)
                    ToastUtils.showLong(R.string.starry_meeting_no_delete)
                    MeetingModel.isCallingOutMeeting = false
                    return@launch
                }

            } else {
                ToastUtils.showLong(R.string.starry_meeting_no_enterprise)
                MeetingModel.isCallingOutMeeting = false
                return@launch
            }

            // 参会人员已达上限
            val count = memberList.size ?: 0
            val limit = StarryPreferences.getInstance().starryUserinfoModel.portLimit
            if (count > limit) {
//          ToastUtils.showLong(getString(R.string.meeting_detail_startmeeting_limit, limit))
                ToastUtils.showLong(getString(R.string.meeting_detail_startmeeting_limit_member))
                MeetingModel.isCallingOutMeeting = false
                return@launch
            }

            val members = ArrayList<MeetingMember>()
            // 获取到参会人员列表
            memberList.forEach {
                val accountNo = it.accountNo ?: ""
                val name = it.name ?: ""
                // 如果是发起者自己，则跳过，后续会自动加入自己为管理员
                if (accountNo != StarryPreferences.getInstance().accountNo) {
                    members.add(MeetingMember(accountNo, name))
                }
            }
            hideProgressDialog()
            intentToMeetingMainActivity(members)
        }

    }

    private fun intentToMeetingMainActivity(members: ArrayList<MeetingMember>) {
        val jsonParam = starryViewModel.startNewMeeting(members)
        val i = Intent(this, MeetingMainActivity::class.java)
        i.putExtra(MeetingMainActivity.KEY_BOOT_DATA, jsonParam)
        i.putExtra(MeetingMainActivity.KEY_BOOT_TYPE, MeetingMainActivity.BOOT_TYPE_START)
        startActivity(i)
        ActivityUtils.finishActivity(this)
    }
}