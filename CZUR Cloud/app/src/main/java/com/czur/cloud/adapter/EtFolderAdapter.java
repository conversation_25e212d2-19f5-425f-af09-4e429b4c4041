package com.czur.cloud.adapter;

import android.app.Activity;
import android.net.Uri;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.R;
import com.czur.cloud.entity.EtEntity;
import com.czur.cloud.model.EtFileModel;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.interfaces.DraweeController;
import com.facebook.drawee.view.SimpleDraweeView;
import com.facebook.imagepipeline.request.ImageRequest;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * Created by s<PERSON><PERSON> on 2020/6/9
 */


public class EtFolderAdapter extends RecyclerView.Adapter<ViewHolder> {

    public static final int ITEM_TYPE_FOLDER = 0;
    public static final int ITEM_TYPE_FILE = 1;
    private Activity mActivity;
    //当前需要显示的所有的图片数据
    private List<EtFileModel.FilesBean> filesDatas;
    private List<EtFileModel.FoldersBean> folderDatas;

    //是否进入选择
    private boolean isSelectItem;
    private LayoutInflater mInflater;
    private LinkedHashMap<String, EtEntity> isCheckedMap = new LinkedHashMap<>();

    /**
     * 构造方法
     */
    public EtFolderAdapter(Activity activity, List<EtFileModel.FoldersBean> folderDatas, List<EtFileModel.FilesBean> filesDatas, boolean isSelectItem) {
        this.mActivity = activity;
        this.isSelectItem = isSelectItem;
        this.filesDatas = filesDatas;
        this.folderDatas = folderDatas;
        mInflater = LayoutInflater.from(activity);
    }

    public void refreshData(List<EtFileModel.FoldersBean> folderDatas, List<EtFileModel.FilesBean> filesDatas, boolean isSelectItem, LinkedHashMap<String, EtEntity> isCheckedMap) {
        this.isSelectItem = isSelectItem;
        this.filesDatas = filesDatas;
        this.folderDatas = folderDatas;
        this.isCheckedMap = isCheckedMap;
        notifyDataSetChanged();
    }

    public void refreshData(List<EtFileModel.FilesBean> filesDatas, boolean isSelectItem, LinkedHashMap<String, EtEntity> isCheckedMap) {
        this.isSelectItem = isSelectItem;
        this.filesDatas = filesDatas;
        this.isCheckedMap = isCheckedMap;
        this.folderDatas = new ArrayList<>();
        notifyDataSetChanged();
    }

    public void refreshData(boolean isSelectItem) {
        this.isSelectItem = isSelectItem;
        notifyDataSetChanged();
    }

    public void refreshData(List<EtFileModel.FilesBean> filesDatas) {
        this.filesDatas = filesDatas;
        notifyDataSetChanged();
    }

    public void refreshData(List<EtFileModel.FoldersBean> folderDatas, List<EtFileModel.FilesBean> filesDatas) {
        this.folderDatas = folderDatas;
        this.filesDatas = filesDatas;
        notifyDataSetChanged();
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        if (viewType == ITEM_TYPE_FOLDER) {
            return new ETFolderHolder(mInflater.inflate(R.layout.item_et_folder, parent, false));
        } else
            return new EtFilesHolder(mInflater.inflate(R.layout.item_et_files, parent, false));
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        if (holder instanceof ETFolderHolder) {
            final ETFolderHolder mHolder = (ETFolderHolder) holder;
            mHolder.mItem = folderDatas.get(position);
            if (mHolder.mItem.isAutoCreate()) {
                mHolder.itemNameTv.setText(mHolder.mItem.getName().replaceAll("New Doc", mActivity.getString(R.string.et_doc)));
            } else {
                mHolder.itemNameTv.setText(mHolder.mItem.getName());
            }
            mHolder.itemSizeTv.setText(String.format(mActivity.getString(R.string.item_picture_count), mHolder.mItem.getFileCounts() + ""));
            if (isSelectItem) {
                mHolder.checkBox.setVisibility(View.VISIBLE);
                mHolder.checkBox.setTag(mHolder.mItem.getId());
            } else {
                mHolder.checkBox.setVisibility(View.GONE);
            }
            mHolder.checkBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    if (isChecked) {
                        if (!isCheckedMap.containsKey(mHolder.checkBox.getTag())) {
                            //选中时添加
                            EtEntity etEntity = new EtEntity();
                            etEntity.setType(0);
                            isCheckedMap.put(mHolder.mItem.getId(), etEntity);
                        }
                    } else {
                        if (isCheckedMap.containsKey(mHolder.checkBox.getTag())) {
                            //没选中时移除
                            isCheckedMap.remove(mHolder.mItem.getId());
                        }
                    }
                    if (onItemCheckListener != null) {
                        onItemCheckListener.onItemCheck(position, isCheckedMap, folderDatas.size(), mHolder.mItem.getName());
                    }
                }
            });

            if (isCheckedMap != null) {
                mHolder.checkBox.setChecked(isCheckedMap.containsKey(mHolder.mItem.getId()) ? true : false);
            } else {
                mHolder.checkBox.setChecked(false);
            }

            mHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onEtFolderClickListener != null) {
                        onEtFolderClickListener.onEtFolderClick(mHolder.mItem, null, position, mHolder.checkBox);
                    }
                }
            });
        }
        if (holder instanceof EtFilesHolder) {
            final EtFilesHolder mHolder = (EtFilesHolder) holder;
            mHolder.mItem = filesDatas.get(position - folderDatas.size());

            Uri lowResUri = Uri.parse(mHolder.mItem.getSmall());
            DraweeController controller = Fresco.newDraweeControllerBuilder()
                    .setImageRequest(ImageRequest.fromUri(lowResUri))
                    .setOldController(mHolder.etFilesImg.getController())
                    .build();
            mHolder.etFilesImg.setController(controller);


            if (isSelectItem) {
                mHolder.checkBox.setVisibility(View.VISIBLE);
                mHolder.checkBox.setTag(mHolder.mItem.getId());
            } else {
                mHolder.checkBox.setVisibility(View.GONE);
            }
            mHolder.checkBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    if (isChecked) {
                        if (!isCheckedMap.containsKey(mHolder.checkBox.getTag())) {
                            //选中时添加
                            EtEntity etEntity = new EtEntity();
                            etEntity.setType(1);
                            etEntity.setFlatten(mHolder.mItem.getFlatten());
                            isCheckedMap.put(mHolder.mItem.getId(), etEntity);
                        }
                    } else {
                        if (isCheckedMap.containsKey(mHolder.checkBox.getTag())) {
                            //没选中时移除
                            isCheckedMap.remove(mHolder.mItem.getId());
                        }
                    }
                    if (onItemCheckListener != null) {
                        onItemCheckListener.onItemCheck(position, isCheckedMap, filesDatas.size(), null);
                    }
                }
            });

            if (isCheckedMap != null) {
                mHolder.checkBox.setChecked(isCheckedMap.containsKey(mHolder.mItem.getId()) ? true : false);
            } else {
                mHolder.checkBox.setChecked(false);
            }

            mHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onEtFolderClickListener != null) {
                        onEtFolderClickListener.onEtFolderClick(null, mHolder.mItem, position, mHolder.checkBox);
                    }

                }
            });
        }

    }


    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return filesDatas.size() + folderDatas.size();
    }


    private static class EtFilesHolder extends ViewHolder {
        public final View mView;
        EtFileModel.FilesBean mItem;
        SimpleDraweeView etFilesImg;
        CheckBox checkBox;


        EtFilesHolder(View itemView) {
            super(itemView);
            mView = itemView;
            etFilesImg = (SimpleDraweeView) itemView.findViewById(R.id.et_files_img);
            checkBox = (CheckBox) itemView.findViewById(R.id.check);

            ViewGroup.LayoutParams layoutParams = itemView.getLayoutParams();
            layoutParams.width = (ScreenUtils.getScreenWidth() - SizeUtils.dp2px(16)) / 3;
            layoutParams.height = (int) (layoutParams.width*1.33f);
            itemView.setLayoutParams(layoutParams);

        }

    }


    private static class ETFolderHolder extends ViewHolder {
        public final View mView;
        EtFileModel.FoldersBean mItem;
        private CheckBox checkBox;
        private TextView itemNameTv;
        private TextView itemSizeTv;


        ETFolderHolder(View itemView) {
            super(itemView);
            mView = itemView;
            checkBox = (CheckBox) itemView.findViewById(R.id.check);
            itemNameTv = (TextView) itemView.findViewById(R.id.item_name_tv);
            itemSizeTv = (TextView) itemView.findViewById(R.id.item_size_tv);

            ViewGroup.LayoutParams layoutParams = itemView.getLayoutParams();
            layoutParams.width = (ScreenUtils.getScreenWidth() - SizeUtils.dp2px(16)) / 3;
            layoutParams.height = (int) (layoutParams.width*1.33f);
            itemView.setLayoutParams(layoutParams);
        }
    }


    @Override
    public int getItemViewType(int position) {
        if (position >= 0 && position < folderDatas.size()) {
            return ITEM_TYPE_FOLDER;
        } else return ITEM_TYPE_FILE;
    }

    public int getTotalSize() {
        return filesDatas.size() + folderDatas.size();
    }


    private OnEtFolderClickListener onEtFolderClickListener;

    public void setOnEtFolderClickListener(OnEtFolderClickListener onEtFolderClickListener) {
        this.onEtFolderClickListener = onEtFolderClickListener;
    }

    public interface OnEtFolderClickListener {
        void onEtFolderClick(EtFileModel.FoldersBean foldersBean, EtFileModel.FilesBean filesBean, int position, CheckBox checkBox);
    }

    private OnItemCheckListener onItemCheckListener;

    public void setOnItemCheckListener(OnItemCheckListener onItemCheckListener) {
        this.onItemCheckListener = onItemCheckListener;
    }

    public interface OnItemCheckListener {
        void onItemCheck(int position, LinkedHashMap<String, EtEntity> isCheckedMap, int totalSize, String folderName);

    }


}
