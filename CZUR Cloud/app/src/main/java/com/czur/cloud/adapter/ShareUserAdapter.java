package com.czur.cloud.adapter;

import android.app.Activity;
import android.net.Uri;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.czur.cloud.R;
import com.czur.cloud.model.UserShareModel;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.interfaces.DraweeController;
import com.facebook.drawee.view.SimpleDraweeView;
import com.facebook.imagepipeline.request.ImageRequest;

import java.util.List;

/**
 * Created by Yz on 2018/3/16
 * Email：<EMAIL>
 */


public class ShareUserAdapter extends RecyclerView.Adapter<ViewHolder> {


    private static final int ITEM_TYPE_NORMA = 0;
    private static final int ITEM_TYPE_ADD = 1;
    private static final int ITEM_TYPE_MINUS = 2;
    private Activity mActivity;
    //当前需要显示的所有的图片数据
    private List<UserShareModel> datas;

    private LayoutInflater mInflater;

    /**
     * 构造方法
     */
    public ShareUserAdapter(Activity activity, List<UserShareModel> datas) {

        this.mActivity = activity;
        this.datas = datas;
        mInflater = LayoutInflater.from(activity);
    }

    public void refreshData(List<UserShareModel> datas) {

        this.datas = datas;
        notifyDataSetChanged();

    }


    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        if (viewType == ITEM_TYPE_NORMA) {
            return new NormalViewHolder(mInflater.inflate(R.layout.item_et_user_head, parent, false));
        } else if (viewType == ITEM_TYPE_ADD) {
            return new AddUserHolder(mInflater.inflate(R.layout.item_et_share_user_add, parent, false));
        } else if (viewType == ITEM_TYPE_MINUS) {
            return new DeleteUserHolder(mInflater.inflate(R.layout.item_et_share_user_delete, parent, false));
        } else {
            return new NormalViewHolder(mInflater.inflate(R.layout.item_et_user_head, parent, false));
        }
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {

        if (holder instanceof NormalViewHolder) {
            final NormalViewHolder mHolder = (NormalViewHolder) holder;

            mHolder.mItem = datas.get(position);
            Uri uri;
            if (null==mHolder.mItem.getPhoto()){
                uri=null;
            }else {
                uri  = Uri.parse(mHolder.mItem.getPhoto());
            }
            mHolder.etUserHeadName.setText(mHolder.mItem.getName());
            DraweeController controller = Fresco.newDraweeControllerBuilder()
                    .setImageRequest(ImageRequest.fromUri(uri))
                    .setOldController(mHolder.etUserHeadImg.getController())
                    .build();
            mHolder.etUserHeadImg.setController(controller);

        } else if (holder instanceof AddUserHolder) {
            final AddUserHolder mHolder = (AddUserHolder) holder;
            mHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (addShareUserClickListener != null) {
                        addShareUserClickListener.onAddShareUser();
                    }
                }
            });


        } else if (holder instanceof DeleteUserHolder) {
            final DeleteUserHolder mHolder = (DeleteUserHolder) holder;
            mHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (deleteShareUserClickListener != null) {
                        deleteShareUserClickListener.onDeleteShareUser();
                    }
                }
            });
        }
    }


    @Override
    public int getItemViewType(int position) {

        if (position >= 0 && position < datas.size()) {
            return ITEM_TYPE_NORMA;
        } else if (position == datas.size()) {
            return ITEM_TYPE_ADD;
        } else if (position == datas.size() + 1) {
            return ITEM_TYPE_MINUS;
        } else {
            return ITEM_TYPE_NORMA;
        }


    }


    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return datas.size() + 2;
    }


    private class NormalViewHolder extends ViewHolder {
        public final View mView;
        UserShareModel mItem;
        SimpleDraweeView etUserHeadImg;
        TextView etUserHeadName;


        NormalViewHolder(View itemView) {
            super(itemView);
            mView = itemView;
            etUserHeadImg = (SimpleDraweeView) itemView.findViewById(R.id.et_user_head_img);
            etUserHeadName = (TextView) itemView.findViewById(R.id.et_user_head_name);

        }


    }


    private class AddUserHolder extends ViewHolder {

        public final View mView;

        public AddUserHolder(View view) {
            super(view);
            mView = view;

        }
    }

    private class DeleteUserHolder extends ViewHolder {

        public final View mView;


        public DeleteUserHolder(View view) {
            super(view);
            mView = view;


        }
    }


    private AddShareUserClickListener addShareUserClickListener;

    public void setAddShareUserClickListener(AddShareUserClickListener addShareUserClickListener) {
        this.addShareUserClickListener = addShareUserClickListener;
    }

    public interface AddShareUserClickListener {
        void onAddShareUser();
    }


    private DeleteShareUserClickListener deleteShareUserClickListener;

    public void setDeleteShareUserClickListener(DeleteShareUserClickListener deleteShareUserClickListener) {
        this.deleteShareUserClickListener = deleteShareUserClickListener;
    }

    public interface DeleteShareUserClickListener {
        void onDeleteShareUser();
    }


}
