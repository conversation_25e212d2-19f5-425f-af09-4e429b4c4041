package com.czur.cloud.ui.mirror;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.SittingModelChangeEvent;
import com.czur.cloud.ui.component.seekbar.BubbleSeekBarSitting;
import com.czur.cloud.ui.mirror.comm.FastBleConstants;
import com.czur.cloud.ui.mirror.comm.FastBleHttpUtils;
import com.czur.cloud.ui.mirror.comm.FastBleOperationUtils;
import com.czur.cloud.ui.mirror.mydialog.SittingDialog;
import com.github.iielse.switchbutton.SwitchView;

import org.greenrobot.eventbus.EventBus;

public class SittingLightActivity extends SittingBaseActivity {
    private BubbleSeekBarSitting seekBar;
    private TextView lightValue;
    private ImageView btnPlus,btnMinus;
    private final int SEEKBARMAX = FastBleConstants.MAX_LIGHT_NUMBER;
    private final int SEEKBARMIN = FastBleConstants.MIN_LIGHT_NUMBER;
    private int lightLevel, lightOldLevel, oldLightLevel;
    private String lightName;
    private SwitchView sittingLightSwitch;
    private boolean isOpen, isOldOpen;
    private long lastTime = 0;
    private LinearLayout light_progress_ll;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_sitting_light);
        initView();
        initListener();
        setPageTitle(R.string.sitting_home_light);

    }

    protected void initView() {
        deviceModel = userPreferences.getSittingDeviceModel();

        light_progress_ll = findViewById(R.id.light_progress_ll);
        imgBack = findViewById(R.id.top_bar_back_btn);
        seekBar = (BubbleSeekBarSitting) findViewById(R.id.seekBar);
        lightValue = findViewById(R.id.sitting_home_vol_level_tv);
        btnPlus = findViewById(R.id.sitting_home_plus_btn);
        btnMinus = findViewById(R.id.sitting_home_minus_btn);

        isOpen = deviceModel.getLightSwitch()==1;
        isOldOpen = isOpen;
        if (isOpen) {
            light_progress_ll.setVisibility(View.VISIBLE);
        }else{
            light_progress_ll.setVisibility(View.GONE);
        }

        lightLevel = deviceModel.getLight();
        lightOldLevel = lightLevel;
        oldLightLevel = lightLevel;

        sittingLightSwitch = findViewById(R.id.sitting_happy_time_switch);

        sittingLightSwitch.setOnStateChangedListener(new SwitchView.OnStateChangedListener() {
            @Override
            public void toggleToOn(SwitchView view) {
                checkHappyTimeSwitch(true);
                setDeviceModelSwitch(true);
            }

            @Override
            public void toggleToOff(SwitchView view) {
                if (FastBleOperationUtils.getDeviceSilent() == FastBleConstants.SITTING_SILENT_INT_YES){
                    showWarnAlertDialog();
                }else {
                    checkHappyTimeSwitch(false);
                    setDeviceModelSwitch(false);
                }
            }
        });

        sittingLightSwitch.setOpened(isOpen);

        lightValue.setText(lightLevel+"");
        seekBar.setProgress((float) lightLevel);

        seekBar.setOnProgressChangedListener(new BubbleSeekBarSitting.OnProgressChangedListener(){

            @Override
            public void onProgressActionDown(BubbleSeekBarSitting bubbleSeekBar, int progress, float progressFloat, boolean fromUser) {
            }

            @Override
            public void onProgressChanged(BubbleSeekBarSitting bubbleSeekBar, int progress, float progressFloat, boolean fromUser) {
                if (progress == 0){
                    progress = 1;
                    seekBar.setProgress((float)progress);
                }
                lightValue.setText(progress+"");
            }

            @Override
            public void getProgressOnActionUp(BubbleSeekBarSitting bubbleSeekBar, int progress, float progressFloat) {
                if (progress == 0){
                    progress = 1;
                    seekBar.setProgress((float)progress);
                }
                lightLevel = progress;
                lightValue.setText(progress+"");
                setDeviceModelValue();
            }

            @Override
            public void getProgressOnFinally(BubbleSeekBarSitting bubbleSeekBar, int progress, float progressFloat, boolean fromUser) {
            }
        });

        checkHappyTimeSwitch(isOpen);
    }

    private void showWarnAlertDialog() {
        SittingDialog dialog = new SittingDialog(SittingLightActivity.this, R.style.sittingDialog,
                confirm -> {
                    if (confirm) {
                        checkHappyTimeSwitch(false);
                        setDeviceModelSwitch(false);
                    }else{
                        checkHappyTimeSwitch(true);
                        setDeviceModelSwitch(true);
                    }
                });
        dialog.setPositiveButton(getString(R.string.czur_dialog_ok));
        dialog.setNegativeButton(getString(R.string.czur_dialog_cancel));
        dialog.setTitle(getString(R.string.czur_dialog_title));
        dialog.setContent(getString(R.string.sitting_light_warm_msg));
        dialog.create();
        dialog.setCanceledOnTouchOutside(false);//加上这个，点击空白处不消失
        dialog.show();
    }

    private void checkHappyTimeSwitch(boolean isChecked) {
        isOpen = isChecked;
        sittingLightSwitch.toggleSwitch(isChecked);
        float alpha = 1F;
        if (!isChecked) {
            alpha = 0.5F;
        }
        seekBar.setAlpha(alpha);
        seekBar.setEnabled(isChecked);
        seekBar.setClickable(isChecked);
        btnPlus.setAlpha(alpha);
        btnPlus.setEnabled(isChecked);
        btnPlus.setClickable(isChecked);
        btnMinus.setAlpha(alpha);
        btnMinus.setEnabled(isChecked);
        btnMinus.setClickable(isChecked);

        if (isOpen) {
            light_progress_ll.setVisibility(View.VISIBLE);
        }else{
            light_progress_ll.setVisibility(View.GONE);
        }
    }

    protected void initListener() {
        imgBack.setOnClickListener(this);
        btnPlus.setOnClickListener(this);
        btnMinus.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        int p = lightLevel;

        switch (v.getId()) {
            case R.id.top_bar_back_btn:
                ActivityUtils.finishActivity(this, true);
                break;

            case R.id.sitting_home_plus_btn:
                p = p + 1;
                if (p > SEEKBARMAX)
                    p = SEEKBARMAX;

                setVolumeProgress(p);
                break;

            case R.id.sitting_home_minus_btn:
                p = p - 1;
                if (p < SEEKBARMIN)
                    p = SEEKBARMIN;

                setVolumeProgress(p);
                break;
        }
    }

    private void setVolumeProgress(int p){
        seekBar.setProgress((float) p);
        lightLevel = p;
        setDeviceModelValue();
    }

    private void setDeviceModelValue() {

        FastBleOperationUtils.SetDeviceParams(lightLevel, FastBleConstants.HEAD_SETTING_LASER_LIGHT);

        deviceModel = userPreferences.getSittingDeviceModel();
        deviceModel.setLight(lightLevel);
        int flag = isOpen?1:0;
        deviceModel.setLightSwitch(flag);
        if (isOpen){
            deviceModel.setLightName(lightLevel+"");
        }else{
            deviceModel.setLightName(CZURConstants.SITTING_LIGHT_NAME_CLOSE);
        }
        EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_CHANGE_LIGHTLEVEL, deviceModel));

        //String u_id, String equipmentId, String level
        FastBleHttpUtils.setSittingDeviceLightLevel(userPreferences.getUserId(),
                equipmentId,
                lightLevel + "");
    }

    private void setDeviceModelSwitch(boolean isFlag) {

        int flag = isFlag?1:0;

        FastBleOperationUtils.SetDeviceParams(flag, FastBleConstants.HEAD_SETTING_LASER_SWITCH);

        deviceModel = userPreferences.getSittingDeviceModel();
        deviceModel.setLight(lightLevel);
        deviceModel.setLightSwitch(flag);
        if (isOpen){
            deviceModel.setLightName(lightLevel+"");
        }else{
            deviceModel.setLightName(CZURConstants.SITTING_LIGHT_NAME_CLOSE);
        }
        EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_CHANGE_LIGHTLEVEL, deviceModel));

        //String u_id, String equipmentId, String level
        FastBleHttpUtils.setSettingLightSwitch(userPreferences.getUserId(),
                equipmentId,
                flag+"");
    }
}
