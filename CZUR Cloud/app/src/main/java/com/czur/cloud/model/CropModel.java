package com.czur.cloud.model;

import android.graphics.Bitmap;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2016/9/9.
 */
public class CropModel implements Serializable {

    private String fileId;
    private String ossKey;
    private String ossMiddleKey;
    private String ossSmallKey;
    private String ossKeyUrl;
    private String ossMiddleKeyUrl;
    private String ossSmallKeyUrl;
    private Bitmap cropBitMap;
    private long fileSize;

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getOssKey() {
        return ossKey;
    }

    public void setOssKey(String ossKey) {
        this.ossKey = ossKey;
    }

    public String getOssMiddleKey() {
        return ossMiddleKey;
    }

    public void setOssMiddleKey(String ossMiddleKey) {
        this.ossMiddleKey = ossMiddleKey;
    }

    public String getOssSmallKey() {
        return ossSmallKey;
    }

    public void setOssSmallKey(String ossSmallKey) {
        this.ossSmallKey = ossSmallKey;
    }

    public String getOssKeyUrl() {
        return ossKeyUrl;
    }

    public void setOssKeyUrl(String ossKeyUrl) {
        this.ossKeyUrl = ossKeyUrl;
    }

    public String getOssMiddleKeyUrl() {
        return ossMiddleKeyUrl;
    }

    public void setOssMiddleKeyUrl(String ossMiddleKeyUrl) {
        this.ossMiddleKeyUrl = ossMiddleKeyUrl;
    }

    public String getOssSmallKeyUrl() {
        return ossSmallKeyUrl;
    }

    public void setOssSmallKeyUrl(String ossSmallKeyUrl) {
        this.ossSmallKeyUrl = ossSmallKeyUrl;
    }

    public Bitmap getCropBitMap() {
        return cropBitMap;
    }

    public void setCropBitMap(Bitmap cropBitMap) {
        this.cropBitMap = cropBitMap;
    }


    public long getFileSize() {
        return fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }
}
