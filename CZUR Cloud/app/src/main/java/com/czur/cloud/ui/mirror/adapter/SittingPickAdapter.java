package com.czur.cloud.ui.mirror.adapter;

import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.czur.cloud.R;
import com.czur.cloud.ui.base.BaseActivity;

import java.util.List;

public class SittingPickAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    private int position;
    private BaseActivity activity;
    private List<String> list;
    public SittingPickAdapter(BaseActivity activity, List<String> list, int position) {
        this.activity = activity;
        this.list = list;
        this.position = position;
    }
    public void setOnItemPickListener(OnItemPickListener onItemPickListener) {
        this.onItemPickListener = onItemPickListener;
    }
    private OnItemPickListener onItemPickListener;

    public void setPosition(int position){
        this.position = position;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ItemHolder(activity.getLayoutInflater().inflate(R.layout.item_mirror_sitting, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int i) {
        ItemHolder itemHolder = (ItemHolder) holder;
        itemHolder.textView.setText(list.get(i));
        if (position == i) {
            itemHolder.textView.setSelected(true);
//            itemHolder.textView.getPaint().setFakeBoldText(true);
            itemHolder.imageView.setVisibility(View.VISIBLE);
        } else {
            itemHolder.textView.setSelected(false);
//            itemHolder.textView.getPaint().setFakeBoldText(false);
            itemHolder.imageView.setVisibility(View.GONE);
        }

        itemHolder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                position = i;
                notifyDataSetChanged();
                if (onItemPickListener != null) {
                    onItemPickListener.onItemPick(i);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return list.size();
    }

    public void setData(int sedentaryReminderDuration) {
        this.position = sedentaryReminderDuration;
        notifyDataSetChanged();
    }


    public interface OnItemPickListener {
        void onItemPick(int position);
    }


    private static class ItemHolder extends RecyclerView.ViewHolder {
        TextView textView;
        ImageView imageView;

        ItemHolder(View itemView) {
            super(itemView);
            textView = itemView.findViewById(R.id.tv_item);
            imageView = itemView.findViewById(R.id.img_check);
        }
    }
}
