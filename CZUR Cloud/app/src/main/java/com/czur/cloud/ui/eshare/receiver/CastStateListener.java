package com.czur.cloud.ui.eshare.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;

import com.blankj.utilcode.util.AppUtils;
import com.czur.czurutils.log.CZURLogUtilsKt;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <NAME_EMAIL> on 2016/11/15.
 */
public class CastStateListener {
    private static String TAG = "CastStateListener";
    // 手机投屏广播
    public static final String ACTION_CAST_START = "com.eshare.action.mirror.connected";
    public static final String ACTION_CAST_STOP = "com.eshare.action.mirror.disconnected";
    public final static String ACTION_MIRROR_PAUSE = "com.eshare.mirror.pausemirror";
    public final static String ACTION_MIRROR_RESUME = "com.eshare.mirror.resumemirror";
    public final static String ACTION_MIRROR_FAILD = "com.eshare.action.mirror.faild";
    private List<Callback> mCallbacks;
    private CastReceiver mCastReceiver;
    private Context mContext;
    // 当前是否处于投屏状态
    private int castStaus;

    private CastStateListener() {
        mCallbacks = new ArrayList<>();
    }

    public static CastStateListener getSingleton() {
        return Holder.sCastStateListener;
    }

    public void initListener(Context context) {
        mContext = context;
    }

    public void registerCallback(Callback callback) {
        mCallbacks.add(callback);
        if (!mCallbacks.isEmpty()) {
            IntentFilter filter = new IntentFilter();
            filter.addAction(ACTION_CAST_START);
            filter.addAction(ACTION_CAST_STOP);
            filter.addAction(ACTION_MIRROR_PAUSE);
            filter.addAction(ACTION_MIRROR_RESUME);
            filter.addAction(ACTION_MIRROR_FAILD);
            mCastReceiver = new CastReceiver();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                mContext.registerReceiver(mCastReceiver, filter, Context.RECEIVER_NOT_EXPORTED);
            } else {
                mContext.registerReceiver(mCastReceiver, filter);
            }
        }
    }

    public void unregisterCallback(Callback callback) {
        mCallbacks.remove(callback);
        if (mCallbacks.isEmpty()) {
            mContext.unregisterReceiver(mCastReceiver);
            mCastReceiver = null;
        }
    }

    public int getCastStaus() {
        return castStaus;
    }

    public interface Callback {
        void onCastStateChanged(int castStaus);
    }

    private static class Holder {
        private static CastStateListener sCastStateListener = new CastStateListener();
    }

    private class CastReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {

            if (!AppUtils.getAppInfo().getPackageName().equals(intent.getPackage())) {
                return;
            }

            CZURLogUtilsKt.logTagI("收到广播==" + TAG,ACTION_CAST_START);

            String action = intent.getAction();

            if (ACTION_CAST_START.equals(action)) {
                //开始投屏
                castStaus = 1;
                for (Callback callback : mCallbacks) {
                    if (callback != null)
                        callback.onCastStateChanged(castStaus);
                }
            } else if (ACTION_CAST_STOP.equals(action)) {
                //结束投屏
                castStaus = 0;
                for (Callback callback : mCallbacks) {
                    if (callback != null)
                        callback.onCastStateChanged(castStaus);
                }
            } else if (ACTION_MIRROR_RESUME.equals(action)) {
                //继续投屏
                castStaus = 2;
                for (Callback callback : mCallbacks) {
                    if (callback != null)
                        callback.onCastStateChanged(castStaus);
                }

            } else if (ACTION_MIRROR_PAUSE.equals(action)) {
                //暂停投屏
                castStaus = 3;
                for (Callback callback : mCallbacks) {
                    if (callback != null)
                        callback.onCastStateChanged(castStaus);
                }

            } else if (ACTION_MIRROR_FAILD.equals(action)) {
                castStaus = -1;
                //投屏失败判断
                for (Callback callback : mCallbacks) {
                    if (callback != null)
                        callback.onCastStateChanged(castStaus);
                }
            }

        }
    }
}
