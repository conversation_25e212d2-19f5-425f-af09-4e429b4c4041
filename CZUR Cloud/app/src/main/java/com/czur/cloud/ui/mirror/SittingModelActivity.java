package com.czur.cloud.ui.mirror;

import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.content.Intent;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.SittingCommonEvent;
import com.czur.cloud.event.SittingModelChangeEvent;
import com.czur.cloud.ui.mirror.adapter.SittingPickAdapter;
import com.czur.cloud.ui.mirror.comm.FastBleConstants;
import com.czur.cloud.ui.mirror.comm.FastBleHexUtils;
import com.czur.cloud.ui.mirror.comm.FastBleHttpUtils;
import com.czur.cloud.ui.mirror.comm.FastBleOperationUtils;
import com.czur.cloud.ui.mirror.model.SittingDeviceModel;
import com.czur.cloud.ui.mirror.mydialog.SittingDialog;
import com.czur.cloud.util.validator.Validator;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Arrays;
import java.util.Calendar;

public class SittingModelActivity extends SittingBaseActivity {
    private String sittingModelName;
    private int iSittingModel, iOldSittingModel;
    private RecyclerView recyclerView;
    private SittingPickAdapter adapter;
    private RelativeLayout defineSitPicture;
    private RelativeLayout sittingHomeStandarSittingRl;     // 自定义坐姿图像的RL
    private TextView sittingHomeStandarSittingTv;           // 自定义坐姿图像的title
    private TextView sittingHomeStandarSittingValueTv;      // 自定义坐姿图像的value
    private ImageView sittingHomeStandarSittingStatusImg;   // 自定义坐姿图像的 状态图标
    private ImageView sittingHomeStandarSittingSmallImg, ble_img_loading;    // 自定义坐姿图像的 缩略图
    private ImageView sittingHomeStandarSittingRedImg;    // 自定义坐姿图像的 小红点

    private int inputStatus = 0;//坐姿录入状态 0：未录入；1：已经录入；2：录入失败；3：重新录入；4：正在录入
    private TextView btnExperience;
    private Timecount timer;

    private LinearLayout sittingModelMoreRlA, sittingModelMoreRlB;//sitting_model_more_rl_a, sitting_model_more_rl_b;
    private int experienceCount = 0;
    private long lastTime = 0;
    private View page_body_mask_view;
    private boolean isExperenceFlag = false;  //是否正在体验模式中。
    private Handler handler=new Handler();
    private Runnable runnable;

    private Animation operatingAnim;

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        logI("SittingModelActivity.onEvent=" + event.getEventType());

        switch (event.getEventType()) {

            case SITTING_EXPER_EXIT:
//                if (AppUtils.isAppForeground()) {
//                    ToastUtils.showLong(getString(R.string.sitting_standar_experience_exit_msg));
//                }
                setBtnExperienceEnable(true);
                FastBleOperationUtils.setIsExperience(false);
                onSetRecyclerViewMask(false);
                handler.removeCallbacks(runnable);
                break;

            case SITTING_EXPER_STATUS:
                SittingCommonEvent commonEvent = (SittingCommonEvent)event;
                String params = commonEvent.getParams();
                //成功进入体验状态:0
                //进入体验状态失败：1
                if (params.equals("0")) {
                    setBtnExperienceEnable(false);
                    FastBleOperationUtils.setIsExperience(true);
                }else{
                    ToastUtils.showLong(getString(R.string.sitting_standar_experience_msg));
                    setBtnExperienceEnable(true);
                    FastBleOperationUtils.setIsExperience(false);
                    onSetRecyclerViewMask(false);
                    handler.removeCallbacks(runnable);
                }
                break;

            case SITTING_PICTURE_STANDAR_CHANGE:
                iSittingModel = 1;
                sittingModelName = CZURConstants.SITTING_MODEL[1];
                adapter.setPosition(iSittingModel);

                deviceModel.setInputStatus(1);
                FastBleOperationUtils.threadSleep(300);
                initStandarUI();
                break;

            case SITTING_APPGET_STATUS: //坐姿监控模式反馈：0：智能坐姿监控；1：自定义坐姿监控；
                SittingCommonEvent commonEvent1 = (SittingCommonEvent)event;
                //0：智能坐姿监控；//1：自定义坐姿监控；
                String params1 = commonEvent1.getParams();
                if (params1.equals("1")){
                    iSittingModel = 1;
                }else{
                    iSittingModel = 0;
                }
                String modelName=CZURConstants.SITTING_MODEL[iSittingModel];
                deviceModel.setPostureMode(iSittingModel);
                deviceModel.setPostureModeName(modelName);
                adapter.setPosition(iSittingModel);
                break;

            case SITTING_FEEDBACK_INPUT://设备主动发出 //坐姿录入结果
                SittingCommonEvent commonEvent2 = (SittingCommonEvent)event;
                //device feedback:"字符串：0，初次成功；1，重新录入成功;2，初次录入失败;3,重新录入失败"
                String params2 = commonEvent2.getParams();
                // //坐姿录入状态 0：未录入；1：已经录入；2：录入失败；3：重新录入；4：正在录入
                int status=0;
                if (Validator.isEmpty(params2)){
                    params2="-";
                }
                switch (params2) {
                    case "0":   //0，初次成功
                    case "1":   //1，重新录入成功
                        status = 1;
                        deviceModel.setInputStatus(status);
                        initStandarUI(true);
                        break;
                    case "3":   //3, 重新录入失败
                        status = 1;
                        deviceModel.setInputStatus(status);
                        initStandarUI();
                        break;
                    case "2":   //2，初次录入失败
                        status = 0;
                        deviceModel.setInputStatus(status);
                        initStandarUI();
                        break;
                }
                break;

            default:
                break;
        }
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_sitting_model);

        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }

        operatingAnim = AnimationUtils.loadAnimation(this, R.anim.ble_scan_rotate);
        operatingAnim.setInterpolator(new LinearInterpolator());

        initView();
        initListener();
        setPageTitle(R.string.sitting_home_model);

    }

    protected void initView() {
        deviceModel = userPreferences.getSittingDeviceModel();

        experienceCount = userPreferences.getExperienceCount();

        imgBack = findViewById(R.id.top_bar_back_btn);
        defineSitPicture = findViewById(R.id.jing_main_sitting_rl);

        sittingModelName = deviceModel.getPostureModeName();
        iSittingModel = deviceModel.getPostureMode();
        iOldSittingModel = iSittingModel;

        deviceModel = userPreferences.getSittingDeviceModel();
        if (deviceModel == null){
            deviceModel = new SittingDeviceModel();
            deviceModel.initDeviceModel();
        }
        inputStatus = deviceModel.getInputStatus();

        btnExperience = findViewById(R.id.btn_standar_experience);
        btnExperience.setOnClickListener(this);

        // 体验中，需要设置按钮置灰
        setBtnExperienceEnable(!FastBleOperationUtils.isIsExperience());

        recyclerView = findViewById(R.id.recycler_view);
        recyclerView.setHasFixedSize(true);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));

        sittingModelMoreRlA = findViewById(R.id.sitting_model_more_btn_a);
        sittingModelMoreRlB = findViewById(R.id.sitting_model_more_btn_b);
        sittingModelMoreRlA.setOnClickListener(this);
        sittingModelMoreRlB.setOnClickListener(this);
        onShowHideMoreBtn(true);

        adapter = new SittingPickAdapter(this, Arrays.asList(CZURConstants.SITTING_MODEL), iSittingModel);
        adapter.setOnItemPickListener(new SittingPickAdapter.OnItemPickListener() {
            @Override
            public void onItemPick(int position) {
                long cTime = Calendar.getInstance().getTimeInMillis();
                if (cTime - lastTime < FastBleConstants.MIN_CLICK_TIME){
                    return;
                }
                lastTime = cTime;

                iSittingModel = position;
                sittingModelName = CZURConstants.SITTING_MODEL[position];
                setDeviceModelValue();
            }
        });
        recyclerView.setAdapter(adapter);

        page_body_mask_view = findViewById(R.id.page_body_mask_view);
        page_body_mask_view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                return;
            }
        });

        // 体验中，需要设置按钮置灰
        onSetRecyclerViewMask(FastBleOperationUtils.isIsExperience());

        //标准坐姿
        sittingHomeStandarSittingRl = (RelativeLayout) findViewById(R.id.jing_main_sitting_rl);
        sittingHomeStandarSittingTv = (TextView) findViewById(R.id.jing_main_setting_tv);
        sittingHomeStandarSittingValueTv = (TextView) findViewById(R.id.jing_main_setting_value_tv);
        sittingHomeStandarSittingStatusImg = (ImageView)findViewById(R.id.jing_main_setting_statue_img);
        sittingHomeStandarSittingSmallImg = (ImageView)findViewById(R.id.jing_main_setting_small_img);
        ble_img_loading = (ImageView)findViewById(R.id.ble_img_loading);
        sittingHomeStandarSittingRedImg = (ImageView)findViewById(R.id.need_new_img);

        //标准坐姿
        sittingHomeStandarSittingRl.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(v.getContext(), SittingStandarActivity.class);
                intent.putExtra("deviceModel", deviceModel);
                ActivityUtils.startActivity(intent);
            }
        });

        // 获取本地的坐姿图片
        if (deviceModel.getInputStatus() == FastBleConstants.SITTING_DEF_STANDARY_SUCESS_INT) {
            String img_name = FastBleOperationUtils.getLocalStandarPicturePath() + FastBleConstants.SITTING_PICTURE_NAME_STANDAR;
            boolean flag = FastBleHexUtils.getLocalStandarPicture(img_name);
            if (!flag){
                FastBleOperationUtils.setBmpStandar(null);
                // 本地没有，则从服务器获取；
                String localStandardImagePath=FastBleOperationUtils.getLocalStandarPicturePath();
                String imageKey = deviceModel.getStandPositionImage();
                new Thread(new Runnable(){
                    @Override
                    public void run() {
                        boolean flag1 = FastBleHttpUtils.downloadOSSPicture(localStandardImagePath,
                                FastBleConstants.SITTING_PICTURE_NAME_STANDAR,
                                imageKey);
                        logI("SittingFragment.checkLocalPicture.flag1="+flag1);
                    }
                }).start();
            }
        }

        // 初始化自定义坐姿图像状态
        initStandarUI();

    }

    // 初始化自定义坐姿图像状态
    private void initStandarUI(){
        initStandarUI(false);
    }
    private void initStandarUI(boolean isLoading){
        //坐姿录入状态 0：未录入；1：已经录入；2：录入失败；3：重新录入；4：正在录入
        int iSittingStatus = deviceModel.getInputStatus();
        sittingHomeStandarSittingRedImg.setVisibility(View.GONE);

        setDefineSitPictureEnable(true);

        // 录入成功==1
        if (iSittingStatus == 1){

            String img_name = FastBleOperationUtils.getLocalStandarPicturePath() + FastBleConstants.SITTING_PICTURE_NAME_STANDAR;
            boolean flag = FastBleHexUtils.getLocalStandarPicture(img_name);
            FastBleOperationUtils.threadSleep();
            sittingHomeStandarSittingStatusImg.setImageResource(R.mipmap.sitting_ok);
            sittingHomeStandarSittingRedImg.setVisibility(View.GONE);
            sittingHomeStandarSittingValueTv.setText(getString(R.string.sitting_standar_sitting_status1_right));
            sittingHomeStandarSittingValueTv.setTextColor(getColor(R.color.gray_bb));
            sittingHomeStandarSittingSmallImg.setVisibility(View.VISIBLE);
            ble_img_loading.setVisibility(View.GONE);
            if (isLoading){
                sittingHomeStandarSittingSmallImg.setVisibility(View.INVISIBLE);
                ble_img_loading.setVisibility(View.VISIBLE);
                ble_img_loading.startAnimation(operatingAnim);
            }else {
                ble_img_loading.clearAnimation();
                sittingHomeStandarSittingSmallImg.setImageBitmap(FastBleOperationUtils.getBmpStandar());
            }
        }else{
            sittingHomeStandarSittingRedImg.setVisibility(View.GONE);
            sittingHomeStandarSittingValueTv.setText(getString(R.string.sitting_standar_sitting_status0_no));
            sittingHomeStandarSittingValueTv.setTextColor(getColor(R.color.jing_standar_red_color));
            sittingHomeStandarSittingSmallImg.setVisibility(View.GONE);
            ble_img_loading.setVisibility(View.GONE);
            sittingHomeStandarSittingStatusImg.setImageResource(R.mipmap.sitting_no);

        }
    }

    private void setDefineSitPictureEnable(boolean flag){
        float alpha = 0.5F;
        if (flag) {
            alpha = 1F;
        }
        defineSitPicture.setAlpha(alpha);
        defineSitPicture.setClickable(flag);
        defineSitPicture.setEnabled(flag);
    }

    protected void initListener() {
        imgBack.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.top_bar_back_btn:
                ActivityUtils.finishActivity(this);
                break;

            case R.id.btn_standar_experience:
                showDialog();
                break;

            case R.id.sitting_model_more_btn_a:
                onShowHideMoreBtn(false);
                break;
            case R.id.sitting_model_more_btn_b:
                onShowHideMoreBtn(true);
                break;

        }
    }

    // flag=true;a显示，b隐藏
    private void onShowHideMoreBtn(boolean flag){
        int aFlag = View.VISIBLE;
        int bFlag = View.GONE;
        if (!flag){
            aFlag = View.GONE;
            bFlag = View.VISIBLE;
        }
        findViewById(R.id.sitting_model_more_rl_a).setVisibility(aFlag);
        findViewById(R.id.sitting_model_more_rl_b).setVisibility(bFlag);
    }

    //体验一下 需要倒计时60s
    private void onClickExprience(){
        // 进入体验状态：1
        FastBleOperationUtils.SetDeviceParams(1, FastBleConstants.HEAD_SETTING_EXPER);

        setBtnExperienceEnable(false);
        FastBleOperationUtils.setIsExperience(true);
        onSetRecyclerViewMask(true);

        runnable = new Runnable() {
            @Override
            public void run() {
//                if (AppUtils.isAppForeground()) {
//                    ToastUtils.showLong(getString(R.string.sitting_standar_experience_exit_msg));
//                }
                setBtnExperienceEnable(true);
                FastBleOperationUtils.setIsExperience(false);
                onSetRecyclerViewMask(false);
            }
        };

        handler.postDelayed(runnable, FastBleConstants.TIMEOUT_FOR_EXPERIENCE);

    }


    private void setDeviceModelValue() {
        SittingDeviceModel deviceModel = userPreferences.getSittingDeviceModel();
        deviceModel.setPostureMode(iSittingModel);
        deviceModel.setPostureModeName(sittingModelName);

        EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_CHANGE_MODEL, deviceModel));
        // 字符串：0，智能模式；1，自定义模式
        FastBleOperationUtils.SetDeviceParams(iSittingModel, FastBleConstants.HEAD_SETTING_MONITOR);

        //api需要转换一下：1为智能坐姿 2位自定义坐姿
        int postureMode = iSittingModel == 0 ? 1:2;
        FastBleHttpUtils.setSittingPostureMode(userPreferences.getUserId(),
                equipmentId,
                postureMode + "");
    }

    class Timecount extends CountDownTimer {

        public Timecount(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @Override
        public void onTick(long millisUntilFinished) {
            setBtnExperienceEnable(false);
            btnExperience.setText(millisUntilFinished / 1000 + "s");
        }

        @Override
        public void onFinish() {// 计时结束
            setBtnExperienceEnable(true);
            btnExperience.setText(getString(R.string.sitting_standar_experience));

            //退出体验状态：0
//            FastBleOperationUtils.SetDeviceParams(0, FastBleConstants.HEAD_SETTING_EXPER);
        }

    }

    private void setBtnExperienceEnable(boolean flag){
        float alpha = 0.5F;
        String title = getString(R.string.sitting_standar_experience30_ing);
        if (flag) {
            alpha = 1F;
            title = getString(R.string.sitting_standar_experience30);
        }
        btnExperience.setAlpha(alpha);
        btnExperience.setEnabled(flag);
        btnExperience.setClickable(flag);
        btnExperience.setText(title);

        // list也要灰掉、、recyclerView

    }

    /**
     * 显示对话框
     */
    private void showDialog() {

//        if (experienceCount >= 3){
//            onClickExprience();
//            return;
//        }
        SittingDialog dialog = new SittingDialog(this, R.style.sittingDialog,
                new SittingDialog.OncloseListener() {
                    @Override
                    public void onClick(boolean confirm) {
                        if (confirm) {
                            onClickExprience();
                        }
                    }
                });
        dialog.setTitle(getString(R.string.sitting_model_dialog_title));
        dialog.setContent(getString(R.string.sitting_standar_experience30_alert));
        dialog.setNegativeButton(getString(R.string.sitting_model_dialog_no));
        dialog.setGrayCancelButton(true);
        dialog.setPositiveButton(getString(R.string.sitting_model_dialog_yes));
        dialog.show();
        experienceCount++;
        userPreferences.setExperienceCount(experienceCount);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        FastBleOperationUtils.threadSleep(FastBleConstants.RUN_DELAY_TIMES100);
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

    private void onSetRecyclerViewMask(boolean flag){
        if (flag){
            page_body_mask_view.setVisibility(View.VISIBLE);
            page_body_mask_view.setAlpha(0.5F);
        }else{
            page_body_mask_view.setVisibility(View.GONE);
            page_body_mask_view.setAlpha(1.0F);
        }

    }
}
