package com.czur.cloud.ui.starry.meeting.widget

import android.animation.ValueAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import androidx.lifecycle.LiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.findViewTreeLifecycleOwner
import com.czur.cloud.R

class NetworkQualityView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {

    private var quality: LiveData<Int>? = null
    private var showVolume = 0

    private val qualityObservable = Observer<Int> { startAnim() }

    private val lifecycleOwner by lazy {
        findViewTreeLifecycleOwner()!!
    }


    private val anim: ValueAnimator by lazy {
        initAnim()
    }

    /**
     * 初始化音量变动时的公话
     */
    private fun initAnim(): ValueAnimator {
        val anim = ValueAnimator.ofInt(0)
            // 该回调每 2 秒触发一次。如果远端有多个用户/主播，该回调每 2 秒会被触发多次。
            .setDuration(2001)
        anim.addUpdateListener(::animListener)
        return anim
    }

    /**
     * 设置音量数据的liveData
     * @param qualityData: 网络质量数据的LiveData
     */
    fun setQualityData(qualityData: LiveData<Int>) {
        if (qualityData === quality) {
            // 如果设定的liveData和目前已经存在的liveData是同一个对象,
            // 就不需要做任何操作
            return
        }
        // 移除的时候, 只移除observable对象,防止影响其他View
        quality?.removeObserver(qualityObservable)
        qualityData.observe(lifecycleOwner, qualityObservable)
        quality = qualityData
    }

    fun setQualityDataNetworkError() {
        this.setImageResource(R.mipmap.starry_quality_1)
        this.visibility = View.VISIBLE
    }

    fun setQualityDataNetworkErrorMain() {
        quality?.removeObserver(qualityObservable)
        quality = null
        this.setImageResource(R.mipmap.starry_quality_1)
        this.visibility = View.VISIBLE
    }

    fun setQualityDataNetworkGone() {
        this.setImageResource(R.color.transparent)
        this.visibility = View.INVISIBLE
    }
    fun setQualityDataNetworkVisible() {
        this.setImageResource(R.color.transparent)
        this.visibility = View.VISIBLE
    }

    fun removeQualityData() {
        quality?.removeObserver(qualityObservable)
        quality = null
    }

    /**
     * 开始动画
     */
    private fun startAnim() {
        when(quality?.value){
//            // Only For Test
//            1, 2, 3 -> {
            3 -> {
                this.setImageResource(R.mipmap.starry_quality_3)
            }
            4 -> {
                this.setImageResource(R.mipmap.starry_quality_2)
            }
            5, 6 -> {
                this.setImageResource(R.mipmap.starry_quality_1)
            }
            else ->  {
                this.setImageResource(R.color.transparent)
            }
        }
        this.visibility = View.VISIBLE
    }

    /**
     * 动画数据改变的监听
     * 数据改变时 刷新页面
     */
    private fun animListener(animation: ValueAnimator) {
        val current = showVolume
        showVolume = animation.animatedValue as Int
        if (current != showVolume) {
            invalidate()
        }
    }

    override fun onVisibilityAggregated(isVisible: Boolean) {
        super.onVisibilityAggregated(isVisible)
    }
}


