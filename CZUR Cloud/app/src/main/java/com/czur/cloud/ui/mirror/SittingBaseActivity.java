package com.czur.cloud.ui.mirror;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.mirror.model.SittingDeviceModel;
import com.czur.cloud.util.validator.Validator;

public class SittingBaseActivity extends BaseActivity implements View.OnClickListener {
    protected ImageView imgBack;
    protected UserPreferences userPreferences;
    protected SittingDeviceModel deviceModel;
    protected String equipmentId;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_sitting_base);

        userPreferences = UserPreferences.getInstance(this);
        imgBack = findViewById(R.id.top_bar_back_btn);
        imgBack.setOnClickListener(this);

        deviceModel = (SittingDeviceModel) getIntent().getSerializableExtra("deviceModel");
        if (Validator.isEmpty(deviceModel)){
            deviceModel = new SittingDeviceModel();
            deviceModel.setId(0);
        }
        userPreferences.setSittingDeviceModel(deviceModel);
        if (deviceModel != null) {
            equipmentId = deviceModel.getEquipmentUID();
        }else{
            equipmentId = "";
        }

        setPageTitle(R.string.sitting_home_text);
    }

    protected void setPageTitle(int id){
        TextView title = (TextView) findViewById(R.id.top_bar_title);
        title.setText(id);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.top_bar_back_btn:
                ActivityUtils.finishActivity(this);
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

}
