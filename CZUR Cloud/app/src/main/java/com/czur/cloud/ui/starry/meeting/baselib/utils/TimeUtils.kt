package com.czur.cloud.ui.starry.meeting.baselib.utils

import android.content.Context
import com.czur.cloud.R
import java.text.SimpleDateFormat
import java.util.*

const val ONE_SECOND = 1000L
const val ONE_MIN = 60 * ONE_SECOND
const val ONE_HOUR = 60 * ONE_MIN
const val ONE_DAY = 24 * ONE_HOUR

val HHMM_FORMAT by lazy {
    SimpleDateFormat("HH:mm", Locale.getDefault())
}
private val sdf = HHMM_FORMAT


private const val PATTERN_YEAR_MONTH_DAY = "yyyy/MM/dd"
private const val PATTERN_YEAR = "yyyy"

private val commonDate: Date = Date()


fun getDateFromPattern(pattern: String, timeStr: String): Date {
    sdf.applyPattern(pattern)
    return sdf.parse(timeStr) ?: Date(0)
}

/**
 * 根据传入的模板,获取时间字符串
 * @param pattern:  时间模板
 * @param time:     指定的时间,如果没有指定,则以当前时间生成字符串
 * @return 时间日期字符串
 */
fun getTimeStr(
    pattern: String,
    time: Date = commonDate.apply { setTime(System.currentTimeMillis()) }
): String {
    sdf.applyPattern(pattern)
    return sdf.format(time)
}

fun Date.toString(pattern: String): String {
    return getTimeStr(pattern, this)
}

/**
 * 判断Date是否是今天
 * @return true, 是今天  false: 不是今天
 */
fun Date.isToday(): Boolean {
    val nowStr = getTimeStr(PATTERN_YEAR_MONTH_DAY)
    val thisStr = getTimeStr(PATTERN_YEAR_MONTH_DAY, this)
    return nowStr == thisStr
}

/**
 * 判断Date是否是昨天
 */
fun Date.isYesterday(): Boolean {
    commonDate.time = System.currentTimeMillis() - ONE_DAY
    val yesterdayStr = getTimeStr(PATTERN_YEAR_MONTH_DAY, commonDate)
    val thisStr = getTimeStr(PATTERN_YEAR_MONTH_DAY, this)
    return yesterdayStr == thisStr
}

/**
 * 判断Date是否是今年
 */
fun Date.isThisYear(): Boolean {
    val nowYearStr = getTimeStr(PATTERN_YEAR)
    val thisYearStr = getTimeStr(PATTERN_YEAR, this)
    return nowYearStr == thisYearStr
}

/**
 * 当前时分
 * @return
 */
fun todayHhMm(): String {
    return HHMM_FORMAT.format(Date())
}

/**
 * 某个时间戳的时分
 * @return
 */
fun dateToHhMm(date: Long): String {
    return HHMM_FORMAT.format(Date(date))
}

/**
 * 获取星期几
 * @param date
 * @return
 */
fun getDayInWeek(context: Context): String {
    val cal = Calendar.getInstance()
    cal.time = Date()
    val days = context.resources.getStringArray(R.array.baselib_day_of_week)
    return days[cal.get(Calendar.DAY_OF_WEEK) - 1]
}

//获取当月第一天本周第几天
fun getFirstDayInMouthAsWeekIndex(change: Int): Int {
    val cal = Calendar.getInstance()
    cal.add(Calendar.MONTH, change)
    cal.set(Calendar.DAY_OF_MONTH, 1)
    return cal.get(Calendar.DAY_OF_WEEK) - 1
}

//获取当月第一天日期
fun getFirstDayDate(change: Int): Date {
    val cal = Calendar.getInstance()
    cal.add(Calendar.MONTH, change)
    cal.set(Calendar.DAY_OF_MONTH, 1)
    return cal.time
}

fun getNearDate(date: Date, near: Int): Date {
    val cal = Calendar.getInstance()
    cal.time = date //把当前时间赋给日历
    cal.add(Calendar.DAY_OF_MONTH, near)
    return cal.time
}


fun getDay(date: Date): Int {
    val cal = Calendar.getInstance()
    cal.time = date //把当前时间赋给日历
    return cal.get(Calendar.DAY_OF_MONTH)
}

//获取当月有多少天
fun getDaysInMouth(change: Int): Int {
    val cal = Calendar.getInstance()
    cal.add(Calendar.MONTH, change)
    return cal.getActualMaximum(Calendar.DAY_OF_MONTH)
}

fun getDate(indexInMonth: Int, change: Int): Long {
    val cal = Calendar.getInstance()
    cal.add(Calendar.MONTH, change)
    cal.set(Calendar.DAY_OF_MONTH, indexInMonth + 1)
    cal.set(Calendar.HOUR_OF_DAY, 0)
    cal.set(Calendar.SECOND, 0)
    cal.set(Calendar.MINUTE, 0)
    cal.set(Calendar.MILLISECOND, 0)
    return cal.time.time
}

fun getYearAndMonth(change: Int): Pair<Int, Int> {
    val cal = Calendar.getInstance()
    cal.add(Calendar.MONTH, change)
    val year = cal.get(Calendar.YEAR)
    val month = cal.get(Calendar.MONTH) + 1
    return Pair(year, month)
}

fun getTimeStampCurrent(time: Long, dateFormatString: String): String {
    val date = Date(time)
    val dateFormat = SimpleDateFormat(dateFormatString)
    return dateFormat.format(date)
}

/**
 * 获取持续时间的字符串
 * @param hourUnitStr: 小时单位
 * @param minUnitStr:  分钟单位
 * @param secondUnitStr: 秒单位
 * @param durationInMs: 持续时间(单位毫秒)
 * @return 格式化后的持续时间, 如果那个单位为0, 则不会显示, 如果不到0秒, 则显示0秒
 *  例如 7分钟30秒(不显示小时)、1小时52秒(不显示分钟)、2分钟(不显示小时和秒)
 */
fun getDurationStr(
    hourUnitStr: String,
    minUnitStr: String,
    secondUnitStr: String,
    durationInMs: Long
): String {

    val builder = StringBuilder()
    val hour: Int = (durationInMs / ONE_HOUR).toInt()
    if (hour > 0) {
        builder.append("${hour}${hourUnitStr}")
    }

    val min = (durationInMs / ONE_MIN).toInt() - hour * 60
    if (min > 0) {
        builder.append("${min}${minUnitStr}")
    }

    val second = (durationInMs / ONE_SECOND).toInt() - hour * 60 * 60 - min * 60
    if (second > 0 || (hour == 0 && min == 0)) {
        builder.append("${second}${secondUnitStr}")
    }

    return builder.toString()
}

fun getNumberDurationStr(durationInMs: Long): String {
    val hour: Int = (durationInMs / ONE_HOUR).toInt()
    val min = (durationInMs / ONE_MIN).toInt() - hour * 60
    val second = (durationInMs / ONE_SECOND).toInt() - hour * 60 * 60 - min * 60

    return "${String.format("%02d", hour)}:${String.format("%02d", min)}:${String.format("%02d", second)}"
}



