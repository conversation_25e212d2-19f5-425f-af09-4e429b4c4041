package com.czur.cloud.ui.mirror;

import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.Manifest;
import android.app.AlertDialog;
import android.bluetooth.BluetoothAdapter;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.location.LocationManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.clj.fastble.BleManager;
import com.clj.fastble.scan.BleScanRuleConfig;
import com.czur.cloud.R;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.SittingCommonEvent;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.home.IndexActivity;
import com.czur.cloud.ui.mirror.comm.FastBleConstants;
import com.czur.cloud.ui.mirror.comm.FastBleToolUtils;
import com.czur.cloud.ui.mirror.model.SittingDeviceModel;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import io.realm.Realm;

/**
 * Created by Jason on 2020.12.24
 */

public class SmartSittingActivity extends BaseActivity implements View.OnClickListener {
    private static final int REQUEST_CODE_OPEN_GPS = 1;
    private static final int REQUEST_CODE_PERMISSION_LOCATION = 2;

    private ImageView sittingHomeBackBtn;
    private RelativeLayout sittingHomeMoreBtn;
    private List<SittingDeviceModel> entity;
    private Realm realm;
    private DynamicReceiver dynamicReceiver;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(getWindow(), true);
        setContentView(R.layout.activity_sitting_home);

        logI("======CZUR Mirror======");
        initComponent();

        registerEvent();

        //动态权限检查，蓝牙
        checkPermissions();

        initFragment(savedInstanceState);
        gotoSettingIgnoringBatteryOptimizations();

        BleManager.getInstance().init(getApplication());

        BleManager.getInstance()
                // 默认打开库中的运行日志，如果不喜欢可以关闭
                .enableLog(true)
                // 设置连接时重连次数和重连间隔（毫秒），默认为0次不重连
                .setReConnectCount(FastBleConstants.BLE_RECONNECT_COUNT, FastBleConstants.BLE_OPERATE_TIMEOUT)
                // 设置连接超时时间（毫秒），默认10秒
                .setConnectOverTime(FastBleConstants.BLE_CONNECT_TIMEOUT)
                // 设置readRssi、setMtu、write、read、notify、indicate的超时时间（毫秒），默认5秒
                .setOperateTimeout(FastBleConstants.BLE_OPERATE_TIMEOUT);

        setScanRule();

        if (!BleManager.getInstance().isBlueEnable()) {
            logI("BleManager.getInstance().isBlueEnable()=" + BleManager.getInstance().isBlueEnable());
            Intent enableBtIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                return;
            }
            startActivityForResult(enableBtIntent, FastBleConstants.REQUEST_CODE_OPEN_BLE);
        }

        // 做好初始化！
        SittingDeviceModel deviceModel = UserPreferences.getInstance(this).getSittingDeviceModel();
        if ( deviceModel == null){
            deviceModel = new SittingDeviceModel();
            deviceModel.initDeviceModel();
        }
        UserPreferences.getInstance(this).setSittingDeviceModel(deviceModel);

    }

    private void setScanRule() {
        UUID[] serviceUuids = new UUID[1];
//        String[] names = null;
//        String mac = "";
        boolean isAutoConnect = false;
        serviceUuids[0] = UUID.fromString(FastBleConstants.exportUUID);
        BleScanRuleConfig scanRuleConfig = new BleScanRuleConfig.Builder()
//                .setServiceUuids(serviceUuids)      // 只扫描指定的服务的设备，可选
//                .setDeviceName(true, names)   // 只扫描指定广播名的设备，可选
//                .setDeviceMac(mac)                  // 只扫描指定mac的设备，可选
//                .setAutoConnect(false)      // 连接时的autoConnect参数，可选，默认false
                .setScanTimeOut(FastBleConstants.BLE_SACN_TIMEOUT)              // 扫描超时时间，可选，默认10秒
                .build();
        BleManager.getInstance().initScanRule(scanRuleConfig);
    }

    private void gotoSettingIgnoringBatteryOptimizations() {
        try {
            Intent intent = new Intent();
            String packageName = getPackageName();
            intent.setAction(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
            intent.setData(Uri.parse("package:" + packageName));
            startActivityForResult(intent, 9001);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void initComponent() {
        realm = Realm.getDefaultInstance();
        sittingHomeBackBtn = (ImageView) findViewById(R.id.sitting_home_back_btn);
        sittingHomeMoreBtn = (RelativeLayout) findViewById(R.id.sitting_home_more_btn);

    }

    public List<SittingDeviceModel> getDevice() {
        return entity;
    }

    public void setDevice(List<SittingDeviceModel> entity) {
        this.entity = entity;
    }

    private void initFragment(Bundle savedInstanceState) {
        Fragment mFragment;
        FragmentManager fragmentManager = getSupportFragmentManager();
        FragmentTransaction transaction = fragmentManager.beginTransaction();   // 开启一个事务
        Bundle bundle = new Bundle();
//        bundle.putString("device",device);
//        bundle.putString("relationId",relationId);
        mFragment = new SittingFragment();
        mFragment.setArguments(bundle);
        transaction.replace(R.id.sitting_home_frameLayout, mFragment);
        transaction.commit();
    }

    private void registerEvent() {
        sittingHomeBackBtn.setOnClickListener(this);
        sittingHomeMoreBtn.setOnClickListener(this);

        //实例化IntentFilter对象
        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_TIME_CHANGED);
        filter.addAction(Intent.ACTION_DATE_CHANGED);
        filter.addAction(Intent.ACTION_TIMEZONE_CHANGED);
        dynamicReceiver = new DynamicReceiver();
        //注册广播接收
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            registerReceiver(dynamicReceiver,filter, Context.RECEIVER_NOT_EXPORTED);
        }else {
            registerReceiver(dynamicReceiver,filter);
        }
//        setNetListener();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.sitting_home_back_btn:
                ActivityUtils.finishToActivity(IndexActivity.class, false);
                break;
            case R.id.sitting_home_more_btn:
                if (!NetworkUtils.isConnected()) {
                    FastBleToolUtils.onNoNetWorkButtonClick();
                    return;
                }

                ActivityUtils.startActivity(SittingMenuActivity.class);
                break;

            default:
                break;
        }
    }

    private void checkPermissions() {
        List<String> mPermissionList = new ArrayList<>();
//        String[] permissions = {Manifest.permission.ACCESS_FINE_LOCATION};

        // Android 版本大于等于 12 时，申请新的蓝牙权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            mPermissionList.add(Manifest.permission.BLUETOOTH_SCAN);
            mPermissionList.add(Manifest.permission.BLUETOOTH_ADVERTISE);
            mPermissionList.add(Manifest.permission.BLUETOOTH_CONNECT);
        } else {
            //根据实际需要申请定位权限
            mPermissionList.add(Manifest.permission.ACCESS_COARSE_LOCATION);
            mPermissionList.add(Manifest.permission.ACCESS_FINE_LOCATION);
        }

        List<String> permissionDeniedList = new ArrayList<>();
        for (String permission : mPermissionList) {
            int permissionCheck = ContextCompat.checkSelfPermission(this, permission);
            if (permissionCheck == PackageManager.PERMISSION_GRANTED) {
                onPermissionGranted(permission);
            } else {
                permissionDeniedList.add(permission);
            }
        }
        if (!permissionDeniedList.isEmpty()) {
            String[] deniedPermissions = permissionDeniedList.toArray(new String[permissionDeniedList.size()]);
            ActivityCompat.requestPermissions(this, deniedPermissions, REQUEST_CODE_PERMISSION_LOCATION);
        }
    }

    private void onPermissionGranted(String permission) {
        switch (permission) {
            case Manifest.permission.ACCESS_FINE_LOCATION:
                if (!checkGPSIsOpen()) {
                    new AlertDialog.Builder(this)
                            .setTitle(R.string.sitting_ble_notifyTitle)
                            .setMessage(R.string.sitting_ble_gpsNotifyMsg)
                            .setNegativeButton(R.string.sitting_ble_cancel,
                                    new DialogInterface.OnClickListener() {
                                        @Override
                                        public void onClick(DialogInterface dialog, int which) {
                                            finish();
                                        }
                                    })
                            .setPositiveButton(R.string.sitting_ble_setting,
                                    new DialogInterface.OnClickListener() {
                                        @Override
                                        public void onClick(DialogInterface dialog, int which) {
                                            Intent intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                                            startActivityForResult(intent, REQUEST_CODE_OPEN_GPS);
                                        }
                                    })

                            .setCancelable(false)
                            .show();
                } else {
                    setScanRule();
                }
                break;
        }
    }

    private boolean checkGPSIsOpen() {
        LocationManager locationManager = (LocationManager) this.getSystemService(Context.LOCATION_SERVICE);
        if (locationManager == null)
            return false;
        return locationManager.isProviderEnabled(android.location.LocationManager.GPS_PROVIDER);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE_OPEN_GPS) {
            if (checkGPSIsOpen()) {
                setScanRule();
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
//        showConnectedDevice();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        realm.close();
        unregisterReceiver(dynamicReceiver);
    }

    //通过继承 BroadcastReceiver建立动态广播接收器
     class DynamicReceiver extends BroadcastReceiver{
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            // android.intent.action.TIME_SET
            // android.intent.action.TIMEZONE_CHANGED
            //通过土司验证接收到广播
            // 日期，时间变化 // 时区变化
            if (action.equals("android.intent.action.TIME_SET")
                    || action.equals("android.intent.action.TIMEZONE_CHANGED")){
                EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_TIMEZONE_CHANGE,""));
            }
        }
     }

}
