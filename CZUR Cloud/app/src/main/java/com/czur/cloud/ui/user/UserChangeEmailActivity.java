package com.czur.cloud.ui.user;

import android.content.Intent;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.Editable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextWatcher;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.KeyboardUtils;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.UserInfoEvent;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.ProgressButton;
import com.czur.cloud.util.EtUtils;
import com.czur.cloud.util.validator.Validator;

import org.greenrobot.eventbus.EventBus;

import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by Yz on 2018/2/28.
 * Email：<EMAIL>
 */

public class UserChangeEmailActivity extends BaseActivity implements View.OnClickListener {


    private ImageView userBackBtn;
    private TextView userTitle;
    private TextView userChangeEmailInfoTv;
    private EditText userChangeEmailCodeEdt;
    private TextView userChangeEmailSendCodeTv;
    private UserPreferences userPreferences;
    private TimeCount timeCount;
    private HttpManager httpManager;
    private boolean codeHasContent = false;
    private ProgressButton userChangeEmailBtn;
    private long currentTime;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_user_change_email);
        initComponent();
        registerEvent();
    }

    private void initComponent() {
        userBackBtn = (ImageView) findViewById(R.id.user_back_btn);
        userTitle = (TextView) findViewById(R.id.user_title);
        userChangeEmailInfoTv = (TextView) findViewById(R.id.user_change_email_info_tv);
        userChangeEmailCodeEdt = (EditText) findViewById(R.id.user_change_email_code_edt);
        userChangeEmailSendCodeTv = (TextView) findViewById(R.id.user_change_email_send_code_tv);
        userChangeEmailBtn = (ProgressButton) findViewById(R.id.user_change_email_btn);
        userTitle.setText(R.string.user_change_email);
        httpManager = HttpManager.getInstance();
        userPreferences = UserPreferences.getInstance(this);
        formatString();
    }

    private void registerEvent() {
        userChangeEmailSendCodeTv.setOnClickListener(this);
        userChangeEmailCodeEdt.setOnClickListener(this);
        userBackBtn.setOnClickListener(this);
        userChangeEmailBtn.setOnClickListener(this);
        userChangeEmailBtn.setSelected(false);
        userChangeEmailBtn.setClickable(false);
        userChangeEmailCodeEdt.addTextChangedListener(codeTextWatcher);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.user_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.user_change_email_send_code_tv:
                getMailCode(userPreferences.getUserEmail());
                break;
            case R.id.user_change_email_btn:
                checkCodeToNextStep();
                break;
            default:
                break;
        }
    }

    /**
     * @des: 获取手机验证码
     * @params:[accountStr]
     * @return:void
     */
    private void getMailCode(String email) {
        Locale locale = getResources().getConfiguration().locale;
        String language = locale.toString();
        HttpManager.getInstance().requestPassport().mailCode(
                email,
                EtUtils.getLocale(language),
                String.class,
                new MiaoHttpManager.Callback<String>() {
            @Override
            public void onStart() {

            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                timeCountBegin();
                showMessage(R.string.toast_code_send);
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                if (entity.getCode() == MiaoHttpManager.STATUS_CODE_1_MIN) {
                    showMessage(R.string.toast_code_1_min);
                } else if (entity.getCode() == MiaoHttpManager.STATUS_5_MIN_4_TIME) {
                    showMessage(R.string.toast_5_min_4_time);
                } else if (entity.getCode() == MiaoHttpManager.STATUS_5_TIME_IN_ONE_DAY) {
                    showMessage(R.string.toast_5_time_in_one_day);
                } else {
                    showMessage(R.string.request_failed_alert);
                }

            }

            @Override
            public void onError(Exception e) {
                showMessage((R.string.request_failed_alert));
            }
        });

    }

    private void checkCodeToNextStep() {
        String code = userChangeEmailCodeEdt.getText().toString();
        if (code.length() <= 5) {
            showMessage(R.string.edit_text_code_length);
        } else {
            currentTime = System.currentTimeMillis();
            KeyboardUtils.hideSoftInput(this);
            httpManager.requestPassport().updateMailFirst(userPreferences.getIMEI(), CZURConstants.CLOUD_ANDROID,
                    userPreferences.getChannel(), userPreferences.getUserId(), userPreferences.getToken(), userPreferences.getUserId(), code, String.class,
                    new MiaoHttpManager.Callback<String>() {
                        @Override
                        public void onStart() {
                            userChangeEmailBtn.startDelayLoading(UserChangeEmailActivity.this);
                        }

                        @Override
                        public void onResponse(MiaoHttpEntity<String> entity) {
                            successDelay(entity);
                        }

                        @Override
                        public void onFailure(MiaoHttpEntity<String> entity) {
                            if (entity.getCode() == MiaoHttpManager.STATUS_CODE_EXPIRED) {
                                failedDelay(R.string.mail_code_expired);
                            } else {
                                failedDelay(R.string.mail_toast_testing_fail);
                            }
                        }

                        @Override
                        public void onError(Exception e) {
                            failedDelay(R.string.request_failed_alert);
                        }
                    });
        }
    }

    public void changeSuccess(MiaoHttpEntity<String> entity) {
        userPreferences.setUkey(entity.getBody());
        EventBus.getDefault().post(new UserInfoEvent(EventType.CHANGE_EMAIL));
        Intent intent = new Intent(UserChangeEmailActivity.this, UserBindEmailActivity.class);
        intent.putExtra("changeEmail", true);
        ActivityUtils.startActivity(intent);
    }

    private void failedDelay(final int failedText) {

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    long sleepTime;
                    if (System.currentTimeMillis() - currentTime < 1000) {
                        sleepTime = 1000 - (System.currentTimeMillis() - currentTime);
                    } else {
                        sleepTime = 1;
                    }
                    Thread.sleep(sleepTime);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            showMessage(failedText);
                            userChangeEmailBtn.stopLoading();
                        }
                    });
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }


    private void successDelay(final MiaoHttpEntity<String> entity) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    long sleepTime;
                    if (System.currentTimeMillis() - currentTime < 1000) {
                        sleepTime = 1000 - (System.currentTimeMillis() - currentTime);
                    } else {
                        sleepTime = 1;
                    }
                    Thread.sleep(sleepTime);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            userChangeEmailBtn.stopLoading();
                            changeSuccess(entity);
                        }
                    });
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }

    /**
     * @des: 修改手机信息字体部分颜色
     * @params:
     * @return:
     */

    private void formatString() {
        String emailStr = userPreferences.getUserEmail();
        String finalStr = String.format(getString(R.string.user_change_email_first_text), emailStr);
        userChangeEmailInfoTv.setText(matcherSearchText(finalStr, emailStr));
    }

    private SpannableString matcherSearchText(String text, String keyword) {
        SpannableString ss = new SpannableString(text);
        Pattern pattern = Pattern.compile(keyword);
        Matcher matcher = pattern.matcher(ss);
        while (matcher.find()) {
            int start = matcher.start();
            int end = matcher.end();
            ss.setSpan(new ForegroundColorSpan(getColor(R.color.blue_29b0d7)), start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        return ss;
    }

    private TextWatcher codeTextWatcher = new TextWatcher() {
        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            // 输入的内容变化的监听
            codeHasContent = s.length() > 0;
            checkChangePhoneButtonToClick();
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count,
                                      int after) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            // 输入后的监听
            codeHasContent = s.length() > 0;
            checkChangePhoneButtonToClick();
        }
    };


    /**
     * @des: 检查注册按钮是否可以点击
     * @params:
     * @return:
     */

    private void checkChangePhoneButtonToClick() {

        if (codeHasContent) {
            userChangeEmailBtn.setSelected(true);
            userChangeEmailBtn.setClickable(true);
        } else {
            userChangeEmailBtn.setSelected(false);
            userChangeEmailBtn.setClickable(false);
        }
    }

    /**
     * @des: 计时器开始倒计时60S
     * @params:
     * @return:void
     */
    private void timeCountBegin() {
        timeCount = new TimeCount(60000, 1000);
        timeCount.start();

    }

    /**
     * @des: 计时器
     * @params:
     * @return:
     */

    class TimeCount extends CountDownTimer {

        public TimeCount(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @Override
        public void onFinish() {
            userChangeEmailSendCodeTv.setText(R.string.resend_code);
            userChangeEmailSendCodeTv.setClickable(true);


        }

        @Override
        public void onTick(long millisUntilFinished) {
            userChangeEmailSendCodeTv.setClickable(false);
            userChangeEmailSendCodeTv.setText(millisUntilFinished / 1000 + " s");


        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (Validator.isNotEmpty(timeCount)) {
            timeCount.cancel();
        }
    }
}
