package com.czur.cloud.netty.core;

import static com.czur.czurutils.log.CZURLogUtilsKt.logD;

import android.content.Context;
import android.util.Log;

import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.Utils;
import com.czur.cloud.netty.bean.CZTcpRequestMessage;
import com.czur.cloud.netty.bean.MessageType;
import com.czur.cloud.netty.observer.NettyUtils;
import com.czur.cloud.preferences.UserPreferences;

import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.handler.timeout.IdleStateEvent;

@ChannelHandler.Sharable
public class HeartbeatHandler extends ChannelInboundHandlerAdapter {

    private static class HeartbeatHandlerInstance {
        private static final HeartbeatHandler INSTANCE = new HeartbeatHandler();
    }


    public static HeartbeatHandler getInstance() {
        return HeartbeatHandler.HeartbeatHandlerInstance.INSTANCE;
    }

    //利用写空闲发送心跳检测消息
    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        if (evt instanceof IdleStateEvent) {
            IdleStateEvent e = (IdleStateEvent) evt;
            switch (e.state()) {
                case ALL_IDLE:
                    Context context = Utils.getApp().getApplicationContext();
                    UserPreferences instance = UserPreferences.getInstance(context);
                    if (instance.isHasAuraMate() && instance.isUserLogin()) {
                        if (ctx != null && ctx.channel() != null && ctx.channel().isActive()) {
                            instance.setHeartBeatTime(System.currentTimeMillis());
                            MessageProcess.sendHeartbeat(ctx);
                        } else {
                            if (ctx != null) {
                                ctx.close();
                            }
                            CZURTcpClient.getInstance().closeChannel();
                            if (instance.isHasAuraMate() && instance.isUserLogin()) {
                                startNettyService(context);
                            }
                        }
                    }
                    break;
                default:
                    break;
            }
        } else {
            super.userEventTriggered(ctx, evt);
        }
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        final CZTcpRequestMessage messageObj = CZTcpRequestMessage.parseMessage(msg.toString());
//        Log.d("Heartbeat", "Heartbeat received msg:{" + msg.toString() + "} from {" + ctx.channel() + "}.");
        if (messageObj != null && MessageType.HEARTBEAT.getType().equals(messageObj.getType())) { // 心跳检测不做处理
            Log.e("Heartbeat", "-----Read Heartbeat------");
            return;
        }
        super.channelRead(ctx, msg);
    }

    private void startNettyService(Context context) {
        if (NetworkUtils.isConnected()) {
            logD("HeartbeatHandler.startNettyService.NettyService.class");
            NettyUtils.getInstance().startNettyService();
        }
    }

}
