package com.czur.cloud.adapter;

import android.app.Activity;
import android.net.Uri;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.R;
import com.czur.cloud.model.EtFileModel;
import com.facebook.drawee.view.SimpleDraweeView;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

public class EtFilesAdapter extends RecyclerView.Adapter<ViewHolder> {

    private Activity mActivity;

    public List<EtFileModel.FilesBean> getDatas() {
        return datas;
    }

    //当前需要显示的所有的图片数据
    private List<EtFileModel.FilesBean> datas;
    //是否进入选择
    private boolean isSelectItem;
    private LayoutInflater mInflater;

    private LinkedHashMap<String, String> isCheckedMap = new LinkedHashMap<>();

    /**
     * 构造方法
     */
    public EtFilesAdapter(Activity activity, boolean isSelectItem) {
        this.mActivity = activity;
        this.isSelectItem = isSelectItem;
        this.datas = new ArrayList<>();
        mInflater = LayoutInflater.from(activity);
    }

    public void refreshData(boolean isSelectItem, LinkedHashMap<String, String> isCheckedMap) {
        this.isSelectItem = isSelectItem;
        this.isCheckedMap = isCheckedMap;
        notifyDataSetChanged();
    }


    public void refreshData(boolean isSelectItem) {
        this.isSelectItem = isSelectItem;
        notifyDataSetChanged();
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return new EtFilesHolder(mInflater.inflate(R.layout.item_et_files, parent, false));
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        final EtFilesHolder mHolder = (EtFilesHolder) holder;
        mHolder.mItem = datas.get(position);
        mHolder.etFilesImg.setImageURI(Uri.parse(mHolder.mItem.getSmall()));
        if (isSelectItem) {
            mHolder.checkBox.setVisibility(View.VISIBLE);
            mHolder.checkBox.setTag(mHolder.mItem.getId());
        } else {
            mHolder.checkBox.setVisibility(View.GONE);
        }
        mHolder.checkBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    if (!isCheckedMap.containsKey(mHolder.checkBox.getTag())) {
                        //选中时添加
                        isCheckedMap.put(mHolder.mItem.getId(), mHolder.mItem.getFlatten());
                    }
                } else {
                    if (isCheckedMap.containsKey(mHolder.checkBox.getTag())) {
                        //没选中时移除
                        isCheckedMap.remove(mHolder.mItem.getId());
                    }
                }
                if (onItemCheckListener != null) {
                    onItemCheckListener.onItemCheck(position, mHolder.mItem, isCheckedMap, datas.size());
                }
            }
        });

        if (isCheckedMap != null) {
            mHolder.checkBox.setChecked(isCheckedMap.containsKey(mHolder.mItem.getId()));
        } else {
            mHolder.checkBox.setChecked(false);
        }

        mHolder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onEtFilesClickListener != null) {
                    onEtFilesClickListener.onEtFilesClick(mHolder.mItem, position, mHolder.checkBox);
                }
            }
        });
    }


    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return datas.size();
    }


    private static class EtFilesHolder extends ViewHolder {
        public final View mView;
        EtFileModel.FilesBean mItem;
        SimpleDraweeView etFilesImg;
        CheckBox checkBox;


        EtFilesHolder(View itemView) {
            super(itemView);
            mView = itemView;
            etFilesImg = (SimpleDraweeView) itemView.findViewById(R.id.et_files_img);
            checkBox = (CheckBox) itemView.findViewById(R.id.check);

            ViewGroup.LayoutParams layoutParams = itemView.getLayoutParams();
            layoutParams.width = (ScreenUtils.getScreenWidth() - SizeUtils.dp2px(16)) / 3;
            layoutParams.height = (int) (layoutParams.width * 1.33f);
            itemView.setLayoutParams(layoutParams);
        }
    }


    private OnEtFilesClickListener onEtFilesClickListener;

    public void setOnEtFilesClickListener(OnEtFilesClickListener onEtFilesClickListener) {
        this.onEtFilesClickListener = onEtFilesClickListener;
    }

    public interface OnEtFilesClickListener {
        void onEtFilesClick(EtFileModel.FilesBean filesBean, int position, CheckBox checkBox);
    }

    private OnEtFilesLongClickListener onEtFilesLongClickListener;

    public void setOnEtFilesLongClickListener(OnEtFilesLongClickListener onEtFilesLongClickListener) {
        this.onEtFilesLongClickListener = onEtFilesLongClickListener;
    }

    public interface OnEtFilesLongClickListener {
        void OnEtFilesLongClick(int position, EtFileModel.FilesBean filesBean, LinkedHashMap<String, String> isCheckedMap);
    }


    private OnItemCheckListener onItemCheckListener;

    public void setOnItemCheckListener(OnItemCheckListener onItemCheckListener) {
        this.onItemCheckListener = onItemCheckListener;
    }

    public interface OnItemCheckListener {
        void onItemCheck(int position, EtFileModel.FilesBean filesBean, LinkedHashMap<String, String> isCheckedMap, int totalSize);

    }

}
