package com.czur.cloud.ui.starry.api

import com.czur.cloud.model.RegisterModel
import com.czur.cloud.preferences.UserPreferences
import com.czur.cloud.ui.starry.meeting.bean.InviteBean
import com.czur.cloud.ui.starry.meeting.model.Model
import com.czur.cloud.ui.starry.meeting.network.HttpManager
import com.czur.cloud.ui.starry.meeting.network.IMeetService
import com.czur.cloud.ui.starry.model.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class StarryRepository: Model() {
    private val TAG = "StarryRepository"
    private val netService: IMeetService by lazy { HttpManager.getService() }
    private val userPreferences by lazy {
        UserPreferences.getInstance()
    }

    override fun doClear() {    }

    // 成者云账号获取用户信息
    suspend fun getStarryUserInfo(uid: String=userPreferences.userId): StarryUserInfoModel {

        val user = userPreferences.user
        val newUser = StarryUserInfoModel(
            id="0",
            name = user.name ?: "",
            mobile = user.mobile ?: "",
            accountNo = user.mobile ?: "",
            headImage = user.photo ?: ""
        )
        val userModel = withContext(Dispatchers.IO) {
            try {
                netService.getStarryUserInfo(uid).body ?: newUser
            }catch (e: Exception){
                newUser
            }
        }
        return userModel
    }

    // 根据企业id将该企业所有邀请信息设置为已读
    suspend fun readByEnterpriseId(enterpriseId: String, type: String): Boolean {
        val flag = withContext(Dispatchers.IO) {
            try {
                netService.readByEnterpriseId(enterpriseId, type.toInt()).body ?: true
            }catch (e: Exception){
                false
            }
        }
        return flag
    }

    // 是否有未读消息
    suspend fun hasUnReadNotices(accountNo: String): Boolean {
        val flag = withContext(Dispatchers.IO) {
            try {
                netService.hasUnReadNotices(accountNo).body ?: false
            }catch (e: Exception){
                false
            }
        }
        return flag
    }

    // 最近是否显示红点
    suspend fun missedCallRecords(id: String=userPreferences.userId): Boolean {
        val flag = withContext(Dispatchers.IO) {
            try{
                netService.missedCallRecords(id).body ?: false
            }catch (e: Exception){
                false
            }
        }
        return flag
    }

    // 红点已读
    suspend fun redpointRead(): Any {
        val flag = withContext(Dispatchers.IO) {
            try{
                netService.read(userPreferences.userId)
            }catch (e: Exception){
                false
            }
        }
        return flag
    }

    // 查询企业通讯录和个人联系人
    suspend fun getEnterpriseMembers(accountId: String,
                                     meetingNo: String,
                                     name: String
                                     ): StarryEnterpriseMemberModel {
        val model = withContext(Dispatchers.IO) {
            try {
                netService.getEnterpriseMembers(accountId, meetingNo, name).body ?: StarryEnterpriseMemberModel()
            }catch (e: Exception){
                StarryEnterpriseMemberModel()
            }
        }
        return model
    }

    suspend fun isInEnterprise(accountId: String, meetingNo: String, name: String): Boolean {
        val model = withContext(Dispatchers.IO) {
            netService.getEnterpriseMembers(accountId, meetingNo, name).body ?: null
        }

        val enterpriseList1 = model?.enterPriseMembers

        return (enterpriseList1?.isNotEmpty() ?: false)
    }

    suspend fun isTaInEnterprise(accountId: Long): Boolean {
        val model = withContext(Dispatchers.IO) {
            netService.getContactDetailByCzurId(accountId).body ?: null
        } ?: return false

        val commonEnterprise = model.commonEnterprise

        return commonEnterprise.isNotEmpty()
    }
    
    // deleteAddressBook
    suspend fun deleteAddressBook(id: String): String {
        val flag = withContext(Dispatchers.IO) {
            netService.deleteAddressBook(id).body ?: ""
        }
        return flag
    }

    // addAddressBook
    suspend fun addAddressBook(accountId: String, meetingNo: String, name: String): Int {
        val flag = withContext(Dispatchers.IO) {
            netService.addAddressBook(accountId, meetingNo, name).code
        }
        return flag
    }

    // contactDetail
    suspend fun getContactDetail(meetingNo: String): ContactDetail {
        val flag = withContext(Dispatchers.IO) {
            netService.getContactDetail(meetingNo).body ?: ContactDetail()
        }
        return flag
    }

    // contactDetail
    suspend fun getContactDetailV2(id: String, source: Int): ContactDetail {
        val flag = withContext(Dispatchers.IO) {
            netService.getContactDetailV2(id,source).body ?: ContactDetail()
        }
        return flag
    }


    suspend fun getContactDetailByCzurId(czurId:String) :ContactDetail{
        val id = czurId.toLong()
        val flag = withContext(Dispatchers.IO){
            netService.getContactDetailByCzurId(id).body ?:ContactDetail()
        }
        return flag
    }


    // updateAddressBook
    suspend fun updateAddressBook(id: String, remark: String): String {
        val flag = withContext(Dispatchers.IO) {
            netService.updateAddressBook(id, remark).body ?: ""
        }
        return flag
    }

    // 会议中邀请新成员-pc端
    suspend fun inviteMemberPC(inviteBean: InviteBean): Boolean {
        val flag = withContext(Dispatchers.IO) {
            netService.inviteMemberPC(inviteBean).body ?: false
        }
        return flag
    }

    // updateJoinStatus
    suspend fun updateJoinStatus(enterpriseId: String, status: Int): StarryCompanyInviterModel {
        val flag = withContext(Dispatchers.IO) {
//            netService.updateJoinStatus(enterpriseId, status.toString()).body ?: ""
            val data = netService.updateJoinStatus(enterpriseId, status.toString())
            StarryCompanyInviterModel(data.body,data.code)
        }
        return flag
    }

    fun getStarryUserAccount(uid: String=userPreferences.userId): StarryUserInfoModel {
        val newUser1 = RegisterModel()
        newUser1.id=""
        newUser1.name=""
        newUser1.email=""
        newUser1.mobile=""
        newUser1.photo=""
        newUser1.token=""
        val user = userPreferences.user ?: newUser1
        val newUser = StarryUserInfoModel(
            id="0",
            name = user.name ?: "",
            mobile = user.mobile ?: "",
            accountNo = user.mobile ?: "",
            headImage = user.photo ?: ""
        )
        val userModel =
            try {
                netService.getStarryUserInfo(uid).body ?: newUser
            }catch (e: Exception){
                newUser
            }

        return userModel
    }

    // 查询已注销的账号
    suspend fun filterDelete(czurIds: String): ContactDetail {
        val flag = withContext(Dispatchers.IO) {
            netService.filterDelete(czurIds).body ?: ContactDetail()
        }
        return flag
    }

    // 加入会议-APP、PC
//    suspend fun joinMeeting(meetingCode: String, meetingPassword: String): JoinMeetData {
//        val flag = withContext(Dispatchers.IO) {
//            netService.joinMeeting(meetingCode, meetingPassword).body ?: JoinMeetData()
//        }
//        return flag
//    }
    suspend fun joinMeeting(meetingCode: String, meetingPassword: String): Int {
        var meetCode1 = meetingCode.replace("\r","")
        meetCode1 = meetCode1.replace("\n","")
        var meetPwd1 = meetingPassword.replace("\r","")
        meetPwd1 = meetPwd1.replace("\n","")
        val flag = withContext(Dispatchers.IO) {
            try {
                JoinMeetModel.joinMeeting(meetCode1, meetPwd1)
            }catch (e: Exception){
                JoinMeetModel.ERROR_CODE_OTHER
            }
        }
        return flag
    }

    suspend fun checkPasswordNew(meetingCode: String, meetingPassword: String): Int {
        var meetCode1 = meetingCode.replace("\r","")
        meetCode1 = meetCode1.replace("\n","")
        var meetPwd1 = meetingPassword.replace("\r","")
        meetPwd1 = meetPwd1.replace("\n","")
        val flag = withContext(Dispatchers.IO) {
            try {
                JoinMeetModel.checkPassword(meetCode1, meetPwd1)
            }catch (e: Exception){
                JoinMeetModel.ERROR_CODE_OTHER
            }
        }
        return flag
    }

    // 修改会议密码-APP、PC
    suspend fun updateMeetingPassword(roomId: String, password: String): Boolean {
        val flag = withContext(Dispatchers.IO) {
            netService.updateMeetingPassword(roomId, password).body ?: false
        }
        return flag
    }

    // 分享会议
    suspend fun shareMeeting(roomName: String, meetingCode: String, meetingPassword: String, account: String): String {
        val flag = withContext(Dispatchers.IO) {
            netService.shareMeeting(roomName, meetingCode, meetingPassword, account).body ?: ""
        }
        return flag
    }

    // 查看分享的会议信息
    suspend fun shareMeetingInfo(meetingUUID: String): JoinMeetData {
        val flag = withContext(Dispatchers.IO) {
            netService.shareMeetingInfo(meetingUUID).body ?: JoinMeetData()
        }
        return flag
    }

    suspend fun checkShareMeetingInfo(meetingUUID: String): Int {
        val flag = withContext(Dispatchers.IO) {
            try {
                JoinMeetModel.checkShareMeetingInfo(meetingUUID)
            }catch (e: Exception){
                JoinMeetModel.ERROR_CODE_OTHER
            }
        }
        return flag
    }

    suspend fun checkPassword(code: String, password: String): Boolean {
        val flag = withContext(Dispatchers.IO) {
            netService.checkPassword(code, password).body ?: false
        }
        return flag
    }

    suspend fun showMeetingRemindDialog(): Boolean {
        try {
            val flag = withContext(Dispatchers.IO) {
                netService.showMeetingRemindDialog().body ?: false
            }
            return flag
        }catch(e: Exception){
            e.printStackTrace()
        }
        return false
    }

}