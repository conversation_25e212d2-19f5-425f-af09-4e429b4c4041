package com.czur.cloud.netty.messageData;

import com.czur.cloud.preferences.UserPreferences;

public class DeviceMessageData {
    /**
     * 手机端向设备端发送的命令
     */
    private String uuid;
    private String message_name;
    private String function;
    private String light_switch;
    private String light_level;
    private String light_mode;
    private String sp_reminder_sensitivity_volume;
    private String sp_reminder_sensitivity_level;
    private String sp_reminder_switch;
    private String sp_wrong_sit_switch;
    private String is_ready_for_video;
    private String system_language;
    private String smart_power_saving_switch;
    private String sedentary_reminder_switch;
    private String sedentary_reminder_duration;
    private String message_api_version;
    private String video_chat_room_no;
    private String call_id;
    // HDViewSaveV2
    private String bucketName;
    private String ossKey;

    private String isHD;

    public String getBucketName() {        return bucketName;    }

    public void setBucketName(String bucketName) {        this.bucketName = bucketName;    }

    public String getOssKey() {        return ossKey;    }

    public void setOssKey(String ossKey) {        this.ossKey = ossKey;    }

    public String getUuid() {
        return uuid;
    }


    public void setUuid(String uuid) {
        this.uuid = uuid;
    }


    public String getMessage_name() {
        return message_name;
    }

    public void setMessage_name(String message_name) {
        this.message_name = message_name;
    }


    public String getFunction() {
        return function;
    }

    public void setFunction(String function) {
        this.function = function;
    }


    public String getLight_switch() {
        return light_switch;
    }

    public void setLight_switch(String light_switch) {
        this.light_switch = light_switch;
    }


    public String getLight_Level() {
        return light_level;
    }

    public void setLight_Level(String lightLevel) {
        this.light_level = lightLevel;
    }

    public String getLight_mode() {
        return light_mode;
    }

    public void setLight_mode(String light_mode) {
        this.light_mode = light_mode;
    }


    public String getSp_reminder_sensitivity_volume() {
        return sp_reminder_sensitivity_volume;
    }

    public void setSp_reminder_sensitivity_volume(String sp_reminder_sensitivity_volume) {
        this.sp_reminder_sensitivity_volume = sp_reminder_sensitivity_volume;
    }

    public String getIs_ready_for_video() {
        return is_ready_for_video;
    }

    public void setIs_ready_for_video(String is_ready_for_video) {
        this.is_ready_for_video = is_ready_for_video;
    }

    public String getSp_reminder_sensitivity_level() {
        return sp_reminder_sensitivity_level;
    }

    public void setSp_reminder_sensitivity_level(String sp_reminder_sensitivity_level) {
        this.sp_reminder_sensitivity_level = sp_reminder_sensitivity_level;
    }

    public String getSp_reminder_switch() {
        return sp_reminder_switch;
    }

    public void setSp_reminder_switch(String sp_reminder_switch) {
        this.sp_reminder_switch = sp_reminder_switch;
    }

    public String getSp_wrong_sit_switch() {        return sp_wrong_sit_switch;    }

    public void setSp_wrong_sit_switch(String sp_wrong_sit_switch) {        this.sp_wrong_sit_switch = sp_wrong_sit_switch; }

    public String getVideo_chat_room_no() {
        return video_chat_room_no;
    }

    public void setVideo_chat_room_no(String video_chat_room_no) {
        this.video_chat_room_no = video_chat_room_no;
    }

    public String getSystem_language() {
        return system_language;
    }

    public void setSystem_language(String system_language) {
        this.system_language = system_language;
    }

    public String getLight_level() {
        return light_level;
    }

    public void setLight_level(String light_level) {
        this.light_level = light_level;
    }

    public String getSmart_power_saving_switch() {
        return smart_power_saving_switch;
    }

    public void setSmart_power_saving_switch(String smart_power_saving_switch) {
        this.smart_power_saving_switch = smart_power_saving_switch;
    }

    public String getSedentary_reminder_switch() {
        return sedentary_reminder_switch;
    }

    public void setSedentary_reminder_switch(String sedentary_reminder_switch) {
        this.sedentary_reminder_switch = sedentary_reminder_switch;
    }

    public String getSedentary_reminder_duration() {
        return sedentary_reminder_duration;
    }

    public void setSedentary_reminder_duration(String sedentary_reminder_duration) {
        this.sedentary_reminder_duration = sedentary_reminder_duration;
    }

    public String getMessage_api_version() {
        return message_api_version;
    }

    public void setMessage_api_version(String message_api_version) {
        this.message_api_version = message_api_version;
    }

    public String getCall_id() {
        return call_id;
    }

    public void setCall_id(String call_id) {
        this.call_id = call_id;
    }

    public String getIsHD() {
        return isHD;
    }

    public void setIsHD(String isHD) {
        this.isHD = isHD;
    }

    @Override
    public String toString() {
        return "DeviceMessageData{" +
                "uuid='" + uuid + '\'' +
                ", message_name='" + message_name + '\'' +
                ", function='" + function + '\'' +
                ", light_switch='" + light_switch + '\'' +
                ", light_level='" + light_level + '\'' +
                ", light_mode='" + light_mode + '\'' +
                ", sp_reminder_sensitivity_volume='" + sp_reminder_sensitivity_volume + '\'' +
                ", sp_reminder_sensitivity_level='" + sp_reminder_sensitivity_level + '\'' +
                ", sp_reminder_switch='" + sp_reminder_switch + '\'' +
                ", is_ready_for_video='" + is_ready_for_video + '\'' +
                ", system_language='" + system_language + '\'' +
                ", smart_power_saving_switch='" + smart_power_saving_switch + '\'' +
                ", sedentary_reminder_switch='" + sedentary_reminder_switch + '\'' +
                ", sedentary_reminder_duration='" + sedentary_reminder_duration + '\'' +
                ", message_api_version='" + message_api_version + '\'' +
                ", video_chat_room_no='" + video_chat_room_no + '\'' +
                ", call_id='" + call_id + '\'' +
                ", isHD='" + isHD + '\'' +
                '}';
    }
}
