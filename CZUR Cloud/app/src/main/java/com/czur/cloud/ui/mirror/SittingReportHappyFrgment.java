package com.czur.cloud.ui.mirror;

import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.annotation.SuppressLint;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.SittingCommonEvent;
import com.czur.cloud.event.SittingModelChangeEvent;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.auramate.reportfragment.CustomXAxisRenderer;
import com.czur.cloud.ui.auramate.reportfragment.ReportUtil;
import com.czur.cloud.ui.base.BaseFragment;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.mirror.comm.FastBleConstants;
import com.czur.cloud.ui.mirror.comm.FastBleOperationUtils;
import com.czur.cloud.ui.mirror.comm.SittingReportUtil;
import com.czur.cloud.ui.mirror.happytime.SittingHappyTimeAdapter;
import com.czur.cloud.ui.mirror.happytime.SittingSitPreviewActivity;
import com.czur.cloud.ui.mirror.model.SittingDeviceModel;
import com.czur.cloud.ui.mirror.model.SittingHappyTimePictureModel;
import com.czur.cloud.ui.mirror.model.SittingReportModelSub;
import com.czur.cloud.util.EtUtils;
import com.czur.cloud.util.validator.Validator;
import com.github.iielse.switchbutton.SwitchView;
import com.github.mikephil.charting.charts.BarChart;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.formatter.ValueFormatter;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet;
import com.github.mikephil.charting.listener.ChartTouchListener;
import com.github.mikephil.charting.listener.OnChartGestureListener;
import com.github.mikephil.charting.listener.OnChartValueSelectedListener;
import com.google.gson.reflect.TypeToken;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnLoadMoreListener;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import org.greenrobot.eventbus.EventBus;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Jason on 2020/12/29.
 */
public class SittingReportHappyFrgment extends BaseFragment implements View.OnClickListener{
    private final int BAR_MAX_COUNT = 7;
    private final float BAR_WIDTH = 0.4f;

    private int currentIndex = 0;   //当前选中的X轴数据序号；
    private String typePageSize = "7";   //当前类型下的获取记录总数量；日：365，周：52，月：12
    private String equipmentId, titleName, type, reportId;
    List<String> barChartDataListX = new ArrayList<>();
    List<Integer> barChartDataListY = new ArrayList<>();
    private boolean isReflashFlag = false;  //判断是否需要加载更多后，刷新界面；没有数据，无需刷新；
    private boolean isLoadMore;//是否加载更多
    private boolean canLoadLeft;//向左是否可以加载更多
    private boolean canLoadRight;//向左是否可以加载更多
    private String oldReportID = "";  // 防止重复加载更多

    private List<SittingReportModelSub> reportListDatas = new ArrayList<>();//接口返回的数据
    private List<SittingReportModelSub> reportAllListDatas = new ArrayList<>();//插入空值的数据；用于显示，查找
    //注意，获取到的网络数据是倒排序，reportListDatas也是倒排序；新的日期在前面，早的日期在后面
    //barchart显示的需要时正排序，即早的日期在前面，新的日期在最后

    private RelativeLayout emptyRl, dataRl;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private BarChart chart;
    private int curSelectValueX = 0;    //当前选中的bar，点击空白处，仍然选择当前的。
    private int oldListDatasCount = 0;  //加载数据前的数据总数
    private RecyclerView recyclerView;
    private List<SittingHappyTimePictureModel> filesBeans;
    private List<SittingHappyTimePictureModel> addFilesBeans;
    private SittingHappyTimeAdapter auraFilesAdapter;
    private String typeClass = "2";     // 请求类型1为错误瞬间 2为愉悦瞬间
    private SimpleDateFormat formatter;
    private String showErrorPictureDate="";     //获取该日的错误坐姿图片
    private String showErrorPictureRelationId="";     //relationId
    private List<SittingHappyTimePictureModel> refreshBeans;
    private LinearLayout emptyLayout;
    private SmartRefreshLayout refreshLayout;
    private String seqNum;

    private RelativeLayout sitting_happy_select_bar_rl;
    private SittingDeviceModel deviceModelHappy;
    private SwitchView sittingHappyTimeSwitch;
    private TextView noteTitle, alertTitle; //开启显示的提示信息，未开启的提示信息
    private TextView etFilesSelectAllBtn;
    private TextView etFilesNoTitleTv;
    private TextView etFilesTitleTv;
    private TextView etFilesCancelBtn;
    private RelativeLayout etFilesUnselectedTopBarRl;
    private RelativeLayout etFilesMultiSelectBtn;
    private boolean isMultiSelect = false;
    private boolean isSelectAll = false;
    private LinkedHashMap<String, String> isCheckedMap;
    private List<String> fileIds;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_sitting_happy_report, container, false);
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        reportId="";
        Bundle bundle = getArguments();
        if (bundle != null) {
            equipmentId = bundle.getString("equipmentId");
            titleName = bundle.getString("titleName");
            type = bundle.getString("type");
        }

//        LinearLayout happy_time_picture_list = (LinearLayout)Objects.requireNonNull(getActivity()).findViewById(R.id.happy_time_picture_list);
//        if(type.equals(CZURConstants.SITTING_REPORT_STR_DAY)){
//            typePageSize="15";
//            happy_time_picture_list.setVisibility(View.VISIBLE);
//        }
//        if(type.equals(CZURConstants.SITTING_REPORT_STR_WEEK)){
//            typePageSize="15";
//            happy_time_picture_list.setVisibility(View.GONE);
//        }
//        if(type.equals(CZURConstants.SITTING_REPORT_STR_MONTH)){
//            typePageSize="12";
//            happy_time_picture_list.setVisibility(View.GONE);
//        }

        initComponent();
    }

    private void initComponent(){
        userPreferences = UserPreferences.getInstance();
        httpManager = HttpManager.getInstance();
        emptyRl = requireActivity().findViewById(R.id.empty_rl);
        dataRl = requireActivity().findViewById(R.id.data_rl);

        emptyRl.setVisibility(View.GONE);
        dataRl.setVisibility(View.GONE);

        deviceModelHappy = userPreferences.getSittingDeviceModel();

        showErrorPictureRelationId = "";
        showErrorPictureDate = "";
        if (Validator.isEmpty(showErrorPictureDate) || showErrorPictureDate.equals("")){
            showErrorPictureDate = ReportUtil.getNowDay("yyyy-MM-dd");
        }

        // init pictures
        formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        initEtFilesRecyclerView();

        refreshLayout = requireActivity().findViewById(R.id.refresh_layout);
        refreshLayout.setEnableOverScrollDrag(false);
        refreshLayout.setEnableOverScrollBounce(false);
        refreshLayout.setEnableAutoLoadMore(true);
        refreshLayout.setEnableRefresh(true);
        refreshLayout.setEnableNestedScroll(false);
        refreshLayout.setEnableFooterFollowWhenNoMoreData(true);
        refreshLayout.setEnableLoadMoreWhenContentNotFull(false);
        refreshLayout.setEnableLoadMore(true);
        refreshLayout.setOnLoadMoreListener(new OnLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                if (type.equals(CZURConstants.SITTING_REPORT_STR_DAY)) {
                    recyclerView.stopScroll();
                    loadMore();
                }
            }
        });
        refreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                getReportListNew(equipmentId);

                if (type.equals(CZURConstants.SITTING_REPORT_STR_DAY)) {
                    getRefreshListForPicture();
                }
            }
        });

        isLoadMore = true;
        canLoadLeft = true;
        canLoadRight = true;

        registerEvent();

        getReportListNew(equipmentId);

//        if (type.equals(CZURConstants.SITTING_REPORT_STR_DAY)) {
//            getRefreshList();
//        }

        // 正确率趋势
        chart = requireActivity().findViewById(R.id.chart1);

        chart.setOnChartValueSelectedListener(new OnChartValueSelectedListener() {
            @Override
            public void onValueSelected(Entry e, Highlight h) {
                int curX = (int) e.getX();

                if ((curX > -1) && (curX < barChartDataListX.size())){
                    String selectXName = barChartDataListX.get(curX);
                    int right = barChartDataListY.get(curX);

                    //当日有学习数据，正确率及错误率为0时，条形图点击切换。
                    //当日未学习，不显示数据，条形图不支持点击切换。还是上一次点击的条图形
                    if (right > 0){
                        curSelectValueX = curX;
                    }else{
                        Highlight high = new Highlight(curSelectValueX, 0, 0);
                        high.setDataIndex(curSelectValueX);
                        chart.highlightValue(high, false);
                        return;
                    }

                    HighlightBarChartCurSelected(curSelectValueX);

                    for (SittingReportModelSub entry:reportAllListDatas){
                        String fromEnd = entry.getFromEnd();
                        if (!Validator.isEmpty(fromEnd)){
                            fromEnd = ReportUtil.foramtDateTime(fromEnd, Integer.parseInt(type));
                        }else{
                            fromEnd = "";
                        }

                        if (fromEnd.equals(selectXName)){
                            //使用Activity的runOnUiThread()方法更新UI
                            requireActivity().runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    initReportHorizontalView(entry);

                                    if (type.equals(CZURConstants.SITTING_REPORT_STR_DAY)) {
                                        refreshBeans = null;
                                        filesBeans = null;
                                        refreshBeans = new ArrayList<>();
                                        filesBeans = new ArrayList<>();
//                                        isShowEmptyPrompt();
                                        showErrorPictureDate = entry.getFromEndDate();

                                        // 先取消复选工具条
                                        cancelEvent();
                                        // 再刷新
                                        getRefreshListForPicture();
                                        FastBleOperationUtils.threadSleep();
                                    }
                                }
                            });

                            break;
                        }
                    }
                }
            }

            @Override
            public void onNothingSelected() {
                HighlightBarChartCurSelected(curSelectValueX);
            }
        });

        chart.setOnChartGestureListener(new OnChartGestureListener() {

            @Override
            public void onChartLongPressed(MotionEvent me) {            }

            @Override
            public void onChartDoubleTapped(MotionEvent me) {            }

            @Override
            public void onChartSingleTapped(MotionEvent me) {            }

            @Override
            public void onChartFling(MotionEvent me1, MotionEvent me2, float velocityX, float velocityY) {            }

            @Override
            public void onChartScale(MotionEvent me, float scaleX, float scaleY) {            }

            @Override
            public void onChartGestureStart(MotionEvent me, ChartTouchListener.ChartGesture lastPerformedGesture) {
                isLoadMore = false;
            }

            @Override
            public void onChartGestureEnd(MotionEvent me, ChartTouchListener.ChartGesture lastPerformedGesture) {
                int leftX = (int) chart.getLowestVisibleX();//获取可视区域中，显示在x轴最左边的index，最小值
                int rightX = (int) chart.getHighestVisibleX();//最大值，最右边的
                int dataCount = reportAllListDatas.size();
                oldListDatasCount = dataCount;

                if(lastPerformedGesture == ChartTouchListener.ChartGesture.DRAG){
                    isLoadMore = true;
                    if((leftX == 0 || leftX == 1) && (rightX == BAR_MAX_COUNT || rightX == BAR_MAX_COUNT-1 )){
                        isLoadMore = false;
                        if (!canLoadLeft)
                            return;

                        //<==加载更多数据的操作
                        String report_id = String.valueOf(reportAllListDatas.get(0).getId());
                        if (!oldReportID.equals(report_id)) {
                            oldReportID = report_id;
                            getReportListBeforeMore(equipmentId, report_id);
                        }
                    }

//                    if((rightX == dataCount-1 || rightX == dataCount) && (leftX == dataCount-BAR_MAX_COUNT || leftX == dataCount-BAR_MAX_COUNT-1 )){
//                        isLoadMore = false;
//                        if (!canLoadRight)
//                            return;
//
//                        //==>加载更多数据的操作
//                        String report_id = String.valueOf(reportAllListDatas.get(dataCount-1).getId());
//                        getReportListAfterMore(equipmentId, report_id);
//                    }
                }
            }

            @Override
            public void onChartTranslate(MotionEvent me, float dX, float dY) {
                if(isLoadMore){
                    int leftX = (int) chart.getLowestVisibleX();//获取可视区域中，显示在x轴最左边的index，最小值
                    int rightX = (int) chart.getHighestVisibleX();//最大值，最右边的
                    int dataCount = reportAllListDatas.size();
                    oldListDatasCount = dataCount;

                    if((leftX == 0 || leftX == 1) && (rightX == BAR_MAX_COUNT || rightX == BAR_MAX_COUNT-1 )){
                        isLoadMore = false;
                        if (!canLoadLeft)
                            return;

                        //<==加载更多数据的操作
                        String report_id = String.valueOf(reportAllListDatas.get(0).getId());
                        if (!oldReportID.equals(report_id)) {
                            oldReportID = report_id;
                            getReportListBeforeMore(equipmentId, report_id);
                        }
                    }

//                    if((rightX == dataCount-1 || rightX == dataCount) && (leftX == dataCount-BAR_MAX_COUNT || leftX == dataCount-BAR_MAX_COUNT-1 )){
//                        isLoadMore = false;
//                        if (!canLoadRight)
//                            return;
//
//                        //==>加载更多数据的操作
//                        String report_id = String.valueOf(reportAllListDatas.get(dataCount-1).getId());
//                        getReportListAfterMore(equipmentId, report_id);
//                    }
                }
            }
        });

    }

    /**
     * @des: 初始化列表
     * @params:
     * @return:
     */
    private void initEtFilesRecyclerView() {
        fileIds = new ArrayList<>();
        filesBeans = new ArrayList<>();
        addFilesBeans = new ArrayList<>();
        isCheckedMap = new LinkedHashMap<>();

        sitting_happy_select_bar_rl = requireActivity().findViewById(R.id.sitting_happy_select_bar_rl);
        sitting_happy_select_bar_rl.setVisibility(View.GONE);

        recyclerView = (RecyclerView) requireActivity().findViewById(R.id.recycler_view);
        auraFilesAdapter = new SittingHappyTimeAdapter(getActivity(), filesBeans, false);
        auraFilesAdapter.setOnItemCheckListener(onItemCheckListener);
        auraFilesAdapter.setOnEtFilesClickListener(onItemClickListener);
        recyclerView.setHasFixedSize(true);
        recyclerView.setLayoutManager(new GridLayoutManager(getActivity(), 3));
        recyclerView.setAdapter(auraFilesAdapter);
        emptyLayout = requireActivity().findViewById(R.id.ll_empty);

        etFilesSelectAllBtn = (TextView) requireActivity().findViewById(R.id.et_files_select_all_btn);
        etFilesNoTitleTv = (TextView) requireActivity().findViewById(R.id.et_files_no_title_tv);
        etFilesTitleTv = (TextView) requireActivity().findViewById(R.id.et_files_title_tv);
        etFilesCancelBtn = (TextView) requireActivity().findViewById(R.id.et_files_cancel_btn);
        etFilesUnselectedTopBarRl = (RelativeLayout) requireActivity().findViewById(R.id.et_files_unselected_top_bar_rl);
        etFilesMultiSelectBtn = (RelativeLayout) requireActivity().findViewById(R.id.et_files_multi_select_btn);
        noteTitle = (TextView)requireActivity().findViewById(R.id.et_note_title_tv);
        alertTitle = (TextView)requireActivity().findViewById(R.id.et_alert_title_tv);
        alertTitle.setVisibility(View.GONE);
        sittingHappyTimeSwitch = (SwitchView)requireActivity().findViewById(R.id.sitting_happy_time_switch);
        sittingHappyTimeSwitch.setOnStateChangedListener(new SwitchView.OnStateChangedListener() {
            @Override
            public void toggleToOn(SwitchView switchView) {
                switchView.toggleSwitch(true);
                checkHappyTimeSwitch(true);
            }

            @Override
            public void toggleToOff(SwitchView switchView) {
                switchView.toggleSwitch(false);
                checkHappyTimeSwitch(false);
            }
        });
        boolean flag = deviceModelHappy.isHappyTimeOpen();
        setMessageShowOrHide(flag);
        sittingHappyTimeSwitch.setOpened(flag);
        etFilesNoTitleTv.setVisibility(View.VISIBLE);

    }

    private void registerEvent() {
        etFilesSelectAllBtn.setOnClickListener(this);
        etFilesCancelBtn.setOnClickListener(this);
        etFilesMultiSelectBtn.setOnClickListener(this);
    }

    //高亮显示当前选中的条目
    private void HighlightBarChartCurSelected(int curx){
        Highlight high = new Highlight(curx, 0, 0);
        high.setDataIndex(curx);
        chart.highlightValue(high, false);
    }

    /**
     * @des: 刷新接口
     */
    private OnRefreshListener onRefreshListener = refreshLayout -> getReportListNew(equipmentId);

    private List<SittingReportModelSub> getBeforeReportList(String equipmentId, String reportId) {
        logI("getBeforeReportList");
        try {
            final MiaoHttpEntity<SittingReportModelSub> reportEntity = httpManager.request().
                    getBeforeSittingReports(
                            userPreferences.getUserId(),
                            equipmentId,
                            type,
                            reportId,
                            typePageSize,
                            new TypeToken<List<SittingReportModelSub>>() {
                            }.getType());

            if (reportEntity == null) {
                return null;
            }
            if (reportEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return reportEntity.getBodyList();
            } else {
                return null;
            }

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private List<SittingReportModelSub> getAfterReportList(String equipmentId, String reportId) {
        logI("getAfterReportList");
        try {
            final MiaoHttpEntity<SittingReportModelSub> reportEntity = httpManager.request().
                    getAfterSittingReports(
                            userPreferences.getUserId(),
                            equipmentId,
                            type,
                            reportId,
                            typePageSize,
                            new TypeToken<List<SittingReportModelSub>>() {
                            }.getType());

            if (reportEntity == null) {
                return null;
            }
            if (reportEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return reportEntity.getBodyList();
            } else {
                return null;
            }

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * @des: 刷新列表
     */
    public void getReportListNew(String equipment_id) {
        if (!NetworkUtils.isConnected()) {
            showMessage(R.string.toast_no_connection_network);
            showEmpty();
            hideProgressDialog();
            return;
        }

        showProgressDialog();

        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                reportListDatas = getBeforeReportList(equipment_id, "");
                // 是否为当天数据？仅限日报
                if (type.equals(CZURConstants.SITTING_REPORT_STR_DAY)) {
                    if (Validator.isNotEmpty(reportListDatas)) {
                        reportListDatas = SittingReportUtil.addTodayZeroData(reportListDatas);
                    }
                }
                reportListDatas = SittingReportUtil.reportListDatasReversal(reportListDatas, Integer.parseInt(type));

                logI("=====SittingReportHappyFrgment.getReportListNew.reportListDatas===");
                ReportUtil.printSittingDatasLog(reportListDatas);

                if (Validator.isNotEmpty(reportListDatas)) {
                    if (reportListDatas.size() < BAR_MAX_COUNT) {
                        canLoadLeft = false;
                        canLoadRight = false;
                    }

                    //插值，补到reportAllListDatas里面
//                    reportAllListDatas = reportListDatasAddSpace(reportListDatas);
                    reportAllListDatas = SittingReportUtil.reportListDatasAddSpace(reportListDatas, Integer.parseInt(type));
//                    ReportUtil.printSittingDatasLog(reportAllListDatas);

                }else{
                    canLoadRight = false;
                    canLoadLeft = false;
                }
                return null;
            }

            @Override
            public void onSuccess(Void result) {
                refreshLayout.finishRefresh(true);

                refreshFiles();
                showEmpty();
                hideProgressDialog();
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                refreshLayout.finishRefresh(false);

                showEmpty();
                hideProgressDialog();
            }
        });
    }

    public void getReportListBeforeMore(String equipment_id, String report_id) {
        logI("getReportListBeforeMore.equipment_id.report_id=="+equipment_id +";"+ report_id);
        if (!NetworkUtils.isConnected()) {
            showMessage(R.string.toast_no_connection_network);
            showEmpty();
            hideProgressDialog();
            return;
        }

        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                isReflashFlag = false;
                reportListDatas = getBeforeReportList(equipment_id, report_id);
                if (Validator.isNotEmpty(reportListDatas)) {
                    if (reportListDatas.size() < BAR_MAX_COUNT)
                        canLoadLeft = false;

                    isReflashFlag = true;
                    //插值，补到reportAllListDatas里面
                    reportListDatas = SittingReportUtil.reportListDatasReversal(reportListDatas, Integer.parseInt(type));

                    logI("=====getReportListBeforeMore.reportListDatas===");
                    ReportUtil.printSittingDatasLog(reportListDatas);

                    ///////
                    reportAllListDatas.addAll(0,reportListDatas);
//                    reportAllListDatas = reportListDatasAddSpace(reportAllListDatas);
                    reportAllListDatas = SittingReportUtil.reportListDatasAddSpace(reportAllListDatas, Integer.parseInt(type));
                    currentIndex = reportAllListDatas.size()-oldListDatasCount-1;

                    logI("=====getReportListBeforeMore.reportAllListDatas===");
                    ReportUtil.printSittingDatasLog(reportAllListDatas);
                }else{
                    canLoadLeft = false;
                    isReflashFlag = false;
                }

                return null;
            }

            @Override
            public void onSuccess(Void result) {
                if (isReflashFlag){
                    refreshBarChart();
                    showEmpty();
                }
                hideProgressDialog();
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                hideProgressDialog();
            }
        });
    }

    public void getReportListAfterMore(String equipment_id, String report_id) {
        if (!NetworkUtils.isConnected()) {
            showMessage(R.string.toast_no_connection_network);
            showEmpty();
            hideProgressDialog();
            return;
        }

        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                isReflashFlag = false;
                reportListDatas = getAfterReportList(equipment_id, report_id);
                if (Validator.isNotEmpty(reportListDatas)) {
                    if (reportListDatas.size() < BAR_MAX_COUNT)
                        canLoadRight = false;

                    isReflashFlag = true;
                    //插值，补到reportAllListDatas里面
//                    reportListDatas = reportListDatasReversal(reportListDatas);
                    reportListDatas = SittingReportUtil.reportListDatasReversal(reportListDatas, Integer.parseInt(type));
                    logI("=====getReportListAfterMore.reportListDatas===");
                    ReportUtil.printSittingDatasLog(reportListDatas);

                    ///////
                    reportAllListDatas.addAll(reportListDatas);
//                    reportAllListDatas = reportListDatasAddSpace(reportAllListDatas);
                    reportAllListDatas = SittingReportUtil.reportListDatasAddSpace(reportAllListDatas, Integer.parseInt(type));
                    logI("=====getReportListAfterMore.reportAllListDatas===");
                    ReportUtil.printSittingDatasLog(reportAllListDatas);
                }else{
                    canLoadRight = false;
                    isReflashFlag = false;
                }
                return null;
            }

            @Override
            public void onSuccess(Void result) {
                if (isReflashFlag){
                    refreshBarChart();
                    showEmpty();
                }
                hideProgressDialog();
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                hideProgressDialog();
            }
        });
    }

    private void refreshBarChart() {

        //对barChartDataListX等数据初始化
        initBarChartDataListFromServer();

        //刷新柱状图
        reflashChart(1, chart);

        //将当前视口的左侧（边）到指定的 x 值。
        chart.moveViewToX(currentIndex);

    }

    //对要在图表展示的数据进行初始化赋值操作
    private void initBarChartDataListFromServer(){
        barChartDataListX.clear();
        barChartDataListY.clear();

        //获取barChart的数据
        for (SittingReportModelSub entry: reportAllListDatas){
            String reportType = entry.getType()+"";

            if (!reportType.equals(type))
                continue;

            int right = entry.getHappy();
            if (Validator.isEmpty(right))
                right = 0;
            int rightProportion = right;
            String fromEnd = entry.getFromEnd();
            barChartDataListX.add(fromEnd);
            barChartDataListY.add(rightProportion);
        }

        //对于少于7天的数据，补齐缺少的，让柱状图靠右对齐
        if (barChartDataListX.size()<BAR_MAX_COUNT){
            int x = BAR_MAX_COUNT - barChartDataListX.size();
            for (int i=0; i<x; i++){
                barChartDataListX.add(0, "");
                barChartDataListY.add(0,0);
            }
        }

    }

    private void refreshFiles() {

        //初始化绘制统计数据
        if (Validator.isEmpty(reportAllListDatas))
            return;

        initBarChartDataListFromServer();

        curSelectValueX = barChartDataListX.size()-1;
        SittingReportModelSub armSub = reportAllListDatas.get(reportAllListDatas.size()-1);

        initReportHorizontalView(armSub);

        int allCount= reportAllListDatas.size();
        if (allCount<BAR_MAX_COUNT){
            int x = BAR_MAX_COUNT - allCount;
            curSelectValueX = curSelectValueX +x;
        }

        initChart(chart);

    }

    private void initReportHorizontalView(SittingReportModelSub reportData) {
        TextView tv_report_right_value, tv_report_alert_per;
        tv_report_right_value = requireActivity().findViewById(R.id.tv_report_right_value);
        int right = reportData.getHappy();
        tv_report_right_value.setText(right+"");

        tv_report_alert_per = requireActivity().findViewById(R.id.tv_report_alert_per);
        tv_report_alert_per.setVisibility(View.INVISIBLE);

        if (type.equals(CZURConstants.SITTING_REPORT_STR_WEEK) || type.equals(CZURConstants.SITTING_REPORT_STR_MONTH)){
            tv_report_alert_per.setVisibility(View.VISIBLE);

//            if (BuildConfig.IS_OVERSEAS) {
//                float size = 12;
//                ((TextView)getActivity().findViewById(R.id.tv_report_alert_han)).setTextSize(size);
//                tv_report_alert_per.setTextSize(size);
//            }
        }

        if (type.equals(CZURConstants.SITTING_REPORT_STR_DAY)) {
//            String sDateTime = reportData.getTitle();
//            int l = sDateTime.length();
//            if (l > 10){
//                l=10;
//            }
//            String reportDate = sDateTime.substring(0, l);
            String reportDate = reportData.getFromEndDate();
            showErrorPictureDate = reportDate;
        }

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    private void showEmpty() {
        if (Validator.isEmpty(reportAllListDatas)){
            emptyRl.setVisibility(View.VISIBLE);
            dataRl.setVisibility(View.GONE);
            return;
        }
        if (reportAllListDatas.size() > 0) {
            emptyRl.setVisibility(View.GONE);
            dataRl.setVisibility(View.VISIBLE);

        } else {
            emptyRl.setVisibility(View.VISIBLE);
            dataRl.setVisibility(View.GONE);
        }
    }

    private void reflashChart(int typeA, BarChart chart){

        XAxis xAxis = chart.getXAxis();

        xAxis.setValueFormatter(new ValueFormatter() {
            @Override
            public String getFormattedValue(float value) {
                if ((int)value > barChartDataListX.size()-1)
                    return "";
//                return barChartDataListX.get((int)value);
                String ret1 = barChartDataListX.get((int)value);
                String ret = ret1;
                //仅仅针对周的情况，需要旋转显示
                if (type.equals(CZURConstants.SITTING_REPORT_STR_WEEK)) {
                    String[] strArray = ret1.split("-");
                    if (strArray.length > 1) {
                        String strFrom = strArray[0];
                        String strEnd = strArray[1];
                        ret =  strFrom + "-\n" + strEnd;
                    }
                }
                return ret;
            }
        });

        int lenSize = barChartDataListY.size();

        // setting data
        ArrayList<BarEntry> values = new ArrayList<>();
        for (int i = 0; i < lenSize; i++) {
            values.add(new BarEntry(i, barChartDataListY.get(i)));
        }

        BarDataSet set1;
        set1 = new BarDataSet(values, "Data Set");
        set1.setDrawValues(false);
        set1.setColor(Color.argb(255,24,143,142));  //柱子的颜色 8a e5 b1 ///188f8e==24,143,142
        if (typeA == 2){
            set1.setColor(Color.argb(255,240,117,117));  //柱子的颜色 f07575==240,117,117
        }
        set1.setHighLightAlpha(45);

        ArrayList<IBarDataSet> dataSets = new ArrayList<>();
        dataSets.add(set1);

        BarData data = new BarData(dataSets);
        data.setBarWidth(BAR_WIDTH);
        chart.setData(data);
        chart.setFitBars(true);

        //setVisibleXRange是设置可见的x范围，我设置成(0, 5)之后，
        // 就只会显示序号0到5的内容(6项)。往左滑才会显示第7,8,9…最后一项。
        chart.setVisibleXRange(0,BAR_MAX_COUNT);

        chart.notifyDataSetChanged();
        chart.invalidate();
    }

    public void initChart(BarChart chart){

        chart.getDescription().setEnabled(false);

        // if more than 60 entries are displayed in the chart, no values will be drawn
        chart.setMaxVisibleValueCount(60);

        //是否能够缩放
        chart.setPinchZoom(false);
        //允许X轴缩放
        chart.setScaleXEnabled(false);
        //允许Y轴缩放
        chart.setScaleYEnabled(false);

        chart.setDrawBarShadow(false);
        chart.setDrawGridBackground(false);

        XAxis xAxis = chart.getXAxis();
        YAxis leftAxis = chart.getAxisLeft();
        YAxis rightAxis = chart.getAxisRight();

        xAxis.setPosition(XAxis.XAxisPosition.BOTTOM);
        xAxis.setGranularity(1f);
        xAxis.setLabelCount(BAR_MAX_COUNT, false);

        // add a nice and smooth animation
        chart.animateY(1000);

        xAxis.setValueFormatter(new ValueFormatter() {
            @Override
            public String getFormattedValue(float value) {
                if ((int)value > barChartDataListX.size()-1)
                    return "";
//                return barChartDataListX.get((int)value);
                String ret1 = barChartDataListX.get((int)value);
                String ret = ret1;
                //仅仅针对周的情况，需要旋转显示
                if (type.equals(CZURConstants.SITTING_REPORT_STR_WEEK)) {
                    String[] strArray = ret1.split("-");
                    if (strArray.length > 1) {
                        String strFrom = strArray[0];
                        String strEnd = strArray[1];
                        ret =  strFrom + "-\n" + strEnd;
                    }
                }
                return ret;
            }
        });

        leftAxis.setValueFormatter(new ValueFormatter() {
            @Override
            public String getFormattedValue(float value) {
                if (value>0)
                    return (int) value + "%";
                else{
                    return "";
                }
            }
        });
        leftAxis.setLabelCount(5, false);
        leftAxis.setTextColor(Color.rgb(128,128,128));
        xAxis.setTextColor(Color.rgb(128,128,128));

        chart.getAxisLeft().setDrawAxisLine(false);
        chart.getXAxis().setDrawAxisLine(false);

        chart.getAxisRight().setEnabled(false);
        chart.getLegend().setEnabled(false);

        chart.setDrawBorders(false); ////是否在折线图上添加边框
        // 如果没有数据的时候，会显示这个，类似ListView的EmptyView
        chart.setDrawGridBackground(true); // 是否显示表格颜色
        chart.setGridBackgroundColor(Color.argb(255, 240,240,240)); // 表格的的颜色，在这里是是给颜色设置一个透明度
        //是否展示网格线
//        chart.setDrawGridBackground(false);
        xAxis.setDrawGridLines(false);
        rightAxis.setDrawGridLines(false);
        leftAxis.setDrawGridLines(false);

        chart.setTouchEnabled(true);//设置是否可以触摸，如为false，则不能拖动，缩放等
        chart.setDragEnabled(true); //设置是否可以拖拽，缩放

        int lenSize = barChartDataListY.size();

        leftAxis.setYOffset(10f);//这样会向下偏移 50%。我也不知道为啥，自己试出来的
        float xSize = 0.95f;

        // setting data
        ArrayList<BarEntry> values = new ArrayList<>();

        for (int i = 0; i < lenSize; i++) {
            values.add(new BarEntry(i, barChartDataListY.get(i)*xSize));
        }

        xAxis.setAxisMinimum(-0.5f);    //X轴数据向右偏移一些
//        xAxis.setAxisMinimum(100f);     //
        leftAxis.setAxisMinimum(0f);    //最小刻度值
        leftAxis.setAxisMaximum(100f);  // the axis maximum is 100

        chart.setExtraBottomOffset(2.2f * 10f);
        xAxis.setTextSize(10f);
        chart.setXAxisRenderer(new CustomXAxisRenderer(chart.getViewPortHandler(), chart.getXAxis(), chart.getTransformer(YAxis.AxisDependency.LEFT)));

        BarDataSet set1;
        set1 = new BarDataSet(values, "Data Set");
        set1.setDrawValues(false);

//        set1.setColor(Color.argb(255,138, 229, 177));  //柱子的颜色 8a e5 b1
        set1.setColor(Color.argb(255,24,143,142));  //柱子的颜色 8a e5 b1 ///188f8e==24,143,142

        set1.setHighLightAlpha(45);

        ArrayList<IBarDataSet> dataSets = new ArrayList<>();
        dataSets.add(set1);

        BarData data = new BarData(dataSets);
        data.setBarWidth(BAR_WIDTH);
        chart.setData(data);
        chart.setFitBars(true);

        //setVisibleXRange是设置可见的x范围，我设置成(0, 5)之后，
        // 就只会显示序号0到5的内容(6项)。往左滑才会显示第7,8,9…最后一项。
        chart.setVisibleXRange(0,BAR_MAX_COUNT);

//        将当前视口的左侧（边）到指定的 x 值。
        chart.moveViewToX(curSelectValueX);

        // 将坐标移动到最新
        // 此代码将刷新图表的绘图
        Highlight high = new Highlight(curSelectValueX, 0, 0);
        high.setDataIndex(curSelectValueX);
        chart.highlightValue(high, false);

        chart.invalidate();
    }

    //30个横坐标时，缩放4f是正好的。
    private float scalePercent = 4f/30f;

    private float scaleNum(int xCount){
        return xCount * scalePercent;
    }

    /**
     * @des: Item选中监听
     * @params:
     * @return:
     */
    private SittingHappyTimeAdapter.OnItemCheckListener onItemCheckListener = new SittingHappyTimeAdapter.OnItemCheckListener() {
        @Override
        public void onItemCheck(int position, SittingHappyTimePictureModel filesBean, LinkedHashMap<String, String> isCheckedMap, int totalSize) {
            isCheckedMap = isCheckedMap;
            checkSize(isCheckedMap, totalSize);
        }

    };

    /**
     * @des: Item点击监听
     * @params:
     * @return:
     */
    private SittingHappyTimeAdapter.OnEtFilesClickListener onItemClickListener = new SittingHappyTimeAdapter.OnEtFilesClickListener() {

        @Override
        public void onEtFilesClick(SittingHappyTimePictureModel filesBean, int position, CheckBox checkBox) {
            if (isMultiSelect) {
                checkBox.setChecked(!checkBox.isChecked());
            } else {
                Intent intent = new Intent(getActivity(), SittingSitPreviewActivity.class);
                intent.putExtra("equipmentId", equipmentId);
                intent.putExtra("showErrorPictureDate", showErrorPictureDate);
                intent.putExtra("showErrorPictureRelationId", showErrorPictureRelationId);
                intent.putExtra("seqNum", filesBean.getId() + "");
                intent.putExtra("typeClass", typeClass);
                String dd = SittingReportUtil.getLocalDateTime(filesBean.getLocaleTime() + "");
                intent.putExtra("date", dd);
                ActivityUtils.startActivity(intent);
            }
        }
    };

    /**
     * @des: 是否显示空文件夹提示区域
     * @params:
     * @return:
     */

    private void isShowEmptyPrompt() {
        if (filesBeans != null && filesBeans.size() > 0) {
            recyclerView.setVisibility(View.VISIBLE);
            emptyLayout.setVisibility(View.GONE);
            sitting_happy_select_bar_rl.setVisibility(View.VISIBLE);
        } else {
            recyclerView.setVisibility(View.GONE);
            emptyLayout.setVisibility(View.VISIBLE);
            sitting_happy_select_bar_rl.setVisibility(View.GONE);
        }
    }

    /**
     * @des: 刷新列表
     * @params:
     * @return:
     */
    public void getRefreshList() {
        if (!NetworkUtils.isConnected()) {
            showMessage(R.string.toast_no_connection_network);
            showEmpty();
            hideProgressDialog();
            return;
        }

        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                refreshBeans = getEtFiles();

                if (Validator.isNotEmpty(refreshBeans)) {
                    filesBeans.clear();
                    filesBeans.addAll(refreshBeans);
                    getSeqNum(refreshBeans);
                }
                return null;
            }

            @Override
            public void onSuccess(Void result) {
                if (refreshBeans == null) {
                    refreshLayout.finishRefresh(false);
                } else if (!Validator.isNotEmpty(refreshBeans)) {
                    refreshLayout.finishRefresh();
                } else {
                    refreshLayout.finishRefresh();
                }
                isShowEmptyPrompt();
                refreshFilesPic();
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                refreshLayout.finishRefresh(false);
            }
        });
    }

    /**
     * @des: 刷新列表
     */
    public void getRefreshListForPicture() {
        if (!NetworkUtils.isConnected()) {
            showMessage(R.string.toast_no_connection_network);
            showEmpty();
            hideProgressDialog();
            return;
        }

        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                refreshBeans = getEtFiles();

                if (Validator.isNotEmpty(refreshBeans)) {
                    filesBeans.clear();
                    filesBeans.addAll(refreshBeans);
                    getSeqNum(refreshBeans);
                }
                return null;
            }

            @Override
            public void onSuccess(Void result) {
                isShowEmptyPrompt();
                auraFilesAdapter.refreshData(filesBeans);
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
            }
        });
    }

    /**
     * @des: 图片列表接口
     * @params:
     * @return:
     */
    private List<SittingHappyTimePictureModel> getEtFiles() {
        if (!NetworkUtils.isConnected()) {
            showMessage(R.string.toast_no_connection_network);
            showEmpty();
            hideProgressDialog();
            return null;
        }

        try {
            final MiaoHttpEntity<SittingHappyTimePictureModel> etFileEntity =
                    httpManager.request().getSittingHappyTimePictureAfter(
                            userPreferences.getUserId(),
                            equipmentId,
                            typeClass,
                            "",
                            showErrorPictureDate,
                            new TypeToken<List<SittingHappyTimePictureModel>>() {}.getType());
            if (etFileEntity == null) {
                return null;
            } else if (etFileEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return etFileEntity.getBodyList();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * @des: 重置选中状态并且刷新
     * @params:
     * @return:
     */
    private void refreshFilesPic() {
        isMultiSelect = false;
        isSelectAll = false;
        hideSelectTopBar();
        auraFilesAdapter.refreshData(filesBeans, isMultiSelect, isCheckedMap);
    }

    /**
     * @des: 显示选择TopBar
     * @params:
     * @return:
     */

    private void showSelectTopBar() {
        etFilesUnselectedTopBarRl.setVisibility(View.GONE);
        etFilesCancelBtn.setVisibility(View.VISIBLE);
        etFilesSelectAllBtn.setVisibility(View.VISIBLE);
        etFilesCancelBtn.setText(R.string.cancel);
        etFilesTitleTv.setVisibility(View.VISIBLE);
        etFilesTitleTv.setText(String.format(getString(R.string.sitting_picture_select_num),
                isCheckedMap.size() + "", filesBeans.size() + ""));
        etFilesNoTitleTv.setVisibility(View.GONE);
        etFilesSelectAllBtn.setText(R.string.delete);
    }

    /**
     * @des: 隐藏选择TopBar
     * @params:
     * @return:
     */

    private void hideSelectTopBar() {
        etFilesUnselectedTopBarRl.setVisibility(View.VISIBLE);
        etFilesCancelBtn.setVisibility(View.GONE);
        etFilesSelectAllBtn.setVisibility(View.GONE);
        etFilesTitleTv.setVisibility(View.GONE);
        etFilesNoTitleTv.setVisibility(View.VISIBLE);
    }

    /**
     * @des: 重置准备刷新
     * @params:
     * @return:
     */

    private void resetToFreshPic() {
        refreshLayout.resetNoMoreData();
        refreshLayout.closeHeaderOrFooter();
        filesBeans = new ArrayList<>();
        fileIds = new ArrayList<>();
        isCheckedMap.clear();
        isCheckedMap = new LinkedHashMap<>();
    }

    private void resetCheckListPic() {
        fileIds = new ArrayList<>();
        isCheckedMap.clear();
        isCheckedMap = new LinkedHashMap<>();
        etFilesTitleTv.setText(String.format(getString(R.string.sitting_picture_select_num),
                isCheckedMap.size() + "", filesBeans.size() + ""));
    }

    private void checkSelectAll(LinkedHashMap<String, String> isCheckedMap) {
        //如果选择不是全部Item  text变为取消全选
        if (isCheckedMap.size() < auraFilesAdapter.getTotalSize()) {
            isSelectAll = false;
        } else {
            isSelectAll = true;
        }
    }

    /**
     * @des: 根据数量显示bottom
     */
    private void judgeToShowBottom(LinkedHashMap<String, String> isCheckedMap) {
        fileIds = new ArrayList<>();
        for (Map.Entry<String, String> stringStringEntry : isCheckedMap.entrySet()) {
            fileIds.add(stringStringEntry.getKey());
        }

    }

    /**
     * @des: 检查选中个数
     */
    private void checkSize(LinkedHashMap<String, String> isCheckedMap, int totalSize) {
        judgeToShowBottom(isCheckedMap);
        if (isCheckedMap.size() == 1) {
            etFilesTitleTv.setText(String.format(getString(R.string.sitting_picture_select_one),
                    filesBeans.size() + ""));
        } else if (isCheckedMap.size() > 1) {
            etFilesTitleTv.setText(String.format(getString(R.string.sitting_picture_select_num),
                    isCheckedMap.size() + "", filesBeans.size() + ""));
        } else {
            if (isMultiSelect) {
                etFilesTitleTv.setText(String.format(getString(R.string.sitting_picture_select_num),
                        isCheckedMap.size() + "", filesBeans.size() + ""));
            }
        }
        checkSelectAll(isCheckedMap);
    }


    /**
     * @des: 下拉加载
     */
    private void loadMore() {
        if (!NetworkUtils.isConnected()) {
            showMessage(R.string.toast_no_connection_network);
            showEmpty();
            hideProgressDialog();
            return;
        }

        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                addFilesBeans = getFilesLoadMore(seqNum);
                if (addFilesBeans == null) {
                    return null;
                }
                if (Validator.isNotEmpty(addFilesBeans)) {
                    filesBeans.addAll(addFilesBeans);
                    getSeqNum(addFilesBeans);
                }
                return null;
            }

            @Override
            public void onSuccess(Void result) {
                if (addFilesBeans == null) {
                    refreshLayout.finishLoadMore(false);
                } else if (Validator.isEmpty(addFilesBeans)) {
                    refreshLayout.finishLoadMoreWithNoMoreData();
                } else {
                    auraFilesAdapter.refreshData(filesBeans);
                    refreshLayout.finishLoadMore(true);
                }
            }
        });
    }

    /**
     * @des: 下拉加载接口
     */
    private List<SittingHappyTimePictureModel> getFilesLoadMore(String seqNum) {
        logI("SittingReportHappyFrgment.refresh seqNuM: " + seqNum);
        if (!NetworkUtils.isConnected()) {
            showMessage(R.string.toast_no_connection_network);
            showEmpty();
            hideProgressDialog();
            return null;
        }

        try {
            final MiaoHttpEntity<SittingHappyTimePictureModel> etFileEntity =
                    httpManager.request().getSittingHappyTimePictureAfter(
                            userPreferences.getUserId(),
                            equipmentId,
                            typeClass,
                            seqNum,
                            showErrorPictureDate,
                            new TypeToken<List<SittingHappyTimePictureModel>>() {}.getType());
            if (etFileEntity == null) {
                return null;
            } else if (etFileEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return etFileEntity.getBodyList();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * @des: 获得下拉加载ID
     */
    private void getSeqNum(List<SittingHappyTimePictureModel> seqNumBeans) {
        if (seqNumBeans.size() > 0) {
            seqNum = seqNumBeans.get(seqNumBeans.size() - 1).getId() + "";
            logI("SittingReportHappyFrgment.seqNum:  :" + seqNum);
        }
    }

    // 检查开关切换
    private void checkHappyTimeSwitch(boolean isChecked) {
//        sittingHappyTimeSwitch.toggleSwitch(isChecked);
        setMessageShowOrHide(isChecked);

//        FastBleOperationUtils.threadSleep();
        // 设置服务器开关
        setSettingToServerHappyTime(isChecked);
//        FastBleOperationUtils.threadSleep();
    }

    private void setMessageShowOrHide(boolean flag){
        if (flag) {
            noteTitle.setVisibility(View.VISIBLE);
            alertTitle.setVisibility(View.INVISIBLE);
        }else{
            noteTitle.setVisibility(View.INVISIBLE);
            alertTitle.setVisibility(View.VISIBLE);
        }
    }

    // 坐姿开关 0为关 1为开 //postureSwitch	true	int	0:关，1:开
    private void setSettingToServerHappyTime(boolean isChecked) {
        httpManager.request().setSettingHappySwitch(
                userPreferences.getUserId(),
                equipmentId,
                isChecked ? "1" : "0",
                String.class, new MiaoHttpManager.CallbackNetwork<String>() {
                    @Override
                    public void onNoNetwork() {
                        showMessage(R.string.toast_no_connection_network);
                    }

                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                        setHappyTimeLocalData(isChecked);
                        // 设置设备开关
                        setSittingDeviceHappyTime(isChecked);
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                        showMessage(R.string.request_failed_alert);
                    }

                    @Override
                    public void onError(Exception e) {
                        showMessage(R.string.request_failed_alert);
                    }
                });
    }

    private void setHappyTimeLocalData(boolean isChecked) {
        int iHappyTime = isChecked ? 1:0;
//        String happyTimeName = isChecked? "开":"关";
        String happyTimeName = isChecked? CZURConstants.SITTING_HAPPYTIME[0]:CZURConstants.SITTING_HAPPYTIME[1];
        deviceModelHappy.setHappyTimeOpen(isChecked);
        deviceModelHappy.setiHappyTimeSwitch(iHappyTime);
        deviceModelHappy.setHappyTimeName(happyTimeName);
        userPreferences.setSittingDeviceModel(deviceModelHappy);
        EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_CHANGE_HAPPYTIME_SWITCH, deviceModelHappy));
    }

    // 设置设备开关
    private void setSittingDeviceHappyTime(boolean isChecked){
        int isOpen = isChecked ? 1:0;
        String param;
        boolean flagError = deviceModelHappy.isErrorTimeOpen();
        boolean flagHappy = deviceModelHappy.isHappyTimeOpen();

        param = FastBleConstants.HEAD_SETTING_SWITCH_HAPPY;
        // if Error开关=开；Happy不下发(都发开)
        // if Error=关；Happy下发
        if (flagError){
            //错误坐姿收集开关：12	0	RECV/SEND	字符串：0，关闭；1，打开
            FastBleOperationUtils.SetDeviceParams( 1, param);
        }else{
            FastBleOperationUtils.SetDeviceParams( isOpen, param);
        }

    }

    @SuppressLint("NonConstantResourceId")
    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.et_files_cancel_btn:
                cancelEvent();
                break;
            case R.id.et_files_select_all_btn:
//                selectAll();
                if (isCheckedMap.size() > 0){
                    confirmDeleteDialog();
                }else{
                    ToastUtils.showLong(R.string.sitting_happy_picture_select);
                }
                break;
            case R.id.et_files_multi_select_btn:
                multiSelect();
                break;
//            case R.id.et_folder_delete_rl:
//                confirmDeleteDialog();
//                break;
            default:
                break;
        }
    }

    /**
     * @des: 多选
     */
    private void multiSelect() {
        logI("SittingReportHappyFrgment.multiSelect.isMultiSelect="+isMultiSelect);
        if (Validator.isNotEmpty(filesBeans)) {
            isMultiSelect = !isMultiSelect;
            auraFilesAdapter.refreshData(isMultiSelect);
            if (isMultiSelect) {
                showSelectTopBar();
            } else {
                hideSelectTopBar();
            }
        }
    }

    /**
     * @des: 取消事件
     */
    private void cancelEvent() {
        fileIds = new ArrayList<>();
        isMultiSelect = false;
        isSelectAll = false;
        isCheckedMap.clear();
        isCheckedMap = new LinkedHashMap<>();
        auraFilesAdapter.refreshData(filesBeans, false, isCheckedMap);
        hideSelectTopBar();
    }

    /**
     * @des: 选中所有
     */
    private void selectAll() {
        if (!isSelectAll) {
            for (int i = 0; i < filesBeans.size(); i++) {
                if (!isCheckedMap.containsKey((filesBeans.get(i).getId()))) {
                    isCheckedMap.put(filesBeans.get(i).getId(), filesBeans.get(i).getSmallImgUrl());
                }
            }
            isSelectAll = true;
        } else {
            isCheckedMap.clear();
            isCheckedMap = new LinkedHashMap<>();
            isSelectAll = false;
        }
        etFilesTitleTv.setText(String.format(getString(R.string.sitting_picture_select_num),
                isCheckedMap.size() + "", filesBeans.size() + ""));
        auraFilesAdapter.refreshData(filesBeans, true, isCheckedMap);
    }

    /**
     * @des: 显示删除Dialog
     * @params:
     * @return:
     */
    private void confirmDeleteDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(getContext(), CloudCommonPopupConstants.COMMON_TWO_BUTTON2);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.confirm_delete));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                delete();
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();
    }

    /**
     * @des: 删除文件或者图片
     * @params:
     * @return:
     */
    private void delete() {
        httpManager.request().deleteSittingHappyTimePicture(
                userPreferences.getUserId(),
                equipmentId,
                EtUtils.transFiles(fileIds),
                String.class,
                new MiaoHttpManager.CallbackNetwork<String>() {
                    @Override
                    public void onNoNetwork() {
                        showMessage(R.string.toast_no_connection_network);
                    }

                    @Override
                    public void onStart() {
                        showProgressDialog();
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                        String key = EtUtils.transFiles(fileIds);
                        hideProgressDialog();
                        refreshAfterDeleteSuccess(key);
//                EventBus.getDefault().post(new DeleteFilesEvent(EventType.AURA_DELETE_FILE,key));
                        EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_HAPPYPIC_DELETE_SUCCESS, ""));
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                        hideProgressDialog();
                        resetCheckListPic();
                        refreshFilesPic();
                    }

                    @Override
                    public void onError(Exception e) {
                        hideProgressDialog();
                        resetCheckListPic();
                        refreshFilesPic();
                    }
                });
    }

    private void refreshAfterDeleteSuccess(String key) {
        Iterator<SittingHappyTimePictureModel> it = filesBeans.iterator();
        while (it.hasNext()) {
            if (key.contains(it.next().getId())) {
                it.remove();
            }
        }
        cancelEvent();
        isShowEmptyPrompt();
    }

}
