package com.czur.cloud.ui.starry.meeting.fragment

import android.content.*
import android.content.res.Configuration
import android.view.View
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.ToastUtils
import com.czur.cloud.BuildConfig
import com.czur.cloud.R
import com.czur.cloud.common.ShareSDKUtils
import com.czur.cloud.event.BaseEvent
import com.czur.cloud.event.EventType
import com.czur.cloud.preferences.StarryPreferences
import com.czur.cloud.ui.auramate.reportfragment.ReportUtil
import com.czur.cloud.ui.starry.activity.StarryActivity
import com.czur.cloud.ui.starry.adapter.StarryCompanyAdapter
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.common.StarryConstants.STARRY_PRE_PAGE_NAME
import com.czur.cloud.ui.starry.livedatabus.LiveDataBus
import com.czur.cloud.ui.starry.meeting.base.FloatFragment
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.meeting.dialog.StarrySocialShareDialog
import com.czur.cloud.ui.starry.meeting.model.MeetingModel
import com.czur.cloud.ui.starry.meeting.viewmodel.MeetingViewModel
import com.czur.cloud.ui.starry.model.StarryEnterpriseModel
import com.czur.cloud.ui.starry.utils.Tools
import com.czur.cloud.ui.starry.utils.singleClick
import com.czur.cloud.ui.starry.viewmodel.StarryViewModel
import com.czur.czurutils.log.logI
import kotlinx.android.synthetic.main.starry_activity_company_list.*
import kotlinx.android.synthetic.main.starry_layout_top_bar.*
import kotlinx.coroutines.delay
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

private const val TAG = "MeetingCompanyListFragment"

class MeetingCompanyListFragment : FloatFragment() {

    override fun getLayoutId() = R.layout.starry_activity_company_list

    private var socialShareDialog: StarrySocialShareDialog? = null

    private val viewModel by lazy {
        ViewModelProvider(StarryActivity.mainActivity ?: this).get(StarryViewModel::class.java)
    }

    private val meetingViewModel: MeetingViewModel by viewModels({ requireActivity() })

    private val linearLayoutManager by lazy {
        LinearLayoutManager(requireContext())
    }
    private val mAdapter by lazy {
        StarryCompanyAdapter(requireActivity())
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    override fun onEvent(event: BaseEvent) {
//        logI("${TAG}.onEvent=" + event.eventType)
        when (event.eventType) {
            // 接收到的会议结束指令-会议结束
            // 接收到的离开会议室指令--被移除
            EventType.STARRY_MEETING_CMD_STOP,
            EventType.STARRY_MEETING_CMD_REMOVE -> {
//                finish()
                dismiss()
            }

            //接收到的成为会议管理员指令
            EventType.STARRY_MEETING_CMD_HOST -> {
                // 判断是否为管理员，显示刷新、隐现按钮
                logI("${TAG}.onEvent=STARRY_MEETING_CMD_HOST")
                launch {
                    delay(1000)
                    onJudgeAdmin()
                }
            }

            else -> {}
        }
    }

    override fun initView() {
        super.initView()
        logI("${TAG}.initViews")

        viewModel.enterpriseListJoined.observe(this) {
            launch {
                delay(200)
                mAdapter.setmDatas(it)
            }
        }

        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }

        // top bar
        user_back_btn?.visibility = View.INVISIBLE
        user_back_btn_bg?.visibility = View.VISIBLE
        user_back_btn_bg?.setOnClickListener {
            dismiss()
        }

        // Title
        user_title.text = getString(R.string.starry_select_company_list)
        if (BuildConfig.IS_OVERSEAS) {
            user_title?.textSize = 16f
            user_title?.maxEms = 14
        }

        // list
        recycler_view_company?.apply {
            setHasFixedSize(true)
            layoutManager = linearLayoutManager
            adapter = mAdapter
        }
        mAdapter.setOnItemPickListener(object : StarryCompanyAdapter.OnItemPickListener {
            override fun onItemPick(position: Int, enterprise: StarryEnterpriseModel) {
                onClickEnterCompany(position, enterprise)
            }
        })

        // 显示、隐藏部分内容
        starry_company_password_rl?.visibility = View.GONE
        starry_meeting_code_pwd_rl?.visibility = View.VISIBLE
        starry_meeting_code_pwd_rl2?.visibility = View.VISIBLE

        // copy
        starry_meeting_code_copy?.singleClick {
            logI("${TAG}.starry_meeting_code_copy")
            launch {
                val paste_str = viewModel.getMeetingCodeString() ?: ""
                val mClipboardManager: ClipboardManager =
                    requireActivity().getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                val mClipData: ClipData = ClipData.newPlainText("czur", paste_str)
                mClipboardManager.setPrimaryClip(mClipData)
                ToastUtils.showLong(R.string.starry_meetingcode_copy_ok)
            }
        }

        // share
        socialShareDialog = StarrySocialShareDialog(requireContext(), shareDialogOnClickListener)
        starry_meeting_share?.singleClick {
            logI("${TAG}.starry_meeting_share")
            socialShareDialog?.show()
        }

        // refresh
        starry_meeting_pwd_refresh?.singleClick {
            logI("${TAG}.starry_meeting_pwd_refresh")
            launch {
                viewModel.onRandMeetingPwdInMeeting()
                starry_meeting_pwd_title?.text = String.format(
                    getString(R.string.starry_company_pwd_title),
                    viewModel.starryMeetingPwdAuto
                )

                // api
                viewModel.updateMeetingPassword(MeetingModel.room, viewModel.starryMeetingPwdAuto)
                if (MeetingModel.isInMeeting.value == false) {
                    // 记录一下密码
                    StarryPreferences.getInstance().lastPwd = viewModel.starryMeetingPwdAuto
                }
            }
        }

        // select
        starry_meeting_code_pwd_show?.singleClick {
            logI("${TAG}.starry_meeting_code_pwd_show")
            val isShowPwd = !it.isSelected

            starry_meeting_code_pwd_show?.isSelected = isShowPwd
            onSetShowPwd(isShowPwd)

            // api
            viewModel.updateMeetingPassword(MeetingModel.room, viewModel.starryMeetingPwdAuto)
        }

        var meeting_code = MeetingModel.meetingCode ?: ""
        meeting_code = Tools.formateMeetCode(meeting_code)
        starry_meeting_code_title?.text = String.format(
            getString(R.string.starry_meeting_code_title), meeting_code
        )

        // 判断是否为管理员，显示刷新、隐现按钮
        onJudgeAdmin()

    }

    private fun onJudgeAdmin() {
        val isShowPwd = MeetingModel.meetingPassword.isNotEmpty() ?: false
        if (isShowPwd) {
            starry_meeting_pwd_title?.alpha = 1.0f
            starry_meeting_pwd_refresh?.visibility = View.VISIBLE
            starry_meeting_pwd_title?.text = String.format(
                getString(R.string.starry_company_pwd_title),
                MeetingModel.meetingPassword
            )
        } else {
            starry_meeting_pwd_title?.alpha = 0.5f
            starry_meeting_pwd_refresh?.visibility = View.INVISIBLE
            viewModel.starryMeetingPwdAuto = ""
            starry_meeting_pwd_title?.text = getString(R.string.starry_company_pwd_title_no)
        }

        if (meetingViewModel.isAdmin()) {
            starry_meeting_code_pwd_show?.visibility = View.VISIBLE
            starry_meeting_code_pwd_show?.isSelected = isShowPwd
        } else {
            starry_meeting_pwd_refresh?.visibility = View.INVISIBLE
            starry_meeting_code_pwd_show?.visibility = View.INVISIBLE
        }


        // MeetingModel.isMeetingLock
        if (MeetingModel.isRoomLocked) {
            starry_meeting_code_pwd_rl2?.visibility = View.GONE
            starry_meeting_share?.visibility = View.GONE
        } else {
            starry_meeting_code_pwd_rl2?.visibility = View.VISIBLE
            starry_meeting_share?.visibility = View.VISIBLE
        }

    }

    private fun onSetShowPwd(flag: Boolean) {
        if (flag) {
//            viewModel.starryMeetingPwdAuto = StarryPreferences.getInstance().lastPwd
            viewModel.starryMeetingPwdAuto = viewModel.inMeetingResetPwd
            starry_meeting_pwd_title?.alpha = 1.0f
            starry_meeting_pwd_refresh?.visibility = View.VISIBLE
            if (StarryPreferences.getInstance().lastPwd == "") {
                viewModel.onRandMeetingPwdInMeeting()
            }
            starry_meeting_pwd_title?.text = String.format(
                getString(R.string.starry_company_pwd_title),
                viewModel.starryMeetingPwdAuto
            )
        } else {
            starry_meeting_pwd_title?.alpha = 0.5f
            starry_meeting_pwd_refresh?.visibility = View.INVISIBLE
            viewModel.starryMeetingPwdAuto = ""
            starry_meeting_pwd_title?.text = getString(R.string.starry_company_pwd_title_no)
        }
    }

    private fun onClickEnterCompany(position: Int, enterprise: StarryEnterpriseModel) {
        viewModel.setCurrentUserTypeCompany(enterprise)
        viewModel.starryPrePageName = STARRY_PRE_PAGE_NAME
        if (MeetingModel.currentOrientationStatus == Configuration.ORIENTATION_LANDSCAPE) {
            MeetingCompanyListContactsFragment().show(
                ByScreenParams(),
                AnimDirection.RIGHT
            )
        } else {
            MeetingCompanyListContactsFragment().show(
                ByScreenParams(),
                AnimDirection.BOTTOM
            )
        }
    }

    override fun initData() {
        super.initData()


        MeetingModel.isMeetingLocked.observe(this) {
            if (it && !MeetingModel.isAdmin()) {

                if (socialShareDialog != null && socialShareDialog?.isShowing == true) {
                    socialShareDialog?.dismiss()
                }
                dismiss()
            }
        }

        LiveDataBus.get()
            .with(StarryConstants.MEETING_USER_LIST_CHANGE_PWD, Boolean::class.java)
            .observe(this) {
                if (MeetingModel.meetingPassword.isNotBlank()) {
                    starry_meeting_pwd_title?.alpha = 1.0f
                    starry_meeting_pwd_title?.text = String.format(
                        getString(R.string.starry_company_pwd_title),
                        MeetingModel.meetingPassword
                    )
                } else {
                    starry_meeting_pwd_title?.alpha = 0.5f
                    starry_meeting_pwd_title?.text = getString(R.string.starry_company_pwd_title_no)
                }
            }

    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
    }

    override fun onDetach() {
        super.onDetach()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

    override fun handleBackPressed(): Boolean {
        //处理自己的逻辑
        logI("${TAG}.handleBackPressed")
        dismiss()
        return true
    }

    /**
     * 分享dialog监听
     */
    private val shareDialogOnClickListener =
        object : StarrySocialShareDialog.ShareDialogOnClickListener {
            override fun onShareItemClick(viewId: Int) {
                val cnt = requireContext()
                var isInstall = false
                when (viewId) {
                    R.id.weixin_share -> {
                        isInstall = ReportUtil.isWeixinInstalled(cnt)
                        if (isInstall) {
                            launch {
                                val content = viewModel.getMeetingCodeString() ?: ""
                                shareTextToWechat(content)
                            }
                        } else {
                            showMessage(R.string.share_not_install_wechat)
                        }
                        socialShareDialog?.dismiss()
                    }

                    R.id.copy_share -> {
                        launch {
//                            socialShareDialog?.copy_share?.let {
//                                Tools.setViewButtonEnable(
//                                    it,
//                                    false,
//                                    0.6f
//                                )
//                            }
                            socialShareDialog?.findViewById<View>(R.id.copy_share).let {
                                if (it != null) {
                                    Tools.setViewButtonEnable(
                                        it,
                                        false,
                                        0.6f
                                    )
                                }
                            }
                            val paste_str = viewModel.getMeetingCodeString() ?: ""
                            val mClipboardManager: ClipboardManager =
                                requireActivity().getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                            val mClipData: ClipData = ClipData.newPlainText("czur", paste_str)
                            mClipboardManager.setPrimaryClip(mClipData)
                            ToastUtils.showLong(R.string.starry_meetingcode_copy_ok)

                            socialShareDialog?.dismiss()
                            socialShareDialog?.findViewById<View>(R.id.copy_share).let {
                                if (it != null) {
                                    Tools.setViewButtonEnable(
                                        it,
                                        true
                                    )
                                }
                            }
                        }
                    }

                    R.id.share_dialog_cancel_btn -> {
                        socialShareDialog?.dismiss()
                    }
                }
            }
        }


    /**
     * 分享到微信
     */
    private fun shareTextToWechat(content: String) {
        if (android.os.Build.MANUFACTURER == "HUAWEI") {
            // 华为机型, 直接分享到微信, 使用SDK分享, 会有概率出现, 进入微信就退出的问题
            shareTextToWechatByDirect(content)
        } else {
            // 其他机型
            ShareSDKUtils.shareTextToWechatByShareSDK(requireContext(), content)
        }
    }

    private fun shareTextToWechatByDirect(content: String) {
        val packageWechat = "com.tencent.mm"
        val intent = Intent().apply {
            val cop = ComponentName(packageWechat, "com.tencent.mm.ui.tools.ShareImgUI")
            component = cop
            action = Intent.ACTION_SEND
            putExtra(Intent.EXTRA_TEXT, content)
            putExtra("Kdescription", "shareTextToWechatFriend")
        }
        startActivity(intent)
    }
}
