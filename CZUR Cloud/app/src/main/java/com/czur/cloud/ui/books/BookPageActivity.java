package com.czur.cloud.ui.books;

import static com.blankj.utilcode.util.PermissionUtils.isGranted;

import android.Manifest;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.badoo.mobile.util.WeakHandler;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.BookPageAdapter;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.entity.realm.BookEntity;
import com.czur.cloud.entity.realm.BookPdfEntity;
import com.czur.cloud.entity.realm.PageEntity;
import com.czur.cloud.entity.realm.TagEntity;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.ChangeTagEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.preferences.FirstPreferences;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.camera.CameraActivity;
import com.czur.cloud.ui.component.ControlScrollGridLayoutManager;
import com.czur.cloud.ui.component.dialog.BookPageSheetBottomDialog;
import com.czur.cloud.ui.component.popup.BlackRightPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.component.popup.SavePdfPopup;
import com.czur.cloud.util.EtUtils;
import com.czur.cloud.util.GeneratePdfUtils;
import com.czur.cloud.util.PermissionUtil;
import com.czur.cloud.util.validator.Validator;
import com.facebook.drawee.backends.pipeline.Fresco;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.UUID;

import io.realm.Realm;
import io.realm.RealmChangeListener;
import io.realm.RealmResults;
import io.realm.Sort;

//import com.czur.cloud.ui.camera.CameraActivity;

/**
 * Created by Yz on 2018/2/28.
 * Email：<EMAIL>
 */

@SuppressWarnings("AlibabaAvoidManuallyCreateThread")
public class BookPageActivity extends BaseActivity implements View.OnClickListener {

    private RecyclerView bookPageRecyclerView;
    private LinearLayout bookPageUnselectedLeftTopBar;
    private ImageView bookPageAllPageImg;
    private ImageView bookPageStarImg;
    private ImageView bookPageBackBtn;
    private ImageView bookPageAddBtn;
    private TextView bookPageFolderNameTitle;
    private TextView booksPageSelectAllBtn;
    private TextView bookPageTitle;
    private TextView bookPageCancelBtn;
    private TextView bookPageAllPageTv;
    private TextView bookPageStarPageTv;
    private LinearLayout bookPageHideBottomLl;
    private RelativeLayout booksPageUnselectedRightTopBar;
    private RelativeLayout bookshelfSortBtn;
    private RelativeLayout bookPageMultiSelectBtn;
    private RelativeLayout bookPageAllPageRl;
    private RelativeLayout bookPageStarRl;
    private RelativeLayout bookPageDeleteRl;
    private RelativeLayout bookPagePdfRl;
    private RelativeLayout bookPageAddRl;
    private RelativeLayout bookPageEmptyRl;
    private RelativeLayout bookPageSaveAlbumRl;
    private RelativeLayout bookshelfInsideTopBar;
    private List<PageEntity> pageEntities;
    private LinkedHashMap<String, Boolean> isCheckedMap;
    private BookPageAdapter bookPageAdapter;
    private ControlScrollGridLayoutManager controlScrollGridLayoutManager;

    private BookPageSheetBottomDialog bottomDialog;
    private boolean isCorrectOrder = true;
    //0页码正序，1页码倒序，2时间正序，3，时间倒序
    private int orderType = 0;
    private boolean isStar = false;
    private String noteName;
    private Realm realm;
    private String bookId;
    private int pageNum = 0;
    private UserPreferences userPreferences;
    private EditText dialogEdt;
    private TextView progressTitle;
    private SavePdfPopup savePdfPopup;
    private CloudCommonPopup commonPopup;
    private WeakHandler handler;
    private List<String> paths;
    private SimpleDateFormat formatter;
    private boolean isOperate = false;
    private boolean isRealmRefresh = false;
    private RelativeLayout pageFileStarRl;

    private ImageView bookshelfPageGuideImg;
    private TextView bookshelfPageGuideTv;
    private TextView bookshelfPageHideTv;
    private ImageView bookPageSaveAlbumImg;
    private TextView bookPageSaveAlbumTv;
    private ImageView bookPagePdfImg;
    private TextView bookPagePdfTv;
    private ImageView pagePreviewStarImg;
    private TextView pagePreviewStarTv;
    private ImageView bookPageDeleteImg;
    private TextView bookPageDeleteTv;
    private FirstPreferences firstPreferences;

    private RelativeLayout bookPageFileTagRl;
    private ImageView pagePreviewTagImg;
    private TextView pagePreviewTagTv;
    private ArrayList<String> pageIds = new ArrayList<>();
    private boolean isTag;
    private String tagId;
    private String tagName;
    private RealmResults<PageEntity> deleteEntities;
    private String bookPdfPath;
    private String pdfId;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_book_page);
        initComponent();
        initRecyclerView();
        createBottomSheetDialog();
        registerEvent();
    }

    private void createBottomSheetDialog() {
        bottomDialog = new BookPageSheetBottomDialog(this, onPageBottomClickListener);
    }

    private void initComponent() {
        realm = Realm.getDefaultInstance();
        isTag = getIntent().getBooleanExtra("isTag", false);
        tagId = getIntent().getStringExtra("tagId");
        tagName = getIntent().getStringExtra("tagName");
        noteName = getIntent().getStringExtra("noteName");
        bookId = getIntent().getStringExtra("bookId");


        handler = new WeakHandler();
        userPreferences = UserPreferences.getInstance(this);
        bookPdfPath = getFilesDir() + File.separator + userPreferences.getUserId() + File.separator + CZURConstants.PDF_PATH;

        firstPreferences = FirstPreferences.getInstance(this);
        EventBus.getDefault().register(this);
        formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        bookPageSaveAlbumRl = (RelativeLayout) findViewById(R.id.book_page_save_album_rl);
        bookPageAllPageImg = (ImageView) findViewById(R.id.book_page_all_page_img);
        bookPageStarImg = (ImageView) findViewById(R.id.book_page_star_img);
        bookPageAllPageTv = (TextView) findViewById(R.id.book_page_all_page_tv);
        bookPageStarPageTv = (TextView) findViewById(R.id.book_page_star_page_tv);
        bookPageEmptyRl = (RelativeLayout) findViewById(R.id.book_page_empty_rl);
        bookPageHideBottomLl = (LinearLayout) findViewById(R.id.book_page_hide_bottom_ll);
        bookPageRecyclerView = (RecyclerView) findViewById(R.id.book_page_recyclerView);
        bookPageAddRl = (RelativeLayout) findViewById(R.id.book_page_add_rl);
        bookshelfInsideTopBar = (RelativeLayout) findViewById(R.id.bookshelf_inside_top_bar);
        bookPageUnselectedLeftTopBar = (LinearLayout) findViewById(R.id.book_page_unselected_left_top_bar);
        bookPageBackBtn = (ImageView) findViewById(R.id.book_page_back_btn);
        bookPageFolderNameTitle = (TextView) findViewById(R.id.book_page_folder_name_title);
        booksPageSelectAllBtn = (TextView) findViewById(R.id.books_page_select_all_btn);
        bookPageTitle = (TextView) findViewById(R.id.book_page_title);
        bookPageCancelBtn = (TextView) findViewById(R.id.book_page_cancel_btn);
        booksPageUnselectedRightTopBar = (RelativeLayout) findViewById(R.id.books_page_unselected_right_top_bar);
        bookshelfSortBtn = (RelativeLayout) findViewById(R.id.bookshelf_sort_btn);
        bookPageMultiSelectBtn = (RelativeLayout) findViewById(R.id.book_page_multi_select_btn);
        bookPageAllPageRl = (RelativeLayout) findViewById(R.id.book_page_all_page_rl);
        bookPageStarRl = (RelativeLayout) findViewById(R.id.book_page_star_rl);
        bookPageDeleteRl = (RelativeLayout) findViewById(R.id.book_page_delete_rl);
        bookPagePdfRl = (RelativeLayout) findViewById(R.id.book_page_pdf_rl);
        bookPageAddBtn = (ImageView) findViewById(R.id.book_page_add_btn);
        pageFileStarRl = (RelativeLayout) findViewById(R.id.book_page_file_star_rl);


        bookshelfPageGuideImg = (ImageView) findViewById(R.id.bookshelf_page_guide_img);
        bookshelfPageGuideTv = (TextView) findViewById(R.id.bookshelf_page_guide_tv);
        bookshelfPageHideTv = (TextView) findViewById(R.id.bookshelf_page_hide_tv);
        bookPageSaveAlbumImg = (ImageView) findViewById(R.id.book_page_save_album_img);
        bookPageSaveAlbumTv = (TextView) findViewById(R.id.book_page_save_album_tv);
        bookPagePdfImg = (ImageView) findViewById(R.id.book_page_pdf_img);
        bookPagePdfTv = (TextView) findViewById(R.id.book_page_pdf_tv);
        pagePreviewStarImg = (ImageView) findViewById(R.id.page_preview_star_img);
        pagePreviewStarTv = (TextView) findViewById(R.id.page_preview_star_tv);
        bookPageDeleteImg = (ImageView) findViewById(R.id.book_page_delete_img);
        bookPageDeleteTv = (TextView) findViewById(R.id.book_page_delete_tv);
        bookPageFileTagRl = (RelativeLayout) findViewById(R.id.book_page_file_tag_rl);
        pagePreviewTagImg = (ImageView) findViewById(R.id.page_preview_tag_img);
        pagePreviewTagTv = (TextView) findViewById(R.id.page_preview_tag_tv);


        bookPageFolderNameTitle.setText(isTag ? tagName : noteName);
        bookPageAddRl.setVisibility(isTag ? View.GONE : View.VISIBLE);
        bookPageStarPageTv.setTextColor(getColor(R.color.normal_blue));
        bookPageAllPageTv.setTextColor(getColor(R.color.blue_29b0d7));
        bookPageAllPageImg.setSelected(true);
        checkPromptToShow();

    }

    private void registerEvent() {
        realm.addChangeListener(realmChangeListener);
        bookshelfPageHideTv.setOnClickListener(this);
        bookPageBackBtn.setOnClickListener(this);
        booksPageSelectAllBtn.setOnClickListener(this);
        bookPageCancelBtn.setOnClickListener(this);
        bookshelfSortBtn.setOnClickListener(this);
        bookPageMultiSelectBtn.setOnClickListener(this);
        bookPageAllPageRl.setOnClickListener(this);
        bookPageStarRl.setOnClickListener(this);
        bookPageDeleteRl.setOnClickListener(this);
        bookPagePdfRl.setOnClickListener(this);
        bookPageAddBtn.setOnClickListener(this);
        bookPageSaveAlbumRl.setOnClickListener(this);
        pageFileStarRl.setOnClickListener(this);
        bookPageFileTagRl.setOnClickListener(this);
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {

            case BOOKS_OR_PAGES_CHANGED:
                if (isTag) {
                    TagEntity tagEntity = realm.where(TagEntity.class)
                            .equalTo("tagName", tagName)
                            .equalTo("isDelete", 1)
                            .findFirst();
                    if (tagEntity != null) {
                        ActivityUtils.finishActivity(this);
                    }
                } else {
                    BookEntity bookEntity = realm.where(BookEntity.class)
                            .equalTo("bookName", this.noteName)
                            .equalTo("isDelete", 1)
                            .findFirst();
                    if (bookEntity != null) {
                        ActivityUtils.finishActivity(this);
                    }
                }

                sortByOrder(orderType, isStar);
                break;
            case ADD_TAGS:
            case DELETE_TAGS:
                cancelEvent();
                break;
            default:
                break;
        }
    }

    /**
     * @des:数据库改变监听
     * @params:
     * @return:
     */

    private RealmChangeListener realmChangeListener = new RealmChangeListener() {
        @Override
        public void onChange(Object element) {
            isRealmRefresh = true;
            sortByOrder(orderType, isStar);

        }
    };

    /**
     * @des: 初始化列表
     * @params:
     * @return:
     */

    private void initRecyclerView() {
        pageEntities = new ArrayList<>();
        if (isTag) {
            pageEntities = realm.where(PageEntity.class).equalTo("tagName", tagName).equalTo("isDelete", 0).findAll().sort("pageNum", Sort.ASCENDING);
        } else {
            pageEntities = realm.where(PageEntity.class).equalTo("noteName", noteName).equalTo("isDelete", 0).findAll().sort("pageNum", Sort.ASCENDING);
        }
        isCheckedMap = new LinkedHashMap<>();
        bookPageAdapter = new BookPageAdapter(this, pageEntities, false);
        bookPageAdapter.setOnItemClickListener(onItemClickListener);
        bookPageAdapter.setOnItemCheckListener(onItemCheckListener);
        bookPageAdapter.setOnItemLongClickListener(onItemLongClickListener);
        bookPageRecyclerView.setAdapter(bookPageAdapter);
        bookPageRecyclerView.setHasFixedSize(true);
        initGirdLayout();
        isShowEmptyPrompt();
    }

    /**
     * @des: 长按监听
     * @params:
     * @return:
     */

    private BookPageAdapter.OnItemLongClickListener onItemLongClickListener = new BookPageAdapter.OnItemLongClickListener() {
        @Override
        public void onPageEntityLongClick(int position, PageEntity pageEntity, LinkedHashMap<String, Boolean> isCheckedMap, int totalSize) {
            multiSelect();
            checkSize(isCheckedMap, totalSize);
            bookPageTitle.setText(R.string.select_one);
            showAll();
            bookPageAdapter.refreshData(true);
        }
    };
    /**
     * @des: item选择监听
     * @params:
     * @return:
     */
    private BookPageAdapter.OnItemCheckListener onItemCheckListener = new BookPageAdapter.OnItemCheckListener() {
        @Override
        public void onItemCheck(int position, PageEntity PageEntity, LinkedHashMap<String, Boolean> isCheckedMap, int totalSize) {
            BookPageActivity.this.isCheckedMap = isCheckedMap;
            //如果选中一个 文案变为已选中1个
            if (isCheckedMap.size() == 1) {
                bookPageTitle.setText(R.string.select_one);
                showAll();
            } else if (isCheckedMap.size() > 1) {
                bookPageTitle.setText(String.format(getString(R.string.select_num), isCheckedMap.size() + ""));
                showAll();
            } else {
                darkAll();
                if (isMultiSelect) {
                    bookPageTitle.setText(String.format(getString(R.string.select_num), isCheckedMap.size() + ""));
                }

            }
            //如果选择不是全部Item  text变为取消全选
            checkSize(isCheckedMap, totalSize);
        }
    };

    /**
     * @des: 判断检查状态
     * @params:
     * @return:
     */

    private void checkSize(LinkedHashMap<String, Boolean> isCheckedMap, int totalSize) {
        if (isCheckedMap.size() < totalSize) {
            booksPageSelectAllBtn.setText(R.string.select_all);
            isSelectAll = false;
        } else {
            booksPageSelectAllBtn.setText(R.string.not_select_all);
            isSelectAll = true;
        }
    }

    /**
     * @des: 通过GirdLayoutManger控制footer占多少行
     * @params:
     * @return:
     */


    private void initGirdLayout() {
        controlScrollGridLayoutManager = new ControlScrollGridLayoutManager(this, 3);
        controlScrollGridLayoutManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
            @Override
            public int getSpanSize(int position) {
                return bookPageAdapter.isFooter(position) ? controlScrollGridLayoutManager.getSpanCount() : 1;
            }
        });
//        bookPageRecyclerView.setOnScrollListener(onScrollListener);
        bookPageRecyclerView.setLayoutManager(controlScrollGridLayoutManager);
    }

    /**
     * @des: Item点击监听
     * @params:
     * @return:
     */

    private BookPageAdapter.OnItemClickListener onItemClickListener = new BookPageAdapter.OnItemClickListener() {


        @Override
        public void onPageEntityClick(PageEntity pageEntity, int position, CheckBox checkBox) {
            if (isMultiSelect) {
                checkBox.setChecked(!checkBox.isChecked());
            } else {
                Intent intent = new Intent(BookPageActivity.this, BookPagePreviewActivity.class);
                intent.putExtra("isCorrectOrder", isCorrectOrder);
                intent.putExtra("orderType", orderType);
                intent.putExtra("isStar", isStar);
                intent.putExtra("isTag", isTag);
                intent.putExtra("isItemStar", pageEntity.getIsStar());
                intent.putExtra("isItemTag", Validator.isNotEmpty(pageEntity.getTagName()) ? 1 : 0);
                intent.putExtra("noteName", noteName);
                intent.putExtra("tagId", tagId);
                intent.putExtra("tagName", tagName);
                intent.putExtra("bookId", bookId);
                intent.putExtra("index", position);
                intent.putExtra("currentPageId", pageEntity.getPageId());
                startActivity(intent);
            }
        }
    };
    private boolean isMultiSelect = false;
    private boolean isSelectAll = false;

    /**
     * @des: 显示所有笔记
     * @params:
     * @return:
     */

    private void showAllNote() {
        isStar = false;
        resetStatus();
        sortByOrder(orderType, isStar);
        bookPageStarPageTv.setTextColor(this.getResources().getColor(R.color.normal_blue));
        bookPageAllPageTv.setTextColor(this.getResources().getColor(R.color.blue_29b0d7));
        bookPageAllPageImg.setSelected(true);
        bookPageStarImg.setSelected(false);
    }

    /**
     * @des: 列表滑动
     * @params:
     * @return:
     */

    int preScrollState = 0;
    private RecyclerView.OnScrollListener onScrollListener = new RecyclerView.OnScrollListener() {
        @Override
        public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            switch (newState) {
                //停止滑动
                case RecyclerView.SCROLL_STATE_IDLE:
                    if (Fresco.getImagePipeline().isPaused()) {
                        Fresco.getImagePipeline().resume();
                    }

                    break;
                case RecyclerView.SCROLL_STATE_DRAGGING:
                    if (preScrollState == RecyclerView.SCROLL_STATE_SETTLING) {
                        //触摸滑动不需要加载
                        Fresco.getImagePipeline().pause();
                    } else {
                        //触摸滑动需要加载
                        if (Fresco.getImagePipeline().isPaused()) {
                            Fresco.getImagePipeline().resume();
                        }
                    }
                    break;
                //惯性滑动
                case RecyclerView.SCROLL_STATE_SETTLING:
                    Fresco.getImagePipeline().pause();
                    break;
                default:
                    break;
            }
            preScrollState = newState;


        }

        @Override
        public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
            super.onScrolled(recyclerView, dx, dy);
        }
    };

    private void checkPromptToShow() {

        if (firstPreferences.isFirstBookPageGuide()) {
            bookshelfPageGuideImg.setVisibility(View.VISIBLE);
            bookshelfPageHideTv.setVisibility(View.VISIBLE);
            bookshelfPageGuideTv.setVisibility(View.VISIBLE);

        } else {
            bookshelfPageGuideImg.setVisibility(View.GONE);
            bookshelfPageHideTv.setVisibility(View.GONE);
            bookshelfPageGuideTv.setVisibility(View.GONE);
        }

    }

    /**
     * @des: 显示星标笔记
     * @params:
     * @return:
     */

    private void showStarNote() {
        isStar = true;
        resetStatus();
        sortByOrder(orderType, isStar);
        bookPageAllPageTv.setTextColor(this.getResources().getColor(R.color.normal_blue));
        bookPageStarPageTv.setTextColor(this.getResources().getColor(R.color.blue_29b0d7));
        bookPageStarImg.setSelected(true);
        bookPageAllPageImg.setSelected(false);
    }

    private void resetStatus() {
        bookPageTitle.setText("");
        isMultiSelect = false;
        isSelectAll = false;
        isCheckedMap.clear();
        isCheckedMap = new LinkedHashMap<>();
        hideSelectTopBar();
    }

    /**
     * @des: 多选
     * @params:
     * @return:
     */

    private void multiSelect() {
        if (Validator.isNotEmpty(pageEntities)) {
            isMultiSelect = !isMultiSelect;
            bookPageAdapter.refreshData(isMultiSelect);
            if (isMultiSelect) {
                showSelectTopBar();
            } else {
                hideSelectTopBar();
            }
        }
    }

    /**
     * @des: 选中所有
     * @params:
     * @return:
     */

    private void selectAll() {
        if (!isSelectAll) {
            for (int i = 0; i < pageEntities.size(); i++) {
                if (!isCheckedMap.containsKey((pageEntities.get(i).getPageId()))) {
                    isCheckedMap.put(pageEntities.get(i).getPageId(), true);
                }
            }
            booksPageSelectAllBtn.setText(R.string.not_select_all);
            isSelectAll = true;

        } else {


            isCheckedMap.clear();
            isCheckedMap = new LinkedHashMap<>();
            booksPageSelectAllBtn.setText(R.string.select_all);
            isSelectAll = false;
        }
        bookPageTitle.setText(String.format(getString(R.string.select_num), isCheckedMap.size() + ""));
        bookPageAdapter.refreshData(pageEntities, true, isCheckedMap);
    }

    /**
     * @des: 取消事件
     * @params:
     * @return:
     */

    private void cancelEvent() {
        resetStatus();
        bookPageTitle.setText("");
        bookPageAdapter.refreshData(pageEntities, false, isCheckedMap);
    }

    /**
     * @des: 删除books
     * @params:
     * @return:
     */

    private void deletePages() {
        final List<String> needDeletePaths = new ArrayList<>();
        for (final String pageId : isCheckedMap.keySet()) {
            //得到待删除pdf paths
            PageEntity deletePageEntity = realm.where(PageEntity.class)
                    .equalTo("pageId", pageId)
                    .equalTo("isDelete", 0).findFirst();
            needDeletePaths.add(deletePageEntity.getPicUrl());
            needDeletePaths.add(deletePageEntity.getSmallPicUrl());
        }
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                //删除sd卡上book page
                for (String needDeletePath : needDeletePaths) {
                    FileUtils.delete(needDeletePath);
                }
                return null;
            }

            @Override
            public void onSuccess(Void result) {

            }
        });

        realm.executeTransaction(new Realm.Transaction() {
            @Override
            public void execute(Realm realms) {
                for (String finalId : isCheckedMap.keySet()) {
                    //先查找后得到PageEntity对象
                    PageEntity deletePageEntity = realm.where(PageEntity.class)
                            .equalTo("pageId", finalId)
                            .equalTo("isDelete", 0)
                            .findFirst();

                    deletePageEntity.setIsDelete(1);
                    deletePageEntity.setIsDirty(1);
                    String curDate = formatter.format(new Date(System.currentTimeMillis()));
                    deletePageEntity.setUpdateTime(curDate);
                }
                EventBus.getDefault().post(new ChangeTagEvent(EventType.DELETE_TAGS));
                resetStatus();
                startAutoSync();
            }
        });
    }

    /**
     * @des: 显示选择TopBar
     * @params:
     * @return:
     */

    private void showSelectTopBar() {
        darkAll();
        bookPageAllPageRl.setClickable(false);
        bookPageAllPageRl.setEnabled(false);
        bookPageStarRl.setClickable(false);
        bookPageStarRl.setEnabled(false);

        bookshelfPageGuideImg.setVisibility(View.GONE);
        bookshelfPageHideTv.setVisibility(View.GONE);
        bookshelfPageGuideTv.setVisibility(View.GONE);
        bookPageHideBottomLl.setVisibility(View.VISIBLE);
        if (isStar) {
            pagePreviewStarTv.setText(R.string.cancel_page);
        } else {
            pagePreviewStarTv.setText(R.string.star);
        }
        isOperate = false;
        bookPageUnselectedLeftTopBar.setVisibility(View.GONE);
        booksPageUnselectedRightTopBar.setVisibility(View.GONE);
        bookPageCancelBtn.setVisibility(View.VISIBLE);
        booksPageSelectAllBtn.setVisibility(View.VISIBLE);
        bookPageCancelBtn.setText(R.string.cancel);
        booksPageSelectAllBtn.setText(R.string.select_all);
        bookPageAddRl.setVisibility(View.GONE);
        bookPageTitle.setText(String.format(getString(R.string.select_num), isCheckedMap.size() + ""));
    }

    /**
     * @des: 隐藏选择TopBar
     * @params:
     * @return:
     */

    private void hideSelectTopBar() {
        bookPageAllPageRl.setClickable(true);
        bookPageAllPageRl.setEnabled(true);
        bookPageStarRl.setClickable(true);
        bookPageStarRl.setEnabled(true);

        checkPromptToShow();
        isOperate = true;
        bookPageHideBottomLl.setVisibility(View.GONE);
        bookPageUnselectedLeftTopBar.setVisibility(View.VISIBLE);
        booksPageUnselectedRightTopBar.setVisibility(View.VISIBLE);
        bookPageCancelBtn.setVisibility(View.GONE);
        booksPageSelectAllBtn.setVisibility(View.GONE);
        bookPageAddRl.setVisibility(isTag ? View.GONE : View.VISIBLE);


    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.bookshelf_page_hide_tv:
                firstPreferences.setIsFirstBookPageGuide(false);
                bookshelfPageGuideImg.setVisibility(View.GONE);
                bookshelfPageGuideTv.setVisibility(View.GONE);
                bookshelfPageHideTv.setVisibility(View.GONE);
                break;
            case R.id.book_page_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.book_page_save_album_rl:
                copyToSd();
                break;
            case R.id.books_page_select_all_btn:
                selectAll();
                break;
            case R.id.book_page_cancel_btn:
                cancelEvent();
                break;

            case R.id.bookshelf_sort_btn:
                bottomDialog.show();
                break;

            case R.id.book_page_multi_select_btn:
                multiSelect();
                break;

            case R.id.book_page_all_page_rl:
                showAllNote();
                break;
            case R.id.book_page_file_star_rl:
                if (isStar) {
                    unstarSelectPage();
                } else {
                    starSelectPage();
                }

                break;
            case R.id.book_page_star_rl:
                showStarNote();
                break;
            case R.id.book_page_delete_rl:
                confirmDeleteDialog();

                break;

            case R.id.book_page_pdf_rl:
                namedPdf();

                break;
            case R.id.book_page_add_btn:
                openCamera();

                break;
            case R.id.book_page_file_tag_rl:
                for (String finalId : isCheckedMap.keySet()) {
                    pageIds.add(finalId);
                }
                Intent intent = new Intent(BookPageActivity.this, EditTagActivity.class);
                intent.putExtra("isPreview", false);
                intent.putStringArrayListExtra("pageIds", pageIds);
                ActivityUtils.startActivity(intent);
                pageIds.clear();
                break;

            default:
                break;
        }
    }

    /**
     * 收藏所选页
     *
     * @param
     * @return
     */

    private void starSelectPage() {

        realm.executeTransaction(new Realm.Transaction() {
            @Override
            public void execute(Realm realms) {
                for (String finalId : isCheckedMap.keySet()) {
                    //先查找后得到PageEntity对象
                    PageEntity deletePageEntity = realm.where(PageEntity.class)
                            .equalTo("pageId", finalId)
                            .equalTo("isDelete", 0)
                            .findFirst();

                    deletePageEntity.setIsStar(1);
                    deletePageEntity.setIsDirty(1);
                    String curDate = formatter.format(new Date(System.currentTimeMillis()));
                    deletePageEntity.setUpdateTime(curDate);
                }
                resetStatus();
                startAutoSync();
            }
        });
    }

    /**
     * 收藏所选页
     *
     * @param
     * @return
     */

    private void unstarSelectPage() {

        realm.executeTransaction(new Realm.Transaction() {
            @Override
            public void execute(Realm realms) {
                for (String finalId : isCheckedMap.keySet()) {
                    //先查找后得到PageEntity对象
                    PageEntity deletePageEntity = realm.where(PageEntity.class)
                            .equalTo("pageId", finalId)
                            .equalTo("isDelete", 0)
                            .findFirst();

                    deletePageEntity.setIsStar(0);
                    deletePageEntity.setIsDirty(1);
                    String curDate = formatter.format(new Date(System.currentTimeMillis()));
                    deletePageEntity.setUpdateTime(curDate);
                }
                resetStatus();
                startAutoSync();
            }
        });
    }

    /**
     * @des: 显示删除Dialog
     * @params:
     * @return:
     */

    private void confirmDeleteDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(BookPageActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.confirm_delete));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                deletePages();
                dialog.dismiss();


            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();
    }

    /**
     * @des: 保存至相册
     * @params:
     * @return:
     */

    private void copyToSd() {
        showProgressDialog();
        final String albumDirPath = userPreferences.getSdPath();
        final List<String> copyList = new ArrayList<>();
        copyList.addAll(isCheckedMap.keySet());
        new Thread(new Runnable() {
            @Override
            public void run() {
                Realm realm = Realm.getDefaultInstance();
                if (FileUtils.createOrExistsDir(albumDirPath)) {
                    for (String id : copyList) {
                        PageEntity page = realm.where(PageEntity.class)
                                .equalTo("pageId", id)
                                .equalTo("isDelete", 0)
                                .findFirst();
                        final String picUrl = page.getPicUrl();
//                        final String sdPicPath = albumDirPath + UUID.randomUUID() + CZURConstants.JPG;
                        final String sdPicPath = CZURConstants.PICTURE_PATH + UUID.randomUUID() + CZURConstants.JPG;

                        FileUtils.copy(picUrl, sdPicPath, new FileUtils.OnReplaceListener() {
                            @Override
                            public boolean onReplace(File srcFile, File destFile) {
                                return false;
                            }

                        });

                        sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.parse("file://" + sdPicPath)));
                    }
                }
                hideProgressDialog();
            }
        }).start();
        hideProgressDialog();
        BlackRightPopup.Builder builder = new BlackRightPopup.Builder(BookPageActivity.this);
        builder.setTitle(getString(R.string.saved_album));
        final BlackRightPopup blackRightPopup = builder.create();
        blackRightPopup.show();
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                blackRightPopup.dismiss();
            }
        }, 900);
        resetStatus();
        sortByOrder(orderType, isStar);

    }

    /**
     * @des: 命名PDF
     * @params:
     * @return:
     */

    private void namedPdf() {
        final CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(BookPageActivity.this, CloudCommonPopupConstants.EDT_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.input_pdf_name));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (Validator.isNotEmpty(dialogEdt.getText().toString())) {
                    //不能含有表情
                    if (EtUtils.containsEmoji(dialogEdt.getText().toString())) {
                        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(BookPageActivity.this, CloudCommonPopupConstants.COMMON_ONE_BUTTON);
                        builder.setTitle(getResources().getString(R.string.prompt));
                        builder.setMessage(getResources().getString(R.string.nickname_toast_symbol));
                        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                dialog.dismiss();
                            }
                        });
                        CloudCommonPopup commonPopup = builder.create();
                        commonPopup.show();
                    } else {
                        //文件名字重复添加括号
                        int i = 0;
                        String pdfName = dialogEdt.getText().toString();
                        String finalName = pdfName;
                        BookPdfEntity sameNamePdf = realm.where(BookPdfEntity.class)
                                .equalTo("pdfName", pdfName)
                                .equalTo("isDelete",0)
                                .findFirst();
                        while (Validator.isNotEmpty(sameNamePdf)) {
                            i++;
                            finalName = pdfName + String.format(getString(R.string.repeat_name_format), i + "");
                            sameNamePdf = realm.where(BookPdfEntity.class)
                                    .equalTo("pdfName", finalName)
                                    .equalTo("isDelete", 0)
                                    .findFirst();
                        }
                        generatePdf(finalName);
                        resetStatus();
                        sortByOrder(orderType, isStar);
                        dialog.dismiss();
                    }
                } else {
                    showMessage(R.string.pdf_rename_empty);
                }
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        commonPopup = builder.create();
        dialogEdt = (EditText) commonPopup.getWindow().findViewById(R.id.edt);
        commonPopup.show();
    }

    /**
     * @des: 申请权限并且打开相机页
     * @params:
     * @return:
     */
    private void openCamera(){
        String[] permissions = {Manifest.permission.CAMERA};
        boolean flag = isGranted(permissions);
        if (flag) { //权限开启状态
            Intent intent = new Intent(BookPageActivity.this, CameraActivity.class);
            intent.putExtra("bookId", bookId);
            intent.putExtra("noteName", noteName);
            ActivityUtils.startActivity(intent);
        } else { //权限关闭状态
            PermissionUtil.checkPermissionWithDialog(this,
                    this.getString(R.string.starry_popupwindow_title),
                    this.getString(R.string.czur_permission_camera,this.getString(R.string.book_scan_page)),
                    this.getString(R.string.starry_go_open_permission),
                    this.getString(R.string.starry_background_start_msg_cancel),
                    v -> {
                        if (v != null) { //点击去设置
                            PermissionUtil.useToolsRequestPermission(
                                    new String[]{Manifest.permission.CAMERA},
                                    () -> {
                                        Intent intent = new Intent(BookPageActivity.this, CameraActivity.class);
                                        intent.putExtra("bookId", bookId);
                                        intent.putExtra("noteName", noteName);
                                        ActivityUtils.startActivity(intent);
                                    }
                            );
                        } else { //点击取消

                        }
                    });
        }
    }

    /**
     * @des: 生成PDF
     * @params:
     * @return:
     */

    private void generatePdf(final String pdfName) {
        commonPopup.dismiss();
        paths = new ArrayList<>();
        pdfId = UUID.randomUUID().toString();
        realm.executeTransaction(new Realm.Transaction() {
            @Override
            public void execute(Realm realms) {
                for (String id : isCheckedMap.keySet()) {
                    //先查找后得到PageEntity对象
                    RealmResults<PageEntity> page = realm.where(PageEntity.class)
                            .equalTo("pageId", id)
                            .equalTo("isDelete", 0)
                            .findAll();
                    String picUrl = page.get(0).getPicUrl();
                    paths.add(picUrl);
                }

                String curDate = formatter.format(new Date(System.currentTimeMillis()));
                BookPdfEntity newPdfEntity = realm.createObject(BookPdfEntity.class, pdfId);
                newPdfEntity.setCreateTime(curDate);
                newPdfEntity.setIsDirty(1);
                newPdfEntity.setIsNewAdd(1);
                newPdfEntity.setPdfPath(bookPdfPath + pdfId + CZURConstants.PDF);
                newPdfEntity.setUpdateTime(curDate);
                newPdfEntity.setPdfName(pdfName);


            }
        });


        final GeneratePdfUtils generatePdfUtils = new GeneratePdfUtils(this, bookPdfPath, paths, pdfId);
        generatePdfUtils.setOnGeneratePdfListener(onGeneratePdfListener);
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                generatePdfUtils.createPdf();
                return null;
            }

            @Override
            public void onSuccess(Void result) {

            }
        });

    }

    private GeneratePdfUtils.OnGeneratePdfListener onGeneratePdfListener = new GeneratePdfUtils.OnGeneratePdfListener() {
        @Override
        public void onStart() {

            SavePdfPopup.Builder builder = new SavePdfPopup.Builder(BookPageActivity.this);
            builder.setTitle("");
            savePdfPopup = builder.create();
            progressTitle = (TextView) savePdfPopup.getWindow().findViewById(R.id.title);
            if (!savePdfPopup.isShowing()) {
                savePdfPopup.show();
            }

        }

        @Override
        public void onGenerate(final int progress, long time) {
            progressTitle.setText(String.format(getString(R.string.have_been_generated), progress + "", paths.size() + ""));
        }

        @Override
        public void onFinish(long totalTime, final String pdfPath, final String pdfName) {
            savePdfPopup.dismiss();
            realm.executeTransaction(new Realm.Transaction() {
                @Override
                public void execute(Realm realm) {
                    BookPdfEntity bookPdfEntity = realm.where(BookPdfEntity.class).equalTo("isDelete", 0).equalTo("pdfId", pdfId).findFirst();
                    bookPdfEntity.setPdfSize(FileUtils.getFileLength(bookPdfEntity.getPdfPath())+"");
                }
            });
            BlackRightPopup.Builder builder = new BlackRightPopup.Builder(BookPageActivity.this);
            builder.setTitle(getString(R.string.saved_pdf));
            final BlackRightPopup blackRightPopup = builder.create();
            blackRightPopup.show();
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    blackRightPopup.dismiss();
                }
            }, 900);
            startSyncNow();

            ActivityUtils.startActivity(BookPdfActivity.class);

        }
    };

    /**
     * @des: 是否显示空文件夹提示区域
     * @params:
     * @return:
     */

    private void isShowEmptyPrompt() {
        if (Validator.isEmpty(pageEntities) || pageEntities.size() <= 0) {
            bookPageRecyclerView.setVisibility(View.GONE);
            bookPageEmptyRl.setVisibility(View.VISIBLE);
        } else {
            bookPageRecyclerView.setVisibility(View.VISIBLE);
            bookPageEmptyRl.setVisibility(View.GONE);
        }
    }

    /**
     * @des: 排序返回结果
     * @params:[descending]
     * @return:void
     */
    private void sortByOrder(int orderType, boolean isStar) {
        if (isTag) {
            if (orderType == 0) {

                pageEntities = realm.where(PageEntity.class)
                        .equalTo("tagName", tagName)
                        .equalTo("isDelete", 0)
                        .findAll()
                        .sort("pageNum", Sort.ASCENDING);
            } else if (orderType == 1) {
                pageEntities = realm.where(PageEntity.class)
                        .equalTo("tagName", tagName)
                        .equalTo("isDelete", 0)
                        .findAll()
                        .sort("pageNum", Sort.DESCENDING);
            } else if (orderType == 2) {
                pageEntities = realm.where(PageEntity.class)
                        .equalTo("tagName", tagName)
                        .equalTo("isDelete", 0)
                        .findAll()
                        .sort("createTime", Sort.ASCENDING);
            } else {
                pageEntities = realm.where(PageEntity.class)
                        .equalTo("tagName", tagName)
                        .equalTo("isDelete", 0)
                        .findAll()
                        .sort("createTime", Sort.DESCENDING);
            }
        } else {
            if (orderType == 0) {

                pageEntities = realm.where(PageEntity.class)
                        .equalTo("noteName", noteName)
                        .equalTo("isDelete", 0)
                        .findAll()
                        .sort("pageNum", Sort.ASCENDING);
            } else if (orderType == 1) {
                pageEntities = realm.where(PageEntity.class)
                        .equalTo("noteName", noteName)
                        .equalTo("isDelete", 0)
                        .findAll()
                        .sort("pageNum", Sort.DESCENDING);
            } else if (orderType == 2) {
                pageEntities = realm.where(PageEntity.class)
                        .equalTo("noteName", noteName)
                        .equalTo("isDelete", 0)
                        .findAll()
                        .sort("createTime", Sort.ASCENDING);
            } else {
                pageEntities = realm.where(PageEntity.class)
                        .equalTo("noteName", noteName)
                        .equalTo("isDelete", 0)
                        .findAll()
                        .sort("createTime", Sort.DESCENDING);
            }
        }

        if (isTag) {
            deleteEntities = realm.where(PageEntity.class)
                    .equalTo("tagName", tagName)
                    .equalTo("isDelete", 1)
                    .findAll();
        } else {
            deleteEntities = realm.where(PageEntity.class)
                    .equalTo("noteName", noteName)
                    .equalTo("isDelete", 1)
                    .findAll();
        }

        for (PageEntity deleteEntity : deleteEntities) {
            if (isCheckedMap.containsKey(deleteEntity.getPageId())) {
                //没选中时移除
                isCheckedMap.remove(deleteEntity.getPageId());
            }
        }
        if (isCheckedMap.size() == 1) {
            bookPageTitle.setText(R.string.select_one);
            showAll();
        } else if (isCheckedMap.size() > 1) {
            bookPageTitle.setText(String.format(getString(R.string.select_num), isCheckedMap.size() + ""));
            showAll();
        } else {
            darkAll();
            if (pageEntities.size() <= 0 && isRealmRefresh) {
                resetStatus();
                bookPageTitle.setText("");
                bookPageAdapter.refreshData(pageEntities, false, isCheckedMap);
                isShowEmptyPrompt();
                return;
            }
            if (isMultiSelect) {
                bookPageTitle.setText(String.format(getString(R.string.select_num), isCheckedMap.size() + ""));
            }

        }
        checkSize(isCheckedMap, bookPageAdapter.getTotalSize());


        bookPageAdapter.refreshData(pageEntities, isMultiSelect, isCheckedMap);
        isShowEmptyPrompt();
        isRealmRefresh = false;
    }


    private BookPageSheetBottomDialog.OnPageBottomClickListener onPageBottomClickListener = new BookPageSheetBottomDialog.OnPageBottomClickListener() {
        @Override
        public void onItemClick(int viewId) {
            switch (viewId) {
                case R.id.book_page_correct_order_ll:
                    orderType = 0;
                    bottomDialog.dismiss();
                    resetStatus();
                    sortByOrder(orderType, isStar);

                    break;
                case R.id.book_page_inverted_order_ll:
                    orderType = 1;
                    bottomDialog.dismiss();
                    resetStatus();
                    sortByOrder(orderType, isStar);
                    break;
                case R.id.book_page_correct_time_order_ll:
                    orderType = 2;
                    bottomDialog.dismiss();
                    resetStatus();
                    sortByOrder(orderType, isStar);
                    break;
                case R.id.book_page_inverted_time_order_ll:
                    orderType = 3;
                    bottomDialog.dismiss();
                    resetStatus();
                    sortByOrder(orderType, isStar);
                    break;
                case R.id.book_page_dialog_cancel_btn:
                    bottomDialog.dismiss();
                    break;

                default:
                    break;
            }
        }
    };

    @Override
    protected void onDestroy() {
        super.onDestroy();
        realm.removeChangeListener(realmChangeListener);
        realm.close();

//        removeStickyEvent();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }


    private void showAll() {
        showPdf();
        showStar();
        showSave();
        showDelete();
        showTag();
    }

    private void darkAll() {
        darkDelete();
        darkPdf();
        darkSave();
        darkStar();
        darkTag();

    }

    private void showSave() {
        bookPageSaveAlbumRl.setClickable(true);
        bookPageSaveAlbumRl.setEnabled(true);

        bookPageSaveAlbumImg.setSelected(true);
        bookPageSaveAlbumTv.setTextColor(getResources().getColor(R.color.white));
    }

    private void darkSave() {
        bookPageSaveAlbumRl.setClickable(false);
        bookPageSaveAlbumRl.setEnabled(false);

        bookPageSaveAlbumImg.setSelected(false);
        bookPageSaveAlbumTv.setTextColor(getResources().getColor(R.color.dark_text));
    }

    private void showDelete() {
        bookPageDeleteRl.setClickable(true);
        bookPageDeleteRl.setEnabled(true);

        bookPageDeleteImg.setSelected(true);
        bookPageDeleteTv.setTextColor(getResources().getColor(R.color.white));
    }

    private void darkDelete() {
        bookPageDeleteRl.setClickable(false);
        bookPageDeleteRl.setEnabled(false);


        bookPageDeleteImg.setSelected(false);
        bookPageDeleteTv.setTextColor(getResources().getColor(R.color.dark_text));
    }

    private void showStar() {
        pageFileStarRl.setClickable(true);
        pageFileStarRl.setEnabled(true);


        pagePreviewStarImg.setSelected(true);
        pagePreviewStarTv.setTextColor(getResources().getColor(R.color.white));
    }

    private void darkStar() {
        pageFileStarRl.setClickable(false);
        pageFileStarRl.setEnabled(false);

        pagePreviewStarImg.setSelected(false);
        pagePreviewStarTv.setTextColor(getResources().getColor(R.color.dark_text));
    }


    private void showTag() {
        bookPageFileTagRl.setClickable(true);
        bookPageFileTagRl.setEnabled(true);


        pagePreviewTagImg.setSelected(true);
        pagePreviewTagTv.setTextColor(getResources().getColor(R.color.white));
    }

    private void darkTag() {
        bookPageFileTagRl.setClickable(false);
        bookPageFileTagRl.setEnabled(false);

        pagePreviewTagImg.setSelected(false);
        pagePreviewTagTv.setTextColor(getResources().getColor(R.color.dark_text));
    }

    private void showPdf() {
        bookPagePdfRl.setClickable(true);
        bookPagePdfRl.setEnabled(true);

        bookPagePdfImg.setSelected(true);
        bookPagePdfTv.setTextColor(getResources().getColor(R.color.white));
    }

    private void darkPdf() {
        bookPagePdfRl.setClickable(false);
        bookPagePdfRl.setEnabled(false);

        bookPagePdfImg.setSelected(false);
        bookPagePdfTv.setTextColor(getResources().getColor(R.color.dark_text));
    }

}
