package com.czur.cloud.ui.starry.meeting.network;

import com.czur.cloud.BuildConfig;
import com.czur.cloud.ui.starry.meeting.network.core.AsyncHttpTask;
import com.czur.cloud.ui.starry.meeting.network.core.MiaoHttpBody;
import com.czur.cloud.ui.starry.meeting.network.core.MiaoHttpEntity;
import com.czur.cloud.ui.starry.meeting.network.core.MiaoHttpGet;
import com.czur.cloud.ui.starry.meeting.network.core.MiaoHttpHeader;
import com.czur.cloud.ui.starry.meeting.network.core.MiaoHttpMethod;
import com.czur.cloud.ui.starry.meeting.network.core.MiaoHttpParam;
import com.czur.cloud.ui.starry.meeting.network.core.MiaoHttpPath;
import com.czur.cloud.ui.starry.meeting.network.core.MiaoHttpPost;
import com.czur.cloud.ui.starry.meeting.network.core.SyncHttpTask;
import com.czur.cloud.ui.starry.meeting.network.core.interceptor.CommonParamInterceptor;
import com.czur.cloud.ui.starry.meeting.network.core.interceptor.LoggingInterceptor;
import com.czur.cloud.ui.starry.meeting.network.core.interceptor.TokenInterceptor;
import com.google.gson.Gson;

import java.lang.annotation.Annotation;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.lang.reflect.Type;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;

public class MiaoHttpManager {

    public static final int STATUS_FAIL = 1001;
    public static final int STATUS_TIMEOUT = 1002;
    public static final int STATUS_NOT_MANAGER = 1003;
    public static final int STATUS_SUCCESS = 1000;

    private static final int METHOD_NONE = 0;
    private static final int METHOD_GET = 1;
    private static final int METHOD_POST = 2;

    private ExecutorService executor = Executors.newFixedThreadPool(5);
    private OkHttpClient client;

    private MiaoHttpManager() {
    }

    public static MiaoHttpManager getInstance() {
        return SingletonHolder.instance;
    }

    private static class SingletonHolder {
        static final MiaoHttpManager instance = new MiaoHttpManager();
    }

    public void init() {
        long timeout = 20L;
        if (BuildConfig.IS_OVERSEAS){
            timeout = 60L;
        }
        client = new OkHttpClient.Builder()
//                .sslSocketFactory(getCertificates(), systemDefaultTrustManager())
                .connectTimeout(timeout, TimeUnit.SECONDS)
                .readTimeout(timeout, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .addInterceptor(new TokenInterceptor())     // Token过期后 刷新Token
//                .addInterceptor(new ActiveInterceptor())    // 激活检测
                .addInterceptor(new CommonParamInterceptor()) // 通用请求参数(请求头和必须参数)
//                .addInterceptor(new SignatureInterceptor()) // 签名
                .addInterceptor(new LoggingInterceptor())   // 输出Log
                .build();
    }

    public <T> T create(Class<T> service, String endpoint) {
        return (T) Proxy.newProxyInstance(service.getClassLoader(), new Class<?>[]{service}, new HttpHandler<T>(endpoint));
    }

    private class HttpHandler<T> implements InvocationHandler {

        private String endpoint;

        HttpHandler(String endpoint) {
            this.endpoint = endpoint;
        }

        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
//            logI("HttpHandler.invoke.proxy=",
//                    "method=",method,
//                    "args=",args);
            if (method.getDeclaringClass() == Object.class) {
                return method.invoke(this, args);
            }

            if (args == null || args.length == 0) {
                throw new Exception("方法参数为空或者规则不正确!");
            }

            Annotation[][] paramAnnotationArr;
            MiaoHttpMethod miaoHttpMethod;
//            MiaoHttpMethod miaoHttpMethod = methodCache.get(method.getName());
//            if (miaoHttpMethod == null) {
            miaoHttpMethod = new MiaoHttpMethod();
            miaoHttpMethod.setIsAsync(args[args.length - 1] instanceof Callback);
            miaoHttpMethod.setNoParamCount(miaoHttpMethod.isAsync() ? 2 : 1);
            paramAnnotationArr = method.getParameterAnnotations();
            String body = null;
            int bodyAnnotationIndex = -1;
            for (int i = 0; i < paramAnnotationArr.length - miaoHttpMethod.getNoParamCount(); i++) {
                Annotation tempAnnotation = paramAnnotationArr[i][0];
                if (tempAnnotation instanceof MiaoHttpParam) {
                    miaoHttpMethod.getParams().put(((MiaoHttpParam) tempAnnotation), i + "");
                } else if (tempAnnotation instanceof MiaoHttpHeader) {
                    miaoHttpMethod.getHeaders().put(((MiaoHttpHeader) tempAnnotation).value(), i + "");
                } else if (tempAnnotation instanceof MiaoHttpPath) {
                    miaoHttpMethod.getPaths().put(((MiaoHttpPath) tempAnnotation).value(), i + "");
                } else if (tempAnnotation instanceof MiaoHttpBody) {
                    bodyAnnotationIndex = i;
                }
            }
            Annotation[] annotations = method.getAnnotations();
            int witchMethod = METHOD_NONE;
            Class<?> clazz;
            MiaoHttpGet miaoGet = null;
            MiaoHttpPost miaoPost = null;
            for (Annotation annotation : annotations) {
                clazz = annotation.annotationType();
                if (clazz == MiaoHttpGet.class) {
                    witchMethod = METHOD_GET;
                    miaoGet = (MiaoHttpGet) annotation;
                } else if (clazz == MiaoHttpPost.class) {
                    witchMethod = METHOD_POST;
                    miaoPost = (MiaoHttpPost) annotation;
                }
            }
            if (witchMethod == METHOD_NONE) {
                throw new Exception("方法上面说好的注解呢?MiaoGet或者MiaoPost什么的?");
            }
            if (bodyAnnotationIndex != -1 && witchMethod != METHOD_POST) {
                throw new Exception("带http body的方法必须是post请求");
            }

            switch (witchMethod) {
                case METHOD_GET:
                    miaoHttpMethod.setMethod(MiaoHttpMethod.Method.Get);
                    miaoHttpMethod.setUrl(endpoint + miaoGet.value());
                    break;
                case METHOD_POST:
                    miaoHttpMethod.setMethod(MiaoHttpMethod.Method.Post);
                    miaoHttpMethod.setUrl(endpoint + miaoPost.value());
                    break;
            }


            HashMap<String, String> postParam = new HashMap<>();
            StringBuilder url = new StringBuilder();
            Gson gson = new Gson();

            // 生成参数列表
            for (Map.Entry<MiaoHttpParam, String> entry : miaoHttpMethod.getParams().entrySet()) {
                // 处理集合
                Object arg = args[Integer.parseInt(entry.getValue())];
                String value = "";
                if (entry.getKey().json()) {
                    // 处理Json数据
                    value = gson.toJson(arg);
                } else {
                    value = String.valueOf(arg);
                }
                if (arg instanceof Collection) {
                    // 去除中括号
                    value = arg.toString().substring(1, value.length() - 1);
                }
                if (arg == null) {
                    // 参数为空, 不添加该参数
                    continue;
                }
                postParam.put(entry.getKey().value(), value);
            }


            if (bodyAnnotationIndex != -1) {
                body = (String) args[bodyAnnotationIndex];
            }

            String baseUrl = miaoHttpMethod.getUrl();
            for (Map.Entry<String, String> entry : miaoHttpMethod.getPaths().entrySet()) {
                String value = (String) args[Integer.parseInt(entry.getValue())];
                if (value == null) {
                    value = "";
                }
                baseUrl = baseUrl.replace("{" + entry.getKey() + "}", value);
            }
            miaoHttpMethod.setUrl(baseUrl + url);
            if (miaoHttpMethod.getHeaders().size() != 0) {
                for (Map.Entry<String, String> entry : miaoHttpMethod.getHeaders().entrySet()) {
                    String value = (String) args[Integer.parseInt(entry.getValue())];
                    if (value == null) {
                        value = "";
                    }
                    miaoHttpMethod.getHeaders().put(entry.getKey(), value);
                }
            }


            if (miaoHttpMethod.isAsync()) {
                // 异步请求
                final Callback<T> callback = (Callback<T>) args[args.length - 1];
                Type type = (Type) args[args.length - 2];
                if (MiaoHttpMethod.Method.Get.equals(miaoHttpMethod.getMethod())) {
                    executor.execute(new AsyncHttpTask<>(miaoHttpMethod.getUrl(), type, callback, miaoHttpMethod.getHeaders(), postParam));
                } else {
                    executor.execute(new AsyncHttpTask<>(miaoHttpMethod.getUrl(), type, postParam, callback, miaoHttpMethod.getHeaders(), body));
                }
            } else {
                // 同步请求
                Type type = (Type) args[args.length - 1];
                if (MiaoHttpMethod.Method.Get.equals(miaoHttpMethod.getMethod())) {
                    // get请求
                    return SyncHttpTask.getInstance().syncGet(miaoHttpMethod.getUrl(), type, miaoHttpMethod.getHeaders(), postParam);
                } else {
                    // post请求
                    return SyncHttpTask.getInstance().syncPost(miaoHttpMethod.getUrl(), type, postParam, miaoHttpMethod.getHeaders(), body);
                }
            }
            return null;
        }
    }

    public interface Callback<T> {
        void onResponse(MiaoHttpEntity<T> entity);

        void onFailure(MiaoHttpEntity<T> entity);

        void onError(Exception e);
    }


    public OkHttpClient getHttpClient() {
        if (client == null) {
            synchronized (MiaoHttpManager.class) {
                if (client == null) {
                    init();
                }
            }
        }
        return client;
    }
}
