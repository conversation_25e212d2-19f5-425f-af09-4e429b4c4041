package com.czur.cloud.ui.starry.model

import com.czur.cloud.ui.starry.api.ApiException

data class StarryResultData<T>(
    val code: Int,
    val data: T,
    val msg: String
){
    companion object{
        const val CODE_SUCCESS = 1000

    }

    fun apiData(): T{
        // 如果是成功，则返回数据； 否则就抛出异常
        if (code == CODE_SUCCESS){
            return data
        }else{
            throw ApiException(code, msg)
        }
    }

}
