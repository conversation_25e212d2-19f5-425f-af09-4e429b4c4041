package com.czur.cloud.ui.component.popup;

import android.app.Dialog;
import android.content.Context;
import android.os.Build;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.czur.cloud.R;

/**
 * Created by <PERSON> on 2021-01-09
 */

public class SaveHdViewPopupDialog extends Dialog {
    public static final float DIMMED_OPACITY = 0.5f;
    private static TextView tv1,tv2;
    private static ImageView img;

    public SaveHdViewPopupDialog(Context context) {
        super(context);
    }

    public SaveHdViewPopupDialog(Context context, int theme) {
        super(context, theme);
    }


    public static class Builder {
        private Context context;

        public Builder(Context context) {
            this.context = context;

        }

        public SaveHdViewPopupDialog create() {
            LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);

            final SaveHdViewPopupDialog dialog = new SaveHdViewPopupDialog(context, R.style.TransparentProgressDialog);
            View layout = commonCustomPopLayout(inflater, dialog);

            dialog.setContentView(layout);
            dialog.setCanceledOnTouchOutside(true);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            params.dimAmount = DIMMED_OPACITY;

            return dialog;
        }

        private View commonCustomPopLayout(LayoutInflater inflater, final SaveHdViewPopupDialog dialog) {
            WindowManager.LayoutParams lp = dialog.getWindow().getAttributes();
            lp.dimAmount = 0.5f;
            dialog.getWindow().setAttributes(lp);
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

            View layout = inflater.inflate(R.layout.hdview_save_success_dialog, null, false);

            tv1 = layout.findViewById(R.id.tv1);
            tv2 = layout.findViewById(R.id.tv2);
            img = layout.findViewById(R.id.img);

            ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);
            dialog.addContentView(layout, params);

            return layout;
        }
    }


    private void fullScreenImmersive(View view) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            int uiOptions = View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_FULLSCREEN;
            view.setSystemUiVisibility(uiOptions);
        }
    }

    //type=1:sucess;
    //type=0:failed
    public void show(String msg1, String msg2, int type){
        if (type == 0){
            img.setImageResource(R.mipmap.hd_save_failed_icon);
        }
        show(msg1, msg2);
    }

    public void show(String msg, int type){
        if (type == 0){
            img.setImageResource(R.mipmap.hd_save_failed_icon);
        }
        show(msg);
    }

    public void show(String msg1){
        tv1.setText(msg1);
        show();
    }

    public void show(String msg1, String msg2){
        tv1.setText(msg1);
        tv2.setText(msg2);
        show();
    }

    @Override
    public void show() {
        this.getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
        super.show();
        fullScreenImmersive(getWindow().getDecorView());
        this.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
    }

}
