package com.czur.cloud.ui.starry.meeting.widget

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import androidx.lifecycle.LiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.findViewTreeLifecycleOwner
import com.czur.cloud.R
import kotlin.math.min
import kotlin.math.sqrt

/**
 * Created by 陈丰尧 on 2021/6/4
 */

private const val MIN_VOLUME = 0
private const val MAX_VOLUME = 255

private const val BG_COLOR_DEF = "#FF4081"  //"#00222222"
private const val VOLUME_COLOR_DEF = "#45C5FC"

private const val TAG = "VolumeView"

class VolumeView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {

    private var scaleWidth = 1.0f
    private var scaleHeight = 1.0f

    // 是否会绘制话筒的白色背景？
    private var isDrawSrcBmp = true

    private val srcBmp by lazy {
        zoomBitmap()
    }

    private fun zoomBitmap(): Bitmap {
        val options = BitmapFactory.Options()
        val bitmap = BitmapFactory.decodeResource(resources, R.mipmap.starry_meeting_ic_tab_mic_on_bg_white, options)
        scaleWidth = this.width.toFloat()/bitmap.width
        scaleHeight = this.height.toFloat()/bitmap.height
        val matrix = Matrix()
        matrix.postScale(scaleWidth, scaleHeight)
//        Log.i(TAG, "bitmap：ByteCount = " + bitmap.byteCount + ":::bitmap：AllocationByteCount = " + bitmap.allocationByteCount)
//        Log.i(TAG, "this.width:" + this.width + ":::this.height:" + this.height)
//        Log.i(TAG, "bitmap.width:" + bitmap.width + ":::bitmap.height:" + bitmap.height)
//        Log.i(TAG, "scaleWidth=$scaleWidth:::scaleHeight = $scaleHeight")
//        Log.i(TAG, "inDensity:" + options.inDensity + ":::inTargetDensity:" + options.inTargetDensity)
//        Log.i(TAG, "===========================================================================")
        val scaleBitmap: Bitmap =
            Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
        bitmap.recycle()
        return scaleBitmap
    }

    private val paint = Paint()
    private val bgColor = Color.parseColor(BG_COLOR_DEF)
    private val volumeColor = Color.parseColor(VOLUME_COLOR_DEF)

    private var volume: LiveData<Int>? = null
    private var showVolume = 0

    private val volumeObservable = Observer<Int> { startAnim() }

    private val lifecycleOwner by lazy {
        findViewTreeLifecycleOwner()!!
    }


    private val anim: ValueAnimator by lazy {
        initAnim()
    }

    private val volumeBgRect: RectF by lazy {
        initVolumeRect()
    }

    override fun draw(canvas: Canvas) {
//        Log.i(TAG, "========VolumeView.draw.isDrawSrcBmp=${isDrawSrcBmp}===============================================================")
        super.draw(canvas)
        val layerID = canvas.saveLayer(
            0f,
            0f,
            (width).toFloat(),
            (height).toFloat(),
            paint,
            Canvas.ALL_SAVE_FLAG
        )

        if (isDrawSrcBmp){
            drawBG(canvas)// 绘制背景
            drawVolume(canvas)
        }

        paint.xfermode = null
        canvas.restoreToCount(layerID)
    }


    // 绘制音量条
    private fun drawVolume(canvas: Canvas) {
        paint.color = volumeColor
        paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN);

        canvas.drawRect(
            volumeBgRect.left,
            volumeBgRect.top + getVolumeOffset(),
            volumeBgRect.right,
            volumeBgRect.bottom,
            paint
        )
    }

    private fun drawBGNo(canvas: Canvas) {
        paint.color = bgColor
        val r = RectF(0f, 0f, 0f, 0f)
        canvas.drawRect(r, paint)
    }

    private fun drawBG(canvas: Canvas) {
        paint.color = bgColor
        canvas.drawBitmap(srcBmp, 0f, 0f, paint)
    }

    private fun initVolumeRect(): RectF {
        val hScale = 0.68f
        val currentX = width*1.0f
        val currentY = height*hScale
        val bian = 4.0f  * scaleHeight
//        Log.i("VolumeView","width=${width},height=${height}," +
//                "left=${0}," +
//                "top=${bian}," +
//                "right=${currentX}," +
//                "bottom=${currentY }")

        return RectF(
            0f,
            bian,
            currentX ,
            currentY
        )
    }

    private fun initVolumeRect123(): RectF {
        val edge = min(width, height)
        val bgEdge = edge * sqrt(2.0) / 2
        val centerX = width / 2f
        val centerY = height / 2f

        val halfBg = (bgEdge / 2f).toFloat()

        return RectF(
            centerX - halfBg + 1, // x轴稍微向内缩一点,防止漏出来
            centerY - halfBg,
            centerX + halfBg - 1,
            centerY + halfBg
        )
    }

    /**
     * 初始化音量变动时的公话
     */
    private fun initAnim(): ValueAnimator {
        val anim = ValueAnimator.ofInt(0)
            // 因为声网的sdk是200ms更新一次, 所以音量动画为400ms
            // 由当前值->设定值(200ms),由设定值->0(200ms)
            .setDuration(400)
        anim.addUpdateListener(::animListener)
        return anim
    }

    /**
     * 设置音量数据的liveData
     * @param volumeData: 音量数据的LiveData
     */
    fun setVolumeData(volumeData: LiveData<Int>) {
//        Log.i(TAG, "========VolumeView.setVolumeData.volumeData=${volumeData.value}===============================================================")
        isDrawSrcBmp = true

        if (volumeData === volume) {
            // 如果设定的liveData和目前已经存在的liveData是同一个对象,
            // 就不需要做任何操作
            return
        }
        // 移除的时候, 只移除observable对象,防止影响其他View
        volume?.removeObserver(volumeObservable)

        volumeData.observe(lifecycleOwner, volumeObservable)
        volume = volumeData
    }

    fun removeVolumeData() {
        isDrawSrcBmp = false

        volume?.removeObserver(volumeObservable)
        volume = null
    }

    /**
     * 开始动画
     */
    private fun startAnim() {
        if (anim.isRunning) {
            anim.cancel()
        }
        val newVolume = getVolume()
//        Log.i(TAG, "newVolume=${newVolume}")

        if (showVolume == 0 && newVolume == 0) {
            // 都是0就不放动画了
            return
        }
        // 如果没有更新数据, 音量就会自动回归到0
        anim.setIntValues(showVolume, newVolume, 0)
        anim.start()
    }

    /**
     * 动画数据改变的监听
     * 数据改变时 刷新页面
     */
    private fun animListener(animation: ValueAnimator) {
        val current = showVolume
        showVolume = animation.animatedValue as Int
        if (current != showVolume) {
            invalidate()
        }
    }

    /**
     * 根据音量大小, 转化为音频条的长度
     */
    private fun getVolumeOffset(): Float {
        val p = showVolume.toFloat() / MAX_VOLUME
        return volumeBgRect.height() - (p * volumeBgRect.height())
    }


    /**
     * 获取当前的音量值,范围是在[MIN_VOLUME]~[MAX_VOLUME]
     */
    private fun getVolume(): Int {
        val volumeData = volume?.value ?: 0

        return when {
            volumeData < MIN_VOLUME -> MIN_VOLUME
            volumeData > MAX_VOLUME -> MAX_VOLUME
            else -> volumeData
        }
    }


    override fun onVisibilityAggregated(isVisible: Boolean) {
        super.onVisibilityAggregated(isVisible)
        // 当View不可见的时候, 不去监听LiveData
        if (isVisible) {
            volume?.observe(lifecycleOwner, volumeObservable)
        } else {
            volume?.removeObserver(volumeObservable)
        }
    }
}


