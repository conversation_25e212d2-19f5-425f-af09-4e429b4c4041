package com.czur.cloud.ui.et;

import android.content.DialogInterface;
import android.os.Bundle;
import android.view.View;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.DeleteUserAdapter;
import com.czur.cloud.event.DeleteShareUserEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.model.UserInfoModel;
import com.czur.cloud.model.UserShareModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.util.EtUtils;
import com.google.gson.reflect.TypeToken;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * Created by Yz on 2018/6/1.
 * Email：<EMAIL>
 */
public class EtDeleteShareUserActivity extends BaseActivity implements View.OnClickListener {
    private ImageView etDeleteShareUserBackBtn;
    private TextView etDeleteShareUserTitle;
    private TextView etDeleteShareUserFinishBtn;
    private RecyclerView etDeleteShareUserRecyclerView;
    private List<UserShareModel> userShareModels;
    private DeleteUserAdapter deleteUserAdapter;
    private LinkedHashMap<String, Boolean> isCheckedMap;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private String deviceId;
    private List<String> userIds;
    private CloudCommonPopup commonPopup;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_delete_share_user);
        initComponent();
        registerEvent();
        initRecyclerView();
        getShareUser();

    }

    private void registerEvent() {
        etDeleteShareUserBackBtn.setOnClickListener(this);
        etDeleteShareUserFinishBtn.setOnClickListener(this);
    }


    private void initComponent() {

        httpManager = HttpManager.getInstance();
        userPreferences = UserPreferences.getInstance(this);
        deviceId = getIntent().getStringExtra("deviceId");
        etDeleteShareUserBackBtn = (ImageView) findViewById(R.id.et_delete_share_user_back_btn);
        etDeleteShareUserTitle = (TextView) findViewById(R.id.et_delete_share_user_title);
        etDeleteShareUserFinishBtn = (TextView) findViewById(R.id.et_delete_share_user_finish_btn);
        etDeleteShareUserRecyclerView = (RecyclerView) findViewById(R.id.et_delete_share_user_recyclerView);
        etDeleteShareUserTitle.setText(R.string.et_delete_share_title);

    }

    /**
     * @des: 初始化列表
     * @params:
     * @return:
     */

    private void initRecyclerView() {
        userIds = new ArrayList<>();
        userShareModels = new ArrayList<>();
        isCheckedMap = new LinkedHashMap<>();
        deleteUserAdapter = new DeleteUserAdapter(this, userShareModels);
        deleteUserAdapter.setOnItemClickListener(onItemClickListener);
        deleteUserAdapter.setOnItemCheckListener(onItemCheckListener);
        etDeleteShareUserRecyclerView.setHasFixedSize(true);
        etDeleteShareUserRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        etDeleteShareUserRecyclerView.setAdapter(deleteUserAdapter);

    }

    private DeleteUserAdapter.onItemClickListener onItemClickListener = new DeleteUserAdapter.onItemClickListener() {
        @Override
        public void onItemClick(int position, UserShareModel UserShareModel, CheckBox checkBox) {
            checkBox.setChecked(!checkBox.isChecked());
        }
    };


    private DeleteUserAdapter.OnItemCheckListener onItemCheckListener = new DeleteUserAdapter.OnItemCheckListener() {
        @Override
        public void onItemCheck(int position, UserShareModel userShareModel, boolean isCheck, LinkedHashMap<String, Boolean> isCheckedMap, int totalSize) {
            EtDeleteShareUserActivity.this.isCheckedMap = isCheckedMap;
            if (isCheck) {
                userIds.add(userShareModel.getUserId());
            } else {
                userIds.remove(userShareModel.getUserId());
            }
            //如果选中一个 文案变为已选中1个
            if (isCheckedMap.size() <= 0) {
                etDeleteShareUserFinishBtn.setVisibility(View.GONE);
            } else {
                etDeleteShareUserFinishBtn.setText(String.format(getString(R.string.et_delete_share_finish), isCheckedMap.size() + ""));
                etDeleteShareUserFinishBtn.setVisibility(View.VISIBLE);
            }
        }
    };


    private void deleteShareUser() {
        httpManager.request().deleteShareUsers(userPreferences.getUserId(), deviceId, EtUtils.transFiles(userIds), new TypeToken<List<UserInfoModel>>() {
        }.getType(), new MiaoHttpManager.Callback<UserInfoModel>() {
            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<UserInfoModel> entity) {
                hideProgressDialog();
                EventBus.getDefault().post(new DeleteShareUserEvent(EventType.DELETE_SHARE_USER));
                ActivityUtils.finishActivity(EtDeleteShareUserActivity.this);
            }

            @Override
            public void onFailure(MiaoHttpEntity<UserInfoModel> entity) {
                hideProgressDialog();
                if (entity.getCode() == MiaoHttpManager.STATUS_THIS_PEOPLE_IS_USING) {
                    showMessage(R.string.toast_device_this_people_is_using);
                } else {
                    showMessage(R.string.toast_server_error);
                }

            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
                showMessage(R.string.toast_server_error);
            }
        });

    }

    /**
     * @des:获取分享用户列表
     * @params:
     * @return:
     */

    private void getShareUser() {
        httpManager.request().getShareUser(deviceId, userPreferences.getUserId(), new TypeToken<List<UserShareModel>>() {
        }.getType(), new MiaoHttpManager.Callback<UserShareModel>() {
            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<UserShareModel> entity) {
                hideProgressDialog();
                deleteUserAdapter.refreshData(entity.getBodyList());

            }

            @Override
            public void onFailure(MiaoHttpEntity<UserShareModel> entity) {
                hideProgressDialog();
                if (entity.getCode() == MiaoHttpManager.STATUS_NOT_MANAGER) {
                    showMessage(R.string.toast_not_manager_jurisdiction);

                }
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
                showMessage(R.string.toast_server_error);
            }
        });
    }

    /**
     * @des:确认是否删除
     * @params:
     * @return:
     */

    private void showConfirmDeleteDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(EtDeleteShareUserActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.confirm_delete));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {

                if (commonPopup != null) {
                    commonPopup.dismiss();
                }
                deleteShareUser();
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        commonPopup = builder.create();
        commonPopup.show();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            case R.id.et_delete_share_user_back_btn:
                finish();
                break;


            case R.id.et_delete_share_user_finish_btn:
                showConfirmDeleteDialog();

                break;

            default:
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }
}
