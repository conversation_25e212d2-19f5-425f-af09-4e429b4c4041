package com.czur.cloud.adapter;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.czur.cloud.R;
import com.czur.cloud.entity.realm.BookEntity;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * Created by Yz on 2018/3/16
 * Email：<EMAIL>
 */


public class BookAdapter extends RecyclerView.Adapter<ViewHolder> {


    private static final int ITEM_TYPE_NORMA = 0;
    private static final int ITEM_TYPE_ADD = 1;
    private static final int ITEM_TYPE_EMPTY = 2;
    private static final int ITEM_TYPE_FOOTER = 3;
    private Activity mActivity;
    //当前需要显示的所有的图片数据
    private List<BookEntity> datas;
    //是否进入选择
    private boolean isSelectItem;
    //行数
    private int rows;
    private LayoutInflater mInflater;
    private int emptyItemCounts;
    private int shouldShowItemCounts;
    private long currentClickTime=0;

    private LinkedHashMap<String, Boolean> isCheckedMap = new LinkedHashMap<>();

    /**
     * 构造方法
     */
    public BookAdapter(Activity activity, List<BookEntity> datas, boolean isSelectItem, int rows) {

        this.mActivity = activity;
        this.isSelectItem = isSelectItem;
        this.rows = rows;
        this.datas = datas;

        shouldShowItemCounts = rows * 3;
        if (shouldShowItemCounts > datas.size()) {
            emptyItemCounts = shouldShowItemCounts - datas.size() - 1;
        } else {
            emptyItemCounts = 3 - (datas.size() % 3) - 1;
        }

        mInflater = LayoutInflater.from(activity);
    }

    public void refreshData(List<BookEntity> books, int rows, boolean isSelectItem, LinkedHashMap<String, Boolean> isCheckedMap) {
        this.rows = rows;
        this.isSelectItem = isSelectItem;
        this.datas = books;
        this.isCheckedMap = isCheckedMap;
        if (rows != -1) {
            shouldShowItemCounts = rows * 3;
            if (shouldShowItemCounts > datas.size()) {
                emptyItemCounts = shouldShowItemCounts - datas.size() - 1;
            } else {
                emptyItemCounts = 3 - (datas.size() % 3) - 1;
            }
        }
        notifyDataSetChanged();

    }

    public void refreshData(boolean isSelectItem) {
        this.isSelectItem = isSelectItem;
        notifyDataSetChanged();
    }


    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        if (viewType == ITEM_TYPE_NORMA) {
            return new BooksViewHolder(mInflater.inflate(R.layout.item_book_shelf_list_item, parent, false));
        } else if (viewType == ITEM_TYPE_ADD) {
            return new AddBooksHolder(mInflater.inflate(R.layout.item_add_book_shelf_list_item, parent, false));
        } else if (viewType == ITEM_TYPE_EMPTY) {
            return new EmptyHolder(mInflater.inflate(R.layout.item_empty_book_shelf_list_item, parent, false));
        } else {
            return new FooterHolder(mInflater.inflate(R.layout.item_footer_book_shelf, parent, false));
        }
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, final int position) {

        if (holder instanceof BooksViewHolder) {
            final BooksViewHolder mHolder = (BooksViewHolder) holder;
            mHolder.mItem = datas.get(position);
            mHolder.itemNameTv.setText(mHolder.mItem.getBookName());
            if (isSelectItem) {
                mHolder.checkBox.setVisibility(View.VISIBLE);
                mHolder.checkBox.setTag(mHolder.mItem.getBookId());
            } else {
                mHolder.checkBox.setVisibility(View.GONE);
            }
            mHolder.checkBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    if (isChecked) {
                        mHolder.shadow.setVisibility(View.VISIBLE);
                        if (!isCheckedMap.containsKey(mHolder.checkBox.getTag())) {
                            //选中时添加
                            isCheckedMap.put(mHolder.mItem.getBookId(), true);
                        }
                    } else {
                        mHolder.shadow.setVisibility(View.GONE);
                        if (isCheckedMap.containsKey(mHolder.checkBox.getTag())) {
                            //没选中时移除
                            isCheckedMap.remove(mHolder.mItem.getBookId());
                        }
                    }
                    if (onItemCheckListener != null) {
                        onItemCheckListener.onItemCheck(position, mHolder.mItem, isCheckedMap, datas.size());
                    }
                }
            });

            if (isCheckedMap != null) {
                mHolder.checkBox.setChecked(isCheckedMap.containsKey(mHolder.mItem.getBookId()) ? true : false);
            } else {
                mHolder.checkBox.setChecked(false);
            }

            mHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onBookItemClickListener != null) {
                        if (System.currentTimeMillis()-currentClickTime>500){
                            onBookItemClickListener.onBookEntityClick(mHolder.mItem, position, mHolder.checkBox);
                            currentClickTime=System.currentTimeMillis();
                        }

                    }

                }
            });

//            mHolder.itemView.setOnLongClickListener(new View.OnLongClickListener() {
//                @Override
//                public boolean onLongClick(View v) {
//                    if (!isSelectItem) {
//                        mHolder.checkBox.setChecked(true);
//                        isCheckedMap.put(mHolder.mItem.getBookId(), true);
//                        if (onBookItemLongClickListener != null) {
//                            onBookItemLongClickListener.onBookEntityLongClick(position, mHolder.mItem, isCheckedMap, datas.size());
//                        }
//                        return false;
//                    }
//                    return true;
//
//                }
//            });


        } else if (holder instanceof AddBooksHolder) {
            final AddBooksHolder mHolder = (AddBooksHolder) holder;
            if (isSelectItem) {
                mHolder.bookAddBg.setVisibility(View.GONE);
                mHolder.bookAddItem.setClickable(false);
                mHolder.bookAddItem.setEnabled(false);

            } else {
                mHolder.bookAddBg.setVisibility(View.VISIBLE);
                mHolder.bookAddItem.setClickable(true);
                mHolder.bookAddItem.setEnabled(true);
            }
            mHolder.bookAddItem.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (addBookClickListener != null) {
                        addBookClickListener.onAddBookClick(position);
                    }
                }
            });


        } else if (holder instanceof EmptyHolder) {
            final EmptyHolder mHolder = (EmptyHolder) holder;
            mHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    return;
                }
            });
        } else if (holder instanceof FooterHolder) {
            final FooterHolder mHolder = (FooterHolder) holder;

        }

    }

    public int getTotalSize() {
        return datas.size();
    }


    @Override
    public int getItemViewType(int position) {

        if (position >= 0 && position < datas.size()) {
            return ITEM_TYPE_NORMA;
        } else if (position == datas.size()) {
            return ITEM_TYPE_ADD;
        } else if (position > datas.size() && position < datas.size() + 1 + emptyItemCounts) {
            return ITEM_TYPE_EMPTY;
        } else {
            return ITEM_TYPE_FOOTER;
        }


    }



    public boolean isFooter(int position) {
        return position == (datas.size() + 1 + emptyItemCounts);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return datas.size() + 1 + emptyItemCounts + 1;
    }


    private class BooksViewHolder extends ViewHolder {
        public final View mView;
        BookEntity mItem;
        TextView itemNameTv;
        RelativeLayout itemBookSelectCircle;
        RelativeLayout bookItem;
        CheckBox checkBox;
        ImageView shadow;

        BooksViewHolder(View itemView) {
            super(itemView);
            mView = itemView;
             shadow=(ImageView) itemView.findViewById(R.id.item_book_shadow);
            bookItem = (RelativeLayout) itemView.findViewById(R.id.book_item);
            itemNameTv = (TextView) itemView.findViewById(R.id.item_name_tv);
            itemBookSelectCircle = (RelativeLayout) itemView.findViewById(R.id.item_book_select_circle);
            checkBox = (CheckBox) itemView.findViewById(R.id.check);


        }


    }


    private class AddBooksHolder extends ViewHolder {

        public final View mView;
        RelativeLayout bookAddItem;
        RelativeLayout bookAddBg;

        public AddBooksHolder(View view) {
            super(view);
            mView = view;
            bookAddItem = (RelativeLayout) view.findViewById(R.id.book_add_item);
            bookAddBg = (RelativeLayout) view.findViewById(R.id.add_book_rl);

        }
    }

    private class EmptyHolder extends ViewHolder {

        public final View mView;


        public EmptyHolder(View view) {
            super(view);
            mView = view;


        }
    }


    private class FooterHolder extends ViewHolder {

        public final View mView;


        public FooterHolder(View view) {
            super(view);
            mView = view;


        }
    }

    private boolean isChecked(ImageView checkedImg) {
        return checkedImg.getVisibility() == View.VISIBLE ? true : false;
    }

    private AddBookClickListener addBookClickListener;

    public void setAddBookClickListener(AddBookClickListener addBookClickListener) {
        this.addBookClickListener = addBookClickListener;
    }

    public interface AddBookClickListener {
        void onAddBookClick(int position);
    }


    private OnBookItemClickListener onBookItemClickListener;

    public void setOnBookItemClickListener(OnBookItemClickListener onBookItemClickListener) {
        this.onBookItemClickListener = onBookItemClickListener;
    }

    public interface OnBookItemClickListener {
        void onBookEntityClick(BookEntity BookEntity, int position, CheckBox checkBox);
    }

    private OnBookItemLongClickListener onBookItemLongClickListener;

    public void setOnBookItemLongClickListener(OnBookItemLongClickListener onBookItemLongClickListener) {
        this.onBookItemLongClickListener = onBookItemLongClickListener;
    }

    public interface OnBookItemLongClickListener {
        void onBookEntityLongClick(int position, BookEntity BookEntity, LinkedHashMap<String, Boolean> isCheckedMap, int totalSize);
    }


    private OnItemCheckListener onItemCheckListener;

    public void setOnItemCheckListener(OnItemCheckListener onItemCheckListener) {
        this.onItemCheckListener = onItemCheckListener;
    }

    public interface OnItemCheckListener {
        void onItemCheck(int position, BookEntity BookEntity, LinkedHashMap<String, Boolean> isCheckedMap, int totalSize);

    }

}
