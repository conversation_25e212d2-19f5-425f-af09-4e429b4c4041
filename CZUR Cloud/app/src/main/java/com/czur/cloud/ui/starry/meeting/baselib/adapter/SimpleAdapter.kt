package com.czur.cloud.ui.starry.meeting.baselib.adapter

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.czur.cloud.ui.starry.meeting.baselib.utils.DifferentLiveData

/**
 * Created by 陈丰尧 on 3/15/21
 */
open class SimpleAdapter<T>(
    private val itemId: Int,
    private val onBind: (holder: BaseVH, position: Int, data: T) -> Unit
) : RecyclerView.Adapter<BaseVH>() {
    private val _dataList = mutableListOf<T>()

    val emptyLiveData = DifferentLiveData(true)
    val isEmpty: Boolean
        get() = itemCount == 0

    var dataList: List<T>
        set(value) {
            _dataList.clear()
            _dataList.addAll(value)
            notifyDataSetChanged()
            emptyLiveData.value = isEmpty
        }
        get() = _dataList

    fun addData(data: T, pos: Int) {
        _dataList.add(pos, data)
        notifyItemInserted(pos)
        emptyLiveData.value = isEmpty
    }

    fun removeData(pos: Int) {
        _dataList.removeAt(pos)
        notifyItemRemoved(pos)
        emptyLiveData.value = isEmpty
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH =
        BaseVH(itemId, parent)

    override fun onBindViewHolder(holder: BaseVH, position: Int) {
        val t = dataList[position]
        onBind(holder, position, t)
    }

    override fun getItemCount() = dataList.size

    fun getData(position: Int) = dataList[position]
}