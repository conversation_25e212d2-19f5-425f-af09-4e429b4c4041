package com.czur.cloud.ui.starry.adapter

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.Utils
import com.czur.cloud.R
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.model.StarryEnterpriseModel
import com.czur.cloud.ui.starry.utils.Tools
import com.czur.cloud.ui.starry.utils.singleClick
import kotlinx.android.synthetic.main.starry_item_select_company_list.view.*
import kotlinx.android.synthetic.main.starry_layout_top_bar.*
import java.util.*

class StarryCompanyAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder> {
    private var mDatas: List<StarryEnterpriseModel> = listOf()
    private var mContext: Context = Utils.getApp().applicationContext
    private var activity: Activity? = null

    constructor(activity: Activity) {
        this.activity = activity
        mContext = activity.applicationContext
    }

    fun setmDatas(datas: List<StarryEnterpriseModel>) {
        mDatas = datas
        notifyDataSetChanged()
    }

    fun setOnItemPickListener(onItemPickListener: OnItemPickListener?) {
        this.onItemPickListener = onItemPickListener
    }

    private var onItemPickListener: OnItemPickListener? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val itemView =
            LayoutInflater.from(activity).inflate(R.layout.starry_item_select_company_list, parent, false)
        return NormalHolder(itemView)
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        @SuppressLint("RecyclerView") position: Int
    ) {
        val itemHolder = holder as NormalHolder
        val enterprise = mDatas[position]
        val title = enterprise.enterpriseName
        itemHolder.itemView.starry_item_company_title?.text = title
        itemHolder.itemView.starry_item_company_icon?.text = title.substring(0, 1) ?: ""
        itemHolder.itemView.singleClick { //                notifyDataSetChanged();
            if (onItemPickListener != null) {
                onItemPickListener?.onItemPick(position, enterprise)
            }
        }

        val companyStatus = enterprise.joinStatus
        val expried = enterprise.expired
        // 已过期企业,未加入的
        if (expried){
            itemHolder.itemView.starry_item_company_arrow?.visibility = View.GONE
            itemHolder.itemView.starry_item_company_expried_tv?.visibility = View.VISIBLE
            Tools.setViewButtonEnable(itemHolder.itemView, false)
        }else if(companyStatus != StarryConstants.STARRY_COMPANY_STATUS_JOINED){
            itemHolder.itemView.starry_item_company_arrow?.visibility = View.GONE
            Tools.setViewButtonEnable(itemHolder.itemView, false)
        }else{
            itemHolder.itemView.starry_item_company_arrow?.visibility = View.VISIBLE
            itemHolder.itemView.starry_item_company_expried_tv?.visibility = View.GONE
            Tools.setViewButtonEnable(itemHolder.itemView, true)
        }

    }

    override fun getItemCount(): Int {
        return mDatas.size
    }

    interface OnItemPickListener {
        fun onItemPick(position: Int, enterprise: StarryEnterpriseModel)
    }

    inner class NormalHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

    }
}