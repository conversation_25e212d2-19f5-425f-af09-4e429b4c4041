package com.czur.cloud.util

import android.app.Notification
import android.app.job.JobScheduler
import android.content.Context
import android.graphics.drawable.Icon
import android.os.Build
import androidx.annotation.RequiresApi
import com.czur.czurutils.log.logI
import com.czur.czurutils.log.logE

/**
 * Android 16 API兼容性工具类
 * 当Android 16的新API正式发布时，可以在这里更新为正式的API调用
 * 目前提供降级实现以确保功能正常
 */
object Android16ApiCompat {
    private const val TAG = "Android16ApiCompat"

    /**
     * 获取完整SDK版本（Android 16新增）
     * 当Build.VERSION.SDK_INT_FULL可用时，替换此实现
     */
    @RequiresApi(36)
    fun getSdkIntFull(): Int {
        return try {
            // TODO: 当Android 16正式发布时，替换为：Build.VERSION.SDK_INT_FULL
            Build.VERSION.SDK_INT
        } catch (e: Exception) {
            logE("$TAG.getSdkIntFull error: ${e.message}")
            Build.VERSION.SDK_INT
        }
    }

    /**
     * 获取Minor Release版本号（Android 16新增）
     * 当Build.getMinorSdkVersion()可用时，替换此实现
     */
    @RequiresApi(36)
    fun getMinorSdkVersion(majorVersion: Int): Int {
        return try {
            // TODO: 当Android 16正式发布时，替换为：Build.getMinorSdkVersion(majorVersion)
            0
        } catch (e: Exception) {
            logE("$TAG.getMinorSdkVersion error: ${e.message}")
            0
        }
    }

    /**
     * 检查高级保护模式状态（Android 16新增）
     * 当AdvancedProtectionManager可用时，替换此实现
     */
    @RequiresApi(36)
    fun isAdvancedProtectionEnabled(context: Context): Boolean {
        return try {
            // TODO: 当Android 16正式发布时，替换为：
            // val manager = context.getSystemService(Context.ADVANCED_PROTECTION_SERVICE) as? AdvancedProtectionManager
            // return manager?.isAdvancedProtectionEnabled() ?: false
            false
        } catch (e: Exception) {
            logE("$TAG.isAdvancedProtectionEnabled error: ${e.message}")
            false
        }
    }

    /**
     * 检查动态刷新率支持（Android 16新增）
     * 当Display.hasArrSupport()可用时，替换此实现
     */
    @RequiresApi(36)
    fun hasAdaptiveRefreshRateSupport(context: Context): Boolean {
        return try {
            // TODO: 当Android 16正式发布时，替换为：
            // val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            // val display = windowManager.defaultDisplay
            // return display.hasArrSupport()
            
            // 降级实现：检查是否支持多种刷新率
            val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as? android.view.WindowManager
            val display = windowManager?.defaultDisplay
            
            if (display != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                val supportedRefreshRates = display.supportedRefreshRates
                supportedRefreshRates.size > 1
            } else {
                false
            }
        } catch (e: Exception) {
            logE("$TAG.hasAdaptiveRefreshRateSupport error: ${e.message}")
            false
        }
    }

    /**
     * 获取建议的刷新率（Android 16新增）
     * 当Display.getSuggestedFrameRate()可用时，替换此实现
     */
    @RequiresApi(36)
    fun getSuggestedFrameRate(context: Context, category: Int): Float {
        return try {
            // TODO: 当Android 16正式发布时，替换为：
            // val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            // val display = windowManager.defaultDisplay
            // return display.getSuggestedFrameRate(category)
            
            // 降级实现：返回最高支持的刷新率
            val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as? android.view.WindowManager
            val display = windowManager?.defaultDisplay
            
            if (display != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                val supportedRefreshRates = display.supportedRefreshRates
                supportedRefreshRates.maxOrNull() ?: 60.0f
            } else {
                60.0f
            }
        } catch (e: Exception) {
            logE("$TAG.getSuggestedFrameRate error: ${e.message}")
            60.0f
        }
    }

    /**
     * 获取JobScheduler任务失败的所有原因（Android 16新增）
     * 当JobScheduler.getPendingJobReasons()可用时，替换此实现
     */
    @RequiresApi(36)
    fun getPendingJobReasons(context: Context, jobId: Int): IntArray {
        return try {
            // TODO: 当Android 16正式发布时，替换为：
            // val jobScheduler = context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler
            // return jobScheduler.getPendingJobReasons(jobId)
            
            // 降级实现：检查任务是否存在
            val jobScheduler = context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as? JobScheduler
            if (jobScheduler != null) {
                val pendingJob = jobScheduler.getPendingJob(jobId)
                if (pendingJob != null) {
                    // 任务存在但原因未知
                    intArrayOf()
                } else {
                    // 任务不存在
                    intArrayOf(14) // PENDING_JOB_REASON_INVALID_JOB_ID
                }
            } else {
                intArrayOf()
            }
        } catch (e: Exception) {
            logE("$TAG.getPendingJobReasons error: ${e.message}")
            intArrayOf()
        }
    }

    /**
     * 创建进度样式通知（Android 16新增）
     * 当Notification.ProgressStyle可用时，替换此实现
     */
    @RequiresApi(36)
    fun createProgressStyleNotification(
        context: Context,
        channelId: String,
        title: String,
        progress: Int,
        maxProgress: Int = 1000,
        segments: List<Pair<Int, Int>>? = null,
        points: List<Pair<Int, Int>>? = null,
        trackerIconRes: Int? = null
    ): Notification? {
        return try {
            // TODO: 当Android 16正式发布时，替换为：
            // val progressStyle = Notification.ProgressStyle()
            //     .setProgress(progress)
            //     .setStyledByProgress(false)
            // 
            // trackerIconRes?.let { iconRes ->
            //     val icon = Icon.createWithResource(context, iconRes)
            //     progressStyle.setProgressTrackerIcon(icon)
            // }
            // 
            // segments?.let { segmentList ->
            //     val segmentObjects = segmentList.map { (length, color) ->
            //         Notification.ProgressStyle.Segment(length).setColor(color)
            //     }
            //     progressStyle.setProgressSegments(segmentObjects)
            // }
            // 
            // points?.let { pointList ->
            //     val pointObjects = pointList.map { (position, color) ->
            //         Notification.ProgressStyle.Point(position).setColor(color)
            //     }
            //     progressStyle.setProgressPoints(pointObjects)
            // }
            // 
            // return Notification.Builder(context, channelId)
            //     .setContentTitle(title)
            //     .setSmallIcon(trackerIconRes ?: android.R.drawable.ic_dialog_info)
            //     .setStyle(progressStyle)
            //     .build()
            
            // 降级实现：创建标准进度通知
            val builder = Notification.Builder(context, channelId)
                .setContentTitle(title)
                .setSmallIcon(trackerIconRes ?: android.R.drawable.ic_dialog_info)
                .setProgress(maxProgress, progress, false)
                .setOngoing(true)
            
            // 在内容文本中显示进度信息
            val progressText = if (segments != null || points != null) {
                "进度: $progress/$maxProgress (增强显示)"
            } else {
                "进度: $progress/$maxProgress"
            }
            builder.setContentText(progressText)
            
            builder.build()
        } catch (e: Exception) {
            logE("$TAG.createProgressStyleNotification error: ${e.message}")
            null
        }
    }

    /**
     * 注册高级保护模式状态变化回调（Android 16新增）
     * 当AdvancedProtectionManager.registerAdvancedProtectionCallback()可用时，替换此实现
     */
    @RequiresApi(36)
    fun registerAdvancedProtectionCallback(
        context: Context,
        callback: (Boolean) -> Unit
    ): Boolean {
        return try {
            // TODO: 当Android 16正式发布时，替换为：
            // val manager = context.getSystemService(Context.ADVANCED_PROTECTION_SERVICE) as? AdvancedProtectionManager
            // if (manager != null) {
            //     val advancedProtectionCallback = object : AdvancedProtectionManager.Callback() {
            //         override fun onAdvancedProtectionChanged(enabled: Boolean) {
            //             callback(enabled)
            //         }
            //     }
            //     manager.registerAdvancedProtectionCallback(Executors.newSingleThreadExecutor(), advancedProtectionCallback)
            //     return true
            // }
            // return false
            
            // 降级实现：暂不支持回调
            logI("$TAG.registerAdvancedProtectionCallback: Not supported in current implementation")
            false
        } catch (e: Exception) {
            logE("$TAG.registerAdvancedProtectionCallback error: ${e.message}")
            false
        }
    }

    /**
     * 检查当前实现是否使用了降级方案
     */
    fun isUsingFallbackImplementation(): Boolean {
        return true // 当前所有实现都是降级方案
    }

    /**
     * 获取API兼容性信息
     */
    fun getCompatibilityInfo(): String {
        return if (Build.VERSION.SDK_INT >= 36) {
            "Android 16 detected, using fallback implementations until official APIs are available"
        } else {
            "Android ${Build.VERSION.SDK_INT} detected, Android 16 features not available"
        }
    }
}
