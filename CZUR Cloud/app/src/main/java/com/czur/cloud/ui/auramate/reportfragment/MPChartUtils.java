package com.czur.cloud.ui.auramate.reportfragment;


import android.graphics.Color;
import android.graphics.Matrix;

import androidx.annotation.ColorInt;

import com.czur.cloud.R;
import com.github.mikephil.charting.charts.BarChart;
import com.github.mikephil.charting.charts.Chart;
import com.github.mikephil.charting.charts.CombinedChart;
import com.github.mikephil.charting.components.AxisBase;
import com.github.mikephil.charting.components.Legend;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.CombinedData;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;
import com.github.mikephil.charting.formatter.ValueFormatter;
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet;
import com.github.mikephil.charting.utils.ViewPortHandler;

import java.util.ArrayList;
import java.util.List;

public class MPChartUtils {
    private final static int BAR_MAX_COUNT = 7;
    private final static float  BAR_WIDTH = 0.4f; // x4 DataSet

    /**
     * 不显示无数据的提示
     *
     * @param mChart
     */
    public static void NotShowNoDataText(Chart mChart) {
        mChart.clear();
        mChart.notifyDataSetChanged();
        mChart.setNoDataText("你还没有记录数据");
        mChart.setNoDataTextColor(Color.WHITE);
        mChart.invalidate();
    }
    /**
     *  配置Chart 基础设置
     * @param mChart 图表
     * @param mLabels x 轴标签
     * @param yMax y 轴最大值
     * @param yMin y 轴最小值
     * @param isShowLegend 是否显示图例
     */
    public static void configChart(CombinedChart mChart, List<String> mLabels, float yMax, float yMin, boolean isShowLegend) {

        mChart.setDrawGridBackground(false);
        mChart.setDrawBorders(false);
        mChart.setScaleEnabled(false);
        mChart.setDragEnabled(true);
        mChart.setNoDataText("");
        // 不显示描述数据
        mChart.getDescription().setEnabled(false);
        mChart.getAxisRight().setEnabled(false);

        Legend legend = mChart.getLegend();
        // 是否显示图例
        if (isShowLegend) {
            legend.setEnabled(true);
            legend.setTextColor(Color.WHITE);
            legend.setForm(Legend.LegendForm.CIRCLE);
            legend.setHorizontalAlignment(Legend.LegendHorizontalAlignment.LEFT);
            legend.setVerticalAlignment(Legend.LegendVerticalAlignment.BOTTOM);
            legend.setOrientation(Legend.LegendOrientation.HORIZONTAL);
            legend.setYEntrySpace(20f);
            //图例的大小
            legend.setFormSize(7f);
            // 图例描述文字大小
            legend.setTextSize(10);
            legend.setXEntrySpace(20f);

        } else {
            legend.setEnabled(false);
        }

        XAxis xAxis = mChart.getXAxis();

        // 是否显示x轴线
        xAxis.setDrawAxisLine(true);
        // 设置x轴线的颜色
        xAxis.setAxisLineColor(Color.parseColor("#4cffffff"));
        // 是否绘制x方向网格线
        xAxis.setDrawGridLines(false);
        //x方向网格线的颜色
        xAxis.setGridColor(Color.parseColor("#30FFFFFF"));

        // 设置x轴数据的位置
        xAxis.setPosition(XAxis.XAxisPosition.BOTTOM);
        // 设置x轴文字的大小
        xAxis.setTextSize(12);

        // 设置x轴数据偏移量
        xAxis.setYOffset(5);
        final List<String> labels = mLabels;
        // 显示x轴标签
        ValueFormatter formatter = new ValueFormatter() {

            @Override
            public String getFormattedValue(float value, AxisBase axis) {
                int index = (int) value;
                if (index < 0 || index >= labels.size()) {
                    return "";
                }
                return labels.get(index);
                // return labels.get(Math.min(Math.max((int) value, 0), labels.size() - 1));
            }

        };
        // 引用标签
        xAxis.setValueFormatter(formatter);
        // 设置x轴文字颜色
        xAxis.setTextColor(mChart.getResources().getColor(R.color.black));
        // 设置x轴每最小刻度 interval
        xAxis.setGranularity(1f);

        YAxis yAxis = mChart.getAxisLeft();
        //设置x轴的最大值
        yAxis.setAxisMaximum(yMax);
        // 设置y轴的最大值
        yAxis.setAxisMinimum(yMin);
        // 不显示y轴
        yAxis.setDrawAxisLine(false);
        // 设置y轴数据的位置
        yAxis.setPosition(YAxis.YAxisLabelPosition.OUTSIDE_CHART);
        // 不从y轴发出横向直线
        yAxis.setDrawGridLines(false);
        // 是否显示y轴坐标线
        yAxis.setDrawZeroLine(true);
        // 设置y轴的文字颜色
        yAxis.setTextColor(mChart.getResources().getColor(R.color.black));
        // 设置y轴文字的大小
        yAxis.setTextSize(12);
        // 设置y轴数据偏移量
        //yAxis.setXOffset(30);
        // yAxis.setYOffset(-3);
        yAxis.setXOffset(15);
        // yAxis.setGranularity(yGranularity);
        yAxis.setLabelCount(5, false);
        //yAxis.setGranularity(5);//interval

        Matrix matrix = new Matrix();
        // 根据数据量来确定 x轴缩放大倍
        if (mLabels.size() <= 10) {
            matrix.postScale(1.0f, 1.0f);
        } else if (mLabels.size() <= 15) {
            matrix.postScale(1.5f, 1.0f);
        } else if (mLabels.size() <= 20) {
            matrix.postScale(2.0f, 1.0f);
        } else {
            matrix.postScale(3.0f, 1.0f);
        }

        // 在图表动画显示之前进行缩放
        mChart.getViewPortHandler().refresh(matrix, mChart, false);
        // x轴执行动画
        mChart.animateX(500);

    }

    /**
     * 初始化数据
     *
     * @param chart
     * @param lineDatas
     */
    public static void initData(CombinedChart chart, LineData... lineDatas) {
        CombinedData combinedData = new CombinedData();
        for (LineData lineData : lineDatas) {
            combinedData.setData(lineData);
        }
        chart.setData(combinedData);
        chart.invalidate();
    }

    /**
     * 获取LineDataSet
     *
     * @param entries
     * @param label
     * @param textColor
     * @param lineColor
     * @return
     */
    public static LineDataSet getLineData(List<Entry> entries, String label, @ColorInt int textColor, @ColorInt int lineColor, boolean isFill) {
        LineDataSet dataSet = new LineDataSet(entries, label);
        // 设置曲线的颜色
        dataSet.setColor(lineColor);
        //数值文字颜色
        dataSet.setValueTextColor(textColor);
        // 模式为贝塞尔曲线
        dataSet.setMode(LineDataSet.Mode.HORIZONTAL_BEZIER);
        // 是否绘制数据值
        dataSet.setDrawValues(false);
        // 是否绘制圆点
        dataSet.setDrawCircles(true);
        dataSet.setDrawCircleHole(false);
        // 这里有一个坑，当我们想隐藏掉高亮线的时候，MarkerView 跟着不见了
        // 因此只有将它设置成透明色
        dataSet.setHighlightEnabled(true);// 隐藏点击时候的高亮线
        //设置高亮线为透明色
        dataSet.setHighLightColor(Color.TRANSPARENT);

        if (isFill) {
            //是否设置填充曲线到x轴之间的区域
            dataSet.setDrawFilled(true);
            // 填充颜色
            dataSet.setFillColor(lineColor);
        }
        //设置圆点的颜色
        dataSet.setCircleColor(lineColor);
        // 设置圆点半径
        dataSet.setCircleRadius(3.5f);
        // 设置线的宽度
        dataSet.setLineWidth(1f);
        return dataSet;
    }

    /**
     * 获取barDataSet
     * @param entries
     * @param label
     * @param textColor
     * @param lineColor
     * @return
     */
    public static BarDataSet getBarDataSet(List<BarEntry> entries, String label, @ColorInt int textColor, @ColorInt int lineColor) {
        BarDataSet dataSet = new BarDataSet(entries, label);
        dataSet.setBarBorderWidth(5);
        dataSet.setBarShadowColor(lineColor);
        dataSet.setValueTextColor(textColor);
        dataSet.setDrawValues(false);
        return dataSet;
    }

    /**
     * 配置柱状图基础设置
     * @param chart
     * @param xLabels
     */
    public static void configBarChart(BarChart chart, final List<String> xLabels) {
        //设置描述
        chart.getDescription().setEnabled(false);
        chart.setTouchEnabled(true);//设置是否可以触摸，如为false，则不能拖动，缩放等
        chart.setDragEnabled(true); //设置是否可以拖拽，缩放
        chart.setNoDataText("暂无数据"); // 没有数据时的提示文案
        chart.getAxisRight().setEnabled(false);//禁用右侧y轴
        chart.setPinchZoom(false);//设置按比例放缩柱状图//是否能够缩放
        chart.setScaleEnabled(false);
        //允许X轴缩放
        chart.setScaleXEnabled(false);
        //允许Y轴缩放
        chart.setScaleYEnabled(false);
        chart.getLegend().setEnabled(false);

        // if more than 60 entries are displayed in the chart, no values will be drawn
        chart.setMaxVisibleValueCount(60);
        chart.setDrawBarShadow(false);
        chart.setDrawGridBackground(false);

        chart.getAxisLeft().setDrawAxisLine(false);
        chart.getXAxis().setDrawAxisLine(false);
        // add a nice and smooth animation
        chart.animateY(1500);

        chart.setDrawBorders(false); ////是否在折线图上添加边框
        // 如果没有数据的时候，会显示这个，类似ListView的EmptyView
        chart.setDrawGridBackground(true); // 是否显示表格颜色
        chart.setGridBackgroundColor(Color.argb(255, 240,240,240)); // 表格的的颜色，在这里是是给颜色设置一个透明度

        chart.setFitBars(true);//使两侧的柱图完全显示

        //x坐标轴设置
        XAxis xAxis = chart.getXAxis();//获取x轴
        xAxis.setPosition(XAxis.XAxisPosition.BOTTOM);//设置X轴标签显示位置
        xAxis.setGranularity(1f);//设置最小间隔，防止当放大时，出现重复标签。
        xAxis.setLabelCount(BAR_MAX_COUNT, false);
        xAxis.setDrawGridLines(false);//不绘制格网线

        // 显示x轴标签
//        xAxis.setValueFormatter(new ValueFormatter() {
//            @Override
//            public String getFormattedValue(float value) {
//                if ((int)value > barChartDataListX.size()-1)
//                    return "";
//                return barChartDataListX.get((int)value);
//            }
//        });
        ValueFormatter formatter = new ValueFormatter() {
            @Override
            public String getFormattedValue(float value, AxisBase axis) {
                return xLabels.get(Math.min(Math.max((int) value, 0), xLabels.size() - 1));
            }
        };
        xAxis.setValueFormatter(formatter);
        xAxis.setAxisMinimum(-0.5f);    //X轴数据向右偏移一些
        xAxis.setTextSize(10f);//设置标签字体大小
//        xAxis.setAxisLineColor(Color.parseColor("#4cffffff"));
//        xAxis.setLabelCount(xLabels.size());//设置标签显示的个数
        xAxis.setTextColor(Color.rgb(128,128,128));
        //是否展示网格线
        xAxis.setDrawGridLines(false);

        //y轴设置
        YAxis leftAxis = chart.getAxisLeft();//获取左侧y轴
        leftAxis.setPosition(YAxis.YAxisLabelPosition.OUTSIDE_CHART);//设置y轴标签显示在外侧
        leftAxis.setAxisMinimum(0f);//设置Y轴最小值
        leftAxis.setAxisMaximum(100f);  // the axis maximum is 100
        leftAxis.setDrawGridLines(false);
        leftAxis.setDrawLabels(true);//禁止绘制y轴标签
        leftAxis.setDrawAxisLine(false);//禁止绘制y轴
//        leftAxis.setAxisLineColor(Color.parseColor("#4cffffff"));
        leftAxis.setLabelCount(5, false);
        leftAxis.setTextColor(Color.rgb(128,128,128));
        leftAxis.setValueFormatter(new ValueFormatter() {
            @Override
            public String getFormattedValue(float value, AxisBase axis) {
                return ((int) (value)) + "%";
            }
        });

        YAxis rightAxis = chart.getAxisRight();
        rightAxis.setDrawGridLines(false);
/*
        Matrix matrix = new Matrix();
        // 根据数据量来确定 x轴缩放大倍
        if (xLabels.size() <= 10) {
            matrix.postScale(1.0f, 1.0f);
        } else if (xLabels.size() <= 15) {
            matrix.postScale(1.5f, 1.0f);
        } else if (xLabels.size() <= 20) {
            matrix.postScale(2.0f, 1.0f);
        } else {
            matrix.postScale(3.0f, 1.0f);
        }
        chart.getViewPortHandler().refresh(matrix, chart, false);
        chart.setExtraBottomOffset(10);//距视图窗口底部的偏移，类似与paddingbottom
        chart.setExtraTopOffset(30);//距视图窗口顶部的偏移，类似与paddingtop
*/

    }

    public static void initBarChartLables(BarChart chart, final List<String> xLabels) {

        //x坐标轴设置
        XAxis xAxis = chart.getXAxis();//获取x轴
        xAxis.setPosition(XAxis.XAxisPosition.BOTTOM);//设置X轴标签显示位置
        xAxis.setGranularity(1f);//设置最小间隔，防止当放大时，出现重复标签。
        xAxis.setLabelCount(BAR_MAX_COUNT, false);
        xAxis.setDrawGridLines(false);//不绘制格网线

        ValueFormatter formatter = new ValueFormatter() {
            @Override
            public String getFormattedValue(float value, AxisBase axis) {
                return xLabels.get(Math.min(Math.max((int) value, 0), xLabels.size() - 1));
            }
        };
        xAxis.setValueFormatter(formatter);
        xAxis.setAxisMinimum(-0.5f);    //X轴数据向右偏移一些
        xAxis.setTextSize(10f);//设置标签字体大小
//        xAxis.setAxisLineColor(Color.parseColor("#4cffffff"));
//        xAxis.setLabelCount(xLabels.size());//设置标签显示的个数
        xAxis.setTextColor(Color.rgb(128,128,128));
        //是否展示网格线
        xAxis.setDrawGridLines(false);

    }

    /**
     * 初始化柱状图图表数据
     * @param chart
     * @param entries
     * @param barColor
     */
    public static void initBarChartData(BarChart chart, List<BarEntry> entries, @ColorInt int barColor) {
        BarDataSet set1 = new BarDataSet(entries, "Data Set");
        set1.setValueTextColor(Color.WHITE);
        set1.setDrawValues(false);
        set1.setColor(barColor);
        set1.setHighLightAlpha(45);//选中的透明度
        ArrayList<IBarDataSet> dataSets = new ArrayList<>();
        dataSets.add(set1);

        BarData data = new BarData(dataSets);
        data.setValueTextSize(10f);
        // 设置bar的宽度，但是点很多少的时候好像没作用，会拉得很宽
        data.setBarWidth(BAR_WIDTH);
        // 设置value值 颜色
        data.setValueTextColor(Color.WHITE);
        //设置y轴显示的标签
        data.setValueFormatter(new ValueFormatter() {
            @Override
            public String getFormattedValue(float value, Entry entry, int dataSetIndex, ViewPortHandler viewPortHandler) {
                return ((int) (value)) + "%";
            }
        });

        //setVisibleXRange是设置可见的x范围，我设置成(0, 5)之后，
        // 就只会显示序号0到5的内容(6项)。往左滑才会显示第7,8,9…最后一项。
        chart.setVisibleXRange(0,BAR_MAX_COUNT);

        chart.invalidate();

    }

    public static void initBarChartData2(BarChart chart, List<BarEntry> entries, @ColorInt int barColor) {
        BarDataSet set1;

//        if (chart.getData() != null &&
//                chart.getData().getDataSetCount() > 0) {
//            set1 = (BarDataSet) chart.getData().getDataSetByIndex(0);
//            set1.setEntries(entries);
//            chart.getData().notifyDataChanged();
//            chart.notifyDataSetChanged();
//        } else {
            set1 = new BarDataSet(entries, "Data Set");
            set1.setValueTextColor(Color.WHITE);
            set1.setDrawValues(false);
            set1.setColor(barColor);
            set1.setHighLightAlpha(45);//选中的透明度

            ArrayList<IBarDataSet> dataSets = new ArrayList<>();
            dataSets.add(set1);

            BarData data = new BarData(dataSets);
            data.setValueTextSize(10f);
            // 设置bar的宽度，但是点很多少的时候好像没作用，会拉得很宽
            data.setBarWidth(BAR_WIDTH);
            // 设置value值 颜色
            data.setValueTextColor(Color.WHITE);

//            chart.clear();
            chart.setData(data);
//        }

        chart.invalidate();
    }



    public static void configBarChart1(BarChart barChart, final List<String> xLabels) {
        barChart.getDescription().setEnabled(false);//设置描述
        barChart.setPinchZoom(false);//设置按比例放缩柱状图
        barChart.setScaleEnabled(false);
        barChart.setDragEnabled(true);
        barChart.setNoDataText(""); // 没有数据时的提示文案
        //x坐标轴设置
        // IAxisValueFormatter xAxisFormatter = new StringAxisValueFormatter(xAxisValue);//设置自定义的x轴值格式化器
        XAxis xAxis = barChart.getXAxis();//获取x轴
        xAxis.setPosition(XAxis.XAxisPosition.BOTTOM);//设置X轴标签显示位置
        xAxis.setDrawGridLines(false);//不绘制格网线
        xAxis.setGranularity(1f);//设置最小间隔，防止当放大时，出现重复标签。
        // 显示x轴标签
        ValueFormatter formatter = new ValueFormatter() {

            @Override
            public String getFormattedValue(float value, AxisBase axis) {
                return xLabels.get(Math.min(Math.max((int) value, 0), xLabels.size() - 1));
            }

        };
        xAxis.setValueFormatter(formatter);
        xAxis.setTextSize(10);//设置标签字体大小
        xAxis.setTextColor(barChart.getResources().getColor(R.color.black));
        xAxis.setAxisLineColor(Color.parseColor("#4cffffff"));
        xAxis.setLabelCount(xLabels.size());//设置标签显示的个数

        //y轴设置
        YAxis leftAxis = barChart.getAxisLeft();//获取左侧y轴
        leftAxis.setPosition(YAxis.YAxisLabelPosition.OUTSIDE_CHART);//设置y轴标签显示在外侧
        leftAxis.setAxisMinimum(0f);//设置Y轴最小值
        leftAxis.setDrawGridLines(false);
        leftAxis.setDrawLabels(true);//禁止绘制y轴标签
        leftAxis.setDrawAxisLine(false);//禁止绘制y轴
        leftAxis.setAxisLineColor(Color.parseColor("#4cffffff"));
        leftAxis.setTextColor(barChart.getResources().getColor(R.color.black));
        leftAxis.setValueFormatter(new ValueFormatter() {
            @Override
            public String getFormattedValue(float value, AxisBase axis) {
                return ((int) (value)) + "%";
            }
        });

        barChart.getAxisRight().setEnabled(false);//禁用右侧y轴
        barChart.getLegend().setEnabled(false);
        //图例设置
       /* Legend legend = barChart.getLegend();
        legend.setHorizontalAlignment(Legend.LegendHorizontalAlignment.CENTER);//图例水平居中
        legend.setVerticalAlignment(Legend.LegendVerticalAlignment.TOP);//图例在图表上方
        legend.setOrientation(Legend.LegendOrientation.HORIZONTAL);//图例的方向为水平
        legend.setDrawInside(false);//绘制在chart的外侧
        legend.setDirection(Legend.LegendDirection.LEFT_TO_RIGHT);//图例中的文字方向

        legend.setForm(Legend.LegendForm.SQUARE);//图例窗体的形状
        legend.setFormSize(0f);//图例窗体的大小
        legend.setTextSize(16f);//图例文字的大小*/
        //legend.setYOffset(-2f);


        Matrix matrix = new Matrix();
        // 根据数据量来确定 x轴缩放大倍
        if (xLabels.size() <= 10) {
            matrix.postScale(1.0f, 1.0f);
        } else if (xLabels.size() <= 15) {
            matrix.postScale(1.5f, 1.0f);
        } else if (xLabels.size() <= 20) {
            matrix.postScale(2.0f, 1.0f);
        } else {
            matrix.postScale(3.0f, 1.0f);
        }
        barChart.getViewPortHandler().refresh(matrix, barChart, false);
        barChart.setExtraBottomOffset(10);//距视图窗口底部的偏移，类似与paddingbottom
        barChart.setExtraTopOffset(30);//距视图窗口顶部的偏移，类似与paddingtop
        barChart.setFitBars(true);//使两侧的柱图完全显示
        barChart.animateX(1500);//数据显示动画，从左往右依次显示
    }


    public static void initBarChart1(BarChart chart, List<BarEntry> entries, String title, @ColorInt int barColor) {
        BarDataSet set1 = new BarDataSet(entries, title);
        set1.setValueTextColor(Color.WHITE);
        set1.setColor(barColor);
        ArrayList<IBarDataSet> dataSets = new ArrayList<>();
        dataSets.add(set1);

        BarData data = new BarData(dataSets);
        data.setValueTextSize(10f);
        // 设置bar的宽度，但是点很多少的时候好像没作用，会拉得很宽
        data.setBarWidth(0.1f);
        // 设置value值 颜色
        data.setValueTextColor(Color.WHITE);
        //设置y轴显示的标签
        data.setValueFormatter(new ValueFormatter() {
            @Override
            public String getFormattedValue(float value, Entry entry, int dataSetIndex, ViewPortHandler viewPortHandler) {
                return ((int) (value)) + "%";
            }
        });


        chart.setData(data);
        chart.invalidate();
    }

}

