package com.czur.cloud.ui.eshare.adapter;

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.findViewTreeLifecycleOwner
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.StringUtils
import com.czur.cloud.R
import com.czur.cloud.ui.eshare.myentity.BrowserFileType
import com.czur.cloud.ui.eshare.myentity.FileBrowserEntity
import com.czur.cloud.ui.eshare.utils.ObservableList
import com.czur.cloud.ui.eshare.widget.IconImageView
import com.czur.cloud.ui.starry.utils.singleClick
import com.czur.czurwma.widget.popup.BasePopup
import com.czur.czurwma.widget.popup.WPopParams
import com.czur.czurwma.widget.popup.WPopupDirection

class FileBrowserAdapter : RecyclerView.Adapter<FileBrowserAdapter.MyViewHolder>() {
    private val items: MutableList<FileBrowserEntity.FileEntity> = mutableListOf()
    var selectedItems = ObservableList<FileBrowserEntity.FileEntity>()

    //    private val selectedItems: MutableList<FileBrowserEntity.FileEntity> = mutableListOf()
//    var selectedItems = ObservableList<FileBrowserEntity.FileEntity>()

    private class MyDiffCallback(
        private val oldItems: List<FileBrowserEntity.FileEntity>,
        private val newItems: List<FileBrowserEntity.FileEntity>
    ) : DiffUtil.Callback() {
        override fun getOldListSize(): Int {
            return oldItems.size
        }

        override fun getNewListSize(): Int {
            return newItems.size
        }

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldItem = oldItems[oldItemPosition]
            val newItem = newItems[newItemPosition]
            // 判断两个项是否代表相同的数据对象
            return oldItem.absPath == newItem.absPath
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldItem = oldItems[oldItemPosition]
            val newItem = newItems[newItemPosition]
            // 判断两个项的内容是否相同（例如，内容是否完全相等）
//            return oldItem.absPath == newItem.absPath
//                    && oldItem.fileSize == newItem.fileSize
//                    && oldItem.fileType == newItem.fileType
//                    && oldItem.absPathWithSuffix == newItem.absPathWithSuffix
//                    && oldItem.parentPath == newItem.parentPath
//                    && oldItem.pinyinName == newItem.pinyinName
//                    && oldItem.name == newItem.name
            return false
        }
    }

    fun setData(newItems: List<FileBrowserEntity.FileEntity>) {

        // 使用 DiffUtil 计算差异并更新数据集
        val diffResult = DiffUtil.calculateDiff(MyDiffCallback(items, newItems))
        items.clear()
        items.addAll(newItems)
        cleanSelectList()
        diffResult.dispatchUpdatesTo(this)
    }

    private var onItemClickListener: OnItemClickListener? = null
    fun setItemClickListener(onItemClickListener: OnItemClickListener) {
        this.onItemClickListener = onItemClickListener
    }

    fun getData(): MutableList<FileBrowserEntity.FileEntity> {
        return items
    }

    fun cleanSelectList() {
        selectedItems.clear()
    }
    fun getSelectList(): MutableList<FileBrowserEntity.FileEntity> {
        return selectedItems
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyViewHolder {
        // 创建 ViewHolder
        val view: View =
            LayoutInflater.from(parent.context)
                .inflate(R.layout.item_browser_file_layout, parent, false)
        return MyViewHolder(view)
    }

    override fun onBindViewHolder(holder: MyViewHolder, position: Int) {
        // 绑定数据到 ViewHolder
        val item = items[position]
        holder.file_name_tv.text = item.name

        holder.file_type_iv.changeIconByEntity(item)
        if (item.fileType == BrowserFileType.FOLDER.typeName) {
            holder.arrow_iv.visibility = View.VISIBLE
            holder.selected_iv.visibility = View.GONE
        } else {
            holder.arrow_iv.visibility = View.GONE
            holder.selected_iv.visibility = View.VISIBLE
        }
        selectedItems.find { it.absPath == item.absPath && it.fileSize == item.fileSize }?.let {
            holder.selected_iv.setImageResource(R.mipmap.ic_selected)
        } ?: run {
            holder.selected_iv.setImageResource(R.mipmap.ic_deselected)
        }

        holder.itemView.singleClick {
            if (item.fileType == BrowserFileType.FOLDER.typeName) {
                onItemClickListener?.onItemClick(position, item)
            } else {
                selectedItems.find { it.absPath == item.absPath && it.fileSize == item.fileSize }
                    ?.let {
                        holder.selected_iv.setImageResource(R.mipmap.ic_deselected)
                        selectedItems.remove(item)
                    } ?: run {
                    holder.selected_iv.setImageResource(R.mipmap.ic_selected)
                    selectedItems.add(item)
                }
            }
        }
    }

    override fun getItemCount(): Int {
        return items.size
    }


    private fun initPopup(view: View): BasePopup {
        val customPopup = BasePopup(
            WPopParams(
                R.layout.baselib_pop_tip,
                ActivityUtils.getTopActivity(),
                false,
                cancelable = true,
                width = ViewGroup.LayoutParams.WRAP_CONTENT,
                height = ViewGroup.LayoutParams.WRAP_CONTENT
            )
        )
        val tv = customPopup.getContentView() as TextView
        tv.text = StringUtils.getString(R.string.transmit_large_file_size)
        customPopup.defaultMargin = 10

        customPopup.dismissListener = {
        }
        customPopup.showAtDirectionByViewAlignRight(view, WPopupDirection.BOTTOM, 20)
        view.findViewTreeLifecycleOwner()?.lifecycle?.let { customPopup.bindLifecycle(it) }

        return customPopup
    }


    class MyViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val file_name_tv: TextView // 文件名称
        val file_type_iv: IconImageView // 文件类型
        val selected_iv: ImageView // 文件选择框
        val arrow_iv: ImageView

        init {
            file_name_tv = itemView.findViewById<TextView>(R.id.file_name_tv)
            file_type_iv = itemView.findViewById<IconImageView>(R.id.file_type_iv)
            selected_iv = itemView.findViewById<ImageView>(R.id.selected_iv)
            arrow_iv = itemView.findViewById<ImageView>(R.id.arrow_iv)
        }

    }

    interface OnItemClickListener {
        fun onItemClick(position: Int, fileEntity: FileBrowserEntity.FileEntity)
    }

}