package com.czur.cloud.common;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Handler;
import android.util.LruCache;
import android.widget.ImageView;

import com.czur.cloud.R;

import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

public class SDCardImageLoader {
    private static final int THREAD_COUNT = 2;
    private LruCache<String, Bitmap> imageCache;
    private ExecutorService executorService = Executors.newFixedThreadPool(THREAD_COUNT);
    private Handler handler = new Handler();
    private int screenW, screenH;
    private ConcurrentLinkedQueue<ImageLoadTask> taskQueue = new ConcurrentLinkedQueue<>();
    private AtomicInteger runningCount = new AtomicInteger(1);

    private void setTask(ImageLoadTask task) {
        taskQueue.offer(task);
    }

    private ImageLoadTask getTask() {
        return taskQueue.poll();
    }

    public SDCardImageLoader(int screenW, int screenH) {
        this.screenW = screenW;
        this.screenH = screenH;

        // 获取应用程序最大可用内存
        int maxMemory = (int) Runtime.getRuntime().maxMemory();
        int cacheSize = maxMemory / 8;

        // 设置图片缓存大小为程序最大可用内存的1/8
        imageCache = new LruCache<String, Bitmap>(cacheSize) {
            @Override
            protected int sizeOf(String key, Bitmap value) {
                return value.getRowBytes() * value.getHeight();
            }
        };
    }

    private Bitmap loadDrawable(final String filePath, boolean isScrolling, final ImageCallback callback) {
        // 如果缓存过就从缓存中取出数据
        if (imageCache.get(filePath) != null) {
            return imageCache.get(filePath);
        }
        if (isScrolling) {
            return null;
        }
        if (runningCount.get() <= THREAD_COUNT) {
            executorService.submit(new ImageLoadTask(filePath, callback));
        } else {
            setTask(new ImageLoadTask(filePath, callback));
        }
        return null;
    }

    public void loadImage(final String filePath, final ImageView imageView, boolean isScrolling) {
        Bitmap bmp = loadDrawable(filePath, isScrolling, new ImageCallback() {
            @Override
            public void imageLoaded(Bitmap bmp) {
                if (bmp != null && imageView.getTag().equals(filePath)) {
                    imageView.setImageBitmap(bmp);
                } else {
                    imageView.setImageResource(R.mipmap.empty_photo);
                }
            }
        });
        if (bmp != null && imageView.getTag().equals(filePath)) {
            imageView.setImageBitmap(bmp);
        } else {
            imageView.setImageResource(R.mipmap.empty_photo);
        }
    }

    /**
     * 异步读取SD卡图片，并按指定的比例进行压缩（最大不超过屏幕像素数）
     *
//     * @param smallRate 压缩比例，不压缩时输入1，此时将按屏幕像素数进行输出
     * @param filePath  图片在SD卡的全路径
     * @param imageView 组件
     */
    public void loadImage(final String filePath, final ImageView imageView) {
        loadImage(filePath, imageView, false);
    }

    private class ImageLoadTask implements Runnable {

        private String filePath;
        private ImageCallback callback;

        public ImageLoadTask(String filePath, ImageCallback callback) {
            this.filePath = filePath;
            this.callback = callback;
        }

        @Override
        public void run() {
            try {
                BitmapFactory.Options opt = new BitmapFactory.Options();
                opt.inJustDecodeBounds = true;
                BitmapFactory.decodeFile(filePath, opt);

                // 获取到这个图片的原始宽度和高度
                int picWidth = opt.outWidth;
                int picHeight = opt.outHeight;

                //读取图片失败时直接返回
                if (picWidth == 0 || picHeight == 0) {
                    return;
                }

                //初始压缩比例
                opt.inSampleSize = 6;
                // 根据屏的大小和图片大小计算出缩放比例
                if (picWidth > picHeight && picWidth > screenW) {
                    opt.inSampleSize *= picWidth / screenW;
                } else if (picHeight > screenH) {
                    opt.inSampleSize *= picHeight / screenH;
                }

                //这次再真正地生成一个有像素的，经过缩放了的bitmap
                opt.inJustDecodeBounds = false;
                final Bitmap bmp = BitmapFactory.decodeFile(filePath, opt);
                //存入map
                imageCache.put(filePath, bmp);

                handler.post(new Runnable() {
                    public void run() {
                        callback.imageLoaded(bmp);
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
            ImageLoadTask task = getTask();
            if (task == null) {
                runningCount.decrementAndGet();
            } else {
                executorService.submit(task);
            }
        }
    }

    // 对外界开放的回调接口
    public interface ImageCallback {
        // 注意 此方法是用来设置目标对象的图像资源
        void imageLoaded(Bitmap imageDrawable);
    }
}
