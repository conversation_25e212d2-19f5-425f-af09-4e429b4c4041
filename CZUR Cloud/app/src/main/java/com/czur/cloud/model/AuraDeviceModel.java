package com.czur.cloud.model;

public class AuraDeviceModel {


    /**
     * id : 77
     * alias : etname78
     * type : ET16
     * bindUserId : 9
     * isBinded : true
     * inUsingUserInfo : <EMAIL>
     * equipmentUID : qaz-147-wsx-258
     * usingOn : 1534904967000
     * offUsingOn : 1534905870000
     * connectNetworkTime : 1532353798000
     * binded : true
     */
    private int scrollY;
    private int id;
    private String alias;
    private String showName;
    private String type;
    private int bindUserId;
    private boolean isBinded;
    private String inUsingUserInfo;
    private String equipmentUID;
    private long usingOn;
    private long offUsingOn;
    private long connectNetworkTime;
    private boolean binded;

    private int light_level;
    private String light_mode;
    private boolean light_switch;
    private boolean sp_reminder_switch;
    private String sp_reminder_sensitivity_level;
    private boolean light_is_online;

    private int has_calibrated_sp;
    private String firmware_need_update;
    private String firmware_current_version;
    private String firmware_update_version;

    private String system_language;
    private String smart_power_saving_switch;
    private int sp_reminder_sensitivity_volume = 5;
    private String wifi_ssid;
    private int unreadCount;
    private boolean isOffline = true;

    private String sedentary_reminder_switch;
    private int sedentary_reminder_duration;
    private int message_api_version;

    private boolean readyForCheckUpdate;
    private String device_mode;

    public String getShowName() {        return showName;    }

    public void setShowName(String showName) {        this.showName = showName;    }

    public boolean isShowLoading() {
        return showLoading;
    }

    public void setShowLoading(boolean showLoading) {
        this.showLoading = showLoading;
    }

    private boolean showLoading = true;

    public boolean isLight_switch() {
        return light_switch;
    }

    public int getHas_calibrated_sp() {
        return has_calibrated_sp;
    }

    public void setHas_calibrated_sp(int has_calibrated_sp) {
        this.has_calibrated_sp = has_calibrated_sp;
    }

    public String getFirmware_need_update() {
        return firmware_need_update;
    }

    public void setFirmware_need_update(String firmware_need_update) {
        this.firmware_need_update = firmware_need_update;
    }

    public String getFirmware_current_version() {
        return firmware_current_version;
    }

    public void setFirmware_current_version(String firmware_current_version) {
        this.firmware_current_version = firmware_current_version;
    }

    public String getFirmware_update_version() {
        return firmware_update_version;
    }

    public void setFirmware_update_version(String firmware_update_version) {
        this.firmware_update_version = firmware_update_version;
    }

    public int getScrollY() {
        return scrollY;
    }

    public void setScrollY(int scrollY) {
        this.scrollY = scrollY;
    }

    public int getSp_reminder_sensitivity_volume() {
        return sp_reminder_sensitivity_volume;
    }

    public void setSp_reminder_sensitivity_volume(int sp_reminder_sensitivity_volume) {
        this.sp_reminder_sensitivity_volume = sp_reminder_sensitivity_volume;
    }

    public String getWifi_ssid() {
        return wifi_ssid;
    }

    public void setWifi_ssid(String wifi_ssid) {
        this.wifi_ssid = wifi_ssid;
    }

    public String getSitting_position_level() {
        return sp_reminder_sensitivity_level;
    }

    public void setSitting_position_level(String sitting_position_level) {
        this.sp_reminder_sensitivity_level = sitting_position_level;
    }

    public int getLight_level() {
        return light_level;
    }

    public void setLight_level(int light_level) {
        this.light_level = light_level;
    }

    public String getLight_mode() {
        return light_mode;
    }

    public void setLight_mode(String light_mode) {
        this.light_mode = light_mode;
    }

    public boolean getLight_switch() {
        return light_switch;
    }

    public void setLight_switch(boolean light_switch) {
        this.light_switch = light_switch;
    }


    public boolean isSitting_position_switch() {
        return sp_reminder_switch;
    }

    public void setSitting_position_switch(boolean sitting_position_switch) {
        this.sp_reminder_switch = sitting_position_switch;
    }


    public boolean isLight_is_online() {
        return light_is_online;
    }

    public void setLight_is_online(boolean light_is_online) {
        this.light_is_online = light_is_online;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getBindUserId() {
        return bindUserId;
    }

    public void setBindUserId(int bindUserId) {
        this.bindUserId = bindUserId;
    }

    public boolean isIsBinded() {
        return isBinded;
    }

    public void setIsBinded(boolean isBinded) {
        this.isBinded = isBinded;
    }

    public String getInUsingUserInfo() {
        return inUsingUserInfo;
    }

    public void setInUsingUserInfo(String inUsingUserInfo) {
        this.inUsingUserInfo = inUsingUserInfo;
    }

    public String getEquipmentUID() {
        return equipmentUID;
    }

    public void setEquipmentUID(String equipmentUID) {
        this.equipmentUID = equipmentUID;
    }

    public long getUsingOn() {
        return usingOn;
    }

    public void setUsingOn(long usingOn) {
        this.usingOn = usingOn;
    }

    public long getOffUsingOn() {
        return offUsingOn;
    }

    public void setOffUsingOn(long offUsingOn) {
        this.offUsingOn = offUsingOn;
    }

    public long getConnectNetworkTime() {
        return connectNetworkTime;
    }

    public void setConnectNetworkTime(long connectNetworkTime) {
        this.connectNetworkTime = connectNetworkTime;
    }

    public boolean isBinded() {
        return binded;
    }

    public void setBinded(boolean binded) {
        this.binded = binded;
    }

    public int getUnreadCount() {
        return unreadCount;
    }

    public void setUnreadCount(int unreadCount) {
        this.unreadCount = unreadCount;
    }

    public String getSystem_language() {
        return system_language;
    }

    public void setSystem_language(String system_language) {
        this.system_language = system_language;
    }

    public boolean isOffline() {
        return isOffline;
    }

    public void setOffline(boolean offline) {
        isOffline = offline;
    }

    public String getSmart_power_saving_switch() {
        return smart_power_saving_switch;
    }

    public void setSmart_power_saving_switch(String smart_power_saving_switch) {
        this.smart_power_saving_switch = smart_power_saving_switch;
    }

    public String getSedentary_reminder_switch() {
        return sedentary_reminder_switch;
    }

    public void setSedentary_reminder_switch(String sedentary_reminder_switch) {
        this.sedentary_reminder_switch = sedentary_reminder_switch;
    }

    public int getSedentary_reminder_duration() {
        return sedentary_reminder_duration;
    }

    public void setSedentary_reminder_duration(int sedentary_reminder_duration) {
        this.sedentary_reminder_duration = sedentary_reminder_duration;
    }

    public int getMessage_api_version() {
        return message_api_version;
    }

    public void setMessage_api_version(int message_api_version) {
        this.message_api_version = message_api_version;
    }

    public boolean isReadyForCheckUpdate() {
        return readyForCheckUpdate;
    }

    public void setReadyForCheckUpdate(boolean readyForCheckUpdate) {
        this.readyForCheckUpdate = readyForCheckUpdate;
    }

    public String getDevice_mode() {
        return device_mode;
    }

    public void setDevice_mode(String device_mode) {
        this.device_mode = device_mode;
    }
}
