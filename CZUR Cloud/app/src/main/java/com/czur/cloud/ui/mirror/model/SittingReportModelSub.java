package com.czur.cloud.ui.mirror.model;

import com.czur.cloud.ui.auramate.reportfragment.ReportUtil;

import java.io.Serializable;

/**
 * Created by <PERSON> on 2021/3/8.
 */
public class SittingReportModelSub implements Serializable {

    private static final long serialVersionUID = 1L;

    private int id; //"id": 12570,
    private String equipmentUuid;   //"equipmentUuid": "CMR01A2101100001",
    private String fromEnd; //"2020/11/16-2020/11/22",//起始结束时间//"fromEnd": "2021-03-08",
    private String fromEndDate; // 来自fromEnd
    private String localeTime; //"2020-11-21 15:30:00",//本地时间//"localeTime": "2021-03-08 03:09:02",
    private int timezone;  //"":8,//时区//"timezone": 0,
    private long createTime;//"createTime": 1615172084000,
    private int dayUsingDuration;  //"":277, //日平均使用时间//"dayUsingDuration": 10960541,
    private String relationId; //"relationId",//"relationId": 161395493489479017,
    private long usingDuration;//"usingDuration": 10960541,
    private long rightDuration;//"rightDuration": 9036041900,
    private long seriousErrorDuration;//"seriousErrorDuration": 624881920,
    private long mildErrorDuration;//"mildErrorDuration": 510229810,
    private long moderateErrorDuration;//"moderateErrorDuration": 789387370,
    private int rightProportion;//"rightProportion": 84,
    private int seriousProportion;//"seriousProportion": 5,
    private int mildProportion;//"mildProportion": 4,
    private int moderateProportion;//"moderateProportion": 7,
    private String rank;//排名//"rank": null,
    private String rankNo;//排名//"rankNo": null,
    private String trend; //"持平",//趋势//"trend": "持平",
    private String level; //"优良",//"level": "优良",
    private int dayRemindCount; //41,//日平均提醒次数//"dayRemindCount": 1,
    private int totalRemindCount; //41,//总提醒次数//"totalRemindCount": 1,
    private String title; //"title": "2021-03-08日报",
    private int type;//"type": 1,
    private boolean published; ////是否发布//"published": true,
    private long publishTime;////"publishTime": 1615188651000,
    private long updateTime;////"updateTime": 1615188651000,
    private int happy;////"happy": 151,
    private int sedentaryTimeCount;//"sedentaryTimeCount": 0
    private int happyImgCount;////"happyImgCount": 0,
    private int errorImgCount; //"errorImgCount": 0
    private String trendDesc; //"trendDesc": "down",
    private String levelDesc; //"levelDesc": "poor"


    public void initModel(){
        this.id = 0;
        this.title = ReportUtil.getNowDay("yyyy.MM.dd")+"日报";
        this.trend="--";
        this.rank="";
        this.level="";
        this.dayRemindCount=0;
        this.dayUsingDuration=0;
        this.rightProportion=0;
        this.seriousProportion=0;//seriousProportion
        this.moderateProportion=0;//moderateProportion
        this.mildProportion=0;//mildProportion
        this.happy=0;

    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getEquipmentUuid() {
        return equipmentUuid;
    }

    public void setEquipmentUuid(String equipmentUuid) {
        this.equipmentUuid = equipmentUuid;
    }

    public String getFromEnd() {
        return fromEnd;
    }

    public void setFromEnd(String fromEnd) {
        this.fromEnd = fromEnd;
    }

    public String getFromEndDate() {        return fromEndDate;    }

    public void setFromEndDate(String fromEndDate) {        this.fromEndDate = fromEndDate;    }

    public String getLocaleTime() {
        return localeTime;
    }

    public void setLocaleTime(String localeTime) {
        this.localeTime = localeTime;
    }

    public int getTimezone() {
        return timezone;
    }

    public void setTimezone(int timezone) {
        this.timezone = timezone;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public int getDayUsingDuration() {
        return dayUsingDuration;
    }

    public void setDayUsingDuration(int dayUsingDuration) {
        this.dayUsingDuration = dayUsingDuration;
    }

    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    public long getUsingDuration() {
        return usingDuration;
    }

    public void setUsingDuration(long usingDuration) {
        this.usingDuration = usingDuration;
    }

    public long getRightDuration() {
        return rightDuration;
    }

    public void setRightDuration(long rightDuration) {
        this.rightDuration = rightDuration;
    }

    public long getSeriousErrorDuration() {
        return seriousErrorDuration;
    }

    public void setSeriousErrorDuration(long seriousErrorDuration) {
        this.seriousErrorDuration = seriousErrorDuration;
    }

    public long getMildErrorDuration() {
        return mildErrorDuration;
    }

    public void setMildErrorDuration(long mildErrorDuration) {
        this.mildErrorDuration = mildErrorDuration;
    }

    public long getModerateErrorDuration() {
        return moderateErrorDuration;
    }

    public void setModerateErrorDuration(long moderateErrorDuration) {
        this.moderateErrorDuration = moderateErrorDuration;
    }

    public int getRightProportion() {
        return rightProportion;
    }

    public void setRightProportion(int rightProportion) {
        this.rightProportion = rightProportion;
    }

    public int getSeriousProportion() {
        return seriousProportion;
    }

    public void setSeriousProportion(int seriousProportion) {
        this.seriousProportion = seriousProportion;
    }

    public int getMildProportion() {
        return mildProportion;
    }

    public void setMildProportion(int mildProportion) {
        this.mildProportion = mildProportion;
    }

    public int getModerateProportion() {
        return moderateProportion;
    }

    public void setModerateProportion(int moderateProportion) {
        this.moderateProportion = moderateProportion;
    }

    public String getRank() {
        return rank;
    }

    public void setRank(String rank) {
        this.rank = rank;
    }

    public String getRankNo() {
        return rankNo;
    }

    public void setRankNo(String rankNo) {
        this.rankNo = rankNo;
    }

    public String getTrend() {
        return trend;
    }

    public void setTrend(String trend) {
        this.trend = trend;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public int getDayRemindCount() {
        return dayRemindCount;
    }

    public void setDayRemindCount(int dayRemindCount) {
        this.dayRemindCount = dayRemindCount;
    }

    public int getTotalRemindCount() {
        return totalRemindCount;
    }

    public void setTotalRemindCount(int totalRemindCount) {
        this.totalRemindCount = totalRemindCount;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public boolean isPublished() {
        return published;
    }

    public void setPublished(boolean published) {
        this.published = published;
    }

    public long getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(long publishTime) {
        this.publishTime = publishTime;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }

    public int getHappy() {
        return happy;
    }

    public void setHappy(int happy) {
        this.happy = happy;
    }

    public int getSedentaryTimeCount() {
        return sedentaryTimeCount;
    }

    public void setSedentaryTimeCount(int sedentaryTimeCount) {
        this.sedentaryTimeCount = sedentaryTimeCount;
    }

    public int getHappyImgCount() {
        return happyImgCount;
    }

    public void setHappyImgCount(int happyImgCount) {
        this.happyImgCount = happyImgCount;
    }

    public int getErrorImgCount() {
        return errorImgCount;
    }

    public void setErrorImgCount(int errorImgCount) {
        this.errorImgCount = errorImgCount;
    }

    public String getTrendDesc() {        return trendDesc;    }

    public void setTrendDesc(String trendDesc) {        this.trendDesc = trendDesc;    }

    public String getLevelDesc() {        return levelDesc;    }

    public void setLevelDesc(String levelDesc) {        this.levelDesc = levelDesc;    }
}
