package com.czur.cloud.adapter;

/*
 * Created by s<PERSON><PERSON> on 2020/4/2.
 * s<PERSON><PERSON>@czur.com
 */

import android.animation.ObjectAnimator;
import android.animation.PropertyValuesHolder;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.constant.PermissionConstants;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.blankj.utilcode.util.UtilsTransActivity;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.entity.realm.SPReportEntity;
import com.czur.cloud.model.AuraDeviceModel;
import com.czur.cloud.netty.CZURMessageConstants;
import com.czur.cloud.netty.core.CZURTcpClient;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.auramate.AuraMateRemoteVideoActivity;
import com.czur.cloud.ui.auramate.AuraMateReportActivity;
import com.czur.cloud.ui.auramate.AuraMateReportNewActivity;
import com.czur.cloud.ui.auramate.AuraMateSettingActivity;
import com.czur.cloud.ui.auramate.AuraMateSittingActivity;
import com.czur.cloud.ui.auramate.SittingPositionMenuActivity;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.CustomClickListener;
import com.czur.cloud.ui.component.seekbar.BubbleSeekBar;
import com.github.iielse.switchbutton.SwitchView;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import io.realm.Realm;
import io.realm.RealmChangeListener;
import io.realm.RealmResults;

public class AuraMateEquipmentAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    public final static int MIN_CLICK_TIME = 1000;
    private long lastTime = 0;

    private static final int TYPE_NORMAL = 0;
    private static final int TYPE_ADD_EQUIPMENT = 1;
    private List<AuraDeviceModel> datas;
    private Context context;
    private final CZURTcpClient client;
    private Realm realm;
    private int lastProgress;
    private static long lastClickTime = 0;

    public AuraMateEquipmentAdapter(BaseActivity activity, List<AuraDeviceModel> datas, Realm realm) {
        this.realm = realm;
        this.context = activity;
        client = CZURTcpClient.getInstance();
        if (datas == null) {
            this.datas = new ArrayList<>();
        } else {
            this.datas = datas;
        }
    }

    public void refreshData(List<AuraDeviceModel> datas) {
        if (datas != null) {
            this.datas = datas;
            notifyDataSetChanged();
        }
    }


    @NotNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NotNull ViewGroup parent, int viewType) {
        if (viewType == TYPE_NORMAL) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_aura_mate_device, parent, false);
            return new NormalViewHolder(view);
        } else {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_aura_mate_empty, parent, false);
            return new AddEquipmentViewHolder(view);
        }
    }


    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, final int position) {
        if (holder instanceof NormalViewHolder) {
            final NormalViewHolder mHolder = (NormalViewHolder) holder;
            mHolder.mItem = datas.get(position);
            mHolder.scroll.setSmoothScrollingEnabled(true);
            mHolder.loadingImg.clearAnimation();
            PropertyValuesHolder valuesHolder = PropertyValuesHolder.ofFloat("rotation", 0, 360);
            ObjectAnimator objectAnimator = ObjectAnimator.ofPropertyValuesHolder(mHolder.loadingImg, valuesHolder);
            objectAnimator.setDuration(2000);
            objectAnimator.setInterpolator(new LinearInterpolator());
            objectAnimator.setRepeatCount(ValueAnimator.INFINITE);
            objectAnimator.start();

            mHolder.auraHomeReportRl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    mHolder.reportCount.setVisibility(View.GONE);
                    Intent intent;
                    //海外暂时没有日周月报
                    if (BuildConfig.IS_OVERSEAS) {
                        intent = new Intent(context, AuraMateReportActivity.class);
                    }else {
                        intent = new Intent(context, AuraMateReportNewActivity.class);
                    }
                    intent.putExtra("equipmentId", mHolder.mItem.getEquipmentUID());
                    ActivityUtils.startActivity(intent);
                }
            });

            if (mHolder.mItem.isShowLoading()) {
                mHolder.loadingRl.setVisibility(View.VISIBLE);
                return;
            } else {
                mHolder.loadingRl.setVisibility(View.GONE);
            }
            mHolder.auraHomeDeviceName.setText(mHolder.mItem.getAlias());
            if (mHolder.mItem.isSitting_position_switch()) {
                mHolder.sittingSwitchTv.setText(context.getString(R.string.open));
            } else {
                mHolder.sittingSwitchTv.setText(context.getString(R.string.close));
            }

            RealmResults<SPReportEntity> spReportEntities = realm.where(SPReportEntity.class).equalTo("equipmentUuid", mHolder.mItem.getEquipmentUID()).equalTo("haveRead", 0).findAllAsync();
            spReportEntities.addChangeListener(new RealmChangeListener<RealmResults<SPReportEntity>>() {
                @Override
                public void onChange(RealmResults<SPReportEntity> spReportEntities) {
                    if (spReportEntities != null && spReportEntities.size() > 0) {
                        mHolder.reportCount.setVisibility(View.VISIBLE);
                        mHolder.reportCount.setText(spReportEntities.size() + "");
                    } else {
                        mHolder.reportCount.setVisibility(View.GONE);
                    }
                }
            });

            mHolder.auraHomeSittingPositionRl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Intent intent = new Intent(context, SittingPositionMenuActivity.class);
                    intent.putExtra("isCalibrate", mHolder.mItem.getHas_calibrated_sp() == 1);
                    intent.putExtra("equipmentId", mHolder.mItem.getEquipmentUID() + "");
                    intent.putExtra("sittingPositionLevel", mHolder.mItem.getSitting_position_level() + "");
                    intent.putExtra("volume", mHolder.mItem.getSp_reminder_sensitivity_volume());
                    intent.putExtra("sittingPositionSwitch", mHolder.mItem.isSitting_position_switch());
                    ActivityUtils.startActivity(intent);
                }
            });

            mHolder.auraHomeSittingLongRl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Intent intent = new Intent(context, AuraMateSittingActivity.class);
                    intent.putExtra("equipmentId", mHolder.mItem.getEquipmentUID());
                    intent.putExtra("sedentaryReminderSwitch", mHolder.mItem.getSedentary_reminder_switch());
                    intent.putExtra("sedentaryReminderDuration", mHolder.mItem.getSedentary_reminder_duration());
                    ActivityUtils.startActivity(intent);
                }
            });

            if (!TextUtils.isEmpty(mHolder.mItem.getDevice_mode()) && mHolder.mItem.getDevice_mode().equals("USB-PC")) {
                setPCMode(mHolder);
            } else {
                setNormalMode(mHolder);
            }

            if (mHolder.mItem.isOffline()) {
                mHolder.loadingRl.setVisibility(View.GONE);
                setOffline(mHolder);
            } else {
                if (mHolder.mItem.isLight_is_online()) {
                    setOnline(mHolder);
                } else {
                    mHolder.loadingRl.setVisibility(View.VISIBLE);
                    setOffline(mHolder);
                }
            }

            if (mHolder.mItem.getLight_switch()) {
                mHolder.auraHomeOffLl.setVisibility(View.GONE);
                mHolder.auraHomeOnlineLl.setVisibility(View.VISIBLE);
            } else {
                mHolder.auraHomeOffLl.setVisibility(View.VISIBLE);
                mHolder.auraHomeOnlineLl.setVisibility(View.GONE);
            }


            if (mHolder.mItem.isSitting_position_switch()) {
                mHolder.sittingSwitchTv.setText(context.getResources().getString(R.string.open));
            } else {
                mHolder.sittingSwitchTv.setText(context.getResources().getString(R.string.close));
            }

            if (!TextUtils.isEmpty(mHolder.mItem.getSmart_power_saving_switch()) && mHolder.mItem.getSmart_power_saving_switch().equals("On")) {
                mHolder.smartPowerSwitch.setOpened(true);
            } else {
                mHolder.smartPowerSwitch.setOpened(false);
            }


            if (!TextUtils.isEmpty(mHolder.mItem.getSedentary_reminder_switch()) && mHolder.mItem.getSedentary_reminder_switch().equals("On")) {
                mHolder.sittingLongSwitchTv.setText(context.getResources().getString(R.string.open));
            } else {
                mHolder.sittingLongSwitchTv.setText(context.getResources().getString(R.string.close));
            }
            switchMode(mHolder);
            switchLevel(mHolder);
            mHolder.seekBar.setOnProgressChangedListener(new BubbleSeekBar.OnProgressChangedListener() {
                @Override
                public void onProgressActionDown(BubbleSeekBar bubbleSeekBar, int progress, float progressFloat, boolean fromUser) {
                }

                @Override
                public void onProgressChanged(BubbleSeekBar bubbleSeekBar, int progress, float progressFloat, boolean fromUser) {
                }

                @Override
                public void getProgressOnActionUp(BubbleSeekBar bubbleSeekBar, int progress, float progressFloat) {
                }

                @Override
                public void getProgressOnFinally(BubbleSeekBar bubbleSeekBar, int progress, float progressFloat, boolean fromUser) {
                    if (fromUser && progress != lastProgress) {
                        switchLevel(progress, getCurLevel(lastProgress), mHolder, fromUser);
                        lastProgress = progress;
                    }
                }
            });
            mHolder.auraHomeOnBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    client.lightSwitch(context, mHolder.mItem.getEquipmentUID(), CZURMessageConstants.LightSwitch.LIGHT_SWITCH_ON.getLightSwitch()
                            , CZURMessageConstants.LightSwitch.LIGHT_SWITCH_OFF.getLightSwitch());
                    mHolder.auraHomeOffLl.setVisibility(View.GONE);
                    mHolder.auraHomeOnlineLl.setVisibility(View.VISIBLE);
                }
            });
            mHolder.auraHomeOffBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    client.lightSwitch(context, mHolder.mItem.getEquipmentUID(), CZURMessageConstants.LightSwitch.LIGHT_SWITCH_OFF.getLightSwitch()
                            , CZURMessageConstants.LightSwitch.LIGHT_SWITCH_ON.getLightSwitch());
                    mHolder.auraHomeOffLl.setVisibility(View.VISIBLE);
                    mHolder.auraHomeOnlineLl.setVisibility(View.GONE);
                }
            });
            mHolder.auraHomePlusBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mHolder.mItem.getLight_level() < 60) {
                        mHolder.mItem.setLight_level(mHolder.mItem.getLight_level() + 10);
                        mHolder.seekBar.setProgress(mHolder.mItem.getLight_level());
                        switchLevel(mHolder.mItem.getLight_level(), getCurLevel(mHolder.mItem.getLight_level() - 10), mHolder, true);
                    }
                }
            });
            mHolder.auraHomeMinusBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mHolder.mItem.getLight_level() > 10) {
                        mHolder.mItem.setLight_level(mHolder.mItem.getLight_level() - 10);
                        mHolder.seekBar.setProgress(mHolder.mItem.getLight_level());
                        switchLevel(mHolder.mItem.getLight_level(), getCurLevel(mHolder.mItem.getLight_level() + 10), mHolder, true);
                    }
                }
            });
            mHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onItemClickListener != null) {
                        onItemClickListener.onItemClick(position, mHolder.mItem);
                    }
                }
            });

            mHolder.auraHomeSettingLl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    long cTime = Calendar.getInstance().getTimeInMillis();
                    if (cTime - lastTime < MIN_CLICK_TIME){
                        return;
                    }
                    lastTime = cTime;

                    Intent intent = new Intent(context, AuraMateSettingActivity.class);
                    intent.putExtra("isShared", !(mHolder.mItem.getBindUserId() + "").equals(UserPreferences.getInstance(context).getUserId()));
                    intent.putExtra("isOnline", mHolder.mItem.isLight_is_online());
                    intent.putExtra("ownerId", mHolder.mItem.getBindUserId() + "");
                    intent.putExtra("name", mHolder.mItem.getAlias());
                    intent.putExtra("deviceId", mHolder.mItem.getId() + "");
                    intent.putExtra("language", mHolder.mItem.getSystem_language());
                    intent.putExtra("equipmentId", mHolder.mItem.getEquipmentUID());
                    if (mHolder.mItem.isLight_is_online()) {
                        intent.putExtra("isNeedUpdate", mHolder.mItem.getFirmware_need_update().equals("1"));
                        intent.putExtra("currentVersion", mHolder.mItem.getFirmware_current_version());
                        intent.putExtra("ssid", mHolder.mItem.getWifi_ssid());
                        intent.putExtra("updateVersion", mHolder.mItem.getFirmware_update_version());
                    }
                    ActivityUtils.startActivity(intent);
                }
            });

            mHolder.auraHomeEyeRl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    openEye(mHolder, true, mHolder.mItem.getLight_mode());
                }
            });
            mHolder.auraHomeReadRl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    openRead(mHolder, true, mHolder.mItem.getLight_mode());
                }
            });
            mHolder.auraHomeComputerRl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    openComputer(mHolder, true, mHolder.mItem.getLight_mode());
                }
            });
            mHolder.auraHomeLightRl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    openLight(mHolder, true, mHolder.mItem.getLight_mode());
                }
            });
            mHolder.auraHomeRemoteVideoRl.setOnClickListener(new CustomClickListener() {
                @Override
                protected void onSingleClick() {
                    long time = System.currentTimeMillis();
                    if (time - lastClickTime > 4000) {//判断系统时间差是否小于点击间隔时间
                        lastClickTime = time;
                        openCamera(mHolder.mItem.getEquipmentUID(), mHolder.mItem.getBindUserId() + "");
                    }
                }

                @Override
                protected void onFastClick() {

                }
            });

            mHolder.scroll.setScrollY(mHolder.mItem.getScrollY());
            mHolder.scroll.setOnScrollChangeListener(new NestedScrollView.OnScrollChangeListener() {
                @Override
                public void onScrollChange(NestedScrollView v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
                    if (onItemScrollListener != null) {
                        onItemScrollListener.onItemScroll(position, scrollY);
                    }
                    mHolder.mItem.setScrollY(scrollY);
                }
            });
            //智能省电开关
            mHolder.smartPowerSwitch.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    boolean isOpened = mHolder.smartPowerSwitch.isOpened();
                    client.smartPowerSwitch(context, mHolder.mItem.getEquipmentUID(), isOpened ? "On" : "Off", isOpened ? "Off" : "On");
                }
            });

        } else if (holder instanceof AddEquipmentViewHolder) {
            AddEquipmentViewHolder mHolder = (AddEquipmentViewHolder) holder;
            mHolder.addAuraMateTv.setText(datas.size() > 0 ? R.string.aura_home_add_text_again : R.string.aura_home_add_text);
            mHolder.addView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onItemAddClickListener != null) {
                        onItemAddClickListener.onItemAddClick(position);
                    }
                }
            });
        }
    }


    private void setPCMode(NormalViewHolder mHolder) {
        mHolder.auraHomeRemoteVideoRl.setEnabled(false);
        mHolder.auraHomeRemoteVideoRl.setClickable(false);
        mHolder.auraHomeRemoteVideoRl.setAlpha(0.5F);
        mHolder.auraHomePcLl.setVisibility(View.VISIBLE);
        mHolder.auraHomeSettingLl.setVisibility(View.GONE);
    }

    private void setNormalMode(NormalViewHolder mHolder) {
        mHolder.auraHomePcLl.setVisibility(View.GONE);
        mHolder.auraHomeSettingLl.setVisibility(View.VISIBLE);
    }

    private void setOffline(NormalViewHolder mHolder) {
        mHolder.auraHomeStateImg.setImageResource(R.drawable.circle_with_gray);
        mHolder.auraHomeStateTv.setText(context.getResources().getString(R.string.offline));
        mHolder.auraHomeOfflineLl.setVisibility(View.VISIBLE);
        mHolder.auraHomeRemoteVideoRl.setEnabled(false);
        mHolder.auraHomeRemoteVideoRl.setClickable(false);
        mHolder.auraHomeRemoteVideoRl.setAlpha(0.5F);

        mHolder.positionTv.setTextColor(context.getColor(R.color.blue_94d7eb));
        mHolder.sittingSwitchTv.setTextColor(context.getColor(R.color.blue_94d7eb));
        mHolder.sittingPositionImg.setImageResource(R.mipmap.offline_right_img);
        mHolder.auraHomeSittingPositionRl.setClickable(false);
        mHolder.auraHomeSittingPositionRl.setEnabled(false);
        mHolder.auraHomeSittingPositionRl.setAlpha(0.5F);

        mHolder.sittingLongTv.setTextColor(context.getColor(R.color.blue_94d7eb));
        mHolder.sittingLongSwitchTv.setTextColor(context.getColor(R.color.blue_94d7eb));
        mHolder.sittingLongImg.setImageResource(R.mipmap.offline_right_img);
        mHolder.auraHomeSittingLongRl.setClickable(false);
        mHolder.auraHomeSittingLongRl.setEnabled(false);
        mHolder.auraHomeSittingLongRl.setAlpha(0.5F);
        mHolder.smartPowerTv.setTextColor(context.getColor(R.color.blue_94d7eb));
        mHolder.smartPowerDetailTv.setTextColor(context.getColor(R.color.blue_94d7eb));
        mHolder.smartPowerSwitch.toggleSwitch(false);
        mHolder.auraHomeSmartPowerRl.setClickable(false);
        mHolder.auraHomeSmartPowerRl.setEnabled(false);
        mHolder.auraHomeSmartPowerRl.setAlpha(0.5F);
        mHolder.loadingRl.setVisibility(View.GONE);
    }

    private void setOnline(NormalViewHolder mHolder) {
        mHolder.auraHomeStateImg.setImageResource(R.drawable.circle_with_green);
        mHolder.auraHomeStateTv.setText(context.getResources().getString(R.string.online));
        mHolder.auraHomeOfflineLl.setVisibility(View.GONE);
        if (TextUtils.isEmpty(mHolder.mItem.getDevice_mode()) || mHolder.mItem.getDevice_mode().equals("Normal")) {
            mHolder.auraHomeRemoteVideoRl.setEnabled(true);
            mHolder.auraHomeRemoteVideoRl.setClickable(true);
            mHolder.auraHomeRemoteVideoRl.setAlpha(1F);
        }
        mHolder.positionTv.setTextColor(context.getColor(R.color.white));
        mHolder.sittingSwitchTv.setTextColor(context.getColor(R.color.white));
        mHolder.sittingPositionImg.setImageResource(R.mipmap.white_arrow);
        mHolder.auraHomeSittingPositionRl.setClickable(true);
        mHolder.auraHomeSittingPositionRl.setEnabled(true);
        mHolder.auraHomeSittingPositionRl.setAlpha(1F);

        mHolder.sittingLongTv.setTextColor(context.getColor(R.color.white));
        mHolder.sittingLongSwitchTv.setTextColor(context.getColor(R.color.white));
        mHolder.sittingLongImg.setImageResource(R.mipmap.white_arrow);
        mHolder.auraHomeSittingLongRl.setClickable(true);
        mHolder.auraHomeSittingLongRl.setEnabled(true);
        mHolder.auraHomeSittingLongRl.setAlpha(1F);

        mHolder.smartPowerTv.setTextColor(context.getColor(R.color.white));
        mHolder.smartPowerDetailTv.setTextColor(context.getColor(R.color.white));
        mHolder.auraHomeSmartPowerRl.setClickable(true);
        mHolder.auraHomeSmartPowerRl.setEnabled(true);
        mHolder.auraHomeSmartPowerRl.setAlpha(1F);

        mHolder.loadingRl.setVisibility(View.GONE);
    }

    /**
     * @des: 申请权限并且打开相机页
     * @params:
     * @return:
     */
    private String getCurLevel(int level) {
        if (level == 10) {
            return CZURMessageConstants.LightLevel.LEVEL_1.getLevel();
        } else if (level == 20) {
            return CZURMessageConstants.LightLevel.LEVEL_2.getLevel();
        } else if (level == 30) {
            return CZURMessageConstants.LightLevel.LEVEL_3.getLevel();
        } else if (level == 40) {
            return CZURMessageConstants.LightLevel.LEVEL_4.getLevel();
        } else if (level == 50) {
            return CZURMessageConstants.LightLevel.LEVEL_5.getLevel();
        } else {
            return CZURMessageConstants.LightLevel.LEVEL_6.getLevel();
        }
    }

    private void openCamera(String equipmentUID, String ownerId) {
        PermissionUtils.permission(PermissionConstants.CAMERA, PermissionConstants.MICROPHONE)
                .rationale(new PermissionUtils.OnRationaleListener() {
                    @Override
                    public void rationale(UtilsTransActivity activity, ShouldRequest shouldRequest) {
                        ToastUtils.showShort(R.string.denied_camera);
                        shouldRequest.again(true);
                    }
                })
                .callback(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(List<String> permissionsGranted) {
                        Intent intent = new Intent(context, AuraMateRemoteVideoActivity.class);
                        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS);
                        intent.putExtra("ownerId", ownerId);
                        intent.putExtra("isCallIn", false);
                        intent.putExtra("udidFrom", equipmentUID);
                        intent.putExtra("equipmentId", equipmentUID);
                        ActivityUtils.startActivity(intent);
                    }

                    @Override
                    public void onDenied(List<String> permissionsDeniedForever,
                                         List<String> permissionsDenied) {
                        ToastUtils.showShort(R.string.denied_camera);
                    }
                })
                .theme(new PermissionUtils.ThemeCallback() {
                    @Override
                    public void onActivityCreate(Activity activity) {
                        ScreenUtils.setFullScreen(activity);
                    }
                })
                .request();
    }

    private void switchLevel(int progress, String currentProgress, NormalViewHolder mHolder, boolean fromUser) {
        switch (progress) {
            case 10:
                mHolder.mItem.setLight_level(progress);
                if (fromUser) {
                    client.lightLevel(context, mHolder.mItem.getEquipmentUID(), CZURMessageConstants.LightLevel.LEVEL_1.getLevel(), currentProgress);
                }
                mHolder.auraHomeLightLevelTv.setText(context.getResources().getString(R.string.level_1));
                break;
            case 20:
                mHolder.mItem.setLight_level(progress);
                if (fromUser) {
                    client.lightLevel(context, mHolder.mItem.getEquipmentUID(), CZURMessageConstants.LightLevel.LEVEL_2.getLevel(), currentProgress);
                }
                mHolder.auraHomeLightLevelTv.setText(context.getResources().getString(R.string.level_2));
                break;
            case 30:
                mHolder.mItem.setLight_level(progress);
                if (fromUser) {
                    client.lightLevel(context, mHolder.mItem.getEquipmentUID(), CZURMessageConstants.LightLevel.LEVEL_3.getLevel(), currentProgress);
                }
                mHolder.auraHomeLightLevelTv.setText(context.getResources().getString(R.string.level_3));
                break;
            case 40:
                mHolder.mItem.setLight_level(progress);
                if (fromUser) {
                    client.lightLevel(context, mHolder.mItem.getEquipmentUID(), CZURMessageConstants.LightLevel.LEVEL_4.getLevel(), currentProgress);
                }
                mHolder.auraHomeLightLevelTv.setText(context.getResources().getString(R.string.level_4));
                break;
            case 50:
                mHolder.mItem.setLight_level(progress);
                if (fromUser) {
                    client.lightLevel(context, mHolder.mItem.getEquipmentUID(), CZURMessageConstants.LightLevel.LEVEL_5.getLevel(), currentProgress);
                }
                mHolder.auraHomeLightLevelTv.setText(context.getResources().getString(R.string.level_5));
                break;
            case 60:
                mHolder.mItem.setLight_level(progress);
                if (fromUser) {
                    client.lightLevel(context, mHolder.mItem.getEquipmentUID(), CZURMessageConstants.LightLevel.LEVEL_6.getLevel(), currentProgress);
                }
                mHolder.auraHomeLightLevelTv.setText(context.getResources().getString(R.string.level_6));
                break;
            default:
                mHolder.mItem.setLight_level(10);
                if (fromUser) {
                    client.lightLevel(context, mHolder.mItem.getEquipmentUID(), CZURMessageConstants.LightLevel.LEVEL_1.getLevel(), currentProgress);
                }
                mHolder.auraHomeLightLevelTv.setText(context.getResources().getString(R.string.level_1));
                break;
        }
    }

    private void switchLevel(NormalViewHolder mHolder) {
        switch (mHolder.mItem.getLight_level()) {
            case 10:
                mHolder.seekBar.setProgress(mHolder.mItem.getLight_level());
                mHolder.auraHomeLightLevelTv.setText(context.getResources().getString(R.string.level_1));
                break;
            case 20:
                mHolder.seekBar.setProgress(mHolder.mItem.getLight_level());
                mHolder.auraHomeLightLevelTv.setText(context.getResources().getString(R.string.level_2));
                break;
            case 30:
                mHolder.seekBar.setProgress(mHolder.mItem.getLight_level());
                mHolder.auraHomeLightLevelTv.setText(context.getResources().getString(R.string.level_3));
                break;
            case 40:
                mHolder.seekBar.setProgress(mHolder.mItem.getLight_level());
                mHolder.auraHomeLightLevelTv.setText(context.getResources().getString(R.string.level_4));
                break;
            case 50:
                mHolder.seekBar.setProgress(mHolder.mItem.getLight_level());
                mHolder.auraHomeLightLevelTv.setText(context.getResources().getString(R.string.level_5));
                break;
            case 60:
                mHolder.seekBar.setProgress(mHolder.mItem.getLight_level());
                mHolder.auraHomeLightLevelTv.setText(context.getResources().getString(R.string.level_6));
                break;
            default:
                mHolder.seekBar.setProgress(10);
                mHolder.auraHomeLightLevelTv.setText(context.getResources().getString(R.string.level_1));
                break;
        }
    }

    private void switchMode(NormalViewHolder mHolder) {
        if (mHolder.mItem.getLight_mode() != null) {
            if (mHolder.mItem.getLight_mode().equals(CZURMessageConstants.LightMode.LIGHT_MODE_Read.getMode())) {
                openRead(mHolder, false, "");
            } else if (mHolder.mItem.getLight_mode().equals(CZURMessageConstants.LightMode.LIGHT_MODE_COMPUTER.getMode())) {
                openComputer(mHolder, false, "");
            } else if (mHolder.mItem.getLight_mode().equals(CZURMessageConstants.LightMode.LIGHT_MODE_NATURAL.getMode())) {
                openLight(mHolder, false, "");
            } else if (mHolder.mItem.getLight_mode().equals(CZURMessageConstants.LightMode.LIGHT_MODE_NIGHT.getMode())) {
                openEye(mHolder, false, "");
            } else {
                openEye(mHolder, false, "");
            }
        } else {
            openEye(mHolder, false, "");
        }
    }


    @Override
    public int getItemViewType(int position) {
        if (datas.size() == 0) {
            return TYPE_ADD_EQUIPMENT;
        } else if (datas.size() == 1) {
            return TYPE_NORMAL;
        } else {
            if (position >= 0 && position < datas.size()) {
                return TYPE_NORMAL;
            } else {
                return TYPE_ADD_EQUIPMENT;
            }
        }

    }

    @Override
    public int getItemCount() {
//        if (datas.size() == 1) {
//            return datas.size();
//        } else {
//            return datas.size() + 1;
//        }
        if (datas.size() < 1){
            return  1;
        }else {
            return datas.size();
        }
    }

    private void openEye(NormalViewHolder mHolder, boolean isButton, String curMode) {
        if (isButton) {
            client.lightMode(context, mHolder.mItem.getEquipmentUID(), CZURMessageConstants.LightMode.LIGHT_MODE_NIGHT.getMode(), curMode);
        }
        mHolder.auraHomeEyeImg.setSelected(true);
        mHolder.auraHomeComputerImg.setSelected(false);
        mHolder.auraHomeReadImg.setSelected(false);
        mHolder.auraHomeLightImg.setSelected(false);

        mHolder.auraHomeEyeTv.setTypeface(Typeface.DEFAULT_BOLD);
        mHolder.auraHomeComputerTv.setTypeface(Typeface.DEFAULT);
        mHolder.auraHomeReadTv.setTypeface(Typeface.DEFAULT);
        mHolder.auraHomeLightTv.setTypeface(Typeface.DEFAULT);
    }

    private void openLight(NormalViewHolder mHolder, boolean isButton, String curMode) {
        if (isButton) {
            client.lightMode(context, mHolder.mItem.getEquipmentUID(), CZURMessageConstants.LightMode.LIGHT_MODE_NATURAL.getMode(), curMode);
        }
        mHolder.auraHomeEyeTv.setTypeface(Typeface.DEFAULT);
        mHolder.auraHomeComputerTv.setTypeface(Typeface.DEFAULT);
        mHolder.auraHomeReadTv.setTypeface(Typeface.DEFAULT);
        mHolder.auraHomeLightTv.setTypeface(Typeface.DEFAULT_BOLD);

        mHolder.auraHomeEyeImg.setSelected(false);
        mHolder.auraHomeComputerImg.setSelected(false);
        mHolder.auraHomeReadImg.setSelected(false);
        mHolder.auraHomeLightImg.setSelected(true);
    }

    private void openComputer(NormalViewHolder mHolder, boolean isButton, String curMode) {
        if (isButton) {
            client.lightMode(context, mHolder.mItem.getEquipmentUID(), CZURMessageConstants.LightMode.LIGHT_MODE_COMPUTER.getMode(), curMode);
        }
        mHolder.auraHomeEyeImg.setSelected(false);
        mHolder.auraHomeComputerImg.setSelected(true);
        mHolder.auraHomeReadImg.setSelected(false);
        mHolder.auraHomeLightImg.setSelected(false);

        mHolder.auraHomeEyeTv.setTypeface(Typeface.DEFAULT);
        mHolder.auraHomeComputerTv.setTypeface(Typeface.DEFAULT_BOLD);
        mHolder.auraHomeReadTv.setTypeface(Typeface.DEFAULT);
        mHolder.auraHomeLightTv.setTypeface(Typeface.DEFAULT);
    }

    private void openRead(NormalViewHolder mHolder, boolean isButton, String curMode) {
        if (isButton) {
            client.lightMode(context, mHolder.mItem.getEquipmentUID(), CZURMessageConstants.LightMode.LIGHT_MODE_Read.getMode(), curMode);
        }
        mHolder.auraHomeEyeImg.setSelected(false);
        mHolder.auraHomeComputerImg.setSelected(false);
        mHolder.auraHomeReadImg.setSelected(true);
        mHolder.auraHomeLightImg.setSelected(false);
        mHolder.auraHomeEyeTv.setTypeface(Typeface.DEFAULT);
        mHolder.auraHomeComputerTv.setTypeface(Typeface.DEFAULT);
        mHolder.auraHomeReadTv.setTypeface(Typeface.DEFAULT_BOLD);
        mHolder.auraHomeLightTv.setTypeface(Typeface.DEFAULT);
    }


    public static class NormalViewHolder extends RecyclerView.ViewHolder {
        public final View mView;
        RelativeLayout itemView;
        ImageView auraHomeItemShadowImg;
        RelativeLayout auraHomeRemoteVideoRl;
        AuraDeviceModel mItem;
        //打开
        ImageView auraHomeStateImg;
        TextView auraHomeStateTv;
        TextView auraHomeDeviceName;
        LinearLayout auraHomeSettingLl;
        RelativeLayout auraHomeOffBtn;
        ImageView auraHomeMinusBtn;
        TextView auraHomeLightLevelTv;
        ImageView auraHomePlusBtn;
        RelativeLayout auraHomeEyeRl;
        ImageView auraHomeEyeImg;
        TextView auraHomeEyeTv;
        RelativeLayout auraHomeLightRl;
        ImageView auraHomeLightImg;
        TextView auraHomeLightTv;
        RelativeLayout auraHomeReadRl;
        ImageView auraHomeReadImg;
        TextView auraHomeReadTv;
        RelativeLayout auraHomeComputerRl;
        ImageView auraHomeComputerImg;
        TextView auraHomeComputerTv;
        RelativeLayout auraHomeSittingPositionRl;
        RelativeLayout auraHomeSittingLongRl;
        RelativeLayout auraHomeSmartPowerRl;
        BubbleSeekBar seekBar;
        LinearLayout auraHomeOnlineLl;
        LinearLayout auraHomeOffLl;
        LinearLayout auraHomeOfflineLl;
        LinearLayout auraHomePcLl;
        TextView positionTv;
        TextView auraHomeOnBtn;
        TextView sittingSwitchTv;
        ImageView sittingPositionImg;
        RelativeLayout auraHomeReportRl;
        TextView reportCount;
        NestedScrollView scroll;
        RelativeLayout loadingRl;
        ImageView loadingImg;

        TextView sittingLongTv;
        TextView sittingLongSwitchTv;
        ImageView sittingLongImg;

        TextView smartPowerTv;
        TextView smartPowerDetailTv;
        SwitchView smartPowerSwitch;

        NormalViewHolder(View view) {
            super(view);
            mView = view;
            itemView = (RelativeLayout) view.findViewById(R.id.aura_home_device_rl);
            scroll = view.findViewById(R.id.scroll);
            //视频
            auraHomeItemShadowImg = (ImageView) view.findViewById(R.id.aura_home_item_shadow_img);
            auraHomeRemoteVideoRl = (RelativeLayout) view.findViewById(R.id.aura_home_remote_video_rl);
            auraHomeRemoteVideoRl.setEnabled(false);
            auraHomeRemoteVideoRl.setClickable(false);
            auraHomeRemoteVideoRl.setAlpha(0.5F);
            //关闭
            auraHomeOnlineLl = (LinearLayout) view.findViewById(R.id.aura_home_online_ll);
            auraHomeOffLl = (LinearLayout) view.findViewById(R.id.aura_home_off_ll);
            auraHomePcLl = view.findViewById(R.id.aura_home_pc_ll);
            auraHomeOfflineLl = (LinearLayout) view.findViewById(R.id.aura_home_offline_ll);
            auraHomeOnBtn = view.findViewById(R.id.aura_home_on_btn);
            //坐姿提醒
            positionTv = (TextView) view.findViewById(R.id.position_tv);
            sittingSwitchTv = (TextView) view.findViewById(R.id.sitting_switch_tv);
            sittingPositionImg = (ImageView) view.findViewById(R.id.sitting_position_img);
            //久坐提醒
            sittingLongTv = (TextView) view.findViewById(R.id.sitting_long_tv);
            sittingLongSwitchTv = (TextView) view.findViewById(R.id.sitting_long_switch_tv);
            sittingLongImg = (ImageView) view.findViewById(R.id.sitting_long_img);

            //智能省电
            smartPowerTv = view.findViewById(R.id.smart_power_tv);
            smartPowerDetailTv = view.findViewById(R.id.smart_power_detail_tv);
            smartPowerSwitch = view.findViewById(R.id.smart_power_switch);

            seekBar = (BubbleSeekBar) view.findViewById(R.id.seekBar);
            auraHomeStateImg = (ImageView) view.findViewById(R.id.aura_home_state_img);
            auraHomeStateTv = (TextView) view.findViewById(R.id.aura_home_state_tv);
            auraHomeDeviceName = (TextView) view.findViewById(R.id.aura_home_device_name);
            auraHomeSettingLl = (LinearLayout) view.findViewById(R.id.aura_home_setting_ll);
            auraHomeOffBtn = (RelativeLayout) view.findViewById(R.id.aura_home_off_btn);
            auraHomeMinusBtn = (ImageView) view.findViewById(R.id.aura_home_minus_btn);
            auraHomeLightLevelTv = (TextView) view.findViewById(R.id.aura_home_light_level_tv);
            auraHomePlusBtn = (ImageView) view.findViewById(R.id.aura_home_plus_btn);
            auraHomeEyeRl = (RelativeLayout) view.findViewById(R.id.aura_home_eye_rl);
            auraHomeEyeImg = (ImageView) view.findViewById(R.id.aura_home_eye_img);
            auraHomeEyeTv = (TextView) view.findViewById(R.id.aura_home_eye_tv);
            auraHomeLightRl = (RelativeLayout) view.findViewById(R.id.aura_home_light_rl);
            auraHomeLightImg = (ImageView) view.findViewById(R.id.aura_home_light_img);
            auraHomeLightTv = (TextView) view.findViewById(R.id.aura_home_light_tv);
            auraHomeReadRl = (RelativeLayout) view.findViewById(R.id.aura_home_read_rl);
            auraHomeReadImg = (ImageView) view.findViewById(R.id.aura_home_read_img);
            auraHomeReadTv = (TextView) view.findViewById(R.id.aura_home_read_tv);
            auraHomeComputerRl = (RelativeLayout) view.findViewById(R.id.aura_home_computer_rl);
            auraHomeComputerImg = (ImageView) view.findViewById(R.id.aura_home_computer_img);
            auraHomeComputerTv = (TextView) view.findViewById(R.id.aura_home_computer_tv);
            auraHomeSittingPositionRl = (RelativeLayout) view.findViewById(R.id.aura_home_sitting_position_rl);
            auraHomeSittingLongRl = (RelativeLayout) view.findViewById(R.id.aura_home_long_sitting_rl);
            auraHomeSmartPowerRl = (RelativeLayout) view.findViewById(R.id.aura_home_smart_rl);
            auraHomeReportRl = (RelativeLayout) view.findViewById(R.id.aura_home_report_rl);
            reportCount = (TextView) view.findViewById(R.id.report_count);
            loadingRl = (RelativeLayout) view.findViewById(R.id.loading_rl);
            loadingImg = (ImageView) view.findViewById(R.id.loading_img);
        }
    }

    public static class AddEquipmentViewHolder extends RecyclerView.ViewHolder {
        public final View mView;
        RelativeLayout itemView;
        LinearLayout addView;
        TextView addAuraMateTv;

        AddEquipmentViewHolder(View view) {
            super(view);
            mView = view;
            addAuraMateTv = (TextView) view.findViewById(R.id.add_aura_mate_tv);
            itemView = (RelativeLayout) view.findViewById(R.id.aura_mate_empty_rl);
            addView = (LinearLayout) view.findViewById(R.id.aura_home_empty_inner_rl);
        }
    }

    public OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void onItemClick(int position, AuraDeviceModel AuraDeviceModel);
    }

    private OnItemAddClickListener onItemAddClickListener;

    public void setOnItemAddClickListener(OnItemAddClickListener onItemAddClickListener) {
        this.onItemAddClickListener = onItemAddClickListener;
    }

    public interface OnItemAddClickListener {
        void onItemAddClick(int position);
    }

    private OnVideoItemClickListener onVideoItemClickListener;

    public void setOnVideoItemClickListener(OnVideoItemClickListener onVideoItemClickListener) {
        this.onVideoItemClickListener = onVideoItemClickListener;
    }

    public interface OnVideoItemClickListener {
        void onVideoItemClick(int position);
    }

    private OnItemScrollListener onItemScrollListener;

    public void setOnItemScrollListener(OnItemScrollListener onItemScrollListener) {
        this.onItemScrollListener = onItemScrollListener;
    }

    public interface OnItemScrollListener {
        void onItemScroll(int position, int y);
    }
}