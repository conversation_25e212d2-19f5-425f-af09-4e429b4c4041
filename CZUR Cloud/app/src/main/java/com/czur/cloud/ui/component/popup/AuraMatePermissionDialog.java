package com.czur.cloud.ui.component.popup;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.czur.cloud.R;

/**
 * Created by Yz on 2018/3/20
 * Email：<EMAIL>
 */

public class AuraMatePermissionDialog extends Dialog {
    public static final float DIMMED_OPACITY = 0.2f;

    public AuraMatePermissionDialog(Context context) {
        super(context);
    }

    public AuraMatePermissionDialog(Context context, int theme) {
        super(context, theme);
    }


    public static class Builder {
        private Context context;

        private String title;
        private View contentsView;

        private OnClickListener positiveListener;
        private ImageView img;
        private TextView confirmBtn;

        public Builder(Context context) {
            this.context = context;

        }


        public Builder setOnPositiveListener(OnClickListener positiveListener) {
            this.positiveListener = positiveListener;
            return this;
        }

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }



        public AuraMatePermissionDialog create() {
            LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);

            AuraMatePermissionDialog dialog = new AuraMatePermissionDialog(context, R.style.TransparentProgressDialog);
            View layout = commonCustomPopLayout(inflater, dialog);

            dialog.setContentView(layout);
            dialog.setCanceledOnTouchOutside(true);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            params.dimAmount = DIMMED_OPACITY;

            return dialog;
        }

        private View commonCustomPopLayout(LayoutInflater inflater, final AuraMatePermissionDialog dialog) {
            WindowManager.LayoutParams lp = dialog.getWindow().getAttributes();
            lp.dimAmount = 0.8f;
            dialog.getWindow().setAttributes(lp);
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            dialog.setCancelable(false);
            dialog.setCanceledOnTouchOutside(false);
            View layout = inflater.inflate(R.layout.dialog_aura_mate_permission, null, false);
            ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);
            dialog.addContentView(layout, params);


            confirmBtn = (TextView) layout.findViewById(R.id.confirm_btn);


            confirmBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    positiveListener.onClick(dialog,R.string.handwriting_confirm_text);
                }
            });

            return layout;
        }


    }

}
