package com.czur.cloud.model;

/**
 * Created by Yz on 2018/9/28.
 * Email：<EMAIL>
 */
public class AuraResultModel {

        /**
         * ossKey : test/9/qaz-147-wsx-258/2018-09-21/process/79e49d1c-d2d5-4128-95b4-4efc226d60f7.jpg
         * url : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-09-21/process/79e49d1c-d2d5-4128-95b4-4efc226d60f7.jpg?Expires=1537865102&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=PoFpRfIxf5HDLy9jiq5789%2F5sBE%3D
         * ossSmallKeyUrl : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-09-21/process/79e49d1c-d2d5-4128-95b4-4efc226d60f7.jpg?Expires=1537865102&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=Vch5nUYO9S%2FQLbyG%2BdUy9MtzTLM%3D&x-oss-process=image%2Fresize%2Cm_fixed%2Cw_150%2Ch_150
         * ossMiddleKeyUrl : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-09-21/process/79e49d1c-d2d5-4128-95b4-4efc226d60f7.jpg?Expires=1537865102&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=bBzbcPqpf7jxp%2Brw0SRtMD%2FbWWU%3D&x-oss-process=image%2Fresize%2Cm_fixed%2Cw_1080%2Ch_1080
         * fileSize : 1942904
         * seqNum : 23
         * ossOrgKey : test/9/qaz-147-wsx-258/2018-09-21/2bc9afc7-e838-497a-9eb3-05728467898e.jpg
         * ossOrgKeyUrl : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-09-21/2bc9afc7-e838-497a-9eb3-05728467898e.jpg?Expires=1537865102&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=iPWaTTsk4PL2iG4NSwYEQHWRtb0%3D
         * orgFileSize : 1818629
         */

        private String ossKey;
        private String url;
        private String ossSmallKeyUrl;
        private String ossMiddleKeyUrl;
        private int fileSize;
        private int seqNum;
        private String ossOrgKey;
        private String ossOrgKeyUrl;
        private int orgFileSize;

        public String getOssKey() {
            return ossKey;
        }

        public void setOssKey(String ossKey) {
            this.ossKey = ossKey;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getOssSmallKeyUrl() {
            return ossSmallKeyUrl;
        }

        public void setOssSmallKeyUrl(String ossSmallKeyUrl) {
            this.ossSmallKeyUrl = ossSmallKeyUrl;
        }

        public String getOssMiddleKeyUrl() {
            return ossMiddleKeyUrl;
        }

        public void setOssMiddleKeyUrl(String ossMiddleKeyUrl) {
            this.ossMiddleKeyUrl = ossMiddleKeyUrl;
        }

        public int getFileSize() {
            return fileSize;
        }

        public void setFileSize(int fileSize) {
            this.fileSize = fileSize;
        }

        public int getSeqNum() {
            return seqNum;
        }

        public void setSeqNum(int seqNum) {
            this.seqNum = seqNum;
        }

        public String getOssOrgKey() {
            return ossOrgKey;
        }

        public void setOssOrgKey(String ossOrgKey) {
            this.ossOrgKey = ossOrgKey;
        }

        public String getOssOrgKeyUrl() {
            return ossOrgKeyUrl;
        }

        public void setOssOrgKeyUrl(String ossOrgKeyUrl) {
            this.ossOrgKeyUrl = ossOrgKeyUrl;
        }

        public int getOrgFileSize() {
            return orgFileSize;
        }

        public void setOrgFileSize(int orgFileSize) {
            this.orgFileSize = orgFileSize;
        }

}
