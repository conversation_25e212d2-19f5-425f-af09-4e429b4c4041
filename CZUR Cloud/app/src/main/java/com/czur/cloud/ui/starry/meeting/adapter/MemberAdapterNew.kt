package com.czur.cloud.ui.starry.meeting.adapter

import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.AsyncListDiffer
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.SizeUtils
import com.czur.cloud.R
import com.czur.cloud.ui.starry.meeting.baselib.adapter.BaseVH
import com.czur.cloud.ui.starry.meeting.baselib.utils.getString
import com.czur.cloud.ui.starry.meeting.baselib.utils.has
import com.czur.cloud.ui.starry.meeting.bean.UserStatus.STATUS_HOLD_ON
import com.czur.cloud.ui.starry.meeting.bean.UserStatus.STATUS_IOS_HOLD
import com.czur.cloud.ui.starry.meeting.bean.UserStatus.STATUS_OFFLINE
import com.czur.cloud.ui.starry.meeting.bean.UserStatus.STATUS_TIMEOUT
import com.czur.cloud.ui.starry.meeting.bean.vo.Member
import com.czur.cloud.ui.starry.meeting.common.UserHandler
import java.util.regex.Pattern


class MemberAdapterNew : RecyclerView.Adapter<BaseVH>() {

    private val itemCallback = object : DiffUtil.ItemCallback<MemberItem>() {
        override fun areItemsTheSame(oldItem: MemberItem, newItem: MemberItem): Boolean {
            return oldItem.accountId == newItem.accountId
        }

        override fun areContentsTheSame(oldItem: MemberItem, newItem: MemberItem): Boolean {
            return oldItem == newItem
        }

    }

    private val differ = AsyncListDiffer(this, itemCallback)

    var rawData: List<Member> = emptyList()
        set(value) {
            field = value
            processData()
        }

    private var isSelfAdmin = false

    private fun processData() {
        val adminAccount = rawData.find {
            it.isAdmin
        }?.accountId ?: UserHandler.accountNo.toString()
        val adminMode = adminAccount == UserHandler.accountNo.toString()

        // 是否处于共享状态
        val inShare = rawData.has {
            it.sharing
        }
        // 转化为MemberItem
        val showData = rawData.map {
            MemberItem(
                accountId = it.accountId,
                isSelf = it.accountId == UserHandler.accountNo.toString(),
                headImg = it.headImg,
                nickName = it.nickName,
                audioEnable = it.isAudioInUse,
                videoEnable = it.isVideoInUse,
                adminMode = adminMode,
                isAdmin = adminAccount == it.accountId,
                status = it.status,
                isShareMode = inShare,
                isFullScreen = true
            )
        }

        differ.submitList(showData)
    }

    fun updateStatus(newStatus: Int, pos: Int) {
        val data = getData(pos)
        data.status = newStatus
        notifyItemChanged(pos)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH =
        BaseVH(R.layout.starry_meeting_item_member, parent)

    override fun onBindViewHolder(holder: BaseVH, position: Int) {
        val data = getData(position)
//        logI("MemberAdapter.onBindViewHolder.data=${data}")

        // 不进行回收，不过会造成性能下降。
//        holder.setIsRecyclable(false)

        // 修改各个元素可见性
        changeVisible(holder, data)

        // 头像
        holder.setImgUrlOrText(
            data.headImg,
            data.nickName,
            viewId = R.id.itemMemberHeadIv
        )

        // 在对应自己的昵称后, 显示 "(我)"
        var nickName = if (data.isSelf) {
            // 如果是
            data.nickName+ getString(R.string.tv_nickname_self)
        } else {
            data.nickName
        }
        //名字长度调整
        val view = holder.getView<TextView>(R.id.itemMemberNickNameTv)
        if (data.isSelf){
            view.ellipsize = TextUtils.TruncateAt.valueOf("MIDDLE")
        }else{
            view.ellipsize = TextUtils.TruncateAt.valueOf("END")
        }
        nickName = OmitName(data, view, nickName)
        holder.setText(nickName, R.id.itemMemberNickNameTv)

        // 音频按钮的图标
        if (data.audioEnable) {
            holder.setImgResource(R.mipmap.starry_meeting_member_icon_btn_mic_gray, R.id.itemMemberAudioIcon)
        } else {
            holder.setImgResource(R.mipmap.starry_meeting_member_icon_btn_mic_red, R.id.itemMemberAudioIcon)
        }

        // 视频按钮的图标
        if (data.videoEnable) {
            holder.setImgResource(R.mipmap.starry_meeting_member_icon_btn_video_gray, R.id.itemMemberVideoIcon)
        } else {
            holder.setImgResource(R.mipmap.starry_meeting_member_icon_btn_video_red, R.id.itemMemberVideoIcon)
        }

        if (data.status == STATUS_TIMEOUT){
            // 显示提醒按钮
            holder.visible(true, R.id.itemMemberRemindIcon)
        }else{
            holder.visible(false, R.id.itemMemberRemindIcon)
        }

    }

    /**
     * 修改每一行各个元素的可见性
     */
    private fun changeVisible(holder: BaseVH, memberItem: MemberItem) {
//        // 管理员标记
//        holder.visible(memberItem.canShow(Icon.ADMIN_FLAG), R.id.itemMemberAdminIv)
        // 管理员文字标签
        holder.visible(memberItem.canShow(Icon.ADMIN_FLAG), R.id.itemMemberAdminTv)
        // 音频按钮
        holder.visibleNotGone(memberItem.canShow(Icon.AUDIO), R.id.itemMemberAudioIcon)
        // 视频按钮
        holder.visibleNotGone(memberItem.canShow(Icon.VIDEO), R.id.itemMemberVideoIcon)
        // 状态文字
        holder.visible(memberItem.canShow(Icon.STATUS_INFO), R.id.itemMemberStatusTv)
        // 网络异常
        holder.visible(memberItem.canShow(Icon.STATUS_NETWORK_ERROR), R.id.itemMemberNetworkErrorTv)

        val view = holder.getView<LinearLayout>(R.id.itemMemberRemindIcon)
        val lp: ConstraintLayout.LayoutParams = view.layoutParams as ConstraintLayout.LayoutParams

        if (memberItem.canShow(Icon.STATUS_INFO_ADMIN)) {
            lp.rightMargin = 0
        }else{
            lp.rightMargin = SizeUtils.dp2px(20f)
        }

        view.layoutParams = lp
        // 更多按钮
        holder.visible(memberItem.canShow(Icon.MORE), R.id.itemMemberMoreIcon)
    }

    override fun getItemCount() = differ.currentList.size

    private fun getData(pos: Int): MemberItem = differ.currentList[pos]

    fun getRawMember(pos: Int): Member? {
        return rawData.getOrNull(pos)
    }

    private fun OmitName(data: MemberItem, view: TextView, originName: String):String {
        //名字是否省略
        var nickName = originName
        val regEx = "[\u4e00-\u9fa5]"
        var count = 0
        val p = Pattern.compile(regEx)
        val m = p.matcher(originName)
        //中文
        while (m.find()) {
            count++
        }
        if (!data.isAdmin) {
            if (data.adminMode) {
                if (data.status == STATUS_TIMEOUT  && originName.length+count >10 ) {
                    nickName = if ( count > 0){
                        "${originName.substring(0, 4)}..."
                    }else{
                        "${originName.substring(0, 7)}..."
                    }

                }

            } else {
                if ((data.status == STATUS_TIMEOUT)
                    && originName.length+count >12) {
                    nickName = if ( count > 0){
                        "${originName.substring(0, 5)}..."
                    }else{
                        "${originName.substring(0, 8)}..."
                    }
                }
            }
        }else{
            if (data.adminMode) {
                if (data.status == STATUS_HOLD_ON  && originName.length+count >10 ) {
                    nickName = if ( count > 0){
                        "${originName.substring(0, 4)}..."
                    }else{
                        "${originName.substring(0, 7)}..."
                    }

                }

            } else {
                if ((data.status == STATUS_HOLD_ON  ||
                            data.status == STATUS_OFFLINE ||
                            data.status == STATUS_IOS_HOLD)
                    && originName.length+count >12) {
                    nickName = if ( count > 0){
                        "${originName.substring(0, 5)}..."
                    }else{
                        "${originName.substring(0, 8)}..."
                    }
                }
            }
        }

        return nickName
    }

    // 告知adapter自己是否是管理员
    fun setIsSelfAdmin(isAdmin: Boolean){
        isSelfAdmin = isAdmin
    }

    interface OnItemClickListener {
        fun onItemClick(itemView: View?, position: Int)
    }

    private var listener: OnItemClickListener? = null

    fun setOnItemClickListener(listener: OnItemClickListener?) {
        this.listener = listener
    }

}