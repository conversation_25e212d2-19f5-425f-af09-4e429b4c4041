package com.czur.cloud.ui.starry.model

import com.czur.cloud.ui.starry.network.parse.GMT8DateJsonDeserializer
import com.google.gson.annotations.JsonAdapter
import java.io.Serializable
import java.text.SimpleDateFormat
import java.util.*

/*
"id": 194185,
"accountNo": "***********",
"meetingId": 41715,
"createTime": "2022-06-09 15:34:42",
"duration": "0",
"direction": "IN",
"status": "0",
"isDelete": false,
"read": false,
"num": 2,
"meetingName": "Jason158406183发起的会议",
"creator": "***********",
"meetingStatus": 2,
"meetingCode": "*********",
"enter": false
*
*/
//meetingDuration字段表示通话记录时长，单位是秒
//status 状态
//0：呼叫中 CALLING 1：未接听超时 TIMEOUT 2：加入 JOINED 3：拒接 REJECTED
//4：被移除 REMOVED 5：离开 LEAVE 6：掉线 OFFLINE 7：暂时离开 HOLD_ON
// meetingStatus会议的状态  1：未开始 NOT_STARTED；2：进行中 PCOCESSING；3：结束 END
data class StarryCallRecordModel(
    var id: String="0",
    var accountNo: String = "",
    var meetingId: String = "",
    @JsonAdapter(GMT8DateJsonDeserializer::class)
    var createTime: Date,
    var duration: String = "",
    var direction: String = "",
    var status: String = "0",
    var isDelete: Boolean = false,
    var read: Boolean = false,
    var num: String = "0",
    var meetingName: String = "",
    var creator: String = "",
    var meetingStatus: Int = 3,
    val meetingCode: String = "",
    var enter: Boolean = false,
    var isPCEnter: Boolean = false, // PC端是否已经接入
): Serializable{
    fun getTimeStr(): String {
        val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ROOT)
        return sdf.format(createTime)
    }
}
