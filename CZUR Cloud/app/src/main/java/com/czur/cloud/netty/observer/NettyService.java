package com.czur.cloud.netty.observer;

import static com.czur.czurutils.log.CZURLogUtilsKt.logD;
import static com.czur.czurutils.log.CZURLogUtilsKt.logE;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.Manifest;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;
import android.text.TextUtils;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.GsonUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.event.vendorPush.NewVendorPushRegIDEvent;
import com.czur.cloud.event.vendorPush.VendorPushRegIDEvent;
import com.czur.cloud.model.NettyModel;
import com.czur.cloud.netty.Config;
import com.czur.cloud.netty.bean.MessageType;
import com.czur.cloud.netty.core.CZURTcpClient;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.preferences.VendorPushPreferences;
import com.czur.cloud.ui.books.sync.BaseService;
import com.czur.cloud.ui.starry.livedatabus.BlankActivityForNotification;
import com.czur.cloud.util.VendorUtils;
import com.czur.cloud.vendorPush.VendorPushTask;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Locale;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Created by shaojun
 */
public class NettyService extends BaseService {
    public static final String TAG = NettyService.class.getName();
    public static NettyService instance;
    private CZURTcpClient client;
    private AtomicBoolean isRegidWaiting = new AtomicBoolean(false);
    private AtomicBoolean isNewRegid = new AtomicBoolean(false);

    private int messageId = 1;


    public NettyService() {
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
        client = CZURTcpClient.getInstance();
        instance = this;

        // Jason 20220113
        initNotification();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        initNotification();
        if (!client.getIsConnected().get() && !client.getIsConnecting().get()) {
            setupUUid();
        }

        // Jason 2022-08-02
        // 如果Service进程被kill掉，保留Service的状态为开始状态，但不保留递送的intent对象。随后系统会尝试重新创建Service，由于服务状态为开始状态，所以创建服务后一定会调用onStartCommand(Intent,int,int)方法。如果在此期间没有任何启动命令被传递到Service，那么参数Intent将为null；
        return START_STICKY;
    }

    private void initNettyConfig() {
        if (client.getIsConnected().get()) {
            if (isNewRegid.get()) {
                client.closeChannel();
                Config.appid = AppUtils.getAppPackageName();
                Config.os = MessageType.OS.getType();
                Config.device = MessageType.DEVICE.getType();
                Config.udid = UserPreferences.getInstance().getUdid();
                Config.userId = UserPreferences.getInstance().getUserId();
                client.connect();
            }
        } else {
            client.closeChannel();
            Config.appid = AppUtils.getAppPackageName();
            Config.os = MessageType.OS.getType();
            Config.device = MessageType.DEVICE.getType();
            Config.udid = UserPreferences.getInstance().getUdid();
            Config.userId = UserPreferences.getInstance().getUserId();
            client.connect();
        }
    }

    private String getPushLanguage() {
        Locale locale = getResources().getConfiguration().locale;
        if (locale.toString().contains("zh_CN")) {
            return "en_us";
        } else if (locale.toString().contains("zh_HK") || locale.toString().contains("zh_TW") || locale.toString().contains("zh_MO")) {
            return "zh_tw";
        }
        return "en_us";

    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        logI("NettyService.onDestroy.STOP");
        if (client != null) {
            try {
                client.closeChannel();
            }catch (Exception e){
                logE("NettyService.onDestroy.client.closeChannel().e="+e.toString());
            }
        }
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        instance = null;
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                stopForeground(true);
            } else {
                stopSelf();
            }
        }catch (Exception e){
            logE("NettyService.onDestroy.e="+e.toString());
        }
    }

    private void getNettyUrl() {
        logD("getNettyUrl");
        HttpManager.getInstance().request().
                getNettyUrl(UserPreferences.getInstance(this).getUserId(),
                        NettyModel.class,
                        new MiaoHttpManager.CallbackNetwork<NettyModel>() {
            @Override
            public void onNoNetwork() {
            }

            @Override
            public void onStart() {

            }

            @Override
            public void onResponse(MiaoHttpEntity<NettyModel> entity) {
                Config.ADDRESS = entity.getBody().getIp();
                Config.PORT = entity.getBody().getPort();
                logD("ADDRESS: " + entity.getBody().getIp()+ ",PORT: " + entity.getBody().getPort());
                initNettyConfig();
            }

            @Override
            public void onFailure(MiaoHttpEntity<NettyModel> entity) {
            }

            @Override
            public void onError(Exception e) {
            }
        });
    }

    // 长连接使用Starry的node接口获取地址
    private void getNettyStarryUrl() {
        logD("getNettyStarryUrl");
        HttpManager.getInstance().requestStarry().
                getNettyStarryUrl(UserPreferences.getInstance(this).getUserId(),
                        NettyModel.class,
                        new MiaoHttpManager.CallbackNetwork<NettyModel>() {
                            @Override
                            public void onNoNetwork() {
                            }

                            @Override
                            public void onStart() {

                            }

                            @Override
                            public void onResponse(MiaoHttpEntity<NettyModel> entity) {
                                logI("onResponse.entity:" + GsonUtils.toJson(entity));
                                Config.ADDRESS = entity.getData().getHost();
                                Config.PORT = entity.getData().getPort();
                                logD("ADDRESS: " + entity.getData().getHost()+ ",PORT: " + entity.getData().getPort());
                                initNettyConfig();
                            }

                            @Override
                            public void onFailure(MiaoHttpEntity<NettyModel> entity) {
                            }

                            @Override
                            public void onError(Exception e) {
                            }
                        });
    }

    private void setupUUid() {
        logD("setupUUid");
        String vendorStr, lang;
        if (BuildConfig.IS_OVERSEAS) {
            vendorStr = "google";
            lang = getPushLanguage();
        } else {
            VendorUtils.RomEnum vendor = VendorPushPreferences.INSTANCE.getPhoneRom();
            vendorStr = vendor == null ? VendorUtils.RomEnum.OTHER.getRom() : vendor.getRom();
            lang = "zh_CN";
        }

        String udid = UserPreferences.getInstance().getUdid();
        String regid = UserPreferences.getInstance().getRegid();
        if (TextUtils.isEmpty(regid)) {
            isRegidWaiting.set(true);
            logD("regid是空的等待");
        } else {
            logD("regid有值去连接了，udid="+udid+"，regid="+regid);
            HttpManager.getInstance().request().
                    getUUID(UserPreferences.getInstance().getUserId(),
                            0 + "",
                            AppUtils.getAppPackageName(),
                            CZURConstants.ANDROID,
                            udid,
                            regid,
                            "",
                            vendorStr,
                            lang,
                            String.class,
                            new MiaoHttpManager.CallbackNetwork<String>() {
                        @Override
                        public void onNoNetwork() {
                        }

                        @Override
                        public void onStart() {
                        }

                        @Override
                        public void onResponse(MiaoHttpEntity<String> entity) {
                            getNettyStarryUrl();
//                            getNettyUrl();
                        }

                        @Override
                        public void onFailure(MiaoHttpEntity<String> entity) {
                        }

                        @Override
                        public void onError(Exception e) {
                        }
                    });
        }
    }

    private void setNewUuid() {
        logD("APP新的注册别名" + UserPreferences.getInstance().getRegid());

        String vendorStr, lang;
        if (BuildConfig.IS_OVERSEAS) {
            vendorStr = "google";
            lang = getPushLanguage();
        } else {
            VendorUtils.RomEnum vendor = VendorPushPreferences.INSTANCE.getPhoneRom();
            vendorStr = vendor == null ? VendorUtils.RomEnum.OTHER.getRom() : vendor.getRom();
            lang = "zh_CN";
        }
        String udid = UserPreferences.getInstance().getUdid();
        String regid = UserPreferences.getInstance().getRegid();
        HttpManager.getInstance().request().
                getUUID(UserPreferences.getInstance().getUserId(),
                        0 + "",
                        AppUtils.getAppPackageName(),
                CZURConstants.ANDROID,
                        udid,
                        regid,
                        "",
                        vendorStr,
                        lang,
                        String.class,
                        new MiaoHttpManager.CallbackNetwork<String>() {
                    @Override
                    public void onNoNetwork() {
                    }

                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                        getNettyStarryUrl();
//                        getNettyUrl();
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onError(Exception e) {
                    }
                });
    }


    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onEvent(VendorPushRegIDEvent event) {
        Log.d(VendorPushTask.TAG, "热乎的regid来了" + UserPreferences.getInstance().getUdid());
        if (isRegidWaiting.get()) {
            isRegidWaiting.set(false);
            setupUUid();
        }
        EventBus.getDefault().removeStickyEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onEvent(NewVendorPushRegIDEvent event) {
        //注册id改变后重连
        isNewRegid.set(true);
        setNewUuid();
        EventBus.getDefault().removeStickyEvent(event);
    }

    // 代码中判断版本进行相关权限申请
    private void checkAndShowNotification(){
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU){
            NotificationManager manager = getSystemService(NotificationManager.class);
            boolean b = manager.areNotificationsEnabled();
            if (!b){
                String[] strings = {Manifest.permission.POST_NOTIFICATIONS};
                PermissionUtils.permission(strings);
//                return;
            }
        }
    }

    private void initNotification() {
        checkAndShowNotification();

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            Intent intent = new Intent(getApplicationContext(), BlankActivityForNotification.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            PendingIntent contentIntent;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S){
                contentIntent = PendingIntent.getActivity(getApplicationContext(), messageId, intent, PendingIntent.FLAG_IMMUTABLE);
            }else {
                contentIntent = PendingIntent.getActivity(getApplicationContext(), messageId, intent, PendingIntent.FLAG_UPDATE_CURRENT);
            }
            // 提高进程优先级 ，就会在通知栏中出现自己的应用，如果不想提高优先级，可以把这个注释
            // 参数1：id 参数2：通知
            String channelId = "com.czur.cloud";
            String channelName = getString(R.string.background);
            NotificationChannel notificationChannel = null;

            notificationChannel = new NotificationChannel(channelId,
                    channelName, NotificationManager.IMPORTANCE_HIGH);
            notificationChannel.setShowBadge(false);
            notificationChannel.enableVibration(false);
            notificationChannel.setSound(null, null);
            notificationChannel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
            NotificationManager manager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            manager.createNotificationChannel(notificationChannel);

            Notification notification = new NotificationCompat.Builder(this)
                    .setChannelId(channelId)
                    .setContentTitle(getString(R.string.aura_mate_title))
                    .setCategory(Notification.CATEGORY_CALL)
                    .setWhen(System.currentTimeMillis())
                    .setSmallIcon(R.mipmap.small_icon)
                    .setContentIntent(contentIntent)
                    .build();
            startForeground(messageId, notification);
        }
    }
}
