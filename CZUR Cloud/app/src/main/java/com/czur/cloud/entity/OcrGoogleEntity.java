package com.czur.cloud.entity;

import java.util.List;

public class OcrGoogleEntity {
    private List<ResponsesBean> responses;

    public List<ResponsesBean> getResponses() {
        return responses;
    }

    public void setResponses(List<ResponsesBean> responses) {
        this.responses = responses;
    }

    public static class ResponsesBean {
        /**
         * textAnnotations : [{"locale":"en","description":"ABBEY\nROAD NW8\nCITY OF WESTMINSTER\n","boundingPoly":{"vertices":[{"x":46,"y":41},{"x":269,"y":41},{"x":269,"y":177},{"x":46,"y":177}]}},{"description":"ABBEY","boundingPoly":{"vertices":[{"x":46,"y":44},{"x":179,"y":41},{"x":180,"y":81},{"x":47,"y":84}]}},{"description":"ROAD","boundingPoly":{"vertices":[{"x":47,"y":95},{"x":155,"y":94},{"x":155,"y":133},{"x":47,"y":134}]}},{"description":"NW8","boundingPoly":{"vertices":[{"x":180,"y":93},{"x":269,"y":92},{"x":269,"y":131},{"x":180,"y":132}]}},{"description":"CITY","boundingPoly":{"vertices":[{"x":50,"y":161},{"x":85,"y":160},{"x":85,"y":176},{"x":50,"y":177}]}},{"description":"OF","boundingPoly":{"vertices":[{"x":95,"y":161},{"x":114,"y":161},{"x":114,"y":177},{"x":95,"y":177}]}},{"description":"WESTMINSTER","boundingPoly":{"vertices":[{"x":122,"y":161},{"x":249,"y":159},{"x":249,"y":175},{"x":122,"y":177}]}}]
         * fullTextAnnotation : {"pages":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"width":320,"height":240,"blocks":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":46,"y":41},{"x":269,"y":41},{"x":269,"y":134},{"x":46,"y":134}]},"paragraphs":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":46,"y":41},{"x":269,"y":41},{"x":269,"y":134},{"x":46,"y":134}]},"words":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":46,"y":44},{"x":179,"y":41},{"x":180,"y":81},{"x":47,"y":84}]},"symbols":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":46,"y":44},{"x":71,"y":43},{"x":72,"y":83},{"x":47,"y":84}]},"text":"A"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":74,"y":43},{"x":100,"y":42},{"x":101,"y":81},{"x":75,"y":82}]},"text":"B"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":101,"y":43},{"x":126,"y":42},{"x":127,"y":81},{"x":102,"y":82}]},"text":"B"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":132,"y":42},{"x":152,"y":42},{"x":153,"y":82},{"x":133,"y":82}]},"text":"E"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"EOL_SURE_SPACE"}},"boundingBox":{"vertices":[{"x":156,"y":42},{"x":179,"y":42},{"x":180,"y":82},{"x":157,"y":82}]},"text":"Y"}]},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":47,"y":95},{"x":155,"y":94},{"x":155,"y":133},{"x":47,"y":134}]},"symbols":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":47,"y":96},{"x":68,"y":96},{"x":68,"y":134},{"x":47,"y":134}]},"text":"R"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":74,"y":95},{"x":97,"y":95},{"x":97,"y":134},{"x":74,"y":134}]},"text":"O"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":101,"y":95},{"x":126,"y":95},{"x":126,"y":133},{"x":101,"y":133}]},"text":"A"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"SPACE"}},"boundingBox":{"vertices":[{"x":131,"y":94},{"x":155,"y":94},{"x":155,"y":133},{"x":131,"y":133}]},"text":"D"}]},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":180,"y":93},{"x":269,"y":92},{"x":269,"y":131},{"x":180,"y":132}]},"symbols":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":180,"y":93},{"x":205,"y":93},{"x":205,"y":132},{"x":180,"y":132}]},"text":"N"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":211,"y":93},{"x":249,"y":93},{"x":249,"y":131},{"x":211,"y":131}]},"text":"W"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"EOL_SURE_SPACE"}},"boundingBox":{"vertices":[{"x":248,"y":92},{"x":269,"y":92},{"x":269,"y":131},{"x":248,"y":131}]},"text":"8"}]}]}],"blockType":"TEXT"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":50,"y":159},{"x":249,"y":159},{"x":249,"y":177},{"x":50,"y":177}]},"paragraphs":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":50,"y":159},{"x":249,"y":159},{"x":249,"y":177},{"x":50,"y":177}]},"words":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":50,"y":161},{"x":85,"y":160},{"x":85,"y":176},{"x":50,"y":177}]},"symbols":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":50,"y":161},{"x":58,"y":161},{"x":58,"y":177},{"x":50,"y":177}]},"text":"C"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":60,"y":161},{"x":67,"y":161},{"x":67,"y":177},{"x":60,"y":177}]},"text":"I"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":68,"y":161},{"x":74,"y":161},{"x":74,"y":177},{"x":68,"y":177}]},"text":"T"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"SPACE"}},"boundingBox":{"vertices":[{"x":76,"y":162},{"x":85,"y":162},{"x":85,"y":177},{"x":76,"y":177}]},"text":"Y"}]},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":95,"y":161},{"x":114,"y":161},{"x":114,"y":177},{"x":95,"y":177}]},"symbols":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":95,"y":161},{"x":104,"y":161},{"x":104,"y":177},{"x":95,"y":177}]},"text":"O"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"SPACE"}},"boundingBox":{"vertices":[{"x":107,"y":161},{"x":114,"y":161},{"x":114,"y":176},{"x":107,"y":176}]},"text":"F"}]},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":122,"y":161},{"x":249,"y":159},{"x":249,"y":175},{"x":122,"y":177}]},"symbols":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":122,"y":161},{"x":138,"y":161},{"x":138,"y":177},{"x":122,"y":177}]},"text":"W"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":141,"y":161},{"x":150,"y":161},{"x":150,"y":177},{"x":141,"y":177}]},"text":"E"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":151,"y":160},{"x":160,"y":160},{"x":160,"y":176},{"x":151,"y":176}]},"text":"S"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":161,"y":160},{"x":170,"y":160},{"x":170,"y":176},{"x":161,"y":176}]},"text":"T"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":172,"y":160},{"x":182,"y":160},{"x":182,"y":176},{"x":172,"y":176}]},"text":"M"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":183,"y":160},{"x":192,"y":160},{"x":192,"y":176},{"x":183,"y":176}]},"text":"I"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":196,"y":160},{"x":206,"y":160},{"x":206,"y":175},{"x":196,"y":175}]},"text":"N"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":208,"y":160},{"x":218,"y":160},{"x":218,"y":175},{"x":208,"y":175}]},"text":"S"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":219,"y":159},{"x":229,"y":159},{"x":229,"y":174},{"x":219,"y":174}]},"text":"T"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":231,"y":159},{"x":238,"y":159},{"x":238,"y":174},{"x":231,"y":174}]},"text":"E"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"EOL_SURE_SPACE"}},"boundingBox":{"vertices":[{"x":241,"y":159},{"x":249,"y":159},{"x":249,"y":174},{"x":241,"y":174}]},"text":"R"}]}]}],"blockType":"TEXT"}]}],"text":"ABBEY\nROAD NW8\nCITY OF WESTMINSTER\n"}
         */

        private FullTextAnnotationBean fullTextAnnotation;
        private List<TextAnnotationsBean> textAnnotations;

        public FullTextAnnotationBean getFullTextAnnotation() {
            return fullTextAnnotation;
        }

        public void setFullTextAnnotation(FullTextAnnotationBean fullTextAnnotation) {
            this.fullTextAnnotation = fullTextAnnotation;
        }

        public List<TextAnnotationsBean> getTextAnnotations() {
            return textAnnotations;
        }

        public void setTextAnnotations(List<TextAnnotationsBean> textAnnotations) {
            this.textAnnotations = textAnnotations;
        }

        public static class FullTextAnnotationBean {
            /**
             * pages : [{"property":{"detectedLanguages":[{"languageCode":"en"}]},"width":320,"height":240,"blocks":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":46,"y":41},{"x":269,"y":41},{"x":269,"y":134},{"x":46,"y":134}]},"paragraphs":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":46,"y":41},{"x":269,"y":41},{"x":269,"y":134},{"x":46,"y":134}]},"words":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":46,"y":44},{"x":179,"y":41},{"x":180,"y":81},{"x":47,"y":84}]},"symbols":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":46,"y":44},{"x":71,"y":43},{"x":72,"y":83},{"x":47,"y":84}]},"text":"A"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":74,"y":43},{"x":100,"y":42},{"x":101,"y":81},{"x":75,"y":82}]},"text":"B"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":101,"y":43},{"x":126,"y":42},{"x":127,"y":81},{"x":102,"y":82}]},"text":"B"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":132,"y":42},{"x":152,"y":42},{"x":153,"y":82},{"x":133,"y":82}]},"text":"E"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"EOL_SURE_SPACE"}},"boundingBox":{"vertices":[{"x":156,"y":42},{"x":179,"y":42},{"x":180,"y":82},{"x":157,"y":82}]},"text":"Y"}]},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":47,"y":95},{"x":155,"y":94},{"x":155,"y":133},{"x":47,"y":134}]},"symbols":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":47,"y":96},{"x":68,"y":96},{"x":68,"y":134},{"x":47,"y":134}]},"text":"R"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":74,"y":95},{"x":97,"y":95},{"x":97,"y":134},{"x":74,"y":134}]},"text":"O"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":101,"y":95},{"x":126,"y":95},{"x":126,"y":133},{"x":101,"y":133}]},"text":"A"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"SPACE"}},"boundingBox":{"vertices":[{"x":131,"y":94},{"x":155,"y":94},{"x":155,"y":133},{"x":131,"y":133}]},"text":"D"}]},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":180,"y":93},{"x":269,"y":92},{"x":269,"y":131},{"x":180,"y":132}]},"symbols":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":180,"y":93},{"x":205,"y":93},{"x":205,"y":132},{"x":180,"y":132}]},"text":"N"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":211,"y":93},{"x":249,"y":93},{"x":249,"y":131},{"x":211,"y":131}]},"text":"W"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"EOL_SURE_SPACE"}},"boundingBox":{"vertices":[{"x":248,"y":92},{"x":269,"y":92},{"x":269,"y":131},{"x":248,"y":131}]},"text":"8"}]}]}],"blockType":"TEXT"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":50,"y":159},{"x":249,"y":159},{"x":249,"y":177},{"x":50,"y":177}]},"paragraphs":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":50,"y":159},{"x":249,"y":159},{"x":249,"y":177},{"x":50,"y":177}]},"words":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":50,"y":161},{"x":85,"y":160},{"x":85,"y":176},{"x":50,"y":177}]},"symbols":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":50,"y":161},{"x":58,"y":161},{"x":58,"y":177},{"x":50,"y":177}]},"text":"C"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":60,"y":161},{"x":67,"y":161},{"x":67,"y":177},{"x":60,"y":177}]},"text":"I"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":68,"y":161},{"x":74,"y":161},{"x":74,"y":177},{"x":68,"y":177}]},"text":"T"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"SPACE"}},"boundingBox":{"vertices":[{"x":76,"y":162},{"x":85,"y":162},{"x":85,"y":177},{"x":76,"y":177}]},"text":"Y"}]},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":95,"y":161},{"x":114,"y":161},{"x":114,"y":177},{"x":95,"y":177}]},"symbols":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":95,"y":161},{"x":104,"y":161},{"x":104,"y":177},{"x":95,"y":177}]},"text":"O"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"SPACE"}},"boundingBox":{"vertices":[{"x":107,"y":161},{"x":114,"y":161},{"x":114,"y":176},{"x":107,"y":176}]},"text":"F"}]},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":122,"y":161},{"x":249,"y":159},{"x":249,"y":175},{"x":122,"y":177}]},"symbols":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":122,"y":161},{"x":138,"y":161},{"x":138,"y":177},{"x":122,"y":177}]},"text":"W"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":141,"y":161},{"x":150,"y":161},{"x":150,"y":177},{"x":141,"y":177}]},"text":"E"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":151,"y":160},{"x":160,"y":160},{"x":160,"y":176},{"x":151,"y":176}]},"text":"S"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":161,"y":160},{"x":170,"y":160},{"x":170,"y":176},{"x":161,"y":176}]},"text":"T"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":172,"y":160},{"x":182,"y":160},{"x":182,"y":176},{"x":172,"y":176}]},"text":"M"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":183,"y":160},{"x":192,"y":160},{"x":192,"y":176},{"x":183,"y":176}]},"text":"I"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":196,"y":160},{"x":206,"y":160},{"x":206,"y":175},{"x":196,"y":175}]},"text":"N"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":208,"y":160},{"x":218,"y":160},{"x":218,"y":175},{"x":208,"y":175}]},"text":"S"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":219,"y":159},{"x":229,"y":159},{"x":229,"y":174},{"x":219,"y":174}]},"text":"T"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":231,"y":159},{"x":238,"y":159},{"x":238,"y":174},{"x":231,"y":174}]},"text":"E"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"EOL_SURE_SPACE"}},"boundingBox":{"vertices":[{"x":241,"y":159},{"x":249,"y":159},{"x":249,"y":174},{"x":241,"y":174}]},"text":"R"}]}]}],"blockType":"TEXT"}]}]
             * text : ABBEY
             ROAD NW8
             CITY OF WESTMINSTER

             */

            private String text;
            private List<PagesBean> pages;

            public String getText() {
                return text;
            }

            public void setText(String text) {
                this.text = text;
            }

            public List<PagesBean> getPages() {
                return pages;
            }

            public void setPages(List<PagesBean> pages) {
                this.pages = pages;
            }

            public static class PagesBean {
                /**
                 * property : {"detectedLanguages":[{"languageCode":"en"}]}
                 * width : 320
                 * height : 240
                 * blocks : [{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":46,"y":41},{"x":269,"y":41},{"x":269,"y":134},{"x":46,"y":134}]},"paragraphs":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":46,"y":41},{"x":269,"y":41},{"x":269,"y":134},{"x":46,"y":134}]},"words":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":46,"y":44},{"x":179,"y":41},{"x":180,"y":81},{"x":47,"y":84}]},"symbols":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":46,"y":44},{"x":71,"y":43},{"x":72,"y":83},{"x":47,"y":84}]},"text":"A"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":74,"y":43},{"x":100,"y":42},{"x":101,"y":81},{"x":75,"y":82}]},"text":"B"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":101,"y":43},{"x":126,"y":42},{"x":127,"y":81},{"x":102,"y":82}]},"text":"B"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":132,"y":42},{"x":152,"y":42},{"x":153,"y":82},{"x":133,"y":82}]},"text":"E"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"EOL_SURE_SPACE"}},"boundingBox":{"vertices":[{"x":156,"y":42},{"x":179,"y":42},{"x":180,"y":82},{"x":157,"y":82}]},"text":"Y"}]},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":47,"y":95},{"x":155,"y":94},{"x":155,"y":133},{"x":47,"y":134}]},"symbols":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":47,"y":96},{"x":68,"y":96},{"x":68,"y":134},{"x":47,"y":134}]},"text":"R"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":74,"y":95},{"x":97,"y":95},{"x":97,"y":134},{"x":74,"y":134}]},"text":"O"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":101,"y":95},{"x":126,"y":95},{"x":126,"y":133},{"x":101,"y":133}]},"text":"A"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"SPACE"}},"boundingBox":{"vertices":[{"x":131,"y":94},{"x":155,"y":94},{"x":155,"y":133},{"x":131,"y":133}]},"text":"D"}]},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":180,"y":93},{"x":269,"y":92},{"x":269,"y":131},{"x":180,"y":132}]},"symbols":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":180,"y":93},{"x":205,"y":93},{"x":205,"y":132},{"x":180,"y":132}]},"text":"N"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":211,"y":93},{"x":249,"y":93},{"x":249,"y":131},{"x":211,"y":131}]},"text":"W"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"EOL_SURE_SPACE"}},"boundingBox":{"vertices":[{"x":248,"y":92},{"x":269,"y":92},{"x":269,"y":131},{"x":248,"y":131}]},"text":"8"}]}]}],"blockType":"TEXT"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":50,"y":159},{"x":249,"y":159},{"x":249,"y":177},{"x":50,"y":177}]},"paragraphs":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":50,"y":159},{"x":249,"y":159},{"x":249,"y":177},{"x":50,"y":177}]},"words":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":50,"y":161},{"x":85,"y":160},{"x":85,"y":176},{"x":50,"y":177}]},"symbols":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":50,"y":161},{"x":58,"y":161},{"x":58,"y":177},{"x":50,"y":177}]},"text":"C"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":60,"y":161},{"x":67,"y":161},{"x":67,"y":177},{"x":60,"y":177}]},"text":"I"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":68,"y":161},{"x":74,"y":161},{"x":74,"y":177},{"x":68,"y":177}]},"text":"T"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"SPACE"}},"boundingBox":{"vertices":[{"x":76,"y":162},{"x":85,"y":162},{"x":85,"y":177},{"x":76,"y":177}]},"text":"Y"}]},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":95,"y":161},{"x":114,"y":161},{"x":114,"y":177},{"x":95,"y":177}]},"symbols":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":95,"y":161},{"x":104,"y":161},{"x":104,"y":177},{"x":95,"y":177}]},"text":"O"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"SPACE"}},"boundingBox":{"vertices":[{"x":107,"y":161},{"x":114,"y":161},{"x":114,"y":176},{"x":107,"y":176}]},"text":"F"}]},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":122,"y":161},{"x":249,"y":159},{"x":249,"y":175},{"x":122,"y":177}]},"symbols":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":122,"y":161},{"x":138,"y":161},{"x":138,"y":177},{"x":122,"y":177}]},"text":"W"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":141,"y":161},{"x":150,"y":161},{"x":150,"y":177},{"x":141,"y":177}]},"text":"E"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":151,"y":160},{"x":160,"y":160},{"x":160,"y":176},{"x":151,"y":176}]},"text":"S"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":161,"y":160},{"x":170,"y":160},{"x":170,"y":176},{"x":161,"y":176}]},"text":"T"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":172,"y":160},{"x":182,"y":160},{"x":182,"y":176},{"x":172,"y":176}]},"text":"M"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":183,"y":160},{"x":192,"y":160},{"x":192,"y":176},{"x":183,"y":176}]},"text":"I"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":196,"y":160},{"x":206,"y":160},{"x":206,"y":175},{"x":196,"y":175}]},"text":"N"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":208,"y":160},{"x":218,"y":160},{"x":218,"y":175},{"x":208,"y":175}]},"text":"S"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":219,"y":159},{"x":229,"y":159},{"x":229,"y":174},{"x":219,"y":174}]},"text":"T"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":231,"y":159},{"x":238,"y":159},{"x":238,"y":174},{"x":231,"y":174}]},"text":"E"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"EOL_SURE_SPACE"}},"boundingBox":{"vertices":[{"x":241,"y":159},{"x":249,"y":159},{"x":249,"y":174},{"x":241,"y":174}]},"text":"R"}]}]}],"blockType":"TEXT"}]
                 */

                private PropertyBean property;
                private int width;
                private int height;
                private List<BlocksBean> blocks;

                public PropertyBean getProperty() {
                    return property;
                }

                public void setProperty(PropertyBean property) {
                    this.property = property;
                }

                public int getWidth() {
                    return width;
                }

                public void setWidth(int width) {
                    this.width = width;
                }

                public int getHeight() {
                    return height;
                }

                public void setHeight(int height) {
                    this.height = height;
                }

                public List<BlocksBean> getBlocks() {
                    return blocks;
                }

                public void setBlocks(List<BlocksBean> blocks) {
                    this.blocks = blocks;
                }

                public static class PropertyBean {
                    private List<DetectedLanguagesBean> detectedLanguages;

                    public List<DetectedLanguagesBean> getDetectedLanguages() {
                        return detectedLanguages;
                    }

                    public void setDetectedLanguages(List<DetectedLanguagesBean> detectedLanguages) {
                        this.detectedLanguages = detectedLanguages;
                    }

                    public static class DetectedLanguagesBean {
                        /**
                         * languageCode : en
                         */

                        private String languageCode;

                        public String getLanguageCode() {
                            return languageCode;
                        }

                        public void setLanguageCode(String languageCode) {
                            this.languageCode = languageCode;
                        }
                    }
                }

                public static class BlocksBean {
                    /**
                     * property : {"detectedLanguages":[{"languageCode":"en"}]}
                     * boundingBox : {"vertices":[{"x":46,"y":41},{"x":269,"y":41},{"x":269,"y":134},{"x":46,"y":134}]}
                     * paragraphs : [{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":46,"y":41},{"x":269,"y":41},{"x":269,"y":134},{"x":46,"y":134}]},"words":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":46,"y":44},{"x":179,"y":41},{"x":180,"y":81},{"x":47,"y":84}]},"symbols":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":46,"y":44},{"x":71,"y":43},{"x":72,"y":83},{"x":47,"y":84}]},"text":"A"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":74,"y":43},{"x":100,"y":42},{"x":101,"y":81},{"x":75,"y":82}]},"text":"B"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":101,"y":43},{"x":126,"y":42},{"x":127,"y":81},{"x":102,"y":82}]},"text":"B"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":132,"y":42},{"x":152,"y":42},{"x":153,"y":82},{"x":133,"y":82}]},"text":"E"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"EOL_SURE_SPACE"}},"boundingBox":{"vertices":[{"x":156,"y":42},{"x":179,"y":42},{"x":180,"y":82},{"x":157,"y":82}]},"text":"Y"}]},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":47,"y":95},{"x":155,"y":94},{"x":155,"y":133},{"x":47,"y":134}]},"symbols":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":47,"y":96},{"x":68,"y":96},{"x":68,"y":134},{"x":47,"y":134}]},"text":"R"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":74,"y":95},{"x":97,"y":95},{"x":97,"y":134},{"x":74,"y":134}]},"text":"O"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":101,"y":95},{"x":126,"y":95},{"x":126,"y":133},{"x":101,"y":133}]},"text":"A"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"SPACE"}},"boundingBox":{"vertices":[{"x":131,"y":94},{"x":155,"y":94},{"x":155,"y":133},{"x":131,"y":133}]},"text":"D"}]},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":180,"y":93},{"x":269,"y":92},{"x":269,"y":131},{"x":180,"y":132}]},"symbols":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":180,"y":93},{"x":205,"y":93},{"x":205,"y":132},{"x":180,"y":132}]},"text":"N"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":211,"y":93},{"x":249,"y":93},{"x":249,"y":131},{"x":211,"y":131}]},"text":"W"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"EOL_SURE_SPACE"}},"boundingBox":{"vertices":[{"x":248,"y":92},{"x":269,"y":92},{"x":269,"y":131},{"x":248,"y":131}]},"text":"8"}]}]}]
                     * blockType : TEXT
                     */

                    private PropertyBeanX property;
                    private BoundingBoxBean boundingBox;
                    private String blockType;
                    private List<ParagraphsBean> paragraphs;

                    public PropertyBeanX getProperty() {
                        return property;
                    }

                    public void setProperty(PropertyBeanX property) {
                        this.property = property;
                    }

                    public BoundingBoxBean getBoundingBox() {
                        return boundingBox;
                    }

                    public void setBoundingBox(BoundingBoxBean boundingBox) {
                        this.boundingBox = boundingBox;
                    }

                    public String getBlockType() {
                        return blockType;
                    }

                    public void setBlockType(String blockType) {
                        this.blockType = blockType;
                    }

                    public List<ParagraphsBean> getParagraphs() {
                        return paragraphs;
                    }

                    public void setParagraphs(List<ParagraphsBean> paragraphs) {
                        this.paragraphs = paragraphs;
                    }

                    public static class PropertyBeanX {
                        private List<DetectedLanguagesBeanX> detectedLanguages;

                        public List<DetectedLanguagesBeanX> getDetectedLanguages() {
                            return detectedLanguages;
                        }

                        public void setDetectedLanguages(List<DetectedLanguagesBeanX> detectedLanguages) {
                            this.detectedLanguages = detectedLanguages;
                        }

                        public static class DetectedLanguagesBeanX {
                            /**
                             * languageCode : en
                             */

                            private String languageCode;

                            public String getLanguageCode() {
                                return languageCode;
                            }

                            public void setLanguageCode(String languageCode) {
                                this.languageCode = languageCode;
                            }
                        }
                    }

                    public static class BoundingBoxBean {
                        private List<VerticesBean> vertices;

                        public List<VerticesBean> getVertices() {
                            return vertices;
                        }

                        public void setVertices(List<VerticesBean> vertices) {
                            this.vertices = vertices;
                        }

                        public static class VerticesBean {
                            /**
                             * x : 46
                             * y : 41
                             */

                            private int x;
                            private int y;

                            public int getX() {
                                return x;
                            }

                            public void setX(int x) {
                                this.x = x;
                            }

                            public int getY() {
                                return y;
                            }

                            public void setY(int y) {
                                this.y = y;
                            }
                        }
                    }

                    public static class ParagraphsBean {
                        /**
                         * property : {"detectedLanguages":[{"languageCode":"en"}]}
                         * boundingBox : {"vertices":[{"x":46,"y":41},{"x":269,"y":41},{"x":269,"y":134},{"x":46,"y":134}]}
                         * words : [{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":46,"y":44},{"x":179,"y":41},{"x":180,"y":81},{"x":47,"y":84}]},"symbols":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":46,"y":44},{"x":71,"y":43},{"x":72,"y":83},{"x":47,"y":84}]},"text":"A"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":74,"y":43},{"x":100,"y":42},{"x":101,"y":81},{"x":75,"y":82}]},"text":"B"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":101,"y":43},{"x":126,"y":42},{"x":127,"y":81},{"x":102,"y":82}]},"text":"B"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":132,"y":42},{"x":152,"y":42},{"x":153,"y":82},{"x":133,"y":82}]},"text":"E"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"EOL_SURE_SPACE"}},"boundingBox":{"vertices":[{"x":156,"y":42},{"x":179,"y":42},{"x":180,"y":82},{"x":157,"y":82}]},"text":"Y"}]},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":47,"y":95},{"x":155,"y":94},{"x":155,"y":133},{"x":47,"y":134}]},"symbols":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":47,"y":96},{"x":68,"y":96},{"x":68,"y":134},{"x":47,"y":134}]},"text":"R"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":74,"y":95},{"x":97,"y":95},{"x":97,"y":134},{"x":74,"y":134}]},"text":"O"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":101,"y":95},{"x":126,"y":95},{"x":126,"y":133},{"x":101,"y":133}]},"text":"A"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"SPACE"}},"boundingBox":{"vertices":[{"x":131,"y":94},{"x":155,"y":94},{"x":155,"y":133},{"x":131,"y":133}]},"text":"D"}]},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":180,"y":93},{"x":269,"y":92},{"x":269,"y":131},{"x":180,"y":132}]},"symbols":[{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":180,"y":93},{"x":205,"y":93},{"x":205,"y":132},{"x":180,"y":132}]},"text":"N"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":211,"y":93},{"x":249,"y":93},{"x":249,"y":131},{"x":211,"y":131}]},"text":"W"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"EOL_SURE_SPACE"}},"boundingBox":{"vertices":[{"x":248,"y":92},{"x":269,"y":92},{"x":269,"y":131},{"x":248,"y":131}]},"text":"8"}]}]
                         */

                        private PropertyBeanXX property;
                        private BoundingBoxBeanX boundingBox;
                        private List<WordsBean> words;

                        public PropertyBeanXX getProperty() {
                            return property;
                        }

                        public void setProperty(PropertyBeanXX property) {
                            this.property = property;
                        }

                        public BoundingBoxBeanX getBoundingBox() {
                            return boundingBox;
                        }

                        public void setBoundingBox(BoundingBoxBeanX boundingBox) {
                            this.boundingBox = boundingBox;
                        }

                        public List<WordsBean> getWords() {
                            return words;
                        }

                        public void setWords(List<WordsBean> words) {
                            this.words = words;
                        }

                        public static class PropertyBeanXX {
                            private List<DetectedLanguagesBeanXX> detectedLanguages;

                            public List<DetectedLanguagesBeanXX> getDetectedLanguages() {
                                return detectedLanguages;
                            }

                            public void setDetectedLanguages(List<DetectedLanguagesBeanXX> detectedLanguages) {
                                this.detectedLanguages = detectedLanguages;
                            }

                            public static class DetectedLanguagesBeanXX {
                                /**
                                 * languageCode : en
                                 */

                                private String languageCode;

                                public String getLanguageCode() {
                                    return languageCode;
                                }

                                public void setLanguageCode(String languageCode) {
                                    this.languageCode = languageCode;
                                }
                            }
                        }

                        public static class BoundingBoxBeanX {
                            private List<VerticesBeanX> vertices;

                            public List<VerticesBeanX> getVertices() {
                                return vertices;
                            }

                            public void setVertices(List<VerticesBeanX> vertices) {
                                this.vertices = vertices;
                            }

                            public static class VerticesBeanX {
                                /**
                                 * x : 46
                                 * y : 41
                                 */

                                private int x;
                                private int y;

                                public int getX() {
                                    return x;
                                }

                                public void setX(int x) {
                                    this.x = x;
                                }

                                public int getY() {
                                    return y;
                                }

                                public void setY(int y) {
                                    this.y = y;
                                }
                            }
                        }

                        public static class WordsBean {
                            /**
                             * property : {"detectedLanguages":[{"languageCode":"en"}]}
                             * boundingBox : {"vertices":[{"x":46,"y":44},{"x":179,"y":41},{"x":180,"y":81},{"x":47,"y":84}]}
                             * symbols : [{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":46,"y":44},{"x":71,"y":43},{"x":72,"y":83},{"x":47,"y":84}]},"text":"A"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":74,"y":43},{"x":100,"y":42},{"x":101,"y":81},{"x":75,"y":82}]},"text":"B"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":101,"y":43},{"x":126,"y":42},{"x":127,"y":81},{"x":102,"y":82}]},"text":"B"},{"property":{"detectedLanguages":[{"languageCode":"en"}]},"boundingBox":{"vertices":[{"x":132,"y":42},{"x":152,"y":42},{"x":153,"y":82},{"x":133,"y":82}]},"text":"E"},{"property":{"detectedLanguages":[{"languageCode":"en"}],"detectedBreak":{"type":"EOL_SURE_SPACE"}},"boundingBox":{"vertices":[{"x":156,"y":42},{"x":179,"y":42},{"x":180,"y":82},{"x":157,"y":82}]},"text":"Y"}]
                             */

                            private PropertyBeanXXX property;
                            private BoundingBoxBeanXX boundingBox;
                            private List<SymbolsBean> symbols;

                            public PropertyBeanXXX getProperty() {
                                return property;
                            }

                            public void setProperty(PropertyBeanXXX property) {
                                this.property = property;
                            }

                            public BoundingBoxBeanXX getBoundingBox() {
                                return boundingBox;
                            }

                            public void setBoundingBox(BoundingBoxBeanXX boundingBox) {
                                this.boundingBox = boundingBox;
                            }

                            public List<SymbolsBean> getSymbols() {
                                return symbols;
                            }

                            public void setSymbols(List<SymbolsBean> symbols) {
                                this.symbols = symbols;
                            }

                            public static class PropertyBeanXXX {
                                private List<DetectedLanguagesBeanXXX> detectedLanguages;

                                public List<DetectedLanguagesBeanXXX> getDetectedLanguages() {
                                    return detectedLanguages;
                                }

                                public void setDetectedLanguages(List<DetectedLanguagesBeanXXX> detectedLanguages) {
                                    this.detectedLanguages = detectedLanguages;
                                }

                                public static class DetectedLanguagesBeanXXX {
                                    /**
                                     * languageCode : en
                                     */

                                    private String languageCode;

                                    public String getLanguageCode() {
                                        return languageCode;
                                    }

                                    public void setLanguageCode(String languageCode) {
                                        this.languageCode = languageCode;
                                    }
                                }
                            }

                            public static class BoundingBoxBeanXX {
                                private List<VerticesBeanXX> vertices;

                                public List<VerticesBeanXX> getVertices() {
                                    return vertices;
                                }

                                public void setVertices(List<VerticesBeanXX> vertices) {
                                    this.vertices = vertices;
                                }

                                public static class VerticesBeanXX {
                                    /**
                                     * x : 46
                                     * y : 44
                                     */

                                    private int x;
                                    private int y;

                                    public int getX() {
                                        return x;
                                    }

                                    public void setX(int x) {
                                        this.x = x;
                                    }

                                    public int getY() {
                                        return y;
                                    }

                                    public void setY(int y) {
                                        this.y = y;
                                    }
                                }
                            }

                            public static class SymbolsBean {
                                /**
                                 * property : {"detectedLanguages":[{"languageCode":"en"}]}
                                 * boundingBox : {"vertices":[{"x":46,"y":44},{"x":71,"y":43},{"x":72,"y":83},{"x":47,"y":84}]}
                                 * text : A
                                 */

                                private PropertyBeanXXXX property;
                                private BoundingBoxBeanXXX boundingBox;
                                private String text;

                                public PropertyBeanXXXX getProperty() {
                                    return property;
                                }

                                public void setProperty(PropertyBeanXXXX property) {
                                    this.property = property;
                                }

                                public BoundingBoxBeanXXX getBoundingBox() {
                                    return boundingBox;
                                }

                                public void setBoundingBox(BoundingBoxBeanXXX boundingBox) {
                                    this.boundingBox = boundingBox;
                                }

                                public String getText() {
                                    return text;
                                }

                                public void setText(String text) {
                                    this.text = text;
                                }

                                public static class PropertyBeanXXXX {
                                    private List<DetectedLanguagesBeanXXXX> detectedLanguages;

                                    public List<DetectedLanguagesBeanXXXX> getDetectedLanguages() {
                                        return detectedLanguages;
                                    }

                                    public void setDetectedLanguages(List<DetectedLanguagesBeanXXXX> detectedLanguages) {
                                        this.detectedLanguages = detectedLanguages;
                                    }

                                    public static class DetectedLanguagesBeanXXXX {
                                        /**
                                         * languageCode : en
                                         */

                                        private String languageCode;

                                        public String getLanguageCode() {
                                            return languageCode;
                                        }

                                        public void setLanguageCode(String languageCode) {
                                            this.languageCode = languageCode;
                                        }
                                    }
                                }

                                public static class BoundingBoxBeanXXX {
                                    private List<VerticesBeanXXX> vertices;

                                    public List<VerticesBeanXXX> getVertices() {
                                        return vertices;
                                    }

                                    public void setVertices(List<VerticesBeanXXX> vertices) {
                                        this.vertices = vertices;
                                    }

                                    public static class VerticesBeanXXX {
                                        /**
                                         * x : 46
                                         * y : 44
                                         */

                                        private int x;
                                        private int y;

                                        public int getX() {
                                            return x;
                                        }

                                        public void setX(int x) {
                                            this.x = x;
                                        }

                                        public int getY() {
                                            return y;
                                        }

                                        public void setY(int y) {
                                            this.y = y;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        public static class TextAnnotationsBean {
            /**
             * locale : en
             * description : ABBEY
             ROAD NW8
             CITY OF WESTMINSTER

             * boundingPoly : {"vertices":[{"x":46,"y":41},{"x":269,"y":41},{"x":269,"y":177},{"x":46,"y":177}]}
             */

            private String locale;
            private String description;
            private BoundingPolyBean boundingPoly;

            public String getLocale() {
                return locale;
            }

            public void setLocale(String locale) {
                this.locale = locale;
            }

            public String getDescription() {
                return description;
            }

            public void setDescription(String description) {
                this.description = description;
            }

            public BoundingPolyBean getBoundingPoly() {
                return boundingPoly;
            }

            public void setBoundingPoly(BoundingPolyBean boundingPoly) {
                this.boundingPoly = boundingPoly;
            }

            public static class BoundingPolyBean {
                private List<VerticesBeanXXXX> vertices;

                public List<VerticesBeanXXXX> getVertices() {
                    return vertices;
                }

                public void setVertices(List<VerticesBeanXXXX> vertices) {
                    this.vertices = vertices;
                }

                public static class VerticesBeanXXXX {
                    /**
                     * x : 46
                     * y : 41
                     */

                    private int x;
                    private int y;

                    public int getX() {
                        return x;
                    }

                    public void setX(int x) {
                        this.x = x;
                    }

                    public int getY() {
                        return y;
                    }

                    public void setY(int y) {
                        this.y = y;
                    }
                }
            }
        }
    }
}
