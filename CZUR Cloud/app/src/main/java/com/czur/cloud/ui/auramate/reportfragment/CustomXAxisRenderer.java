package com.czur.cloud.ui.auramate.reportfragment;

import android.graphics.Canvas;
import android.graphics.Paint;

import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.renderer.XAxisRenderer;
import com.github.mikephil.charting.utils.MPPointF;
import com.github.mikephil.charting.utils.Transformer;
import com.github.mikephil.charting.utils.Utils;
import com.github.mikephil.charting.utils.ViewPortHandler;

public class CustomXAxisRenderer extends XAxisRenderer {
    public CustomXAxisRenderer(ViewPortHandler viewPortHandler, XAxis xAxis, Transformer trans){
        super(viewPortHandler, xAxis, trans);
    }
    @Override
    protected void drawLabel(Canvas c, String formattedLabel, float x, float y, MPPointF anchor, float angleDegrees) {
        //super.drawLabel(c, formattedLabel, x, y, anchor, angleDegrees);//注释掉
        String[] labels = formattedLabel.split("\n");
//        for (int i = 0; i < labels.length; i++) {
//            float vOffset = i * mAxisLabelPaint.getTextSize();
//            Utils.drawXAxisValue(c, labels[i], x, y + vOffset, mAxisLabelPaint, anchor, angleDegrees);
//        }

        float labelHeight = mXAxis.getTextSize();
        float labelInterval = 5f;

        Paint mFirstLinePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mFirstLinePaint.setColor(0xFF9b9b9b);
        mFirstLinePaint.setTextAlign(Paint.Align.LEFT);
        mFirstLinePaint.setTextSize(Utils.convertDpToPixel(10f));
        mFirstLinePaint.setTypeface(mXAxis.getTypeface());

        Paint mSecondLinePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mSecondLinePaint.setColor(0xFF9b9b9b);
        mSecondLinePaint.setTextAlign(Paint.Align.LEFT);
        mSecondLinePaint.setTextSize(Utils.convertDpToPixel(10f));
        mSecondLinePaint.setTypeface(mXAxis.getTypeface());

        if (labels.length > 1) {
            float width0 = Utils.calcTextWidth(mAxisLabelPaint, labels[0]);
            float labelWidth0 = width0 / 2;
            float width1 = Utils.calcTextWidth(mAxisLabelPaint, labels[1]);
            float labelWidth1 = width1 / 2;
            float interWidth = labelWidth0-labelWidth1;
            Utils.drawXAxisValue(c, labels[0], x, y, mFirstLinePaint, anchor, angleDegrees);
            Utils.drawXAxisValue(c, labels[1], x-interWidth, y + labelHeight + labelInterval, mSecondLinePaint, anchor, angleDegrees);
        } else {
            Utils.drawXAxisValue(c, formattedLabel, x, y, mFirstLinePaint, anchor, angleDegrees);
        }
    }
}