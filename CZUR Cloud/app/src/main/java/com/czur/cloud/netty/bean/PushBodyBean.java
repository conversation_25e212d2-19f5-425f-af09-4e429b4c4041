package com.czur.cloud.netty.bean;

/**
 * Created by Yz on 2018/10/29.
 * Email：<EMAIL>
 */
public class PushBodyBean {


    /**
     * type : PUSH
     * body : {"title":"what离线了","alert":"ALERT","sound":"happy","msg_content":"what离线了","badge":0,"extra":""}
     * timestamp : 1572082860377
     */

    private String type;
    private BodyBean body;
    private long timestamp;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public BodyBean getBody() {
        return body;
    }

    public void setBody(BodyBean body) {
        this.body = body;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public static class BodyBean {
        /**
         * title : what离线了
         * alert : ALERT
         * sound : happy
         * msg_content : what离线了
         * badge : 0
         * extra :
         */

        private String title;
        private String alert;
        private String sound;
        private String msg_content;
        private int badge;
        private String extra;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getAlert() {
            return alert;
        }

        public void setAlert(String alert) {
            this.alert = alert;
        }

        public String getSound() {
            return sound;
        }

        public void setSound(String sound) {
            this.sound = sound;
        }

        public String getMsg_content() {
            return msg_content;
        }

        public void setMsg_content(String msg_content) {
            this.msg_content = msg_content;
        }

        public int getBadge() {
            return badge;
        }

        public void setBadge(int badge) {
            this.badge = badge;
        }

        public String getExtra() {
            return extra;
        }

        public void setExtra(String extra) {
            this.extra = extra;
        }
    }
}
