package com.czur.cloud.ui.starry.meeting.baselib.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import com.czur.cloud.R
import kotlin.math.min

/**
 * Created by 陈丰尧 on 1/12/21
 */
class CircleView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    private var paintColor = Color.WHITE
    var circleColor: Int
        get() = paintColor
        set(value) {
            paintColor = value
            paint.color = paintColor
            invalidate()
        }
    private val paint = Paint()


    init {
        val ta = context.obtainStyledAttributes(attrs, R.styleable.CircleView)
        paintColor = ta.getColor(R.styleable.CircleView_circleColor, Color.WHITE)
        ta.recycle()

        paint.isAntiAlias = true
        paint.color = paintColor
        paint.style = Paint.Style.FILL
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val width = measuredWidth
        val height = measuredHeight
        // 宽高的最小值为半径
        val diameter = min(width, height)
        setMeasuredDimension(diameter, diameter)
    }

    override fun onDraw(canvas: Canvas) {
        val r = width / 2f //圆的半径
        canvas.drawCircle(width / 2f, height / 2f, r, paint)
    }


}