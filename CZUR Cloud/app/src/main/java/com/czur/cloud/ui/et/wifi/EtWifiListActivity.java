package com.czur.cloud.ui.et.wifi;

import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.location.LocationManager;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.Group;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.badoo.mobile.util.WeakHandler;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.WifiAdapter;
import com.czur.cloud.entity.WifiEntity;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.NoHintEditText;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.util.EtUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Yz on 2018/6/1.
 * Email：<EMAIL>
 */
public class EtWifiListActivity extends BaseActivity implements View.OnClickListener {


    private ImageView normalBackBtn;
    private TextView etConnectWifiNameTv;
    private TextView etWifiChooseWifiBtn;
    private NoHintEditText etConnectWifiPasswordEdt;
    private RecyclerView etWifiRecyclerView;
    private Group noWifiGroup;
    private Group wifiListGroup;
    private Group wifiInputGroup;
    private TextView etWifiNextStepBtn;
    private List<ScanResult> scanResults;
    private List<WifiEntity> wifiEntities;
    private WifiManager wm;
    private WifiAdapter wifiAdapter;
    private WifiReceiver wifiReceiver;
    private TextView wifiWarnTv;
    private View wifiAnimView;
    private View etWifiBorder;
    private LinearLayout wifiAnimLl;
    private ImageView wifiAnimImg;
    private View wifiAnimBg;
    private ImageView wifiFoldAnimImg;
    private View wifiFoldAnimBg;

    private boolean isNeedUpdateList = true;
    private WeakHandler handler;
    private String deviceId;
    private int GPS_REQUEST_CODE = 10;
    private long currentTimeMillis;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_et_wifi);
        initComponent();
        registerEvent();
        initRecyclerView();
        refreshWifiList(false);
    }

    private void initComponent() {
        handler = new WeakHandler();
        deviceId = getIntent().getStringExtra("deviceId");
        normalBackBtn = (ImageView) findViewById(R.id.normal_back_btn);
        etConnectWifiNameTv = (TextView) findViewById(R.id.et_connect_wifi_name_tv);
        etWifiChooseWifiBtn = (TextView) findViewById(R.id.et_wifi_choose_wifi_btn);
        etConnectWifiPasswordEdt = (NoHintEditText) findViewById(R.id.et_connect_wifi_password_edt);
        etWifiRecyclerView = (RecyclerView) findViewById(R.id.et_wifi_recyclerView);
        wifiWarnTv = (TextView) findViewById(R.id.wifi_warn_tv);
        noWifiGroup = (Group) findViewById(R.id.no_wifi_group);
        wifiListGroup = (Group) findViewById(R.id.wifi_list_group);
        wifiInputGroup = (Group) findViewById(R.id.wifi_input_group);
        etWifiBorder = (View) findViewById(R.id.et_wifi_border);
        etWifiNextStepBtn = (TextView) findViewById(R.id.et_wifi_next_step_btn);
        wifiAnimView = (View) findViewById(R.id.wifi_anim_view);
        wifiAnimLl = (LinearLayout) findViewById(R.id.wifi_anim_ll);
        wifiAnimImg = (ImageView) findViewById(R.id.wifi_anim_img);
        wifiAnimBg = (View) findViewById(R.id.wifi_anim_bg);
        wifiFoldAnimImg = (ImageView) findViewById(R.id.wifi_fold_anim_img);
        wifiFoldAnimBg = (View) findViewById(R.id.wifi_fold_anim_bg);

        if (!NetworkUtils.getWifiEnabled()) {
            NetworkUtils.setWifiEnabled(true);
        }

        // 获取系统wifi服务
        wm = (WifiManager) getApplicationContext().getSystemService(WIFI_SERVICE);
        registerBroadcastReceiver();

        if (!checkGPSIsOpen()) {
            confirmGpsDialog();
        }
    }

    /**
     * @des: 显示删除Dialog
     * @params:
     * @return:
     */

    private void confirmGpsDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(EtWifiListActivity.this, CloudCommonPopupConstants.GPS_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.gps_permission));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                Intent intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                startActivityForResult(intent, GPS_REQUEST_CODE);
                dialog.dismiss();

            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                finish();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();
    }

    private void registerEvent() {
        normalBackBtn.setOnClickListener(this);
        etWifiNextStepBtn.setOnClickListener(this);
        etWifiChooseWifiBtn.setOnClickListener(this);
    }

    /**
     * 检测GPS是否打开
     *
     * @return
     */
    private boolean checkGPSIsOpen() {
        boolean isOpen;
        LocationManager locationManager = (LocationManager) this
                .getSystemService(Context.LOCATION_SERVICE);
        isOpen = locationManager.isProviderEnabled(android.location.LocationManager.GPS_PROVIDER);
        return isOpen;
    }

    /**
     * @des: 初始化列表
     * @params:
     * @return:
     */

    private void initRecyclerView() {
        wifiEntities = new ArrayList<>();
        scanResults = new ArrayList<>();
        wifiAdapter = new WifiAdapter(this, wifiEntities);
        wifiAdapter.setOnItemClickListener(onItemClickListener);
        etWifiRecyclerView.setHasFixedSize(true);
        etWifiRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        etWifiRecyclerView.setAdapter(wifiAdapter);
    }

    private void registerBroadcastReceiver() {
        IntentFilter filter = new IntentFilter();
        //设置意图过滤
        filter.addAction(WifiManager.WIFI_STATE_CHANGED_ACTION);
        filter.addAction(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION);
        filter.addAction(WifiManager.NETWORK_STATE_CHANGED_ACTION);
        wifiReceiver = new WifiReceiver(wifiReceiverActionListener);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            registerReceiver(wifiReceiver, filter, Context.RECEIVER_NOT_EXPORTED);
        }else {
            registerReceiver(wifiReceiver, filter);
        }

    }

    private WifiReceiverActionListener wifiReceiverActionListener = new WifiReceiverActionListener() {
        @Override
        public void onWifiConnected(WifiInfo wifiInfo) {
            logI(wifiInfo.getSSID());
        }

        @Override
        public void onWifiScanResultBack() {
            if (System.currentTimeMillis() - currentTimeMillis < 5000) {
                return;
            }
            logI("onWifiScanResultBack xx");
            refreshWifiList(false);
        }

        @Override
        public void onWifiOpened() {
            logI("onWifiOpened");
        }

        @Override
        public void onWifiOpening() {
            logI("onWifiOpening");
        }

        @Override
        public void onWifiClosed() {
            logI("onWifiClosed");
        }

        @Override
        public void onWifiClosing() {
            logI("onWifiClosing");
        }
    };

    private void refreshWifiList(boolean isShowAnim) {
        if (isNeedUpdateList) {
            LinkedHashMap<String, WifiEntity> tempWifiMap = new LinkedHashMap<>();
            scanResults = getAllNetWorkList();
            wifiEntities = new ArrayList<>();
            for (ScanResult scanResult : scanResults) {
                WifiEntity wifiEntity = new WifiEntity();
                wifiEntity.setSsid(scanResult.SSID);
                wifiEntity.setLevel(scanResult.level);
                tempWifiMap.put(scanResult.SSID, wifiEntity);
            }
            for (Map.Entry<String, WifiEntity> entry : tempWifiMap.entrySet()) {
                wifiEntities.add(entry.getValue());
            }
            if (wifiEntities.size() > 0) {
                currentTimeMillis = System.currentTimeMillis();
                ViewGroup.LayoutParams layoutParams = etWifiBorder.getLayoutParams();
                ViewGroup.LayoutParams animContainerLayoutParams = wifiAnimLl.getLayoutParams();
                if (wifiEntities.size() <= 6) {
                    layoutParams.height = SizeUtils.dp2px(60 * wifiEntities.size());
                    animContainerLayoutParams.height = SizeUtils.dp2px(60 * wifiEntities.size() + 36);
                } else {
                    layoutParams.height = SizeUtils.dp2px(60 * 6.5f);
                    animContainerLayoutParams.height = SizeUtils.dp2px(436);
                }
                etWifiBorder.setLayoutParams(layoutParams);
                wifiAnimLl.setLayoutParams(animContainerLayoutParams);
                if (isShowAnim) {
                    expandAnim(layoutParams);
                } else {
                    hideAnimLayouts();
                    wifiListGroup.setVisibility(View.VISIBLE);
                    wifiInputGroup.setVisibility(View.INVISIBLE);
                    noWifiGroup.setVisibility(View.GONE);
                }
            } else {
                hideAnimLayouts();
                wifiListGroup.setVisibility(View.GONE);
                wifiInputGroup.setVisibility(View.INVISIBLE);
                noWifiGroup.setVisibility(View.VISIBLE);
            }
            wifiAdapter.refreshData(wifiEntities);
        }

    }

    private void showAnimLayouts() {
        wifiAnimBg.setVisibility(View.VISIBLE);
        wifiAnimImg.setVisibility(View.VISIBLE);
        wifiAnimView.setVisibility(View.VISIBLE);
    }

    private void hideAnimLayouts() {
        wifiAnimBg.setVisibility(View.GONE);
        wifiAnimImg.setVisibility(View.GONE);
        wifiAnimView.setVisibility(View.GONE);
    }

    private void expandAnim(final ViewGroup.LayoutParams anim) {
        isNeedUpdateList = false;
        wifiListGroup.setVisibility(View.VISIBLE);
        showAnimLayouts();
        ValueAnimator scanAnim = ValueAnimator.ofInt(0, anim.height);
        scanAnim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                int currentHeight = (Integer) animation.getAnimatedValue();
                ViewGroup.LayoutParams layoutParams = wifiAnimView.getLayoutParams();
                layoutParams.height = currentHeight;
                wifiAnimView.setLayoutParams(layoutParams);
                if (currentHeight == anim.height) {
                    hideAnimLayouts();
                    wifiListGroup.setVisibility(View.VISIBLE);
                    wifiInputGroup.setVisibility(View.INVISIBLE);
                    noWifiGroup.setVisibility(View.GONE);
                }
            }
        });
        scanAnim.setDuration(200);
        scanAnim.start();
    }

    private void foldAnim() {
        final ViewGroup.LayoutParams totalLayoutParams = etWifiBorder.getLayoutParams();
        ValueAnimator scanAnim = ValueAnimator.ofInt(0, totalLayoutParams.height);
        scanAnim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                int currentHeight = (Integer) animation.getAnimatedValue();
                ViewGroup.LayoutParams layoutParams = wifiFoldAnimBg.getLayoutParams();
                layoutParams.height = currentHeight;
                wifiFoldAnimBg.setLayoutParams(layoutParams);
                if (currentHeight == totalLayoutParams.height) {
                    hideFoldAnimLayouts();
                    wifiListGroup.setVisibility(View.GONE);
                    noWifiGroup.setVisibility(View.GONE);
                    wifiInputGroup.setVisibility(View.VISIBLE);
                    showInputLayouts();
                }
            }
        });
        scanAnim.setDuration(200);
        scanAnim.start();
    }

    public List<ScanResult> getAllNetWorkList() {
        wm.startScan();
        List<ScanResult> list = wm.getScanResults();
        List<ScanResult> finalList = null;
        if (list != null) {
            finalList = new ArrayList<>();
            for (ScanResult scanResult : list) {
                if (scanResult.frequency > 2400 && scanResult.frequency < 2500) {
                    finalList.add(scanResult);
                }
            }

            for (int i = 0; i < finalList.size(); i++) {
                int frequency = finalList.get(i).frequency;
                if (frequency > 2400 && frequency < 2500) {
                    for (int j = 1; j < finalList.size(); j++) {
                        //level属性即为强度
                        if (finalList.get(i).level < finalList.get(j).level) {
                            ScanResult temp = finalList.get(i);
                            finalList.set(i, finalList.get(j));
                            finalList.set(j, temp);
                        }
                    }
                }
            }
        }
        return finalList;
    }

    private WifiAdapter.onItemClickListener onItemClickListener = new WifiAdapter.onItemClickListener() {
        @Override
        public void onItemClick(int position, WifiEntity wifiEntity) {
            etWifiNextStepBtn.setVisibility(View.VISIBLE);
            etWifiNextStepBtn.setAlpha(0);
            etWifiNextStepBtn.setClickable(false);
            isNeedUpdateList = false;
            hideAnimLayouts();
            showFoldAnimLayouts();
            foldAnim();
            etConnectWifiNameTv.setText(wifiEntity.getSsid());
        }
    };

    private void showFoldAnimLayouts() {
        wifiFoldAnimBg.setVisibility(View.VISIBLE);
        wifiFoldAnimImg.setVisibility(View.VISIBLE);
    }

    private void hideFoldAnimLayouts() {
        wifiFoldAnimBg.setVisibility(View.GONE);
        wifiFoldAnimImg.setVisibility(View.GONE);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.normal_back_btn:
                finish();
                break;
            case R.id.et_wifi_choose_wifi_btn:
                isNeedUpdateList = true;
                hideInputLayouts();
                break;
            case R.id.et_wifi_next_step_btn:
                String edtString = etConnectWifiPasswordEdt.getText().toString();
                if (EtUtils.containsEmoji(edtString)) {
                    CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(EtWifiListActivity.this, CloudCommonPopupConstants.COMMON_ONE_BUTTON);
                    builder.setTitle(getResources().getString(R.string.prompt));
                    builder.setMessage(getResources().getString(R.string.nickname_toast_symbol));
                    builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            dialog.dismiss();
                        }
                    });
                    CloudCommonPopup commonPopup = builder.create();
                    commonPopup.show();
                } else {
                    Intent intent = new Intent(this, GenerateMp3Service.class);
                    intent.putExtra("ssid", etConnectWifiNameTv.getText().toString());
                    intent.putExtra("password", etConnectWifiPasswordEdt.getText().toString());
                    intent.putExtra("deviceId", deviceId);
                    startService(intent);

                    Intent intent1 = new Intent(this, WifiConnectResetActivity.class);
                    intent1.putExtra("ssid", etConnectWifiNameTv.getText().toString());
                    intent1.putExtra("password", etConnectWifiPasswordEdt.getText().toString());
                    intent1.putExtra("deviceId", deviceId);
                    startActivity(intent1);

                }
                break;
            default:
                break;
        }
    }

    private void showInputLayouts() {
        ObjectAnimator showNameTvAnim = ObjectAnimator.ofFloat(etConnectWifiNameTv, "alpha", 0, 1.0f);
        ObjectAnimator showInputAnim = ObjectAnimator.ofFloat(etWifiChooseWifiBtn, "alpha", 0, 1.0f);
        ObjectAnimator showPasswordAnim = ObjectAnimator.ofFloat(etConnectWifiPasswordEdt, "alpha", 0, 1.0f);
        ObjectAnimator showNextBtnAnim = ObjectAnimator.ofFloat(etWifiNextStepBtn, "alpha", 0, 1.0f);
        ObjectAnimator showWarningTvAnim = ObjectAnimator.ofFloat(wifiWarnTv, "alpha", 0, 1.0f);
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(showNameTvAnim, showInputAnim, showPasswordAnim, showNextBtnAnim, showWarningTvAnim);
        animatorSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                etWifiNextStepBtn.setClickable(true);
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        animatorSet.setDuration(200);
        animatorSet.start();

    }


    private void hideInputLayouts() {
        ObjectAnimator hideNameTvAnim = ObjectAnimator.ofFloat(etConnectWifiNameTv, "alpha", 1.0f, 0);
        ObjectAnimator hideInputAnim = ObjectAnimator.ofFloat(etWifiChooseWifiBtn, "alpha", 1.0f, 0);
        ObjectAnimator hidePasswordAnim = ObjectAnimator.ofFloat(etConnectWifiPasswordEdt, "alpha", 1.0f, 0);
        ObjectAnimator hideNextBtnAnim = ObjectAnimator.ofFloat(etWifiNextStepBtn, "alpha", 1.0f, 0);
        ObjectAnimator hideWarningTvAnim = ObjectAnimator.ofFloat(wifiWarnTv, "alpha", 1.0f, 0);
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(hideNameTvAnim, hideInputAnim, hidePasswordAnim, hideNextBtnAnim, hideWarningTvAnim);
        animatorSet.setDuration(200);
        animatorSet.addListener(hideInputAnimListener);
        animatorSet.start();

    }

    private Animator.AnimatorListener hideInputAnimListener = new Animator.AnimatorListener() {
        @Override
        public void onAnimationStart(Animator animation) {
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    refreshWifiList(true);
                }
            }, 150);

        }

        @Override
        public void onAnimationEnd(Animator animation) {
            wifiInputGroup.setVisibility(View.INVISIBLE);
        }

        @Override
        public void onAnimationCancel(Animator animation) {

        }

        @Override
        public void onAnimationRepeat(Animator animation) {

        }
    };


    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (null != wifiReceiver) {
            unregisterReceiver(wifiReceiver);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == GPS_REQUEST_CODE) {
            //做需要做的事情，比如再次检测是否打开GPS了 或者定位
            if (!checkGPSIsOpen()) {
                confirmGpsDialog();
            }
        }
    }
}
