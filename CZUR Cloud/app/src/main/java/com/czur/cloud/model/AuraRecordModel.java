package com.czur.cloud.model;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2016/9/9.
 */
public class AuraRecordModel implements Serializable {

    private int pageNum;
    private int pageSize;
    private int startRec;
    private int endRec;
    private int total;
    private int pages;
    private int pageLen;
    private int startPage;
    private int endPage;
    private List<ResultDTO> result;

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getStartRec() {
        return startRec;
    }

    public void setStartRec(int startRec) {
        this.startRec = startRec;
    }

    public int getEndRec() {
        return endRec;
    }

    public void setEndRec(int endRec) {
        this.endRec = endRec;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getPages() {
        return pages;
    }

    public void setPages(int pages) {
        this.pages = pages;
    }

    public int getPageLen() {
        return pageLen;
    }

    public void setPageLen(int pageLen) {
        this.pageLen = pageLen;
    }

    public int getStartPage() {
        return startPage;
    }

    public void setStartPage(int startPage) {
        this.startPage = startPage;
    }

    public int getEndPage() {
        return endPage;
    }

    public void setEndPage(int endPage) {
        this.endPage = endPage;
    }

    public List<ResultDTO> getResult() {
        return result;
    }

    public void setResult(List<ResultDTO> result) {
        this.result = result;
    }

    public static class ResultDTO {
        private int id;
        private String roomId;
        private String recorderUid;
        private String resourceId;
        private String agoraSid;
        private int agoraStatus;
        private String agoraFilesKey;
        private String fileName;
        private long startTime;
        private long endTime;
        private boolean stopped;

        private boolean isChecked;

        public boolean isChecked() {
            return isChecked;
        }

        public void setChecked(boolean checked) {
            isChecked = checked;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getRoomId() {
            return roomId;
        }

        public void setRoomId(String roomId) {
            this.roomId = roomId;
        }

        public String getRecorderUid() {
            return recorderUid;
        }

        public void setRecorderUid(String recorderUid) {
            this.recorderUid = recorderUid;
        }

        public String getResourceId() {
            return resourceId;
        }

        public void setResourceId(String resourceId) {
            this.resourceId = resourceId;
        }

        public String getAgoraSid() {
            return agoraSid;
        }

        public void setAgoraSid(String agoraSid) {
            this.agoraSid = agoraSid;
        }

        public int getAgoraStatus() {
            return agoraStatus;
        }

        public void setAgoraStatus(int agoraStatus) {
            this.agoraStatus = agoraStatus;
        }

        public String getAgoraFilesKey() {
            return agoraFilesKey;
        }

        public void setAgoraFilesKey(String agoraFilesKey) {
            this.agoraFilesKey = agoraFilesKey;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public long getStartTime() {
            return startTime;
        }

        public void setStartTime(long startTime) {
            this.startTime = startTime;
        }

        public long getEndTime() {
            return endTime;
        }

        public void setEndTime(long endTime) {
            this.endTime = endTime;
        }

        public boolean isStopped() {
            return stopped;
        }

        public void setStopped(boolean stopped) {
            this.stopped = stopped;
        }
    }
}
