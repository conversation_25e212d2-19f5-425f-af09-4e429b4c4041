package com.czur.cloud.ui.starry.network;

import static com.czur.czurutils.log.CZURLogUtilsKt.logE;

import android.app.Application;
import android.os.Handler;
import android.os.Looper;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;

class AsyncHttpTaskStarry<T> implements Runnable {

    private Application application;
    private String url;
    private boolean isMethodGet;
    private Type type;
    private HashMap<String, String> postParam;
    private StarryHttpManager.Callback<T> callback;
    private HashMap<String, String> headers;
    private Handler handler = new Handler(Looper.getMainLooper());
    private ArrayList<String> logs;
    private boolean isRetry;

    AsyncHttpTaskStarry(Application application, String url, Type type, StarryHttpManager.Callback<T> callback, HashMap<String, String> headers, ArrayList<String> logs, boolean isRetry) {
        isMethodGet = true;
        this.application = application;
        this.url = url;
        this.type = type;
        this.callback = callback;
        this.headers = headers;
        this.logs = logs;
        this.isRetry = isRetry;
    }

    AsyncHttpTaskStarry(Application application, String url, Type type, HashMap<String, String> postParam, StarryHttpManager.Callback<T> callback, HashMap<String, String> headers, ArrayList<String> logs, boolean isRetry) {
        isMethodGet = false;
        this.application = application;
        this.url = url;
        this.type = type;
        this.postParam = postParam;
        this.callback = callback;
        this.headers = headers;
        this.logs = logs;
        this.isRetry = isRetry;
    }

    @Override
    public void run() {
        try {
            final StarryHttpEntity<T> entity;
            if (isMethodGet) {
                entity = SyncHttpTaskStarry.getInstance().syncGet(application, url, type, headers, logs, isRetry);
            } else {
                entity = SyncHttpTaskStarry.getInstance().syncPost(application, url, type, postParam, headers, logs, isRetry);
            }
            if (entity != null) {
                switch (entity.getCode()) {
                    case StarryHttpManager.STATUS_SUCCESS:
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callback.onResponse(entity);
                            }
                        });
                        break;
                    case StarryHttpManager.STATUS_FAIL:
                        handler.post(new ErrorRunnable(new Exception("服务器异常")));
                        break;
                    case StarryHttpManager.STATUS_TIMEOUT:
                    case StarryHttpManager.STATUS_SINGLE_LOGIN:
                        break;
                    default:

                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callback.onFailure(entity);
                            }
                        });
                        break;
                }
            } else {
                handler.post(new ErrorRunnable(new IOException("请求失败,还没封装异常处理")));
            }
        } catch (Exception e) {
            handler.post(new ErrorRunnable(e));
        }
    }

    private class ErrorRunnable implements Runnable {
        private Exception exception;

        ErrorRunnable(Exception exception) {
            this.exception = exception;
        }

        @Override
        public void run() {
            exception.printStackTrace();
            logE("ErrorRunnable.run."+exception.getMessage());
            callback.onError(exception);
        }
    }
}
