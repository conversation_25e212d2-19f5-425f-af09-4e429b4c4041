package com.czur.cloud.ui.starry.meeting.baselib.widget

import android.content.Context
import android.util.AttributeSet
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.MotionEvent.ACTION_SCROLL
import android.view.View
import android.view.ViewGroup
import android.webkit.WebView
import android.widget.FrameLayout
import androidx.core.view.forEach
import kotlin.math.abs
import kotlin.math.pow

/**
 * Created by 陈丰尧 on 2021/8/11
 */
class ScrollWebFrameLayout @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    companion object {
        private const val TAG = "ScrollWebFrameLayout"

        private const val SHIFT_THRESHOLD = 70 // shift按下后70ms内, 都是横向滑动
        private const val SCROLL_THRESHOLD = 200 // 超过200ms的滑动事件, 就认为是2次事件
        private const val SCROLL_STEP_PX = 50  // 滚轮滚一次, 页面滑动的像素值
    }

    private var lastShiftPress = 0L
    private var lastScrollTime = 0L

    // 是否要响应shift事件
    private val respondToShift: Boolean
        get() {
            return System.currentTimeMillis() - lastShiftPress <= SHIFT_THRESHOLD
        }


    private var innerWebView: WebView? = null

    /**
     * 绑定指定的WebView
     */
    fun bindWebView(webView: WebView) {
        innerWebView = webView
    }

    /**
     * 解绑指定的WebView
     */
    fun unBindView() {
        innerWebView = null
    }


    override fun dispatchKeyEvent(event: KeyEvent?): Boolean {
        if (event?.keyCode == KeyEvent.KEYCODE_SHIFT_LEFT) {
            // 记录下 上次shift键被按下的时间(包含按下和抬起2个事件)
            lastShiftPress = System.currentTimeMillis()
        }
        return super.dispatchKeyEvent(event)
    }

    override fun dispatchGenericMotionEvent(event: MotionEvent?): Boolean {
        innerWebView?.let {
            // 绑定联网webview,才会去处理滚动事件
            if (event?.action == ACTION_SCROLL) {
                // 是滚动事件
                if (System.currentTimeMillis() - lastScrollTime >= SCROLL_THRESHOLD) {
                    // 滚动事件第一次触发, 抛弃掉, 因为不确定是否按下了shift
                    // 所以确定不了方向
                    lastScrollTime = System.currentTimeMillis()
                    return true
                }
                lastScrollTime = System.currentTimeMillis()
                if (respondToShift) {
                    val v = event.getAxisValue(MotionEvent.AXIS_VSCROLL)
                    // c=-(0.5*|v|^-2) + 2   最后的 (v / abs(v)) 是控制方向
                    val c = (-(0.5 * abs(v).toDouble().pow(-2.0) + 0.5) + 2) * (v / abs(v))
                    val dx = (c * SCROLL_STEP_PX).toInt()
                    it.scrollBy(dx, 0)
                    return true
                }
            }
        }
        return super.dispatchGenericMotionEvent(event)
    }


    override fun onViewAdded(child: View?) {
        super.onViewAdded(child)
        bindWebViewWhenAdd(child)
    }

    override fun onViewRemoved(child: View?) {
        super.onViewRemoved(child)
        unbindWebViewWhenRemove(child)
    }

    private fun bindWebViewWhenAdd(view: View?): Boolean {
        when (view) {
            is WebView -> {
                bindWebView(view)
                return true
            }
            is ViewGroup -> {
                view.forEach { child ->
                    val bindRes = bindWebViewWhenAdd(child)
                    if (bindRes) {
                        return true
                    }
                }
            }
        }
        return false
    }

    private fun unbindWebViewWhenRemove(view: View?): Boolean {
        if (innerWebView == null) {
            return true
        }
        when (view) {
            innerWebView -> {
                unBindView()
                return true
            }
            is ViewGroup -> {
                view.forEach { child ->
                    val unbindRes = unbindWebViewWhenRemove(child)
                    if (unbindRes) {
                        return true
                    }
                }
            }
        }
        return false
    }


}