package com.czur.cloud.ui.starry.base

import android.os.Bundle
import android.view.View
import android.widget.ImageView
import com.czur.cloud.R
import com.blankj.utilcode.util.BarUtils
import android.widget.TextView
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.ToastUtils
import com.czur.cloud.ui.base.BaseActivity

open class StarryBaseActivity : BaseActivity(), View.OnClickListener {
    protected var imgBack: ImageView? = null
    private var exitTime: Long = 0


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.white)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.starry_activity_base)
        imgBack = findViewById(R.id.user_back_btn)
        imgBack?.setOnClickListener(this)
        setPageTitle(R.string.starry_title)
    }

    protected fun setPageTitle(id: Int) {
        val title = findViewById<View>(R.id.user_title) as TextView
        title.setText(id)
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.user_back_btn -> ActivityUtils.finishActivity(this)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
    }

    protected fun backToHome() {
        if (System.currentTimeMillis() - exitTime > 2000) {
            ToastUtils.showShort(R.string.back_to_home)
            exitTime = System.currentTimeMillis()
        } else {
            moveTaskToBack(true)
        }
    }
}