package com.czur.cloud.util;

import static com.czur.czurutils.log.CZURLogUtilsKt.logD;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.app.Application;
import android.content.Context;

import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ServiceUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.blankj.utilcode.util.Utils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.cache.CustomCacheKeyFactory;
import com.czur.cloud.common.MobSDKUtils;
import com.czur.cloud.common.OSSInstance;
import com.czur.cloud.common.ShareSDKUtils;
import com.czur.cloud.entity.realm.BookEntity;
import com.czur.cloud.entity.realm.PageEntity;
import com.czur.cloud.netty.observer.NettyService;
import com.czur.cloud.netty.observer.NettyUtils;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.MyMigration;
import com.czur.cloud.ui.mirror.comm.OSSInstanceSitting;
import com.czur.cloud.vendorPush.VendorPushTask;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.imagepipeline.core.ImagePipelineConfig;
import com.tencent.bugly.crashreport.CrashReport;

import io.realm.Realm;
import io.realm.RealmConfiguration;
import io.realm.RealmResults;

public class initializeUtils {

    private static Application context = null;
    private final static String LOG_TAG = "CZUR";

    public static void initAll(Application application){

        context = application;

        //初始化fresco
        initFresco();
        //初始化数据库
        initRealm();
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                //初始化OSS单例
                OSSInstance.Companion.getInstance().init(application);
                OSSInstanceSitting.Companion.getInstance().init(application);
                return null;
            }

            @Override
            public void onSuccess(Void result) {

            }
        });
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                // 友盟推送
//                UMConfigure.init(application, BuildConfig.UMENG_APP_KEY, CZURConstants.CHANNEL_ID_DEFAULT, UMConfigure.DEVICE_TYPE_PHONE, "");
                MobSDKUtils.INSTANCE.init(application);

                //初始化Bugly
                CrashReport.initCrashReport(application, "615f3d0688", !BuildConfig.DEBUG);
                CrashReport.setIsDevelopmentDevice(application, BuildConfig.DEBUG);

                // 初始化shareSdk
                ShareSDKUtils.INSTANCE.init(application);
                return null;
            }

            @Override
            public void onSuccess(Void result) {

            }
        });
        // 手机厂商检查
        VendorPushTask.INSTANCE.init(application);
        registerNetworkListener();
    }

    private static void registerNetworkListener() {
        NetworkUtils.registerNetworkStatusChangedListener(onNetworkStatusChangedListener);
    }

    public static NetworkUtils.OnNetworkStatusChangedListener onNetworkStatusChangedListener = new NetworkUtils.OnNetworkStatusChangedListener() {
        @Override
        public void onDisconnected() {
            logD("<--- 网络已断开连接 --->");
//            ServiceUtils.stopService(NettyService.class);
            NettyUtils.getInstance().stopNettyService();
        }

        @Override
        public void onConnected(NetworkUtils.NetworkType networkType) {
            logD("<--- 网络已连接 --->");
            Context applicationContext = Utils.getApp().getApplicationContext();
            UserPreferences userPreferences = UserPreferences.getInstance(applicationContext);
            if (userPreferences.isUserLogin() && userPreferences.isHasAuraMate()
                && !(ServiceUtils.isServiceRunning(NettyService.class))) {
                logI("initializeUtils.onConnected.NettyService.class");
                NettyUtils.getInstance().startNettyService();
            }
        }
    };

    private static void initRealm() {
        Realm.init(context);
        RealmConfiguration config = new RealmConfiguration.Builder()
                //文件名
                .name("czur.realm")
                //版本号
                .schemaVersion(7)
                .compactOnLaunch()
                .deleteRealmIfMigrationNeeded()
                .migration(new MyMigration())
                .allowWritesOnUiThread(true)
                .allowQueriesOnUiThread(true)
                .build();
        Realm.setDefaultConfiguration(config);
        fillNoteName();
    }

    private static void fillNoteName() {
        Realm realm = Realm.getDefaultInstance();
        realm.beginTransaction();
        RealmResults<BookEntity> bookEntities = realm.where(BookEntity.class).equalTo("isDelete", 0).findAll();
        for (BookEntity bookEntity : bookEntities) {
            RealmResults<PageEntity> pageEntities = realm.where(PageEntity.class).equalTo("bookId", bookEntity.getBookId()).findAll();
            for (PageEntity pageEntity : pageEntities) {
                pageEntity.setNoteName(bookEntity.getBookName());
            }
        }
        realm.commitTransaction();
        realm.close();
    }

    private static void initFresco() {
        ImagePipelineConfig config = ImagePipelineConfig.newBuilder(context)
                .setCacheKeyFactory(CustomCacheKeyFactory.getInstance())
                .setDownsampleEnabled(true)
                .build();
        Fresco.initialize(context, config);
    }
}
