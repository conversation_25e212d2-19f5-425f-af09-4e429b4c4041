package com.czur.cloud.ui.album;

import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.ImageRecyclerAdapter;
import com.czur.cloud.entity.ImageFolder;
import com.czur.cloud.entity.ImageItem;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.home.IndexActivity;
import com.czur.cloud.util.validator.Validator;

import org.greenrobot.eventbus.EventBus;

/**
 * Created by Yz on 2018/3/15
 * Email：<EMAIL>
 */

public class ImageGridActivity extends BaseActivity implements  ImageRecyclerAdapter.OnImageItemClickListener, View.OnClickListener {


    private ImagePicker imagePicker;
    private RecyclerView mRecyclerView;
    private ImageRecyclerAdapter mRecyclerAdapter;
    private ImageFolder imageFolder;
    private TextView tvDes;
    private RelativeLayout noBackTopBarCancel;
    private LinearLayout btnBack;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this,true);
        setContentView(R.layout.activity_image_grid);
        initComponent();
        initRecyclerView();
        registerEvent();


    }
    private void initComponent() {
        ImagePickerEventModel stickyEvent = EventBus.getDefault().getStickyEvent(ImagePickerEventModel.class);
        imageFolder = stickyEvent.getImageFolder();
        EventBus.getDefault().removeStickyEvent(stickyEvent);
        //        imageFolder = (ImageFolder) getIntent().getSerializableExtra("imageFolder");
        imagePicker = ImagePicker.getInstance();
        mRecyclerView = (RecyclerView) findViewById(R.id.recycler);
        btnBack = (LinearLayout) findViewById(R.id.btn_back_ll);
        tvDes = (TextView) findViewById(R.id.tv_des);
        noBackTopBarCancel = (RelativeLayout) findViewById(R.id.album_top_bar_cancel);
        if(Validator.isNotEmpty(imageFolder)){
            tvDes.setText(imageFolder.name);
        }

    }
    private void registerEvent() {
        btnBack.setOnClickListener(this);
        noBackTopBarCancel.setOnClickListener(this);
    }



    private void initRecyclerView() {


        mRecyclerAdapter = new ImageRecyclerAdapter(this,imageFolder.images);
        mRecyclerAdapter.setOnImageItemClickListener(this);
        mRecyclerView.setLayoutManager(new GridLayoutManager(this, 3));
        mRecyclerView.addItemDecoration(new GridSpacingItemDecoration(3, SizeUtils.dp2px(10), false));
        mRecyclerView.setAdapter(mRecyclerAdapter);
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_back_ll:
                //点击返回按钮
                ActivityUtils.finishActivity(this);
                break;
            case R.id.album_top_bar_cancel:
                ActivityUtils.startActivity(IndexActivity.class);

                break;
            default:
                break;
        }
    }



    @Override
    public void onImageItemClick(View view, ImageItem imageItem, int position) {

                Intent intent = new Intent(ImageGridActivity.this, ImageCropActivity.class);
                intent.putExtra("path",imageItem.path);
                logI(imageItem.path);
                startActivityForResult(intent, ImagePicker.REQUEST_CODE_CROP);  //单选需要裁剪，进入裁剪界面

    }



}