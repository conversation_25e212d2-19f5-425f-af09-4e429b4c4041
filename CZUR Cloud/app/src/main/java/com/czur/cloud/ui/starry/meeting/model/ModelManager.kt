package com.czur.cloud.ui.starry.meeting.model

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import java.util.concurrent.atomic.AtomicReference


object ModelManager : Model() {
    private var scope = CoroutineScope(Job())

    private var _chatModel = AtomicReference<ChatModel>()
    private var _membersModel = AtomicReference<MembersModel>()


    private inline fun <T> getModel(localProp: AtomicReference<T>, instanceBlock: () -> T): T {
        while (true) {
            val current = localProp.get()
            if (current != null) {
                return current
            }
            val newInstance = instanceBlock()
            if (localProp.compareAndSet(null, newInstance)) {
                return newInstance
            }
        }
    }

    val charModel: ChatModel
        get() = getModel(_chatModel) { ChatModel() }

    val membersModel: MembersModel
        get() = getModel(_membersModel) { MembersModel() }

    override fun doClear() {
        charModel.doClear()
        _chatModel.set(null)

        membersModel.doClear()
        _membersModel.set(null)

        scope.cancel()
        scope = CoroutineScope(Job())
    }


}