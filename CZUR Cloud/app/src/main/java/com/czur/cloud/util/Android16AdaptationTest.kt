package com.czur.cloud.util

import android.content.Context
import android.os.Build
import com.czur.czurutils.log.logI
import com.czur.czurutils.log.logE

/**
 * Android 16适配测试工具类
 * 用于验证Android 16适配功能是否正常工作
 */
object Android16AdaptationTest {
    private const val TAG = "Android16AdaptationTest"

    /**
     * 运行所有Android 16适配测试
     */
    fun runAllTests(context: Context): TestResult {
        logI("$TAG.runAllTests: Starting Android 16 adaptation tests")
        
        val results = mutableListOf<SingleTestResult>()
        
        // 1. 测试版本检查
        results.add(testVersionCheck())
        
        // 2. 测试API兼容性
        results.add(testApiCompatibility(context))
        
        // 3. 测试通知功能
        results.add(testNotificationFeatures(context))
        
        // 4. 测试服务功能
        results.add(testServiceFeatures(context))
        
        // 5. 测试行为变更适配
        results.add(testBehaviorAdaptations())
        
        val passedCount = results.count { it.passed }
        val totalCount = results.size
        
        val overallResult = TestResult(
            passed = passedCount == totalCount,
            passedCount = passedCount,
            totalCount = totalCount,
            results = results
        )
        
        logI("$TAG.runAllTests: Tests completed. Passed: $passedCount/$totalCount")
        return overallResult
    }

    /**
     * 测试版本检查功能
     */
    private fun testVersionCheck(): SingleTestResult {
        return try {
            val isAndroid16 = Android16Utils.isAndroid16OrHigher()
            val expectedResult = Build.VERSION.SDK_INT >= 36
            
            if (isAndroid16 == expectedResult) {
                SingleTestResult("Version Check", true, "Version check working correctly")
            } else {
                SingleTestResult("Version Check", false, "Version check mismatch: expected $expectedResult, got $isAndroid16")
            }
        } catch (e: Exception) {
            SingleTestResult("Version Check", false, "Exception: ${e.message}")
        }
    }

    /**
     * 测试API兼容性
     */
    private fun testApiCompatibility(context: Context): SingleTestResult {
        return try {
            // 测试各种API调用是否会崩溃
            val fullSdkVersion = if (Build.VERSION.SDK_INT >= 36) {
                Android16Utils.getFullSdkVersion()
            } else {
                Build.VERSION.SDK_INT
            }
            
            val minorSdkVersion = if (Build.VERSION.SDK_INT >= 36) {
                Android16Utils.getMinorSdkVersion()
            } else {
                0
            }
            
            val hasArrSupport = Android16Utils.hasAdaptiveRefreshRateSupport(context)
            val suggestedFrameRate = Android16Utils.getSuggestedFrameRate(context, 0)
            val isAdvancedProtectionEnabled = Android16Utils.isAdvancedProtectionEnabled(context)
            
            logI("$TAG.testApiCompatibility: fullSdkVersion=$fullSdkVersion, minorSdkVersion=$minorSdkVersion")
            logI("$TAG.testApiCompatibility: hasArrSupport=$hasArrSupport, suggestedFrameRate=$suggestedFrameRate")
            logI("$TAG.testApiCompatibility: isAdvancedProtectionEnabled=$isAdvancedProtectionEnabled")
            
            SingleTestResult("API Compatibility", true, "All API calls completed without crashes")
        } catch (e: Exception) {
            SingleTestResult("API Compatibility", false, "Exception: ${e.message}")
        }
    }

    /**
     * 测试通知功能
     */
    private fun testNotificationFeatures(context: Context): SingleTestResult {
        return try {
            // 初始化通知渠道
            Android16NotificationUtils.initNotificationChannels(context)
            
            // 测试创建进度通知
            val progressNotification = Android16NotificationUtils.createProgressNotification(
                context = context,
                title = "测试通知",
                content = "测试内容",
                progress = 50
            )
            
            // 测试创建分段进度通知
            val segmentedNotification = if (Build.VERSION.SDK_INT >= 36) {
                Android16NotificationUtils.createSegmentedProgressNotification(
                    context = context,
                    title = "分段测试",
                    progress = 500,
                    segments = listOf(Pair(300, android.graphics.Color.GREEN), Pair(200, android.graphics.Color.GRAY))
                )
            } else {
                null
            }
            
            val hasNotificationPermission = Android16NotificationUtils.checkNotificationPermission(context)
            
            logI("$TAG.testNotificationFeatures: progressNotification created: ${progressNotification != null}")
            logI("$TAG.testNotificationFeatures: segmentedNotification created: ${segmentedNotification != null}")
            logI("$TAG.testNotificationFeatures: hasNotificationPermission: $hasNotificationPermission")
            
            SingleTestResult("Notification Features", true, "Notification features working correctly")
        } catch (e: Exception) {
            SingleTestResult("Notification Features", false, "Exception: ${e.message}")
        }
    }

    /**
     * 测试服务功能
     */
    private fun testServiceFeatures(context: Context): SingleTestResult {
        return try {
            // 测试JobScheduler状态检查
            val jobStatus = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                Android16ServiceUtils.checkJobSchedulerStatus(context, 12345)
            } else {
                null
            }
            
            // 测试前台服务类型描述
            val serviceTypeDescription = Android16ServiceUtils.getForegroundServiceTypeDescription(
                android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_CONNECTED_DEVICE
            )
            
            logI("$TAG.testServiceFeatures: jobStatus: ${jobStatus?.description}")
            logI("$TAG.testServiceFeatures: serviceTypeDescription: $serviceTypeDescription")
            
            SingleTestResult("Service Features", true, "Service features working correctly")
        } catch (e: Exception) {
            SingleTestResult("Service Features", false, "Exception: ${e.message}")
        }
    }

    /**
     * 测试行为变更适配
     */
    private fun testBehaviorAdaptations(): SingleTestResult {
        return try {
            val shouldAdapt16KB = Android16Utils.shouldAdapt16KBPageSize()
            val shouldAdaptLayout = Android16Utils.shouldAdaptAdaptiveLayout()
            val shouldAdaptBack = Android16Utils.shouldAdaptPredictiveBack()
            
            logI("$TAG.testBehaviorAdaptations: shouldAdapt16KB=$shouldAdapt16KB")
            logI("$TAG.testBehaviorAdaptations: shouldAdaptLayout=$shouldAdaptLayout")
            logI("$TAG.testBehaviorAdaptations: shouldAdaptBack=$shouldAdaptBack")
            
            SingleTestResult("Behavior Adaptations", true, "Behavior adaptation checks working correctly")
        } catch (e: Exception) {
            SingleTestResult("Behavior Adaptations", false, "Exception: ${e.message}")
        }
    }

    /**
     * 测试结果数据类
     */
    data class TestResult(
        val passed: Boolean,
        val passedCount: Int,
        val totalCount: Int,
        val results: List<SingleTestResult>
    ) {
        fun getReport(): String {
            val builder = StringBuilder()
            builder.appendLine("Android 16 Adaptation Test Report")
            builder.appendLine("================================")
            builder.appendLine("Overall Result: ${if (passed) "PASSED" else "FAILED"}")
            builder.appendLine("Tests Passed: $passedCount/$totalCount")
            builder.appendLine()
            
            results.forEach { result ->
                builder.appendLine("${result.testName}: ${if (result.passed) "PASS" else "FAIL"}")
                builder.appendLine("  Details: ${result.details}")
                builder.appendLine()
            }
            
            return builder.toString()
        }
    }

    /**
     * 单个测试结果数据类
     */
    data class SingleTestResult(
        val testName: String,
        val passed: Boolean,
        val details: String
    )

    /**
     * 记录测试报告到日志
     */
    fun logTestReport(context: Context) {
        try {
            val testResult = runAllTests(context)
            val report = testResult.getReport()
            
            logI("$TAG.logTestReport:")
            report.lines().forEach { line ->
                logI("$TAG.logTestReport: $line")
            }
        } catch (e: Exception) {
            logE("$TAG.logTestReport error: ${e.message}")
        }
    }
}
