package com.czur.cloud.ui.starry.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.czur.cloud.preferences.StarryPreferences
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.model.RecentlyModel
import com.czur.cloud.ui.starry.model.StarryCallRecordDetailMemberModel
import com.czur.cloud.ui.starry.model.StarryCallRecordDetailModel
import com.czur.czurutils.log.logE
import kotlinx.coroutines.flow.MutableSharedFlow
import java.util.*

class MeetingDetailViewModel : ViewModel() {

    // 成员列表
    var memberList = MutableLiveData<List<StarryCallRecordDetailMemberModel>>()
    var detailModel = StarryCallRecordDetailModel(createTime = Date())

    // 删除是否成功
    var isDelete = MutableSharedFlow<Int>()

    // 查询最近通话记录详情
    fun getCallRecordDetail(meetingID: String, obj: (() -> Unit)? = null) {
        launch {
            detailModel = RecentlyModel.getCallRecordDetail(meetingID)
            // 在参会人中找出自己 StarryCallRecordDetailMemberModel
            memberList.postValue(findMyselfInMemberList(detailModel))
            obj?.invoke()
        }
    }

    fun findMyselfInMemberList(detailModel: StarryCallRecordDetailModel): List<StarryCallRecordDetailMemberModel> {
        var index = 0
        val detailList = detailModel.details
        for (detail in detailList) {
            val accountNo = StarryPreferences.getInstance().accountNo
            val m_no = detail.accountNo
            if (accountNo == m_no) {
                detailList[index].isMyself = true
                break
            }
            index++
        }
        return detailList
    }

    // 删除最近通话记录
    fun deleteCallRecords(meetingID: String) {
        launch {
            try {
                val ret = RecentlyModel.deleteCallRecords(meetingID)
                if (ret.toInt() > 0) {
                    isDelete.emit(1)
                } else {
                    isDelete.emit(-1)
                }
            } catch (e: Exception) {
                logE("deleteCallRecords:meetingID=${meetingID} error,e=${e.toString()}")
            }
        }
    }

}