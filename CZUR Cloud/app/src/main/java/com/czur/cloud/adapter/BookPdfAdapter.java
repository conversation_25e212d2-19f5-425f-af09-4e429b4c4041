package com.czur.cloud.adapter;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.czur.cloud.R;
import com.czur.cloud.entity.realm.BookPdfEntity;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * Created by Yz on 2018/3/16
 * Email：<EMAIL>
 */


public class BookPdfAdapter extends RecyclerView.Adapter<ViewHolder> {


    private static final int ITEM_TYPE_NORMA = 0;
    private static final int ITEM_TYPE_FOOTER = 1;
    private Activity mActivity;
    //当前需要显示的所有的图片数据
    private List<BookPdfEntity> datas;
    //是否进入选择
    private boolean isSelectItem;

    private LinkedHashMap<String, Boolean> isCheckedMap = new LinkedHashMap<>();

    /**
     * 构造方法
     */
    public BookPdfAdapter(Activity activity, List<BookPdfEntity> datas, boolean isSelectItem) {
        this.mActivity = activity;
        this.isSelectItem = isSelectItem;
        this.datas = datas;
    }

    public void refreshData(List<BookPdfEntity> books, boolean isSelectItem, LinkedHashMap<String, Boolean> isCheckedMap) {
        this.isSelectItem = isSelectItem;
        this.datas = books;
        this.isCheckedMap = isCheckedMap;
        notifyDataSetChanged();

    }

    public void refreshData(boolean isSelectItem) {
        this.isSelectItem = isSelectItem;
        notifyDataSetChanged();
    }


    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        if (viewType == ITEM_TYPE_NORMA) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_my_pdf, parent, false);
            return new MyPdfHolder(view);
        } else if (viewType == ITEM_TYPE_FOOTER) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_footer_book_page, parent, false);
            return new FooterHolder(view);
        } else {
            return null;
        }
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {

        if (holder instanceof MyPdfHolder) {
            final MyPdfHolder mHolder = (MyPdfHolder) holder;
            mHolder.mItem = datas.get(position);
            mHolder.myPdfNameTv.setText(mHolder.mItem.getPdfName() + "");
            if (isSelectItem) {
                mHolder.myPdfLeftArrow.setVisibility(View.GONE);
                mHolder.checkBox.setVisibility(View.VISIBLE);
                mHolder.checkBox.setTag(mHolder.mItem.getPdfId());
            } else {
                mHolder.myPdfLeftArrow.setVisibility(View.VISIBLE);
                mHolder.checkBox.setVisibility(View.GONE);
            }
            mHolder.checkBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    if (isChecked) {
                        if (!isCheckedMap.containsKey(mHolder.checkBox.getTag())) {
                            //选中时添加
                            isCheckedMap.put(mHolder.mItem.getPdfId(), true);
                        }
                    } else {
                        if (isCheckedMap.containsKey(mHolder.checkBox.getTag())) {
                            //没选中时移除
                            isCheckedMap.remove(mHolder.mItem.getPdfId());
                        }
                    }
                    if (onItemCheckListener != null) {
                        onItemCheckListener.onItemCheck(position, mHolder.mItem, isCheckedMap, datas.size());
                    }
                }
            });

            if (isCheckedMap != null) {
                mHolder.checkBox.setChecked(isCheckedMap.containsKey(mHolder.mItem.getPdfId()) ? true : false);
            } else {
                mHolder.checkBox.setChecked(false);
            }

            mHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onItemClickListener != null) {
                        onItemClickListener.onBookPdfEntityClick(mHolder.mItem, position,mHolder.checkBox);
                    }

                }
            });

            mHolder.itemView.setOnLongClickListener(new View.OnLongClickListener() {
                @Override
                public boolean onLongClick(View v) {
                    if (!isSelectItem) {
                        mHolder.checkBox.setChecked(true);
                        isCheckedMap.put(mHolder.mItem.getPdfId(),true);
                        if (onItemLongClickListener != null) {
                            onItemLongClickListener.onBookPdfEntityLongClick(position, mHolder.mItem, isCheckedMap,datas.size());
                        }
                        return false;
                    }
                    return true;

                }
            });


        }  else if (holder instanceof FooterHolder) {
            final FooterHolder mHolder = (FooterHolder) holder;

        }

    }




    @Override
    public int getItemViewType(int position) {

        if (position >= 0 && position < datas.size()) {
            return ITEM_TYPE_NORMA;
        } else {
            return ITEM_TYPE_FOOTER;
        }

    }



    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return datas.size() + 1;
    }


    private class MyPdfHolder extends ViewHolder {
        public final View mView;
        BookPdfEntity mItem;
        LinearLayout myPdfItem;
        TextView myPdfNameTv;
        CheckBox checkBox;
         ImageView myPdfLeftArrow;

        MyPdfHolder(View itemView) {
            super(itemView);
            mView = itemView;

            myPdfItem = (LinearLayout) itemView.findViewById(R.id.item_my_pdf_rl);
            myPdfNameTv = (TextView) itemView.findViewById(R.id.my_pdf_name_tv);


            checkBox = (CheckBox) itemView.findViewById(R.id.check);
            myPdfLeftArrow = (ImageView) itemView.findViewById(R.id.my_pdf_left_arrow);


        }


    }




    private class FooterHolder extends ViewHolder {

        public final View mView;
        public FooterHolder(View view) {
            super(view);
            mView = view;

        }
    }




    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void onBookPdfEntityClick(BookPdfEntity BookPdfEntity, int position,CheckBox checkBox);
    }

    private OnItemLongClickListener onItemLongClickListener;

    public void setOnItemLongClickListener(OnItemLongClickListener onItemLongClickListener) {
        this.onItemLongClickListener = onItemLongClickListener;
    }

    public interface OnItemLongClickListener {
        void onBookPdfEntityLongClick(int position, BookPdfEntity BookPdfEntity, LinkedHashMap<String, Boolean> isCheckedMap, int totalSize);
    }


    private OnItemCheckListener onItemCheckListener;

    public void setOnItemCheckListener(OnItemCheckListener onItemCheckListener) {
        this.onItemCheckListener = onItemCheckListener;
    }

    public interface OnItemCheckListener {
        void onItemCheck(int position, BookPdfEntity BookPdfEntity, LinkedHashMap<String, Boolean> isCheckedMap, int totalSize);

    }

}
