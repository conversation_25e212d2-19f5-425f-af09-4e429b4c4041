package com.czur.cloud.ui.eshare.transmitfile

import android.content.Intent
import android.os.Bundle
import android.os.Parcelable
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.content.res.AppCompatResources
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.NetworkUtils
import com.czur.cloud.BuildConfig
import com.czur.cloud.R
import com.czur.cloud.ui.base.CzurCloudApplication
import com.czur.cloud.ui.eshare.adapter.FileBrowserAdapter
import com.czur.cloud.ui.eshare.adapter.FileBrowserTabAdapter
import com.czur.cloud.ui.eshare.myentity.BrowserFileType
import com.czur.cloud.ui.eshare.myentity.FileBrowserEntity
import com.czur.cloud.ui.eshare.myenum.TransmitFileResultEnum
import com.czur.cloud.ui.eshare.receiver.okhttp3.TransmitFileHttpClient
import com.czur.cloud.ui.eshare.service.EShareHeartBeatService
import com.czur.cloud.ui.eshare.utils.ListObserver
import com.czur.cloud.ui.starry.base.StarryBaseActivity
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import kotlin.coroutines.suspendCoroutine


/**
 * 文件浏览器
 */
class TransmitChildFileBrowserActivity : StarryBaseActivity() {

    var parentFilePath = ""
    var parentFileName = ""
    var fileTabHistoryList = mutableListOf<FileBrowserEntity.FileEntity>()

    private val user_back_btn by lazy {
        findViewById<ImageView>(R.id.user_back_btn)
    }


    private val user_title by lazy {
        findViewById<TextView>(R.id.user_title)
    }

    private val file_browser_tab_rv by lazy {
        findViewById<RecyclerView>(R.id.file_browser_tab_rv)
    }

    private val file_browser_rv by lazy {
        findViewById<RecyclerView>(R.id.file_browser_rv)
    }

    private val applicationViewModel by lazy {
        (applicationContext as CzurCloudApplication).getEshareViewModel1()
    }

    private val cancel_tv by lazy {
        findViewById<TextView>(R.id.cancel_tv)
    }

    private val start_tv by lazy {
        findViewById<TextView>(R.id.start_tv)
    }
    private val file_browser_folder_status_tv by lazy {
        findViewById<TextView>(R.id.file_browser_folder_status_tv)
    }
    private val file_browser_folder_status_iv by lazy {
        findViewById<ImageView>(R.id.file_browser_folder_status_iv)
    }
    private lateinit var transferFileBrowserAdapter: FileBrowserAdapter
    private lateinit var transferFileBrowserTabAdapter: FileBrowserTabAdapter
    private var castStatus = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.eshare_bg_gray_f5)
//        BarUtils.setStatusBarLightMode(this, false)
        setContentView(R.layout.eshare_activity_child_file_browser)
        initRegister()
        initIntent()
        initView()
        initAdapter()
        initData()
    }

    private fun initRegister() {
        launch {
            EShareHeartBeatService.castStatusChanged.collect {
                castStatus = it
            }
        }

        val callback = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                // 如果你希望执行默认的后退操作，可以调用 isEnabled = false
                pressBackBtn()
                isEnabled = true
            }
        }
        onBackPressedDispatcher.addCallback(this, callback)
    }

    private fun initIntent() {
        parentFilePath = intent.getStringExtra("rootType").toString()
        parentFileName = intent.getStringExtra("fileName").toString()

    }

    private fun initView() {
        user_title.text = parentFileName
        user_back_btn.setOnClickListener {
            onBackPressed()
        }

        if (BuildConfig.IS_OVERSEAS){
            // 海外需要字体字号减小
            start_tv.textSize = 13f
            cancel_tv.textSize = 13f
        }

        start_tv.setOnClickListener {
            if (transferFileBrowserAdapter.getSelectList().isEmpty()) {
                return@setOnClickListener
            }

            val intent = Intent(this, TransmitFileDownloadActivity::class.java)
            intent.putParcelableArrayListExtra(
                "downloadList",
                ArrayList<Parcelable>(transferFileBrowserAdapter.getSelectList())
            )
            startActivity(intent)
            finish()
        }

        cancel_tv.setOnClickListener {
            finish()
        }
    }

    private fun initData() {
        launch {

            transferFileBrowserAdapter.selectedItems.setListener(object :
                ListObserver<FileBrowserEntity.FileEntity> {
                override fun onAdd(element: FileBrowserEntity.FileEntity) {
                }

                override fun onRemove(element: FileBrowserEntity.FileEntity) {
                }

                override fun onChange(element: FileBrowserEntity.FileEntity?) {
                    if (transferFileBrowserAdapter.getSelectList().isEmpty()) {
                        start_tv.isEnabled = false
                        start_tv.background = AppCompatResources.getDrawable(
                            this@TransmitChildFileBrowserActivity,
                            R.drawable.eshare_circle_dark_blue_6dp
                        )
                    } else {
                        start_tv.isEnabled = true
                        start_tv.background = AppCompatResources.getDrawable(
                            this@TransmitChildFileBrowserActivity,
                            R.drawable.eshare_circle_color_blue_6dp
                        )
                    }
                }
            })
        }
        showProgressDialog()
        launch(Dispatchers.IO) {
            val fileList =
                TransmitFileHttpClient.getFileList(parentFilePath, applicationViewModel.currentIP)

            handleFileList(fileList)
        }
    }


    private fun initAdapter() {
        file_browser_rv.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(this)
        transferFileBrowserAdapter = FileBrowserAdapter()
        file_browser_rv.adapter = transferFileBrowserAdapter
        transferFileBrowserAdapter.setItemClickListener(object :
            FileBrowserAdapter.OnItemClickListener {
            override fun onItemClick(position: Int, fileEntity: FileBrowserEntity.FileEntity) {
                if (fileEntity.fileType == BrowserFileType.FOLDER.typeName) {
                    loadFolderList(fileEntity) {
                        loadFolderTabList(fileEntity)
                    }
                }
            }

        })

        fileTabHistoryList.clear()
        fileTabHistoryList.add(
            FileBrowserEntity.FileEntity(
                absPath = parentFilePath,
                name = parentFileName
            )
        )
        file_browser_tab_rv.layoutManager =
            androidx.recyclerview.widget.LinearLayoutManager(this, RecyclerView.HORIZONTAL, false)
        transferFileBrowserTabAdapter = FileBrowserTabAdapter()
        file_browser_tab_rv.adapter = transferFileBrowserTabAdapter
        transferFileBrowserTabAdapter.setData(fileTabHistoryList)
        transferFileBrowserTabAdapter.setItemClickListener(object :
            FileBrowserTabAdapter.OnItemClickListener {
            override fun onItemClick(entity: FileBrowserEntity.FileEntity) {
                loadFolderList(entity) {
                    loadFolderTabList(entity)
                }
            }
        })

    }


    private fun loadFolderTabList(fileEntity: FileBrowserEntity.FileEntity) {
        launch(Dispatchers.IO) {
            fileTabHistoryList.find { it.absPath == fileEntity.absPath }?.let {
                fileTabHistoryList =
                    fileTabHistoryList.subList(0, fileTabHistoryList.indexOf(it) + 1)
            } ?: run {
                fileTabHistoryList.add(fileEntity)
            }

            withContext(Dispatchers.Main) {
                transferFileBrowserTabAdapter.setData(fileTabHistoryList)
                // 添加数据时有动画效果的滚动到最后一个
                file_browser_tab_rv.smoothScrollToPosition(fileTabHistoryList.size - 1)
            }
        }
    }

    private fun loadFolderList(
        fileEntity: FileBrowserEntity.FileEntity,
        handleFinish: (Boolean) -> Unit = {}
    ) {
        showProgressDialog()
        launch(Dispatchers.IO) {
            val fileList =
                TransmitFileHttpClient.getFileList(
                    fileEntity.absPath!!,
                    applicationViewModel.currentIP
                )
            val handleFileList = handleFileList(fileList)
            handleFinish.invoke(handleFileList)
        }
    }

    private suspend fun handleFileList(fileList: List<FileBrowserEntity.FileEntity>): Boolean {
        return suspendCoroutine { continuation ->
            launch(Dispatchers.Main) {
                hideProgressDialog(true)
                file_browser_tab_rv.visibility = View.VISIBLE
                cancel_tv.visibility = View.VISIBLE
                start_tv.visibility = View.VISIBLE
            }
            if (fileList.isNotEmpty()) {
                when (fileList[0].resultEnumCode) {
                    TransmitFileResultEnum.CODE_200.code -> {
                        // 正常
                        launch(Dispatchers.Main) {
                            transferFileBrowserAdapter.setData(fileList)
                            file_browser_folder_status_tv.visibility = View.GONE
                            file_browser_folder_status_iv.visibility = View.GONE
                        }
                        continuation.resumeWith(Result.success(true))
                        return@suspendCoroutine
                    }
                    TransmitFileResultEnum.CODE_220.code -> {//关闭了文件传输开关
                        launch (Dispatchers.Main){
                            transferFileBrowserAdapter.setData(arrayListOf())
                            file_browser_folder_status_tv.visibility = View.VISIBLE
                            file_browser_folder_status_iv.visibility = View.VISIBLE
                            cancel_tv.visibility = View.GONE
                            start_tv.visibility = View.GONE

                            file_browser_folder_status_iv.setImageResource(R.mipmap.ic_transmit_file_switch)
                            file_browser_folder_status_tv.setText(R.string.eshare_transmit_file_dialog_retry_title)

                        }
                        // 文件传输开关关闭
                        continuation.resumeWith(Result.success(false))
                        return@suspendCoroutine
                    }
                    TransmitFileResultEnum.CODE_405.code -> {
                        // 文件上锁
                        launch(Dispatchers.Main) {
                            transferFileBrowserAdapter.setData(arrayListOf())
                            file_browser_folder_status_tv.visibility = View.VISIBLE
                            file_browser_folder_status_iv.visibility = View.VISIBLE
                            cancel_tv.visibility = View.GONE
                            start_tv.visibility = View.GONE

                            file_browser_folder_status_iv.setImageResource(R.mipmap.ic_folder_locked)
                            file_browser_folder_status_tv.setText(R.string.browser_folder_locked_tips)
                        }

                        continuation.resumeWith(Result.success(false))
                        return@suspendCoroutine
                    }

                    TransmitFileResultEnum.CODE_404.code -> {
                        // 文件丢失的情况, 直接刷新到最初始的页面
                        launch(Dispatchers.Main) {
                            initAdapter()
                            initData()
                        }

                        continuation.resumeWith(Result.success(false))
                        return@suspendCoroutine
                    }

                    else -> {//报错
                        launch(Dispatchers.Main) {
                            transferFileBrowserAdapter.setData(arrayListOf())

                            file_browser_folder_status_tv.visibility = View.VISIBLE
                            file_browser_folder_status_iv.visibility = View.VISIBLE
                            cancel_tv.visibility = View.GONE
                            start_tv.visibility = View.GONE

                            if (!NetworkUtils.isConnected()
                                || fileList[0].resultEnumCode == TransmitFileResultEnum.CODE_9527.code) {
                                file_browser_folder_status_iv.setImageResource(R.mipmap.ic_net_error)
                                file_browser_folder_status_tv.setText(R.string.eshare_error_net_str)
                            } else {// 无文件
                                file_browser_tab_rv.visibility = View.INVISIBLE
                                file_browser_folder_status_iv.setImageResource(R.mipmap.ic_empty_folder)
                                file_browser_folder_status_tv.setText(R.string.browser_no_file_tips)

                            }

                        }
                        continuation.resumeWith(Result.success(false))
                        return@suspendCoroutine
                    }
                }

            }

            continuation.resumeWith(Result.success(false))
        }
    }

    private fun pressBackBtn(){
    // 看看是不是最后一层,如果是,结束页面,不是的话查找上一层的tab
        if (fileTabHistoryList.size == 1){
            finish()
        }else{
            selectLastTab()
        }
    }

    // 返回到上一层tab
    private fun selectLastTab(){
        val lastTab = fileTabHistoryList[fileTabHistoryList.size - 2]
        loadFolderList(lastTab){
            loadFolderTabList(lastTab)
        }
    }
}