package com.czur.cloud.ui.starry.viewmodel

import android.content.Context
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.blankj.utilcode.util.ToastUtils
import com.czur.cloud.R
import com.czur.cloud.preferences.StarryPreferences
import com.czur.cloud.ui.starry.activity.StarryContactDetailActivity
import com.czur.cloud.ui.starry.api.StarryRepository
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.model.CommonEnterprise
import com.czur.cloud.ui.starry.model.ContactDetail
import com.czur.cloud.ui.starry.model.MeetingMember
import com.czur.cloud.ui.starry.model.StarryEnterpriseModel
import com.czur.czurutils.log.logD
import com.czur.czurutils.log.logI
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext

class StarryContactViewModel : ViewModel() {
    private val starryRepository by lazy {
        StarryRepository()
    }

    // del
    var isDeleteAddressFlag = MutableLiveData<Boolean>(false)

    // add
    var isAddAddressFlag = MutableLiveData<Boolean>(false)

    // update
    var isUpdateAddressFlag = MutableLiveData<Boolean>(false)

    // detail
    var contactDetail = MutableLiveData<ContactDetail>()

    // 共同企业列表
    var commonEnterprise = MutableLiveData<List<CommonEnterprise>>()
    var currentSelectCompanyIndex = 0
    var currentSelectCompany = MutableLiveData<CommonEnterprise>()

    var contactDetailInMeeting = MutableLiveData<ContactDetail>()
    var commonEnterpriseInMeeting = MutableLiveData<List<CommonEnterprise>>()
    var currentSelectCompanyIndexInMeeting = 0
    var currentSelectCompanyInMeeting = MutableLiveData<CommonEnterprise>()

    // 当前选中的contact
    var isCheckedMap = MutableLiveData<LinkedHashMap<String, String>>()

    // 添加时，原先的参会人员，不可取消的
    var isDisableCheckedMap = MutableLiveData<LinkedHashMap<String, String>>()

    // 记录选中的用户的信息
    val checkedMeetingMemberList = ArrayList<MeetingMember>()

    // 当前临时选中的contact
    var tempCheckedMap = MutableLiveData<LinkedHashMap<String, String>>()

    var currentFromType =
        StarryConstants.INTENT_FROM_TYPE_ADDRESS //针对获取联系人详情,定义从哪里跳转 1-通讯录，2-企业成员，3-会议详情

    // 删除联系人
    suspend fun deleteAddressBook(id: String): Boolean {
        val flag = starryRepository.deleteAddressBook(id)
        isDeleteAddressFlag.postValue(flag == "1")

        return (flag == "1")
    }

    // 添加联系人
    suspend fun addAddressBook(accountId: String, meetingNo: String, name: String): Boolean {
        val flag = starryRepository.addAddressBook(accountId, meetingNo, name)
        // 1000:成功；2039：联系人已经存在
        val ret = (flag == 1000) or (flag == 2039)
        isAddAddressFlag.postValue(ret)

        return ret
    }

    // 添加联系人
    fun getContactDetailV2(id: String, intentType: Int = -1, loaded: (() -> Unit)? = null) {
        launch {
            val detail = starryRepository.getContactDetailV2(
                id,
                if (intentType == -1) currentFromType else intentType
            )
            contactDetail.value = detail ?: ContactDetail()
            commonEnterprise.value = detail.commonEnterprise ?: listOf()
            loaded?.invoke()
        }
    }

    // Ta是否在企业中
    suspend fun isTaInCompany(id: String, intentType: Int = -1): Boolean {
        val detail = withContext(Dispatchers.IO) {
            starryRepository.getContactDetailV2(
                id,
                if (intentType == -1) currentFromType else intentType
            )
        }
        contactDetail.value = detail ?: ContactDetail()
        commonEnterprise.value = detail.commonEnterprise ?: listOf()

        return detail.commonEnterprise.isNotEmpty()
    }

    fun isTaInCompanyNew(id: String, intentType: Int = -1): Boolean = runBlocking {
        val detail = starryRepository.getContactDetailV2(
            id,
            if (intentType == -1) currentFromType else intentType
        )
        contactDetail.value = detail
        commonEnterprise.value = detail.commonEnterprise
        detail.commonEnterprise.isNotEmpty()
    }

    //获取联系人详情
    fun getContactDetailByCzurId(czurId: String, detailLoaded: ((ContactDetail) -> Unit)? = null) {
        launch {
            val detail = starryRepository.getContactDetailByCzurId(czurId)
            contactDetail.value = detail ?: ContactDetail()
            commonEnterprise.value = detail.commonEnterprise ?: listOf()
            detailLoaded?.invoke(detail)
        }
    }

    fun getContactDetailByCzurIdInMeeting(
        czurId: String,
        detailLoaded: ((ContactDetail) -> Unit)? = null
    ) {
        launch {
            val detail = starryRepository.getContactDetailByCzurId(czurId)
            contactDetailInMeeting.value = detail ?: ContactDetail()
            commonEnterpriseInMeeting.value = detail.commonEnterprise ?: listOf()
            detailLoaded?.invoke(detail)
        }
    }

    // 修改备注名称
    suspend fun updateAddressBook(id: String, remark: String): Boolean {
        val flag = starryRepository.updateAddressBook(id, remark)
        isUpdateAddressFlag.postValue(flag == "1")

        return (flag == "1")
    }


    // 找到所属的企业
    fun getCurrentCompany(model: StarryEnterpriseModel?) {
        if (model != null) {
            var index = 0
            currentSelectCompanyIndex = 0
            commonEnterprise.value?.forEach {
                if (it.enterpriseName == model.enterpriseName) {
                    currentSelectCompanyIndex = index
                    return@forEach
                } else {
                    index++
                }
            }
        }
        currentSelectCompany.value = commonEnterprise.value?.get(currentSelectCompanyIndex)
    }

    fun getCurrentCompanyInfo(model: StarryEnterpriseModel?): CommonEnterprise? {
        if (commonEnterprise.value?.size == 1) {
            return commonEnterprise.value?.get(0)
        }
        for (enterprise in commonEnterprise.value!!) {
            if (enterprise.enterpriseName == model?.enterpriseName) {
                return enterprise
            }
        }
        if ((commonEnterprise.value?.size ?: 0) > 1) {
            return commonEnterprise.value?.get(0)
        }
        return null
    }

    fun changeCurrentCompany(pos: Int) {
        currentSelectCompanyIndex = pos
        currentSelectCompany.value = commonEnterprise.value?.get(pos)
    }

    // 找到所属的企业
    fun getCurrentCompanyInMeeting(model: StarryEnterpriseModel?) {
        if (model != null) {
            var index = 0
            currentSelectCompanyIndexInMeeting = 0
            commonEnterpriseInMeeting.value?.forEach {
                if (it.enterpriseName == model.enterpriseName) {
                    currentSelectCompanyIndexInMeeting = index
                    return@forEach
                } else {
                    index++
                }
            }
        }
        currentSelectCompanyInMeeting.value =
            commonEnterpriseInMeeting.value?.get(currentSelectCompanyIndexInMeeting)
    }

    fun changeCurrentCompanyInMeeting(pos: Int) {
        currentSelectCompanyIndexInMeeting = pos
        currentSelectCompanyInMeeting.value = commonEnterpriseInMeeting.value?.get(pos)
    }

    // 获取选中的用户的信息,存到checkedMeetingMemberList中备用
    fun getCheckedMemberInfo(enterpriseList: List<StarryEnterpriseModel>) {
        val accountNo = StarryPreferences.getInstance().accountNo
        isCheckedMap.value = tempCheckedMap.value
        checkedMeetingMemberList.clear()
        val meetingAccountsList = ArrayList<String>()

        isCheckedMap.value?.forEach { (t, u) ->
            if (t != accountNo) {
                meetingAccountsList.add(t)
                checkedMeetingMemberList.add(MeetingMember(t, u))
            }
        }

        // 剔除之前已选中的,即在会议中的成员
        isDisableCheckedMap.value?.forEach { (dis_k, dis_v) ->
            if (meetingAccountsList.contains(dis_k)) {
                meetingAccountsList.remove(dis_k)

                val member = MeetingMember(dis_k, dis_v)
                checkedMeetingMemberList.first {
                    it.accountNo == dis_k
                }.let {
                    checkedMeetingMemberList.remove(it)
                }
            }
        }

        checkedMeetingMemberList.forEach s1@{ it1 ->
            val concat_id = it1.contactID
            enterpriseList.forEach s2@{ it2 ->
                runCatching {
                    val memberList = it2.membersList ?: listOf()
                    memberList.first {
                        it.id == concat_id
                    }.let {
                        it1.memberName = it.name
                        return@s2
                    }
                }
            }
        }

        logI("getCheckedMemberInfo.checkedMeetingMemberList=${checkedMeetingMemberList}")
    }

    // 发起会议/添加成员 之前,成员有效性检查
    // 根据id查
    // 1. 查退出\被移除的账号 -- 企业信息有误
    // 2. 查注销了的账号  meetingAccout有000的 ---账号信息有误
    // 3. 查修改手机号的账号 根据id查
    // 4. 查找是否有账号信息,有正常呼叫;
    fun checkValidMembers(context: Context, enterpriseList: List<StarryEnterpriseModel>): Boolean {
        // 用户退出了企业/被移除企业,是企业信息有误
        //邀请/发起会议等选择企业成员页面，如果企业成员已退出toast提示：
        //一个人：xx 企业信息有误
        //2人及以上：xx等x人企业信息有误
        var count = 0   // 不在企业中的数量
        var countClosed = 0     // 注销了的数量
        var msgName = ""
        checkedMeetingMemberList.forEach s1@{ it1 ->
            var isInEnterprise = false
            val meetingNo = it1.accountNo.toString()
            val concat_id = it1.contactID
            enterpriseList.forEach s2@{ it2 ->
                runCatching {
                    val memberList = it2.membersList ?: listOf()
//                    logI("checkValidMembers.memberList=${memberList}")
                    memberList.first {
                        if (concat_id?.isEmpty() == true) {
                            it.meetingAccout == meetingNo
                        } else {
                            it.id == concat_id
                        }
                    }.let {
                        // id在企业中
                        isInEnterprise = true

                        // 注销的账号,或者被邀请的账号所在企业过期了
                        if (it.meetingAccout.startsWith(StarryContactDetailActivity.DEL_ACCOUNT_PRE)
                            || it.meetingAccout.startsWith(StarryContactDetailActivity.DEL_ACCOUNT_000)
                            || it2.expired
                        ) {
                            countClosed++
                            msgName = it1.memberName ?: meetingNo
                            logI("checkValidMembers.注销的账号=${it1}")
                            return@s2
                        }

                        // 修改手机号了
                        if (it.meetingAccout != meetingNo) {
                            it1.accountNo = it.meetingAccout
                            logI("checkValidMembers.修改手机号了=${it1}")
                        }
                        return@s2
                    }
                }.onFailure {
                    logD("checkValidMembers-onFailure meetingNo $meetingNo")
//                    isInEnterprise = true
                    return@s2
                }
            }

            // id不在企业中
            if (!isInEnterprise) {
                count++
                if (count == 1) {
                    msgName = meetingNo
                }
                logI("checkValidMembers.id不在企业中=${it1}")
            }
            logI("checkValidMembers.isInEnterprise=${isInEnterprise};count=${count};countClosed=${countClosed}")
        }

        // 删除/退出企业的数量
        if (count == 1) {
            ToastUtils.showLong(
                String.format(
                    context.getString(R.string.starry_contact_startmeeting_check_msg),
                    msgName
                )
            )
            return false
        }
        if (count > 1) {
            ToastUtils.showLong(
                String.format(
                    context.getString(R.string.starry_contact_startmeeting_check2_msg),
                    msgName, count
                )
            )
            return false
        }

        // 注销的数量
        if (countClosed == 1) {
            ToastUtils.showLong(
                String.format(
                    context.getString(R.string.starry_contact_account_error_msg),
                    msgName
                )
            )
            return false
        }
        if (countClosed > 1) {
            ToastUtils.showLong(
                String.format(
                    context.getString(R.string.starry_contact_account_error2_msg),
                    msgName, countClosed
                )
            )
            return false
        }

        logI("checkValidMembers.checkedMeetingMemberList=${checkedMeetingMemberList}")
        return true
    }

    override fun onCleared() {
        isDisableCheckedMap.value?.clear()
        isCheckedMap.value?.clear()
        super.onCleared()
    }

}