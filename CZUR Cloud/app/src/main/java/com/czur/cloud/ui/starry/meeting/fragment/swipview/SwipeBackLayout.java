package com.czur.cloud.ui.starry.meeting.fragment.swipview;


import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.os.Handler;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.ScrollView;
import android.widget.Scroller;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

public class SwipeBackLayout extends FrameLayout {

    private static final String TAG = "SwipeBackLayout";

    private Context mContext;
    private Scroller mScroller;
    private int mTouchSlop;

    public static final int SWIPE_LEFT = 1;
    public static final int SWIPE_RIGHT = 2;
    public static final int SWIPE_TOP = 3;
    public static final int SWIPE_BOTTOM = 4;

//    protected int model = SWIPE_RIGHT;
    protected int model = SWIPE_BOTTOM;

    private int mScreenWidth = 0;
    private int mScreenHeight = 0;

    private int mLastDownX = 0;
    private int mLastDownY = 0;

    private int mCurryX;
    private int mCurryY;

    private int mDelX;
    private int mDelY;

    private boolean isClose = false;

    private Paint mPaint;

    //判断是否需要在分发原MotionEvent事件之前，分发一个滑动距离为滑动阈值的事件
    private Boolean splitFlag = false;
    // View中的RecyleView是否在顶部(在顶部，可以下滑该页面close)
    private Boolean isRvTop = true;    // 初始时候，为false，可以滑动recycleview
    private Boolean isSvTop = false;    // 初始时候，为false，可以滑动ScrollView


    public SwipeBackLayout(Context context) {
        super(context);
        mContext = context;
        setupView();
    }

    public SwipeBackLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        setupView();
    }

    @SuppressLint("NewApi")
    private void setupView() {

        // 滑动阈值+1，以区别内部的recycleview的滑动阈值
        mTouchSlop = ViewConfiguration.get(mContext).getScaledTouchSlop()+1;

        mScroller = new Scroller(mContext);

        WindowManager wm = (WindowManager) (mContext.getSystemService(Context.WINDOW_SERVICE));
        DisplayMetrics dm = new DisplayMetrics();
        wm.getDefaultDisplay().getMetrics(dm);
        mScreenHeight = dm.heightPixels;
        mScreenWidth = dm.widthPixels;


        ImageView mImgView = new ImageView(mContext);
        mImgView.setLayoutParams(new LayoutParams(LayoutParams.MATCH_PARENT,
                LayoutParams.MATCH_PARENT));
        mImgView.setScaleType(ImageView.ScaleType.FIT_XY);
        mImgView.setBackgroundColor(getResources().getColor(android.R.color.transparent));
        addView(mImgView);//添加一层白色的imageview作为背景
        setClickable(true);
//        setBackgroundColor(Color.argb(0, 0, 0, 0));//背景设为透明
        setBackgroundColor(getResources().getColor(android.R.color.transparent));

        //初始化画笔 用来画阴影
        mPaint = new Paint();
        mPaint.setStrokeWidth(2);
        mPaint.setAntiAlias(true);
        mPaint.setColor(Color.DKGRAY);

    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        viewPagers.clear();
        findViewPager(SwipeBackLayout.this);

        recycleViews.clear();
        findRecycleView(SwipeBackLayout.this);
        Log.i("Jason", "recycleViews.size="+recycleViews.size());
        if (recycleViews.size() < 1){
            // 重新搜索一遍
            new Handler().postDelayed(
                    () -> {
                        findRecycleView(SwipeBackLayout.this);
                        Log.i("Jason", "recycleViews.size222="+recycleViews.size());
                    }
                    , 300);
//            try {
//                Thread.sleep(300);
//            } catch (InterruptedException e) {
//                throw new RuntimeException(e);
//            }
        }

        scrollViews.clear();
        findScrollView(SwipeBackLayout.this);

    }
    List<ViewPager> viewPagers = new ArrayList<>();
    List<RecyclerView> recycleViews = new ArrayList<>();
    List<ScrollView> scrollViews = new ArrayList<>();

    private void findScrollView(ViewGroup viewGroup){
        if (viewGroup == null || viewGroup.getVisibility() != View.VISIBLE) {
            return ;
        }
        for (int i = 0; i < viewGroup.getChildCount(); i++) {
            View view = viewGroup.getChildAt(i);
            if (view instanceof ScrollView) {
                scrollViews.add((ScrollView) view);
            }else if(view instanceof ViewGroup){
                findScrollView((ViewGroup)view);
            }
        }
    }

    private void findViewPager(ViewGroup viewGroup){
        if (viewGroup == null || viewGroup.getVisibility() != View.VISIBLE) {
            return ;
        }
        for (int i = 0; i < viewGroup.getChildCount(); i++) {
            View view = viewGroup.getChildAt(i);
            if (view instanceof ViewPager) {
                viewPagers.add((ViewPager)view);
            }else if(view instanceof ViewGroup){
                findViewPager((ViewGroup)view);
            }
        }
    }

    private void findRecycleView(ViewGroup viewGroup){
        if (viewGroup == null || viewGroup.getVisibility() != View.VISIBLE) {
            return ;
        }
        for (int i = 0; i < viewGroup.getChildCount(); i++) {
            View view = viewGroup.getChildAt(i);
//            Log.i(TAG, "findRecycleView.view: " +  view.getClass().getSimpleName() + " , " + view);
            if (view instanceof RecyclerView) {
                recycleViews.add((RecyclerView) view);
            }else if(view instanceof ViewGroup){
                findRecycleView((ViewGroup)view);
            }
        }
//        Log.i(TAG, "findRecycleView.recycleViews="+recycleViews.size()+","+ recycleViews);
    }

    private void startScroll(int startX, int startY, int dx, int dy, int duration) {
        mScroller.startScroll(startX, startY, dx, dy, duration);//传入参数
        invalidate();//重绘调用draw里面computeScroll方法
    }

    private void startScrollToFinish(int startX, int startY, int dx, int dy, int duration) {
        if (ComputeScrollFinish)
            startScroll(startX, startY, dx, dy, duration);
        isClose = true;
        hideShadow = true;
        invalidate();
        if (onSwipeFinishListener != null)
            onSwipeFinishListener.swipeStart();
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_MOVE:
                mCurryX = (int) event.getX();
                mCurryY = (int) event.getY();
                mDelX = mCurryX - mLastDownX;
                mDelY = mCurryY - mLastDownY;
                invalidate();
                if (model == SWIPE_RIGHT && mDelX > 0) {// 右滑
                    scrollTo(-mDelX, 0);
                } else if (model == SWIPE_BOTTOM && mDelY > 0) {// 下滑
                    scrollTo(0, -mDelY);
                } else if (model == SWIPE_LEFT && mDelX < 0) {// 左滑
                    scrollTo(-mDelX, 0);
                } else if (model == SWIPE_TOP && mDelY < 0) {// 上滑
                    scrollTo(0, -mDelY);
                }
                break;
            case MotionEvent.ACTION_UP:
                mCurryX = (int) event.getX();
                mCurryY = (int) event.getY();
                mDelX = mCurryX - mLastDownX;
                mDelY = mCurryY - mLastDownY;
                if (model == SWIPE_RIGHT) {
                    if (Math.abs(mDelX) > mScreenWidth / 3 && mDelX > 0) { //右滑并且滑动距离大于屏幕宽度的1/3
                        startScrollToFinish(getScrollX(), 0, -mScreenWidth - getScrollX(), 0, 1000);//滑动至结束
                    } else {
                        startScroll(getScrollX(), 0, -getScrollX(), 0, 500);//滑动回初始状态
                    }
                } else if (model == SWIPE_BOTTOM) {
                    if (Math.abs(mDelY) > mScreenHeight / 4 && mDelY > 0) {
                        startScrollToFinish(0, getScrollY(), 0, -mScreenHeight - getScrollY(), 1000);
                    } else {
                        startScroll(0, getScrollY(), 0, -getScrollY(), 500);
                    }
                } else if (model == SWIPE_LEFT) {
                    if (Math.abs(mDelX) > mScreenWidth / 3 && mDelX < 0) {
                        startScrollToFinish(getScrollX(), 0, mScreenWidth - getScrollX(), 0, 1000);
                    } else {
                        startScroll(getScrollX(), 0, -getScrollX(), 0, 500);
                    }
                } else if (model == SWIPE_TOP) {
                    if (Math.abs(mDelY) > mScreenHeight / 4 && mDelY < 0) {
                        startScrollToFinish(0, getScrollY(), 0, mScreenHeight - getScrollY(), 1000);
                    } else {
                        startScroll(0, getScrollY(), 0, -getScrollY(), 500);
                    }
                }

                break;
        }
        return super.onTouchEvent(event);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent event) {
        if (!isEnabled())
            return false;
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mLastDownX = (int) event.getX();
                mLastDownY = (int) event.getY();
                break;
            case MotionEvent.ACTION_MOVE:
                final float eventX = event.getX();
                final float eventY = event.getY();
                float xDiff = Math.abs(eventX - mLastDownX);
                float yDiff = Math.abs(eventY - mLastDownY);
                if (model == SWIPE_TOP || model == SWIPE_BOTTOM) {
                    if (yDiff > mTouchSlop && xDiff < yDiff) { //x滑动距离大于y滑动距离，并且大于mTouchSlop，认为是左滑
                        for(RecyclerView rv : recycleViews){
                            if(inRangeOfView(rv,event)){//如果滑动的是recycleView
//                                Log.d(TAG, "      -------> recycleView,event=" + event.getAction());
//                                Log.d(TAG, "      -------> recycleView,isRvTop=" + isRvTop);

                                if (isRecyclerScrollable(rv)) {
                                    // 判断是否滑到顶部
                                    rv.addOnScrollListener(new RecyclerView.OnScrollListener() {
                                        @Override
                                        public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                                            super.onScrollStateChanged(recyclerView, newState);
                                            // OnScrollListener.SCROLL_STATE_IDLE; //停止滑动状态
                                            // 记录当前滑动状态
                                            isRvTop = false;
                                            if (newState == RecyclerView.SCROLL_STATE_IDLE) { //当前状态为停止滑动
                                                if (!rv.canScrollVertically(1)) { // 到达底部
//                                                    Log.d(TAG, "到达底部");
                                                } else if (!rv.canScrollVertically(-1)) { // 到达顶部
                                                    isRvTop = true;
//                                                    Log.d(TAG, "到顶了");
                                                }
                                            }
                                        }
                                    });
                                    if (isRvTop){
                                        Log.d(TAG, "      -------> isRvTop is true");
                                        isRvTop = false;
                                        if(eventY - mLastDownY < 0)
                                            return false;

                                        return true;
                                    }
                                    return false;//不拦截，事件交由recycleView处理
                                }else{
                                    return true;
                                }
                            }
                        }

                        for(ScrollView sv : scrollViews){
                            if(inRangeOfView(sv,event)){//如果滑动的是ScrollView

                                sv.setOnScrollChangeListener(new OnScrollChangeListener() {
                                     @Override
                                     public void onScrollChange(View view, int i, int i1, int i2, int i3) {
                                         isSvTop = sv.getScrollY() == 0;
                                     }
                                 }

                                );
                                if (isSvTop){
                                    isSvTop = false;
                                    return true;
                                }
                                return false;//不拦截，事件交由ScrollView处理
                            }
                        }

                        return true;//拦截处理事件
                    }
                }
                break;
        }
        return false;
    }

    public boolean isRecyclerScrollable(RecyclerView recyclerView) {
        return recyclerView.computeHorizontalScrollRange() > recyclerView.getWidth() || recyclerView.computeVerticalScrollRange() > recyclerView.getHeight();
    }

    //    @Override
    public boolean onInterceptTouchEvent123(MotionEvent event) {
        if (!isEnabled())
            return false;
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mLastDownX = (int) event.getX();
                mLastDownY = (int) event.getY();
                break;
            case MotionEvent.ACTION_MOVE:
                final float eventX = event.getX();
                final float eventY = event.getY();
                float xDiff = Math.abs(eventX - mLastDownX);
                float yDiff = Math.abs(eventY - mLastDownY);
                if (model == SWIPE_LEFT || model == SWIPE_RIGHT) {
                    if (xDiff > mTouchSlop && xDiff > yDiff) { //x滑动距离大于y滑动距离，并且大于mTouchSlop，认为是左滑
                        for(ViewPager vp : viewPagers){
                            if(inRangeOfView(vp,event)){//如果滑动的是viewPager
                                return false;//不拦截，事件交由viewPager处理
                            }
                        }
                        return true;//拦截处理事件
                    }
                } else if (model == SWIPE_TOP || model == SWIPE_BOTTOM) {
                    if (yDiff > mTouchSlop && xDiff < yDiff) {
                        return true;
                    }
                }
                break;
        }
        return false;
    }

//    @Override
    public boolean onInterceptTouchEvent456(MotionEvent event) {
        if (!isEnabled())
            return false;
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mLastDownX = (int) event.getX();
                mLastDownY = (int) event.getY();
                splitFlag = true;
                break;
            case MotionEvent.ACTION_MOVE:
                final float eventX = event.getX();
                final float eventY = event.getY();
                float xDiff = Math.abs(eventX - mLastDownX);
                float yDiff = Math.abs(eventY - mLastDownY);

                RecyclerView recyclerView = searchRecyclerView(this);
                //获取ViewPager2的滑动阈值
                int touchSlop = 0;
                try {
                    assert recyclerView != null;
                    touchSlop = getRecyclerViewTouchSlop(recyclerView) / 6;
//                    Log.i("Jason", "onInterceptTouchEvent.touchSlop="+touchSlop);
//                    Log.i("Jason", "onInterceptTouchEvent.mTouchSlop="+mTouchSlop);
//                    Log.i("Jason", "onInterceptTouchEvent.eventX="+eventX+",eventY="+eventY);
//                    Log.i("Jason", "onInterceptTouchEvent.xDiff="+xDiff+",yDiff="+yDiff);
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    e.printStackTrace();
                }

                boolean isScroll = (yDiff > touchSlop);
//                Log.i("Jason", "onInterceptTouchEvent.isScroll="+isScroll+",splitFlag="+splitFlag);

                if (isScroll && splitFlag){
                    MotionEvent newMotionEvent = MotionEvent.obtain(event);
                    float newX = event.getX();
                    float newY = event.getY();
                    if (yDiff > touchSlop){
                        if (yDiff > 0){
                            newY = mLastDownY + touchSlop;
                        }else{
                            newY = mLastDownY - touchSlop;
                        }
                    }
                    newMotionEvent.setLocation(newX, newY);
                    //发生新的MotionEvent
                    super.dispatchTouchEvent(newMotionEvent);

                    splitFlag = false;

                    return false;//不拦截处理事件
                }else{
                    return true;//拦截处理事件
                }
        }

        return  super.onInterceptTouchEvent(event);
    }

    public final void setRecyclerViewTouchSlop(@NotNull RecyclerView recyclerView, int touchSlop) throws NoSuchFieldException, IllegalAccessException {
        Class clazz = RecyclerView.class;
        Field field = clazz.getDeclaredField("mTouchSlop");
        field.setAccessible(true);
        field.set(recyclerView, touchSlop);
    }

    /**
     * 获取RecyclerView的滑动阈值
     * @param recyclerView RecyclerView
     * @return Int 滑动阈值
     */
    public final int getRecyclerViewTouchSlop(@NotNull RecyclerView recyclerView) throws NoSuchFieldException, IllegalAccessException {
//        Intrinsics.checkNotNullParameter(recyclerView, "recyclerView");
        Class clazz = RecyclerView.class;
        Field field = clazz.getDeclaredField("mTouchSlop");
//        Intrinsics.checkNotNullExpressionValue(field, "field");
        field.setAccessible(true);
        Object var10000 = field.get(recyclerView);
        if (var10000 == null) {
            throw new NullPointerException("null cannot be cast to non-null type kotlin.Int");
        } else {
            int touchSlop = (Integer)var10000;
            return touchSlop;
        }
    }

    /**
     * 获取ViewGroup中的RecyclerView
     * @param vg ViewGroup
     * @return RecyclerView?
     */
    @Nullable
    public final RecyclerView searchRecyclerView(@NotNull ViewGroup vg) {
//        Intrinsics.checkNotNullParameter(vg, "vg");
        for(int i = 0; i < vg.getChildCount(); ++i) {
            View view = vg.getChildAt(i);
            if (view instanceof RecyclerView) {
                return (RecyclerView)view;
            }

            if (view instanceof ViewGroup) {
                RecyclerView recyclerView = this.searchRecyclerView((ViewGroup)view);
                if (recyclerView != null) {
                    return recyclerView;
                }
            }
        }

        return null;
    }

    private boolean inRangeOfView(View view, MotionEvent event) {
        int[] location = new int[2];
        view.getLocationOnScreen(location);
        int x = location[0];
        int y = location[1];
        if (event.getX() < x || event.getX() > (x + view.getWidth()) || event.getY() < y || event.getY() > (y + view.getHeight())) {
            return false;
        }
        return true;
    }

    /**
     * computeScroll在View的draw方法里面是空实现，所以这里需要自己去实现，以下是较为标准的写法
     * 可以看到的是在computeScroll方法里面通过scrollTo方法来实现View的滑动，紧接着调用了postInvalidate重绘
     * 会再次进入此方法，如此循环从而实现滑动的效果
     */
    @Override
    public void computeScroll() {
        if (mScroller.computeScrollOffset()) {
            scrollTo(mScroller.getCurrX(), mScroller.getCurrY());
            postInvalidate();
        } else {
            if (isClose) {
                setVisibility(View.GONE);
                if (onSwipeFinishListener != null)
                    onSwipeFinishListener.swipeFinish();
            }
        }
    }

    @Override
    protected void dispatchDraw(Canvas canvas) {
        drawShadow(canvas);
//        Log.d("", "------>dispatchDraw");
        super.dispatchDraw(canvas);
    }

    private boolean hideShadow = false;

    /**
     * 通过改变画笔的alpha值来改变阴影的深浅
     * RectF为限定画笔绘制的矩形区域，注意这里需要根据滑动值来调整区域，
     * 例如往右边滑动，其阴影左边界要相应向左偏移
     */
    private void drawShadow(Canvas canvas) {
        canvas.save();
        RectF rectF = new RectF(0, 0, mScreenWidth, mScreenHeight);
        int alpha = 0;
        if (!hideShadow) {
            if (model == SWIPE_RIGHT) {
                alpha = (int) (255 + getScrollX() / (float) (mScreenWidth / 2) * 255);
                rectF = new RectF(getScrollX(), 0, mScreenWidth, mScreenHeight);
            } else if (model == SWIPE_LEFT) {
                alpha = (int) (255 - getScrollX() / (float) (mScreenWidth / 2) * 255);
                rectF = new RectF(0, 0, mScreenWidth + getScrollX(), mScreenHeight);
            } else if (model == SWIPE_BOTTOM) {
                alpha = (int) (255 + getScrollY() / (float) (mScreenHeight / 2) * 255);
                rectF = new RectF(0, getScrollY(), mScreenWidth, mScreenHeight);
            } else {
                alpha = (int) (255 - getScrollY() / (float) (mScreenHeight / 2) * 255);
                rectF = new RectF(0, 0, mScreenWidth, mScreenHeight + getScrollY());
            }
            if (alpha < 0) alpha = 0;
        }

        // 背景完全透明
        alpha = 0;
        mPaint.setAlpha(alpha);
        canvas.drawRect(rectF, mPaint);
        canvas.restore();
    }

    /**
     * 设置滑动方向
     *
     * @param model 如SwipeBackLayout.SWIPE_LEFT
     */

    public void setModel(int model) {
        this.model = model;
    }

    boolean ComputeScrollFinish = true;

    public void setComputeScrollFinish(boolean ComputeScrollFinish) {
        this.ComputeScrollFinish = ComputeScrollFinish;
    }

    private OnSwipeFinishListener onSwipeFinishListener;

    public interface OnSwipeFinishListener {
        void swipeFinish();

        void swipeStart();
    }

    /**
     * 设置自动滑动结束的监听
     */
    public void setOnSwipeFinishListener(OnSwipeFinishListener onSwipeFinishListener) {
        this.onSwipeFinishListener = onSwipeFinishListener;
    }
}
