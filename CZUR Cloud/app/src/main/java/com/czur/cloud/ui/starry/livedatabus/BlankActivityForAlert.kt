package com.czur.cloud.ui.starry.livedatabus

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.KeyEvent
import com.blankj.utilcode.util.ActivityUtils
import com.czur.cloud.R
import com.czur.cloud.ui.base.BaseActivity
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.meeting.baselib.utils.ONE_SECOND
import com.czur.cloud.ui.starry.meeting.dialog.StarryCommonPopupAlert
import com.czur.cloud.ui.starry.meeting.dialog.StarryCommonToast
import com.czur.czurutils.log.logE
import java.util.*
import kotlin.concurrent.timerTask

open class BlankActivityForAlert : BaseActivity() {
    val TAG = "BlankActivityForAlert:"
    var commonPopup: StarryCommonPopupAlert? = null
    private var checkPopupVisableTimer: Timer? = null
    

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.transparent)
        setContentView(R.layout.starry_activity_blank)
        initData(intent)
        if (checkPopupVisableTimer == null){
            checkPopupVisableTimer = Timer()
        }
        checkPopupVisableTimer?.schedule(timerTask {
            if (commonPopup == null || commonPopup?.isShowing == false) {
                finish()
            }
        }, 1000)

    }

    fun initData(intent: Intent?) {
        ActivityUtils.getActivityList()
        val type = intent?.getStringExtra(StarryConstants.STARRY_BLANK_TYPE)
                ?: StarryConstants.STARRY_BLANK_TYPE_REMOVED

        try {
            if (commonPopup != null) {
                commonPopup?.dismiss()
                commonPopup = null
            }
        } catch (e: Exception) {
            logE(TAG + "initData:e=${e}")
            commonPopup = null
        }

        LiveDataBus.get()
                .with(StarryConstants.STARRY_BLANK_TYPE_CLOSE, String::class.java)
                .observe(this) {
                    try {
                        if (commonPopup != null) {
                            commonPopup?.dismiss()
                            commonPopup = null
                            finish()
                        }
                    } catch (e: Exception) {
                        logE(TAG + "initData:e=${e}")
                        commonPopup = null
                        finish()
                    }
                }


        when (type) {
            StarryConstants.STARRY_BLANK_TYPE_STOP_FORCE -> {
                showStopForceDialog()
            }

            StarryConstants.STARRY_BLANK_TYPE_REMOVED -> {
                showRemovedDialog()
            }

            StarryConstants.STARRY_BLANK_TYPE_STOP -> {
                showStopDialog()
            }

            StarryConstants.STARRY_BLANK_TYPE_RECALL -> {
                val name = intent?.getStringExtra(StarryConstants.STARRY_BLANK_PARAM2) ?: ""
                showRecallDialog(name)
            }

            StarryConstants.STARRY_BLANK_TYPE_QUITSHARE -> {
                showQuitShareDialogAutoClose()
            }

            StarryConstants.STARRY_BLANK_TYPE_OTHER_JOIN -> {
                showStarryMeetingOtherJoined()
            }
        }
    }

    // 停止
    private fun showStopDialog() {
        val builder = StarryCommonPopupAlert.Builder(this)
                .setTitle(getString(R.string.starry_popupwindow_title))
                .setMessage(getString(R.string.starry_popupwindow_msg_overmeeting))
                .setOnPositiveListener { dialog, _ ->
                    LiveDataBus.get().with(StarryConstants.STARRY_BLANK_TYPE_CLOSE).value = ""
                    dialog?.dismiss()
                    finish()
                }
                .setOnKeyBackListener { dialog, keyCode, _ ->
                    if (keyCode == KeyEvent.KEYCODE_BACK) {
                        LiveDataBus.get().with(StarryConstants.STARRY_BLANK_TYPE_CLOSE).value = ""
                        dialog?.dismiss()
                        finish()
                    }
                    false
                }
                .setOnDismissListener {
                    commonPopup = null
                    this.finish()
                }
        commonPopup = builder.create()
        commonPopup?.show()

    }

    // 长时间挂机熔断处理消息
    private fun showStopForceDialog() {
        val builder = StarryCommonPopupAlert.Builder(this)
                .setTitle(getString(R.string.starry_popupwindow_title))
                .setMessage(getString(R.string.starry_popupwindow_msg_stop_force))
                .setOnPositiveListener { dialog, _ ->
                    LiveDataBus.get().with(StarryConstants.STARRY_BLANK_TYPE_CLOSE).value = ""
                    dialog?.dismiss()
                    finish()
                }
                .setOnKeyBackListener { dialog, keyCode, _ ->
                    if (keyCode == KeyEvent.KEYCODE_BACK) {
                        LiveDataBus.get().with(StarryConstants.STARRY_BLANK_TYPE_CLOSE).value = ""
                        dialog?.dismiss()
                        finish()
                    }
                    false
                }
                .setOnDismissListener {
                    commonPopup = null
                    this.finish()
                }
        commonPopup = builder.create()
        commonPopup?.show()
    }

    // 被移除
    private fun showRemovedDialog() {
        val builder = StarryCommonPopupAlert.Builder(this)
            .setTitle(getString(R.string.starry_popupwindow_title))
            .setPositiveTitle(getString(R.string.ok))
            .setMessage(getString(R.string.starry_popupwindow_msg_removed))
            .setOnPositiveListener { dialog, _ ->
                LiveDataBus.get().with(StarryConstants.STARRY_BLANK_TYPE_CLOSE).value = ""
                dialog?.dismiss()
                finish()
            }
            .setOnKeyBackListener { dialog, keyCode, _ ->
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    LiveDataBus.get().with(StarryConstants.STARRY_BLANK_TYPE_CLOSE).value = ""
                    dialog?.dismiss()
                    finish()
                }
                false
            }
            .setOnDismissListener {
                commonPopup = null
                this.finish()
            }
        commonPopup = builder.create()
        commonPopup?.show()
    }

    // 会议中来电弹窗提示
    private fun showRecallDialog(name: String) {
        val t = Timer()
        val builder = StarryCommonToast.Builder(this)
                .setMessage(getString(R.string.starry_recall_msg, name))
                .setOnPositiveListener { dialog, _ ->
                    t.cancel()
                    dialog?.dismiss()
                    finish()
                }
                .setOnDismissListener {
                    commonPopup = null
                    this.finish()
                }

        val customToast = builder.create()
        customToast.show()

        t.schedule(object : TimerTask() {
            override fun run() {
                t.cancel()
                customToast.dismiss()
                finish()
            }
        }, 10 * ONE_SECOND)

    }

    //您已被主持人停止分享
    private fun showQuitShareDialogAutoClose() {
        val builder = StarryCommonPopupAlert.Builder(this)
                .setTitle(getString(R.string.starry_popupwindow_title))
                .setMessage(getString(R.string.starry_popupwindow_msg_quitshare))
                .setOnPositiveListener { dialog, _ ->
                    LiveDataBus.get().with(StarryConstants.STARRY_BLANK_TYPE_CLOSE).value = ""
                    dialog?.dismiss()
                    finish()
                }
                .setOnKeyBackListener { dialog, keyCode, _ ->
                    if (keyCode == KeyEvent.KEYCODE_BACK) {
                        LiveDataBus.get().with(StarryConstants.STARRY_BLANK_TYPE_CLOSE).value = ""
                        dialog?.dismiss()
                        finish()
                    }
                    false
                }
                .setOnDismissListener {
                    commonPopup = null
                    this.finish()
                }
        commonPopup = builder.create()
        commonPopup?.show()

    }

    //其他端接听
    private fun showStarryMeetingOtherJoined() {
        val builder = StarryCommonPopupAlert.Builder(this)
                .setTitle(getString(R.string.starry_popupwindow_title))
                .setMessage(getString(R.string.starry_meeting_other_joined))
                .setOnPositiveListener { dialog, _ ->
                    LiveDataBus.get().with(StarryConstants.STARRY_BLANK_TYPE_CLOSE).value = ""
                    dialog?.dismiss()
                    finish()
                }
                .setOnKeyBackListener { dialog, keyCode, _ ->
                    if (keyCode == KeyEvent.KEYCODE_BACK) {
                        LiveDataBus.get().with(StarryConstants.STARRY_BLANK_TYPE_CLOSE).value = ""
                        dialog?.dismiss()
                        finish()
                    }
                    false
                }
                .setOnDismissListener {
                    commonPopup = null
                    this.finish()
                }
        commonPopup = builder.create()
        commonPopup?.show()

    }

    override fun onPause() {
        super.onPause()
        if (commonPopup == null || commonPopup?.isShowing == false) {
            finish()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.i(TAG, "onDestroy")
        if (commonPopup != null) {
            commonPopup?.dismiss()
            commonPopup = null
        }
        checkPopupVisableTimer?.cancel()
    }

}