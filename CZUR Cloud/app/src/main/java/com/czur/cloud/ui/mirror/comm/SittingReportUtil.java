package com.czur.cloud.ui.mirror.comm;

import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.graphics.Color;
import android.os.Build;

import androidx.annotation.RequiresApi;

import com.czur.cloud.model.AuraMateReportModelSub;
import com.czur.cloud.ui.auramate.reportfragment.CustomXAxisRenderer;
import com.czur.cloud.ui.auramate.reportfragment.ReportUtil;
import com.czur.cloud.ui.mirror.model.SittingReportModelSub;
import com.czur.cloud.util.validator.Validator;
import com.github.mikephil.charting.charts.LineChart;
import com.github.mikephil.charting.components.LimitLine;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;
import com.github.mikephil.charting.formatter.ValueFormatter;
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class SittingReportUtil {
    private static final String TAG = "SittingReportUtil";


    // 获取并判断localTime日期字符串
    public static String getLocalDateTime(String localTime){
        String str=" ";
        if (Validator.isNotEmpty(localTime)){
            if (localTime.equals("null") || localTime.equals("NULL"))
                localTime = "";
            str = localTime;
        }

        return str+" ";
    }

    // 初始化lineChart
    @RequiresApi(api = Build.VERSION_CODES.HONEYCOMB)
    public static void initChart(LineChart chart){

        chart.getDescription().setEnabled(false);

        // if more than 60 entries are displayed in the chart, no values will be drawn
        chart.setMaxVisibleValueCount(60);

        //是否能够缩放
        chart.setPinchZoom(false);
        //允许X轴缩放
        chart.setScaleXEnabled(false);
        //允许Y轴缩放
        chart.setScaleYEnabled(false);

        chart.setDrawGridBackground(false);

        XAxis xAxis = chart.getXAxis();
        YAxis leftAxis = chart.getAxisLeft();
        YAxis rightAxis = chart.getAxisRight();

        xAxis.setPosition(XAxis.XAxisPosition.BOTTOM);
        xAxis.setGranularity(1f);
        xAxis.setLabelCount(FastBleConstants.BAR_MAX_COUNT, false);

        // add a nice and smooth animation
        chart.animateY(1000);

        leftAxis.setValueFormatter(new ValueFormatter() {
            @Override
            public String getFormattedValue(float value) {
                if (value>0)
                    return ((int) (value)) + "%";
                else{
                    return "";
                }
            }
        });
        leftAxis.setLabelCount(5, false);
        leftAxis.setTextColor(Color.rgb(128,128,128));
        xAxis.setTextColor(Color.rgb(128,128,128));

        chart.getAxisLeft().setDrawAxisLine(false);
        chart.getXAxis().setDrawAxisLine(false);

        chart.getAxisRight().setEnabled(false);
        chart.getLegend().setEnabled(false);

        chart.setDrawBorders(false); ////是否在折线图上添加边框
        // 如果没有数据的时候，会显示这个，类似ListView的EmptyView
        chart.setDrawGridBackground(true); // 是否显示表格颜色
        chart.setGridBackgroundColor(Color.argb(128, 240,240,240)); // 表格的的颜色，在这里是是给颜色设置一个透明度

        int lineColor = Color.argb(100, 128, 128, 128);
        int lineColor_x = Color.argb(200, 128, 128, 128);
        float lineLen = 5f, spaceLen = 10f, phase = 0f;

        //是否展示网格线
        xAxis.setDrawGridLines(true);
        xAxis.setGridColor(lineColor_x);
        rightAxis.setDrawGridLines(false);
        leftAxis.setDrawGridLines(false);
        leftAxis.enableGridDashedLine(lineLen, spaceLen, phase);// 设置X Y轴网格线为虚线（实体线长度、间隔距离、偏移量：通常使用 0）
        xAxis.enableGridDashedLine(lineLen, spaceLen, phase);// 设置X Y轴网格线为虚线（实体线长度、间隔距离、偏移量：通常使用 0）

        float dd = 1f;
        float limitDD = 20f;

        LimitLine limitLine2 = new LimitLine(limitDD-dd);
        limitLine2.setLineColor(lineColor);
        limitLine2.enableDashedLine(lineLen, spaceLen, phase);
        leftAxis.addLimitLine(limitLine2);

        LimitLine limitLine4 = new LimitLine(limitDD*2-dd*2);
        limitLine4.setLineColor(lineColor);
        limitLine4.enableDashedLine(lineLen, spaceLen, phase);
        leftAxis.addLimitLine(limitLine4);

        LimitLine limitLine6 = new LimitLine(limitDD*3-dd*3);
        limitLine6.setLineColor(lineColor);
        limitLine6.enableDashedLine(lineLen, spaceLen, phase);
        leftAxis.addLimitLine(limitLine6);

        LimitLine limitLine8 = new LimitLine(limitDD*4-dd*4);
        limitLine8.setLineColor(lineColor);
        limitLine8.enableDashedLine(lineLen, spaceLen, phase);
        leftAxis.addLimitLine(limitLine8);

        chart.setTouchEnabled(true);//设置是否可以触摸，如为false，则不能拖动，缩放等
        chart.setDragEnabled(true); //设置是否可以拖拽，缩放

        leftAxis.setYOffset(8f);//这样会向下偏移 50%。我也不知道为啥，自己试出来的
        chart.setExtraRightOffset(20f);     // x轴文字右下角日期显示不全
        xAxis.setYOffset(10f);//

        xAxis.setAxisMinimum(-0.5f);    //X轴数据向右偏移一些
        leftAxis.setAxisMinimum(0f);    //最小刻度值
        leftAxis.setAxisMaximum(100f);  // the axis maximum is 100

        chart.setExtraBottomOffset(2.2f * 10f);
        xAxis.setTextSize(10f);
        chart.setXAxisRenderer(new CustomXAxisRenderer(chart.getViewPortHandler(), chart.getXAxis(), chart.getTransformer(YAxis.AxisDependency.LEFT)));

        chart.invalidate();
    }

    // 初始化lineChart
    @RequiresApi(api = Build.VERSION_CODES.HONEYCOMB)
    public static void initChartOversea(LineChart chart){

        chart.getDescription().setEnabled(false);

        // if more than 60 entries are displayed in the chart, no values will be drawn
        chart.setMaxVisibleValueCount(60);

        //是否能够缩放
        chart.setPinchZoom(false);
        //允许X轴缩放
        chart.setScaleXEnabled(false);
        //允许Y轴缩放
        chart.setScaleYEnabled(false);

        chart.setDrawGridBackground(false);

        XAxis xAxis = chart.getXAxis();
        YAxis leftAxis = chart.getAxisLeft();
        YAxis rightAxis = chart.getAxisRight();

        xAxis.setPosition(XAxis.XAxisPosition.BOTTOM);
        xAxis.setGranularity(1f);
        xAxis.setLabelCount(FastBleConstants.BAR_MAX_COUNT, false);

        // add a nice and smooth animation
        chart.animateY(1000);

        leftAxis.setValueFormatter(new ValueFormatter() {
            @Override
            public String getFormattedValue(float value) {
                if (value>0)
                    return ((int) (value)) + "%";
                else{
                    return "";
                }
            }
        });
        leftAxis.setLabelCount(5, false);
        leftAxis.setTextColor(Color.rgb(128,128,128));
        xAxis.setTextColor(Color.rgb(128,128,128));

        chart.getAxisLeft().setDrawAxisLine(false);
        chart.getXAxis().setDrawAxisLine(false);

        chart.getAxisRight().setEnabled(false);
        chart.getLegend().setEnabled(false);

        chart.setDrawBorders(false); ////是否在折线图上添加边框
        // 如果没有数据的时候，会显示这个，类似ListView的EmptyView
        chart.setDrawGridBackground(true); // 是否显示表格颜色
        chart.setGridBackgroundColor(Color.argb(128, 240,240,240)); // 表格的的颜色，在这里是是给颜色设置一个透明度

        int lineColor = Color.argb(100, 128, 128, 128);
        int lineColor_x = Color.argb(200, 128, 128, 128);
        float lineLen = 5f, spaceLen = 10f, phase = 0f;
        float lineWidth = 1.0f;

        //是否展示网格线
//        rightAxis.setDrawGridLines(false);
//        leftAxis.setDrawGridLines(false);
//        xAxis.setDrawGridLines(false);

        xAxis.setDrawGridLines(true);
        xAxis.setGridLineWidth(lineWidth);
        xAxis.setGridColor(lineColor_x);
        leftAxis.enableGridDashedLine(lineLen, spaceLen, phase);// 设置X Y轴网格线为虚线（实体线长度、间隔距离、偏移量：通常使用 0）
        xAxis.enableGridDashedLine(lineLen, spaceLen, phase);// 设置X Y轴网格线为虚线（实体线长度、间隔距离、偏移量：通常使用 0）

        float dd = 1f;
        float limitDD = 20f;

        LimitLine limitLine2 = new LimitLine(limitDD-dd);
        limitLine2.setLineColor(lineColor);
        limitLine2.enableDashedLine(lineLen, spaceLen, phase);
        leftAxis.addLimitLine(limitLine2);

        LimitLine limitLine4 = new LimitLine(limitDD*2-dd*2);
        limitLine4.setLineColor(lineColor);
        limitLine4.enableDashedLine(lineLen, spaceLen, phase);
        leftAxis.addLimitLine(limitLine4);

        LimitLine limitLine6 = new LimitLine(limitDD*3-dd*3);
        limitLine6.setLineColor(lineColor);
        limitLine6.enableDashedLine(lineLen, spaceLen, phase);
        leftAxis.addLimitLine(limitLine6);

        LimitLine limitLine8 = new LimitLine(limitDD*4-dd*4);
        limitLine8.setLineColor(lineColor);
        limitLine8.enableDashedLine(lineLen, spaceLen, phase);
        leftAxis.addLimitLine(limitLine8);

        chart.setTouchEnabled(true);//设置是否可以触摸，如为false，则不能拖动，缩放等
        chart.setDragEnabled(true); //设置是否可以拖拽，缩放

        leftAxis.setYOffset(8f);//这样会向下偏移 50%。我也不知道为啥，自己试出来的
        chart.setExtraRightOffset(20f);     // x轴文字右下角日期显示不全
        xAxis.setYOffset(10f);//

        xAxis.setAxisMinimum(-0.5f);    //X轴数据向右偏移一些
        leftAxis.setAxisMinimum(0f);    //最小刻度值
        leftAxis.setAxisMaximum(100f);  // the axis maximum is 100

        chart.setExtraBottomOffset(2.2f * 10f);
        xAxis.setTextSize(10f);
        chart.setXAxisRenderer(new CustomXAxisRenderer(chart.getViewPortHandler(), chart.getXAxis(), chart.getTransformer(YAxis.AxisDependency.LEFT)));

        chart.invalidate();
    }

    // 刷新LineChart UI
    public static void reflashChart(LineChart chart,
                                    List<String> barChartDataListX,
                                    List<Integer> barChartDataListY,
                                    List<Integer> barChartDataListYError,
                                    int colorRight,
                                    int colorError,
                                    String type){

        XAxis xAxis = chart.getXAxis();

        xAxis.setValueFormatter(new ValueFormatter() {
            @Override
            public String getFormattedValue(float value) {
                if ((int)value > barChartDataListX.size()-1)
                    return "";
//                return barChartDataListX.get((int)value);
                String ret1 = barChartDataListX.get((int)value);
                String ret = ret1;
                //仅仅针对周的情况，需要旋转显示
                if (type.equals("2")) {
                    String[] strArray = ret1.split("-");
                    if (strArray.length > 1) {
                        String strFrom = strArray[0];
                        String strEnd = strArray[1];
                        ret =  strFrom + "-\n" + strEnd;
                    }
                }
                return ret;
            }
        });

        int lenSize = barChartDataListY.size();

        // setting data
        ArrayList<Entry> valuesGood = new ArrayList<>();
        ArrayList<Entry> valuesError = new ArrayList<>();
        ArrayList<Entry> valuesBlank = new ArrayList<>();
        float xSize = 0.95f;

        for (int i = 0; i < lenSize; i++) {
            valuesGood.add(new Entry(i, barChartDataListY.get(i) * xSize));
            valuesError.add(new Entry(i, barChartDataListYError.get(i) * xSize));
            valuesBlank.add(new Entry(i + 0.3f, barChartDataListY.get(i) * xSize));
        }

        ArrayList<ILineDataSet> dataSets = new ArrayList<>();
        float r = 6.0f;
        float lineWidth = 2.5f;

        LineDataSet dGood = new LineDataSet(valuesGood, "DataSet " + (1));
        LineDataSet dError = new LineDataSet(valuesError, "DataSet " + (2));
        LineDataSet dBlank = new LineDataSet(valuesBlank, "DataSet " + (3));

        int c = Color.argb(150, 55, 55, 55);
        float w = 1.0f;
        dGood.setHighLightColor(c);//高亮交叉指示线颜色
        dGood.setHighlightLineWidth(w);
        dGood.setDrawHorizontalHighlightIndicator(false);
        dGood.setDrawVerticalHighlightIndicator(true);
        dError.setHighLightColor(c);//高亮交叉指示线颜色
        dError.setHighlightLineWidth(w);
        dError.setDrawHorizontalHighlightIndicator(false);
        dError.setDrawVerticalHighlightIndicator(true);

        // 不显示值
        dGood.setDrawValues(false);
        dError.setDrawValues(false);

        dError.setLineWidth(lineWidth);
        dError.setColor(colorError);
        dError.setCircleColor(colorError);
        dError.setCircleHoleColor(colorError);
        dError.setCircleRadius(r);
        dataSets.add(dError);

        dGood.setLineWidth(lineWidth);
        dGood.setColor(colorRight);
        dGood.setCircleColor(colorRight);
        dGood.setCircleHoleColor(colorRight);
        dGood.setCircleRadius(r);
        dataSets.add(dGood);

        int colorBlank = Color.TRANSPARENT;
        dBlank.setColor(colorBlank);
        dBlank.setCircleColor(colorBlank);
        dBlank.setCircleHoleColor(colorBlank);
        dBlank.setDrawValues(false);
        dBlank.setHighlightEnabled(false);
        dataSets.add(dBlank);

        LineData data = new LineData(dataSets);
        chart.setData(data);

        //setVisibleXRange是设置可见的x范围，我设置成(0, 5)之后，
        // 就只会显示序号0到5的内容(6项)。往左滑才会显示第7,8,9…最后一项。
        chart.setVisibleXRange(0,FastBleConstants.BAR_MAX_COUNT);

        chart.notifyDataSetChanged();
        chart.invalidate();
        chart.requestLayout();
    }


    //接口获取的数据插值补充
    public static List<SittingReportModelSub> reportListDatasAddSpace(List<SittingReportModelSub> reportDatas, int type) {
        List<SittingReportModelSub> retDatas = new ArrayList<>();
        ArrayList<String> retList = new ArrayList<>();
        ArrayList<String> retListFormat = new ArrayList<>();
        int count = reportDatas.size();

        if (count > 1) {
            for ( int indx=0; indx<count-1; indx++){

                String sStartDate = String.valueOf(reportDatas.get(indx).getFromEnd());
                String sEndDate = String.valueOf(reportDatas.get(indx + 1).getFromEnd());
                String sStartDateFormat = String.valueOf(reportDatas.get(indx).getFromEndDate());
                String sEndDateFormat = String.valueOf(reportDatas.get(indx + 1).getFromEndDate());

                sStartDate = foramtDateTime(sStartDate, type);
                sEndDate = foramtDateTime(sEndDate, type);

                if (sStartDate.equals(sEndDate)) {
                    retDatas.add(reportDatas.get(indx));
                }else {
                    retList = insertDateList(sStartDate, sEndDate, type);
                    if (type == 1) {
                        retListFormat = insertDateListFormat(sStartDateFormat, sEndDateFormat, type);
                    }
                    retDatas.add(reportDatas.get(indx));
                    for (int i=1;i<retList.size();i++){
                        SittingReportModelSub sub = new SittingReportModelSub();
                        sub.setId(0);
                        sub.setType(type);
                        sub.setTitle("");
                        String fromEnd = retList.get(i);
                        sub.setFromEnd(fromEnd);
                        if (type == 1) {
                            sub.setFromEndDate(retListFormat.get(i));
                        }
                        sub.setRightProportion(0);
                        sub.setSeriousProportion(0);//seriousProportion
                        sub.setModerateProportion(0);//moderateProportion
                        sub.setMildProportion(0);//mildProportion
                        retDatas.add(sub);

                    }
                    //tmpDatas.add(tmpDatasRev.get(indx+1));不添加，给下一个留着
                }
            }
            //最后再加上最后一个数据
            retDatas.add(reportDatas.get(count-1));
        }else {
            retDatas = reportDatas;
        }
        return retDatas;
    }

    //反转数据，并格式化日期
    public static List<SittingReportModelSub> reportListDatasReversal(List<SittingReportModelSub> reportDatas,
                                                                      int type) {
        List<SittingReportModelSub> tmpDatasRev = new ArrayList<>();

        if (Validator.isEmpty(reportDatas)){
            return null;
        }

        int count = reportDatas.size();
        for (int i = 0;i < count; i++) {
            SittingReportModelSub sub = reportDatas.get(i);
            String fromEndDate = sub.getFromEnd();
            String sEndDate = String.valueOf(sub.getFromEnd());
            sEndDate = foramtDateTime(sEndDate, type);
            sub.setFromEnd(sEndDate);
            sub.setFromEndDate(fromEndDate);
            tmpDatasRev.add(0,sub);
        }
        return tmpDatasRev;
    }

    // 判断最新数据是否为当天日期，否则添加空数据到当日
    public  static List<SittingReportModelSub> addTodayZeroData(List<SittingReportModelSub> models) {
        List<SittingReportModelSub> reportListDatas = new ArrayList<>();

        reportListDatas = models;
        SittingReportModelSub modelSub = reportListDatas.get(0);
        String fromEnd1 = modelSub.getFromEnd();     //"fromEnd": "2021-03-24",
        String fromEnd = foramtDateTime(fromEnd1, "yyyy.MM.dd");
//        String nowDay = "2021.07.23";
        String nowDay = ReportUtil.getNowDay("yyyy.MM.dd");
        boolean isNew = ReportUtil.isDateOneBigger(nowDay, fromEnd);
        if (isNew) {
            SittingReportModelSub sub = new SittingReportModelSub();
            sub.setId(0);
            sub.setType(1);
            sub.setTitle(nowDay + "");
            sub.setFromEnd(nowDay);
            sub.setRightProportion(0);
            sub.setSeriousProportion(0);//seriousProportion
            sub.setModerateProportion(0);//moderateProportion
            sub.setMildProportion(0);//mildProportion
            sub.setHappy(0);
            reportListDatas.add(0, sub);
        }

        return reportListDatas;
    }

    //打印列表数据的首位数据；
    public  static void printDatasLog(List<AuraMateReportModelSub> modelSub){
        if (Validator.isEmpty(modelSub)){
            return;
        }
        int count = modelSub.size();
        logI("=====printSittingDatasLog",
                "count="+count,
                "No.0",
                "id="+modelSub.get(0).getId(),
                "FromEnd="+modelSub.get(0).getFromEnd(),
                "getTitle="+modelSub.get(0).getTitle(),
                "No."+(count-1),
                "id="+modelSub.get(count-1).getId(),
                "FromEnd="+modelSub.get(count-1).getFromEnd(),
                "getTitle="+modelSub.get(count-1).getTitle());
    }

    //打印列表数据的首位数据；
    public  static void printSittingDatasLog(List<SittingReportModelSub> modelSub){
        if (Validator.isEmpty(modelSub)){
            logI("=====printSittingDatasLog:modelSub is Null");
            return;
        }
        int count = modelSub.size();
        logI("=====printSittingDatasLog",
                "count="+count,
                "No.0",
                "id="+modelSub.get(0).getId(),
                "FromEnd="+modelSub.get(0).getFromEnd(),
                "getTitle="+modelSub.get(0).getTitle(),
                "No."+(count-1),
                "id="+modelSub.get(count-1).getId(),
                "FromEnd="+modelSub.get(count-1).getFromEnd(),
                "getTitle="+modelSub.get(count-1).getTitle());
    }

    //给定起止日期，连续生成有效的日期数据；根据类型区分；
    //日：11.26 ==> [11.20,11.21,11.22,11.23,11.24,11.25,11.26]
    //周：11.16-11.22 ==> [10.5-10.11,10.12-10.18,11.19-10.25,10.26-11.1,11.2-11.8,11.9-11.15,11.16-11.22]
    //月：2020.11 ==> [2020.05,2020.06,2020.07,2020.08,2020.09,2020.10,2020.11]
    public static ArrayList<String> insertDateList(String startDate, String endDate, int type) {
        final int countSize = 30;
        ArrayList<String> retList = new ArrayList<>();

        retList.add(startDate);
        //日
        if (type == 1){
            for (int i=1;i<countSize;i++){
                String ret = getBeforeDay(startDate, -i);
                if (ret.equals(endDate))
                    break;

                retList.add(ret);
            }
        }

        //周
        if (type == 2){
            for (int i=1;i<countSize;i++){
                String ret = getBeforeWeek(startDate, -i);
                if (ret.equals(endDate))
                    break;

                retList.add(ret);
            }
        }

        //月
        if (type == 3){
            for (int i=1;i<countSize;i++){
                String ret = getBeforeMonth(startDate, -i);
                if (ret.equals(endDate))
                    break;

                retList.add(ret);
            }
        }
//        retList.add(endDate);

        return retList;
    }

    // 日：[2021-07-20,2021-07-21,2021-07-22,2021-07-23,2021-07-24]
    public static ArrayList<String> insertDateListFormat(String startDate, String endDate, int type) {
        final int countSize = 30;
        ArrayList<String> retList = new ArrayList<>();

        retList.add(startDate);
        //日
        for (int i=1;i<countSize;i++){
            String ret = getBeforeDayFormat(startDate, -i);
            if (ret.equals(endDate))
                break;

            retList.add(ret);
        }

        return retList;
    }

    /**
     * 获取本周的第一天--最后一天
     * @return String
     * **/
    public static String getWeekStartEnd(){
        Calendar cal=Calendar.getInstance();
        cal.add(Calendar.WEEK_OF_MONTH, 0);
        cal.set(Calendar.DAY_OF_WEEK, 2);
        Date time=cal.getTime();
        String start = new SimpleDateFormat("MM.dd").format(time);
        cal.set(Calendar.DAY_OF_WEEK, cal.getActualMaximum(Calendar.DAY_OF_WEEK));
        cal.add(Calendar.DAY_OF_WEEK, 1);
        Date time2=cal.getTime();
        String end = new SimpleDateFormat("MM.dd").format(time2);

        return start + "-" + end;
    }

    /**
     * 返回给定月份的前n月
     * 2020.11 == 前3月 ==》 2020.8
     */
    public static String getBeforeMonth(String oldDate, int n){
        String retStr = "";

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM");
            Date dt = sdf.parse(oldDate);
            Calendar rightNow = Calendar.getInstance();
            rightNow.setTime(dt);
            rightNow.add(Calendar.MONTH, -n);//-n月
            Date dt1 = rightNow.getTime();

            retStr = sdf.format(dt1);

        }catch (Exception e){
            e.printStackTrace();
        }
        return retStr;
    }

    /**
     * 返回给定星期的前n周
     * 11.16-11.22 == 前1周 ==》 11.9-11.15
     */
    public static String getBeforeWeek(String oldDate, int n){
        String retStr = "";
        String[] strArray = oldDate.split("-");

        if (strArray.length < 1){
            return "";
        }

        String strMon = strArray[0];
        String strSun = strArray[1];

        String strFrom = getBeforeDay(strMon,n*7);
        String strEnd = getBeforeDay(strSun,n*7);

        retStr = strFrom+"-"+strEnd;

        return retStr;
    }

    /**
     * 返回给定日期的前n天日
     */
    public static String getBeforeDay(String oldDate, int n){
        String retStr = "";

        try {
//            SimpleDateFormat sdf = new SimpleDateFormat("yyyy.M.d");
            SimpleDateFormat sdf = new SimpleDateFormat("MM.dd");
            Date dt = sdf.parse(oldDate);
            Calendar rightNow = Calendar.getInstance();
            rightNow.setTime(dt);
            //rightNow.add(Calendar.YEAR, -1);//日期减1年
            //rightNow.add(Calendar.MONTH, 3);//日期加3个月
            rightNow.add(Calendar.DAY_OF_YEAR, -n);//日期-7天
            Date dt1 = rightNow.getTime();

            retStr = sdf.format(dt1);

        }catch (Exception e){
            e.printStackTrace();
        }

        return retStr;
    }

    /**
     * 返回给定日期的前n天日
     */
    public static String getBeforeDayFormat(String oldDate, int n){
        String retStr = "";

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date dt = sdf.parse(oldDate);
            Calendar rightNow = Calendar.getInstance();
            rightNow.setTime(dt);
            rightNow.add(Calendar.DAY_OF_YEAR, -n);//日期-7天
            Date dt1 = rightNow.getTime();

            retStr = sdf.format(dt1);

        }catch (Exception e){
            e.printStackTrace();
        }

        return retStr;
    }

    //按照 日周月分别格式化日期字符串
    //日：12-01 ==> 12.1
    //    2020-12-08 ==》 12.8
    //周：2020/11/23-2020/11/29 ==> 11.23-11.29
    //月：2020-12 ==》 2020.12
    public static String foramtDateTime(String oldDate, int type){
        String retData = "";
//        System.out.println("foramtDateTime.oldDate===" + oldDate);

        // 12-01 ==> 12.1
        if (type == 1){
            retData = foramtDayDateTime(oldDate);
        }
        // 2020/11/23-2020/11/29 ==> 11.23-11.29
        else if (type == 2){
            String[] strArray = oldDate.split("-");

            if (strArray.length < 1){
                return "";
            }
            String strMon = strArray[0];
            String strSun = strArray[1];

            String strFrom = foramtDayDateTime(strMon);
            String strEnd = foramtDayDateTime(strSun);

            retData = strFrom+"-"+strEnd;
        }
        // 2020-12 ==> 2020.12
        else if (type == 3){
            retData = foramtMonthDateTime(oldDate);
        }
        else{
            retData = "";
        }

        return retData;
    }

    // 格式化日字符串为：MM.dd
    public static String foramtDayDateTime(String oldDate){
        String retData = "";
        oldDate = oldDate.replace("/",".").replace("-",".");
        if (oldDate.length() > 5)
            oldDate = oldDate.substring(5);

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("MM.dd");
            Date dt = sdf.parse(oldDate);
            retData = sdf.format(dt);
        }catch (Exception e){
            e.printStackTrace();
        }
        return retData;
    }

    //格式化月字符串为：yyyy.M
    public static String foramtMonthDateTime(String oldDate){
        String retData = "";
        oldDate = oldDate.replace("/",".").replace("-",".");
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM");
            Date dt = sdf.parse(oldDate);
            retData = sdf.format(dt);
        }catch (Exception e){
            e.printStackTrace();
        }
        return retData;
    }

    // formater: "yyyy.MM.dd"
    public static String foramtDateTime(String oldDate, String formater){
        String retData = "";
        oldDate = oldDate.replace("/",".").replace("-",".");
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(formater);
            Date dt = sdf.parse(oldDate);
            retData = sdf.format(dt);
        }catch (Exception e){
            e.printStackTrace();
        }
        return retData;
    }

    public static String getTodayFormatDate(){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM.dd");
        String retData = sdf.format(new Date());
        return retData;
    }

}
