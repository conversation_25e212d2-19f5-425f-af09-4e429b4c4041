package com.czur.cloud.ui.album;

import android.Manifest;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.UtilsTransActivity;
import com.czur.cloud.R;
import com.czur.cloud.adapter.AlbumFolderAdapter;
import com.czur.cloud.entity.ImageFolder;
import com.czur.cloud.event.EventType;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.starry.meeting.dialog.StarryCommonPopup;
import com.czur.cloud.ui.starry.utils.RomUtils;
import com.czur.cloud.util.PermissionUtil;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Yz on 2018/2/28.
 * Email：<EMAIL>
 */

public class SelectAlbumPhotoActivity extends BaseActivity implements View.OnClickListener, ImageDataSource.OnImagesLoadedListener {
    public static final int REQUEST_PERMISSION_STORAGE = 0x01;
    private TextView noBackTopBarTitle;
    private RelativeLayout noBackTopBarCancel;
    private List<ImageFolder> mImageFolders;
    private AlbumFolderAdapter albumFolderAdapter;
    private RecyclerView albumFolderList;
    private ImagePicker imagePicker;
    private boolean isFirstLoad = true;
    private ImageDataSource imageDataSource;
    private boolean hasIntentToRequestPermission = false;
    private StarryCommonPopup requestPermissionDialog;
    private boolean clickDialogOpen = false;
    private boolean UserRefusePermission = false;
    long requestPermissionClickTime = 0;
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_select_album_photo);
        initComponent();
    }

    private void initComponent() {

        albumFolderList = (RecyclerView) findViewById(R.id.album_folder_list);
        noBackTopBarTitle = (TextView) findViewById(R.id.no_back_top_bar_title);
        noBackTopBarCancel = (RelativeLayout) findViewById(R.id.no_back_top_bar_cancel);
        imagePicker = ImagePicker.getInstance();
        noBackTopBarTitle.setText(R.string.album);
        initAlbumList();
        showExplainForPermission();
        registerEvent();

    }

    private void useToolsRequestPermission(String[] permission) {
        requestPermissionClickTime = System.currentTimeMillis();
        final boolean[] isRefuseSecondPermission = {false};//当时点击了永久拒绝
        PermissionUtils.permission(permission)
                .rationale(new PermissionUtils.OnRationaleListener() {
                    @Override
                    public void rationale(@NonNull UtilsTransActivity activity, @NonNull ShouldRequest shouldRequest) {
                        //解释后是否需要继续弹出请求权限弹窗
                        shouldRequest.again(true);
                        isRefuseSecondPermission[0] = true;
                    }
                })

                .callback(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(@NonNull List<String> granted) {
                        hasIntentToRequestPermission = false;
                        //权限通过
                        if (requestPermissionDialog != null && requestPermissionDialog.isShowing()) {
                            requestPermissionDialog.dismiss();
                        }
                        showProgressDialog();
                        imageDataSource = new ImageDataSource(SelectAlbumPhotoActivity.this, null, SelectAlbumPhotoActivity.this);

                    }

                    @Override
                    public void onDenied(@NonNull List<String> deniedForever, @NonNull List<String> denied) {
                        //权限拒绝
                        if (deniedForever.size()>0){//永久拒绝
                            if (isRefuseSecondPermission[0]){
                                //当时点击永久拒绝的时候不做处理
                            }else {
                                if ((System.currentTimeMillis() - requestPermissionClickTime) < 500){//500ms之内 主观认为是系统返回,而非用户点击
                                    if (RomUtils.INSTANCE.isVivo()){
                                        RomUtils.PermissionPageManagement.INSTANCE.goIntentSetting(SelectAlbumPhotoActivity.this);
                                    }else {
                                        RomUtils.PermissionPageManagement.INSTANCE.goToSetting(SelectAlbumPhotoActivity.this);
                                    }
                                    hasIntentToRequestPermission = true;
                                }else {
                                    showExplainForPermission();
                                }


                            }

                        }else if (denied.size()>0){
                            //第一次拒绝

                        }
                    }
                })
                .explain(new PermissionUtils.OnExplainListener() {
                    @Override
                    public void explain(@NonNull UtilsTransActivity activity, @NonNull List<String> denied, @NonNull ShouldRequest shouldRequest) {
                        //第一次拒绝过了, 现在进行第二次权限弹窗,提示你需要弹出解释窗了
                        shouldRequest.start(true);
                    }
                }).request();

    }

    private void showExplainForPermission() {
        if (requestPermissionDialog != null && requestPermissionDialog.isShowing()){
            requestPermissionDialog.dismiss();
        }//Manifest.permission.READ_EXTERNAL_STORAGE,
        if (PermissionUtils.isGranted(PermissionUtil.getStoragePermission())) {
            showProgressDialog();
            //有权限
            imageDataSource = new ImageDataSource(SelectAlbumPhotoActivity.this, null, SelectAlbumPhotoActivity.this);
        } else {
            String str = "";
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                str = getString(R.string.starry_storage_photo13_permission_explain);
            } else {
                str = getString(R.string.starry_storage_photo_permission_explain);
            }
            //没权限时候
            requestPermissionDialog = new StarryCommonPopup.Builder(this, CloudCommonPopupConstants.COMMON_TWO_BUTTON)
                    .setTitle(getString(R.string.starry_popupwindow_title))
                    .setMessage(str)
                    .setPositiveTitle(getString(R.string.starry_drawoverlays_msg_open))
                    .setNegativeTitle(getString(R.string.starry_drawoverlays_msg_cancel))
                    .setOnPositiveListener(new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            useToolsRequestPermission(PermissionUtil.getStoragePermission());
                        }
                    })
                    .setOnNegativeListener(new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            dialog.dismiss();
                            ActivityUtils.finishActivity(SelectAlbumPhotoActivity.this);
                        }
                    })
                    .create();
            requestPermissionDialog.show();
        }

    }

    @Override
    public void onImagesLoaded(List<ImageFolder> imageFolders) {

        this.mImageFolders = imageFolders;
        imagePicker.setImageFolders(mImageFolders);
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (albumFolderAdapter != null) {
                    albumFolderAdapter.refreshData(mImageFolders);
                    hideProgressDialog();
                }
            }
        });

    }

    /**
     * @des: 初始化列表
     * @params:
     * @return:
     */

    private void initAlbumList() {
        mImageFolders = new ArrayList<>();
        albumFolderAdapter = new AlbumFolderAdapter(this, mImageFolders);
        albumFolderAdapter.setOnItemClickListener(onItemClickListener);
        albumFolderList.setAdapter(albumFolderAdapter);
        albumFolderList.setHasFixedSize(true);
        albumFolderList.setLayoutManager(new LinearLayoutManager(this));
    }


    private void registerEvent() {
        noBackTopBarCancel.setOnClickListener(this);
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.no_back_top_bar_cancel:
                ActivityUtils.finishActivity(this);
                break;
            default:
                break;
        }
    }


    private AlbumFolderAdapter.OnItemClickListener onItemClickListener = new AlbumFolderAdapter.OnItemClickListener() {
        @Override
        public void OnItemClick(int position, ImageFolder imageFolder) {
            imagePicker.setCurrentImageFolderPosition(position);
            if (null != imageFolder) {
                Intent intent = new Intent(SelectAlbumPhotoActivity.this, ImageGridActivity.class);
                EventBus.getDefault().postSticky(new ImagePickerEventModel(EventType.CLICK_IMAGE_FOLDER, imageFolder));
                startActivity(intent);
            }
        }
    };


    /**
     * @des:检查权限
     * @params:
     * @return:
     */


    public boolean checkPermission(@NonNull String permission) {
        return ActivityCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED;
    }

    @Override
    protected void onRestoreInstanceState(Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        ImagePicker.getInstance().restoreInstanceState(savedInstanceState);
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        ImagePicker.getInstance().saveInstanceState(outState);
    }

    @Override
    protected void onDestroy() {
//        imageDataSource.setCursorClosed(true);
        super.onDestroy();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (hasIntentToRequestPermission) {
            showExplainForPermission();
        }
    }
}
