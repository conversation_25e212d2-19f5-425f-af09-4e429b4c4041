package com.czur.cloud.ui.books;

import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.badoo.mobile.util.WeakHandler;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.ui.base.BaseActivity;

/**
 * Created by Yz on 2018/3/30
 * Email：<EMAIL>
 */


public class HandwritingGuideActivity extends BaseActivity implements View.OnClickListener {

    private RecyclerView gridFileList;
    private TextView handwritingConfirmBtn;
    private WeakHandler handler;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.blue_29b0d7);
        BarUtils.setStatusBarLightMode(this,false);
        setContentView(R.layout.activity_handwriting_guide);
        initComponent();
        registerEvent();
    }

    private void initComponent() {
        handler = new WeakHandler();
        handwritingConfirmBtn = (TextView) findViewById(R.id.handwriting_confirm_btn);

    }

    private void registerEvent() {
        handwritingConfirmBtn.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            case R.id.handwriting_confirm_btn:
                ActivityUtils.finishActivity(this);
                break;

            default:
                break;
        }
    }


}
