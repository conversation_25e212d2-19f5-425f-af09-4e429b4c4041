package com.czur.cloud.model;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2016/9/9.
 */
public class NotificationModel implements Serializable {


    /**
     "id":26321,
     "userId":3171,
     "notification":true,
     "useNotification":true,
     "offlineNotification":true,
     "newFileNotification":true,
     "dataCollection":false,
     "udid":"CET15A2001P00007",
     "updateTime":1611653385000

     * notification	boolean	通知开关
     * useNotification	boolean	使用通知
     * offlineNotification	boolean	离线通知
     * newFileNotification	boolean	新文件通知
     * dataCollection	boolean	用户数据收集
     */

    private int id;
    private int userId;
    private boolean notification;
    private boolean useNotification;
    private boolean offlineNotification;
    private boolean newFileNotification;
    private Object updateTime;

    //dataCollection	boolean	用户数据收集
    private boolean dataCollection;

    public boolean isDataCollection() {        return dataCollection;    }

    public void setDataCollection(boolean dataCollection) {        this.dataCollection = dataCollection;    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public boolean isNotification() {
        return notification;
    }

    public void setNotification(boolean notification) {
        this.notification = notification;
    }

    public boolean isUseNotification() {
        return useNotification;
    }

    public void setUseNotification(boolean useNotification) {
        this.useNotification = useNotification;
    }

    public boolean isOfflineNotification() {
        return offlineNotification;
    }

    public void setOfflineNotification(boolean offlineNotification) {
        this.offlineNotification = offlineNotification;
    }

    public boolean isNewFileNotification() {
        return newFileNotification;
    }

    public void setNewFileNotification(boolean newFileNotification) {
        this.newFileNotification = newFileNotification;
    }

    public Object getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Object updateTime) {
        this.updateTime = updateTime;
    }
}
