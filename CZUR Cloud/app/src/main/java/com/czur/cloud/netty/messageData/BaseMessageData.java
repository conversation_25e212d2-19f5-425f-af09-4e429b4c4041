package com.czur.cloud.netty.messageData;

import java.util.UUID;

public class BaseMessageData {

    private String uuid;
    private String message_name;


    public BaseMessageData() {
        uuid = UUID.randomUUID().toString();
    }

    public BaseMessageData(String messageName) {
        uuid = UUID.randomUUID().toString();
        this.message_name = messageName;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getMessage_name() {
        return message_name;
    }

    public void setMessage_name(String message_name) {
        this.message_name = message_name;
    }
}
