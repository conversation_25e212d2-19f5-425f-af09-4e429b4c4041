package com.czur.cloud.model;

/**
 * Created by Liu<PERSON>ue on 2016/11/16.
 */

public class UserDeviceModel  {

    private String alias;
    private String bindUserId;
    private String isBinded;
    private String inUsingUserId;
    private String isInUsing;
    private String isShared;
    private String equipmentUID;
    private String serialNumber;
    private String licenceNumber;
    private String createOn;
    private String bindOn;
    private String usingOn;
    private String offUsingOn;
    private String connectNetworkTime;
    private String isConnectedTutk;
    private String isDomestic;
    private String timeZone;
    private boolean isPublic;

    private String id;
    private String name;
    private String mobile;
    private String email;
    private String photo;
    private String photoOssKey;
    private Long usages;
    private Long usagesLimit;




    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }


    public String getPhotoOssKey() {
        return photoOssKey;
    }

    public void setPhotoOssKey(String photoOssKey) {
        this.photoOssKey = photoOssKey;
    }

    public Long getUsages() {
        return usages;
    }

    public void setUsages(Long usages) {
        this.usages = usages;
    }

    public Long getUsagesLimit() {
        return usagesLimit;
    }

    public void setUsagesLimit(Long usagesLimit) {
        this.usagesLimit = usagesLimit;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }


    public String getEquipmentUID() {
        return equipmentUID;
    }

    public void setEquipmentUID(String equipmentUID) {
        this.equipmentUID = equipmentUID;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getLicenceNumber() {
        return licenceNumber;
    }

    public void setLicenceNumber(String licenceNumber) {
        this.licenceNumber = licenceNumber;
    }



    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public String getBindUserId() {
        return bindUserId;
    }

    public void setBindUserId(String bindUserId) {
        this.bindUserId = bindUserId;
    }

    public String getIsBinded() {
        return isBinded;
    }

    public void setIsBinded(String isBinded) {
        this.isBinded = isBinded;
    }

    public String getInUsingUserId() {
        return inUsingUserId;
    }

    public void setInUsingUserId(String inUsingUserId) {
        this.inUsingUserId = inUsingUserId;
    }

    public String getIsInUsing() {
        return isInUsing;
    }

    public void setIsInUsing(String isInUsing) {
        this.isInUsing = isInUsing;
    }

    public String getIsShared() {
        return isShared;
    }

    public void setIsShared(String isShared) {
        this.isShared = isShared;
    }

    public String getCreateOn() {
        return createOn;
    }

    public void setCreateOn(String createOn) {
        this.createOn = createOn;
    }

    public String getBindOn() {
        return bindOn;
    }

    public void setBindOn(String bindOn) {
        this.bindOn = bindOn;
    }

    public String getUsingOn() {
        return usingOn;
    }

    public void setUsingOn(String usingOn) {
        this.usingOn = usingOn;
    }

    public String getOffUsingOn() {
        return offUsingOn;
    }

    public void setOffUsingOn(String offUsingOn) {
        this.offUsingOn = offUsingOn;
    }

    public String getConnectNetworkTime() {
        return connectNetworkTime;
    }

    public void setConnectNetworkTime(String connectNetworkTime) {
        this.connectNetworkTime = connectNetworkTime;
    }

    public String getIsConnectedTutk() {
        return isConnectedTutk;
    }

    public void setIsConnectedTutk(String isConnectedTutk) {
        this.isConnectedTutk = isConnectedTutk;
    }

    public String getIsDomestic() {
        return isDomestic;
    }

    public void setIsDomestic(String isDomestic) {
        this.isDomestic = isDomestic;
    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public boolean isPublic() {
        return isPublic;
    }

    public void setPublic(boolean aPublic) {
        isPublic = aPublic;
    }
}
