package com.czur.cloud.ui.auramate.reportfragment;

import android.annotation.SuppressLint;
import android.app.KeyguardManager;
import android.content.Context;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.os.PowerManager;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.annotation.Nullable;

import com.badoo.mobile.util.WeakHandler;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.czur.cloud.R;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.HdViewSaveEvent;
import com.czur.cloud.model.AuraDeviceModel;
import com.czur.cloud.netty.core.CZURTcpClient;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.auramate.AuramateBaseActivity;
import com.czur.cloud.ui.component.popup.SaveHdViewPopupDialog;
import com.czur.cloud.util.validator.Validator;
import com.davemorrissey.labs.subscaleview.ImageSource;
import com.nostra13.universalimageloader.cache.disc.naming.Md5FileNameGenerator;
import com.nostra13.universalimageloader.cache.memory.impl.UsingFreqLimitedMemoryCache;
import com.nostra13.universalimageloader.core.DisplayImageOptions;
import com.nostra13.universalimageloader.core.ImageLoader;
import com.nostra13.universalimageloader.core.ImageLoaderConfiguration;
import com.nostra13.universalimageloader.core.assist.QueueProcessingType;
import com.nostra13.universalimageloader.core.display.RoundedBitmapDisplayer;
import com.nostra13.universalimageloader.core.download.BaseImageDownloader;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Jason on 2021.1.8.
 */

public class AuraMateHDViewAlbumActivity extends AuramateBaseActivity implements View.OnClickListener {

    private String sdCardPath;
    // 创建ImageLoader对象
    private ImageLoader imageLoader = ImageLoader.getInstance();
    private GridView gv_image_loader;
    private ImageView back_btn,close_btn;
    private LinearLayout fav_btn_ll,ablum_btn_ll;

    private com.davemorrissey.labs.subscaleview.SubsamplingScaleImageView picture_iv;
    private RelativeLayout hdview_picture_rl,hdview_ablum_rl;
    private SaveHdViewPopupDialog successPopup;
    private WeakHandler handler;
    private int version;
    private String equipmentId1;
    private HdViewData locHdViewData;
    private RelativeLayout saveLoadingRl;
    private AuraDeviceModel devModel;
    private List<HdViewData> locHdViewDataList= new ArrayList<>();

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        this.getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
        super.onCreate(savedInstanceState);
        PowerManager pm = ((PowerManager) getSystemService(POWER_SERVICE));
        @SuppressLint("InvalidWakeLockTag") PowerManager.WakeLock screenLock = pm.newWakeLock(PowerManager.SCREEN_DIM_WAKE_LOCK | PowerManager.ACQUIRE_CAUSES_WAKEUP, "TAG");
        screenLock.acquire(100 * 60 * 1000L /*100 minutes*/);
        KeyguardManager km = (KeyguardManager) getSystemService(Context.KEYGUARD_SERVICE);
        final KeyguardManager.KeyguardLock kl = km.newKeyguardLock("unLock");
        kl.disableKeyguard();
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
                | WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED
                | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
                | WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD);
        WindowManager.LayoutParams params = getWindow().getAttributes();
        params.systemUiVisibility = View.SYSTEM_UI_FLAG_LOW_PROFILE;
        setStatusBarColor(R.color.transparent);
        BarUtils.setNavBarVisibility(this, false);
        ScreenUtils.setFullScreen(this);
        BarUtils.setStatusBarLightMode(this, true);

        sdCardPath = this.getExternalFilesDir("hdview").getAbsolutePath();
        equipmentId1 = getIntent().getStringExtra("equipmentId");

        DisplayImageOptions options = new DisplayImageOptions.Builder()
                .cacheInMemory(false) //设置下载的图片是否缓存在内存中
                .cacheOnDisc(true)//设置下载的图片是否缓存在SD卡中
//                .showImageOnLoading(R.mipmap.notice_image_bg)
//                .showImageOnFail(R.mipmapmap.notice_image_bg)
                .bitmapConfig(Bitmap.Config.RGB_565)
                .considerExifParams(true)//是否考虑JPEG图像EXIF参数（旋转，翻转）
                .resetViewBeforeLoading(true)// 设置图片在下载前是否重置，复位
                .build();
        ImageLoaderConfiguration config = new ImageLoaderConfiguration.Builder(this)
                .memoryCacheExtraOptions(480, 800) // max width, max height，即保存的每个缓存文件的最大长宽
                .threadPoolSize(3) //线程池内加载的数量
                .threadPriority(Thread.NORM_PRIORITY - 2)
                .denyCacheImageMultipleSizesInMemory()
                .diskCacheFileNameGenerator(new Md5FileNameGenerator()) //将保存的时候的URI名称用MD5 加密
                .memoryCache(new UsingFreqLimitedMemoryCache(2 * 1024 * 1024)) // You can pass your own memory cache implementation/你可以通过自己的内存缓存实现
                .memoryCacheSize(2 * 1024 * 1024) // 内存缓存的最大值
                .diskCacheSize(50 * 1024 * 1024)  // 50 Mb sd卡(本地)缓存的最大值
                .tasksProcessingOrder(QueueProcessingType.LIFO)
                .defaultDisplayImageOptions(options)// 由原先的discCache -> diskCache
//                .diskCache(new UnlimitedDiskCache(new File(temp_path)))//自定义缓存路径
                .imageDownloader(new BaseImageDownloader(this, 5 * 1000, 30 * 1000)) // connectTimeout (5 s), readTimeout (30 s)超时时间
                .writeDebugLogs() // Remove for release app
                .build();
        ImageLoader.getInstance().init(config);//全局初始化此配置

        // 设置该Activity使用的布局文件
        setContentView(R.layout.activity_imageloader_view);

        initView();
        initData();

        // 创建ImageLoaderBaseAdapter对象
        ImageLoaderBaseAdapter adapter = new ImageLoaderBaseAdapter(this);
        // listView设置adapter
        gv_image_loader.setAdapter(adapter);
        gv_image_loader.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {

                locHdViewData = HdViewFileUtils.hdViewDataList.get(position);
                String filePath = locHdViewData.getFull_filename();
                picture_iv.setImage(ImageSource.uri(filePath));

                showPicture(true);
            }
        });

        showPicture(false);
    }

    private void initView(){
        hdview_ablum_rl = findViewById(R.id.hdview_ablum_rl);
        hdview_picture_rl = findViewById(R.id.hdview_picture_rl);
        picture_iv = findViewById(R.id.save_show_img);
        back_btn = findViewById(R.id.hdview_btn_back);
        close_btn = findViewById(R.id.hdview_btn_close);
        fav_btn_ll = findViewById(R.id.hdview_btn_fav_ll);
        ablum_btn_ll = findViewById(R.id.hdview_btn_ablum_ll);
        back_btn.setOnClickListener(this);
        close_btn.setOnClickListener(this);
        fav_btn_ll.setOnClickListener(this);
        ablum_btn_ll.setOnClickListener(this);

        // 获取ListView控件
        gv_image_loader = (GridView) findViewById(R.id.gridView);

        SaveHdViewPopupDialog.Builder builder = new SaveHdViewPopupDialog.Builder(AuraMateHDViewAlbumActivity.this);
        successPopup = builder.create();
        handler = new WeakHandler();
        saveLoadingRl = (RelativeLayout) findViewById(R.id.save_loading_rl);

    }

    private void showPicture(boolean isShow){

        if (isShow){
            hdview_picture_rl.setVisibility(View.VISIBLE);
            hdview_ablum_rl.setVisibility(View.GONE);
            fav_btn_ll.setVisibility(View.VISIBLE);
            //判断是否显示收藏按钮
            if (Validator.isNotEmpty(locHdViewData)){
                if (locHdViewData.isFav()){
                    fav_btn_ll.setVisibility(View.INVISIBLE);
                }else {
                    fav_btn_ll.setVisibility(View.VISIBLE);
                }
            }

        }else{
            hdview_picture_rl.setVisibility(View.GONE);
            hdview_ablum_rl.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public boolean PCNeedFinish() {
        return false;
    }

    private void initData() {

        UserPreferences userPreferences = UserPreferences.getInstance(getApplicationContext());
        devModel = new AuraDeviceModel();
        List<AuraDeviceModel> devicesList = userPreferences.getAuraMateDevices();
        if (Validator.isNotEmpty(devicesList)) {
            for (AuraDeviceModel model : devicesList) {
                String equipmentId2 = model.getEquipmentUID();
                if (equipmentId2.equals(equipmentId1)) {
                    devModel = model;
                    break;
                }
            }
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.hdview_btn_close:
            case R.id.hdview_btn_back:
                ActivityUtils.finishActivity(this);
                break;
                
            case R.id.hdview_btn_ablum_ll:
                showPicture(false);
                break;

            case R.id.hdview_btn_fav_ll:
                saveLoadingRl.setVisibility(View.VISIBLE);
                version = getResources().getInteger(R.integer.message_api_version);

                if (devModel.getMessage_api_version() < version){
                    UserPreferences userPreferences = UserPreferences.getInstance(this);
                    String ownerId = devModel.getBindUserId()+"";
                    String msg = getString(R.string.hdview_fav_lower_version2);
                    if (!ownerId.equals(userPreferences.getUserId())){
                        msg = getString(R.string.hdview_fav_lower_version);
                    }
                    ToastUtils.showLong(msg);
                    return;
                }

                String osskey = locHdViewData.getOss_key();
                String ossbucket= locHdViewData.getOss_bucket();
                CZURTcpClient.getInstance().hdViewSaveV2(AuraMateHDViewAlbumActivity.this, equipmentId1, osskey, ossbucket);

                break;
            default:
                break;
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case HD_SAVE_VIEW:
                HdViewSaveEvent hdViewSaveEvent = (HdViewSaveEvent) event;
                saveLoadingRl.setVisibility(View.INVISIBLE);
                if (hdViewSaveEvent.getStatus() == 0) {
                    successPopup.show(getString(R.string.hdview_save_failed1),getString(R.string.hdview_save_failed2), 0);
                    handler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            successPopup.dismiss();
                        }
                    }, 2000);
                } else {
                    successPopup.show(getString(R.string.hdview_save_success1),getString(R.string.hdview_save_success2), 1);
                    fav_btn_ll.setVisibility(View.INVISIBLE);

                    locHdViewData.setFav(true);

                    handler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            successPopup.dismiss();
                        }
                    }, 2000);
                }
                break;

            case HD_SAVE_VIEW_V2:

            default:
                break;
        }
    }

    /**
     * 自定义ImageLoaderBaseAdapter继承BaseAdapter，重写相关方法
     */
    class ImageLoaderBaseAdapter extends BaseAdapter {

        private LayoutInflater inflater;

        public ImageLoaderBaseAdapter(Context context) {
            this.inflater = LayoutInflater.from(context);
        }

        @Override
        public int getCount() {
            return  HdViewFileUtils.hdViewDataList.size();
        }

        @Override
        public Object getItem(int position) {
            // Auto-generated method stub
            return HdViewFileUtils.hdViewDataList.get(position);
        }

        @Override
        public long getItemId(int position) {
            // Auto-generated method stub
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            // Auto-generated method stub
            ViewHolder holder = null;
            if (convertView == null) {
                convertView = inflater.inflate(R.layout.layout_imageloader_item,
                        null);
                holder = new ViewHolder();
                holder.iv_lin = (ImageView) convertView
                        .findViewById(R.id.iv_head);
                convertView.setTag(holder);
            }
            holder = (ViewHolder) convertView.getTag();
            // 创建DisplayImageOptions对象并进行相关选项配置
            DisplayImageOptions options = new DisplayImageOptions.Builder()
//                    .showImageOnLoading(R.mipmap.ic_launcher)// 设置图片下载期间显示的图片
//                    .showImageForEmptyUri(R.mipmap.ic_launcher)// 设置图片Uri为空或是错误的时候显示的图片
//                    .showImageOnFail(R.mipmap.ic_launcher)// 设置图片加载或解码过程中发生错误显示的图片
                    .cacheInMemory(true)// 设置下载的图片是否缓存在内存中
                    .cacheOnDisk(true)// 设置下载的图片是否缓存在SD卡中
                    .displayer(new RoundedBitmapDisplayer(20))// 设置成圆角图片
                    .build();// 创建DisplayImageOptions对象
            // 使用ImageLoader加载图片
            String full_path = HdViewFileUtils.hdViewDataList.get(position).getFull_filename();
            imageLoader.displayImage(full_path, holder.iv_lin, options);

            return convertView;
        }

        /**
         * 控件缓存类
         */
        class ViewHolder {
            ImageView iv_lin;
        }
    }

    @Override
    protected void onPause() {
        super.onPause();

        ActivityUtils.finishActivity(this);
    }

    @Override
    protected void onDestroy() {
        // 回收该页面缓存在内存中的图片
        imageLoader.clearMemoryCache();

        super.onDestroy();
    }

}
