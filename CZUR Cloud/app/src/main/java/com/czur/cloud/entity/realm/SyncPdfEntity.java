package com.czur.cloud.entity.realm;

import io.realm.RealmObject;

/**
 * Created by <PERSON>z on 2018/3/31
 * Email：<EMAIL>
 */


public class SyncPdfEntity extends RealmObject {

    private String pdfId;
    private String pdfName;
    private String pdfSize;
    private String pdfPath;
    private int isDelete;
    private int isDirty;
    private String syncTime;
    private String createTime;
    private String updateTime;
    private int isNewAdd;

    private boolean hasUploadPdf;

    public String getPdfPath() {
        return pdfPath;
    }

    public void setPdfPath(String pdfPath) {
        this.pdfPath = pdfPath;
    }

    public int getIsNewAdd() {
        return isNewAdd;
    }

    public void setIsNewAdd(int isNewAdd) {
        this.isNewAdd = isNewAdd;
    }

    public String getPdfId() {
        return pdfId;
    }

    public void setPdfId(String pdfId) {
        this.pdfId = pdfId;
    }

    public String getPdfName() {
        return pdfName;
    }

    public void setPdfName(String pdfName) {
        this.pdfName = pdfName;
    }

    public String getPdfSize() {
        return pdfSize;
    }

    public void setPdfSize(String pdfSize) {
        this.pdfSize = pdfSize;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }


    public String getSyncTime() {
        return syncTime;
    }

    public void setSyncTime(String syncTime) {
        this.syncTime = syncTime;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public int getIsDirty() {
        return isDirty;
    }

    public void setIsDirty(int isDirty) {
        this.isDirty = isDirty;
    }
    public boolean isHasUploadPdf() {
        return hasUploadPdf;
    }

    public void setHasUploadPdf(boolean hasUploadPdf) {
        this.hasUploadPdf = hasUploadPdf;
    }

}

