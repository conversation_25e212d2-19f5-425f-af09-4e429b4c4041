package com.czur.cloud.model;

import java.util.List;

public class AuraHomeFileModel {


    private List<FoldersBean> folders;
    private List<FilesBean> files;

    public List<FoldersBean> getFolders() {
        return folders;
    }

    public void setFolders(List<FoldersBean> folders) {
        this.folders = folders;
    }

    public List<FilesBean> getFiles() {
        return files;
    }

    public void setFiles(List<FilesBean> files) {
        this.files = files;
    }

    public static class FoldersBean {
        /**
         * id : 20rsowwc2neqk53
         * name : 新建文件夹
         * fileCounts : 5
         * latestUpdate : 1539853926000
         * seqId : 1
         * isAutoCreate : false
         */

        private String id;
        private String name;
        private int fileCounts;
        private long latestUpdate;
        private int seqId;
        private boolean isAutoCreate;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getFileCounts() {
            return fileCounts;
        }

        public void setFileCounts(int fileCounts) {
            this.fileCounts = fileCounts;
        }

        public long getLatestUpdate() {
            return latestUpdate;
        }

        public void setLatestUpdate(long latestUpdate) {
            this.latestUpdate = latestUpdate;
        }

        public int getSeqId() {
            return seqId;
        }

        public void setSeqId(int seqId) {
            this.seqId = seqId;
        }

        public boolean isIsAutoCreate() {
            return isAutoCreate;
        }

        public void setIsAutoCreate(boolean isAutoCreate) {
            this.isAutoCreate = isAutoCreate;
        }
    }

    public static class FilesBean {
        /**
         * id : sqobbyk4kn7i3zc
         * seqNum : 53
         * userId : 9
         * dirId : null
         * mode : 1
         * userSelectMode : 0
         * smartResult : 1
         * orgImgId : 92
         * orgKey : test/9/qaz-147-wsx-258/2018-10-19/d685f480-1734-4c1d-86c6-418849580e9b.jpg
         * org : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/d685f480-1734-4c1d-86c6-418849580e9b.jpg?Expires=1539920547&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=/P5m5HG1EjrPC0nUwiLLqUA2qB4=
         * middleOrg : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/d685f480-1734-4c1d-86c6-418849580e9b.jpg?Expires=1539920547&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=G7bDPg532W9vTBNJ7OM3ZU6qoNs=&x-oss-process=image/resize,m_fixed,w_1080,h_1080
         * smallOrg : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/d685f480-1734-4c1d-86c6-418849580e9b.jpg?Expires=1539920547&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=j/c+xnRE5/SzPfK7yY/kzvODAiU=&x-oss-process=image/resize,m_fixed,w_150,h_150
         * orgFileSize : 1887501
         * orgFileSizeUnit : 1.8 MB
         * fileName : 20181019094354
         * equipmentUID : qaz-147-wsx-258
         * singleId : 93
         * singleKey : test/9/qaz-147-wsx-258/2018-10-19/process/7ded6826-e3e4-4c7c-b60e-aae694abd201.jpg
         * singleFileSize : 1693212
         * singleFileSizeUnit : 1.6 MB
         * single : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/7ded6826-e3e4-4c7c-b60e-aae694abd201.jpg?Expires=1539920547&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=pNOqqiG9MKgWqRV5m//M445ZCl0=
         * smallSingle : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/7ded6826-e3e4-4c7c-b60e-aae694abd201.jpg?Expires=1539920547&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=h9uZvBMtalyfDfXR53DCBOI6zdk=&x-oss-process=image/resize,m_fixed,w_150,h_150
         * middleSingle : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/7ded6826-e3e4-4c7c-b60e-aae694abd201.jpg?Expires=1539920547&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=Ra5gWgL+HBdpgTzrYanqzNF1W18=&x-oss-process=image/resize,m_fixed,w_1080,h_1080
         * bookId : null
         * bookKey : null
         * book : null
         * middleBook : null
         * smallBook : null
         * bookFileSize : null
         * bookFileSizeUnit : null
         * takeOn : 1539913434000
         * createOn : 1539913435000
         * localeTime : null
         * small : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/7ded6826-e3e4-4c7c-b60e-aae694abd201.jpg?Expires=1539920547&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=h9uZvBMtalyfDfXR53DCBOI6zdk=&x-oss-process=image/resize,m_fixed,w_150,h_150
         * middle : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/7ded6826-e3e4-4c7c-b60e-aae694abd201.jpg?Expires=1539920547&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=Ra5gWgL+HBdpgTzrYanqzNF1W18=&x-oss-process=image/resize,m_fixed,w_1080,h_1080
         * big : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/7ded6826-e3e4-4c7c-b60e-aae694abd201.jpg?Expires=1539920547&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=pNOqqiG9MKgWqRV5m//M445ZCl0=
         * fileSize : 1693212
         * fileSizeUnit : 1.6 MB
         */

        private String id;
        private int seqNum;
        private String userId;
        private Object dirId;
        private int mode;
        private int userSelectMode;
        private int smartResult;
        private int orgImgId;
        private String orgKey;
        private String org;
        private String middleOrg;
        private String smallOrg;
        private int orgFileSize;
        private String orgFileSizeUnit;
        private String fileName;
        private String equipmentUID;
        private int singleId;
        private String singleKey;
        private int singleFileSize;
        private String singleFileSizeUnit;
        private String single;
        private String smallSingle;
        private String middleSingle;
        private String bookId;
        private String bookKey;
        private String book;
        private String middleBook;
        private String smallBook;
        private Object bookFileSize;
        private Object bookFileSizeUnit;
        private String takeOn;
        private long createOn;
        private Object localeTime;
        private String small;
        private String middle;
        private String big;
        private int fileSize;
        private String fileSizeUnit;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public int getSeqNum() {
            return seqNum;
        }

        public void setSeqNum(int seqNum) {
            this.seqNum = seqNum;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public Object getDirId() {
            return dirId;
        }

        public void setDirId(Object dirId) {
            this.dirId = dirId;
        }

        public int getMode() {
            return mode;
        }

        public void setMode(int mode) {
            this.mode = mode;
        }

        public int getUserSelectMode() {
            return userSelectMode;
        }

        public void setUserSelectMode(int userSelectMode) {
            this.userSelectMode = userSelectMode;
        }

        public int getSmartResult() {
            return smartResult;
        }

        public void setSmartResult(int smartResult) {
            this.smartResult = smartResult;
        }

        public int getOrgImgId() {
            return orgImgId;
        }

        public void setOrgImgId(int orgImgId) {
            this.orgImgId = orgImgId;
        }

        public String getOrgKey() {
            return orgKey;
        }

        public void setOrgKey(String orgKey) {
            this.orgKey = orgKey;
        }

        public String getOrg() {
            return org;
        }

        public void setOrg(String org) {
            this.org = org;
        }

        public String getMiddleOrg() {
            return middleOrg;
        }

        public void setMiddleOrg(String middleOrg) {
            this.middleOrg = middleOrg;
        }

        public String getSmallOrg() {
            return smallOrg;
        }

        public void setSmallOrg(String smallOrg) {
            this.smallOrg = smallOrg;
        }

        public int getOrgFileSize() {
            return orgFileSize;
        }

        public void setOrgFileSize(int orgFileSize) {
            this.orgFileSize = orgFileSize;
        }

        public String getOrgFileSizeUnit() {
            return orgFileSizeUnit;
        }

        public void setOrgFileSizeUnit(String orgFileSizeUnit) {
            this.orgFileSizeUnit = orgFileSizeUnit;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public String getEquipmentUID() {
            return equipmentUID;
        }

        public void setEquipmentUID(String equipmentUID) {
            this.equipmentUID = equipmentUID;
        }

        public int getSingleId() {
            return singleId;
        }

        public void setSingleId(int singleId) {
            this.singleId = singleId;
        }

        public String getSingleKey() {
            return singleKey;
        }

        public void setSingleKey(String singleKey) {
            this.singleKey = singleKey;
        }

        public int getSingleFileSize() {
            return singleFileSize;
        }

        public void setSingleFileSize(int singleFileSize) {
            this.singleFileSize = singleFileSize;
        }

        public String getSingleFileSizeUnit() {
            return singleFileSizeUnit;
        }

        public void setSingleFileSizeUnit(String singleFileSizeUnit) {
            this.singleFileSizeUnit = singleFileSizeUnit;
        }

        public String getSingle() {
            return single;
        }

        public void setSingle(String single) {
            this.single = single;
        }

        public String getSmallSingle() {
            return smallSingle;
        }

        public void setSmallSingle(String smallSingle) {
            this.smallSingle = smallSingle;
        }

        public String getMiddleSingle() {
            return middleSingle;
        }

        public void setMiddleSingle(String middleSingle) {
            this.middleSingle = middleSingle;
        }

        public String getBookId() {
            return bookId;
        }

        public void setBookId(String bookId) {
            this.bookId = bookId;
        }

        public String getBookKey() {
            return bookKey;
        }

        public void setBookKey(String bookKey) {
            this.bookKey = bookKey;
        }

        public String getBook() {
            return book;
        }

        public void setBook(String book) {
            this.book = book;
        }

        public String getMiddleBook() {
            return middleBook;
        }

        public void setMiddleBook(String middleBook) {
            this.middleBook = middleBook;
        }

        public String getSmallBook() {
            return smallBook;
        }

        public void setSmallBook(String smallBook) {
            this.smallBook = smallBook;
        }

        public Object getBookFileSize() {
            return bookFileSize;
        }

        public void setBookFileSize(Object bookFileSize) {
            this.bookFileSize = bookFileSize;
        }

        public Object getBookFileSizeUnit() {
            return bookFileSizeUnit;
        }

        public void setBookFileSizeUnit(Object bookFileSizeUnit) {
            this.bookFileSizeUnit = bookFileSizeUnit;
        }

        public String getTakeOn() {
            return takeOn;
        }

        public void setTakeOn(String takeOn) {
            this.takeOn = takeOn;
        }

        public long getCreateOn() {
            return createOn;
        }

        public void setCreateOn(long createOn) {
            this.createOn = createOn;
        }

        public Object getLocaleTime() {
            return localeTime;
        }

        public void setLocaleTime(Object localeTime) {
            this.localeTime = localeTime;
        }

        public String getSmall() {
            return small;
        }

        public void setSmall(String small) {
            this.small = small;
        }

        public String getMiddle() {
            return middle;
        }

        public void setMiddle(String middle) {
            this.middle = middle;
        }

        public String getBig() {
            return big;
        }

        public void setBig(String big) {
            this.big = big;
        }

        public int getFileSize() {
            return fileSize;
        }

        public void setFileSize(int fileSize) {
            this.fileSize = fileSize;
        }

        public String getFileSizeUnit() {
            return fileSizeUnit;
        }

        public void setFileSizeUnit(String fileSizeUnit) {
            this.fileSizeUnit = fileSizeUnit;
        }
    }
}
