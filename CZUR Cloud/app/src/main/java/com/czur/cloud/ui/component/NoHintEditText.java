package com.czur.cloud.ui.component;

import android.content.Context;
import android.os.Handler;
import android.util.AttributeSet;
import android.view.View;

import androidx.appcompat.widget.AppCompatEditText;

/**
 * Created by Yz on 2018/6/22.
 * Email：<EMAIL>
 */
public class NoHintEditText extends AppCompatEditText {

    private Context mContext;
    public NoHintEditText(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;

        // 取消掉，现在需求：edittext聚焦，hint不消失
        // Bug #18946【登录】光标定位在输入框内，但未输入内容时，UI文言被隐藏了
//        init();
    }

    private void init() {
      final  String hint=getHint().toString();
      setOnFocusChangeListener(new OnFocusChangeListener() {
          @Override
          public void onFocusChange(View v, boolean hasFocus) {
              if (hasFocus){
//                  setHint("");
                  new Handler().postDelayed(new Runnable() {
                      @Override
                      public void run() {
                          setHint("");
                      }
                  },100);
              }else {
//                  setHint(hint);
                  new Handler().postDelayed(new Runnable() {
                      @Override
                      public void run() {
                          setHint(hint);
                      }
                  },100);
              }
          }
      });
    }
}
