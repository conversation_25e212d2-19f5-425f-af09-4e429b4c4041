package com.czur.cloud.ui.starry.livedatabus;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;

import java.util.HashMap;
import java.util.Map;

/**
 *
 *
 *注册：
 LiveDataBus.get().with(StarryConstants.MEETING_RECALL).value = "name"

 * 使用：
 LiveDataBus.get()
     .with(StarryConstants.MEETING_RECALL, String::class.java)
     .observe(this) {
        logI("LiveDataBus.get().with(StarryConstants.MEETING_RECALL)=${it}")
     }

 LiveDataBus.get().with(StarryConstants.MEETING_RECALL).value = true

 LiveDataBus.get()
     .with(StarryConstants.MEETING_RECALL, Boolean::class.java)
     .observe(this) {
        logI("LiveDataBus.get().with(StarryConstants.MEETING_RECALL)=${it}")
 }

 */
public class LiveDataBus {

    private final Map<String,BusMutableLiveData<Object>> bus;

    private LiveDataBus(){
        bus = new HashMap<>();
    }
    private static class SingletonHolder{
        private static final LiveDataBus DEFAULT_BUS = new LiveDataBus();
    }

    public static LiveDataBus get(){
        return SingletonHolder.DEFAULT_BUS;
    }

    public <T> MutableLiveData<T> with(String key, Class<T> type){
        if(!bus.containsKey(key)) {
            bus.put(key, new BusMutableLiveData<>());
        }
        return (MutableLiveData<T>) bus.get(key);
    }

    public MutableLiveData<Object> with(String key){
        return with(key,Object.class);
    }

    private static class ObserverWrapper<T> implements Observer<T> {

        private Observer<T> observer;
        private boolean isFirstCall = true;

        public ObserverWrapper(Observer<T> observer) {
            this.observer = observer;
        }

        @Override
        public void onChanged(@Nullable T t) {
            if (observer != null) {
                // 跳过第一次调用，避免粘性事件
                if (isFirstCall) {
                    isFirstCall = false;
                    return;
                }
                observer.onChanged(t);
            }
        }
    }

    private static class BusMutableLiveData<T> extends SafeMutableLiveData<T> {

        private Map<Observer, Observer> observerMap = new HashMap<>();
        private boolean hasData = false;

        @Override
        public void observe(@NonNull LifecycleOwner owner, @NonNull Observer<? super T> observer) {
            // 如果已经有数据，使用包装器来避免粘性事件
            if (hasData && getValue() != null) {
                ObserverWrapper<T> wrapper = new ObserverWrapper<T>((Observer<T>) observer);
                observerMap.put(observer, wrapper);
                super.observe(owner, wrapper);
            } else {
                super.observe(owner, observer);
            }
        }

        @Override
        public void observeForever(@NonNull Observer<? super T> observer) {
            if (!observerMap.containsKey(observer)) {
                observerMap.put(observer, new ObserverWrapper(observer));
            }
            super.observeForever(observerMap.get(observer));
        }

        @Override
        public void removeObserver(@NonNull Observer<? super T> observer) {
            Observer realObserver = null;
            if (observerMap.containsKey(observer)) {
                realObserver = observerMap.remove(observer);
            } else {
                realObserver = observer;
            }
            super.removeObserver(realObserver);
        }

        @Override
        public void setValue(T value) {
            hasData = true;
            super.setValue(value);
        }

        @Override
        public void postValue(T value) {
            hasData = true;
            super.postValue(value);
        }
    }
}
