package com.czur.cloud.ui.starry.meeting.network.core;


import com.czur.cloud.ui.starry.meeting.baselib.utils.ExceptionUtilKt;
import com.czur.cloud.ui.starry.meeting.network.core.common.ResCode;

import java.util.List;

public class MiaoHttpEntity<T> {
    // 请求状态码,定义在:ResCode.kt中
    private int code;
    private String msg;
    private T body;
    private T data;
    private List<T> bodyList;
    private List<T> dataList;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getBody() {
        return body;
    }

    public void setBody(T body) {
        this.body = body;
    }

    public List<T> getBodyList() {
        return bodyList;
    }

    public void setBodyList(List<T> bodyList) {
        this.bodyList = bodyList;
    }

    ////
    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public List<T> getDataList() {
        return dataList;
    }

    public void setDataList(List<T> dataList) {
        this.dataList = dataList;
    }

    ////
    public boolean isSuccess() {
        return code == ResCode.RESULT_CODE_SUCCESS;
    }

    /**
     * 是否是网络错误
     * @return true: 网络错误
     */
    public boolean isNetError() {
        return code == ResCode.RESULT_CODE_NO_INTERNET || code == ResCode.RESULT_CODE_NO_NET_CONNECT;
    }

    /**
     * 检查一次网络错误, 如果有错误会抛出异常,否则正常返回
     * @return 返回自己
     */
    public MiaoHttpEntity<T> withCheck(){
        ExceptionUtilKt.checkAndThrowException(this);
        return this;
    }
}
