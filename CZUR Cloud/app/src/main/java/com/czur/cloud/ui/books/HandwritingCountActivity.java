package com.czur.cloud.ui.books;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.badoo.mobile.util.WeakHandler;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.KeyboardUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.R;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.HandWritingCountEvent;
import com.czur.cloud.model.HandwritingCountModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.NoHintEditText;
import com.czur.cloud.ui.component.ProgressButton;
import com.czur.cloud.util.FixKeyboardUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * Created by Yz on 2018/3/7.
 * Email：<EMAIL>
 */

public class HandwritingCountActivity extends BaseActivity implements View.OnClickListener {


    private ImageView hideKeyboardHandwritingBackBtn;
    private RelativeLayout showKeyboardHandwritingTopBar;
    private ImageView showKeyboardHandwritingBackBtn;
    private TextView showKeyboardHandwritingTitle;
    private TextView handwritingCountTv;
    private RelativeLayout hideKeyboardHandwritingTopBar;
    private TextView hideKeyboardHandwritingTitle;
    private LinearLayout handwritingKeyboardLl;
    private NoHintEditText handwritingCodeEdt;
    private ProgressButton handwritingConfirmBtn;
    private TextView handwritingAddBtn;
    private RelativeLayout handwritingCircleImgRl;
    private ImageView handwritingUnusedIcon;
    private boolean canGetCount = false;
    private UserPreferences userPreferences;
    private HttpManager httpManager;
    private WeakHandler handler;
    private long currentLoginTime;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.blue_29b0d7);
        BarUtils.setStatusBarLightMode(this, false);
        setContentView(R.layout.activity_get_handwriting_count);
        initComponent();
        registerEvent();
        getHandwritingCount();
    }

    private void initComponent() {
        EventBus.getDefault().register(this);
        handler = new WeakHandler();
        httpManager = HttpManager.getInstance();
        userPreferences = UserPreferences.getInstance(this);


        handwritingUnusedIcon = (ImageView) findViewById(R.id.handwriting_unused_icon);
        handwritingCircleImgRl = (RelativeLayout) findViewById(R.id.handwriting_count_circle_rl);
        hideKeyboardHandwritingBackBtn = (ImageView) findViewById(R.id.hide_keyboard_handwriting_back_btn);
        showKeyboardHandwritingTopBar = (RelativeLayout) findViewById(R.id.show_keyboard_handwriting_top_bar);
        showKeyboardHandwritingBackBtn = (ImageView) findViewById(R.id.show_keyboard_handwriting_back_btn);
        showKeyboardHandwritingTitle = (TextView) findViewById(R.id.show_keyboard_handwriting_title);
        hideKeyboardHandwritingTopBar = (RelativeLayout) findViewById(R.id.hide_keyboard_handwriting_top_bar);
        hideKeyboardHandwritingTitle = (TextView) findViewById(R.id.hide_keyboard_handwriting_title);
        handwritingKeyboardLl = (LinearLayout) findViewById(R.id.handwriting_keyboard_ll);
        handwritingCodeEdt = (NoHintEditText) findViewById(R.id.handwriting_code_edt);
        handwritingConfirmBtn = (ProgressButton) findViewById(R.id.handwriting_confirm_btn);
        handwritingAddBtn = (TextView) findViewById(R.id.handwriting_add_btn);
        handwritingCountTv = (TextView) findViewById(R.id.handwriting_count_tv);

        //设置标题
        showKeyboardHandwritingTitle.setText(R.string.handwriting);
        hideKeyboardHandwritingTitle.setText(R.string.handwriting);
    }

    private void registerEvent() {
        showKeyboardHandwritingBackBtn.setOnClickListener(this);
        hideKeyboardHandwritingBackBtn.setOnClickListener(this);
        handwritingConfirmBtn.setOnClickListener(this);
        handwritingAddBtn.setOnClickListener(this);
        handwritingConfirmBtn.setSelected(false);
        handwritingConfirmBtn.setClickable(false);
        handwritingCodeEdt.addTextChangedListener(textWatcher);
        setSoftInputVisibleListener();

    }

    private void setSoftInputVisibleListener() {


        FixKeyboardUtils.registerSoftInputChangedListener(this,
                new FixKeyboardUtils.OnSoftInputChangedListener() {
                    @Override
                    public void onSoftInputChanged(int height) {
                        int screenHeight = ScreenUtils.getScreenHeight();
                        boolean softInputVisible = height < (screenHeight / 4) ? false : true;

                        if (softInputVisible) {

                            int remainHeight = ScreenUtils.getScreenHeight() - height;
                            handler.post(new KeyBoardActionRunnable(View.GONE, height, remainHeight));
                        } else {
                            handler.post(new KeyBoardActionRunnable(View.VISIBLE, 0, 0));
                        }
                    }
                });

    }

    private class KeyBoardActionRunnable implements Runnable {

        private int visibility;

        private int height;
        private int remainHeight;

        public KeyBoardActionRunnable(int visibility, int height, int remainHeight) {
            this.visibility = visibility;
            this.height = height;
            this.remainHeight = remainHeight;
        }

        @Override
        public void run() {
            handwritingCircleImgRl.setVisibility(visibility);
            handwritingUnusedIcon.setVisibility(visibility);
            hideKeyboardHandwritingTopBar.setVisibility(visibility);
            if (visibility == View.GONE) {
                setStatusBarColor(R.color.gary_f9);
                BarUtils.setStatusBarLightMode(HandwritingCountActivity.this, true);
                RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) handwritingKeyboardLl.getLayoutParams();
                layoutParams.setMargins(0, 0, 0, remainHeight / 2 - SizeUtils.dp2px(218) / 2);
                handwritingKeyboardLl.setLayoutParams(layoutParams);
            } else {
                setStatusBarColor(R.color.blue_29b0d7);
                BarUtils.setStatusBarLightMode(HandwritingCountActivity.this, false);
                RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) handwritingKeyboardLl.getLayoutParams();
                layoutParams.setMargins(0, 0, 0, SizeUtils.dp2px(38));
                handwritingKeyboardLl.setLayoutParams(layoutParams);
            }
        }
    }

    /**
     * @des: 获取手写识别次数
     * @params:[]
     * @return:void
     */
    private void getHandwritingCount() {

        httpManager.request().getHandwritingCount(
                userPreferences.getUserId(), HandwritingCountModel.class, new MiaoHttpManager.CallbackNetwork<HandwritingCountModel>() {
                    @Override
                    public void onNoNetwork() {
                        hideProgressDialog();
                        handwritingCountTv.setText(userPreferences.getHandwritingCount());
                    }

                    @Override
                    public void onStart() {
                        showProgressDialog();
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<HandwritingCountModel> entity) {
                        hideProgressDialog();
                        handwritingCountTv.setText(entity.getBody().getOcrNum());
                        userPreferences.setHandwritingCount(entity.getBody().getOcrNum());

                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<HandwritingCountModel> entity) {
                        hideProgressDialog();
                        if (entity.getCode() == MiaoHttpManager.STATUS_FAIL) {
                            showMessage(R.string.toast_internal_error);
                        } else {
                            showMessage(R.string.request_failed_alert);
                        }

                    }

                    @Override
                    public void onError(Exception e) {
                        hideProgressDialog();
                        showMessage(R.string.request_failed_alert);
                    }
                });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(HandWritingCountEvent event) {
        switch (event.getEventType()) {
            case HANDWRITING_COUNT_REDUCE:
            case HANDWRITING_COUNT_ADD:
                getHandwritingCount();
                break;
            default:
                break;
        }
    }

    /**
     * @des: 手写识别充值
     * @params:[]
     * @return:void
     */
    private void handwritingRecharge() {
        currentLoginTime = System.currentTimeMillis();
        KeyboardUtils.hideSoftInput(this);
        httpManager.request().handwritingRecharge(
                userPreferences.getUserId(), handwritingCodeEdt.getText().toString(), HandwritingCountModel.class, new MiaoHttpManager.Callback<HandwritingCountModel>() {
                    @Override
                    public void onStart() {
                        handwritingConfirmBtn.startDelayLoading(HandwritingCountActivity.this);
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<HandwritingCountModel> entity) {
//                        mobClickEvent(HandwritingCountActivity.this,BuildConfig.PHASE.getOcrInputCount());
                        successDelay();

                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<HandwritingCountModel> entity) {
                        if (entity.getCode() == MiaoHttpManager.STATUS_BOOK_CODE_INCORRECT) {
                            failedDelay(R.string.handwriting_error_three_times);
                        } else if (entity.getCode() == MiaoHttpManager.STATUS_FAIL) {
                            failedDelay(R.string.toast_internal_error);
                        } else if (entity.getCode() == MiaoHttpManager.STATUS_BOOK_OCR_HAS_BEEN_USED) {
                            failedDelay(R.string.handwriting_code_has_been_used);
                        } else if (entity.getCode() == MiaoHttpManager.STATUS_BOOK_OCR_INVALID) {
                            failedDelay(R.string.invalid_handwriting_code);
                        } else {
                            failedDelay(R.string.request_failed_alert);
                        }

                    }

                    @Override
                    public void onError(Exception e) {
                        failedDelay(R.string.request_failed_alert);
                    }
                });
    }

    private void failedDelay(final int failedText) {

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    long sleepTime;
                    if (System.currentTimeMillis() - currentLoginTime < 1000) {
                        sleepTime = 1000 - (System.currentTimeMillis() - currentLoginTime);
                    } else {
                        sleepTime = 1;
                    }
                    Thread.sleep(sleepTime);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            showMessage(failedText);
                            handwritingConfirmBtn.stopLoading();
                        }
                    });
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }


    private void successDelay() {

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    long sleepTime;
                    if (System.currentTimeMillis() - currentLoginTime < 1000) {
                        sleepTime = 1000 - (System.currentTimeMillis() - currentLoginTime);
                    } else {
                        sleepTime = 1;
                    }
                    Thread.sleep(sleepTime);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            EventBus.getDefault().post(new HandWritingCountEvent(EventType.HANDWRITING_COUNT_ADD));
                            handwritingConfirmBtn.stopLoading();
                        }
                    });
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.handwriting_add_btn:
                ActivityUtils.startActivity(HandwritingGuideActivity.class);
                break;
            case R.id.handwriting_confirm_btn:
                handwritingRecharge();
                break;
            case R.id.hide_keyboard_handwriting_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.show_keyboard_handwriting_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            default:
                break;
        }
    }


    private TextWatcher textWatcher = new TextWatcher() {


        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            // 输入的内容变化的监听
            if (s.length() > 0) {
                canGetCount = true;
            } else {
                canGetCount = false;
            }


        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count,
                                      int after) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            // 输入后的监听
            if (s.length() > 0) {
                canGetCount = true;
            } else {
                canGetCount = false;
            }
            checkNextStepButtonToClick();
        }
    };


    /**
     * @des: 检查确定是否可以点击
     * @params:
     * @return:
     */

    private void checkNextStepButtonToClick() {

        if (canGetCount) {
            handwritingConfirmBtn.setSelected(true);
            handwritingConfirmBtn.setClickable(true);
        } else {
            handwritingConfirmBtn.setSelected(false);
            handwritingConfirmBtn.setClickable(false);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        FixKeyboardUtils.unregisterSoftInputChangedListener(this);
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }
}
