package com.czur.cloud.ui.books.sync;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;

import androidx.annotation.Nullable;

public class BaseService extends Service {
    public static final String TAG = BaseService.class.getSimpleName() + ":";
    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
//        Log.i(TAG, getClass().getSimpleName() + ":Service--onBind");
        return null;
    }

    @Override
    public void onCreate() {
//        Log.i(TAG, getClass().getSimpleName() + ":Service--onCreate");
        super.onCreate();
    }

    @Override
    public void onStart(Intent intent, int startId) {
//        Log.i(TAG, getClass().getSimpleName() + ":Service--onStart");
        super.onStart(intent, startId);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
//        Log.i(TAG, getClass().getSimpleName() + ":Service--onStartCommand");
        return super.onStartCommand(intent, flags, startId);

    }

    @Override
    public void onDestroy() {
//        Log.i(TAG, getClass().getSimpleName() + ":Service--onDestroy");
        super.onDestroy();
    }

    @Override
    public boolean onUnbind(Intent intent) {
//        Log.i(TAG, getClass().getSimpleName() + ":Service--onUnbind");
        return super.onUnbind(intent);
    }

    @Override
    public void onRebind(Intent intent) {
//        Log.i(TAG, getClass().getSimpleName() + ":Service--onRebind");
        super.onRebind(intent);
    }
}
