package com.czur.cloud.netty.observer;

import static com.czur.czurutils.log.CZURLogUtilsKt.logI;
import static com.czur.czurutils.log.CZURLogUtilsKt.logTagD;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.blankj.utilcode.util.ThreadUtils;
import com.blankj.utilcode.util.Utils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.entity.realm.MessageEntity;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.VideoCameraEvent;
import com.czur.cloud.event.aurahome.ATCheckDeviceIsOnlineEvent;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.books.sync.BaseService;
import com.czur.cloud.ui.starry.livedatabus.BlankActivityForNotification;

import org.greenrobot.eventbus.EventBus;

import io.realm.Realm;
import io.realm.RealmResults;

//检测主动超时服务
public class CheckTimeOutService extends BaseService {
    public static final String TAG = CheckTimeOutService.class.getName();
    private Handler handler = new Handler();
    private Runnable task;

    @Override
    public void onCreate() {
        super.onCreate();
        initTask();
        handler.post(task);
        initNotification();
    }

    private void initTask() {
        task = new Runnable() {
            public void run() {
                Context context = Utils.getApp().getApplicationContext();
                UserPreferences instance = UserPreferences.getInstance(context);
                if (!instance.isHasAuraMate() || !instance.isUserLogin()) {
                    handler.removeCallbacksAndMessages(null);
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        stopForeground(true);
                    } else {
                        stopSelf();
                    }
                    return;
                }

                ThreadUtils.executeByCached(new ThreadUtils.SimpleTask<Void>() {

                    @Override
                    public Void doInBackground() throws Throwable {
                        try (Realm realm = Realm.getDefaultInstance()) {
                            long localTime = System.currentTimeMillis();
                            realm.executeTransaction(new Realm.Transaction() {
                                @Override
                                public void execute(Realm realm) {
                                    RealmResults<MessageEntity> messageEntities = realm.where(MessageEntity.class)
                                            .notEqualTo("status", 4)
                                            .equalTo("type", 0)
                                            .equalTo("needReceipt", true)
                                            .findAll();
                                    for (MessageEntity messageEntity : messageEntities) {
                                        //消息主动超时检测
//                                        if (localTime - messageEntity.getCreateTime() > 8000) {
                                        if (localTime - messageEntity.getCreateTime() > BuildConfig.CLIENT_TIMEOUT) {
                                            logI(messageEntity.getName() + "超时了！！！");
                                            timeOutToReset(messageEntity);
                                            messageEntity.setStatus(4);
                                        }
                                    }
                                    RealmResults<MessageEntity> oldMessages = realm.where(MessageEntity.class)
                                            .lessThan("createTime", localTime - 3600 * 1000)
                                            .findAll();
                                    oldMessages.deleteAllFromRealm();
                                }
                            });

                            if (Realm.getDefaultConfiguration() != null) {
                                Realm.compactRealm(Realm.getDefaultConfiguration());
                            }
                        }
                        return null;
                    }

                    @Override
                    public void onSuccess(Void result) {
                        handler.postDelayed(task, 3000);
                    }
                });

                new Thread(new Runnable() {
                    @Override
                    public void run() {

                    }
                }).start();

            }
        };
    }


    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        handler.removeCallbacksAndMessages(null);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            stopForeground(true);
        } else {
            stopSelf();
        }
    }


    //主动请求超时回调
    private void timeOutToReset(MessageEntity entity) {
        switch (entity.getName()) {
            case CZURConstants.CHECK_DEVICE_IS_ONLINE:
                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.CHECK_DEVICE_IS_ONLINE,
                        entity.getDeviceUDID(), entity.getDataBegin()));
                break;
            case CZURConstants.LIGHT_SWITCH:
                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.LIGHT_SWITCH,
                        entity.getDeviceUDID(), entity.getDataBegin()));
                break;
            case CZURConstants.LIGHT_LEVEL:
                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.LIGHT_LEVEL,
                        entity.getDeviceUDID(), entity.getDataBegin()));
                break;
            case CZURConstants.LIGHT_MODE:
                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.LIGHT_MODE,
                        entity.getDeviceUDID(), entity.getDataBegin()));
                break;
            case CZURConstants.SP_SWITCH:
                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.SP_SWITCH,
                        entity.getDeviceUDID(), entity.getDataBegin()));
                break;
            case CZURConstants.SP_LEVEL:
                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.SP_LEVEL,
                        entity.getDeviceUDID(), entity.getDataBegin()));
                break;
            case CZURConstants.SP_VOLUME:
                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.SP_VOLUME,
                        entity.getDeviceUDID(), entity.getDataBegin()));
                break;
            case CZURConstants.CHANGE_LANGUAGE:
                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.SYSTEM_LANGUAGE,
                        entity.getDeviceUDID(), entity.getDataBegin()));
                break;
            case CZURConstants.SMART_POWER_SAVING:
                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.SMART_POWER,
                        entity.getDeviceUDID(), entity.getDataBegin()));
                break;
            case CZURConstants.SEDENTARY_REMINDER_SWITCH:
                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.SEDENTARY_REMINDER_SWITCH,
                        entity.getDeviceUDID(), entity.getDataBegin()));
                break;
            case CZURConstants.SEDENTARY_REMINDER_DURATION:
                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.SEDENTARY_REMINDER_DURATION,
                        entity.getDeviceUDID(), entity.getDataBegin()));
                break;
            case CZURConstants.SWITCH_CAMERA:
                EventBus.getDefault().post(new VideoCameraEvent(EventType.VIDEO_CAMERA_SWITCH,
                        entity.getDeviceUDID(), entity.getDataBegin()));
                break;
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        initNotification();
        return START_STICKY;
    }


    private void initNotification() {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            // 提高进程优先级 ，就会在通知栏中出现自己的应用，如果不想提高优先级，可以把这个注释
            // 参数1：id 参数2：通知
            int messageId = 1;
            Intent intent = new Intent(getApplicationContext(), BlankActivityForNotification.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//            PendingIntent contentIntent = PendingIntent.getActivity(getApplicationContext(), messageId, intent, PendingIntent.FLAG_UPDATE_CURRENT);
            PendingIntent contentIntent;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S){
                contentIntent = PendingIntent.getActivity(getApplicationContext(), messageId, intent, PendingIntent.FLAG_IMMUTABLE);
            }else {
                contentIntent = PendingIntent.getActivity(getApplicationContext(), messageId, intent, PendingIntent.FLAG_UPDATE_CURRENT);
            }
            String channelId = "com.czur.cloud";
            String channelName = getString(R.string.background);
            NotificationChannel notificationChannel = null;
            notificationChannel = new NotificationChannel(channelId,
                    channelName, NotificationManager.IMPORTANCE_LOW);
            notificationChannel.setShowBadge(false);
            notificationChannel.enableVibration(false);
            notificationChannel.setSound(null, null);
            notificationChannel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
            NotificationManager manager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            manager.createNotificationChannel(notificationChannel);
            Notification notification = new NotificationCompat.Builder(this)
                    .setChannelId(channelId)
                    .setContentTitle(getString(R.string.aura_mate_title))
                    .setCategory(Notification.CATEGORY_CALL)
                    .setWhen(System.currentTimeMillis())
                    .setSmallIcon(R.mipmap.small_icon)
                    .setContentIntent(contentIntent)
                    .build();
            startForeground(messageId, notification);
        }
    }
}
