package com.czur.cloud.ui.starry.livedatabus

import android.content.Intent
import android.os.Bundle
import com.blankj.utilcode.util.ActivityUtils
import com.czur.cloud.R
import com.czur.cloud.event.EventType
import com.czur.cloud.event.StarryCommonEvent
import com.czur.cloud.preferences.UserPreferences
import com.czur.cloud.ui.base.BaseActivity
import com.czur.cloud.ui.base.CzurCloudApplication
import com.czur.cloud.ui.eshare.EShareActivity
import com.czur.cloud.ui.eshare.EShareEmptyActivity
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.czurutils.log.logI
import com.czur.czurutils.log.logTagD
import org.greenrobot.eventbus.EventBus


/**
 * 跳板activity
 */
class ShortCutTrampolineActivity : BaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.starry_activity_blank)

//        val userPreferences = UserPreferences.getInstance(this);
//        if (userPreferences.isUserLogin) {
//            HttpManager.getInstance().requestStarry().getStarryUserInfo(
//                userPreferences.userId,
//                UserInfoStarryModel::class.java,
//                object : MiaoHttpManager.Callback<UserInfoStarryModel?> {
//                    override fun onStart() {
//                        //logTagD("songs", "Onstart")// 只有onstart没有返回值,说明是打开之前是登录状态,打开之后是单点登录,被挤掉了
//                        launch {
//                            delay(1500)
//                            finish()
//                        }
//                    }
//
//                    override fun onResponse(entity: MiaoHttpEntity<UserInfoStarryModel?>?) {
//                        // 有返回值 说明是正常登录状态
//                        normalFun()
//                    }
//
//                    override fun onFailure(entity: MiaoHttpEntity<UserInfoStarryModel?>?) {
//                    }
//
//                    override fun onError(e: java.lang.Exception) {
//                    }
//
//                })
//        } else {
        normalFun()
//        }


    }


    // 正常逻辑
    fun normalFun() {
        val type = intent.getStringExtra(StarryConstants.ESHARE_EMPTY_TYPE)
            ?: StarryConstants.ESHARE_EMPTY_TYPE_NORMAL
        var isMeeting = CzurCloudApplication.isJoinedMeeting
        var isEshare = EShareActivity.eshareIsRunning

        if (type == StarryConstants.STARRY_EMPTY_TYPE_JOIN) {//加入会议
            if (UserPreferences.getInstance().isUserLogin) {
                if (CzurCloudApplication.isJoinedMeeting) {
                    isMeeting = true
                }
            }
        }
        logTagD("song", "isMeeting = $isMeeting isEshare = $isEshare  type = $type")
        // 已知问题
        // 小组件扫码投屏--小组件加入会议  断开投屏,弹出会议号弹窗
        // 小组件加入会议--小组件扫码投屏  结束会议,弹出扫一扫
        // 小组件扫码投屏--小组件扫码投屏  断开投屏,弹出扫一扫
        // 小组件加入会议--小组件加入会议  会议结束,弹出会议号弹窗

        if (!isMeeting && !isEshare) {
            // 除了正在开会,正在投屏
            // 其他的都不管,全部杀死app重新来,很多情况跳转不了EShareEmptyActivity
            ActivityUtils.finishAllActivitiesExceptNewest()
        }

        if (isEshare && isMeeting) {
            if (type == StarryConstants.ESHARE_EMPTY_TYPE_SCAN) {
                // 会议中,投屏中,并且是小组件扫一扫
                EventBus.getDefault()
                    .postSticky(StarryCommonEvent(EventType.ESHARE_AND_MEETING_ESHARE, ""))
            } else if (type == StarryConstants.STARRY_EMPTY_TYPE_JOIN) {
                EventBus.getDefault()
                    .postSticky(StarryCommonEvent(EventType.ESHARE_AND_MEETING_MEETING, ""))
            }
        } else if (isEshare && type == StarryConstants.STARRY_EMPTY_TYPE_JOIN) {
            // 加入会议的一种特殊情况, 现在是通过小组件进入的扫一扫投屏 ,正在投屏界面,然后用小组件进入加入会议,跳转EShareEmptyActivity会失效
            // 猜测是因为不同栈跳转的原因, 但是具体原因不详
            // 采用这种方式发送到EShareEmptyActivity所在栈进行跳转,就没问题
            logI("发送 ESHARE_JOIN_MEETING")
            EventBus.getDefault()
                .postSticky(StarryCommonEvent(EventType.ESHARE_SCANED_JOIN_MEETING, ""))
        } else if (isMeeting && type == StarryConstants.ESHARE_EMPTY_TYPE_SCAN) {
            EventBus.getDefault()
                .postSticky(StarryCommonEvent(EventType.ESHARE_JOINED_MEETING_SCAN, ""))
        } else {
            val intent1 =
                Intent(this@ShortCutTrampolineActivity, EShareEmptyActivity::class.java)
            intent1.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            intent1.putExtra(StarryConstants.ESHARE_EMPTY_TYPE, type)
            startActivity(intent1)
        }

        finish()

    }
}