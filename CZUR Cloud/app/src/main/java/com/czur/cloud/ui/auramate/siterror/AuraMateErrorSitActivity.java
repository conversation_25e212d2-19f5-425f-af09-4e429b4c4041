package com.czur.cloud.ui.auramate.siterror;

import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.czur.cloud.R;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.DeleteFilesEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.model.AuraErrorSitPictureModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.auramate.AuramateBaseActivity;
import com.czur.cloud.ui.auramate.reportfragment.ReportUtil;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.util.EtUtils;
import com.czur.cloud.util.validator.Validator;
import com.google.gson.reflect.TypeToken;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnLoadMoreListener;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Jason on 2021/01/18.
 */

@SuppressWarnings("AlibabaAvoidManuallyCreateThread")
public class AuraMateErrorSitActivity extends AuramateBaseActivity implements View.OnClickListener {

    private AuraErrorSitAdapter auraFilesAdapter;
    private LinkedHashMap<String, String> isCheckedMap;
    private UserPreferences userPreferences;
    private HttpManager httpManager;
    private SimpleDateFormat formatter;
    private ImageView etFilesBackBtn;
    private TextView etFilesSelectAllBtn;
    private TextView etFilesNoTitleTv;
    private TextView etFilesTitleTv;
    private TextView etFilesCancelBtn;
    private RelativeLayout etFilesUnselectedTopBarRl;
    private RelativeLayout etFilesMultiSelectBtn;
    private RecyclerView recyclerView;
    private LinearLayout etFilesBottomLl;
    private RelativeLayout etFilesDeleteRl;
    private LinearLayout emptyLayout;
    private SmartRefreshLayout refreshLayout;
    private TextView noteTitle, alertTitle; //开启显示的提示信息，未开启的提示信息
    private boolean isShowWrongPicture = true; //是否开启显示错误坐姿图像
    private boolean isShowErrorPicture = false;  //3天内显示图片，否则不显示
    private String showErrorPictureDate="";     //获取该日的错误坐姿图片
    private String showErrorPictureRelationId="";     //relationId

    private List<AuraErrorSitPictureModel> filesBeans;
    private List<AuraErrorSitPictureModel> addFilesBeans;

    private List<String> fileIds;

    List<AuraErrorSitPictureModel> refreshBeans;
    private String seqNum;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_aura_errorsit);

        isShowErrorPicture = getIntent().getBooleanExtra("isShowErrorPicture", false);
        equipmentId = getIntent().getStringExtra("equipmentId");
        showErrorPictureRelationId = getIntent().getStringExtra("showErrorPictureRelationId");
        showErrorPictureDate = getIntent().getStringExtra("showErrorPictureDate");
        if (Validator.isEmpty(showErrorPictureDate) || showErrorPictureDate.equals("")){
            showErrorPictureDate = ReportUtil.getNowDay("yyyy-MM-dd");
        }

        // Test Only
//        equipmentId = "CET15A2001P00007";
//        showErrorPictureDate = "2021-01-29";

        logI("AuraMateErrorSitActivity.isShowErrorPicture=" + isShowErrorPicture,
                "equipmentId="+equipmentId,
                "showErrorPictureDate="+showErrorPictureDate,
                "showErrorPictureRelationId="+showErrorPictureRelationId);

        initComponent();
        initEtFilesRecyclerView();
        registerEvent();

        isShowWrongPicture = userPreferences.isErrorsitAll();

        showWrongPictureUI();


        if (isShowWrongPicture && isShowErrorPicture) {
            getRefreshList();
        }else {
            refreshBeans = null;
            isShowEmptyPrompt();
        }
    }

    @Override
    public boolean PCNeedFinish() {
        return false;
    }


    private void initComponent() {
        userPreferences = UserPreferences.getInstance(this);
        httpManager = HttpManager.getInstance();
        formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        etFilesBackBtn = (ImageView) findViewById(R.id.et_files_back_btn);
        etFilesSelectAllBtn = (TextView) findViewById(R.id.et_files_select_all_btn);
        etFilesNoTitleTv = (TextView) findViewById(R.id.et_files_no_title_tv);
        etFilesTitleTv = (TextView) findViewById(R.id.et_files_title_tv);
        etFilesCancelBtn = (TextView) findViewById(R.id.et_files_cancel_btn);
        etFilesUnselectedTopBarRl = (RelativeLayout) findViewById(R.id.et_files_unselected_top_bar_rl);
        etFilesMultiSelectBtn = (RelativeLayout) findViewById(R.id.et_files_multi_select_btn);

        recyclerView = (RecyclerView) findViewById(R.id.recycler_view);
        refreshLayout = findViewById(R.id.refresh_layout);
        refreshLayout.setEnableOverScrollDrag(false);
        refreshLayout.setEnableOverScrollBounce(false);
        refreshLayout.setEnableAutoLoadMore(true);
        refreshLayout.setEnableRefresh(true);
        refreshLayout.setEnableNestedScroll(false);
        refreshLayout.setEnableFooterFollowWhenNoMoreData(true);
        refreshLayout.setEnableLoadMoreWhenContentNotFull(false);
        refreshLayout.setEnableLoadMore(true);
        refreshLayout.setOnLoadMoreListener(new OnLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                recyclerView.stopScroll();
                loadMore();
            }
        });
        refreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                resetToFresh();
                getRefreshList();
            }
        });
        etFilesBottomLl = (LinearLayout) findViewById(R.id.et_folder_bottom_ll);
        etFilesBottomLl.setVisibility(View.GONE);
        etFilesDeleteRl = (RelativeLayout) findViewById(R.id.et_folder_delete_rl);
        emptyLayout = findViewById(R.id.ll_empty);
        etFilesNoTitleTv.setText("错误坐姿图像");
//        etFilesNoTitleTv.setSelected(true);
        etFilesNoTitleTv.setVisibility(View.VISIBLE);

        noteTitle = findViewById(R.id.et_note_title_tv);
        alertTitle = findViewById(R.id.et_alert_title_tv);

    }

    private void showWrongPictureUI(){

        int flag = View.INVISIBLE;
        int flag1 = View.INVISIBLE;
        if (isShowWrongPicture){
            flag = View.VISIBLE;
        }else{
            flag1 = View.VISIBLE;
        }
        noteTitle.setVisibility(flag);
        alertTitle.setVisibility(flag1);
    }
    /**
     * @des: 是否显示空文件夹提示区域
     * @params:
     * @return:
     */

    private void isShowEmptyPrompt() {
        if (filesBeans != null && filesBeans.size() > 0) {
            recyclerView.setVisibility(View.VISIBLE);
            emptyLayout.setVisibility(View.GONE);
        } else {
            recyclerView.setVisibility(View.GONE);
            emptyLayout.setVisibility(View.VISIBLE);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
    }

    /**
     * @des: 初始化列表
     * @params:
     * @return:
     */
    private void initEtFilesRecyclerView() {
        fileIds = new ArrayList<>();
        filesBeans = new ArrayList<>();
        addFilesBeans = new ArrayList<>();
        isCheckedMap = new LinkedHashMap<>();
        auraFilesAdapter = new AuraErrorSitAdapter(this, filesBeans, false);
        auraFilesAdapter.setOnItemCheckListener(onItemCheckListener);
        auraFilesAdapter.setOnEtFilesClickListener(onItemClickListener);
        recyclerView.setHasFixedSize(true);
        recyclerView.setLayoutManager(new GridLayoutManager(this, 3));
        recyclerView.setAdapter(auraFilesAdapter);
    }

    private void registerEvent() {
        etFilesSelectAllBtn.setOnClickListener(this);
        etFilesCancelBtn.setOnClickListener(this);
        etFilesMultiSelectBtn.setOnClickListener(this);
        etFilesDeleteRl.setOnClickListener(this);
        etFilesBackBtn.setOnClickListener(this);
        setNetListener();
    }

    /**
     * @des: 下拉加载
     * params:
     * @return:
     */
    private void loadMore() {
        if (!isShowWrongPicture || !isShowErrorPicture) {
            return;
        }

        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                addFilesBeans = getFilesLoadMore(seqNum);
                if (addFilesBeans == null) {
                    return null;
                }
                if (Validator.isNotEmpty(addFilesBeans)) {
                    filesBeans.addAll(addFilesBeans);
                    getSeqNum(addFilesBeans);
                }
                return null;
            }

            @Override
            public void onSuccess(Void result) {
                checkSize(isCheckedMap, auraFilesAdapter.getTotalSize());
                if (addFilesBeans == null) {
                    refreshLayout.finishLoadMore(false);
                } else if (Validator.isEmpty(addFilesBeans)) {
                    refreshLayout.finishLoadMoreWithNoMoreData();
                } else {
                    auraFilesAdapter.refreshData(filesBeans);
                    refreshLayout.finishLoadMore(true);
                }
            }
        });
    }

    /**
     * @des: 刷新列表
     * @params:
     * @return:
     */
    public void getRefreshList() {
        if (!isShowWrongPicture || !isShowErrorPicture) {
            return;
        }

        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                refreshBeans = getEtFiles();

                if (Validator.isNotEmpty(refreshBeans)) {
                    filesBeans.addAll(refreshBeans);
                    getSeqNum(refreshBeans);
                }
                return null;
            }

            @Override
            public void onSuccess(Void result) {
                if (refreshBeans == null) {
                    refreshLayout.finishRefresh(false);
                } else if (!Validator.isNotEmpty(refreshBeans)) {
                    refreshLayout.finishRefresh();
                } else {
                    refreshLayout.finishRefresh();
                }
                isShowEmptyPrompt();
                refreshFiles();
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                if (!NetworkUtils.isConnected()) {
                    showMessage(R.string.toast_no_connection_network);
                }
                refreshLayout.finishRefresh(false);
            }
        });
    }

    /**
     * @des: 重置选中状态并且刷新
     * @params:
     * @return:
     */
    private void refreshFiles() {
        isMultiSelect = false;
        isSelectAll = false;
        hideSelectTopBar();
        auraFilesAdapter.refreshData(filesBeans, isMultiSelect, isCheckedMap);
    }

    /**
     * @des: 重置准备刷新
     * @params:
     * @return:
     */

    private void resetToFresh() {
        refreshLayout.resetNoMoreData();
        refreshLayout.closeHeaderOrFooter();
        filesBeans = new ArrayList<>();
        fileIds = new ArrayList<>();
        isCheckedMap.clear();
        isCheckedMap = new LinkedHashMap<>();
    }

    private void resetCheckList() {
        fileIds = new ArrayList<>();
        isCheckedMap.clear();
        isCheckedMap = new LinkedHashMap<>();
        etFilesTitleTv.setText(String.format(getString(R.string.select_num_et), isCheckedMap.size() + ""));
    }

    /**
     * @des: 图片列表接口
     * @params:
     * @return:
     */
    private List<AuraErrorSitPictureModel> getEtFiles() {
        try {
            final MiaoHttpEntity<AuraErrorSitPictureModel> etFileEntity =
                    httpManager.request().getAuraErrorSitPictureAfter(
                            userPreferences.getUserId(),
                            equipmentId,
                            showErrorPictureDate,
                            "",
                            showErrorPictureRelationId,
                            new TypeToken<List<AuraErrorSitPictureModel>>() {}.getType());
            if (etFileEntity == null) {
                return null;
            } else if (etFileEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return etFileEntity.getBodyList();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * @des: 获得下拉加载ID
     * @params:
     * @return:
     */
    private void getSeqNum(List<AuraErrorSitPictureModel> seqNumBeans) {
        if (seqNumBeans.size() > 0) {
            seqNum = seqNumBeans.get(seqNumBeans.size() - 1).getId() + "";
        }
    }

    /**
     * @des: 下拉加载接口
     * @params:
     * @return:
     */
    private List<AuraErrorSitPictureModel> getFilesLoadMore(String seqNum) {
        try {
            final MiaoHttpEntity<AuraErrorSitPictureModel> etFileEntity =
                    httpManager.request().getAuraErrorSitPictureAfter(
                            userPreferences.getUserId(),
                            equipmentId,
                            showErrorPictureDate,
                            seqNum,
                            showErrorPictureRelationId,
                            new TypeToken<List<AuraErrorSitPictureModel>>() {}.getType());
            if (etFileEntity == null) {
                return null;
            } else if (etFileEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return etFileEntity.getBodyList();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * @des: Item选中监听
     * @params:
     * @return:
     */
    private AuraErrorSitAdapter.OnItemCheckListener onItemCheckListener = new AuraErrorSitAdapter.OnItemCheckListener() {
        @Override
        public void onItemCheck(int position, AuraErrorSitPictureModel filesBean, LinkedHashMap<String, String> isCheckedMap, int totalSize) {
            AuraMateErrorSitActivity.this.isCheckedMap = isCheckedMap;
            checkSize(isCheckedMap, totalSize);
        }

    };
    /**
     * @des: Item点击监听
     * @params:
     * @return:
     */
    private AuraErrorSitAdapter.OnEtFilesClickListener onItemClickListener = new AuraErrorSitAdapter.OnEtFilesClickListener() {

        @Override
        public void onEtFilesClick(AuraErrorSitPictureModel filesBean, int position, CheckBox checkBox) {
            if (isMultiSelect) {
                checkBox.setChecked(!checkBox.isChecked());
            } else {
                Intent intent = new Intent(AuraMateErrorSitActivity.this, AuraMateSitPreviewActivity.class);
                intent.putExtra("equipmentId", equipmentId);
                intent.putExtra("showErrorPictureDate", showErrorPictureDate);
                intent.putExtra("showErrorPictureRelationId", showErrorPictureRelationId);
                intent.putExtra("seqNum", filesBean.getId() + "");
                Date date = new Date(Long.parseLong(filesBean.getCreateTime()));
                intent.putExtra("date", formatter.format(date));
                ActivityUtils.startActivity(intent);
            }
        }
    };

    /**
     * @des: 检查选中个数
     * @params:
     * @return:
     */

    private void checkSize(LinkedHashMap<String, String> isCheckedMap, int totalSize) {
        judgeToShowBottom(isCheckedMap);
        if (isCheckedMap.size() == 1) {
            etFilesTitleTv.setText(R.string.select_one_et);
            etFilesBottomLl.setVisibility(View.VISIBLE);
        } else if (isCheckedMap.size() > 1) {
            etFilesTitleTv.setText(String.format(getString(R.string.select_num_et), isCheckedMap.size() + ""));
            etFilesBottomLl.setVisibility(View.VISIBLE);
        } else {
            if (isMultiSelect) {
                etFilesTitleTv.setText(String.format(getString(R.string.select_num_et), isCheckedMap.size() + ""));
                etFilesBottomLl.setVisibility(View.GONE);
            }
        }
        checkSelectAll(isCheckedMap);
    }

    /**
     * @des: 根据数量显示bottom
     * @params:
     * @return:
     */

    private void judgeToShowBottom(LinkedHashMap<String, String> isCheckedMap) {
        fileIds = new ArrayList<>();
        for (Map.Entry<String, String> stringStringEntry : isCheckedMap.entrySet()) {
            fileIds.add(stringStringEntry.getKey());
        }

    }

    private void checkSelectAll(LinkedHashMap<String, String> isCheckedMap) {
        //如果选择不是全部Item  text变为取消全选
        if (isCheckedMap.size() < auraFilesAdapter.getTotalSize()) {
            etFilesSelectAllBtn.setText(R.string.select_all);
            isSelectAll = false;
        } else {
            etFilesSelectAllBtn.setText(R.string.not_select_all);
            isSelectAll = true;
        }
    }

    private boolean isMultiSelect = false;
    private boolean isSelectAll = false;

    /**
     * @des: 多选
     * @params:
     * @return:
     */

    private void multiSelect() {
        if (Validator.isNotEmpty(filesBeans)) {
            isMultiSelect = !isMultiSelect;
            auraFilesAdapter.refreshData(isMultiSelect);
            if (isMultiSelect) {
                showSelectTopBar();
            } else {
                hideSelectTopBar();
            }
        }
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.et_files_cancel_btn:
                cancelEvent();
                break;
            case R.id.et_files_select_all_btn:
                selectAll();
                break;
            case R.id.et_files_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.et_files_multi_select_btn:
                multiSelect();
                break;
            case R.id.et_folder_delete_rl:
                confirmDeleteDialog();
                break;
            default:
                break;
        }
    }

    /**
     * @des: 显示选择TopBar
     * @params:
     * @return:
     */

    private void showSelectTopBar() {
        etFilesBackBtn.setVisibility(View.GONE);
        etFilesUnselectedTopBarRl.setVisibility(View.GONE);
        etFilesCancelBtn.setVisibility(View.VISIBLE);
        etFilesSelectAllBtn.setVisibility(View.VISIBLE);
        etFilesCancelBtn.setText(R.string.cancel);
        etFilesTitleTv.setVisibility(View.VISIBLE);
        etFilesTitleTv.setText(String.format(getString(R.string.select_num_et), isCheckedMap.size() + ""));
        etFilesNoTitleTv.setVisibility(View.GONE);
        etFilesSelectAllBtn.setText(R.string.select_all);
    }

    /**
     * @des: 隐藏选择TopBar
     * @params:
     * @return:
     */

    private void hideSelectTopBar() {
        etFilesBottomLl.setVisibility(View.GONE);
        etFilesUnselectedTopBarRl.setVisibility(View.VISIBLE);
        etFilesBackBtn.setVisibility(View.VISIBLE);
        etFilesCancelBtn.setVisibility(View.GONE);
        etFilesSelectAllBtn.setVisibility(View.GONE);
        etFilesTitleTv.setVisibility(View.GONE);
        etFilesNoTitleTv.setVisibility(View.VISIBLE);
//        etFilesNoTitleTv.setText(folderName);
    }

    /**
     * @des: 显示删除Dialog
     * @params:
     * @return:
     */
    private void confirmDeleteDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(AuraMateErrorSitActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.confirm_delete));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                delete();
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();
    }

    /**
     * @des: 选中所有
     * @params:
     * @return:
     */
    private void selectAll() {
        if (!isSelectAll) {
            for (int i = 0; i < filesBeans.size(); i++) {
                if (!isCheckedMap.containsKey((filesBeans.get(i).getId()))) {
                    isCheckedMap.put(filesBeans.get(i).getId(), filesBeans.get(i).getErrorImgOSSKey());
                }
            }
            etFilesBottomLl.setVisibility(View.VISIBLE);
            etFilesSelectAllBtn.setText(R.string.not_select_all);
            isSelectAll = true;
        } else {
            etFilesBottomLl.setVisibility(View.GONE);
            isCheckedMap.clear();
            isCheckedMap = new LinkedHashMap<>();
            etFilesSelectAllBtn.setText(R.string.select_all);
            isSelectAll = false;
        }
        etFilesTitleTv.setText(String.format(getString(R.string.select_num_et), isCheckedMap.size() + ""));
        auraFilesAdapter.refreshData(filesBeans, true, isCheckedMap);
    }

    /**
     * @des: 取消事件
     * @params:
     * @return:
     */

    private void cancelEvent() {
        fileIds = new ArrayList<>();
        isMultiSelect = false;
        isSelectAll = false;
        isCheckedMap.clear();
        isCheckedMap = new LinkedHashMap<>();
        auraFilesAdapter.refreshData(filesBeans, false, isCheckedMap);
        hideSelectTopBar();
    }

    /**
     * @des: 删除文件或者图片
     * @params:
     * @return:
     */
    private void delete() {
        httpManager.request().deleteAuraErrorSitPicture(
                userPreferences.getUserId(),
                showErrorPictureRelationId,
                EtUtils.transFiles(fileIds),
                String.class,
                new MiaoHttpManager.CallbackNetwork<String>() {
            @Override
            public void onNoNetwork() {
                showMessage(R.string.toast_no_connection_network);
            }

            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                String key = EtUtils.transFiles(fileIds);
                hideProgressDialog();
                refreshAfterDeleteSuccess(key);
                EventBus.getDefault().post(new DeleteFilesEvent(EventType.AURA_DELETE_FILE,key));
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                resetCheckList();
                refreshFiles();
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
                resetCheckList();
                refreshFiles();
            }
        });
    }

    private void refreshAfterDeleteSuccess(String key) {
        Iterator<AuraErrorSitPictureModel> it = filesBeans.iterator();
        while (it.hasNext()) {
            if (key.contains(it.next().getId())) {
                it.remove();
            }
        }
        cancelEvent();
        isShowEmptyPrompt();
    }


}
