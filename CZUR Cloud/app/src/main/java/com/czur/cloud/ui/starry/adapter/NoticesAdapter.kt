package com.czur.cloud.ui.starry.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ToastUtils
import com.czur.cloud.R
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.model.Notice
import com.czur.cloud.ui.starry.utils.Tools
import com.czur.cloud.ui.starry.utils.singleClick
import kotlinx.android.synthetic.main.starry_item_message.view.*

class NoticesAdapter: RecyclerView.Adapter<NoticesAdapter.InnerHodler>() {

    private val mNoticesList = arrayListOf<Notice>()
    private var mSelected = false       // true:选择模式；false：列表模式
    private var isCheckedMap = LinkedHashMap<String, String>()
    private var isAllChecked = false

    class InnerHodler(itemView: View): RecyclerView.ViewHolder(itemView) {

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): InnerHodler {
        val itemView = LayoutInflater.from(parent.context).inflate(R.layout.starry_item_message, parent, false)
        return InnerHodler(itemView)
    }

    override fun onBindViewHolder(holder: InnerHodler, position: Int) {
        holder.itemView.apply {
            with(mNoticesList[position]){
                val msg_id = this.id.toString()
                val msg_title = this.title
                starry_item_msg_title?.text = msg_title
//                val date1: String = createTime.toString()
                val date1 = getTimeStr()
                val date2 = Tools.getFormatDateFromStamp(date1)
                val date = Tools.getFormatDateTimeForRecently(date2)
                starry_item_msg_date?.text = date

                // 选择模式
                msg_view?.visibility = View.INVISIBLE

                if (mSelected){
                    msg_red_point?.visibility = View.GONE
                    starry_msg_check?.visibility = View.VISIBLE
                    starry_msg_check?.tag = msg_id

                    starry_msg_check?.setOnCheckedChangeListener { buttonView, isChecked ->
                        if (isChecked) {
                            if (!isCheckedMap.containsKey(starry_msg_check.getTag())) {
                                //选中时添加
                                isCheckedMap[msg_id] = ""
                            }
                        } else {
                            if (isCheckedMap.containsKey(starry_msg_check.getTag())) {
                                //没选中时移除
                                isCheckedMap.remove(msg_id)
                            }
                        }
                        clicklistener?.onclickSel(
                            position,
                            isCheckedMap
                        )
                    }

                    starry_item_message_rl?.setOnClickListener {
                        if (mSelected) {
                            val isChecked1 = !(starry_msg_check?.isChecked ?: true)
                            if (isChecked1) {
                                if (!isCheckedMap.containsKey(starry_msg_check.getTag())) {
                                    //选中时添加
                                    isCheckedMap[msg_id] = ""
                                }
                            } else {
                                if (isCheckedMap.containsKey(starry_msg_check.getTag())) {
                                    //没选中时移除
                                    isCheckedMap.remove(msg_id)
                                }
                            }
                            starry_msg_check?.isChecked = isChecked1
                            clicklistener?.onclick(mSelected, msg_id, msg_title, position, isCheckedMap)
                        }
                    }

                    starry_msg_check?.isChecked = isCheckedMap.containsKey(msg_id)

                }else {//列表模式
                    msg_red_point?.visibility = View.INVISIBLE
                    starry_msg_check?.visibility = View.GONE

                    if (status == StarryConstants.STARRY_MESSAGE_STATUS_UNREAD) {
                        msg_red_point?.visibility = View.VISIBLE
                    } else {
                        msg_red_point?.visibility = View.INVISIBLE
                    }

                    starry_item_message_rl?.singleClick {
                        if (!mSelected) {
                            msg_red_point?.visibility = View.INVISIBLE
                            starry_item_msg_title?.setTextColor(context.getColor(R.color.starry_home_text_gray_color))
                            mNoticesList[position].status = StarryConstants.STARRY_MESSAGE_STATUS_READ
                            clicklistener?.onclick(mSelected, msg_id, msg_title, position, isCheckedMap)
                        }
                    }

                    starry_item_message_rl?.setOnLongClickListener {
                        if (!mSelected) {
                            clicklistener?.onclicklong(holder, position, msg_id)
                        }
                        true
                    }

                }
                if (status == StarryConstants.STARRY_MESSAGE_STATUS_UNREAD) {
                    starry_item_msg_title?.setTextColor(context.getColor(R.color.title_black_color))
                } else {
                    starry_item_msg_title?.setTextColor(context.getColor(R.color.starry_home_text_gray_color))
                }
            }
        }
    }

    private fun showDetail(title: String) {
        ToastUtils.showLong(title)
    }

    override fun getItemCount(): Int {
        return mNoticesList.size
    }

    fun setDatas(it: List<Notice>?) {
        mNoticesList.clear()
        if (it != null) {
            mNoticesList.addAll(it)
        }
        notifyDataSetChanged()
    }

    fun reflashUI(flag: Boolean){
        mSelected = flag
        notifyDataSetChanged()
    }

    fun setCheckMap(checkMap: LinkedHashMap<String, String>){
        this.isCheckedMap = checkMap
        notifyDataSetChanged()
    }
    fun getCheckMap() = this.isCheckedMap

    fun setAllChecked(flag: Boolean){
        this.isAllChecked = flag

        if (isAllChecked) {
            mNoticesList.forEach {
                isCheckedMap[it.id.toString()] = ""
            }
        }else{
            isCheckedMap.clear()
        }
        notifyDataSetChanged()
    }

    fun isSelecting():Boolean = mSelected

    fun isAllChecked(): Boolean{
        return isAllChecked
    }

    //自定义点击事件接口
    interface OnClickListener{
        fun onclick(isSelected: Boolean, id:String, title: String, position: Int, checkMap: LinkedHashMap<String, String>)
        fun onclickSel(position: Int, checkMap: LinkedHashMap<String, String>)
        fun onclicklong(holder: InnerHodler, position: Int, id: String)
    }
    var clicklistener:OnClickListener?=null

    fun setOnItemClickListener(listener: OnClickListener){
        clicklistener=listener
    }

}