package com.czur.cloud.ui.starry.meeting.dialog

import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.view.*
import android.widget.LinearLayout
import android.widget.TextView
import com.czur.cloud.R
import com.czur.cloud.util.validator.StringUtils

/**
 * 通用弹出dialog
 */
class StarryCommonToast : Dialog {

    constructor(context: Context?, theme: Int) : super(context!!, theme) {}

    class Builder(private val context: Context) {
        private var message: String? = null
        private var contentsView: View? = null
        private var positiveListener: DialogInterface.OnClickListener? = null
        private var onDismissListener: DialogInterface.OnDismissListener? = null
        fun setMessage(message: String?): Builder {
            this.message = message
            return this
        }

        fun setContentsView(contentsView: View?): Builder {
            this.contentsView = contentsView
            return this
        }

        fun setContentsView(resource: Int): Builder {
            contentsView = LayoutInflater.from(context).inflate(resource, null)
            return this
        }

        fun setOnPositiveListener(positiveListener: DialogInterface.OnClickListener?): Builder {
            this.positiveListener = positiveListener
            return this
        }

        fun setOnDismissListener(onDismissListener: DialogInterface.OnDismissListener?): Builder {
            this.onDismissListener = onDismissListener
            return this
        }

        fun create(): StarryCommonToast {
            val inflater =
                context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
            val dialog = StarryCommonToast(context, R.style.TransparentProgressDialog)
            val layout = commonCustomPopLayout(inflater, dialog)
            dialog.setContentView(layout)
            dialog.setCanceledOnTouchOutside(false)
            val params = dialog.window?.attributes
            params?.dimAmount = DIMMED_OPACITY
            return dialog
        }

        private fun commonCustomPopLayout(
            inflater: LayoutInflater,
            dialog: StarryCommonToast
        ): View {
            val lp = dialog.window?.attributes
            lp?.dimAmount = 0.8f
            dialog.window?.attributes = lp
            dialog.window?.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
            dialog.window?.setGravity(Gravity.TOP)

            val layout = inflater.inflate(R.layout.starry_common_custom_toast, null, false)
            val params = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            dialog.addContentView(layout, params)
            val message = layout.findViewById(R.id.message) as TextView
            val starry_custom_toast_ll = layout.findViewById(R.id.starry_custom_toast_ll) as LinearLayout

            if (StringUtils.isNotEmpty(this.message)) {
                message.text = this.message + StringUtils.EMPTY
            }

            if (positiveListener != null) {
                starry_custom_toast_ll.setOnClickListener {
                    positiveListener?.onClick(
                        dialog,0
                    )
                }
            } else {
                starry_custom_toast_ll.setOnClickListener { dialog.dismiss() }
            }

            if (onDismissListener != null) {
                dialog.setOnDismissListener(onDismissListener)
            }
            return layout
        }
    }

    companion object {
        const val DIMMED_OPACITY = 0.2f
    }

    override fun show() {
        super.show()

//        /**
//         * 设置宽度全屏，要设置在show的后面
//         */
        val params: WindowManager.LayoutParams = getWindow()!!.getAttributes()
        params.width = WindowManager.LayoutParams.WRAP_CONTENT
        params.height = WindowManager.LayoutParams.WRAP_CONTENT
        params.gravity = Gravity.TOP
        params.y=100
        // 拥有穿透效果 dialog布局之外可以相应事件传递
        params.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
        getWindow()?.setBackgroundDrawableResource(android.R.color.transparent)
        getWindow()?.setDimAmount(0f)
        getWindow()?.setAttributes(params)

    }
}