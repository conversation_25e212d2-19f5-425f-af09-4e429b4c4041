package com.czur.cloud.ui.home;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.czur.cloud.R;
import com.czur.cloud.ui.market.MallFragment;
import com.czur.cloud.ui.user.UserFragment;
import com.czur.cloud.util.validator.Validator;


public class IndexFragmentAttacher {
    private static final String TAG = "AuraMateFragmentAttacher";
    public static String INDEX_FRAGMENT_NAME = "index_fragment_name";
    private FragmentManager fm;

    public IndexFragmentAttacher(FragmentManager fm) {
        this.fm = fm;
    }

    public Fragment showFragment(int willShowFragmentIndex, int currentShowFragmentIndex) {
        Fragment currentShowFragment = getFragment(currentShowFragmentIndex);
        Fragment willShowFragment = getFragment(willShowFragmentIndex);
        FragmentTransaction trx = fm.beginTransaction();
        if (willShowFragmentIndex != currentShowFragmentIndex) {

            if (Validator.isNotEmpty(currentShowFragment)) {
                trx.hide(currentShowFragment);
            } else {
            }

            if (Validator.isEmpty(willShowFragment)) {
                willShowFragment = createFragment(willShowFragmentIndex);
            } else {
            }

            if (!willShowFragment.isAdded()) {
                trx.add(R.id.index_frameLayout, willShowFragment, INDEX_FRAGMENT_NAME + willShowFragmentIndex);
            } else {
            }
            trx.show(willShowFragment);
            trx.commit();
            return  willShowFragment;
        }
        return  willShowFragment;
    }

    public Fragment showFragment(int willShowFragmentIndex) {
        Fragment willShowFragment = getFragment(willShowFragmentIndex);
        FragmentTransaction trx = fm.beginTransaction();

        if (Validator.isEmpty(willShowFragment)) {
            willShowFragment = createFragment(willShowFragmentIndex);
        }

        if (!willShowFragment.isAdded()) {
            trx.add(R.id.index_frameLayout, willShowFragment, INDEX_FRAGMENT_NAME + willShowFragmentIndex);
        } else {
        }

        trx.show(willShowFragment);
        trx.commit();
        return  willShowFragment;
    }

    public Fragment createFragment(int index) {
        Fragment fragment = null;
        switch (index) {
            case 0:
                fragment = HomeFragment.newInstance();
                break;
            case 1:
                fragment = MallFragment.newInstance();
                break;
            case 2:
                fragment = UserFragment.newInstance();
                break;
        }
        return fragment;
    }

    public Fragment getFragment(int index) {
        return fm.findFragmentByTag(INDEX_FRAGMENT_NAME + index);
    }
}
