package com.czur.cloud.ui.home;

import static com.blankj.utilcode.util.PermissionUtils.isGranted;
import static com.czur.czurutils.log.CZURLogUtilsKt.logD;
import static com.czur.czurutils.log.CZURLogUtilsKt.logE;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.Manifest;
import android.app.Activity;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.ServiceUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.adapter.HomeEquipmentAdapter;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.entity.realm.HomeCacheEntity;
import com.czur.cloud.entity.realm.PageEntity;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.UpdateEvent;
import com.czur.cloud.model.CheckForceUpdateModel;
import com.czur.cloud.model.IndexEquipmentModel;
import com.czur.cloud.netty.observer.NettyUtils;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.FirstPreferences;
import com.czur.cloud.preferences.StarryPreferences;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.preferences.VersionPreferences;
import com.czur.cloud.ui.aura.AuraRemoteActivity;
import com.czur.cloud.ui.auramate.AuraMateActivity;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.base.LazyLoadBaseFragment;
import com.czur.cloud.ui.books.BookShelfActivity;
import com.czur.cloud.ui.component.damping.VerticalRecyclerView;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.et.EtManageActivity;
import com.czur.cloud.ui.mirror.SmartSittingActivity;
import com.czur.cloud.ui.mirror.comm.FastBleOperationUtils;
import com.czur.cloud.ui.mirror.mydialog.SittingDialog;
import com.czur.cloud.ui.starry.activity.StarryActivity;
import com.czur.cloud.ui.starry.common.StarryConstants;
import com.czur.cloud.ui.starry.livedatabus.LiveDataBus;
import com.czur.cloud.ui.starry.meeting.common.MeetingCMD;
import com.czur.cloud.ui.starry.meeting.common.MsgProcessor;
import com.czur.cloud.ui.starry.meeting.dialog.StarryCommonPopup;
import com.czur.cloud.ui.starry.utils.RomUtils;
import com.czur.cloud.ui.user.AboutActivity;
import com.czur.cloud.ui.user.download.DownloadAFileService;
import com.czur.cloud.util.AppClearUtils;
import com.czur.cloud.util.PermissionUtil;
import com.czur.cloud.util.VersionUtil;
import com.czur.cloud.util.validator.Validator;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import io.realm.Realm;
import io.realm.RealmResults;
import io.realm.Sort;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;


public class HomeFragment extends LazyLoadBaseFragment implements View.OnClickListener {

    private VerticalRecyclerView homeRecyclerView;
    private RelativeLayout addEquipmentBtn;

    private List<IndexEquipmentModel> equipmentEntities;
    private HomeEquipmentAdapter homeEquipmentAdapter;
    private ImageView hasEquipmentAddBtn;
    private LinearLayout noEquipmentLl;
    private ImageView noEquipmentTitleAddBtn;
    private RelativeLayout noEquipmentAddRl;
    private RelativeLayout defaultRl;
    private ImageView noEquipmentAddTopImg;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private ImageView noEquipmentBgImg;
    private RelativeLayout noEquipmentRl;
    private FirstPreferences firstPreferences;
    private String updateUrl;
    private String notes;
    private String version;
    private String apkPath;
    private String apkName;
    private Realm realm;
    private boolean needCheckUpdate;
    private IndexActivity activity;
    private StarryCommonPopup checkForceUpdatePopup;
    private CheckForceUpdateModel checkForceUpdateModel;
    private CloudCommonPopup commonPopup;

    public static HomeFragment newInstance() {
        HomeFragment mallFragment = new HomeFragment();
        return mallFragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }


    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        activity = (IndexActivity) getActivity();
        registerEvent();
        resetTempAndCreateDir();
        initRecyclerView();

        // 直接启动长连接
//        startNettyStarry();
        logI("HomeFragment.onActivityCreated.startNettyStarryService");
        AppClearUtils.startNettyStarryService();

        getEquipmentList();
        if (activity != null) {
            needCheckUpdate = activity.getNeedSync();
            logI("HomeFragment.onActivityCreated.needCheckUpdate="+needCheckUpdate);
            if (needCheckUpdate) {
                // 海外版无需更新，只检测新版本而已
                if (BuildConfig.IS_OVERSEAS){
                    EventBus.getDefault().post(new UpdateEvent(EventType.HAS_NO_FORCE_NEW_VERSION));
                    checkUpdate();
                }else {
                    checkForceUpdate();
                }
            }
        }

        LiveDataBus.get()
                .with(StarryConstants.STARRY_REMOVE, Boolean.class)
                .observe(getViewLifecycleOwner(), aBoolean -> {
                    logI("HomeFragment.STARRY_REMOVE");
                    getEquipmentList();
                });

        // 下载中
        LiveDataBus.get()
            .with(StarryConstants.DOWNLOAD_FORCE_RUNNING, Boolean.class)
            .observe(getViewLifecycleOwner(), aBoolean -> {
                logI("HomeFragment.DOWNLOAD_FORCE_RUNNING");
                if (checkForceUpdateBuilder != null){
                    TextView posTv = checkForceUpdateBuilder.getLayout().findViewById(R.id.positive_button);
                    posTv.setText(getString(R.string.czur_force_updating));
                }
            });
        // 下载完成
        LiveDataBus.get()
            .with(StarryConstants.DOWNLOAD_FORCE_DONE, Boolean.class)
            .observe(getViewLifecycleOwner(), aBoolean -> {
                logI("HomeFragment.DOWNLOAD_FORCE_DONE.aBoolean="+aBoolean);
                if (checkForceUpdateBuilder != null) {
                    TextView posTv = checkForceUpdateBuilder.getLayout().findViewById(R.id.positive_button);
                    posTv.setText(getString(R.string.czur_force_update_yes));
                }
            });
    }


    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_home;
    }


    @Override
    protected void initView(View view) {
        realm = Realm.getDefaultInstance();
        noEquipmentRl = view.findViewById(R.id.index_no_equipment_container);
        noEquipmentBgImg =  view.findViewById(R.id.no_equipment_bg_img);
        homeRecyclerView =  view.findViewById(R.id.home_list);
        addEquipmentBtn =  view.findViewById(R.id.no_equipment_add_btn);
        noEquipmentTitleAddBtn =  view.findViewById(R.id.no_equipment_title_add_btn);
        hasEquipmentAddBtn =  view.findViewById(R.id.has_equipment_add_btn);
        noEquipmentLl =  view.findViewById(R.id.no_equipment_bg_ll);
        defaultRl = view.findViewById(R.id.rl_default);
        httpManager = HttpManager.getInstance();
        firstPreferences = FirstPreferences.getInstance(getActivity());
        userPreferences = UserPreferences.getInstance(getActivity());
        Bitmap bitmap = ImageUtils.compressByScale(ImageUtils.getBitmap(R.mipmap.index_no_equipment_bg), ScreenUtils.getScreenWidth(), ScreenUtils.getScreenHeight());
        noEquipmentBgImg.setImageBitmap(bitmap);
        noEquipmentBgImg.setScaleType(ImageView.ScaleType.CENTER_CROP);
        noEquipmentAddRl = view.findViewById(R.id.no_equipment_add_rl);
        noEquipmentAddTopImg = view.findViewById(R.id.no_equipment_add_top_img);
        int shadowWidth = ScreenUtils.getScreenWidth() - SizeUtils.dp2px(15);
        int itemWidth = ScreenUtils.getScreenWidth() - SizeUtils.dp2px(35);

        FrameLayout.LayoutParams addEquipmentBtnLayoutParams = (FrameLayout.LayoutParams) addEquipmentBtn.getLayoutParams();
        addEquipmentBtnLayoutParams.height = shadowWidth * 270 / 360;
        addEquipmentBtn.setLayoutParams(addEquipmentBtnLayoutParams);

        RelativeLayout.LayoutParams noEquipmentAddRlLayoutParams = (RelativeLayout.LayoutParams) noEquipmentAddRl.getLayoutParams();
        noEquipmentAddRlLayoutParams.height = itemWidth * 250 / 340;
        noEquipmentAddRl.setLayoutParams(noEquipmentAddRlLayoutParams);

        RelativeLayout.LayoutParams noEquipmentAddTopImgLayoutParams = (RelativeLayout.LayoutParams) noEquipmentAddTopImg.getLayoutParams();
        noEquipmentAddTopImgLayoutParams.height = itemWidth * 402 / 680;
        noEquipmentAddTopImg.setLayoutParams(noEquipmentAddTopImgLayoutParams);


        // 获取starry用户信息
        // 海外同步一下Starry的accountNo到userPreference中的mobile
        logI("HomeFragment.initView.StarryUserAccount");
        if (BuildConfig.IS_OVERSEAS) {
            try {
                Thread.sleep(1000);
                AppClearUtils.syncStarryUserAccount();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        } else {
            AppClearUtils.getStarryUserAccount();
        }

    }

    /**
     * @des: 重置不在相机预览页的Page状态
     * @params:
     * @return:
     */

    private void resetTempAndCreateDir() {
        realm.executeTransaction(new Realm.Transaction() {
            @Override
            public void execute(Realm realms) {
                RealmResults<PageEntity> pageEntities = realm.where(PageEntity.class)
                        .equalTo("isTemp", 1)
                        .equalTo("isDelete", 0)
                        .findAll().sort("takePhotoTime", Sort.ASCENDING);

                for (PageEntity tempPageEntity : pageEntities) {
                    tempPageEntity.setIsTemp(0);
                    tempPageEntity.setTakePhotoTime(CZURConstants.TAKE_PHOTO_INIT_TIME);
                }
            }
        });
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                if (activity != null) {
                    String pagePath = activity.getFilesDir() + File.separator + userPreferences.getUserId() + CZURConstants.PAGE_PATH;
                    boolean orExistsPagePath = FileUtils.createOrExistsDir(pagePath);
                }
                return null;
            }

            @Override
            public void onSuccess(Void result) {

            }
        });

    }

    private void initRecyclerView() {
        equipmentEntities = new ArrayList<>();
        getHomeListFromCache();
        hasEquipment();
        homeEquipmentAdapter = new HomeEquipmentAdapter((BaseActivity) getActivity(), equipmentEntities);
        homeEquipmentAdapter.setOnItemClickListener(onItemClickListener);
        homeRecyclerView.setAdapter(homeEquipmentAdapter);
        homeRecyclerView.setHasFixedSize(true);
        homeRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
    }

    private void registerEvent() {
        addEquipmentBtn.setOnClickListener(this);
        hasEquipmentAddBtn.setOnClickListener(this);
        noEquipmentTitleAddBtn.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.has_equipment_add_btn:
            case R.id.no_equipment_title_add_btn:
            case R.id.no_equipment_add_btn:
                ActivityUtils.startActivity(AddEquipmentActivity.class);
                break;
            default:
                break;
        }
    }

    /**
     * @des: 获取设备列表
     * @params:[]
     * @return:void
     */
    private void getEquipmentList() {
        httpManager.request().getEquipmentList(
                userPreferences.getUserId(), new TypeToken<List<IndexEquipmentModel>>() {
                }.getType(), new MiaoHttpManager.CallbackNetwork<IndexEquipmentModel>() {
                    @Override
                    public void onNoNetwork() {
                        hideProgressDialog();
                    }

                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<IndexEquipmentModel> entity) {
                        hideProgressDialog();
                        equipmentEntities = entity.getBodyList();
                        Iterator<IndexEquipmentModel> it = equipmentEntities.iterator();
                        while (it.hasNext()) {
                            String str = it.next().getKey();
                            //海外 没有 ETS
                            if (BuildConfig.IS_OVERSEAS) {
                                if (str.contains(getString(R.string.ETS))) {
                                    it.remove();
                                }
                            }

                            if (str.contains(getString(R.string.Starry))){ //移除starry
                                it.remove();
                            }
                        }


                        // 无需starry模块，均启动长连接
                        logI("HomeFragment.getEquipmentList.startNettyStarryService");
                        AppClearUtils.startNettyStarryService();

                        hasEquipment();
                        homeEquipmentAdapter.refreshData(equipmentEntities);
                        setHomeListCache(new Gson().toJson(equipmentEntities));
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<IndexEquipmentModel> entity) {
                        logE(new Gson().toJson(entity));
                        if (entity.getCode() == MiaoHttpManager.STATUS_FAIL) {
                            showMessage(R.string.toast_internal_error);
                        } else {
                            showMessage(R.string.request_failed_alert);
                        }
                        hideProgressDialog();
                    }

                    @Override
                    public void onError(Exception e) {
                        logE("HomeFragment.getEquipmentList.e="+e.toString());
                        showMessage(R.string.request_failed_alert);
                        hideProgressDialog();
                    }
                });
    }

    private void checkToStartNetty() {
        boolean hasAuraMate = false;
        for (IndexEquipmentModel equipmentEntity : equipmentEntities) {
            if ((equipmentEntity.getKey().equals(getString(R.string.AURA_HOME)))
                    || (equipmentEntity.getKey().equals(getString(R.string.Mirror)))
                    || (equipmentEntity.getKey().equals(getString(R.string.Starry)))
            ) {
                setServerTime();
                hasAuraMate = true;
                userPreferences.setHasAuraMate(true);
            }

        }
        userPreferences.setHasAuraMate(hasAuraMate);
    }

    /**
     * @des: 从数据库中读取缓存的gson
     * @params:
     * @return:
     */

    private void getHomeListFromCache() {
        RealmResults<HomeCacheEntity> homeCacheEntities = realm.where(HomeCacheEntity.class).findAll();
        if (Validator.isNotEmpty(homeCacheEntities)) {
            String homeListGson = homeCacheEntities.get(0).getHomeListGson();
            if (Validator.isNotEmpty(homeListGson)) {
                equipmentEntities = new Gson().fromJson(homeListGson,
                        new TypeToken<List<IndexEquipmentModel>>() {
                        }.getType());
            }
        }
        checkToStartNetty();
    }


    private HomeEquipmentAdapter.OnItemClickListener onItemClickListener = new HomeEquipmentAdapter.OnItemClickListener() {
        @Override
        public void onItemClick(int position, View view, IndexEquipmentModel indexEquipmentEntity) {
            logD("onItemClickListener.onItemClick.indexEquipmentEntity.getKey()="+indexEquipmentEntity.getKey());

            if (indexEquipmentEntity.getKey().equals(getString(R.string.AURA_HOME))) {
                ActivityUtils.startActivity(AuraMateActivity.class);

            } else if (indexEquipmentEntity.getKey().equals(getString(R.string.ET))) {
                ActivityUtils.startActivity(EtManageActivity.class);

            } else if (indexEquipmentEntity.getKey().equals(getString(R.string.ETS))) {
                Intent intent = new Intent(getActivity(), AuraRemoteActivity.class);
                intent.putExtra("isAura", false);
                ActivityUtils.startActivity(intent);

            } else if (indexEquipmentEntity.getKey().equals("Aura")) {
                Intent intent = new Intent(getActivity(), AuraRemoteActivity.class);
                intent.putExtra("isAura", true);
                ActivityUtils.startActivity(intent);

            }
            // 智能笔记本
            else if (indexEquipmentEntity.getKey().equals(getString(R.string.notebookCN))) {
                ActivityUtils.startActivity(BookShelfActivity.class);

            }
            // 坐姿仪
            else if (indexEquipmentEntity.getKey().equals(getString(R.string.Mirror))) {
                logI("HomeFragment.Mirror on Click!");
                if (BuildConfig.IS_OVERSEAS) {
                    if (FirstPreferences.getInstance(activity.getApplicationContext()).isFirstMirrorLocation()) {
                        // googleplay for location
                        showLocationAlertDailog(activity);
                        FirstPreferences.getInstance(activity.getApplicationContext()).setIsFirstMirrorLocation(false);
                    } else {
                        checkBlePermission();
                    }
                } else {
                    checkBlePermission();
                }

            }
            // 投影仪
            else if (indexEquipmentEntity.getKey().equals(getString(R.string.Starry))) {
                try {
                    Thread.sleep(200);
                } catch (Exception e) {
                    //
                }
                ActivityUtils.startActivity(StarryActivity.class);

            }
            // 无线投屏
            else if (indexEquipmentEntity.getKey().equals(getString(R.string.EShare))) {
//                checkPermission();
                AppClearUtils.checkESharePermission(requireContext(), requireActivity(), true);
            }

        }

        @Override
        public void onItemLongClick(int position, IndexEquipmentModel indexEquipmentEntity) {

        }
    };

    private void checkBlePermission() {
        String[] permissions = PermissionUtil.getBlueToothPermissions();
        boolean flag = isGranted(permissions);
        logI("HomeFragment.checkBlePermission.flag=" + flag);
        if (flag) { //权限开启状态
            ActivityUtils.startActivity(SmartSittingActivity.class);
        } else { //权限关闭状态
            String str= requireActivity().getString(R.string.czur_permission_bluetooth);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S){
                str = requireActivity().getString(R.string.czur_permission_bluetooth12);
            }

                PermissionUtil.checkPermissionWithDialog(
                    requireContext(),
                    requireActivity().getString(R.string.starry_popupwindow_title),
                    str,
                    requireActivity().getString(R.string.starry_go_open_permission),
                    requireActivity().getString(R.string.starry_background_start_msg_cancel),
                    v -> {
                        if (v != null){//点击去设置
                            PermissionUtil.useToolsRequestPermission(
                                    PermissionUtil.getBlueToothPermissions(),
                                    () -> {
                                        ActivityUtils.startActivity(SmartSittingActivity.class);
                                    }
                            );
                        }
                    }
            );
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        logI("HomeFragment.onEvent=" + event.getEventType());
        switch (event.getEventType()) {
            case CHECK_FORCE_NEW_VERSION:
                // 海外版无需检测更新
                if (BuildConfig.IS_OVERSEAS){
                    break;
                }

                NettyUtils.getInstance().setNettyState(NettyUtils.KEEP_STOP);
                NettyUtils.getInstance().stopNettyService();

                checkForceUpdate();
                break;
            case HAS_FORCE_NEW_VERSION:
                // 海外版无需检测更新
                if (BuildConfig.IS_OVERSEAS){
                    break;
                }

                if (checkForceUpdateModel.getBody().getForce() == 1) {//强制更新
                    NettyUtils.getInstance().setNettyState(NettyUtils.KEEP_STOP);
                    NettyUtils.getInstance().stopNettyService();

                    showForceUpdateDialog();
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            NettyUtils.getInstance().stopNettyService();
                        }
                    }, 3000);
                } else {
                    checkUpdate();
                }
                break;

            case STARRY_EQUIPMENTLIST_REFRESH:
            case ADD_EQUIPMENT:
                getEquipmentList();
                break;

            // starry上线，获取一下是否有正在呼叫的会议，弹出呼叫邀请页面
            case STARRY_DEVICE_ONLINE:
                String meetNo = StarryPreferences.getInstance().getAccountNo();
                if (meetNo == null || meetNo.isEmpty()) {
                    meetNo = UserPreferences.getInstance().getUserMobile();
                }
                String cmd = MeetingCMD.RECENT_MEETING.getCmd();
                if (meetNo != null && !meetNo.isEmpty()) {
                    MsgProcessor.INSTANCE.commonMeetCMD(cmd, meetNo);
                }
                break;

            // 昵称更新，需要starry数据同步一下
            case EDIT_USER_IMAGE:
            case CHANGE_PHONE:
            case CHANGE_EMAIL:
            case USER_EDIT_NAME:
//                getStarryUserInfo();
                // 同步一下starry的用户信息
                AppClearUtils.getStarryUserInfo();
                break;
            case HAS_NEW_VERSION:
                if (event instanceof UpdateEvent) {

                    //如果是第一次显示更新红点，才通知userfragment NEW展示
                    if (firstPreferences.isFirstNewVersion()) {
                        firstPreferences.setIsUserNewVersion(true);
                    }
                    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.R) {
                        checkInstallPermission();
                    }else{
                        checkNewVersion();
                    }


                }
                break;

            case HAS_NEW_VERSION_OK:
                FastBleOperationUtils.setIsAPPUpdateOK(true);
                break;

            default:
                break;
        }
    }

    /**
     * @des: 检查新版本后下载或者安装
     * @params:
     * @return:
     */
    private void checkNewVersion() {
        logI("HomeFragment.checkNewVersion.apkPath=" + apkPath + ";apkName=" + apkName);
        File apkFile = new File(apkPath, apkName);
        File realApkFile = new File(apkPath, apkName);
        if (VersionPreferences.getInstance().getDownloadId() != -1){//如果存有下载记录并且下载记录是当前版本的,就去验证是否可以使用
            if (VersionPreferences.getInstance().getApkName().contains(version)){
                apkFile = new File(apkPath, VersionPreferences.getInstance().getApkName());
                if (!TextUtils.isEmpty(VersionPreferences.getInstance().getRealApkName())){
                    realApkFile = new File(apkPath, VersionPreferences.getInstance().getRealApkName());
                }
            }
        }

        //apk是否存在，存在就进行安装
        boolean flag = (apkFile.exists() || realApkFile.exists()) && VersionPreferences.getInstance().getDownloadId() != -1L;
        logI("HomeFragment.checkNewVersion.apkFile=" + apkFile + ";apkFile.exists()=" + flag);

//        // 清理一下旧的apk
//        AppClearUtils.checkOldApkFiles(apkPath, apkName);

        if (flag) {
            // apk 文件存在, 证明开启过下载任务
            // 启动Service 去同步一下下载任务, 看看是否完成
            logI(apkPath + " apk is exist!");
            DownloadAFileService.checkHistoryDownloadTask(requireContext(), false);
        } else {
//            FastBleOperationUtils.setIsAPPUpdateOK(false);
            if (NetworkUtils.isWifiConnected()) {
                FastBleOperationUtils.setIsAPPUpdateOK(false);
                startDownloadService(true);
            } else{
                downloadInNotWifi();
            }
        }
    }

    /**
     * @des: 检查更新
     * @params:
     * @return:
     */

    private void checkUpdate() {
        // 海外版本GooglePlay不需要自动更新
        if (BuildConfig.IS_OVERSEAS) {
            return;
        }
        
        if (!NetworkUtils.isConnected()) {
            return;
        }

        VersionUtil.INSTANCE.getNewVersionInfoForJava(this, versionInfoEntity -> {
            logI("HomeFragment.checkUpdate.new version : " + versionInfoEntity);
            updateUrl = versionInfoEntity.getPackageUrl();
            notes = versionInfoEntity.getNotes();
            version = versionInfoEntity.getVersionName();
            apkPath = CZURConstants.DOWNLOAD_PATH;
            String randomVersion = String.valueOf(System.currentTimeMillis() / 1000);
            randomVersion = randomVersion.substring(randomVersion.length() -6,randomVersion.length());
            if (getActivity() != null) {
                apkName = getActivity().getString(R.string.app_name) + "_" + version + "." + randomVersion + ".apk";
                if (VersionUtil.INSTANCE.hasNewVersion(versionInfoEntity)) {
                    EventBus.getDefault().post(new UpdateEvent(EventType.HAS_NEW_VERSION));
                } else {
                    // 无更新,关闭forceupdate弹窗
                    if(checkForceUpdatePopup != null){
                        checkForceUpdatePopup.dismiss();
                    }
                    EventBus.getDefault().post(new UpdateEvent(EventType.IS_LATEST_VERSION));
                }
            }

            return null;
        }, throwable -> {
            logE(throwable.toString());
            hideProgressDialog();
            return null;
        });
    }


    /**
     * @des: 列表状态存到数据库
     * @params:
     * @return:
     */

    private void setHomeListCache(final String gsonString) {
        if (realm == null || realm.isEmpty() || realm.isClosed()) {
            realm = Realm.getDefaultInstance();
        }
        realm.executeTransaction(new Realm.Transaction() {
            @Override
            public void execute(Realm realm) {
                RealmResults<HomeCacheEntity> homeCacheEntities = realm.where(HomeCacheEntity.class).findAll();
                if (Validator.isEmpty(homeCacheEntities)) {
                    HomeCacheEntity pageEntity = realm.createObject(HomeCacheEntity.class, 0);
                    pageEntity.setHomeListGson(gsonString);
                } else {
                    for (HomeCacheEntity homeCacheEntity : homeCacheEntities) {
                        homeCacheEntity.setHomeListGson(gsonString);
                    }
                }

            }
        });
    }

    /**
     * @des: 是否显示列表
     * @params:
     * @return:
     */

    public void hasEquipment() {
        if (equipmentEntities == null) {
            return;
        }
        if (equipmentEntities.size() > 0) {
            homeRecyclerView.setVisibility(View.VISIBLE);
            noEquipmentRl.setVisibility(View.GONE);
            BarUtils.setStatusBarColor(activity, activity.getColor(R.color.gary_f9));
            BarUtils.addMarginTopEqualStatusBarHeight(defaultRl);
            BarUtils.setStatusBarLightMode(activity, true);
        } else {
            homeRecyclerView.setVisibility(View.GONE);
            noEquipmentRl.setVisibility(View.VISIBLE);
            BarUtils.setStatusBarColor(activity, activity.getColor(R.color.transparent));
            BarUtils.addMarginTopEqualStatusBarHeight(noEquipmentLl);
            BarUtils.setStatusBarLightMode(activity, false);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        if (realm != null) {
            realm.close();
        }
    }

    // 弹出对话框，告知用户
    private void showLocationAlertDailog(Activity activity) {
        SittingDialog dialog = new SittingDialog(activity, R.style.sittingDialog,
                confirm -> {
                    if (confirm) {
                        checkBlePermission();
                    }
                });
        dialog.setOneButton(true);
        dialog.setPositiveButton(getString(R.string.czur_location_dialog_ok));
        dialog.setTitle(getString(R.string.czur_dialog_title));
        dialog.setContent(getString(R.string.czur_location_alert_msg));
        dialog.create();
        dialog.setCanceledOnTouchOutside(false);//加上这个，点击空白处不消失
        dialog.show();

    }


    public void refreshList() {
        getEquipmentList();
    }


    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == StarryConstants.RESULT_CHECK_OVERLAYS_CODE) {
            if (RomUtils.isXiaoMi()) {
                new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                    @Override
                    public void run() {
//                        checkPermission();
                        AppClearUtils.checkESharePermission(requireContext(), requireActivity());
                    }
                }, 500);
            }
        }
    }


    private void checkForceUpdate() {
        String clearCacheStr = "?" + UUID.randomUUID().toString();
        String version = "?" + "version=" + AppUtils.getAppVersionName();
        String boundId = "&" + "boundId=com.czur.cloud";
        String platform = "&" + "platform=Android";
        String param = version + boundId + platform;
        logI("checkForceUpdate.url="+BuildConfig.CHECK_FORCE_UPDATE + param);
        Request checkRequest = new Request.Builder().url(BuildConfig.CHECK_FORCE_UPDATE + param).get().build();
        Call checkCall = new OkHttpClient().newCall(checkRequest);
        checkCall.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                logE("checkForceUpdate.onFailure:"+ e.toString());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                String responseStr = Objects.requireNonNull(response.body()).string();
                logI("checkForceUpdate.onResponse: " + responseStr);
                try {
                    checkForceUpdateModel = new Gson().fromJson(responseStr, CheckForceUpdateModel.class);
//                    // only for force update Test
//                    checkForceUpdateModel.getBody().update = 1;
//                    checkForceUpdateModel.getBody().force = 1;

                    CheckForceUpdateModel.Body body = checkForceUpdateModel.getBody();

                    if (body.getForce() == 1 && body.getUpdate() == 1) {
                        EventBus.getDefault().post(new UpdateEvent(EventType.HAS_FORCE_NEW_VERSION));
                    } else {
                        EventBus.getDefault().post(new UpdateEvent(EventType.HAS_NO_FORCE_NEW_VERSION));
                        checkUpdate();
                    }


                } catch (Exception e) {
                    checkUpdate();
                }


            }
        });

    }

    StarryCommonPopup.Builder checkForceUpdateBuilder = null;

    private void showForceUpdateDialog() {
//        checkForceUpdatePopup =
        checkForceUpdateBuilder = new StarryCommonPopup.Builder(ActivityUtils.getTopActivity(), CloudCommonPopupConstants.CONFIRM_ONE_BUTTON)
                .setTitle(getString(R.string.starry_popupwindow_title))
                .setMessage(getString(R.string.czur_force_update_app))
                .setPositiveTitle(getString(R.string.czur_force_update_yes))
                .setOnPositiveListener(new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {

                        if (NetworkUtils.isConnected()) {
                            if (!FastBleOperationUtils.isIsAPPUpdateOK()) {
                                showMessage(R.string.no_wifi_download);
                            } else {
                                checkUpdate();
                            }
                        } else {
                            showMessage(R.string.toast_no_connection_network_update);
                        }
                    }
                })
                .setOnNegativeListener(new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        checkForceUpdatePopup.dismiss();
                        ActivityUtils.finishAllActivities();
                        //退出应用
                    }
                });
        checkForceUpdatePopup = checkForceUpdateBuilder.create();
        checkForceUpdatePopup.show();

        // 有强制更新时,询问一下是否有下载中
        DownloadAFileService.checkHistoryDownloadTask(requireContext(), false);

    }


    /**
     * @des: 显示是否在非wifi网络下载Dialog
     * @params:
     * @return:
     */

    private void downloadInNotWifi() {
        
        // 如果当前最顶页面是About页面,则不需要显示该对话框
        if (ActivityUtils.getTopActivity() instanceof AboutActivity) {
            return;
        }

        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(getActivity(), CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.download_app_prompt));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {

                if (commonPopup != null) {
                    commonPopup.dismiss();
                }
                FastBleOperationUtils.setIsAPPUpdateOK(false);
                startDownloadService(false);
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                FastBleOperationUtils.setIsAPPUpdateOK(true);
                dialog.dismiss();
            }
        });
        commonPopup = builder.create();
        commonPopup.show();
    }


    private void startDownloadService(boolean isWifi) {
        boolean flag = ServiceUtils.isServiceRunning(DownloadAFileService.class);

        if (!flag) {
            if (isWifi) {
                showMessage(R.string.wifi_download);
            } else {
                showMessage(R.string.no_wifi_download);
            }
        } else {
            ServiceUtils.stopService(DownloadAFileService.class);
            showMessage(R.string.no_wifi_download);
        }
        logI("DOWNLOAD startDownloadService");
        Intent intent = new Intent(getActivity(), DownloadAFileService.class);
        intent.putExtra("updateUrl", updateUrl);
        intent.putExtra("notes", notes);
        intent.putExtra("apkName", apkName);
        getActivity().startService(intent);

        if (checkForceUpdateBuilder != null) {
            TextView posTv = checkForceUpdateBuilder.getLayout().findViewById(R.id.positive_button);
            posTv.setText(getString(R.string.czur_force_updating));
        }
    }


    private void checkInstallPermission() {
        // 海外版本GooglePlay不需要自动更新
        if (BuildConfig.IS_OVERSEAS) {
            return;
        }

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
            checkNewVersion();
            return;
        }
        String[] permissions = PermissionUtil.getStoragePermission();

        boolean flag = isGranted(permissions);
        if (flag) { //权限开启状态
            checkNewVersion();
        } else { //权限关闭状态
            PermissionUtil.checkPermissionWithDialog(
                    requireContext(),
                    requireActivity().getString(R.string.starry_popupwindow_title),
                    requireActivity().getString(R.string.czur_permission_install),
                    requireActivity().getString(R.string.starry_go_open_permission),
                    requireActivity().getString(R.string.starry_background_start_msg_cancel),
                    v -> {
                        if (v != null) {//点击去设置
                            PermissionUtil.useToolsRequestPermission(
                                    permissions,
                                    this::checkNewVersion
                            );
                        }
                    }
            );
        }
    }

}
