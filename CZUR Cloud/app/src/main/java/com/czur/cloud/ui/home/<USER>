package com.czur.cloud.ui.home;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.entity.realm.HomeCacheEntity;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EquipmentEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.model.IndexEquipmentModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.starry.meeting.dialog.StarryCommonPopup;
import com.czur.cloud.ui.user.UserBindPhoneActivity;
import com.czur.cloud.util.AppClearUtils;
import com.czur.cloud.util.EtUtils;
import com.czur.cloud.util.validator.Validator;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

import io.realm.Realm;
import io.realm.RealmResults;

/**
 * Created by Yz on 2018/2/26.
 * Email：<EMAIL>
 */

public class AddEquipmentActivity extends BaseActivity implements View.OnClickListener {

    private ImageView normalBackBtn;
    private TextView normalTitle;
    private RelativeLayout addEquipmentAuraHasAddedRl;
    private TextView equipmentAuraTv;
    private RelativeLayout addEquipmentEtHasAddedRl;
    private TextView equipmentEtTv;
    private ImageView addEquipmentAuraSelection;
    private ImageView addEquipmentEtSelection;
    private LinearLayout equipmentEtLl;
    private ImageView addEquipmentBooksSelection;
    private TextView choseEquipmentBtn;
    private RelativeLayout addEquipmentBooksHasAddedRl;
    private LinearLayout equipmentAuraLl;
    private TextView equipmentBooksTv;
    private LinearLayout equipmentBooksLl;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private boolean isETS = false;
    private boolean isAuraMate = false;
    private boolean isAura = false;
    private boolean isBooks = false;
    private boolean isET = false;
    private boolean isMirror = false;
    private boolean isStarry = false;
    private boolean isEShare = false;
    private List<String> equipmentNames;
    private Realm realm;
    private List<IndexEquipmentModel> equipmentEntities;

    private LinearLayout equipmentEtsLl;
    private ImageView addEquipmentEtsSelection;
    private RelativeLayout addEquipmentEtsHasAddedRl;
    private TextView equipmentEtsTv;

    private LinearLayout equipmentAuraMateLl;
    private ImageView addEquipmentAuraMateSelection;
    private RelativeLayout addEquipmentAuraMateHasAddedRl;
    private TextView equipmentAuraMateTv;

    private LinearLayout equipmentSittingLl;
    private ImageView addEquipmentSittingSelection;
    private RelativeLayout addEquipmentSittingHasAddedRl;
    private TextView equipmentSittingTv;

    private LinearLayout equipmentStarryLl;
    private ImageView addEquipmentStarrySelection;
    private RelativeLayout addEquipmentStarryHasAddedRl;
    private TextView equipmentStarryTv;

    private LinearLayout equipmentEShareLl;
    private ImageView addEquipmentEShareSelection;
    private RelativeLayout addEquipmentEShareHasAddedRl;
    private TextView equipmentEShareTv;

    // ”添加功能程序“页面功能摆放顺序：1智能扫描仪、2智能坐姿仪、3智能投影仪、4智能陪伴仪、5智能笔记本

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case BIND_PHONE:
                addEquipment();

                break;

            default:
                break;
        }
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_add_equipment);
        initComponent();
        registerEvent();
    }


    private void initComponent() {

        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }

        realm = Realm.getDefaultInstance();
        equipmentEntities = new ArrayList<>();
        equipmentNames = new ArrayList<>();

        addEquipmentAuraHasAddedRl = (RelativeLayout) findViewById(R.id.add_equipment_aura_has_added_rl);
        equipmentAuraTv = (TextView) findViewById(R.id.equipment_aura_tv);
        addEquipmentEtHasAddedRl = (RelativeLayout) findViewById(R.id.add_equipment_et_has_added_rl);
        equipmentEtTv = (TextView) findViewById(R.id.equipment_et_tv);
        addEquipmentBooksHasAddedRl = (RelativeLayout) findViewById(R.id.add_equipment_books_has_added_rl);
        normalBackBtn = (ImageView) findViewById(R.id.normal_back_btn);
        normalTitle = (TextView) findViewById(R.id.normal_title);
        equipmentBooksTv = (TextView) findViewById(R.id.equipment_books_tv);
        addEquipmentAuraSelection = (ImageView) findViewById(R.id.add_equipment_aura_selection);
        addEquipmentEtSelection = (ImageView) findViewById(R.id.add_equipment_et_selection);
        addEquipmentBooksSelection = (ImageView) findViewById(R.id.add_equipment_books_selection);
        choseEquipmentBtn = (TextView) findViewById(R.id.chose_equipment_btn);
        equipmentAuraLl = (LinearLayout) findViewById(R.id.equipment_aura_ll);
        equipmentEtLl = (LinearLayout) findViewById(R.id.equipment_et_ll);
        equipmentBooksLl = (LinearLayout) findViewById(R.id.equipment_books_ll);

        equipmentEtsLl = (LinearLayout) findViewById(R.id.equipment_ets_ll);
        addEquipmentEtsSelection = (ImageView) findViewById(R.id.add_equipment_ets_selection);
        addEquipmentEtsHasAddedRl = (RelativeLayout) findViewById(R.id.add_equipment_ets_has_added_rl);
        equipmentEtsTv = (TextView) findViewById(R.id.equipment_ets_tv);

        equipmentAuraMateLl = (LinearLayout) findViewById(R.id.equipment_aura_mate_ll);
        addEquipmentAuraMateSelection = (ImageView) findViewById(R.id.add_equipment_aura_mate_selection);
        addEquipmentAuraMateHasAddedRl = (RelativeLayout) findViewById(R.id.add_equipment_aura_mate_has_added_rl);
        equipmentAuraMateTv = (TextView) findViewById(R.id.equipment_aura_mate_tv);

        equipmentSittingLl = (LinearLayout) findViewById(R.id.equipment_sitting_ll);
        addEquipmentSittingSelection = (ImageView) findViewById(R.id.add_equipment_sitting_selection);
        addEquipmentSittingHasAddedRl = (RelativeLayout) findViewById(R.id.add_equipment_sitting_has_added_rl);
        equipmentSittingTv = (TextView) findViewById(R.id.equipment_sitting_tv);

        equipmentStarryLl = (LinearLayout) findViewById(R.id.equipment_starry_ll);
        equipmentStarryLl.setVisibility(View.GONE);// 移除starry模块 2024/12/28
        addEquipmentStarrySelection = (ImageView) findViewById(R.id.add_equipment_starry_selection);
        addEquipmentStarryHasAddedRl = (RelativeLayout) findViewById(R.id.add_equipment_starry_has_added_rl);
        equipmentStarryTv = (TextView) findViewById(R.id.equipment_starry_tv);

        equipmentEShareLl = (LinearLayout) findViewById(R.id.equipment_eshare_ll);
        addEquipmentEShareSelection = (ImageView) findViewById(R.id.add_equipment_eshare_selection);
        addEquipmentEShareHasAddedRl = (RelativeLayout) findViewById(R.id.add_equipment_eshare_has_added_rl);
        equipmentEShareTv = (TextView) findViewById(R.id.equipment_eshare_tv);

        if (BuildConfig.IS_OVERSEAS) {
            //   equipmentAuraMateLl.setVisibility(View.GONE);
            equipmentEtsLl.setVisibility(View.GONE);
        }

        httpManager = HttpManager.getInstance();
        userPreferences = UserPreferences.getInstance(this);
        normalTitle.setText(R.string.add_equipment);

    }

    private void registerEvent() {
        equipmentEtsLl.setOnClickListener(this);
        equipmentAuraMateLl.setOnClickListener(this);
        equipmentAuraLl.setOnClickListener(this);
        equipmentEtLl.setOnClickListener(this);
        equipmentBooksLl.setOnClickListener(this);
        equipmentSittingLl.setOnClickListener(this);
        equipmentStarryLl.setOnClickListener(this);
        equipmentEShareLl.setOnClickListener(this);
        normalBackBtn.setOnClickListener(this);
        choseEquipmentBtn.setOnClickListener(this);
        choseEquipmentBtn.setSelected(false);
        choseEquipmentBtn.setClickable(false);
        getHomeListFromCache();
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.equipment_ets_ll:
                selectETS();
                break;
            case R.id.equipment_aura_mate_ll:
                selectAuraMate();
                break;
            case R.id.equipment_aura_ll:
                selectAura();
                break;
            case R.id.equipment_et_ll:
                selectET();
                break;
            case R.id.equipment_books_ll:
                selectBooks();
                break;

            //Jason 20201224 坐姿仪
            case R.id.equipment_sitting_ll:
                selectSitting();
                break;

            //Jason 20210824 投影仪
            case R.id.equipment_starry_ll:
                selectStarry();
                break;

            //Jason 20220106 EShare
            case R.id.equipment_eshare_ll:
                selectEShare();
                break;

            case R.id.chose_equipment_btn:
                addEquipmentCheck();
                break;
            case R.id.normal_back_btn:
                ActivityUtils.finishActivity(AddEquipmentActivity.this);
                break;
            default:
                break;
        }
    }

    /**
     * @des: 添加设备
     * @params:[]
     * @return:void
     */
    private void addEquipmentCheck() {
        final String equipments = EtUtils.transFiles(equipmentNames);

        if (BuildConfig.IS_OVERSEAS){
            // 暂不判断手机号
        }else {
            // 判断是否有Starry，是否绑定手机号？
            if (isStarry) {
                String mobile = UserPreferences.getInstance().getUserMobile();
                if (mobile == null || mobile.isEmpty()) {
//                ToastUtils.showLong(R.string.starry_module_bind_msg);
                    showDlg();
                    return;
                }
            }
        }

        addEquipment();

    }

    private void showDlg(){
        StarryCommonPopup.Builder builder = new StarryCommonPopup.Builder(this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getString(R.string.starry_module_bind_msg));
        builder.setPositiveTitle(getString(R.string.starry_module_bind_ok));
        builder.setNegativeTitle(getString(R.string.starry_module_bind_cancel));
        builder.setOnPositiveListener((dialog, which) -> {
            // 绑定手机号
            Intent i = new Intent(AddEquipmentActivity.this, UserBindPhoneActivity.class);
            ActivityUtils.startActivity(i);
            dialog.dismiss();
        });
        builder.setOnNegativeListener((dialog, which) -> dialog.dismiss());
        StarryCommonPopup dlg = builder.create();
        dlg.setCancelable(false);
        dlg.show();
    }

    private void addEquipment() {
        final String equipments = EtUtils.transFiles(equipmentNames);

        httpManager.request().addEquipment(
                userPreferences.getUserId(), equipments, String.class, new MiaoHttpManager.CallbackNetwork<String>() {
                    @Override
                    public void onStart() {
                        showProgressDialog();
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                        hideProgressDialog();
                        if (equipments.contains("笔记本")) {
                            if (UserPreferences.getInstance(AddEquipmentActivity.this).isValidUser()) {
                                startSyncNow();
                            }
                        } else if (equipments.contains(getString(R.string.AURA_HOME))) {
                            setServerTime();
                            userPreferences.setHasAuraMate(true);
                            startNetty();
                        }
                        else if (equipments.contains(getString(R.string.Mirror))) {
                            userPreferences.setHasAuraMate(true);
                            startNetty();
                        }
                        else if (equipments.contains(getString(R.string.Starry))) {
                            userPreferences.setHasAuraMate(true);
                            startNetty();

                            // 同步一下starry的用户信息
                            try {
                                AppClearUtils.getStarryUserInfo();
                                AppClearUtils.syncStarryUserInfo();
                                Thread.sleep(1000);
                            }catch (Exception e){

                            }
//                            AppClearUtils.getStarryUserAccount();
                        }

                        EventBus.getDefault().post(new EquipmentEvent(EventType.ADD_EQUIPMENT));
                        ActivityUtils.finishActivity(AddEquipmentActivity.this);

                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                        hideProgressDialog();
                        if (entity.getCode() == MiaoHttpManager.STATUS_FAIL) {
                            showMessage(R.string.toast_internal_error);
                        } else if (entity.getCode() == MiaoHttpManager.STATUS_EQUIPMENT_IS_ACTIVED) {
                            showMessage(R.string.equipment_is_activated);
                        } else {
                            showMessage(R.string.starry_network_error_msg);
                        }
                        ActivityUtils.startActivity(AddEquipmentFailedActivity.class);
                    }

                    @Override
                    public void onError(Exception e) {
                        hideProgressDialog();
                        showMessage(R.string.starry_network_error_msg);
                        ActivityUtils.startActivity(AddEquipmentFailedActivity.class);
                    }

                    @Override
                    public void onNoNetwork() {
                        ToastUtils.showShort(R.string.starry_network_error_msg);
                    }
                });

    }

    /**
     * @des: 从数据库中读取缓存的gson
     * @params:
     * @return:
     */

    private void getHomeListFromCache() {
        RealmResults<HomeCacheEntity> homeCacheEntities = realm.where(HomeCacheEntity.class).findAll();
        if (Validator.isNotEmpty(homeCacheEntities)) {
            String homeListGson = homeCacheEntities.get(0).getHomeListGson();
            if (Validator.isNotEmpty(homeListGson)) {
                equipmentEntities = new Gson().fromJson(homeListGson,
                        new TypeToken<List<IndexEquipmentModel>>() {
                        }.getType());
                for (IndexEquipmentModel equipmentEntity : equipmentEntities) {
                    if (equipmentEntity.getKey().equals("笔记本")) {
                        addEquipmentBooksHasAddedRl.setVisibility(View.VISIBLE);
                        equipmentBooksTv.setTextColor(getColor(R.color.black50_22));
                        equipmentBooksLl.setClickable(false);
                    } else if (equipmentEntity.getKey().equals(getString(R.string.ET))) {
                        addEquipmentEtHasAddedRl.setVisibility(View.VISIBLE);
                        equipmentEtTv.setTextColor(getColor(R.color.black50_22));
                        equipmentEtLl.setClickable(false);
                    } else if (equipmentEntity.getKey().equals("Aura")) {
                        addEquipmentAuraHasAddedRl.setVisibility(View.VISIBLE);
                        equipmentAuraTv.setTextColor(getColor(R.color.black50_22));
                        equipmentAuraLl.setClickable(false);
                    } else if (equipmentEntity.getKey().equals(getString(R.string.ETS))) {
                        addEquipmentEtsHasAddedRl.setVisibility(View.VISIBLE);
                        equipmentEtsTv.setTextColor(getColor(R.color.black50_22));
                        equipmentEtsLl.setClickable(false);
                    } else if (equipmentEntity.getKey().equals(getString(R.string.AURA_HOME))) {
                        addEquipmentAuraMateHasAddedRl.setVisibility(View.VISIBLE);
                        equipmentAuraMateTv.setTextColor(getColor(R.color.black50_22));
                        equipmentAuraMateLl.setClickable(false);
                    }else if (equipmentEntity.getKey().equals(getString(R.string.Mirror))) {
                        //Jason 20201224 坐姿仪
                        addEquipmentSittingHasAddedRl.setVisibility(View.VISIBLE);
                        equipmentSittingTv.setTextColor(getColor(R.color.black50_22));
                        equipmentSittingLl.setClickable(false);

                    }else if (equipmentEntity.getKey().equals(getString(R.string.Starry))) {
                        //Jason 20210824 投影仪
                        addEquipmentStarryHasAddedRl.setVisibility(View.VISIBLE);
                        equipmentStarryTv.setTextColor(getColor(R.color.black50_22));
                        equipmentStarryLl.setClickable(false);

                    }else if (equipmentEntity.getKey().equals(getString(R.string.EShare))) {
                        //Jason 20220106 无线投屏
                        addEquipmentEShareHasAddedRl.setVisibility(View.VISIBLE);
                        equipmentEShareTv.setTextColor(getColor(R.color.black50_22));
                        equipmentEShareLl.setClickable(false);
                    }

                }
            }
        }
    }


    /**
     * @des: 选中Aura
     * @params:
     * @return:
     */

    private void selectAura() {
        isAura = !isAura;
        if (isAura) {
            addEquipmentAuraSelection.setVisibility(View.VISIBLE);
            equipmentNames.add("Aura");
        } else {
            addEquipmentAuraSelection.setVisibility(View.GONE);
            equipmentNames.remove("Aura");
        }
        canButtonClick();

    }

    /**
     * @des: 选中Aura
     * @params:
     * @return:
     */

    private void selectETS() {
        isETS = !isETS;
        if (isETS) {
            addEquipmentEtsSelection.setVisibility(View.VISIBLE);
            equipmentNames.add(getString(R.string.ETS));
        } else {
            addEquipmentEtsSelection.setVisibility(View.GONE);
            equipmentNames.remove(getString(R.string.ETS));
        }
        canButtonClick();
    }

    private void selectAuraMate() {
        isAuraMate = !isAuraMate;
        if (isAuraMate) {
            addEquipmentAuraMateSelection.setVisibility(View.VISIBLE);
            equipmentNames.add(getString(R.string.AURA_HOME));
        } else {
            addEquipmentAuraMateSelection.setVisibility(View.GONE);
            equipmentNames.remove(getString(R.string.AURA_HOME));
        }
        canButtonClick();

    }

    private void canButtonClick() {
        if (BuildConfig.IS_OVERSEAS) {
            if (isAura || isBooks || isET || isAuraMate || isMirror|| isStarry || isEShare) {
                choseEquipmentBtn.setSelected(true);
                choseEquipmentBtn.setClickable(true);
            } else {
                choseEquipmentBtn.setSelected(false);
                choseEquipmentBtn.setClickable(false);
            }
        } else {
            if (isAura || isBooks || isET || isETS || isAuraMate || isMirror|| isStarry || isEShare) {
                choseEquipmentBtn.setSelected(true);
                choseEquipmentBtn.setClickable(true);
            } else {
                choseEquipmentBtn.setSelected(false);
                choseEquipmentBtn.setClickable(false);
            }
        }
    }

    /**
     * @des: 选中Books
     * @params:
     * @return:
     */

    private void selectBooks() {
        isBooks = !isBooks;
        if (isBooks) {
            addEquipmentBooksSelection.setVisibility(View.VISIBLE);
            equipmentNames.add("笔记本");
        } else {
            addEquipmentBooksSelection.setVisibility(View.GONE);
            equipmentNames.remove("笔记本");
        }
        canButtonClick();
    }

    /**
     * @des: 选中坐姿仪
     * @params:
     * @return:
     */
    private void selectSitting() {
        isMirror = !isMirror;
        if (isMirror) {
            addEquipmentSittingSelection.setVisibility(View.VISIBLE);
            equipmentNames.add(getString(R.string.Mirror));
        } else {
            addEquipmentSittingSelection.setVisibility(View.GONE);
            equipmentNames.remove(getString(R.string.Mirror));
        }
        canButtonClick();
    }

    /**
     * @des: 选中投影仪
     */
    private void selectStarry() {
        isStarry = !isStarry;
        if (isStarry) {
            addEquipmentStarrySelection.setVisibility(View.VISIBLE);
            equipmentNames.add(getString(R.string.Starry));
        } else {
            addEquipmentStarrySelection.setVisibility(View.GONE);
            equipmentNames.remove(getString(R.string.Starry));
        }
        canButtonClick();
    }

    /**
     * @des: 选中无线投屏
     */
    private void selectEShare() {
        isEShare = !isEShare;
        if (isEShare) {
            addEquipmentEShareSelection.setVisibility(View.VISIBLE);
            equipmentNames.add(getString(R.string.EShare));
        } else {
            addEquipmentEShareSelection.setVisibility(View.GONE);
            equipmentNames.remove(getString(R.string.EShare));
        }
        canButtonClick();
    }

    /**
     * @des: 选中ET
     * @params:
     * @return:
     */

    private void selectET() {
        isET = !isET;
        if (isET) {
            addEquipmentEtSelection.setVisibility(View.VISIBLE);
            equipmentNames.add(getString(R.string.ET));
        } else {
            addEquipmentEtSelection.setVisibility(View.GONE);
            equipmentNames.remove(getString(R.string.ET));
        }
        canButtonClick();
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        realm.close();

        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }

    }
}
