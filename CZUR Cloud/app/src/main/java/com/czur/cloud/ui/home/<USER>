package com.czur.cloud.ui.home;

import static com.czur.cloud.util.AppClearUtils.logoutAndCleanData;
import static com.czur.czurutils.log.CZURLogUtilsKt.logE;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.Manifest;
import android.app.Activity;
import android.app.Notification;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ShortcutInfo;
import android.content.pm.ShortcutManager;
import android.content.res.Configuration;
import android.graphics.drawable.Icon;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.WindowInsets;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;

import com.badoo.mobile.util.WeakHandler;
import com.blankj.utilcode.constant.PermissionConstants;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.ServiceUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.blankj.utilcode.util.Utils;
import com.blankj.utilcode.util.UtilsTransActivity;
import com.clj.fastble.BleManager;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.event.AuraMateCheckVideoEvent;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.LogoutEvent;
import com.czur.cloud.event.StarryClipboardEvent;
import com.czur.cloud.event.StarryCommonEvent;
import com.czur.cloud.event.StopServiceEvent;
import com.czur.cloud.event.StopSyncTimeCountEvent;
import com.czur.cloud.event.UpdateEvent;
import com.czur.cloud.event.vendorPush.VendorPushRegIDEvent;
import com.czur.cloud.model.CountryCode;
import com.czur.cloud.model.CountryList;
import com.czur.cloud.model.IndexEquipmentModel;
import com.czur.cloud.model.MissedCallModel;
import com.czur.cloud.model.RegisterModel;
import com.czur.cloud.netty.CZURMessageConstants;
import com.czur.cloud.netty.core.CZURTcpClient;
import com.czur.cloud.netty.observer.NettyService;
import com.czur.cloud.netty.observer.NettyUtils;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.FirstPreferences;
import com.czur.cloud.preferences.StarryPreferences;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.account.LoginActivity;
import com.czur.cloud.ui.auramate.AuraMateActivity;
import com.czur.cloud.ui.auramate.AuraMatePreRemoteVideoActivity;
import com.czur.cloud.ui.auramate.AuraMateReportActivity;
import com.czur.cloud.ui.auramate.AuraMateReportNewActivity;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.base.CzurCloudApplication;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.market.MallFragment;
import com.czur.cloud.ui.mirror.comm.FastBleOperationUtils;
import com.czur.cloud.ui.starry.activity.StarryActivity;
import com.czur.cloud.ui.starry.common.StarryConstants;
import com.czur.cloud.ui.starry.livedatabus.BlankActivityForJoinMeeting;
import com.czur.cloud.ui.starry.livedatabus.BlankActivityForNotification;
import com.czur.cloud.ui.starry.livedatabus.ShortCutTrampolineActivity;
import com.czur.cloud.ui.starry.meeting.MeetingMainActivity;
import com.czur.cloud.ui.starry.meeting.dialog.RemindChoseCountryPopup;
import com.czur.cloud.ui.starry.meeting.dialog.StarryCommonPopup;
import com.czur.cloud.ui.starry.meeting.model.MeetingModel;
import com.czur.cloud.ui.starry.utils.ClipboardUtiKt;
import com.czur.cloud.ui.starry.utils.Tools;
import com.czur.cloud.ui.starry.viewmodel.StarryViewModel;
import com.czur.cloud.ui.user.UserFragment;
import com.czur.cloud.ui.user.download.DownloadAFileService;
import com.czur.cloud.util.AppClearUtils;
import com.czur.cloud.util.NotifyUtil;
import com.czur.cloud.util.PermissionUtil;
import com.czur.cloud.util.validator.Validator;
import com.czur.cloud.vendorPush.VendorPushTask;
import com.google.gson.reflect.TypeToken;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * Created by Yz on 2018/2/15
 * Email：<EMAIL>
 */

public class IndexActivity extends BaseActivity implements View.OnClickListener {
    private Handler mHandler = new Handler(Looper.getMainLooper());
    private RelativeLayout userTabLl;
    private ImageView userTabImg;
    private LinearLayout mallTabLl;
    private ImageView mallTabImg;
    private LinearLayout homeTabLl;
    private ImageView homeTabImg;
    private TextView homeTabTv;
    private TextView mallTabTv;
    private TextView mineTabTv;
    private ImageView mineTabRedPoint;

    private IndexFragmentAttacher indexFragmentAttacher;
    private int currentShowFragmentIndex;
    private int willShowFragmentIndex;
    private Fragment showFragment;
    private long exitTime = 0;
    private FirstPreferences firstPreferences;
    private boolean needSync;
    private UserPreferences userPreferences;
    private Intent intent;
    private String missedCall_call_id, missedCall_udid_from;
    private boolean isMissedCall30s = false;

    private int retryReConnectTimes = 0;     // 重连次数；

    public static boolean isJumpToApp = false;        // 从外部跳转到app的

    // 是否有Starry模块？
    private boolean isStarryModuel = false;
    private RemindChoseCountryPopup choseCountryPopup;
    private ArrayList<CountryCode> countryList;
    private CountryCode selectCountryCode;
    private StarryCommonPopup remindMissedMeetingPopup;
    private StarryViewModel starryViewModel;

    // 标识是否已经运行了一次,避免重复运行,(弹出2次入会框),5秒后恢复
    private Boolean isOneTimesRunning = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_index);
        // 3.2.0单独处理，清除本地数据，重新登录一下，解决数据库升级问题
        if (UserPreferences.getInstance().isFirstInVersion408()) {
            logoutAndCleanData(getApplication());
        }
//        configShortCuts();
        CzurCloudApplication.isOtherLogin = false;

        // Android 16适配
        initAndroid16Adaptation();

        initComponent(savedInstanceState);

        checkMissedCall30s();

        checkMissedMeetingObserve();
//        requestPhonePermission(this);
        // 检查是否为Android13，通知权限申请
        checkAndShowPermission();

        if (!BuildConfig.IS_OVERSEAS) {
            try {
                VendorPushTask.INSTANCE.on();
            } catch (Exception e) {
                // 非法进入，从头开始
                logI("IndexActivity.非法进入，从头开始 ");
                ActivityUtils.finishActivity(this);
                ActivityUtils.startActivity(WelcomeActivity.class);
                return;
            }
        }

        checkEventBusStickyMsg();

        RelativeLayout bgRl = (RelativeLayout)findViewById(R.id.index_bg_rl);
        bgRl.setOnApplyWindowInsetsListener(new View.OnApplyWindowInsetsListener() {
            @NonNull
            @Override
            public WindowInsets onApplyWindowInsets(@NonNull View v, @NonNull WindowInsets insets) {
                if (Build.VERSION.SDK_INT > Build.VERSION_CODES.Q) {
                    v.setPadding(v.getPaddingLeft(),v.getPaddingTop(),v.getPaddingRight(),insets.getSystemWindowInsets().bottom);
                }
                return insets;
            }
        });
    }


    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        this.intent = intent;

        // 会议中，直接进入会议！！！
        if (Boolean.TRUE.equals(MeetingModel.isInMeeting.getValue())) {
            logI("IndexActivity.onResume().MeetingModel.isInMeeting=true");
            Intent intent1 = new Intent(IndexActivity.this, MeetingMainActivity.class);
            intent1.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP);
            ActivityUtils.startActivity(intent1);
        } else {
            handlePushMessage();
        }
    }

    //语言变化 用于修改推送语言
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (BuildConfig.IS_OVERSEAS) {
//            ServiceUtils.stopService(NettyService.class);
            NettyUtils.getInstance().stopNettyService();
            Context applicationContext = Utils.getApp().getApplicationContext();
            UserPreferences userPreferences = UserPreferences.getInstance(applicationContext);
            if (userPreferences.isUserLogin() && userPreferences.isHasAuraMate()) {
                logI("IndexActivity.onConfigurationChanged.NettyService.class");
                NettyUtils.getInstance().startNettyService();
            }
        }

    }

    /**
     * 处理通知跳转
     */
    private void handlePushMessage() {
        logI("handlePushMessage====");
        Uri data = getIntent().getData();
        logI("IndexActivity.data = " + data);

        // 判断一下,只需要在5秒内执行一次即可.
        if (isOneTimesRunning) {
            return;
        } else {
            isOneTimesRunning = true;
            new Handler().postDelayed(
                    () -> isOneTimesRunning = false
                    , 5000);
        }

        if (!userPreferences.isUserLogin()) {
            if (data != null) {
                isJumpToApp = true;
                // starry://com.czur.cloud:9007?uuid=049f0f4510394444a3ce5747d2b9f3f3
//                IndexActivity.meetingUUID = data.getQueryParameter("uuid");
            }
            ActivityUtils.finishAllActivities();
            ActivityUtils.startActivity(LoginActivity.class);
        } else {
            if (data != null) {
                isJumpToApp = true;
                // starry://com.czur.cloud:9007?uuid=049f0f4510394444a3ce5747d2b9f3f3
                String meetingUUID = data.getQueryParameter("uuid");

                if (meetingUUID != null && !meetingUUID.equals("")) {
                    // 判断一下是否为手机号用户？
                    String mobile = UserPreferences.getInstance().getUserMobile();
                    if (Validator.isEmpty(mobile)) {
                        // 1.非手机号用户，toast提示：请添加Starry Meeting后开始会议
                        ToastUtils.showLong(R.string.starry_no_mobile);
                        isJumpToApp = false;
                    } else {
                        // 2.手机号用户：正常按点击连接/复制信息出现的入会窗口提示，同时将Starry模块添加至个人主页。
                        Intent defaultIntent = new Intent(IndexActivity.this, BlankActivityForJoinMeeting.class);
                        defaultIntent.putExtra(StarryConstants.STARRY_BLANK_CLIPBOARD_TYPE, StarryConstants.STARRY_BLANK_TYPE_WEB);
                        defaultIntent.putExtra(StarryConstants.STARRY_BLANK_CLIPBOARD_UUID, meetingUUID);
                        ActivityUtils.startActivity(defaultIntent);
                    }
                }

                // 判断一下是否是快捷菜单跳转过来的
                // 快捷菜单 跳转不进来,data = null
                String shortCutData = data.getQueryParameter("eshareEmptyType");
                if (shortCutData != null && shortCutData.equals(StarryConstants.STARRY_EMPTY_TYPE_JOIN)) {
                    // 判断一下是否为手机号用户？
                    String mobile = UserPreferences.getInstance().getUserMobile();
                    if (Validator.isEmpty(mobile)) {
                        // 1.非手机号用户，toast提示：请添加Starry Meeting后开始会议
                        ToastUtils.showLong(R.string.starry_no_mobile);
                        isJumpToApp = false;
                    } else {
                        // 2.手机号用户
//                        Intent defaultIntent = new Intent(IndexActivity.this, BlankActivityForJoinMeeting.class);
                        Intent defaultIntent = new Intent(IndexActivity.this, StarryActivity.class);
                        defaultIntent.putExtra(StarryConstants.STARRY_BLANK_CLIPBOARD_TYPE, StarryConstants.STARRY_BLANK_TYPE_SHORTCUT);
                        ActivityUtils.startActivity(defaultIntent);
                    }
                }

            }

            if (intent.hasExtra("msgType")) {
                String msgType = intent.getStringExtra("msgType");
                logI("msgType", msgType);
                getEquipmentList(msgType);
            } else {
                //已进入app就检查一下，当前30s内是否有视频通话请求
                if (!BuildConfig.IS_OVERSEAS) {
                    checkMissedCall30s();
                }
            }
        }
    }

    /**
     * 收到push接听电话
     *
     * @param context
     * @param callId
     * @param udidFrom
     * @param isTransfer
     */
    private void openCamera(Context context, String callId, String udidFrom, boolean isTransfer) {
        PermissionUtils.permission(PermissionConstants.CAMERA, PermissionConstants.MICROPHONE)
                .rationale(new PermissionUtils.OnRationaleListener() {
                    @Override
                    public void rationale(UtilsTransActivity activity, ShouldRequest shouldRequest) {
                        ToastUtils.showShort(R.string.denied_camera);
                        shouldRequest.again(true);
                    }
                })
                .callback(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(List<String> permissionsGranted) {
                        Intent intent = new Intent(context, AuraMatePreRemoteVideoActivity.class);
                        intent.putExtra("isCallIn", true);
                        intent.putExtra("isTransfer", isTransfer);
                        intent.putExtra("callId", callId);
                        intent.putExtra("udidFrom", udidFrom);
                        intent.putExtra("equipmentId", udidFrom);
                        ActivityUtils.startActivity(intent);
                    }

                    @Override
                    public void onDenied(List<String> permissionsDeniedForever,
                                         List<String> permissionsDenied) {
                        ToastUtils.showShort(R.string.denied_camera);
                    }
                })
                .theme(new PermissionUtils.ThemeCallback() {
                    @Override
                    public void onActivityCreate(Activity activity) {
                        ScreenUtils.setFullScreen(activity);
                    }
                })
                .request();
    }

    @Override
    protected void onResume() {
        super.onResume();

        // 会议中，直接进入会议！！！
        if (Boolean.TRUE.equals(MeetingModel.isInMeeting.getValue())) {
            logI("IndexActivity.onResume().MeetingModel.isInMeeting=true");
            Intent intent = new Intent(IndexActivity.this, MeetingMainActivity.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP);
            ActivityUtils.startActivity(intent);
        }

    }

    private void initComponent(Bundle savedInstanceState) {
        intent = getIntent();
        userPreferences = UserPreferences.getInstance(this);
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }

        // 初始化会议密码
        if (StarryPreferences.getInstance().getLastShowPwd()) {
            if (StarryPreferences.getInstance().getLastPwd().equals("")) {
                String pwd = Tools.onRandMeetingPassword();
                StarryPreferences.getInstance().setLastPwd(pwd);
            }
        }

        needSync = getIntent().getBooleanExtra("needSync", true);
        firstPreferences = FirstPreferences.getInstance(this);
        firstPreferences.setIsAuraMateFwPrompt(true);
        getSharedPreferences("update_equipment_uid_sp", Context.MODE_PRIVATE).edit().clear().apply();
        homeTabLl = findViewById(R.id.home_tab_ll);
        homeTabImg = findViewById(R.id.home_tab_img);
        mallTabLl = findViewById(R.id.mall_tab_ll);
        mallTabImg = findViewById(R.id.mall_tab_img);
        userTabLl = findViewById(R.id.mine_tab_ll);
        userTabImg = findViewById(R.id.user_tab_img);
        homeTabTv = findViewById(R.id.home_tab_tv);
        mallTabTv = findViewById(R.id.mall_tab_tv);
        mineTabTv = findViewById(R.id.user_tab_tv);
        mineTabRedPoint = findViewById(R.id.mine_tab_red_point);

        initIndexFragment(savedInstanceState);

        registerEvent();

    }


    public boolean getNeedSync() {
        return needSync;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        logI("IndexActivity.onEvent=" + event.getEventType());
        switch (event.getEventType()) {

//            // Token失效,重新连接
//            case STARRY_RESULT_CODE_INVALID_TOKEN:
//                ActivityUtils.finishActivity(this);
//                ActivityUtils.startActivity(WelcomeActivity.class);

            // 快捷菜单跳转
//            case SHORTCUT_JOIN_MEET:
////                Intent defaultIntent = new Intent(IndexActivity.this, BlankActivityForJoinMeeting.class);
//                if (!CzurCloudApplication.isJoinedMeeting) {
//                    Intent defaultIntent = new Intent(IndexActivity.this, StarryActivity.class);
//                    defaultIntent.putExtra(StarryConstants.STARRY_BLANK_CLIPBOARD_TYPE, StarryConstants.STARRY_BLANK_TYPE_SHORTCUT);
//                    ActivityUtils.startActivity(defaultIntent);
//                }
//                break;

            case HAS_NO_FORCE_NEW_VERSION://没有强制更新的时候才连接长连接与其他短连接初始化
                initializeWhenThereIsNoForcedUpdate();
                break;
            // 放置后台收到来电
            case STARRY_MESSAGE_CALL_VIDEO_IN_BACKGROUND:
                if (event instanceof StarryCommonEvent) {
                    String nickname_from = ((StarryCommonEvent) event).getParams();
                    fullScreenNotify(nickname_from);

                }
                break;
            case AURAMATE_MESSAGE_CALL_VIDEO_IN_BACKGROUND:
                if (event instanceof StarryCommonEvent) {
                    int channelId = (int) SystemClock.uptimeMillis();
                    int smallIcon = R.mipmap.small_icon;
                    String ticker = getResources().getString(R.string.auramate_new_message);
                    String title = getResources().getString(R.string.auramate_video_call_in_title);
                    String content = getResources().getString(R.string.auramate_video_call_in_text);
                    Intent fullScreenIntent = new Intent(this, BlankActivityForNotification.class);
                    fullScreenIntent.putExtra(BlankActivityForNotification.INTENT_ACTION_TYPE, BlankActivityForNotification.AURA_MATE_VIDEO_IN_TYPE);

                    PendingIntent fullScreenPendingIntent;
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                        fullScreenPendingIntent = PendingIntent.getActivity(this, 0, fullScreenIntent, PendingIntent.FLAG_IMMUTABLE | PendingIntent.FLAG_UPDATE_CURRENT);
                    } else {
                        fullScreenPendingIntent = PendingIntent.getActivity(this, 0, fullScreenIntent, PendingIntent.FLAG_UPDATE_CURRENT);
                    }
                    AppUtils.relaunchApp();
                    NotifyUtil notify = new NotifyUtil(this, channelId);
                    logI("IndexActivity.showAuramateNotification");
                    notify.notifyNormalSingline(fullScreenPendingIntent, smallIcon, ticker, title, content, true, true, false);
                }
                break;
            case STARRY_NO_INCOMING_CALL:
                checkMissedMeeting();
                break;
            // 加入企业邀请通知消息
            case STARRY_COMPANY_NOTICE:
                // ②收到会议邀请，加入会议，自动添加到功能列表
                EventBus.getDefault()
                        .post(new StarryCommonEvent(EventType.STARRY_EQUIPMENTLIST_CHECK_ADD, ""));
                break;

            // 会议中跳转入会
            case STARRY_MEETING_REJOIN_FROM_WEB:
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        StarryActivity.onJoinMeetingFromWeb(IndexActivity.this);
                    }
                }, 1500);
                break;

            // 检查是否添加了starry模块
            case STARRY_EQUIPMENTLIST_CHECK_ADD:
                logI("IndexActivity.STARRY_EQUIPMENTLIST_CHECK_ADD");
                onAddStarryModuel();
                break;

            // 他端注销了
            case DE_REGISTER:
                logI("IndexActivity.DE_REGISTER.DE_REGISTER");
                AppClearUtils.requestUserInfo();
                ToastUtils.showLong(R.string.starry_account_allready_delete);
                break;

            // starry读取剪贴板内容
            case STARRY_MEETING_CLIPBOARD:
                // 获取剪贴板内容
                if (!CzurCloudApplication.isJoinedMeeting) {
                    getClipboardContentDelay();
                }

                // 退出登录
            case LOG_OUT:
                // 停止长连接
                // 清理starry信息
                break;

            // Starry设备掉线
            case STARRY_DEVICE_OFFLINE:
                logI("IndexActivity.Starry设备掉线");
                // 重接长连接，延迟连接长连接
                retryReConnectTimes = 0;
                reTryConnectLong();
                break;

            case STARRY_DEVICE_ONLINE:
                logI("IndexActivity.Starry设备上线");
                // 重接长连接
                retryReConnectTimes = 0;
                reTryConnectLong();

                break;

            // 单点登录， 在其他设备登录
            case STARRY_NOTICE_OTHER_DEVICE_LOGIN:
                // 触发一次短连接,会自动解析返回的1111
                AppClearUtils.requestUserInfo();
                break;

            case IS_LATEST_VERSION:
            case FIRST_CLICK_NEW:
                if (event instanceof UpdateEvent) {
                    mineTabRedPoint.setVisibility(View.GONE);
                }
                break;
            case HAS_NEW_VERSION:
                // 海外版本GooglePlay不需要自动更新
                if (BuildConfig.IS_OVERSEAS) {
                    mineTabRedPoint.setVisibility(View.GONE);
                    return;
                }

                if (event instanceof UpdateEvent) {
                    if (firstPreferences.isFirstNewVersion()) {
                        mineTabRedPoint.setVisibility(View.VISIBLE);
                    } else {
                        mineTabRedPoint.setVisibility(View.GONE);
                    }
                }
                break;

            case HAS_NEW_VERSION_OK:
                FastBleOperationUtils.setIsAPPUpdateOK(true);
                break;

            case CHECK_VIDEO_REQUEST_ACTIVE:
                logI("IndexActivity.onEvent.isMissedCall30s=" + isMissedCall30s);
                // 30秒内的通话处理
                if (isMissedCall30s) {
                    isMissedCall30s = false;
                    int is_request_active = ((AuraMateCheckVideoEvent) event).getIsRequestActive();
                    if (is_request_active == 1) {
                        openCamera(IndexActivity.this, missedCall_call_id,
                                missedCall_udid_from,
                                false);
                    }
                    break;
                }
                if (event instanceof AuraMateCheckVideoEvent) {
                    int is_request_active = ((AuraMateCheckVideoEvent) event).getIsRequestActive();
                    if (is_request_active == 1) {
                        openCamera(IndexActivity.this, intent.getStringExtra("call_id"),
                                intent.getStringExtra("udid_from"),
                                intent.getStringExtra("action").equals(CZURMessageConstants.MessageName.MESSAGE_CALL_VIDEO_TRANSFER.getMsg()));
                    } else if (is_request_active == 0) {
                        showLongMessage(getString(R.string.call_end));
                    } else if (is_request_active == -1) {
                        showLongMessage(getString(R.string.aura_mate_offline));
                    }
                }
                break;
            default:
                break;
        }
    }


    /**
     * @des: 初始化Fragment
     * @params:[savedInstanceState]
     * @return:void
     */
    private void initIndexFragment(Bundle savedInstanceState) {
        FragmentManager fm = getSupportFragmentManager();
        indexFragmentAttacher = new IndexFragmentAttacher(fm);
        if (savedInstanceState != null) {
            for (int i = 0; i < 3; i++) {
                Fragment fragment = indexFragmentAttacher.getFragment(i);
                if (Validator.isNotEmpty(fragment)) {
                    fm.beginTransaction().hide(fragment).commit();
                }
            }
        }
        changeDefaultPage();
    }


    private void getEquipmentList(String msgType) {
        HttpManager.getInstance().request().getEquipmentList(
                userPreferences.getUserId(), new TypeToken<List<IndexEquipmentModel>>() {
                }.getType(), new MiaoHttpManager.CallbackNetwork<IndexEquipmentModel>() {
                    @Override
                    public void onNoNetwork() {

                    }

                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<IndexEquipmentModel> entity) {
                        List<IndexEquipmentModel> models = entity.getBodyList();
                        if (models != null && models.size() > 0) {
                            for (IndexEquipmentModel model : models) {

                                // 是否添加了starry模块？
                                if (model != null && model.getKey() != null && model.getKey().equals(getString(R.string.Starry))) {
                                    isStarryModuel = true;
                                }

                                //是否添加了 AuraMate 模块
                                if (model != null && model.getKey() != null && model.getKey().equals(getString(R.string.AURA_HOME))) {
                                    switch (msgType) {
                                        case "FILE":
                                            Intent fileIntent = new Intent(IndexActivity.this, AuraMateActivity.class);
                                            fileIntent.putExtra("device", intent.getStringExtra("device"));
                                            fileIntent.putExtra("relationId", intent.getStringExtra("relationId"));
                                            ActivityUtils.startActivity(fileIntent);
                                            break;
                                        case "REPORT":
                                            Intent reportIntent = null;
                                            //海外暂时没有日周月报
                                            if (BuildConfig.IS_OVERSEAS) {
                                                reportIntent = new Intent(IndexActivity.this, AuraMateReportActivity.class);
                                            } else {
                                                reportIntent = new Intent(IndexActivity.this, AuraMateReportNewActivity.class);
                                            }
                                            reportIntent.putExtra("equipmentId", intent.getStringExtra("device"));
                                            ActivityUtils.startActivity(reportIntent);
                                            break;

                                        case "DAY_REPORT":
                                            //如果有reportid和type，则直接跳转到日报界面
                                            String reportId = intent.getStringExtra("reportId");
                                            String type = intent.getStringExtra("type");

                                            Intent reportIntent1 = new Intent(IndexActivity.this, AuraMateReportNewActivity.class);
                                            reportIntent1.putExtra("equipmentId", intent.getStringExtra("device"));
                                            if (Validator.isNotEmpty(reportId) && Validator.isNotEmpty(type)) {
                                                reportIntent1.putExtra("reportId", reportId);
                                                reportIntent1.putExtra("type", type);
                                            }
                                            ActivityUtils.startActivity(reportIntent1);
                                            break;

                                        case "CALL_VIDEO":
                                        case "CALL_VIDEO_T":
                                            new Handler().postDelayed(new Runnable() {
                                                @Override
                                                public void run() {
                                                    CZURTcpClient.getInstance().checkVideoRequestActive(IndexActivity.this, intent.getStringExtra("call_id"), intent.getStringExtra("udid_from"));
                                                }
                                            }, 2000);
                                            break;
                                        default:
                                            Intent defaultIntent = new Intent(IndexActivity.this, AuraMateActivity.class);
                                            defaultIntent.putExtra("device", intent.getStringExtra("device"));
                                            ActivityUtils.startActivity(defaultIntent);
                                            break;
                                    }
                                    break;
                                }
                            }
                        }

                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<IndexEquipmentModel> entity) {

                    }

                    @Override
                    public void onError(Exception e) {
                    }
                });
    }

    private void registerEvent() {
        userTabLl.setOnClickListener(this);
        mallTabLl.setOnClickListener(this);
        homeTabLl.setOnClickListener(this);

    }

    private void changeDefaultPage() {
        changeTabIcon(0);
        showFragment = indexFragmentAttacher.showFragment(0);
        currentShowFragmentIndex = 0;
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.home_tab_ll:
                willShowFragmentIndex = 0;
                changeFragment(0);
                changeTabIcon(0);
                HomeFragment homeFragment = (HomeFragment) indexFragmentAttacher.getFragment(0);
                homeFragment.hasEquipment();
                break;
            case R.id.mall_tab_ll:
                willShowFragmentIndex = 1;
                changeFragment(1);
                changeTabIcon(1);
                break;
            case R.id.mine_tab_ll:
                BarUtils.setStatusBarLightMode(getWindow(), false);
                willShowFragmentIndex = 2;
                changeFragment(2);
                changeTabIcon(2);
                setStatusBarColor(R.color.black_2a);
                BarUtils.setStatusBarLightMode(getWindow(), false);
                break;
            default:
                break;
        }
    }

    /**
     * @des: 选择主页tab
     * @params:[index]
     * @return:
     */

    private void changeTabIcon(int index) {
        switch (index) {
            case 0:
                homeTabImg.setSelected(true);
                mallTabImg.setSelected(false);
                userTabImg.setSelected(false);

                homeTabTv.setSelected(true);
                mallTabTv.setSelected(false);
                mineTabTv.setSelected(false);
                break;

            case 1:
                homeTabImg.setSelected(false);
                mallTabImg.setSelected(true);
                userTabImg.setSelected(false);

                homeTabTv.setSelected(false);
                mallTabTv.setSelected(true);
                mineTabTv.setSelected(false);
                break;

            case 2:
                homeTabImg.setSelected(false);
                mallTabImg.setSelected(false);
                userTabImg.setSelected(true);
                homeTabTv.setSelected(false);
                mallTabTv.setSelected(false);
                mineTabTv.setSelected(true);
                break;
            default:
                break;
        }
    }

    /**
     * @des: 切换tab
     * @params:[index]
     * @return:
     */

    public void changeFragment(int id) {

        showFragment = indexFragmentAttacher.showFragment(willShowFragmentIndex, currentShowFragmentIndex);
        currentShowFragmentIndex = willShowFragmentIndex;
    }

    /**
     * @des: AuraMateFragment 返回逻辑
     * @params:
     * @return:
     */

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (showFragment instanceof MallFragment) {
                boolean canWebViewGoBack = ((MallFragment) showFragment).onKeyDown(keyCode, event);
                //如果WebView可以返回
                if (canWebViewGoBack) {
                    canWebViewGoBack = ((MallFragment) showFragment).onKeyDown(keyCode, event);
                    return super.onKeyDown(keyCode, event);
                } else {
                    //WebView不可以返回 退出APP
                    exitApp();
                    return false;
                }
            } else {
                //不是购买Fragment 退出APP
                exitApp();
                return false;
            }
        }
        return super.onKeyDown(keyCode, event);
    }

    /**
     * @des: 退出App
     * @params:
     * @return:
     */

    private void exitApp() {
        if ((System.currentTimeMillis() - exitTime) > 2000) {
            showMessage(R.string.confirm_exit);
            exitTime = System.currentTimeMillis();
        } else {
            ActivityUtils.finishAllActivities();
        }

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        NetworkUtils.unregisterNetworkStatusChangedListener(CzurCloudApplication.onNetworkStatusChangedListener);
        if (ServiceUtils.isServiceRunning(DownloadAFileService.class)) {
//            ServiceUtils.stopService(DownloadApkService.class);
        }

        FastBleOperationUtils.setIsAPPUpdateOK(true);
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }

        BleManager.getInstance().disconnectAllDevice();
        BleManager.getInstance().destroy();

        // 记录退出app的时间
        String lastTime = Tools.getNowDateTime();
        StarryPreferences.getInstance().setLastCloseAppTime(lastTime);
        mHandler.removeCallbacksAndMessages(null);
    }

    private void checkMissedCall30s() {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<String>() {

            @Override
            public String doInBackground() throws Throwable {
                List<MissedCallModel> callList = getMissedCall30s();
                if (Validator.isNotEmpty(callList)) {
                    missedCall_call_id = callList.get(0).getCallId();
                    missedCall_udid_from = callList.get(0).getEquipmentUuid();

                    intent.putExtra("call_id", missedCall_call_id);
                    intent.putExtra("udid_from", missedCall_udid_from);
                    String msgType = "CALL_VIDEO";
                    isMissedCall30s = true;
                    getEquipmentList(msgType);

                }
                return "success";
            }

            @Override
            public void onSuccess(String result) {
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
            }
        });
    }

    /**
     * @des: 获取30秒内是否有通话
     * @params:[]
     * @return:void
     */
    private List<MissedCallModel> getMissedCall30s() {
        if (!NetworkUtils.isConnected()) {
            return null;
        }
        try {
            final MiaoHttpEntity<MissedCallModel> reportEntity = HttpManager.getInstance().request().getMissedCall30s(
                    userPreferences.getUserId(),
                    new TypeToken<List<MissedCallModel>>() {
                    }.getType());
            if (reportEntity == null) {
                return null;
            }
            if (reportEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return reportEntity.getBodyList();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private NetworkUtils.OnNetworkStatusChangedListener networkStatusChangedListener = new NetworkUtils.OnNetworkStatusChangedListener() {
        @Override
        public void onDisconnected() {
            logI("IndexActivity.initNetListener.onDisconnected.isConnected()=" + NetworkUtils.isConnected());
        }

        @Override
        public void onConnected(NetworkUtils.NetworkType networkType) {
            logI("IndexActivity.initNetListener.onConnected.isConnected()=" + NetworkUtils.isConnected());
            // 重接长连接
            retryReConnectTimes = 0;
            reTryConnectLong();
        }
    };

    private void initNetListener() {
        // 这里没太看明白是否需要取消这个监听,目前没有发现其他问题, 先这样吧
        if (!NetworkUtils.isRegisteredNetworkStatusChangedListener(networkStatusChangedListener)) {
            NetworkUtils.registerNetworkStatusChangedListener(networkStatusChangedListener);
        }
    }

    // 重连长连接
    private void reTryConnectLong() {
        if (NetworkUtils.isConnected()) {
//            startNettyStarry();
            logI("IndexActivity.reTryConnectLong.startNettyStarryService.retryReConnectTimes=" + retryReConnectTimes);

            if (ServiceUtils.isServiceRunning(NettyService.class)) {
                logI("IndexActivity.reTryConnectLong.isServiceRunning");
                if (!CZURTcpClient.getInstance().getIsConnected().get()) {
                    logI("IndexActivity.reTryConnectLong.isServiceRunning.getIsConnected: false");
//                    ServiceUtils.stopService(NettyService.class);
                    NettyUtils.getInstance().stopNettyService();
                    try {
                        Thread.sleep(1000);
                        AppClearUtils.startNettyStarryService();
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            } else {
                ServiceUtils.stopService(NettyService.class);
                try {
                    Thread.sleep(300);
                    AppClearUtils.startNettyStarryService();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }

            retryReConnectTimes = 0;
        } else {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    retryReConnectTimes++;
                    logI("IndexActivity.reTryConnectLong.startNettyStarryService.retryReConnectTimes.NetworkUtils.isNotConnected()=" + retryReConnectTimes);

                    if (retryReConnectTimes > 300) {
                        NettyUtils.getInstance().stopNettyService();
                        return;
                    } else {
                        reTryConnectLong();
                    }
                }
            }, 1000L);
        }
    }

    /**
     * 获取剪切板内容
     */
    public void getClipboardContentDelay() {
        new Handler().postDelayed(
                this::getClipboardContentThread,
                1000);
    }

    private void getClipboardContentThread() {
        try {
            this.runOnUiThread(this::getClipboardContent);

        } catch (Exception e) {
            logE("IndexActivity.getClipboardContentThread.error=" + e.toString());
            e.printStackTrace();
        }
    }

    private void getClipboardContent() {
        logI("IndexActivity.getClipboardContent");
        // 返回数据
        ClipboardManager clipboard = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
        if (clipboard == null) {
            logI("IndexActivity.ClipboardManager is null! ignore");
            return;
        }
        ClipData clipData = clipboard.getPrimaryClip();

        String ret = ClipboardUtiKt.getContent(clipData);
        if (ClipboardUtiKt.isJoinMeetingText(ret)) {

            // 清空剪切板
            ClipboardUtiKt.clearContent(clipboard);

            if (!isJumpToApp) {
                // 判断一下是否为手机号用户？
                String mobile = UserPreferences.getInstance().getUserMobile();
                if (Validator.isEmpty(mobile)) {
                    // 1.非手机号用户，toast提示：请添加Starry Meeting后开始会议
                    ToastUtils.showLong(R.string.starry_no_mobile);
                } else {
                    Intent defaultIntent = new Intent(IndexActivity.this, BlankActivityForJoinMeeting.class);
                    defaultIntent.putExtra(StarryConstants.STARRY_BLANK_CLIPBOARD_TYPE, StarryConstants.STARRY_BLANK_TYPE_COPY);
                    defaultIntent.putExtra(StarryConstants.STARRY_BLANK_CLIPBOARD_CONTENT, ret);
                    ActivityUtils.startActivity(defaultIntent);
                }
            }
            isJumpToApp = false;
        }

    }

    // 同时将Starry模块添加至个人主页。
    private void onAddStarryModuel() {
        if (isStarryModuel) {
            return;
        }

        String equipments = getString(R.string.Starry);
        HttpManager.getInstance().request().addEquipment(
                userPreferences.getUserId(), equipments, String.class, new MiaoHttpManager.Callback<String>() {

                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                        // 判断当前的fragment是不是UserFragment

                        Fragment myFragment = getSupportFragmentManager().findFragmentById(R.id.index_frameLayout);
                        if (myFragment instanceof UserFragment) {
                            // 当前在MyFragment中
                            logE("In UserFragment.onResponse");
                        } else {
                            // 当前不在MyFragment中
                            logE("not in UserFragment.onResponse");
                            EventBus.getDefault().post(new StarryClipboardEvent(EventType.STARRY_EQUIPMENTLIST_REFRESH, ""));
                        }
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                    }

                    @Override
                    public void onError(Exception e) {
                    }
                });
    }

    private void checkAccountCountryCode() {
        String accountCountryCode = userPreferences.getCountryCode();
        if ("null".equals(accountCountryCode) || TextUtils.isEmpty(accountCountryCode)) {
            if (BuildConfig.IS_OVERSEAS) {
                getCountryCode(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                    }
                });
            }
        }
    }


    //    setCurrentUserData
    //在此方法之前进行弹窗,设置成功后,在写入本地
    private void showChoseCountryPopup(View.OnClickListener onClickListener) {
        choseCountryPopup = new RemindChoseCountryPopup.Builder(this)
                .setTitle(getString(R.string.starry_popupwindow_title))
                .setMessage(getString(R.string.user_location_country_explain))
                .setPositiveTitle(getString(R.string.confirm))
                .setCountryList(countryList)
                .setOnPositiveListener(new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {

                        String packageName = AppUtils.getAppPackageName();
                        if (which != -1) {//-1情况没点击选择, 使用默认
                            selectCountryCode = countryList.get(which);
                        }

                        updateCountryCode(selectCountryCode.getCountryCode(), new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {

                                if (choseCountryPopup != null && choseCountryPopup.isShowing()) {
                                    choseCountryPopup.dismiss();
                                }
                                onClickListener.onClick(v);
//                                setCurrentUserData();
                            }
                        });
                    }
                })
                .create();

        choseCountryPopup.show();
    }

    // 获取国家代码
    private void getCountryCode(View.OnClickListener onClickListener) {
        HttpManager.getInstance().requestPassport().countryCode(
                "en-US",
                CountryList.class,
                new MiaoHttpManager.Callback<CountryList>() {

                    @Override
                    public void onStart() {
//                        showProgressDialog();
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<CountryList> entity) {
                        hideProgressDialog();
                        countryList = (ArrayList<CountryCode>) entity.getBody().getCountryList();
                        if (BuildConfig.IS_OVERSEAS) {
                            boolean needResetDefaultCountry = false;
                            for (int i = 0; i < countryList.size(); i++) {//海外版删除列表中的中国
                                CountryCode countryCode = countryList.get(i);
                                if ("CHN".equals(countryCode.getCountryCode())) {
                                    if (countryCode.getDefaultCountry()) {
                                        needResetDefaultCountry = true;
                                    }
                                    countryList.remove(i);
                                    break;
                                }
                            }

                            if (needResetDefaultCountry) {
                                countryList.get(0).setDefaultCountry(true);
                            }
                        } else {

                        }

                        showChoseCountryPopup(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                onClickListener.onClick(v);
                            }
                        });
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<CountryList> entity) {
                        hideProgressDialog();
                    }

                    @Override
                    public void onError(Exception e) {
                        hideProgressDialog();
                    }
                }
        );
    }


    // 更新国家代码
    private void updateCountryCode(String countryCode, View.OnClickListener onClickListener) {
        HttpManager.getInstance().requestPassport().updateCountry(
                userPreferences.getIMEI(),
                CZURConstants.CLOUD_ANDROID,
                userPreferences.getChannel(),
                userPreferences.getUserId(),
                userPreferences.getToken(),
                userPreferences.getUserId(),
                "",
                countryCode,
                String.class,
                new MiaoHttpManager.Callback<String>() {
                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                        // 旧账号更新地区码成功，记录本地
                        userPreferences.setCountryCode(countryCode);
                        onClickListener.onClick(new View(IndexActivity.this));
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                        if (entity.getCode() == 1091) {//只有测试才有情况发生, 账号已经更新过了, 但是没有重新登录过, 没有存过本地
                            userPreferences.setCountryCode("USA");
                            onClickListener.onClick(new View(IndexActivity.this));
                        }

                    }

                    @Override
                    public void onError(Exception e) {
                        ToastUtils.showShort(R.string.eshare_connect_fail);
                    }
                }
        );
    }

    private void checkMissedMeetingObserve() {
        starryViewModel = new ViewModelProvider(this).get(StarryViewModel.class);

        // 是否有未接听呼叫,显示弹窗
        //  被呼叫中,  会议中！不弹
        if ((MeetingModel.isCallingInMeeting != true)
                || (MeetingModel.isInMeeting.getValue() != true)) {
            starryViewModel.showMeetingRemindDialog();
        }

        starryViewModel.isShowMeetingRemindDialog().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean) { // 有强制更新，不弹出
                    showMeetingRemindDialog();
                }
            }
        });

        MeetingModel.isCallingInMeetingFlag.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean) {
                    if (remindMissedMeetingPopup != null) {
                        remindMissedMeetingPopup.dismiss();
                    }
                }
            }
        });
    }

    private void checkMissedMeeting() {
        starryViewModel.showMeetingRemindDialog();
    }

    private long showDialogTime = 0;

    //检测到有未处理会议
    private void showMeetingRemindDialog() {
        if (remindMissedMeetingPopup != null) {
            return;
        }
        if (System.currentTimeMillis() - showDialogTime < 1000) {
            return;
            //小于一秒内的两次,不处理第二次
        }
        showDialogTime = System.currentTimeMillis();

        remindMissedMeetingPopup = new StarryCommonPopup.Builder(this, CloudCommonPopupConstants.COMMON_TWO_BUTTON)
                .setTitle(getString(R.string.starry_popupwindow_title))
                .setMessage(getString(R.string.starry_go_see_missed_meeting_remind))
                .setPositiveTitle(getString(R.string.starry_go_see_missed_meeting))
                .setNegativeTextColor(getColor(R.color.starry_text_title_color_black))
                .setNegativeTitle(getString(R.string.starry_common_dialog_no))
                .setOnPositiveListener(new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        EventBus.getDefault().post(new StarryCommonEvent(EventType.STARRY_COMPANY_NOTICE, ""));

                        if (!NetworkUtils.isConnected()) {
                            ToastUtils.showLong(R.string.starry_network_error_msg);
                            return;
                        }
                        dialog.dismiss();
                        remindMissedMeetingPopup = null;
                        Intent intent = new Intent(IndexActivity.this, StarryActivity.class);
                        intent.putExtra("showFragmentIndex", 1);
                        startActivity(intent);
                    }
                })
                .setOnNegativeListener(new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.dismiss();
                        remindMissedMeetingPopup = null;
                    }
                })
                .create();
        remindMissedMeetingPopup.show();
    }

    public void fullScreenNotify(String nickname_from) {
        logI("IndexActivity.fullScreenNotify.nickname_from=" + nickname_from);
        int messageId = StarryConstants.STARRY_MESSAGE_CALL_VIDEO_IN_BACKGROUND_CHANNEL_ID;
        Intent fullScreenIntent = new Intent(this, BlankActivityForNotification.class);
        PendingIntent fullScreenPendingIntent;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            fullScreenPendingIntent = PendingIntent.getActivity(this, 0, fullScreenIntent, PendingIntent.FLAG_IMMUTABLE);
        } else {
            fullScreenPendingIntent = PendingIntent.getActivity(this, 0, fullScreenIntent, PendingIntent.FLAG_UPDATE_CURRENT);
        }

        String channelId = "com.czur.cloud";
        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, channelId)
                .setSmallIcon(R.mipmap.small_icon)
                .setContentTitle(nickname_from + getString(R.string.starry_request_video))
                .setCategory(Notification.CATEGORY_CALL)
//                .setContentText("Hello World!")
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setFullScreenIntent(fullScreenPendingIntent, true);//将Notification变为悬挂式Notification

        Notification notify = builder.build();
//        startForeground(messageId, notification);
        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) != PackageManager.PERMISSION_GRANTED) {
            return;
        }
        NotificationManagerCompat.from(this).notify(messageId, notify);
    }


    public void initializeWhenThereIsNoForcedUpdate() {
        NettyUtils.getInstance().setNettyState(NettyUtils.NORMAL_STATE);
        NettyUtils.getInstance().startNettyService();
        if (needSync) {
            startSyncNow();
        }
        checkMissedCall30s();

        checkMissedMeetingObserve();
        // 注册网络监听
        initNetListener();

        // 重新连接长连接
        if (ServiceUtils.isServiceRunning(NettyService.class)) {
//            ServiceUtils.stopService(NettyService.class);
            NettyUtils.getInstance().stopNettyService();
        }

        if (BuildConfig.IS_OVERSEAS) {
            try {
                VendorPushTask.INSTANCE.initGooglePush(this);
            } catch (Exception e) {
                e.printStackTrace();
                if (TextUtils.isEmpty(UserPreferences.getInstance().getRegid())) {
                    UserPreferences.getInstance().setRegid(UUID.randomUUID().toString());
                    EventBus.getDefault().postSticky(new VendorPushRegIDEvent());
                }
            }
        }

        // 检查长连接
        logI("IndexActivity.onCreate.startNettyStarryService");
        // 这里不在判断是否有网,
        // 1. 这里的触发本身就由于收到了网络请求的回调
        // 2. 刚切网的时候, NetUtils的判断 经常会判定为没有网
        NettyUtils.getInstance().startNettyService();

        handlePushMessage();

        // 获取剪贴板内容
        getClipboardContentDelay();

        // 检查是否选择了国家代码
        if (BuildConfig.IS_OVERSEAS) {
            checkAccountCountryCode();
        }

        String meetingUUID = getIntent().getStringExtra("meetingUUID");
        if (meetingUUID != null && !meetingUUID.equals("")) {
            isJumpToApp = true;
            logI("IndexActivity.onCreate.meetingUUID = " + meetingUUID);
            Intent defaultIntent = new Intent(IndexActivity.this, BlankActivityForJoinMeeting.class);
            defaultIntent.putExtra(StarryConstants.STARRY_BLANK_CLIPBOARD_TYPE, StarryConstants.STARRY_BLANK_TYPE_WEB);
            defaultIntent.putExtra(StarryConstants.STARRY_BLANK_CLIPBOARD_UUID, meetingUUID);
            ActivityUtils.startActivity(defaultIntent);
        }

    }

    // 代码中判断版本进行通知权限申请
    private void checkAndShowPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            NotificationManager manager = getSystemService(NotificationManager.class);
            boolean b = manager.areNotificationsEnabled();
            logI("IndexActivity.checkAndShowPermission.areNotificationsEnabled=" + b);
            if (!b) {
                String[] strings = {Manifest.permission.POST_NOTIFICATIONS};
                requestPermissions(strings, 0);
            }
        }
    }


    private void checkEventBusStickyMsg() {//检查是否是通过快捷方式进入的,并且没有登陆,需要弹出toast
        UpdateEvent stickyEvent = EventBus.getDefault().getStickyEvent(UpdateEvent.class);
        if (stickyEvent != null) {
            if (stickyEvent.getEventType() == EventType.SHORTCUT_JOIN_MEET) {
                if (!CzurCloudApplication.isJoinedMeeting) {
                    WeakReference<Activity> activityWeakReference = new WeakReference<Activity>(this);
                    mHandler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            Intent defaultIntent = new Intent(IndexActivity.this, StarryActivity.class);
                            defaultIntent.putExtra(StarryConstants.STARRY_BLANK_CLIPBOARD_TYPE, StarryConstants.STARRY_BLANK_TYPE_SHORTCUT);
                            ActivityUtils.startActivity(defaultIntent);

                        }
                    }, 200);
                }
                EventBus.getDefault().removeStickyEvent(stickyEvent);

            }
        }
    }

    private void configShortCuts() {
//生成单个动态的Shortcut
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N_MR1) {
            ShortcutManager shortcutManager = getSystemService(ShortcutManager.class);

            Intent dynamicIntent = new Intent(this, ShortCutTrampolineActivity.class);
            dynamicIntent.setAction(Intent.ACTION_VIEW);
//            dynamicIntent.setFlags(Intent.FLAG_ACTIVITY_MULTIPLE_TASK|Intent.FLAG_ACTIVITY_NEW_TASK);
            dynamicIntent.putExtra("eshareEmptyType", StarryConstants.ESHARE_EMPTY_TYPE_SCAN);
            ShortcutInfo scanSInfo = new ShortcutInfo.Builder(this, "shortcut_scan")
                    .setShortLabel(getResources().getString(R.string.shortcut_scan))
                    .setLongLabel(getResources().getString(R.string.shortcut_scan))
                    .setIcon(Icon.createWithResource(this, R.mipmap.shortcut_starry_scan_small))
                    .setIntent(dynamicIntent)
                    .build();

            Intent joinMeetingIntent = new Intent(this, ShortCutTrampolineActivity.class);
            joinMeetingIntent.setAction(Intent.ACTION_VIEW);
//            joinMeetingIntent.setFlags(Intent.FLAG_ACTIVITY_MULTIPLE_TASK|Intent.FLAG_ACTIVITY_NEW_TASK);
            joinMeetingIntent.putExtra("eshareEmptyType", StarryConstants.STARRY_EMPTY_TYPE_JOIN);
            ShortcutInfo joinMeetingSInfo = new ShortcutInfo.Builder(this, "shortcut_joinmeeting")
                    .setShortLabel(getResources().getString(R.string.shortcut_joinmeet))
                    .setLongLabel(getResources().getString(R.string.shortcut_joinmeet))
                    .setIcon(Icon.createWithResource(this, R.mipmap.shortcut_starry_join_small))
                    .setIntent(joinMeetingIntent)
                    .build();

            shortcutManager.setDynamicShortcuts(Arrays.asList(scanSInfo, joinMeetingSInfo));
        }
    }

}