package com.czur.cloud.ui.books;

import static com.czur.cloud.ui.books.sync.SyncService.JSON;
import static com.czur.czurutils.log.CZURLogUtilsKt.logE;

import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.badoo.mobile.util.WeakHandler;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.blankj.utilcode.util.StringUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.entity.HandwritingEntity;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.HandWritingCountEvent;
import com.czur.cloud.model.HandwritingCountModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.callback.ProgressHelper;
import com.czur.cloud.network.callback.UIProgressRequestListener;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.component.progressbar.RingProgressBar;
import com.czur.cloud.util.BitmapUtils;
import com.google.gson.Gson;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;

import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * Created by Yz on 2018/2/28.
 * Email：<EMAIL>
 */

public class HandwritingRecognitionActivity extends BaseActivity implements View.OnClickListener {


    private RingProgressBar progressBar;
    private TextView progressTv;
    private View animView;
    private ImageView handwritingRecognitionImg;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private WeakHandler handler;
    private String path;
    private String sdPath;
    private int progress = 0;
    private String pageId;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.black_2a);
        BarUtils.setNavBarColor(this, getColor(R.color.black_2a));
        BarUtils.setStatusBarLightMode(this, false);
        setContentView(R.layout.activity_handwriting_recognition);
        initComponent();
        registerEvent();
    }


    private void initComponent() {
        pageId = getIntent().getStringExtra("pageId");
        path = getIntent().getStringExtra("path");
        sdPath = getIntent().getStringExtra("sdPath");
        httpManager = HttpManager.getInstance();
        userPreferences = UserPreferences.getInstance(this);
        handler = new WeakHandler();
        animView = (View) findViewById(R.id.anim_view);
        handwritingRecognitionImg = (ImageView) findViewById(R.id.handwriting_recognition_img);
        progressBar = (RingProgressBar) findViewById(R.id.progress_bar);
        progressTv = (TextView) findViewById(R.id.progress_tv);
        initBitmap();
        getHandwritingCount();


    }

    private void registerEvent() {

    }

    /**
     * @des: 初始化 bitmap大小
     * @params:
     * @return:
     */

    private void initBitmap() {
        Bitmap bitmapSource = BitmapFactory.decodeFile(path);
        Bitmap bitmap = ImageUtils.compressBySampleSize(bitmapSource, SizeUtils.dp2px(260), SizeUtils.dp2px(260) * 3 / 4);
        handwritingRecognitionImg.setImageBitmap(bitmap);
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.normal_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            default:
                break;
        }
    }

    /**
     * @des: 识别失败提示框
     * @params:
     * @return:
     */

    private void showDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(this, CloudCommonPopupConstants.COMMON_ONE_BUTTON);
        builder.setTitle(getResources().getString(R.string.recognition_defeat));
        builder.setMessage(getResources().getString(R.string.has_no_recognition_count));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                ActivityUtils.finishActivity(HandwritingRecognitionActivity.this);
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();

    }

    /**
     * @des: 获取手写识别次数
     * @params:[]
     * @return:void
     */
    private void getHandwritingCount() {
        httpManager.request().getHandwritingCount(
                userPreferences.getUserId(), HandwritingCountModel.class, new MiaoHttpManager.Callback<HandwritingCountModel>() {
                    @Override
                    public void onStart() {
                        showProgressDialog(true);
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<HandwritingCountModel> entity) {
                        hideProgressDialog();
                        String ocrNumStr = entity.getBody().getOcrNum();
                        userPreferences.setHandwritingCount(ocrNumStr);
                        if (!StringUtils.isEmpty(ocrNumStr)) {
                            int ocrNumber = Integer.valueOf(ocrNumStr).intValue();
                            if (ocrNumber <= 0) {
                                showDialog();
                            } else {
                                recognitionPrepared();
                            }
                        }

                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<HandwritingCountModel> entity) {
                        hideProgressDialog();
                        if (entity.getCode() == MiaoHttpManager.STATUS_FAIL) {
                            showMessage(R.string.toast_internal_error);
                        } else {
                            showMessage(R.string.request_failed_alert);
                        }

                    }

                    @Override
                    public void onError(Exception e) {
                        hideProgressDialog();
                        showMessage(R.string.request_failed_alert);
                    }
                });
    }

    /**
     * @des: 进度置0
     * @params:
     * @return:
     */

    private void recognitionPrepared() {
        progressBar.setProgress(0);
        progressTv.setText(0 + "");
        if (NetworkUtils.isConnected()) {
            handwritingRecognition(new File(path));
        } else {
            showMessage(R.string.handwriting_without_network);
        }

    }

    /**
     * @des: 手写识别
     * @params:
     * @return:
     */

    private void handwritingRecognition(final File file) {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                upload(file);
                return null;
            }

            @Override
            public void onSuccess(Void result) {

            }
        });

    }

    /**
     * @des: 上传手写体识别image
     * @params:
     * @return:
     */

    private void upload(File file) {
        try {
            //这个是ui线程回调，可直接操作UI
            final UIProgressRequestListener uiProgressRequestListener = new UIProgressRequestListener() {
                @Override
                public void onUIRequestProgress(long bytesWrite, long contentLength, boolean done) {
                    //ui层回调
                    progressBar.setProgress((int) ((100 * bytesWrite) / contentLength));
                    progressTv.setText((int) ((100 * bytesWrite) / contentLength) + "");
                    ViewGroup.LayoutParams layoutParams = animView.getLayoutParams();
                    layoutParams.height = SizeUtils.dp2px(260) * ((int) ((100 * bytesWrite) / contentLength)) / 100;
                    animView.setLayoutParams(layoutParams);
                }
            };

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("base64", BitmapUtils.imageToBase64(file.getPath()));
            String syncJson = jsonObject.toString();
            RequestBody requestBody = RequestBody.create(JSON, syncJson);

            Request request = new Request.Builder()
                    .header("Content-Type", "application/json")
                    .header("T-ID", userPreferences.getToken())
                    .header("U-ID", userPreferences.getUserId())
                    .url(CZURConstants.HANDWRITING_URL)
                    .post(ProgressHelper.addProgressRequestListener(requestBody, uiProgressRequestListener))
                    .build();

            Response response = MiaoHttpManager.getInstance().getHttpClient().newCall(request).execute();
            String jsonString = response.body().string();
            HandwritingEntity handwritingEntity = new Gson().fromJson(jsonString, HandwritingEntity.class);
            // 请求成功
            if (response.isSuccessful()) {
                int code = handwritingEntity.getCode();
                // 上传成功
                if (code == 1000) {
                    Gson gson = new Gson();
                    HandwritingEntity.DataBean dataBean = new Gson().fromJson(handwritingEntity.getData(), HandwritingEntity.DataBean.class);
                    handwritingEntity.setDatabean(dataBean);
                    afterUpload(handwritingEntity, jsonString);
                } else {
                    FailedToast();
                }
            }
            // 请求失败
            else {
                FailedToast();
            }
        } catch (IOException | JSONException e) {
            logE(e.toString());
            FailedToast();
        } catch (Exception e) {
            logE(e.toString());
        }
    }


    private void afterUpload(final HandwritingEntity handwritingEntity, final String jsonString) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                handwritingCharge();
                Intent intent = new Intent(HandwritingRecognitionActivity.this, HandwritingResultActivity.class);
                intent.putExtra("resultText", jsonString);
                intent.putExtra("pageId", pageId);
                intent.putExtra("isHandwritingRecognition", true);
                ActivityUtils.startActivity(intent);
                ActivityUtils.finishActivity(HandwritingRecognitionActivity.this);
            }
        });
    }

    private void FailedToast() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                showMessage(R.string.request_failed_alert);
            }
        });
    }

    /**
     * @des: 手写识别扣除
     * @params:[]
     * @return:void
     */
    private void handwritingCharge() {

        httpManager.request().handwritingCharge(
                userPreferences.getUserId(), HandwritingCountModel.class, new MiaoHttpManager.Callback<HandwritingCountModel>() {
                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<HandwritingCountModel> entity) {
                        EventBus.getDefault().post(new HandWritingCountEvent(EventType.HANDWRITING_COUNT_REDUCE));
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<HandwritingCountModel> entity) {
                        showMessage(R.string.request_failed_alert);
                    }

                    @Override
                    public void onError(Exception e) {
                        showMessage(R.string.request_failed_alert);
                    }
                });
    }


}
