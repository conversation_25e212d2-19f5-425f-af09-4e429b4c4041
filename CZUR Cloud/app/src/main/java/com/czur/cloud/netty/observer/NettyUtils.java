package com.czur.cloud.netty.observer;

import android.os.Looper;
import android.os.Message;

import com.badoo.mobile.util.WeakHandler;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ServiceUtils;

public class NettyUtils {
    private static final String TAG = "NettyUtils";

    public static final int NOTHING = -1;
    public static final int START = 0;
    public static final int STOP = 1;
    public static final int RESTART = 2;

    public static final int KEEP_STOP = 1;//长连接保持关闭(例如强制升级时,需要保持长连接的断开)
    public static final int NORMAL_STATE = 0;//长连正常状态

    private static final int MSG_WHAT_DISPATCH = 1; // 调度信息
    private static final int MSG_WHAT_ADD_TASK = 2; // 添加Task
    private static final long DEBOUNCE_TIME = 1200L; // 去重的时间间隔 这个时间是通过log看的, 基本上可以保证切网的时候只执行一次操作

    // 表示当前正在执行的动作
    public static int lastWorkingType = NOTHING;

    private Integer nextTask = null;

    private int nettyState = KEEP_STOP;//长连接期望的状态(例如强制升级时,需要保持长连接的断开)

    private NettyUtils() {
        handler = new WeakHandler(Looper.getMainLooper(), msg -> {
            switch (msg.what) {
                case MSG_WHAT_ADD_TASK:
                    int type = msg.arg1;
                    handler.removeMessages(MSG_WHAT_DISPATCH);
                    if (nextTask == null) {
                        nextTask = type;
                    } else if (type != START) {
                        nextTask = type;
                    } else {
                        if (nextTask == STOP) {
                            nextTask = RESTART;
                        }
                    }
                    handler.sendEmptyMessageDelayed(MSG_WHAT_DISPATCH, DEBOUNCE_TIME);
                    break;
                case MSG_WHAT_DISPATCH:
                    if (lastWorkingType != NOTHING) {
                        // 当前正在执行动作, 等待执行完成后, 再下一步
                        handler.removeMessages(MSG_WHAT_DISPATCH);
                        handler.sendEmptyMessageDelayed(MSG_WHAT_DISPATCH, DEBOUNCE_TIME);
                    } else {
                        start();
                    }
                    break;
                default:
                    break;
            }

            return false;
        });
    }

    private static NettyUtils singleton;
    private static WeakHandler handler;

    public static NettyUtils getInstance() {
        if (singleton == null) {
            synchronized (NettyUtils.class) {
                if (singleton == null) {
                    singleton = new NettyUtils();
                }
            }
        }

        return singleton;
    }

    public void setNettyState(int state) {
        nettyState = state;
    }

    public void startNettyService() {
        if (nettyState == NORMAL_STATE) {
            add(START);
        }

    }

    public void stopNettyService() {
        add(STOP);
    }

    public void restartNettyService() {
        if (nettyState == NORMAL_STATE) {
            add(RESTART);
        }
    }

    /**
     * 逻辑如下:
     * 1. 如果当前没有任务, 则记录下一步的任务
     * 2. 如果想要尝试START, 则会检查下一步的任务是否是STOP, 如果是, 则会合并成RESTART任务
     * 3. 任务需要经过 #DEBOUNCE_TIME debounce后,才会真正被执行
     * <p>
     * 添加和执行都在handler线程中(目前看是这样, 如果实际), 所以不会有线程安全问题
     */
    private void add(int type) {
        Message msg = Message.obtain();
        msg.what = MSG_WHAT_ADD_TASK;
        msg.arg1 = type;
        handler.sendMessage(msg);
    }

    private void start() {
        if (nextTask == null) {
            lastWorkingType = NOTHING;
            return;
        }
        int task = nextTask;
        nextTask = null;

        lastWorkingType = task;
        doWork(task);
    }

    private void doWork(int type) {
        if (type == START) {
            if (NetworkUtils.isConnected()) {
                ServiceUtils.startService(NettyService.class);
            }
            lastWorkingType = NOTHING;
        } else if (type == STOP) {
            if (ServiceUtils.isServiceRunning(NettyService.class)) {

                ServiceUtils.stopService(NettyService.class);
            }
            lastWorkingType = NOTHING;
        } else if (type == RESTART) {
            boolean stopped = ServiceUtils.stopService(NettyService.class);
            if (stopped) {
                handler.postDelayed(() -> doWork(START), 300);
            } else if (ServiceUtils.isServiceRunning(NettyService.class)) {
                handler.postDelayed(() -> doWork(RESTART), 300);
            } else {
                doWork(START);
            }
        }
    }

}
