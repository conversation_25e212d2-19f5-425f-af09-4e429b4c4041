package com.czur.cloud.preferences;

import android.app.Application;
import android.content.Context;
import android.text.TextUtils;

import com.czur.cloud.ui.starry.model.StarryUserInfoModel;
import com.czur.cloud.ui.starry.utils.Tools;
import com.czur.cloud.util.validator.StringUtils;
import com.czur.cloud.util.validator.Validator;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
public class StarryPreferences extends BasePreferences {

    private static final String PREF = StarryPreferences.class.getSimpleName();

    private static final String STARRY_USERINFO_MODEL = "czur_cloud_starry_userinfo_model";

    private static StarryPreferences instance;

    public static void init(Application application) {
        instance = new StarryPreferences(application, PREF);
    }

    public static StarryPreferences getInstance() {
        return instance;
    }

    public static StarryPreferences getInstance(Context context) {
        if (instance == null) {
            instance = new StarryPreferences(context, PREF);
        }
        return instance;
    }

    public StarryPreferences(Context context, String prefsName) {
        super(context, prefsName);
    }


    public void setStarryUserinfoModel(StarryUserInfoModel data){
        if (Validator.isNotEmpty(data)) {
            String json = new Gson().toJson(data);
            put(STARRY_USERINFO_MODEL, json);
        }
    }

    public StarryUserInfoModel getStarryUserinfoModel(){
        String json = (String) get(STARRY_USERINFO_MODEL);
        if (StringUtils.isBlank(json)) {
            return null;
        }

        return new Gson().fromJson(json,
                new TypeToken<StarryUserInfoModel>(){}.getType());
    }

    public Boolean getLastCam() {
        if (isValidUser()) {
            return getStarryUserinfoModel().getLastCam();
        }
        return false;
    }
    public Boolean getLastMic() {
        if (isValidUser()) {
            return getStarryUserinfoModel().getLastMic();
        }
        return true;
    }
    public void setLastCam(Boolean flag) {
        if (isValidUser()) {
            StarryUserInfoModel userData = getStarryUserinfoModel();
            userData.setLastCam(flag);
            setStarryUserinfoModel(userData);
        }
    }
    public void setLastMic(Boolean flag) {
        if (isValidUser()) {
            StarryUserInfoModel userData = getStarryUserinfoModel();
            userData.setLastMic(flag);
            setStarryUserinfoModel(userData);
        }
    }


    public String getTargetCamStatus() {
        if (isValidUser()) {
            return getStarryUserinfoModel().getTargetCamStatus();
        }
        return "-1";
    }
    public String getTargetMicStatus() {
        if (isValidUser()) {
            return getStarryUserinfoModel().getTargetMicStatus();
        }
        return "-1";
    }
    public void setTargetCamStatus(String flag) {
        if (isValidUser()) {
            StarryUserInfoModel userData = getStarryUserinfoModel();
            userData.setTargetCamStatus(flag);
            setStarryUserinfoModel(userData);
        }
    }
    public void setTargetMicStatus(String flag) {
        if (isValidUser()) {
            StarryUserInfoModel userData = getStarryUserinfoModel();
            userData.setTargetMicStatus(flag);
            setStarryUserinfoModel(userData);
        }
    }

    public String getCallInTargetCamStatus() {
        if (isValidUser()) {
            return getStarryUserinfoModel().getCallInTargetCamStatus();
        }
        return "-1";
    }
    public String getCallInTargetMicStatus() {
        if (isValidUser()) {
            return getStarryUserinfoModel().getCallInTargetMicStatus();
        }
        return "-1";
    }
    public void setCallInTargetCamStatus(String flag) {
        if (isValidUser()) {
            StarryUserInfoModel userData = getStarryUserinfoModel();
            userData.setCallInTargetCamStatus(flag);
            setStarryUserinfoModel(userData);
        }
    }
    public void setCallInTargetMicStatus(String flag) {
        if (isValidUser()) {
            StarryUserInfoModel userData = getStarryUserinfoModel();
            userData.setCallInTargetMicStatus(flag);
            setStarryUserinfoModel(userData);
        }
    }



    public Boolean getLastShowPwd() {
        if (isValidUser()) {
            return getStarryUserinfoModel().getLastShowPwd();
        }
        return false;
    }
    public void setLastShowPwd(Boolean flag) {
        if (isValidUser()) {
            StarryUserInfoModel userData = getStarryUserinfoModel();
            userData.setLastShowPwd(flag);
            setStarryUserinfoModel(userData);
        }
    }

    public Boolean getLastPwdEye() {
        if (isValidUser()) {
            return getStarryUserinfoModel().getLastPwdEye();
        }
        return false;
    }
    public void setLastPwdEye(Boolean flag) {
        if (isValidUser()) {
            StarryUserInfoModel userData = getStarryUserinfoModel();
            userData.setLastPwdEye(flag);
            setStarryUserinfoModel(userData);
        }
    }

    public String getLastPwd() {
        if (isValidUser()) {
            return getStarryUserinfoModel().getLastPwd();
        }
        return "";
    }
    public void setLastPwd(String pwd) {
        if (isValidUser()) {
            StarryUserInfoModel userData = getStarryUserinfoModel();
            userData.setLastPwd(pwd);
            setStarryUserinfoModel(userData);
        }
    }

    public String getUserId() {
        if (isValidUser()) {
            return getStarryUserinfoModel().getId();
        }
        return "";
    }

    public long getLongUserId() {
        if (isValidUser()) {
            return Long.parseLong(getStarryUserinfoModel().getId());
        }
        return 0;
    }

    public String getAccountNo() {
        if (isValidUser()) {
            return getStarryUserinfoModel().getAccountNo();
        }
        return "";
    }
    public void setAccountNo(String account) {
        if (account == null)
            return;

        if (isValidUser()) {
            StarryUserInfoModel userData = getStarryUserinfoModel();
            userData.setAccountNo(account);
            setStarryUserinfoModel(userData);
        }
    }

    public String getCzurId() {
        if (isValidUser()) {
            return getStarryUserinfoModel().getCzurId();
        }
        return "";
    }

    public String setCzurId(String czurid) {
        if (isValidUser()) {
            StarryUserInfoModel userData = getStarryUserinfoModel();
            userData.setCzurId(czurid);
            setStarryUserinfoModel(userData);
        }
        return "";
    }

    public boolean getHasShowedPermissionsDialog(){
        if (isValidUser()) {
            return getStarryUserinfoModel().getHasShowedPermissionsDialog();
        }
        return true;
    }

    public String setHasShowedPermissionsDialog(boolean showed) {
        if (isValidUser()) {
            StarryUserInfoModel userData = getStarryUserinfoModel();
            userData.setHasShowedPermissionsDialog(showed);
            setStarryUserinfoModel(userData);
        }
        return "";
    }

    public String getFirstMeetingRecordId(){
        if (isValidUser()) {
            return getStarryUserinfoModel().getFirstMeetingRecordId();
        }
        return "";
    }

    public String setFirstMeetingRecordId(String meetingId) {
        if (isValidUser()) {
            StarryUserInfoModel userData = getStarryUserinfoModel();
            userData.setFirstMeetingRecordId(meetingId);
            setStarryUserinfoModel(userData);
        }
        return "";
    }

    public String getLastCloseAppTime(){
//        Log.i("StarryPreferences", "getLastCloseAppTime.getStarryUserinfoModel()="+new Gson().toJson(getStarryUserinfoModel()));
        if (isValidUser()) {
            return getStarryUserinfoModel().getLastCloseAppTime();
        }
        return "";
    }

    public void setLastCloseAppTime(String lastTime) {
        if (isValidUser()) {
            StarryUserInfoModel userData = getStarryUserinfoModel();
            userData.setLastCloseAppTime(lastTime);
            setStarryUserinfoModel(userData);
//            Log.i("StarryPreferences", "setLastCloseAppTime.userData="+ new Gson().toJson(userData).toString());
        }
    }


    public boolean getIsSpecialXiaomi(){
        if (isValidUser()) {
            return getStarryUserinfoModel().isSpecialXiaomi();
        }
        return true;
    }

    public String setIsSpecialXiaomi(boolean isSepcialXiaomi) {
        if (isValidUser()) {
            StarryUserInfoModel userData = getStarryUserinfoModel();
            userData.setSpecialXiaomi(isSepcialXiaomi);
            setStarryUserinfoModel(userData);
        }
        return "";
    }

    public String getName() {
        if (isValidUser()) {
            return getStarryUserinfoModel().getName();
        }
        return "";
    }

    public boolean isValidUser() {
        StarryUserInfoModel userInfo = getStarryUserinfoModel();
        if (userInfo != null && Validator.isNotEmpty(userInfo.getId())) {
            return true;
        }
        return false;
    }

    public boolean isInValidUser() {
        return !isValidUser();
    }

    // TODO 凡是新增的字段，要在此处体现
    public void setNewUserInfo(StarryUserInfoModel user){
        user.setLastCam(getLastCam());
        user.setLastMic(getLastMic());
        user.setLastShowPwd(getLastShowPwd());
        user.setLastPwdEye(getLastPwdEye());
        String lastpwd = getLastPwd();
        if (lastpwd.equals("")){
            lastpwd = Tools.onRandMeetingPassword();
        }
        user.setLastPwd(lastpwd);
        user.setHasShowedPermissionsDialog(getHasShowedPermissionsDialog());
        user.setSpecialXiaomi(getIsSpecialXiaomi());
        user.setFirstMeetingRecordId(getFirstMeetingRecordId());
        user.setLastCloseAppTime(getLastCloseAppTime());
        user.setCallInTargetCamStatus(getCallInTargetCamStatus());
        user.setCallInTargetMicStatus(getCallInTargetMicStatus());
        setStarryUserinfoModel(user);
    }
}
