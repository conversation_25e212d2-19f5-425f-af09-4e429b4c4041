package com.czur.cloud.ui.starry.meeting.adapter

import android.text.TextUtils
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.czur.cloud.R
import com.czur.cloud.ui.starry.activity.StarryActivity
import com.czur.cloud.ui.starry.meeting.baselib.adapter.BaseVH
import com.czur.cloud.ui.starry.meeting.baselib.utils.getString
import com.czur.cloud.ui.starry.meeting.baselib.utils.has
import com.czur.cloud.ui.starry.meeting.bean.UserStatus.STATUS_HOLD_ON
import com.czur.cloud.ui.starry.meeting.bean.UserStatus.STATUS_IOS_HOLD
import com.czur.cloud.ui.starry.meeting.bean.UserStatus.STATUS_JOINED
import com.czur.cloud.ui.starry.meeting.bean.UserStatus.STATUS_OFFLINE
import com.czur.cloud.ui.starry.meeting.bean.UserStatus.STATUS_TIMEOUT
import com.czur.cloud.ui.starry.meeting.bean.vo.Member
import com.czur.cloud.ui.starry.meeting.common.UserHandler
import com.czur.cloud.ui.starry.meeting.widget.HeadImageView
import com.czur.cloud.ui.starry.viewmodel.StarryViewModel
import java.util.regex.Pattern


/**
 * Created by 陈丰尧 on 4/29/21
 * 参会人员列表Adapter
 */
data class MemberItem(
    val accountId: String,      // item的accountID
    val isSelf: Boolean,        // 是否是自己
    val headImg: String?,       // 头像
    var nickName: String,       // 昵称
    val audioEnable: Boolean,   // 音频是否开启
    val videoEnable: Boolean,   // 视频是否开启
    val adminMode: Boolean,     // 是否处于管理员模式(自己是管理员)
    val isAdmin: Boolean,       // item是否是管理员
    var status: Int,            // item的状态(是否已经加入/等待中/超时)
    val isShareMode: Boolean,   // 是否处于分享模式
    val isFullScreen: Boolean   // item是否已经是全屏显示
) {

    val isJoined = (status == STATUS_JOINED)
    val isTimeOut = (status == STATUS_TIMEOUT)
    val isNetworError = (status == STATUS_OFFLINE)
    val isIOSHold = (status == STATUS_IOS_HOLD)

    fun canShow(icon: Icon): Boolean {
        return when (icon) {
            /* 管理员标记* 是管理员 */
            Icon.ADMIN_FLAG -> {
                isAdmin
            }

            /* 音视频按钮:
             *  2. 已经加入的成员*      2.1 是管理员*      2.2 不是管理员, 但是音频打开
             */
            Icon.AUDIO -> {
//                !audioEnable && isJoined && !isAdmin
                (isJoined || isIOSHold) && !isAdmin
            }
            Icon.VIDEO -> {
//                !videoEnable && isJoined && !isAdmin
                (isJoined || isIOSHold) && !isAdmin
            }

            /* 状态文本 * 用户还没有加入 */
            Icon.STATUS_INFO_ADMIN -> {
                adminMode && !isJoined && !isAdmin
            }
            Icon.STATUS_INFO -> {
                !isJoined && !isNetworError && !isIOSHold
            }

            Icon.STATUS_NETWORK_ERROR -> {
                isNetworError
            }

            /*  全屏按钮*  1. 当前不是分享模式 *  2. 当前不是全屏显示的 *  3. 已经加入 */
            Icon.FULL_SCREEN -> {
                !isShareMode && !isFullScreen && isJoined
            }

            /*  转让管理员按钮: *  1. 当前是管理员模式 *  2. 这个用户不是管理员 *  3. 已经加入 */
            Icon.ABDICATE_ADMIN -> {
                adminMode && !isAdmin && isJoined
            }

            /* 踢出按钮* 1. 当前是管理员模式2. 不是自己 */
            Icon.KICK_OFF_MEMBER -> {
                adminMode && !isSelf
            }

            /* 超时按钮* 已超时 */
            Icon.REMIND -> {
                isTimeOut
            }

            // 更多
            Icon.MORE -> {
                adminMode && !isAdmin
            }
        }
    }
}

enum class Icon {
    AUDIO,          // 音频
    VIDEO,          // 视频
    FULL_SCREEN,    // 全屏
    ABDICATE_ADMIN, // 转让管理员
    KICK_OFF_MEMBER,// 踢出成员
    STATUS_INFO_ADMIN,    // admin状态文字
    STATUS_INFO,    // 状态文字
    STATUS_NETWORK_ERROR,    // 网络异常
    REMIND,         // 提醒
    ADMIN_FLAG,     // 管理员标记
    MORE,           // 更多按钮
}

class MemberAdapter : RecyclerView.Adapter<BaseVH>() {

    private val itemCallback = object : DiffUtil.ItemCallback<MemberItem>() {
        override fun areItemsTheSame(oldItem: MemberItem, newItem: MemberItem): Boolean {
            return oldItem == newItem
        }

        override fun areContentsTheSame(oldItem: MemberItem, newItem: MemberItem): Boolean {
            if (oldItem.accountId != newItem.accountId){
                return false
            }

            if (oldItem.headImg != newItem.headImg){
                return false
            }

            if (oldItem.nickName != newItem.nickName){
                return false
            }
            return oldItem.accountId == newItem.accountId
        }

        override fun getChangePayload(oldItem: MemberItem, newItem: MemberItem): Any? {
            return super.getChangePayload(oldItem, newItem)
        }

    }

//    private val differ = AsyncListDiffer(this, itemCallback)
    private var showData = mutableListOf<MemberItem>()

    var rawData: List<Member> = emptyList()
        set(value) {
            field = value
            processData()
            notifyDataSetChanged()
        }

    private var isSelfAdmin = false

    private var remindCLickList = arrayListOf<String>()
    fun setRemindClickList(arrayList: ArrayList<String>) {
        remindCLickList = arrayList
    }
    private val viewModel by lazy {
        StarryActivity.mainActivity?.let {
            ViewModelProvider(it).get(StarryViewModel::class.java) }
    }
    private fun processData() {
        Log.i("Jason", "MemberAdapter.processData")

        val adminAccount = rawData.find {
            it.isAdmin
        }?.accountId ?: UserHandler.accountNo.toString()
        val adminMode = adminAccount == UserHandler.accountNo.toString()

        // 是否处于共享状态
        val inShare = rawData.has {
            it.sharing
        }
        // 转化为MemberItem
        val showData = rawData.map {
            MemberItem(
                accountId = it.accountId,
                isSelf = it.accountId == UserHandler.accountNo.toString(),
                headImg = it.headImg,
                nickName = it.nickName,
                audioEnable = it.isAudioInUse,
                videoEnable = it.isVideoInUse,
                adminMode = adminMode,
                isAdmin = adminAccount == it.accountId,
                status = it.status,
                isShareMode = inShare,
                isFullScreen = true
            )
        }

        if (viewModel != null && viewModel?.addressBookList?.value != null){

            for (contact in viewModel!!.addressBookList.value!!){
                for (data in showData){
                    if (data.accountId == contact.meetingNo){
                        Log.i("Jason", "MemberAdapter.processData.data=${data}")
                        data.nickName = contact.remark
                    }
                }
            }
        }

        this.showData.clear()
        this.showData.addAll(showData)
//        val newList = mutableListOf<MemberItem>()
//        newList.addAll(showData)
//        differ.submitList(newList)
        Log.i("Jason", "MemberAdapter.processData.differ=${showData}")
//        Log.i("Jason", "MemberAdapter.processData.differ=${differ}")
    }

    fun updateStatus(newStatus: Int, pos: Int) {
        val data = getData(pos)
        data.status = newStatus
        notifyItemChanged(pos)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH =
        BaseVH(R.layout.starry_meeting_item_member_new, parent)

    override fun onBindViewHolder(holder: BaseVH, position: Int) {
        val data = getData(position)
//        logI("MemberAdapter.onBindViewHolder.data=${data}")

        val headImageView: HeadImageView = holder.getView(R.id.itemMemberHeadIv)
        headImageView.setTag(R.id.itemMemberHeadIv, data.accountId)

        // 不进行回收，不过会造成性能下降。
//        holder.setIsRecyclable(false)

        // 修改各个元素可见性
        changeVisible(holder, data)

        // 头像
//        holder.setImgUrlOrText(
//            data.headImg,
//            data.nickName,
//            R.id.itemMemberHeadIv
//        )
        val url = data.headImg ?: ""
        val name = data.nickName ?: ""
        if (url.isEmpty()) {
            headImageView.setRecycleViewImageText(name, position)
        } else {
            headImageView.setRecycleViewImageText("", position)
            headImageView.setRecycleViewImageUrl(url, data.accountId)
        }
        //名字长度调整
        val view = holder.getView<TextView>(R.id.itemMemberNickNameTv)
        val editNameBtn = holder.getView<ImageView>(R.id.name_edit_btn)
        val showNickName = if (data.isSelf) {
            editNameBtn.visibility = View.VISIBLE
            view.ellipsize = TextUtils.TruncateAt.valueOf("MIDDLE")
            data.nickName + getString(R.string.tv_nickname_self)
        } else {
            editNameBtn.visibility = View.GONE
//            view.ellipsize = TextUtils.TruncateAt.valueOf("END")
            view.ellipsize = TextUtils.TruncateAt.valueOf("MIDDLE")
            data.nickName
        }
//        nickName = OmitName(data, view, nickName)
        holder.setText(showNickName, R.id.itemMemberNickNameTv)

        // 音频按钮的图标
        if (data.audioEnable) {
            holder.setImgResource(
                R.mipmap.starry_meeting_member_icon_btn_mic_gray,
                R.id.itemMemberAudioIcon
            )
        } else {
            holder.setImgResource(
                R.mipmap.starry_meeting_member_icon_btn_mic_red,
                R.id.itemMemberAudioIcon
            )
        }

        // 视频按钮的图标
        if (data.videoEnable) {
            holder.setImgResource(
                R.mipmap.starry_meeting_member_icon_btn_video_gray,
                R.id.itemMemberVideoIcon
            )
        } else {
            holder.setImgResource(
                R.mipmap.starry_meeting_member_icon_btn_video_red,
                R.id.itemMemberVideoIcon
            )
        }

        if (remindCLickList.contains(data.accountId)) {
            holder.visible(false, R.id.itemMemberRemindIcon)
        } else {
            if (data.status == STATUS_TIMEOUT) {
                // 显示提醒按钮
                holder.visible(true, R.id.itemMemberRemindIcon)
            } else {
                holder.visible(false, R.id.itemMemberRemindIcon)
            }
        }


    }

    /**
     * 修改每一行各个元素的可见性
     */
    private fun changeVisible(holder: BaseVH, memberItem: MemberItem) {
//        // 管理员标记
//        holder.visible(memberItem.canShow(Icon.ADMIN_FLAG), R.id.itemMemberAdminIv)
        // 管理员文字标签
        holder.visible(memberItem.canShow(Icon.ADMIN_FLAG), R.id.itemMemberAdminTv)
        // 音频按钮
        holder.visible(memberItem.canShow(Icon.AUDIO), R.id.itemMemberAudioIcon)
        // 视频按钮
        holder.visible(memberItem.canShow(Icon.VIDEO), R.id.itemMemberVideoIcon)
        // 状态文字
        holder.visible(memberItem.canShow(Icon.STATUS_INFO), R.id.itemMemberStatusTv)
        // 网络异常
        holder.visible(memberItem.canShow(Icon.STATUS_NETWORK_ERROR), R.id.itemMemberNetworkErrorTv)

//        val view = holder.getView<LinearLayout>(R.id.itemMemberRemindIcon)
//        val lp: LinearLayout.LayoutParams = view.layoutParams as LinearLayout.LayoutParams
//
//        if (memberItem.canShow(Icon.STATUS_INFO_ADMIN)) {
//            lp.rightMargin = 0
//        } else {
//            lp.rightMargin = SizeUtils.dp2px(20f)
//        }

//        view.layoutParams = lp
//        // 提醒
//        holder.visible(memberItem.canShow(Icon.REMIND), R.id.itemMemberRemindIcon)
        // 更多按钮
        holder.visible(memberItem.canShow(Icon.MORE), R.id.itemMemberMoreIcon)
    }

    override fun getItemCount() = showData.size

    private fun getData(pos: Int): MemberItem = showData[pos]

    fun getRawMember(pos: Int): Member? {
        return rawData.getOrNull(pos)
    }

    private fun OmitName(data: MemberItem, view: TextView, originName: String): String {
        //名字是否省略
        var nickName = originName
        val regEx = "[\u4e00-\u9fa5]"
        var count = 0
        val p = Pattern.compile(regEx)
        val m = p.matcher(originName)
        //中文
        while (m.find()) {
            count++
        }
        if (!data.isAdmin) {
            if (data.adminMode) {
                if (data.status == STATUS_TIMEOUT && originName.length + count > 10) {
                    nickName = if (count > 0) {
                        "${originName.substring(0, 4)}..."
                    } else {
                        "${originName.substring(0, 7)}..."
                    }

                } else {
                    nickName = originName
                }

            } else {
                if ((data.status == STATUS_TIMEOUT)
                    && originName.length + count > 12
                ) {
                    nickName = if (count > 0) {
                        "${originName.substring(0, 5)}..."
                    } else {
                        "${originName.substring(0, 8)}..."
                    }
                } else {
                    nickName = originName
                }
            }
        } else {
            if (data.adminMode) {
                if (data.status == STATUS_HOLD_ON && originName.length + count > 10) {
                    if (count > 0) {
                        nickName = "${originName.substring(0, 4)}..."
                    } else {
                        nickName = "${originName.substring(0, 7)}..."
                    }

                }else{
                    nickName = originName
                }

            } else {
                if ((data.status == STATUS_HOLD_ON ||
                            data.status == STATUS_OFFLINE ||
                            data.status == STATUS_IOS_HOLD)
                    && originName.length + count > 12
                ) {
                    if (count > 0) {
                        nickName = "${originName.substring(0, 5)}..."
                    } else {
                        nickName = "${originName.substring(0, 8)}..."
                    }
                }
            }
        }

//        // 如果是（我），则单独处理，中间...
//        if (data.isSelf){
//            logI("OmitName.nickName=${nickName},len=${nickName.length}")
//            if (nickName.length > 6) {
//                val old = nickName.substring(0, nickName.length - 6)
//                nickName = "$old..."+getString(R.string.tv_nickname_self)
//            }
//        }

        return nickName
    }

    // 告知adapter自己是否是管理员
    fun setIsSelfAdmin(isAdmin: Boolean) {
        isSelfAdmin = isAdmin
    }

    interface OnItemClickListener {
        fun onItemClick(itemView: View?, position: Int)
    }

    private var listener: OnItemClickListener? = null

    fun setOnItemClickListener(listener: OnItemClickListener?) {
        this.listener = listener
    }

    override fun getItemViewType(position: Int): Int {
        return position
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }


}