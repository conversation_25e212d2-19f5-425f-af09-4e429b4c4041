package com.czur.cloud.entity;

import java.util.Objects;

/**
 * Created by Yz on 2018/6/11.
 * Email：<EMAIL>
 */
public class AuraMateWrongQuestionModel {


    /**
     * id : 90
     * questionName : null
     * tagId : 15
     * userId : 3028
     * deleted : false
     * ossKey : test/3028/2222222222/2019-07-24/9045e119-c214-470e-91d6-744ab8d48269_single_5_img_wrong318492.jpg
     * createTime : 2019-07-26 08:57:55.000
     * fileId : q8vnpbt5s8lpeat
     * imageId : 1913
     * imageHeight : 986
     * imageWidth : 2665
     * fileSize : 273905
     * smallOssKeyUrl : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/3028/2222222222/2019-07-24/9045e119-c214-470e-91d6-744ab8d48269_single_5_img_wrong318492.jpg?Expires=1564129294&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=f6aa3IRhAFx689CoUKrq19NUHjM%3D&x-oss-process=image%2Fcrop%2Cx_0%2Cy_0%2Cw_1080%2Ch_1080%7Cimage%2Fresize%2Cm_lfit%2Cw_150%2Ch_150
     * ossKeyUrl : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/3028/2222222222/2019-07-24/9045e119-c214-470e-91d6-744ab8d48269_single_5_img_wrong318492.jpg?Expires=1564129294&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=af6Z4cl0mQaXR%2F%2FncSorO3sBqYE%3D
     * updateTime : null
     */

    private int id;
    private String questionName;
    private int tagId;
    private String userId;
    private boolean deleted;
    private String ossKey;
    private String createTime;
    private String fileId;
    private int imageId;
    private int imageHeight;
    private int imageWidth;
    private int fileSize;
    private String smallOssKeyUrl;
    private String ossKeyUrl;
    private String updateTime;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Object getQuestionName() {
        return questionName;
    }

    public void setQuestionName(String questionName) {
        this.questionName = questionName;
    }

    public int getTagId() {
        return tagId;
    }

    public void setTagId(int tagId) {
        this.tagId = tagId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    public String getOssKey() {
        return ossKey;
    }

    public void setOssKey(String ossKey) {
        this.ossKey = ossKey;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public int getImageId() {
        return imageId;
    }

    public void setImageId(int imageId) {
        this.imageId = imageId;
    }

    public int getImageHeight() {
        return imageHeight;
    }

    public void setImageHeight(int imageHeight) {
        this.imageHeight = imageHeight;
    }

    public int getImageWidth() {
        return imageWidth;
    }

    public void setImageWidth(int imageWidth) {
        this.imageWidth = imageWidth;
    }

    public int getFileSize() {
        return fileSize;
    }

    public void setFileSize(int fileSize) {
        this.fileSize = fileSize;
    }

    public String getSmallOssKeyUrl() {
        return smallOssKeyUrl;
    }

    public void setSmallOssKeyUrl(String smallOssKeyUrl) {
        this.smallOssKeyUrl = smallOssKeyUrl;
    }

    public String getOssKeyUrl() {
        return ossKeyUrl;
    }

    public void setOssKeyUrl(String ossKeyUrl) {
        this.ossKeyUrl = ossKeyUrl;
    }

    public Object getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof AuraMateWrongQuestionModel)) return false;
        AuraMateWrongQuestionModel that = (AuraMateWrongQuestionModel) o;
        return getId() == that.getId();
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId());
    }
}
