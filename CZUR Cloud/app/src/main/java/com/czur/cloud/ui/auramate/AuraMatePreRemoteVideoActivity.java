package com.czur.cloud.ui.auramate;

import static com.czur.czurutils.log.CZURLogUtilsKt.logE;

import android.annotation.SuppressLint;
import android.app.KeyguardManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.media.MediaPlayer;
import android.os.Build;
import android.os.Bundle;
import android.os.PowerManager;
import android.os.Vibrator;
import android.text.TextUtils;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.Group;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.TransShareUserPopupAdapter;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.RefreshMissedCallEvent;
import com.czur.cloud.event.aurahome.VideoEvent;
import com.czur.cloud.model.AuraMateShareUserModel;
import com.czur.cloud.netty.core.CZURTcpClient;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.component.popup.AuraMateButtonPopup;
import com.google.gson.reflect.TypeToken;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

public class AuraMatePreRemoteVideoActivity extends AuramateBaseActivity implements View.OnClickListener {

    private static final String LOG_TAG = AuraMatePreRemoteVideoActivity.class.getSimpleName();
    private static final int PERMISSION_REQ_ID_RECORD_AUDIO = 22;
    private static final int PERMISSION_REQ_ID_CAMERA = PERMISSION_REQ_ID_RECORD_AUDIO + 1;
    private ImageView remoteVideoBackBtn;
    private ImageView dialogOutBtn;
    private ImageView callInBtn;
    private ImageView callOutBtn;
    private Group callGroup;
    private String ownerId;
    private ImageView callTransferBtn;
    private Group callTransferGroup;

    private RelativeLayout videoLoadingRl;
    private ImageView loadingImg;
    private boolean isCallIn;
    private boolean isTransfer;
    private AuraMateButtonPopup.Builder builder;
    private AuraMateButtonPopup auraMateListPopup;
    private RecyclerView dialogRecyclerView;
    private TransShareUserPopupAdapter popupAdapter;
    private List<AuraMateShareUserModel> auraMateShareUserModels;
    private List<AuraMateShareUserModel> tempModels;
    private UserPreferences userPreferences;
    private String callId;
    private String udidFrom;
    private String userIdTo;
    private TextView confirmBtn;
    private MediaPlayer mMediaPlayer;
    private Vibrator vibrator;
    private long[] pattern;
    private int i;
    private boolean isRun = false;
    private boolean isMissedCall = false;
    private BroadcastReceiver receiver;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.transparent);
        PowerManager pm = ((PowerManager) getSystemService(POWER_SERVICE));
        @SuppressLint("InvalidWakeLockTag") PowerManager.WakeLock screenLock = pm.newWakeLock(PowerManager.SCREEN_DIM_WAKE_LOCK | PowerManager.ACQUIRE_CAUSES_WAKEUP, "TAG");
        screenLock.acquire(10 * 60 * 1000L /*10 minutes*/);
        KeyguardManager km = (KeyguardManager) getSystemService(Context.KEYGUARD_SERVICE);
        final KeyguardManager.KeyguardLock kl = km.newKeyguardLock("unLock");
        kl.disableKeyguard();
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON  //保持屏幕长亮
                | WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED          //锁屏状态下显示
                | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON            //打开屏幕
                | WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD);       //解锁
        WindowManager.LayoutParams params = getWindow().getAttributes();
        params.systemUiVisibility = View.SYSTEM_UI_FLAG_LOW_PROFILE;
        BarUtils.setNavBarVisibility(this,false);
        ScreenUtils.setFullScreen(this);
        setContentView(R.layout.activity_pre_remote_video);
        initComponent();
        registerEvent();
    }


    @Override
    public boolean PCNeedFinish() {
        if (vibrator != null) {
            vibrator.cancel();
        }
        killMediaPlayer();
        return !TextUtils.isEmpty(equipmentId);
    }

    private void initComponent() {
        isCallIn = getIntent().getBooleanExtra("isCallIn", false);
        isTransfer = getIntent().getBooleanExtra("isTransfer", false);
        callId = getIntent().getStringExtra("callId");
        udidFrom = getIntent().getStringExtra("udidFrom");
        ownerId = getIntent().getStringExtra("ownerId");

        userPreferences = UserPreferences.getInstance(this);
        remoteVideoBackBtn = (ImageView) findViewById(R.id.remote_video_back_btn);
        dialogOutBtn = (ImageView) findViewById(R.id.dialog_out_btn);

        callInBtn = (ImageView) findViewById(R.id.call_out_btn);
        callOutBtn = (ImageView) findViewById(R.id.call_in_btn);
        callGroup = (Group) findViewById(R.id.call_group);
        callTransferBtn = (ImageView) findViewById(R.id.call_transfer_btn);
        callTransferGroup = (Group) findViewById(R.id.call_transfer_group);
        videoLoadingRl = (RelativeLayout) findViewById(R.id.video_loading_rl);
        loadingImg = (ImageView) findViewById(R.id.loading_img);
        vibrator = (Vibrator) this.getSystemService(this.VIBRATOR_SERVICE);
        pattern = new long[]{100, 1200, 500, 1200, 500};

        if (isCallIn) {
            isMissedCall = false;
            checkVideoTimeOut();
            callGroup.setVisibility(View.VISIBLE);
            playAudio();
        } else {
            isRun = true;
            callGroup.setVisibility(View.GONE);
            callTransferGroup.setVisibility(View.GONE);
        }

        Animation imgAnim = AnimationUtils.loadAnimation(
                this, R.anim.dialog_anim);
        loadingImg.startAnimation(imgAnim);
        videoLoadingRl.setVisibility(View.VISIBLE);
        initDialogList();
    }

    private void checkVideoTimeOut() {
        i = 0;
        new Thread(new Runnable() {
            @Override
            public void run() {
                while (i <= 29) {
                    try {
                        if (isRun || isMissedCall) {
                            i = 0;
                            break;
                        }
                        i++;
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (!isRun) {
                            EventBus.getDefault().post(new RefreshMissedCallEvent(EventType.REFRESH_MISSED_CALL));
                            finish();
                        }
                    }
                });
            }

        }).start();
    }

    private void playAudio() {
        try {
            killMediaPlayer();
            mMediaPlayer = MediaPlayer.create(this, R.raw.ringtone);
//            mMediaPlayer.prepare();
            mMediaPlayer.start();
            mMediaPlayer.setLooping(true);
            vibrator.vibrate(pattern, 0);
        } catch (IllegalStateException e) {
            logE(e.toString());
        }
    }

    private void killMediaPlayer() {
        if (null != mMediaPlayer) {
            mMediaPlayer.release();
        }
    }

    private void registerEvent() {
        dialogOutBtn.setOnClickListener(this);
        remoteVideoBackBtn.setOnClickListener(this);
        callOutBtn.setOnClickListener(this);
        callInBtn.setOnClickListener(this);
        callTransferBtn.setOnClickListener(this);

        //注册锁屏广播接收者
        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_SCREEN_OFF);
        receiver = new LockScreenReceiver();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            registerReceiver(receiver, filter, Context.RECEIVER_NOT_EXPORTED);
        }else {
            registerReceiver(receiver, filter);
        }
    }

    private void initDialogList() {
        builder = new AuraMateButtonPopup.Builder(this);
        auraMateListPopup = builder.create();
        dialogRecyclerView = (RecyclerView) auraMateListPopup.findViewById(R.id.aura_home_recyclerView);
        confirmBtn = (TextView) auraMateListPopup.findViewById(R.id.confirm_btn);
        popupAdapter = new TransShareUserPopupAdapter(this, auraMateShareUserModels);
        popupAdapter.setOnItemClickListener(new TransShareUserPopupAdapter.onItemClickListener() {
            @Override
            public void onItemClick(int position, AuraMateShareUserModel auraMateShareUserModel, boolean isEmpty) {
                if (isEmpty) {
                    userIdTo = auraMateShareUserModel.getMemberId() + "";
                    confirmBtn.setBackground(getDrawable(R.drawable.btn_rec_5_bg_with_blue_aura_home));
                    confirmBtn.setEnabled(true);
                    confirmBtn.setClickable(true);
                } else {
                    confirmBtn.setBackground(getDrawable(R.drawable.btn_rec_5_bg_with_light_blue_aura_home));
                    confirmBtn.setEnabled(false);
                    confirmBtn.setClickable(false);
                }

            }
        });
        confirmBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                auraMateListPopup.cancel();
                CZURTcpClient.getInstance().videoRequestSecond(AuraMatePreRemoteVideoActivity.this, equipmentId, 3, userIdTo, callId);
                ActivityUtils.finishActivity(AuraMatePreRemoteVideoActivity.this);
            }
        });
        dialogRecyclerView.setHasFixedSize(true);
        dialogRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        dialogRecyclerView.setAdapter(popupAdapter);
        tempModels = new ArrayList<>();
        auraMateShareUserModels = new ArrayList<>();
        if (isCallIn || !isTransfer) {
            getShareUserList();
        }
    }

    private void getShareUserList() {
        HttpManager.getInstance().request().getAuraMateShareUser(userPreferences.getUserId(), equipmentId, userPreferences.getUserId(), new TypeToken<List<AuraMateShareUserModel>>() {
        }.getType(), new MiaoHttpManager.CallbackNetwork<AuraMateShareUserModel>() {
            @Override
            public void onNoNetwork() {

            }

            @Override
            public void onStart() {

            }

            @Override
            public void onResponse(MiaoHttpEntity<AuraMateShareUserModel> entity) {
                tempModels = new ArrayList<>();
                List<AuraMateShareUserModel> bodyList = entity.getBodyList();
                for (AuraMateShareUserModel auraMateShareUserModel : bodyList) {
                    if (auraMateShareUserModel.getMemberId() != auraMateShareUserModel.getOwnerId()) {
                        tempModels.add(auraMateShareUserModel);
                    }
                }
                if (isCallIn) {
                    if (tempModels.size() > 0 && tempModels.get(0).getOwnerId() == Integer.parseInt(userPreferences.getUserId())) {
                        callTransferGroup.setVisibility(View.VISIBLE);
                    } else {
                        callTransferGroup.setVisibility(View.GONE);
                    }

                }
                popupAdapter.refreshData(tempModels);
            }

            @Override
            public void onFailure(MiaoHttpEntity<AuraMateShareUserModel> entity) {

            }

            @Override
            public void onError(Exception e) {
                logE(e.toString());
            }
        });

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.call_transfer_btn:
                if (!isFinishing()) {
                    auraMateListPopup.show();
                }
                break;
            case R.id.call_in_btn:
                isRun = true;
                if (vibrator != null) {
                    vibrator.cancel();
                }
                killMediaPlayer();
                Intent intent = new Intent(this, AuraMateRemoteVideoActivity.class);
                intent.putExtra("ownerId", ownerId);
                intent.putExtra("isCallIn", isCallIn);
                intent.putExtra("udidFrom", udidFrom);
                intent.putExtra("equipmentId", equipmentId);
                intent.putExtra("callId", callId);
                intent.putExtra("userIdTo", userIdTo);
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS);
                ActivityUtils.startActivity(intent);
                ActivityUtils.finishActivity(this);
                break;
            case R.id.call_out_btn:
                vibrator.cancel();
                killMediaPlayer();
                CZURTcpClient.getInstance().videoRequestSecond(this, equipmentId, 2, userIdTo, callId);
                ActivityUtils.finishActivity(this);
                break;
            case R.id.remote_video_back_btn:
            case R.id.dialog_out_btn:
                ActivityUtils.finishActivity(this);
                break;
            default:
                break;
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            //case APP_IS_READY_FOR_VIDEO:
            case VIDEO_CANCEL:
                // case DEVICE_IS_READY_FOR_VIDEO_TIME_OUT:
                VideoEvent videoEvent1 = (VideoEvent) event;
                if (videoEvent1.getDeviceUdid().equals(equipmentId)) {
                    ActivityUtils.finishActivity(this);
                }
                break;
            case DEVICE_CANCEL_VIDEO:
                isMissedCall = true;
                EventBus.getDefault().post(new RefreshMissedCallEvent(EventType.REFRESH_MISSED_CALL));
                ActivityUtils.finishActivity(this);
                break;

            default:
                break;
        }
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        isRun = false;
        if (vibrator != null) {
            vibrator.cancel();
        }
        killMediaPlayer();
        if (receiver != null) {
            unregisterReceiver(receiver);
        }

    }


    private final class LockScreenReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            vibrator.cancel();
            killMediaPlayer();
            CZURTcpClient.getInstance().videoRequestSecond(AuraMatePreRemoteVideoActivity.this, equipmentId, 2, userIdTo, callId);
            ActivityUtils.finishActivity(AuraMatePreRemoteVideoActivity.class);
        }
    }



}
