package com.czur.cloud.ui.starry.meeting.baselib.utils

import android.annotation.SuppressLint
import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.net.wifi.WifiManager
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.Utils
import com.czur.cloud.ui.starry.meeting.base.CZURAtyManager
import com.czur.czurutils.log.logI
import com.czur.czurutils.log.logV

/**
 * Created by 陈丰尧 on 2021/7/12
 */
private const val TAG = "NetStatusUtil"
private const val WIFI_LEVEL_COUNT = 3 // wifi信号强度一共有3个等级

class NetStatusUtil(val context: Context) {
    // 网络状态
    enum class NetStatus {
        NO_NET_WORK,    // 无网络
        WIFI,           // wifi连接
        ETHERNET        // 有线网连接
    }

    // 网络状态监控
    private val _netStatus = MutableLiveData(NetStatus.NO_NET_WORK)
    val netStatus: LiveData<NetStatus> = _netStatus

    // wifi网络状态
    private var wifiNetWork: Pair<String, NetworkCapabilities>? = null

    // 有线网络状态
    private var ethernetNetWork: Pair<String, NetworkCapabilities>? = null

    // 连接管理对象
    private val connectivityManager: ConnectivityManager by lazy {
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    }

    // 监控请求
    private val networkRequest = NetworkRequest.Builder()
        .addTransportType(NetworkCapabilities.TRANSPORT_WIFI)       // 监控wifi状态
        .addTransportType(NetworkCapabilities.TRANSPORT_ETHERNET)   // 监控有线网络状态
        .build()

    // 网络状态改变的回调
    private val networkCallBack = NetworkCallbackImpl()


    /**
     * 开始监控网络状态
     */
    fun startWatching() {
        connectivityManager.registerNetworkCallback(networkRequest, networkCallBack)
    }

    /**
     * 停止监控网络状态
     */
    fun stopWatching() {
        connectivityManager.unregisterNetworkCallback(networkCallBack)
        // 停止观测后, 状态恢复成初始状态
        wifiNetWork = null
        ethernetNetWork = null
        updateNetStatus()
    }

    /**
     * 更新网络数据
     * @param netId:    连接的ID, 在断开连接时使用
     * @param networkCapabilities:  网络连接的详细信息, 可以从中获取例如wifi信号强度, 能否连接Internet等信息
     */
    private fun updateNetWork(
        netId: String,
        networkCapabilities: NetworkCapabilities
    ) {
        when {
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> {
                logV("更新WIFI")
                wifiNetWork = netId to networkCapabilities
            }
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> {
                logV("更新有线网")
                ethernetNetWork = netId to networkCapabilities
            }
            else -> {
                logV("其他网络:${networkCapabilities}")
            }
        }
        updateNetStatus()
    }

    /**
     * 清除网络连接
     * 在断开连接时调用
     * @param networkID: 连接的ID信息
     */
    private fun clearNetwork(networkID: String) {
        when {
            ethernetNetWork?.first == networkID -> {
                ethernetNetWork = null
            }
            wifiNetWork?.first == networkID -> {
                wifiNetWork = null
            }
            else -> {
                logI("未知ID:${networkID}")
            }
        }
        updateNetStatus()
    }

    /**
     * 更新当前网络连接的状态
     */
    private fun updateNetStatus() {
        // 有线连接了, 以有线为准
        // 没有有线, 检查wifi
        // 都没连接 则变成 无网状态
        when {
            ethernetNetWork != null -> {
                _netStatus.postValue(NetStatus.ETHERNET)
            }
            wifiNetWork != null -> {
                _netStatus.postValue(NetStatus.WIFI)
            }
            else -> {
                _netStatus.postValue(NetStatus.NO_NET_WORK)
            }
        }

    }

    /**
     * 获取wifi信号强度
     * wifi信号最多分为3个等级(UI式样决定) [WIFI_LEVEL_COUNT]
     * 如果获取不到信号强度,则会返回 -1
     * NOTE: signalStrength 标记是api29 可以调用, 但是低于29也可以,所以忽略警告
     */
    @SuppressLint("NewApi")
    fun getWifiSignalLevel(): Int {
        return wifiNetWork?.let {
            val networkCapabilities = it.second
            WifiManager.calculateSignalLevel(networkCapabilities.signalStrength, WIFI_LEVEL_COUNT)
        } ?: -1
    }

    // 网络状态的回调
    private inner class NetworkCallbackImpl : ConnectivityManager.NetworkCallback() {

        // 网络已连接的回调
        override fun onAvailable(network: Network) {
            super.onAvailable(network)
            logV("网络已连接")
            val networkCapabilities = connectivityManager.getNetworkCapabilities(network)
            networkCapabilities?.let {
                updateNetWork(network.toString(), it)
                logV(
                    "能否访问互联网-${it.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)}"
                )
            }
        }

        // 连接信息改变,比如wifi信号强度等信息
        override fun onCapabilitiesChanged(
            network: Network,
            networkCapabilities: NetworkCapabilities
        ) {
            super.onCapabilitiesChanged(network, networkCapabilities)
            logV("网络变化")
            updateNetWork(network.toString(), networkCapabilities)

        }

        /**
         * 网络已断开
         */
        override fun onLost(network: Network) {
            super.onLost(network)
            logV("网络已断开-${network}")
            clearNetwork(network.toString())
        }
    }
}

// 通用工具方法

/**
 * 网络是否连接
 */
fun isNetworkConnected(context: Context = CZURAtyManager.appContext): Boolean {
    val mConnectivityManager =
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    val mNetworkInfo = mConnectivityManager.activeNetworkInfo
    if (mNetworkInfo != null) {
        return mNetworkInfo.isAvailable
    }

    return false
}