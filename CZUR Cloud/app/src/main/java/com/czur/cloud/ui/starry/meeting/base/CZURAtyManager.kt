package com.czur.cloud.ui.starry.meeting.base

import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Bundle
import com.blankj.utilcode.util.Utils
import com.czur.cloud.ui.starry.meeting.baselib.utils.has
import com.czur.czurutils.log.logI

/**
 * Created by 陈丰尧 on 12/29/20
 */
object CZURAtyManager : Application.ActivityLifecycleCallbacks {
    private const val TAG = "CZURAtyManager"
//    private const val SETTING_PKG = "com.czur.starry.device.settings"


    private val atys = mutableListOf<BaseActivity>()
    var appContext: Context = Utils.getApp().applicationContext


    private val am by lazy {
        appContext.getSystemService(android.app.ActivityManager::class.java)
    }
    private val packageName by lazy {
        appContext.packageName
    }

    fun hasActivity(simpleName: String): Boolean {
        return atys.has {
            it::class.java.simpleName == simpleName
        }
    }

    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
        if (activity is BaseActivity) {
            logI("添加:${activity::class.java.simpleName}(onCreate)")
            atys.add(activity)
        } else {
            logI("${activity.javaClass.name}没有继承BaseActivity,不进行管理")
        }
    }

    override fun onActivityStarted(activity: Activity) {
    }

    override fun onActivityResumed(activity: Activity) {
    }

    override fun onActivityPaused(activity: Activity) {
    }

    override fun onActivityStopped(activity: Activity) {
        if (!isAppOnForeground()) {
            // ActivityManager 中的最后一个Activity是自己,表示回到了Home
            logI("App:${packageName} 处于后台, 清理禁止后台显示的Activity")
            clearAllBackground()
        }
    }

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
    }

    override fun onActivityDestroyed(activity: Activity) {
        logI("remove:${activity::class.java.simpleName}(onDestroy)")
        atys.remove(activity)
    }

    /**
     * 获取当前Activity
     */
    fun currentActivity(): BaseActivity {
        return atys.last()
    }

    /**
     * 将Activity栈中, 所有标记为不能后台显示的Activity都finish
     * 直到找到 标记为可以后台显示的Activity
     */
    private fun clearAllBackground() {
        atys.asReversed().forEach {
            if (it.banBackGround) {
                if (!it.isFinishing) {
                    it.finish()
                }
            } else {
                return
            }
        }
    }

    /**
     * 获取Context, 如果没有Activity,就使用Application的Context
     */
    fun getContext() = if (atys.isNotEmpty()) atys.last() else appContext


    private fun isAppOnForeground(): Boolean {
        val runningAppProcesses = am.runningAppProcesses
        runningAppProcesses.forEach { appProcess ->
            // 有可能是自动对焦中,自动对焦时是不算后台的
            if (isPkgForeground(packageName, appProcess)
//                || isPkgForeground(SETTING_PKG, appProcess)
            ) {
                return true
            }
        }
        return false
    }

    private fun isPkgForeground(
        packageName: String,
        appProcess: android.app.ActivityManager.RunningAppProcessInfo,
    ): Boolean {
        return appProcess.processName == packageName
                && appProcess.importance == android.app.ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND

    }

}