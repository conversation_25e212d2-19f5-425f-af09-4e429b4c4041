package com.czur.cloud.model

data class CountryCode(
//{
//    "id": 1,
//    "countryName": "中国",
//    "code": "86",
//    "sort": 0,
//    "countryCode": "CN",
//    "countryNameUs": null,
//    "countryNameTw": null,
//    "defaultCountry": true
//},
    val code: String = "",
    val countryCode: String = "",
    val countryName: String = "",
    val countryNameTw: String = "",
    val countryNameUs: String = "",
    var defaultCountry: Boolean = false,
    val id: Int = 0,
    val sort: Int = 0
)

data class CountryList(
    val countryList: List<CountryCode> = listOf(),
    val country: String = "",
    val ip: String = ""
)