package com.czur.cloud.ui.starry.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.czur.cloud.R
import com.czur.cloud.ui.starry.meeting.baselib.utils.getString
import com.czur.cloud.ui.starry.meeting.bean.UserStatus
import com.czur.cloud.ui.starry.model.StarryCallRecordModel
import com.czur.cloud.ui.starry.utils.Tools
import com.czur.cloud.ui.starry.utils.singleClick
import kotlinx.android.synthetic.main.starry_item_recently.view.*

class StarryRecentlyAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    val TYPE_NORMAL = 0
    val TYPE_FOOTER = 1

    private var mDatas = ArrayList<StarryCallRecordModel>()
    private var mfooterView: View?=null
    private var mCount: Int=0

    private var recentlyTotal = 0

    private var footerCountVis = View.GONE
    inner class InnerHodler(itemView: View): RecyclerView.ViewHolder(itemView) {
    }

    inner class Contactfooter internal constructor(view: View) : RecyclerView.ViewHolder(view) {
        var mTextView: TextView = view.findViewById<View>(R.id.footer) as TextView
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int):RecyclerView.ViewHolder {
        if(mfooterView != null && viewType == TYPE_FOOTER){
            return Contactfooter(mfooterView!!)
        }else{
        val itemView =
            LayoutInflater.from(parent.context).inflate(R.layout.starry_item_recently, parent, false)
        return InnerHodler(itemView)
        }
    }

    @SuppressLint("SuspiciousIndentation")
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is Contactfooter){
            holder.itemView.setBackgroundResource(R.color.white)
            holder.mTextView.text = getString(R.string.starry_recently_bottom_msg_count, mCount )
            holder.itemView.visibility = footerCountVis
        }

        else
        holder.itemView.apply {
            with(mDatas[position]){
//                logI("StarryRecentlyAdapter.mDatas[${position}]=${mDatas[position]}")
                val title = this.meetingName
//                val time1 = this.createTime
                val time1 = getTimeStr()
                val time = Tools.getFormatDateTimeForRecently(time1)
                val meetingStatus = this.meetingStatus
                val userStatus = this.status
                val enter = this.enter

                starry_item_recently_title.text = title
                starry_item_recently_time.text = time

                // 会议的状态  1：未开始 NOT_STARTED；2：进行中 PCOCESSING；3：结束 END
                //0：呼叫中 CALLING 1：未接听超时 TIMEOUT 2：加入 JOINED 3：拒接 REJECTED
                //4：被移除 REMOVED 5：离开 LEAVE 6：掉线 OFFLINE 7：暂时离开 HOLD_ON
                starry_item_recently_red_point.visibility = View.INVISIBLE
                starry_item_recently_time_ll.visibility = View.VISIBLE
                starry_item_recently_status_btn.visibility = View.GONE
                starry_item_recently_nojoin.visibility = View.GONE
                starry_item_recently_title.setTextColor(context.getColor(R.color.starry_text_title_color_black))
                if (meetingStatus == 2) {
                    // 进行中，可参加
                    if (userStatus == UserStatus.STATUS_CALLING.toString() ||
                        userStatus == UserStatus.STATUS_TIMEOUT.toString() ||
                        userStatus == UserStatus.STATUS_JOINED.toString() ||
                        userStatus == UserStatus.STATUS_OFFLINE.toString() ||
                        userStatus == UserStatus.STATUS_IOS_HOLD.toString() ||
                        userStatus == UserStatus.STATUS_HOLD_ON.toString()) {
                        starry_item_recently_time_ll?.visibility = View.GONE
                        starry_item_recently_status_btn?.visibility = View.VISIBLE
                        starry_item_recently_status_btn?.let {
                            it.visibility = View.VISIBLE
                            // pc端是否加入, 显示的文言不同
                            val textRes = if (isPCEnter && userStatus == UserStatus.STATUS_JOINED.toString()) {
                                R.string.starry_recently_join_by_app
                            } else {
                                R.string.starry_recently_join
                            }
                            it.setText(textRes)
                        }

                        // red point
                        starry_item_recently_red_point?.visibility = View.VISIBLE

                        // 暂时离开会议后，最近会议列表-该条会议名文字颜色为黑色
                        if (userStatus == UserStatus.STATUS_HOLD_ON.toString() ||
                            userStatus == UserStatus.STATUS_OFFLINE.toString() ){
                            starry_item_recently_title?.setTextColor(context.getColor(R.color.starry_text_title_color_black))
                        }else{
                            starry_item_recently_title?.setTextColor(context.getColor(R.color.starry_text_color_red))
                        }
                    }
                } else if (meetingStatus == 3) {
                    // 已结束，未参加
                    if (userStatus == UserStatus.STATUS_CALLING.toString() ||
                        userStatus == UserStatus.STATUS_TIMEOUT.toString() ||
                        userStatus == UserStatus.STATUS_REJECT.toString()){
                        starry_item_recently_nojoin?.visibility = View.VISIBLE
                        starry_item_recently_title?.setTextColor(context.getColor(R.color.starry_text_color_red))
                    }
                }

                // 未参加字样
                if (!enter && meetingStatus != 2){
                    starry_item_recently_nojoin?.visibility = View.VISIBLE
                }


                <EMAIL> {
                    if (onItemPickListener != null) {
                        onItemPickListener?.onItemPick(position, mDatas[position])
                    }
                }

                starry_item_recently_status_btn?.singleClick {
                    if (onItemPickListener != null) {
                        onItemPickListener?.onItemButtonClick(position, mDatas[position])
                    }
                }
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        if(position == itemCount-1)return TYPE_FOOTER
        return TYPE_NORMAL
    }

    override fun getItemCount(): Int {
        if(mfooterView != null){
            return mDatas.size+1
        }
        return mDatas.size
    }

    fun setTotal(total: Int){
        recentlyTotal = total
    }

    fun setmDatas(datas: List<StarryCallRecordModel>) {
        mDatas.clear()
        mDatas.addAll(datas)
        notifyDataSetChanged()
    }

    fun setFooterVis(footerCountVis:Int){
        this.footerCountVis = footerCountVis
    }
    fun setFooterFivew(footerView: View){
        mfooterView = footerView
        notifyItemInserted(itemCount - 1)
    }

    fun setFootertext(count: Int){
        mCount = count
    }

    fun setOnItemPickListener(onItemPickListener: OnItemPickListener?) {
        this.onItemPickListener = onItemPickListener
    }

    private var onItemPickListener: OnItemPickListener? = null

    //自定义点击事件接口
    interface OnItemPickListener {
        fun onItemPick(position: Int, model: StarryCallRecordModel)
        fun onItemButtonClick(position: Int, model: StarryCallRecordModel)
    }
}