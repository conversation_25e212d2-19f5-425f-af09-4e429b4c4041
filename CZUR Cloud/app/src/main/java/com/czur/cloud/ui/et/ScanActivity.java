package com.czur.cloud.ui.et;

import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.Vibrator;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.event.BindSuccessEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.model.UserDeviceModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;

import org.greenrobot.eventbus.EventBus;

import cn.bingoogolapple.qrcode.core.QRCodeView;
import cn.bingoogolapple.qrcode.zbar.ZBarView;

public class ScanActivity extends BaseActivity implements QRCodeView.Delegate {

    private ImageView scanBackBtn;
    private QRCodeView mQRCodeView;
    private UserPreferences userPreferences;
    private HttpManager httpManager;
    private static final String ERROR_CODE = "errorStr";
    private static final String SUCCESS_CODE = "successStr";

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.black_2a);
        BarUtils.setNavBarColor(this,getColor(R.color.black_2a));
        setContentView(R.layout.activity_scan);
        userPreferences = UserPreferences.getInstance(this);
        httpManager = HttpManager.getInstance();
        mQRCodeView = (ZBarView) findViewById(R.id.zxingview);
        mQRCodeView.setDelegate(this);

        scanBackBtn = (ImageView) findViewById(R.id.scan_back_btn);
        scanBackBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ActivityUtils.finishActivity(ScanActivity.this);
            }
        });
    }

    @Override
    protected void onStart() {
        super.onStart();
        mQRCodeView.startCamera();
        mQRCodeView.startSpotAndShowRect();
    }

    @Override
    protected void onStop() {
        mQRCodeView.stopCamera();
        super.onStop();
    }

    @Override
    protected void onDestroy() {
        mQRCodeView.onDestroy();
        super.onDestroy();
    }

    private void vibrate() {
        Vibrator vibrator = (Vibrator) getSystemService(VIBRATOR_SERVICE);
        vibrator.vibrate(150);
    }

    @Override
    public void onScanQRCodeSuccess(String result) {
        if (result.contains("serialNumber=")) {
            String s_num = result.substring(result.indexOf("serialNumber=") + 13, result.length());
            logI("result:" + result+";s_num:"+ s_num);
            vibrate();
            bindPublicDevice(s_num);
        } else {
            showMessage(R.string.invalid_qr_code);
        }


    }

    @Override
    public void onCameraAmbientBrightnessChanged(boolean isDark) {

    }

    private void bindPublicDevice(final String s_num) {
        HttpManager.getInstance().request().deviceBindPublicBind(userPreferences.getUserId(), s_num, UserDeviceModel.class, new MiaoHttpManager.CallbackNetwork<UserDeviceModel>() {
            @Override
            public void onNoNetwork() {
                showMessage(R.string.toast_no_connection_network);
                mQRCodeView.startSpotAndShowRect();
            }

            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<UserDeviceModel> entity) {
                hideProgressDialog();
                if (entity.getBody().getSerialNumber() != null) {
                    if (!entity.getBody().getBindUserId().equals(userPreferences.getUserId())) {
                        EventBus.getDefault().post(new BindSuccessEvent(EventType.BIND_SUCCESS));
                        String text = String.format(getString(R.string.toast_device_become_user), entity.getBody().getAlias());
                        Intent intent = new Intent(ScanActivity.this, BindDeviceSuccessActivity.class);
                        intent.putExtra("deviceId", entity.getBody().getId());
                        intent.putExtra(SUCCESS_CODE, text);
                        startActivity(intent);
                    }
                } else {
                    Intent intent = new Intent(ScanActivity.this, BindDeviceFailedActivity.class);
                    intent.putExtra(ERROR_CODE, String.format(getString(R.string.message_has_been_bound), entity.getBody().getName()));
                    ActivityUtils.startActivity(intent);
                }
            }

            @Override
            public void onFailure(MiaoHttpEntity<UserDeviceModel> entity) {
                hideProgressDialog();
                if (entity.getCode() == MiaoHttpManager.STATUS_DEVICE_NOT_EXIST) {
                    Intent intent = new Intent(ScanActivity.this, BindDeviceFailedActivity.class);
                    intent.putExtra(ERROR_CODE, getString(R.string.toast_device_bind_exist));
                    startActivity(intent);
                } else if (entity.getCode() == MiaoHttpManager.STATUS_DEVICE_NOT_SAME_AREA) {
                    Intent intent = new Intent(ScanActivity.this, BindDeviceFailedActivity.class);
                    intent.putExtra(ERROR_CODE, getString(R.string.toast_device_bind_not_same_area));
                    startActivity(intent);
                } else if (entity.getCode() == MiaoHttpManager.STATUS_DEVICE_NOT_BIND) {
                    Intent intent = new Intent(ScanActivity.this, BindDeviceActivity.class);
                    intent.putExtra("bindStr", getString(R.string.toast_device_not_bind));
                    intent.putExtra("s_num", s_num);
                    startActivity(intent);
                }
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
                ActivityUtils.startActivity(BindDeviceFailedActivity.class);
            }
        });
    }

    @Override
    public void onScanQRCodeOpenCameraError() {
        showMessage(R.string.can_not_open_camera);
    }

    /**
     * 隐藏虚拟按键，并且全屏
     */
    protected void hideBottomUIMenu() {
        //隐藏虚拟按键，并且全屏
        if (Build.VERSION.SDK_INT > 11 && Build.VERSION.SDK_INT < 19) {
            View v = this.getWindow().getDecorView();
            v.setSystemUiVisibility(View.GONE);
        } else if (Build.VERSION.SDK_INT >= 19) {
            //for new api versions.
            View decorView = getWindow().getDecorView();
            int uiOptions = View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION // hide nav bar
                    | View.SYSTEM_UI_FLAG_FULLSCREEN // hide status bar
                    | View.SYSTEM_UI_FLAG_IMMERSIVE;
            decorView.setSystemUiVisibility(uiOptions);
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);
        }
    }

}