package com.czur.cloud.ui.component.popup;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.czur.cloud.R;


/**
 * Created by shaojun
 * Email：<EMAIL>
 */

public class AuraMateUpdatePopup extends Dialog {
    public static final float DIMMED_OPACITY = 0.2f;

    private AuraMateUpdatePopup(Context context, int theme) {
        super(context, theme);
    }

    public static class Builder {
        private Context context;
        private String equipmentUID;
        private OnEnsureClickListener onEnsureClickListener;

        public interface OnEnsureClickListener {
            void onEnsureClick();
        }

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setOnPositiveListener(OnEnsureClickListener onEnsureClickListener) {
            this.onEnsureClickListener = onEnsureClickListener;
            return this;
        }

        public AuraMateUpdatePopup create() {
            LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            final AuraMateUpdatePopup dialog = new AuraMateUpdatePopup(context, R.style.TransparentProgressDialog);
            View layout = commonCustomPopLayout(inflater, dialog);
            dialog.setContentView(layout);
            dialog.setCanceledOnTouchOutside(true);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            params.dimAmount = DIMMED_OPACITY;
            return dialog;
        }

        private View commonCustomPopLayout(LayoutInflater inflater, final AuraMateUpdatePopup dialog) {
            WindowManager.LayoutParams lp = dialog.getWindow().getAttributes();
            lp.dimAmount = 0.6f;
            dialog.getWindow().setAttributes(lp);
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            View layout = inflater.inflate(R.layout.dialog_aura_mate_update, null, false);
            ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);
            dialog.addContentView(layout, params);
            ImageView backBtn = (ImageView) layout.findViewById(R.id.back_btn);
            TextView updateBtn = (TextView) layout.findViewById(R.id.update_btn);
            updateBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.dismiss();
                    if (onEnsureClickListener != null) {
                        onEnsureClickListener.onEnsureClick();
                    }
                }
            });
            backBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.dismiss();
                }
            });
            return layout;
        }
    }
}
