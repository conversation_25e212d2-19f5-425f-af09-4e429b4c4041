package com.czur.cloud.model;

public class AuraMateFilesModel {


    /**
     * id : h2vaf21eiemdvbx
     * name : 1
     * fileCounts : 0
     * latestUpdate : null
     * seqId : 183
     * isAutoCreate : false
     */

    private String id;
    private String name;
    private int fileCounts;
    private Object latestUpdate;
    private int seqId;
    private boolean isAutoCreate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getFileCounts() {
        return fileCounts;
    }

    public void setFileCounts(int fileCounts) {
        this.fileCounts = fileCounts;
    }

    public Object getLatestUpdate() {
        return latestUpdate;
    }

    public void setLatestUpdate(Object latestUpdate) {
        this.latestUpdate = latestUpdate;
    }

    public int getSeqId() {
        return seqId;
    }

    public void setSeqId(int seqId) {
        this.seqId = seqId;
    }

    public boolean isIsAutoCreate() {
        return isAutoCreate;
    }

    public void setIsAutoCreate(boolean isAutoCreate) {
        this.isAutoCreate = isAutoCreate;
    }
}
