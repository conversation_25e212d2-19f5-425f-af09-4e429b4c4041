package com.czur.cloud.ui.mirror;

import static com.czur.czurutils.log.CZURLogUtilsKt.logE;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.app.Activity;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattService;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.KeyEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.clj.fastble.BleManager;
import com.clj.fastble.callback.BleWriteCallback;
import com.clj.fastble.data.BleDevice;
import com.clj.fastble.exception.BleException;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.SittingCommonEvent;
import com.czur.cloud.event.SittingModelChangeEvent;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.auramate.reportfragment.ReportUtil;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.component.recyclerview.RecycleViewDivider;
import com.czur.cloud.ui.mirror.adapter.SittingUpdateAdapter;
import com.czur.cloud.ui.mirror.comm.FastBleConstants;
import com.czur.cloud.ui.mirror.comm.FastBleHexUtils;
import com.czur.cloud.ui.mirror.comm.FastBleMD5Utils;
import com.czur.cloud.ui.mirror.comm.FastBleOperationUtils;
import com.czur.cloud.ui.mirror.comm.FastBleToolUtils;
import com.czur.cloud.ui.mirror.component.DownLoadProgressBar;
import com.czur.cloud.ui.mirror.download.DownloadRunnable;
import com.czur.cloud.ui.mirror.download.TaskInfo;
import com.czur.cloud.ui.mirror.model.SittingDeviceModel;
import com.czur.cloud.ui.mirror.model.SittingUpdateModel;
import com.czur.cloud.ui.mirror.mydialog.SittingDialog;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.UUID;

public class SittingUpdateActivity extends SittingBaseActivity implements View.OnClickListener {
    //使用Handler制造一个200毫秒为周期的循环delayMillis
    private static final int DELAY_MILLIS = 200;
    private static final int HANDLER_WHAT = 666;
    private static final int DEFINE_BYTE_1024 = 1024;
    private static final int USE_SPEED = 3 * 1024;

    private static final float MAX_SIZE = DEFINE_BYTE_1024*DEFINE_BYTE_1024* 10L;
    private ImageView imgUpdateState, imgBack;
    private TextView tvWarn;
    private TextView btnUpdate;
    private TextView tvTitle;
    private RecyclerView rvUpdateLog;
    private SittingUpdateAdapter adapter;
    private SittingUpdateModel sittingUpdateModel;
    private LinearLayout noupdate_main_ll, update_log_ll, size_time_ll;
    private RelativeLayout update_main_rl, rl_progress;
    private TextView update_support_msg_tv, update_support_msg2_tv, update_download_title_tv, update_download_cancel;
    private TextView mSize;
    private DownLoadProgressBar mProgress;
    private int progressMax = 100; //总的大小
    private TaskInfo info;//任务信息
    private DownloadRunnable runnable;//下载任务
    private float oldReciveLen = 0.0f;
    private String deviceVersion, currentVer;
    private String titleUpdate = "";
    private BleDevice bleDevice;
    private String sendUpdatePackageType = FastBleConstants.HEAD_UPDATE_SEND;   //发送还是续发的类型；
    private long seekFileUpdate = 0L;
    private int downloadTime = 0, deviceTime = 0;
    private long fileAllSize = 0L;
    private boolean isShowFailFalg = false; // 是否已经显示失败的对话框了？
    private boolean isShowAlertFalg = false; // 是否已经显示温馨提示的对话框了？
    private long oldSendFileLen = 0L;     // 已经发给设备的字节数;
    private long oldSendFileTime = 0;        // 上次收到反馈字节数的时间；
    private TextView update_support_copy;   // copy button
    private TextView update_log_content;    //log,日志
    private boolean isUpdating = false;     // 是否正在升级？正在升级，禁止返回；
    private UserPreferences userPreferences;
    private boolean isSelfUpdateSuccess = false; //是app自己升级成功的,不是pc升级成功的

    private Handler handler690=new Handler();
    private Runnable runnable690;
    private CloudCommonPopup commonPopup;
    private boolean isCancelUpdate = false; // 按了取消按钮吗？
    private boolean is670Coming = false;    // 670先到了，就要忽略660的返回

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        logI("SittingUpdateActivity.onEvent="+event.getEventType());

        String params = "";
        if (event instanceof SittingCommonEvent) {
            SittingCommonEvent commonEvent = (SittingCommonEvent) event;
            params = commonEvent.getParams();
        }

        switch (event.getEventType()) {

            // 固件升级取消
            case SITTING_UPDATE_CANCEL:
                //设备端：0， 取消成功；1，取消失败"	APP端务必监听设备端返回，因为升级过程已经进行时无法取消
                logI("SittingUpdateActivity.SITTING_UPDATE_CANCEL.params="+params);
                if (params != null && params.equals("0")) {
                    hideProgressDialog();

                    clearHandler();
                    changeUIState(FastBleConstants.STATE_PREPARE);

                    // 取消升级后复位操作
                    FastBleOperationUtils.onUpdateCancelOperation();

                    // 重新初始化update页面
                    // 初始化update页面
                    updateInit();

                    seekFileUpdate = 0;
                    FastBleOperationUtils.setSeekFile(seekFileUpdate);
                    sendUpdatePackageType = FastBleConstants.HEAD_UPDATE_SEND;
                    logI("SittingUpdateActivity.SITTING_UPDATE_CANCEL.seekFileUpdate="+seekFileUpdate);

                }
                break;

            // 固件接收长度
            case SITTING_UPDATE_RECV_SIZE:
                if (params != null && !params.equals("")) {
                    isSelfUpdateSuccess = true;

                    long dataSize = Long.parseLong(params);

//                    if ((dataSize>0)  && (dataSize < fileAllSize)) {
                    if (dataSize>0) {
                        // 更新UI接收的数据
                        if ( sittingUpdateModel != null) {
                            sittingUpdateModel.setToDeviceSize(dataSize);
                            userPreferences.setSittingUpdateModel(sittingUpdateModel);
                        }

                        int l = (int) ((float) dataSize/ (float) fileAllSize * 100);
                        String s = String.format("%.2f", ((float) dataSize/ (float) fileAllSize * 100)) + "%";
                        mSize.setText(s);
                        mProgress.setMaxValue(progressMax);
                        mProgress.setCurrentValue(l);

                        // 计算剩余时间
                        int leftTime = deviceTime;
                        long cTime = Calendar.getInstance().getTimeInMillis();
                        long ccTime = cTime - oldSendFileTime;
                        if (ccTime<1) {
                            ccTime = 1;
                        }
                        long speed = (dataSize - oldSendFileLen) / ccTime * 1000; //  byte/s 当前速度
                        if (speed < 1){
                            speed = 1;
                        }
                        leftTime = (int)((fileAllSize - dataSize) / speed / 60);    //剩余需要的时间min
                        if (leftTime < 1){
                            leftTime = 1;
                        }
                        if (leftTime > deviceTime*10){
                            leftTime = deviceTime;
                        }
                        update_download_title_tv.setText(String.format(getString(R.string.sitting_download_to_device), leftTime+""));

                        oldSendFileTime = cTime;
                        oldSendFileLen = dataSize;
                    }else{
                        if (!isShowFailFalg) {
                            logI("SittingUpdateActivity.SITTING_UPDATE_RECV_SIZE.error");
                            if (FastBleOperationUtils.isIsChangeUpdateActivity()) {
                                showFailDialog(SittingUpdateActivity.this);
                            }
                            isShowFailFalg = true;
                            clearHandler();
                            //升级完成，需要恢复置灰设置项按钮
                            FastBleOperationUtils.setSettingItemDisable(false);
                        }
                    }
                }
                break;

            // 设备离线了
            case SITTING_OFFLINE:
                if (FastBleOperationUtils.getSittingUpdateStatus() == FastBleConstants.STATE_PREPARE
                        ||FastBleOperationUtils.getSittingUpdateStatus() == FastBleConstants.STATE_UPDATING
                        ||FastBleOperationUtils.getSittingUpdateStatus() == FastBleConstants.STATE_DOWNLOAD
                        ||FastBleOperationUtils.getSittingUpdateStatus() == FastBleConstants.STATE_SENDING
                        ||FastBleOperationUtils.getSittingUpdateStatus() == FastBleConstants.STATE_RESEND) {
                    // 弹出对话框，告知用户，并需要返回主页面
                    if (FastBleOperationUtils.isIsChangeUpdateActivity() && !isShowAlertFalg && (!isCancelUpdate)) {
                        isShowAlertFalg = true;
                        showSittingDailog(SittingUpdateActivity.this);
                    }
                }
                break;

            // 固件升级
            case SITTING_UPDATE_QUERY:     //固件版本查询  设备端返回：固件版本号字符串"
                if (params != null && !params.equals("")) {
                    // 进行比对，是否需要升级
                    // 检查是否需要升级
//                    checkUpdateFromServer(params);
                }
                break;

            case SITTING_UPDATE_RESEND:     //固件续发  "设备端：续发文件偏移位置   //APP：固件偏移数据+MD5校验值"
                logI("SittingUpdateActivity.onEvent=SITTING_UPDATE_RESEND");
                if (isCancelUpdate) {
                    // 是取消了固件升级；
                    return;
                }

                sendUpdatePackageType = FastBleConstants.HEAD_UPDATE_RESEND;
                if (params != null && !params.equals("")) {

                    isSelfUpdateSuccess = true;
                    seekFileUpdate = Long.parseLong(params);
                    logI("SittingUpdateActivity.SITTING_UPDATE_RESEND.seekFileUpdate="+seekFileUpdate);

                    changeUIState(FastBleConstants.STATE_RESEND);
                }
                break;

            case SITTING_UPDATE_RECV_OK650:     //固件接收完毕  字符串：0，接收成功；1，接收数据发生错误	设备端必须保证数据接收结果被正确发送给APP一次
                isSelfUpdateSuccess = true;
                logI("SittingUpdateActivity.SITTING_UPDATE_RECV_OK650.is670Coming="+is670Coming);
                if (params != null && params.equals("0")) {

                    Thread mThread = Thread.currentThread();
                    logI("SittingUpdateActivity.SITTING_UPDATE_RECV_OK650.params="+params,
                            "isUpdateSend660="+FastBleOperationUtils.isIsUpdateSend660(),
                            "currentThread="+mThread);
                    //  防止660发送2遍；因为 SITTING_UPDATE_RECV_OK 会收到多次，什么原因呢？
                    if (!FastBleOperationUtils.isIsUpdateSend660()) {
                        FastBleOperationUtils.setIsUpdateSend660(true);
                        FastBleOperationUtils.SetDeviceParamsStr("", FastBleConstants.HEAD_UPDATE_UPDATEING);
                    }
                }else{
                    logI("SittingUpdateActivity.SITTING_UPDATE_RECV_OK650.showFailDialog.NO！params="+params);
                    if (isCancelUpdate) {
                        // 是取消了固件升级；
                        isShowFailFalg = true;
                    }
                    clearHandler();
                    changeUIState(FastBleConstants.STATE_UPDATE_FAIL);
                    if (FastBleOperationUtils.isIsChangeUpdateActivity() && !isShowFailFalg) {
                        showFailDialog(SittingUpdateActivity.this);
                        isShowFailFalg = true;
                    }
                }
                //升级完成，需要恢复置灰设置项按钮
                FastBleOperationUtils.setSettingItemDisable(false);

                break;

            case SITTING_UPDATE_UPDATEING660:     //设备开始升级 //设备端返回：0，开始升级；1，其他错误"
                isSelfUpdateSuccess = true;
                logI("SittingUpdateActivity.SITTING_UPDATE_UPDATEING660.is670Coming="+is670Coming);
                if (is670Coming){
                    return;
                }

                if (params != null && params.equals("0")) {
                    logI("SittingUpdateActivity.SITTING_UPDATE_UPDATEING660.开始升级啦.params="+params);
                    changeUIState(FastBleConstants.STATE_REBOOT);
                }else{
                    logI("SittingUpdateActivity.SITTING_UPDATE_UPDATEING660.showFailDialog.升级失败！params="+params);
                    if (!isShowFailFalg) {
                        if (FastBleOperationUtils.isIsChangeUpdateActivity()) {
                            showFailDialog(SittingUpdateActivity.this);
                        }
                        isShowFailFalg = true;
                    }
                    clearHandler();
                }

                break;

            case SITTING_UPDATE_RESULT670:     //设备升级结果 字符串：0，升级成功；1，升级发生错误
                logI("SittingUpdateActivity.SITTING_UPDATE_RESULT670.is670Coming="+is670Coming,
                        "isSelfUpdateSuccess="+isSelfUpdateSuccess);
                is670Coming = true;
                if (!isSelfUpdateSuccess){
                    return;
                }
                // 升级完成，需要恢复置灰设置项按钮
                FastBleOperationUtils.setSettingItemDisable(false);

                if (params != null && params.equals("0")) {
                    logI("SittingUpdateActivity.SITTING_UPDATE_RESULT670.升级成功！params="+params);
                    changeUIState(FastBleConstants.STATE_UPDATE_SUCESS);
                }else{
                    logI("SittingUpdateActivity.SITTING_UPDATE_RESULT670.showFailDialog.升级失败！params="+params);
                    clearHandler();
                    changeUIState(FastBleConstants.STATE_UPDATE_FAIL);
                    if (!isShowFailFalg) {
                        if (FastBleOperationUtils.isIsChangeUpdateActivity()) {
                            showFailDialog(SittingUpdateActivity.this);
                        }
                        isShowFailFalg = true;
                    }
                }

                break;

            default:
                break;
        }
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_sitting_update);

        ReportUtil.verifyStoragePermissions(this);

        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
        FastBleOperationUtils.setIsChangeUpdateActivity(true);
        FastBleOperationUtils.setIsUpdateSend660(false);

        currentVer = getIntent().getStringExtra("deviceVersion");

        int status = FastBleOperationUtils.getSittingUpdateStatus();
        if (status == FastBleConstants.STATE_NEW){
            deviceVersion = getIntent().getStringExtra("deviceVersion");
        }
        if (status == FastBleConstants.STATE_RESEND) {
            sendUpdatePackageType = FastBleConstants.HEAD_UPDATE_RESEND;
        }
        if (status == FastBleConstants.STATE_SENDING) {
            sendUpdatePackageType = FastBleConstants.HEAD_UPDATE_SEND;
        }

        userPreferences = UserPreferences.getInstance(this);
        isShowFailFalg = false;
        isShowAlertFalg = false;
        isSelfUpdateSuccess = false;
        FastBleOperationUtils.setIsUpdateSend660(false);
        isCancelUpdate = false;
        is670Coming = false;

        seekFileUpdate = FastBleOperationUtils.getSeekFile();
        logI("SittingUpdateActivity.onCreate.STATE_RESEND.seekFileUpdate="+seekFileUpdate);

        initView();
        setPageTitle(R.string.sitting_home_update);

        initListener();

        logI("SittingUpdateActivity.getSittingUpdateStatus="+status);

        // 初始化update页面
        updateInit();

        //deviceVersion  //smart_sitting
        titleUpdate = getString(R.string.smart_sitting) + " " + deviceVersion;
        tvTitle.setText(titleUpdate);

        checkUpdateFromServer();

        boolean activityBackFlag = getIntent().getBooleanExtra("activityBackFlag", false);
        logI("SittingUpdateActivity.activityBackFlag="+activityBackFlag);
        if (activityBackFlag){
            finish();
        }

    }

    // 初始化update页面
    private void updateInit(){

        sittingUpdateModel = userPreferences.getSittingUpdateModel();
        if (sittingUpdateModel == null){
            sittingUpdateModel = new SittingUpdateModel();
            sittingUpdateModel.initSittingUpdateModel();
        }
        deviceVersion=sittingUpdateModel.getVersion();
        if (deviceVersion == null){
            deviceVersion = "";
        }

        //实例化任务信息对象
//        String filePath = Environment.getExternalStorageDirectory().getAbsolutePath() + "/Download/";
        String filePath = CZURConstants.MIRROR_PATH + "Download/";
        String download_url = sittingUpdateModel.getPackageUrl();
        String fileName = sittingUpdateModel.getVersion() + ".zip";
        fileAllSize = sittingUpdateModel.getFileSize();
        logI("updateInit.filePath="+filePath,
                "download_filename="+download_url,
                "fileName="+fileName,
                "fileAllSize="+fileAllSize);

        info = new TaskInfo();
        info.setPath(filePath);
        info.setName(fileName);
        info.setUrl(download_url);
        info.setContentLen(fileAllSize);

        // 初始化ble的update特征；
        initBle();

    }

    // 初始化传输进度条
    private void initTranslateView(long size){
        int status = FastBleOperationUtils.getSittingUpdateStatus();
        logI("SittingUpdateActivity.initTranslateView="+status,"size="+size);
        if (status == FastBleConstants.STATE_SENDING ||
                status == FastBleConstants.STATE_RESEND){

            long totaleSize = fileAllSize;
            if (sittingUpdateModel!=null && sittingUpdateModel.getToDeviceSize()>0){
                totaleSize = fileAllSize - sittingUpdateModel.getToDeviceSize();
            }
            deviceTime = (int) ((totaleSize / USE_SPEED) / 60); // 分钟
            int l = (int) ((float) size / (float) fileAllSize * 100);
            String s = String.format("%.2f", ((float) size/ (float) fileAllSize * 100)) + "%";
            mSize.setText(s);
            mProgress.setMaxValue(progressMax);
            mProgress.setCurrentValue(l);
            update_download_title_tv.setText(String.format(getString(R.string.sitting_download_to_device), deviceTime+""));
        }
    }

    @Override
    protected void onResume(){
        super.onResume();

//        if (!EventBus.getDefault().isRegistered(this)) {
//            EventBus.getDefault().register(this);
//        }

        // 初始化传输进度条 resume
        long size = 0L;
        if (sittingUpdateModel != null) {
            size = sittingUpdateModel.getToDeviceSize();
        }
        initTranslateView(size);
    }


    private void initView() {
        noupdate_main_ll = findViewById(R.id.noupdate_main_ll);
        update_main_rl = findViewById(R.id.update_main_rl);
        update_log_ll = findViewById(R.id.update_log_ll);
        btnUpdate = findViewById(R.id.update_btn_tv);
        size_time_ll = findViewById(R.id.size_time_ll);
        update_support_msg_tv = findViewById(R.id.update_support_msg_tv);
        update_support_msg2_tv = findViewById(R.id.update_support_msg2_tv);
        rl_progress = findViewById(R.id.rl_progress);
        update_download_title_tv= findViewById(R.id.update_download_title_tv);
        update_download_cancel = findViewById(R.id.update_download_cancel);

        mProgress = (DownLoadProgressBar) findViewById(R.id.dp_game_progress);
        mSize = (TextView) findViewById(R.id.tv_size1);
        //初始化下载进度
        mSize.setText( "0.00%");
        //初始化下载速度
        oldSendFileTime = Calendar.getInstance().getTimeInMillis();

        tvWarn = findViewById(R.id.tv_warn);
        tvTitle = findViewById(R.id.tv_title);
        imgUpdateState = findViewById(R.id.img_state);

        imgBack = findViewById(R.id.top_bar_back_btn);
        rvUpdateLog = findViewById(R.id.rv_update_log);
        rvUpdateLog.setLayoutManager(new LinearLayoutManager(this));
        //添加自定义分割线：可自定义分割线高度和颜色
        rvUpdateLog.addItemDecoration(new RecycleViewDivider(
                this, LinearLayoutManager.HORIZONTAL, SizeUtils.dp2px(8), getColor(R.color.white)));
        update_log_content = findViewById(R.id.update_log_content);

        update_support_copy = findViewById(R.id.update_support_copy);
        update_support_copy.setOnClickListener(this);
    }

    // 状态改变，UI更新
    private void changeUIState(int state) {
        FastBleOperationUtils.setSittingUpdateStatus(state);

        tvWarn.setVisibility(View.GONE);
        size_time_ll.setVisibility(View.GONE);
        btnUpdate.setVisibility(View.GONE);
        rl_progress.setVisibility(View.GONE);
        noupdate_main_ll.setVisibility(View.GONE);

        update_main_rl.setVisibility(View.VISIBLE);
        update_support_msg_tv.setVisibility(View.VISIBLE);
        update_support_msg2_tv.setVisibility(View.VISIBLE);
        update_log_ll.setVisibility(View.VISIBLE);

        update_download_cancel.setVisibility(View.GONE);

        switch (state) {
            case FastBleConstants.STATE_UPDATE_SUCESS:
            case FastBleConstants.STATE_NEW:
                noupdate_main_ll.setVisibility(View.VISIBLE);
                update_main_rl.setVisibility(View.GONE);
                update_support_msg_tv.setVisibility(View.GONE);
                update_support_msg2_tv.setVisibility(View.GONE);
                update_log_ll.setVisibility(View.GONE);
                ((TextView)findViewById(R.id.tv_title_no)).setText(titleUpdate);
                update_support_copy.setVisibility(View.GONE);
                // 升级中，需要置灰设置项按钮
                FastBleOperationUtils.setSettingItemDisable(false);
                FastBleOperationUtils.setSittingUpdateStatus(FastBleConstants.STATE_NEW);
                break;

            case FastBleConstants.STATE_UPDATE_PC:
                imgUpdateState.setImageResource(R.mipmap.sitting_update_state_before);
                tvTitle.setText(titleUpdate);
                tvWarn.setVisibility(View.VISIBLE);
                size_time_ll.setVisibility(View.GONE);
                update_support_msg_tv.setText(getString(R.string.sitting_support_msg2));
                break;

            case FastBleConstants.STATE_UPDATE_FAIL:
                checkDownloadTimes();
            case FastBleConstants.STATE_PREPARE:
                update_main_rl.setVisibility(View.VISIBLE);
                imgUpdateState.setImageResource(R.mipmap.sitting_update_state_before);
                tvTitle.setText(titleUpdate);
                tvWarn.setVisibility(View.GONE);
                size_time_ll.setVisibility(View.VISIBLE);
                btnUpdate.setVisibility(View.VISIBLE);
                btnUpdate.setText(getResources().getString(R.string.sitting_update_now));
                FastBleOperationUtils.setSittingUpdateStatus(FastBleConstants.STATE_PREPARE);
                break;

            case FastBleConstants.STATE_DOWNLOAD:
                update_main_rl.setVisibility(View.VISIBLE);
                imgUpdateState.setImageResource(R.mipmap.sitting_update_state_working);
                tvTitle.setText(getString(R.string.sitting_updating));
                size_time_ll.setVisibility(View.GONE);
                tvWarn.setVisibility(View.VISIBLE);
                tvWarn.setText(getString(R.string.sitting_update_warn_msg2));
                rl_progress.setVisibility(View.VISIBLE);
                update_download_title_tv.setText(String.format(getString(R.string.sitting_download_from_server), downloadTime+""));
                update_support_msg_tv.setText(getString(R.string.sitting_support_msg3));

                // 取消按钮
                update_download_cancel.setVisibility(View.VISIBLE);

                // 升级中，需要置灰设置项按钮
                FastBleOperationUtils.setSettingItemDisable(true);
                break;

            case FastBleConstants.STATE_SENDING:
            case FastBleConstants.STATE_RESEND:
                // 升级中，需要置灰设置项按钮
                FastBleOperationUtils.setSettingItemDisable(true);

                update_main_rl.setVisibility(View.VISIBLE);
                imgUpdateState.setImageResource(R.mipmap.sitting_update_state_working);
                size_time_ll.setVisibility(View.GONE);
                tvTitle.setText(getString(R.string.sitting_updating));
                tvWarn.setVisibility(View.VISIBLE);
                tvWarn.setText(getString(R.string.sitting_update_warn_msg2));
                rl_progress.setVisibility(View.VISIBLE);
                update_download_title_tv.setText(String.format(getString(R.string.sitting_download_to_device), deviceTime+""));
                update_support_msg_tv.setText(getString(R.string.sitting_support_msg3));

                // 取消按钮
                update_download_cancel.setVisibility(View.VISIBLE);

                //该传输状态，已经发送过升级包了。
                logI("SittingUpdateActivity.isIsSittingUpdateSend()="+FastBleOperationUtils.isIsSittingUpdateSend());
                // 此时需要向设备端发送大文件了，怎么发送呢？
                String filename = info.getPath() + info.getName();

                if (sittingUpdateModel != null && sittingUpdateModel.getToDeviceSize()>0)  {
                    logI("SittingUpdateActivity.sittingUpdateModel.getToDeviceSize()="+sittingUpdateModel.getToDeviceSize());
                    return;
                }
                if (seekFileUpdate < fileAllSize){
                    readBinFile(filename);
                }else{
                    // 传输文件失败
                    EventBus.getDefault().post(new SittingCommonEvent(EventType.SITTING_UPDATE_RECV_OK650, "1"));
                }

                break;

            case FastBleConstants.STATE_REBOOT:
                // 升级中，需要置灰设置项按钮
                FastBleOperationUtils.setSettingItemDisable(false);

                update_main_rl.setVisibility(View.VISIBLE);
                imgUpdateState.setImageResource(R.mipmap.sitting_update_state_working);
                size_time_ll.setVisibility(View.GONE);
                tvTitle.setText(getString(R.string.sitting_updating));
                tvWarn.setVisibility(View.VISIBLE);
                tvWarn.setText(getString(R.string.sitting_update_warn_msg2));
                rl_progress.setVisibility(View.VISIBLE);
                update_download_title_tv.setText(getString(R.string.sitting_download_reboot));
                mProgress.setVisibility(View.GONE);
                mSize.setVisibility(View.GONE);
                update_support_msg_tv.setText(getString(R.string.sitting_support_msg3));
                break;

        }

    }

    private void initListener() {
        btnUpdate.setOnClickListener(this);
        imgBack.setOnClickListener(this);
        // 取消按钮
        update_download_cancel.setOnClickListener(this);

//        setNetListener();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            // 取消升级
            case R.id.update_download_cancel:
                showConfirmCancelDialog();
                break;

            case R.id.update_support_copy:
                String data = getString(R.string.sitting_support_msg_url);
                // 获取系统剪贴板
                ClipboardManager mClipboardManager = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
                // 创建一个剪贴数据集，包含一个普通文本数据条目（需要复制的数据）,其他的还有
                // newHtmlText、 // newIntent、  // newUri、 // newRawUri
                ClipData clipData = ClipData.newPlainText(null, data);
                mClipboardManager.setPrimaryClip(clipData);
                ToastUtils.showLong(R.string.sitting_support_copy_ok);
                break;

            case R.id.update_btn_tv:

                isShowFailFalg = false;
                isCancelUpdate = false;
                is670Coming = false;
                SittingDeviceModel currentModel = userPreferences.getSittingDeviceModel();
                currentModel.setUpdateCancel(isCancelUpdate);
                userPreferences.setSittingDeviceModel(currentModel);
                EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_CANCEL_UPDATE, currentModel));
                logI("R.id.update_btn_tv.setUpdateCancel="+isCancelUpdate);
                isSelfUpdateSuccess = true;

                // 点击升级按钮了
                // 告知设备
                // 新固件版本发送：1
                FastBleOperationUtils.SetDeviceParamsStr(deviceVersion, FastBleConstants.HEAD_UPDATE_NEW_VER);
                FastBleOperationUtils.threadSleep(200);

                FastBleOperationUtils.SetDeviceParamsStr("0", FastBleConstants.HEAD_UPDATE_VERTYPE);

                // 升级中，需要置灰设置项按钮
                changeUIState(FastBleConstants.STATE_DOWNLOAD);

                startRunable();

                break;

            case R.id.top_bar_back_btn:
                if (!isUpdating) {
                    stopRunable();
                    // 此处保存文件信息，以备后续断点续传使用；

                    finish();
                }
                break;
        }
    }

    /**
     * 开始下载按钮监听
     * @param //view
     */
    private void startRunable() {
        //创建下载任务
        runnable = new DownloadRunnable(info);
        //开始下载任务
        new Thread(runnable).start();
        //开始Handler循环
        if (handlerUpdate != null) {
            handlerUpdate.sendEmptyMessageDelayed(HANDLER_WHAT, DELAY_MILLIS);
        }
    }

    /**
     * 停止下载按钮监听
     * @param //view
     */
    private void stopRunable() {
        //调用DownloadRunnable中的stop方法，停止下载
        if (runnable != null) {
            runnable.stop();
            runnable = null;//强迫症，不用的对象手动置空
        }
        isUpdating = false;
    }

    // 检查是否需要升级
    private void checkUpdateFromServer(){
        // 1, 获取设备当前的版本号
        // 2, 获取api的升级接口
        // 3, 判断比对是否需要升级
        long size = sittingUpdateModel.getFileSize();
        int update = sittingUpdateModel.getUpdate();
        logI("checkUpdateFromServer.size="+size+";update="+update);

        //===public static final int STATE_NEW         = 0;    //没有升级,当前最新
        if (update != 1){
            // 无需升级，直接返回
            changeUIState(FastBleConstants.STATE_NEW);
        }else {
            //需要升级
            String note=sittingUpdateModel.getNote();
            List<String> versionInfo = new ArrayList<>();
            if (note != null) {
//                versionInfo.add(note);
                versionInfo = FastBleToolUtils.getStringToList(note);//把JSON字符串转为对象
            }
//            versionInfo = sittingUpdateModel.getNote();
            logI("checkUpdateFromServer.versionInfo="+versionInfo);
            adapter = new SittingUpdateAdapter(versionInfo, SittingUpdateActivity.this);
            rvUpdateLog.setAdapter(adapter);

            if (size > MAX_SIZE){
                //===public static final int STATE_UPDATE_PC   = 9;    //无法升级（PC升级）
                // 该版本文件大小过大，需要PC升级
                changeUIState(FastBleConstants.STATE_UPDATE_PC);
            }else {
                // 可以升级
                checkDownloadTimes();

                int status = FastBleOperationUtils.getSittingUpdateStatus();

                // 初始化传输进度条 create
                long size1 = 0L;
                if (status == FastBleConstants.STATE_SENDING) {
                    if (sittingUpdateModel != null) {
                        size1 = sittingUpdateModel.getToDeviceSize();
                    }
                }
                if (status == FastBleConstants.STATE_RESEND) {
                    size1 = FastBleOperationUtils.getSeekFile();
                }
                initTranslateView(size1);

                changeUIState(status);

            }
        }
    }

    // 计算升级时长（下载+传输+升级）
    private void checkDownloadTimes(){
        // 计算升级时长（下载+传输+升级）；
        // 1,根据文件大小，计算下载时长；（1024k/s）
        // 2，根据文件大小，计算传输到设备的时长（5k/s）
        // 3,设备自己升级重启的时长（120s）
        String showSize = FastBleToolUtils.getShowFileSize(fileAllSize);

        int idownloadspeed = DEFINE_BYTE_1024*10;   // kb
        deviceTime = (int) ((fileAllSize / USE_SPEED) / 60) *2; // 分钟
        downloadTime = (int)((fileAllSize / idownloadspeed) / 60 ) ; // 分钟
        int reboottime = 2; // 重启时长2分钟；
        int totaleTime = downloadTime + deviceTime + reboottime;

        String strSize = getString(R.string.sitting_update_size) + showSize + "  " + getString(R.string.sitting_update_current_ver) + currentVer ;
        String strTime = getString(R.string.sitting_update_time) + "" + totaleTime + getString(R.string.sitting_home_sitting_min);
        ((TextView)findViewById(R.id.tv_size)).setText(strSize);
        ((TextView)findViewById(R.id.tv_time)).setText(strTime);

    }

    //用于更新进度的Handler
    private Handler handlerUpdate = new Handler(new Handler.Callback() {
        @Override
        public boolean handleMessage(Message msg) {
            logI("handleMessage.msg="+msg.toString());

            boolean isStop = true;
            if (runnable != null){
                isStop = runnable.isStop();
            }
            if (!isStop){
                //使用Handler制造一个200毫秒为周期的循环delayMillis
                handlerUpdate.sendEmptyMessageDelayed(HANDLER_WHAT, DELAY_MILLIS);
            }else{
                handlerUpdate.removeCallbacksAndMessages(null);
            }

            //计算下载进度
            float nowReciveLen = (float) info.getCompletedLen();
            int l = (int) ((float) info.getCompletedLen() / (float) fileAllSize * 100);

            //设置进度条进度
            String s = String.format("%.2f", ((float) info.getCompletedLen()/ (float) fileAllSize * 100)) + "%";
            mSize.setText(s);
            mProgress.setMaxValue(progressMax);
            mProgress.setCurrentValue(l);
            update_download_title_tv.setText(String.format(getString(R.string.sitting_download_from_server), downloadTime+""));
            if (l>=progressMax) {//当进度>=100时，取消Handler循环
                handlerUpdate.removeCallbacksAndMessages(null);
                mProgress.setMaxValue(progressMax);
                mProgress.setCurrentValue(0);
                oldReciveLen = 0;
                changeUIState(FastBleConstants.STATE_SENDING);
                mSize.setText("0.00%");
                return true;
            }

            oldReciveLen = (float) info.getCompletedLen();
            return true;
        }
    });

    private void clearHandler(){
        isUpdating = false;

        stopRunable();

        FastBleOperationUtils.setSittingUpdateStatus(FastBleConstants.STATE_PREPARE);
        if ( sittingUpdateModel != null) {
            sittingUpdateModel.setToDeviceSize(0);
        }else{
            sittingUpdateModel = new SittingUpdateModel();
            sittingUpdateModel.initSittingUpdateModel();
        }
        userPreferences.setSittingUpdateModel(sittingUpdateModel);
        checkUpdateFromServer();
        changeUIState(FastBleConstants.STATE_UPDATE_FAIL);

//        bleDevice = FastBleOperationUtils.getBleDevice();
//        BluetoothGatt gatt = BleManager.getInstance().getBluetoothGatt(bleDevice);;
//        UUID serviceUUID = UUID.fromString(FastBleConstants.exportUUID);
//        UUID characteristicUUID = UUID.fromString(FastBleConstants.writeFwUUID);
//        BluetoothGattService service = gatt.getService(serviceUUID);
//
//        BleManager.getInstance().stopNotify(uuid_service, uuid_characteristic_notify);

    }

    @Override
    protected void onDestroy() {

        isUpdating =false;
        FastBleOperationUtils.setIsChangeUpdateActivity(false);

        //在Activity销毁时移除回调和msg，并置空，防止内存泄露
//        if(handlerUpdate != null){
//            handlerUpdate.removeCallbacksAndMessages(null);
//            handlerUpdate = null;
//        }
        super.onDestroy();
    }

    // 初始化ble的update特征；
    private void initBle() {

        bleDevice = FastBleOperationUtils.getBleDevice();
        BluetoothGatt bleGatt;
        BluetoothGattService bleService;
        BluetoothGattCharacteristic bleCharacteristicFWWrite;

        if (bleDevice == null) {
            return;
        }
        bleGatt = BleManager.getInstance().getBluetoothGatt(bleDevice);
        bleService = bleGatt.getService(UUID.fromString(FastBleConstants.exportUUID.toLowerCase()));
        bleCharacteristicFWWrite = bleService.getCharacteristic(UUID.fromString(FastBleConstants.writeFwUUID.toLowerCase()));

        enableNotification();

    }

    // 打开ble提醒通知
    private void enableNotification() {
        BluetoothGatt gatt = BleManager.getInstance().getBluetoothGatt(bleDevice);;
        UUID serviceUUID = UUID.fromString(FastBleConstants.exportUUID);
        UUID characteristicUUID = UUID.fromString(FastBleConstants.writeFwUUID);

        if (gatt != null){
            return;
        }

        BluetoothGattService service = gatt.getService(serviceUUID);

        if (service != null) {
            BluetoothGattCharacteristic characteristic = findNotifyCharacteristic(service, characteristicUUID);
            if (characteristic != null) {
            }
        }
    }

    private static BluetoothGattCharacteristic findNotifyCharacteristic(BluetoothGattService service, UUID characteristicUUID) {
        BluetoothGattCharacteristic characteristic = null;
        List<BluetoothGattCharacteristic> characteristics = service.getCharacteristics();
        for (BluetoothGattCharacteristic c : characteristics) {
            if ((c.getProperties() & BluetoothGattCharacteristic.PROPERTY_NOTIFY) != 0
                    && characteristicUUID.equals(c.getUuid())) {
                characteristic = c;
                break;
            }
        }
        if (characteristic != null)
            return characteristic;
        for (BluetoothGattCharacteristic c : characteristics) {
            if ((c.getProperties() & BluetoothGattCharacteristic.PROPERTY_INDICATE) != 0
                    && characteristicUUID.equals(c.getUuid())) {
                characteristic = c;
                break;
            }
        }
        return characteristic;
    }

    // ble写入操作
    // `void write(BleDevice bleDevice,
    //                  String uuid_service,
    //                  String uuid_write,
    //                  byte[] data,
    //                  boolean split,
    //                  boolean sendNextWhenLastSuccess,
    //                  long intervalBetweenTwoPackage,
    //                  BleWriteCallback callback)`
    //Tips:
    //- 在没有扩大MTU及扩大MTU无效的情况下，当遇到超过20字节的长数据需要发送的时候，需要进行分包。参数`boolean split`表示是否使用分包发送；无`boolean split`参数的`write`方法默认对超过20字节的数据进行分包发送。
    //- 关于`onWriteSuccess`回调方法: `current`表示当前发送第几包数据，`total`表示本次总共多少包数据，`justWrite`表示刚刚发送成功的数据包。
    //- 对于分包发送的辅助策略，可以选择发送上一包数据成功之后发送下一包，或直接发送下一包，参数`sendNextWhenLastSuccess`表示是否待收到`onWriteSuccess`之后再进行下一包的发送。默认true。
    //- 参数`intervalBetweenTwoPackage`表示延时多长时间发送下一包，单位ms，默认0。
    private void writeFWMessage(byte[] data){
        //写入
        BleManager.getInstance().write(
                bleDevice,
                FastBleConstants.exportUUID,
                FastBleConstants.writeFwUUID,
                data,
//                true,
//                true,
//                20,
                new BleWriteCallback() {
                    @Override
                    public void onWriteSuccess(int current, int total, byte[] justWrite) {
                        // 发送数据到设备成功（分包发送的情况下，可以通过方法中返回的参数可以查看发送进度）
                        if ((current % 100) == 0 || (current < 10) || (current > total-10)) {
                            logI("SittingUpdateActivity.write.onWriteSuccess.current/total=" +
                                    current + "/" + total);
                        }
                    }

                    @Override
                    public void onWriteFailure(BleException exception) {
                        // 发送数据到设备失败
                        logI("SittingUpdateActivity.write.onWriteFailure="+exception.toString());
                    }
                });
    }

    private void writeFWMessageWithHeader(byte[] data, int len1){
        int len = FastBleConstants.HEAD_LENGTH_COUNT + len1;
        String sixteen = FastBleHexUtils.tenToHex(len);
        String sendData = sixteen + sendUpdatePackageType;
        byte[] res = FastBleHexUtils.hexStr2Bytes(sendData);

        byte[] sendByte = FastBleToolUtils.concat(res, data);

        logI("SittingUpdateActivity.writeFWMessage.len="+len,
                "len1="+len1,
                "data.len="+data.length,
                "sendData="+sendData);//,
//                "res=",res,
//                "sendByte=",sendByte);
        //写入
        writeFWMessage(sendByte);
    }


    // 读取升级包文件，并写入设备；
    private void readBinFile(String filename) {
        logI("TestForUpdate.readBinFile(filename)==begin");

        logI("SittingUpdateActivity.readBinFile.filename====" + filename);
        RandomAccessFile raf;//随机读写器，用于写入文件，实现断点续传
        int len = 0;//每次读取的数组长度

        try {
            //通过文件路径和文件名实例化File
            File file = new File(filename);
            if (!file.exists()){
                logI("SittingUpdateActivity.readBinFile.!file.exists()");
                startRunable();
                return;
            }
            byte[] sendMD5Byte = FastBleMD5Utils.getFileMd5Buffer(file);
//            logI("getFileMD5String.sendMD5Byte=" + Arrays.toString(sendMD5Byte));

            //实例化RandomAccessFile，rwd模式
            raf = new RandomAccessFile(file, "rwd");
            if (seekFileUpdate > fileAllSize){
                seekFileUpdate = fileAllSize;
            }
            raf.seek(seekFileUpdate);

            //从流读取字节数组到缓冲区
            int bufSize = (int)(raf.length() - seekFileUpdate);
            int totaleSize = bufSize + sendMD5Byte.length;
            byte[] buffData = new byte[bufSize];
            len = raf.read(buffData);

            byte[] sendData = FastBleToolUtils.concat(buffData, sendMD5Byte);
            writeFWMessageWithHeader(sendData, totaleSize);
            FastBleOperationUtils.setIsSittingUpdateSend(true);
            raf.close();
        } catch (IOException e) {
            FastBleOperationUtils.setIsSittingUpdateSend(false);
            e.printStackTrace();
            logE("readBinFile.e="+e.toString());
        }
        logI("SittingUpdateActivity.readBinFile.finish!====" + filename);
        logI("TestForUpdate.readBinFile(filename)==end");
    }

    private void showFailDialog(Activity activity){
        SittingDialog dialog = new SittingDialog(activity, R.style.sittingDialog,
            new SittingDialog.OncloseListener() {
                @Override
                public void onClick(boolean confirm) {
                }
            });
        dialog.setTitle(getString(R.string.sitting_model_dialog_update_title));
        dialog.setOneButton(true);
        dialog.setPositiveButton(getString(R.string.sitting_model_dialog_update_yes));
        dialog.setContent(getString(R.string.sitting_update_fail));
        dialog.create();
        dialog.show();
    }

    // 弹出对话框，告知用户，并需要返回主页面
    private void showSittingDailog(Activity activity){
        SittingDialog dialog = new SittingDialog(activity, R.style.sittingDialog,
                new SittingDialog.OncloseListener() {
                    @Override
                    public void onClick(boolean confirm) {
                    }
                });
        dialog.setOneButton(true);
        dialog.setPositiveButton(getString(R.string.sitting_model_dialog_ok));
        dialog.setTitle(getString(R.string.sitting_model_dialog_title));
        dialog.setContent(getString(R.string.sitting_dialog_offline_msg));
        dialog.create();
        dialog.show();
    }

    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (isUpdating) {
            if (keyCode == KeyEvent.KEYCODE_BACK) {
                return true;
            }
        }
        return super.onKeyDown(keyCode, event);
    }

    // 取消升级提示框
    private void showConfirmCancelDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(SittingUpdateActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.sitting_confirm_cancel));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (commonPopup != null) {
                    commonPopup.dismiss();
                }
                cancelDownload();
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        commonPopup = builder.create();
        commonPopup.show();
    }

    private void cancelDownload() {
        logI("SittingUpdateActivity.update_download_cancel=");
        // Loading，等待断开ble，重连，690取消成功
        showProgressDialog(true);

        // 需要断开ble，并重新连接设备，然后判断640，发690
        isCancelUpdate = true;
        String address = FastBleOperationUtils.getBleDevice().getMac();
        SittingDeviceModel currentModel = userPreferences.getSittingDeviceModel();
        currentModel.setUpdateCancel(isCancelUpdate);
        currentModel.setAddress(address);
        userPreferences.setSittingDeviceModel(currentModel);
        logI("cancelDownload.setUpdateCancel="+isCancelUpdate);
        EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_CANCEL_UPDATE, currentModel));

        // 取消升级，直接断开ble，loading，超时1分钟，重连后，收到640，发690；
        runnable690 = new Runnable() {
            @Override
            public void run() {
                hideProgressDialog();
            }
        };
        handler690.postDelayed(runnable690, FastBleConstants.TIMEOUT_FOR_EXPERIENCE);

        BleManager.getInstance().disconnect(FastBleOperationUtils.getBleDevice());
        FastBleOperationUtils.threadSleep(FastBleConstants.RUN_DELAY_TIMES1000);
        EventBus.getDefault().post(new SittingModelChangeEvent(EventType.SITTING_DEVICE_RECONNECT, currentModel));
    }

}
