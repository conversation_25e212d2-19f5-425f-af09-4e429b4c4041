package com.czur.cloud.ui.eshare.widget;
import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.view.*
import android.widget.EditText
import android.widget.RelativeLayout
import android.widget.TextView
import com.blankj.utilcode.util.ScreenUtils
import com.blankj.utilcode.util.StringUtils
import com.czur.cloud.R


/**
 * 通用弹出dialog
 */
class StarryCommonPopup : Dialog {
    val isChecked = false
    var positiveBtn = null
    constructor(context: Context?, theme: Int) : super(context!!, theme) {}

    class Builder(private val context: Context,
                  private val constants: CloudCommonPopupConstants = CloudCommonPopupConstants.COMMON_TWO_BUTTON) {
        private var message: String? = null
        private var title: String? = null
        private var btnPositiveTitle: String? = null
        private var positiveTextColor: Int? = null  // 文字颜色
        private var negativeTextColor: Int? = null // 消极的文字颜色
        private var btnNegativeTitle: String? = null
        private var contentsView: View? = null
        private var positiveListener: DialogInterface.OnClickListener? = null
        private var onNegativeListener: DialogInterface.OnClickListener? = null
        private var onDismissListener: DialogInterface.OnDismissListener? = null
        private var canceledOnTouchOutside: Boolean = false
        private var textViewContentGravity: Int = -999

        fun setMessage(message: String?): Builder {
            this.message = message
            return this
        }

        fun setTitle(title: String?): Builder {
            this.title = title
            return this
        }

        fun setPositiveTitle(title: String?): Builder {
            this.btnPositiveTitle = title
            return this
        }

        fun setPositiveTextColor(color:Int): Builder {
            this.positiveTextColor = color
            return this
        }

        fun setNegativeTitle(title: String?): Builder {
            this.btnNegativeTitle = title
            return this
        }

        fun setNegativeTextColor(color: Int): Builder {
            negativeTextColor = color
            return this
        }

        fun setContentsView(contentsView: View?): Builder {
            this.contentsView = contentsView
            return this
        }

        fun setContentsView(resource: Int): Builder {
            contentsView = LayoutInflater.from(context).inflate(resource, null)
            return this
        }

        fun setOnPositiveListener(positiveListener: DialogInterface.OnClickListener?): Builder {
            this.positiveListener = positiveListener
            return this
        }

        fun setOnNegativeListener(onNegativeListener: DialogInterface.OnClickListener?): Builder {
            this.onNegativeListener = onNegativeListener
            return this
        }

        fun setOnDismissListener(onDismissListener: DialogInterface.OnDismissListener?): Builder {
            this.onDismissListener = onDismissListener
            return this
        }

        fun setCanceledOnTouchOutside(canceledOnTouchOutside : Boolean): Builder {
            this.canceledOnTouchOutside = canceledOnTouchOutside
            return this
        }

        fun setTextContentGravity(textViewContentGravity: Int): Builder {
            this.textViewContentGravity = textViewContentGravity
            return this
        }

        var layout: View? = null
        fun create(): StarryCommonPopup {
            val inflater =
                context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
            val dialog = StarryCommonPopup(context, R.style.TransparentProgressDialog)
             layout = commonCustomPopLayout(inflater, dialog)
            dialog.setContentView(layout!!)
            dialog.setCanceledOnTouchOutside(canceledOnTouchOutside)
            dialog.setCancelable(false)
            val params = dialog.window?.attributes
            params?.dimAmount = DIMMED_OPACITY
            if (ScreenUtils.isLandscape()){//针对高版本(小米12) 横屏状态时,弹出提示窗时,无法拉下状态栏
                dialog.window?.setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE)
            }
            return dialog
        }

        private fun commonCustomPopLayout(
            inflater: LayoutInflater,
            dialog: StarryCommonPopup
        ): View {
            val lp = dialog.window?.attributes
            lp?.dimAmount = 0.8f
            dialog.window?.attributes = lp
            dialog.window?.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
            val layout = inflater.inflate(R.layout.starry_common_custom_popup, null, false)
            val params = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            dialog.addContentView(layout, params)
            val title = layout.findViewById<View>(R.id.title) as TextView
            val message = layout.findViewById<View>(R.id.message) as TextView
            val positiveBtn = layout.findViewById<View>(R.id.positive_button) as TextView
            val middleLineView = layout.findViewById<View>(R.id.middle_line) as View
            val negativeBtn = layout.findViewById<View>(R.id.negative_button) as TextView
            val editText = layout.findViewById<View>(R.id.edt) as EditText
            if (contentsView == null) {
                if (textViewContentGravity != -999){
                    message.gravity = textViewContentGravity
                }
                if (!StringUtils.isEmpty(this.message)) {
                    message.text = this.message + ""
                } else if (constants.message > 0) {
                    message.text = context.resources.getString(constants.message)
                }
                if (!StringUtils.isEmpty(this.title)) {
                    title.text = this.title + ""
                } else if (constants.title > 0) {
                    title.text = context.resources.getString(constants.title)
                }
                if (!StringUtils.isEmpty(this.btnPositiveTitle)) {
                    positiveBtn.text = this.btnPositiveTitle + ""
                } else if (constants.positiveBtn > 0) {
                    positiveBtn.text = context.resources.getString(constants.positiveBtn)
                }
                if (!StringUtils.isEmpty(this.btnNegativeTitle)) {
                    negativeBtn.text = this.btnNegativeTitle + ""
                } else if (constants.negativeBtn > 0) {
                    negativeBtn.text = context.resources.getString(constants.negativeBtn)
                }

                if (constants.positiveBtn > 0) {
                    positiveBtn.visibility = View.VISIBLE
                } else {
                    positiveBtn.visibility = View.GONE
                    middleLineView.visibility = View.GONE
                }

                if (constants.negativeBtn > 0) {
                    negativeBtn.visibility = View.VISIBLE
                } else {
                    negativeBtn.visibility = View.GONE
                    middleLineView.visibility = View.GONE
                }

                if (constants.negativeBtn > 0 && constants.positiveBtn > 0){
                    middleLineView.visibility = View.VISIBLE
                }
            } else {
                message.visibility = View.GONE
                title.visibility = View.GONE
                if (constants.positiveBtn > 0) {
                    positiveBtn.text = context.resources.getString(constants.positiveBtn)
                } else {
                    positiveBtn.visibility = View.GONE
                    middleLineView.visibility = View.GONE
                }
                if (constants.negativeBtn > 0) {
                    negativeBtn.text = context.resources.getString(constants.negativeBtn)
                } else {
                    negativeBtn.visibility = View.GONE
                    middleLineView.visibility = View.GONE
                }
            }

            // 设置字体颜色
            positiveTextColor?.let {
                positiveBtn.setTextColor(it)
            }

            negativeTextColor?.let {
                negativeBtn.setTextColor(it)
            }

            if (constants.editText > 0) {
                val layoutParams = message.layoutParams as RelativeLayout.LayoutParams
                layoutParams.setMargins(0, 0, 0, 20)
            } else {
                editText.visibility = View.GONE
            }
            if (positiveListener != null) {
                positiveBtn.setOnClickListener {
                    positiveListener?.onClick(
                        dialog,
                        constants.positiveBtn
                    )
                }
            } else {
                positiveBtn.setOnClickListener { dialog.dismiss() }
            }

            if (onNegativeListener != null) {
                negativeBtn.setOnClickListener {
                    onNegativeListener?.onClick(
                        dialog,
                        constants.negativeBtn
                    )
                }
            } else {
                negativeBtn.setOnClickListener { dialog.dismiss() }
            }
            if (onDismissListener != null) {
                dialog.setOnDismissListener(onDismissListener)
            }
            return layout
        }
    }

    companion object {
        const val DIMMED_OPACITY = 0.4f
    }
}