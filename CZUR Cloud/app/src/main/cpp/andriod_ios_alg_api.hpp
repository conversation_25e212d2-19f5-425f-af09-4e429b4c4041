#pragma once
/*
 * @brief  此文件为CZUR笔记本对安卓和IOS的相应算法api
 * <AUTHOR>
 */
#include <string>
#include <vector>
#include <opencv2/opencv.hpp>

#include <android/log.h> 
#define TAG "CZURxx-jni" // 这个是自定义的LOG的标识
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG,TAG ,__VA_ARGS__) // 定义LOGD类型 
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO,TAG ,__VA_ARGS__) // 定义LOGI类型 
#define LOGW(...) __android_log_print(ANDROID_LOG_WARN,TAG ,__VA_ARGS__) // 定义LOGW类型 
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR,TAG ,__VA_ARGS__) // 定义LOGE类型 
#define LOGF(...) __android_log_print(ANDROID_LOG_FATAL,TAG ,__VA_ARGS__) // 定义LOGF类型 
namespace imflow
{
	class AlgEngine;
	/*
	* @brief  稳定器
	*
	*/
	class Stabler
	{
	public:
		Stabler();
		void push(std::string pageCode);
		//  获取就行，如果是"NULL"  说明没初始或不稳定
		std::string get();
		bool is_stable();
	private:
		const static int N = 5;
		int _data[N];
	};


	/* 
	 * @brief  有关扫描笔记本的算法相关类
	 * 
	 */
	class AlgEngine
	{
	public:
		Stabler stabler;
		// @param modelPath 模型文件路径，用于识别页码
		AlgEngine(std::string modelPath);
		//////////////////////////////////////

		// 预览实时检测关键点以及识别页码
		// @param srcGray 预览图像灰度图
		// @param keyPoints4 返回的关键点
		// @param pageCode 识别到的页码
		// @param pageLocRect  页码定位矩形框
		// @return -2： 未检测到页面  -1：检测到页面但没识别页码  0： 一切ok
		int on_preview(cv::Mat &srcGray, std::vector<cv::Point2f> &keyPoints4,
			           std::string &pageCode,cv::Rect &pageLocRect);
		/////////////////////////////////////////
		 
		// 根据关键点裁剪出页面
		// @param src 源图像,拍照的彩色图
		// @param pts 四个关键点
		// @param dst 裁剪出的页面图像
		// @return 0: 正常 -1：失败
		int cut_by_keypoints(cv::Mat &src, std::vector<cv::Point2f> &pts,cv::Mat &dst,float angleX=0,float angleY=0);
		//////////////////////////////////////////

		// 根据关键点裁剪出页面 并做色彩模式（底色净化）
		// @param srcPath 源图像路径
		// @param pts 四个关键点
		// @param dstPath 处理后存储路径
		// @return 0: 正常 -1：失败
		int cut_by_keypoints(const char *srcPath, std::vector<cv::Point2f> &pts,
			const char * dstPath, float angleX=0, float angleY=0);
		//////////////////////////////////////////


		// 根据关键点裁剪出页面 并做色彩模式（底色净化）
		// @param srcPath 源图像路径
		// @param dstPath 处理后存储路径
		// @return 0: 正常 -1：失败
		int cut_by_keypoints(const char *srcPath,
			const char * dstPath, float angleX = 0, float angleY = 0);
		//////////////////////////////////////////

	    // 颜色增强
		// @param src_dst  输入图像，也是输出图像
		int color_enhance(cv::Mat &src_dst);
		//////////////////////////////////////////

		// 压缩像素，压缩质量保存
		int save_file(cv::Mat &src, std::string filename, int sizeS = 60, int quality = 70);

	private:
		// 在定位后的roi区域分割出页码
		// @param roi 定位页码后的roi图像
		// @param pagecodeCutted  分割后的多个字符图像
		int cut_pagecode_in_roi(cv::Mat &roi, std::vector<cv::Mat> &charCutted,
			cv::Rect &pageLocRect);
		/////////////////////////////////////////
		cv::Ptr<cv::ml::SVM> _svm;
		


	};
	// ui 相关
	namespace ui {
		// 根据四个角点扩展成沿边的12个点
		// pts4Ext  会有四组  std::vector<cv::Point2f>  每组里包含三个点 
		// 第一个点是原始角点 其他两个是沿着边扩展的
		int   extend_corner(std::vector<cv::Point2f> &pts4,
			std::vector<std::vector<cv::Point2f> > &pts4Ext, float ratio=0.1);
	}
}// namespace imflow