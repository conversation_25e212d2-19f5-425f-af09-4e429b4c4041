#include <jni.h>
#include <string>
#include <stdio.h>
#include <stdlib.h>
#include "andriod_ios_alg_api.hpp"
#include <opencv2/opencv.hpp>
#include <string.h>
#include <android/asset_manager_jni.h>
#include <android/asset_manager.h>
#include <android/bitmap.h>

using namespace cv;

#import "point_line_utils.hpp"

extern "C"
{
//Bitmap转Mat
cv::Mat bitmap_to_Mat(JNIEnv *env, jobject bitmap);
//Mat转Bitmap
jobject mat_to_bitmap(JNIEnv *env, Mat &src, bool needPremultiplyAlpha, jobject bitmap_config);
//字符串转换
char *Jstring_to_CStr(JNIEnv *env, jstring jstr);
//存储缩略图
void makeSmallImage(cv::Mat image, std::string path);

// 初始化Jni 读取页码模型文件
// @param assetManager AssetManager去获取asset下文件
// @return 0: 正常 -1：失败
JNIEXPORT jlong

JNICALL Java_com_czur_cloud_jni_CloudNative_initNativeAndReadXml
        (JNIEnv *env, jobject obj, jobject assetManager) {
    LOGI("jnixx initNativeAndReadXml begin");

    jlong result;
    AAssetManager *mgr = AAssetManager_fromJava(env, assetManager);
    if (mgr == NULL) {
        LOGI(" %s", "AAssetManager==NULL");
    }
    /*获取文件名并打开*/
    jboolean iscopy;
    /*不能写“assets/text.txt”，路径已经在assets下了*/
    const char *mfile = "model.xml";
    AAsset *asset = AAssetManager_open(mgr, mfile, AASSET_MODE_UNKNOWN);
    if (asset == NULL) {
        LOGI(" %s", "asset==NULL");
    }
    /*获取文件大小*/
    off_t bufferSize = AAsset_getLength(asset);
    char *buffer = (char *) malloc(bufferSize + 1);
    buffer[bufferSize] = 0;
    int numBytesRead = AAsset_read(asset, buffer, bufferSize);
    LOGI(": %s", buffer);
    /*关闭文件*/
    AAsset_close(asset);


    /*把assert读出来模型文件的内容传入*/
    result = (jlong) new imflow::AlgEngine(buffer);
    free(buffer);
    return result;
}

/*
 * 获取4个角 12个点XY坐标
 */
JNIEXPORT jobject JNICALL Java_com_czur_cloud_jni_CloudNative_getBorderPoints
        (JNIEnv *env, jobject obj, jlong thiz, jbyteArray buf, jint w, jint h) {

    /*  对象指针调用方法  */
    jbyte *yuv_nv21 = env->GetByteArrayElements(buf, JNI_FALSE);
    if (yuv_nv21 == NULL) {
        return NULL;
    }
    cv::Size s(w, h);
    cv::Mat gray(s, CV_8UC1, yuv_nv21);

    std::vector<std::vector<cv::Point2f> > keyPoints12;
    std::vector<cv::Point2f> keyPoints4;
    std::string pageCode;
    bool isStable;
    cv::Rect rr;

    int result = ((imflow::AlgEngine *) thiz)->on_preview(gray, keyPoints4, pageCode, rr);
    ((imflow::AlgEngine *) thiz)->stabler.push(pageCode);
    pageCode = ((imflow::AlgEngine *) thiz)->stabler.get();
    isStable=((imflow::AlgEngine *) thiz)->stabler.is_stable();
    float arr_ui[24];

    /*  把页码的边框旋转90度  */
    std::vector<cv::Point2f> transVector;
    transVector.push_back(cv::Point2f(rr.x, rr.y + rr.height));
    transVector.push_back(cv::Point2f(rr.x + rr.width, rr.y));
    PointLineUtils::transpose_endpoints(transVector, h);
    cv::Rect pageRec = cv::Rect(transVector[0].x, transVector[0].y,
                                transVector[1].x - transVector[0].x,
                                transVector[1].y - transVector[0].y);

    /*   关于描述符com/czur/cloud/entity/JniEntity  */
    /*   获得JniEntity类引用 */
    jclass jnicls = env->FindClass("com/czur/cloud/entity/JniEntity");
    //获得得该类型的构造函数  函数名为 <init> 返回类型必须为 void 即 V
    jmethodID constrocMID = env->GetMethodID(jnicls, "<init>", "([FZIIIII)V");
    if (constrocMID == NULL) {
        LOGI("get methodId failed!");
    }
    if (result == 0) {

        PointLineUtils::transpose_endpoints(keyPoints4, h);
        imflow::ui::extend_corner(keyPoints4, keyPoints12);

        for (int i = 0; i < keyPoints12.size(); i++) {
            cv::Point2f p0 = keyPoints12[i][0];
            cv::Point2f p1 = keyPoints12[i][1];
            cv::Point2f p2 = keyPoints12[i][2];

            arr_ui[6 * i + 0] = p0.x;
            arr_ui[6 * i + 1] = p0.y;
            arr_ui[6 * i + 2] = p1.x;
            arr_ui[6 * i + 3] = p1.y;
            arr_ui[6 * i + 4] = p2.x;
            arr_ui[6 * i + 5] = p2.y;

        }
        jfloatArray drawPoints = env->NewFloatArray(24);
        env->SetFloatArrayRegion(drawPoints, 0, 24, arr_ui);
        jobject stu_ojb = env->NewObject(jnicls, constrocMID, drawPoints,isStable,
                                         atoi(pageCode.c_str()), pageRec.x, pageRec.y,
                                         pageRec.width,
                                         pageRec.height);  //构造一个对象，调用该类的构造函数，并且传递参数
        env->ReleaseByteArrayElements(buf, yuv_nv21, JNI_FALSE);
        return stu_ojb;
    } else {
        jfloatArray endPoints = env->NewFloatArray(24);
        float arr_ui[24];
        for(int i=0;i<24;i++)
            arr_ui[i]=-1;
        env->SetFloatArrayRegion(endPoints, 0, 24, arr_ui);
        jobject stu_ojb = env->NewObject(jnicls, constrocMID, endPoints,false,
                                         -1,0, 0,
                                         0,
                                         0);  //构造一个对象，调用该类的构造函数，并且传递参数
        env->ReleaseByteArrayElements(buf, yuv_nv21, JNI_FALSE);
        return stu_ojb;
    }


}
/*
 * 返回预览最后一帧的的Bitmap
 */
JNIEXPORT jobject JNICALL
Java_com_czur_cloud_jni_CloudNative_getPrintScreenImg(JNIEnv *env, jobject instance, jlong AlgAdd,
                                                      jbyteArray buf, jint w, jint h,jobject bitmap_config) {
    LOGI("jnixx print screen begin");

    /*  对象指针调用方法  */
    jbyte *yuv_nv21 = env->GetByteArrayElements(buf, JNI_FALSE);
    if (yuv_nv21 == NULL) {
        return NULL;
    }
    cv::Size s(w, h);
    cv::Mat yuv(s,CV_8UC3,yuv_nv21);
    cv::Mat bgr;
    cv::cvtColor(yuv,bgr,CV_YUV2RGB_NV21);

    jobject _bitmap = mat_to_bitmap(env, bgr, false, bitmap_config);
    env->ReleaseByteArrayElements(buf, yuv_nv21, 0);

    LOGI("jnixx print screen end");

    return _bitmap;

}


/*
 * 拍照获取4个角 4个点XY坐标
 * 并且返回裁剪后的Bitmap
 */
JNIEXPORT jobject JNICALL Java_com_czur_cloud_jni_CloudNative_getPointsAndCut
        (JNIEnv *env, jobject obj, jlong thiz, jbyteArray buf, jint w, jint h, jobject bitmap) {
    LOGI("jnixx getPointsAndCut begin");

    /*  对象指针调用方法  */
    jbyte *yuv_nv21 = env->GetByteArrayElements(buf, JNI_FALSE);
    if (yuv_nv21 == NULL) {
        LOGI("yuv_nv21 error");
    }
    cv::Mat rotateMat;
    cv::Mat mat = bitmap_to_Mat(env, bitmap);
    cv::Mat afterCutMat;
    std::vector<cv::Point2f> keyPoints4, keyPts4_2;
    std::string pageCode;
    cv::Rect rr;
    /*构造一个灰色图去让on preview方法识别裁剪的4个点*/
    cv::Mat gray;
    cv::cvtColor(mat, gray, CV_BGR2GRAY);
    /*获得裁剪的4个点是否成功*/
    int result = ((imflow::AlgEngine *) thiz)->on_preview(gray, keyPoints4, pageCode, rr);
    if (result != 0) {
        LOGI(" detect 4pts error");
        if (result == -2) {
            LOGI(" detect 4pts not found");
        }
        return NULL;
    } else {
        LOGI(" detect 4pts success");
    }
    /*将裁剪点转正*/
    keyPts4_2.push_back(cv::Point2f(keyPoints4[2]));
    keyPts4_2.push_back(cv::Point2f(keyPoints4[0]));
    keyPts4_2.push_back(cv::Point2f(keyPoints4[3]));
    keyPts4_2.push_back(cv::Point2f(keyPoints4[1]));
    double Time = (double) cvGetTickCount();
    /*裁剪*/
    ((imflow::AlgEngine *) thiz)->cut_by_keypoints(mat, keyPts4_2, afterCutMat, 0, 0);
    Time = (double) cvGetTickCount() - Time;
    LOGI("cut run time = %gms\n", Time / (cvGetTickFrequency() * 1000));//毫秒

    /*构造Bitmap返回Java层*/
    jclass java_bitmap_class = (jclass) env->FindClass("android/graphics/Bitmap");
    jmethodID mid = env->GetMethodID(java_bitmap_class, "getConfig",
                                     "()Landroid/graphics/Bitmap$Config;");
    jobject bitmap_config = env->CallObjectMethod(bitmap, mid);
    jobject _bitmap = mat_to_bitmap(env, afterCutMat, false, bitmap_config);

    AndroidBitmap_unlockPixels(env, bitmap);
    env->ReleaseByteArrayElements(buf, yuv_nv21, JNI_FALSE);
    LOGI("jnixx getPointsAndCut end");

    return _bitmap;

}

JNIEXPORT void JNICALL Java_com_czur_cloud_jni_CloudNative_doColorMode
        (JNIEnv *env, jobject obj, jlong thiz, jobject bitmap, jstring path, jstring smallPath) {
    LOGI("jnixx doColorMode begin");
    cv::Mat mat = bitmap_to_Mat(env, bitmap);
    cv::cvtColor(mat, mat, CV_BGR2RGB);
    double Time = (double) cvGetTickCount();

    const static int WW = 1800;
    const static int HH = 2400;
    if (mat.cols * mat.rows > WW * HH) {
        cv::resize(mat, mat, cv::Size(WW, HH));
    }

    int colorResult = ((imflow::AlgEngine *) thiz)->color_enhance(mat);
    Time = (double) cvGetTickCount() - Time;
    LOGI("color run time = %gms\n", Time / (cvGetTickFrequency() * 1000));//毫秒
    if (colorResult != 0) {
        LOGI("do color mode error");
    } else {
        LOGI("do color mode success");
    }
    char *colorPath = Jstring_to_CStr(env, path);
    int compressResult = ((imflow::AlgEngine *) thiz)->save_file(mat, colorPath, 60, 70);
    if (compressResult != 0) {
        LOGI("compress error");
    } else {
        LOGI("compress success");
    }
    char *sPath = Jstring_to_CStr(env, smallPath);
    makeSmallImage(mat, sPath);
    LOGI("jnixx doColorMode end");
}

cv::Mat bitmap_to_Mat(JNIEnv *env, jobject bitmap) {
    AndroidBitmapInfo inBmpInfo;
    void *inPixelsAddress;
    int ret;
    if ((ret = AndroidBitmap_getInfo(env, bitmap, &inBmpInfo)) < 0) {
        LOGD("AndroidBitmap_getInfo() failed ! error=%d", ret);
    }
    LOGI("original image :: width is %d; height is %d; stride is %d; format is %d;flags is   %d,stride is %u",
         inBmpInfo.width, inBmpInfo.height, inBmpInfo.stride, inBmpInfo.format, inBmpInfo.flags,
         inBmpInfo.stride);
    if ((ret = AndroidBitmap_lockPixels(env, bitmap, &inPixelsAddress)) < 0) {
        LOGE("AndroidBitmap_lockPixels() failed ! error=%d", ret);
    }
    Mat inMat(inBmpInfo.height, inBmpInfo.width,
              CV_8UC4, inPixelsAddress);
    AndroidBitmap_unlockPixels(env, bitmap);
    cv::cvtColor(inMat, inMat, CV_BGRA2BGR);

    return inMat;
}
char *Jstring_to_CStr(JNIEnv *env, jstring jstr) {
    char *rtn = NULL;
    jclass clsstring = env->FindClass("java/lang/String");
    jstring strencode = env->NewStringUTF("GB2312");
    jmethodID mid = env->GetMethodID(clsstring, "getBytes", "(Ljava/lang/String;)[B");
    jbyteArray barr = (jbyteArray) env->CallObjectMethod(jstr, mid, strencode);
    jsize alen = env->GetArrayLength(barr);
    jbyte *ba = env->GetByteArrayElements(barr, JNI_FALSE);
    if (alen > 0) {
        rtn = (char *) malloc(alen + 1); //new char[alen+1];
        memcpy(rtn, ba, alen);
        rtn[alen] = 0;
    }
    env->ReleaseByteArrayElements(barr, ba, JNI_FALSE);

    return rtn;
}


jobject mat_to_bitmap(JNIEnv *env, Mat &src, bool needPremultiplyAlpha, jobject bitmap_config) {

    jclass java_bitmap_class = (jclass) env->FindClass("android/graphics/Bitmap");
    jmethodID mid = env->GetStaticMethodID(java_bitmap_class,
                                           "createBitmap",
                                           "(IILandroid/graphics/Bitmap$Config;)Landroid/graphics/Bitmap;");

    jobject bitmap = env->CallStaticObjectMethod(java_bitmap_class,
                                                 mid, src.size().width, src.size().height,
                                                 bitmap_config);
    AndroidBitmapInfo info;
    void *pixels = 0;


    try {
        //validate
        CV_Assert(AndroidBitmap_getInfo(env, bitmap, &info) >= 0);
        CV_Assert(src.type() == CV_8UC1 || src.type() == CV_8UC3 || src.type() == CV_8UC4);
        CV_Assert(AndroidBitmap_lockPixels(env, bitmap, &pixels) >= 0);
        CV_Assert(pixels);

        //type mat
        if (info.format == ANDROID_BITMAP_FORMAT_RGBA_8888) {
            Mat tmp(info.height, info.width, CV_8UC4, pixels);
            if (src.type() == CV_8UC1) {
                cvtColor(src, tmp, CV_GRAY2RGBA);
            } else if (src.type() == CV_8UC3) {
                cvtColor(src, tmp, CV_RGB2RGBA);
            } else if (src.type() == CV_8UC4) {
                if (needPremultiplyAlpha) {
                    cvtColor(src, tmp, COLOR_RGBA2mRGBA);
                } else {
                    src.copyTo(tmp);
                }
            }

        } else {
            Mat tmp(info.height, info.width, CV_8UC2, pixels);
            if (src.type() == CV_8UC1) {
                cvtColor(src, tmp, CV_GRAY2BGR565);
            } else if (src.type() == CV_8UC3) {
                cvtColor(src, tmp, CV_RGB2BGR565);
            } else if (src.type() == CV_8UC4) {
                cvtColor(src, tmp, CV_RGBA2BGR565);
            }
        }
        AndroidBitmap_unlockPixels(env, bitmap);
        return bitmap;
    } catch (cv::Exception e) {
        AndroidBitmap_unlockPixels(env, bitmap);
        jclass je = env->FindClass("org/opencv/core/CvException");
        if (!je) je = env->FindClass("java/lang/Exception");
        env->ThrowNew(je, e.what());
        return bitmap;
    } catch (...) {
        AndroidBitmap_unlockPixels(env, bitmap);
        jclass je = env->FindClass("java/lang/Exception");
        env->ThrowNew(je, "Unknown exception in JNI code {nMatToBitmap}");
        return bitmap;
    }
}

void makeSmallImage(cv::Mat image, std::string path) {
    LOGI("jnixx small Image begin");

    cv::Mat smallMat;
    bool isH = image.cols >= image.rows;
    cv::Size size;
    if (isH) {
        size = cv::Size(300, (int) (300.0f / (((float) image.cols) / ((float) image.rows))));
    } else {
        size = cv::Size((int) (((float) image.cols) / ((float) image.rows) * 300.0f), 300);
    }
    cv::resize(image, smallMat, size);
    cv::imwrite(path.c_str(), smallMat);
    LOGI("jnixx small Image end");

}

}

