#include "alg_kernel.hpp"
#include "point_line_utils.hpp"
#include <omp.h>
#define WITH_OMP 1
namespace imflow
{

	// 获得最近的直线的距离， 自身除外
	float get_nearest_line_dis(std::vector<cv::Vec4f>& srcLines, cv::Vec4f &line, int *inxp = NULL)
	{
		float dis = FLT_MAX;
		int inx = 0;
		for (unsigned int i = 0; i < srcLines.size(); i++)
		{

			float d = PointLineUtils::dis_between_lines(srcLines[i], line);
			if (d != 0 && d < dis)
			{
				dis = d;
				inx = i;
			}
		}
		*inxp = inx;
		return dis;
	}
	// 获得滤出pair线段
	// srcLines  所有直线
	// dstLines  滤出的pair线
	// disThresh1  pair线的最大距离阈值
	// disThresh2  pair线中点的最大距离阈值
	void get_pair_lines(std::vector<cv::Vec4f>& srcLines,
		float disThresh1, float disThresh2,
		std::vector<cv::Vec4f>& dstLines
	)
	{
		for (unsigned int i = 0; i < srcLines.size(); i++)
		{
			int inx = 0;
			float dis1 = get_nearest_line_dis(srcLines, srcLines[i], &inx);

			cv::Point2f p1((srcLines[i][0] + srcLines[i][2]) / 2, (srcLines[i][1] + srcLines[i][3]) / 2);
			cv::Point2f p2((srcLines[inx][0] + srcLines[inx][2]) / 2, (srcLines[inx][1] + srcLines[inx][3]) / 2);
			float dis2 = PointLineUtils::dis_between_points(p1, p2);
			if (dis1 < disThresh1 && // 直线间距离判断
				dis2 < disThresh2)// 最近直线间的中点距离判断
			{
				dstLines.push_back(srcLines[i]);
			}
		}
	}

	float shape_diff(cv::Vec4f & upL, cv::Vec4f& downL, cv::Vec4f & leftL, cv::Vec4f & rightL)
	{
		float angle1 = PointLineUtils::angle_between_lines(upL, leftL);// 左上
		float angle2 = PointLineUtils::angle_between_lines(upL, rightL);// 右上
		float angle3 = PointLineUtils::angle_between_lines(downL, leftL);// 左下
		float angle4 = PointLineUtils::angle_between_lines(downL, rightL);// 右下
																		  // 菱形差
		float diff1 = abs(angle1 - angle4) + abs(angle2 - angle3);
		// 梯形差
		float diff2 = abs(angle1 - angle2) + abs(angle3 - angle4);

		return std::min(diff1, diff2);
	}
	bool is_possible_card(cv::Vec4f & upL, cv::Vec4f& downL, cv::Vec4f & leftL, cv::Vec4f & rightL,
		int imW, int imH, int boundThres = 120, float angleThres1 = 65, float angleThres2 = 18)
	{

		// 交点超过图形区域，则可能性为0
		cv::Rect r(0, 0, imW, imH);
		cv::Point2f p1 = PointLineUtils::line_intersection(upL, leftL);
		cv::Point2f p2 = PointLineUtils::line_intersection(upL, rightL);
		cv::Point2f p3 = PointLineUtils::line_intersection(downL, leftL);
		cv::Point2f p4 = PointLineUtils::line_intersection(downL, rightL);
		if (PointLineUtils::is_point_over_rect(p1, r))
			return false;
		if (PointLineUtils::is_point_over_rect(p2, r))
			return false;
		if (PointLineUtils::is_point_over_rect(p3, r))
			return false;
		if (PointLineUtils::is_point_over_rect(p4, r))
			return false;
#if RECT_CUT_IN_DEBUG
		std::cout << "No cross point over rect!" << endl;
#endif
		//进一步检查交点
		if (p1.x > std::min(upL[0], upL[2]) + boundThres ||
			p2.x < std::max(upL[0], upL[2]) - boundThres ||
			p3.x > std::min(downL[0], downL[2]) + boundThres ||
			p4.x < std::max(downL[0], downL[2]) - boundThres)
			return false;

		if (p1.y > std::min(leftL[1], leftL[3]) + boundThres ||
			p2.y > std::min(rightL[1], rightL[3]) + boundThres ||
			p3.y < std::max(leftL[1], leftL[3]) - boundThres ||
			p4.y < std::max(rightL[1], rightL[3]) - boundThres)
			return false;
#if RECT_CUT_IN_DEBUG
		std::cout << "No cross point over boundary!" << endl;
#endif
		float angle1 = PointLineUtils::angle_between_lines(upL, leftL);// 左上
		float angle2 = PointLineUtils::angle_between_lines(upL, rightL);// 右上
		float angle3 = PointLineUtils::angle_between_lines(downL, leftL);// 左下
		float angle4 = PointLineUtils::angle_between_lines(downL, rightL);// 右下

																		  // 交角检查 
																		  // 交角不能过小
		if (angle1 <angleThres1 || angle2 < angleThres1 || angle3 <angleThres1 || angle4 < angleThres1)
			return false;
#if RECT_CUT_IN_DEBUG
		std::cout << "No angle less than angleThres1!" << endl;
#endif
		// 要么是接近菱形 要么接近梯形
		if (!((abs(angle1 - angle2) < angleThres2 && abs(angle3 - angle4) < angleThres2) ||
			(abs(angle1 - angle4) < angleThres2 && abs(angle2 - angle3) < angleThres2) ||
			(abs(angle1 - angle3) < angleThres2 && abs(angle2 - angle4)< angleThres2)))
		{
#if RECT_CUT_IN_DEBUG
			std::cout << "Not similar to rect !" << endl;
#endif	
			return false;
		}

		return true;
	}

	float final_simi_score(cv::Vec4f & upL, cv::Vec4f& downL, cv::Vec4f & leftL, cv::Vec4f & rightL)
	{
		cv::Point2f p1 = PointLineUtils::line_intersection(upL, leftL);//左上
		cv::Point2f p2 = PointLineUtils::line_intersection(upL, rightL);//右上
		cv::Point2f p3 = PointLineUtils::line_intersection(downL, leftL);//左下
		cv::Point2f p4 = PointLineUtils::line_intersection(downL, rightL);//右下

		float dis1, dis2, dis3, dis4;
		dis1 = PointLineUtils::dis_between_points(p1, p4);
		dis2 = PointLineUtils::dis_between_points(p2, p3);
		cv::Vec4f line1, line2;
		line1[0] = p1.x; line1[1] = p1.y;
		line1[2] = p4.x; line1[3] = p4.y;
		line2[0] = p2.x; line2[1] = p2.y;
		line2[2] = p3.x; line2[3] = p3.y;

		float angleTmp = PointLineUtils::angle_between_lines(line1, line2);
		//面积 越大越好
		float scoreS = dis1*dis2*sin(angleTmp*CV_PI / 180);
		// 封闭性 越小越好
		cv::Point2f p_1, p_2;
		p_1.x = std::min(upL[0], upL[2]);
		p_1.y = p_1.x == upL[0] ? upL[1] : upL[3];
		p_2.y = std::min(leftL[1], leftL[3]);
		p_2.x = p_2.y == leftL[1] ? leftL[0] : leftL[2];
		dis1 = PointLineUtils::dis_between_points(p_1, p_2);

		p_1.x = std::max(upL[0], upL[2]);
		p_1.y = p_1.x == upL[0] ? upL[1] : upL[3];
		p_2.y = std::min(rightL[1], rightL[3]);
		p_2.x = p_2.y == rightL[1] ? rightL[0] : rightL[2];
		dis2 = PointLineUtils::dis_between_points(p_1, p_2);

		p_1.x = std::min(downL[0], downL[2]);
		p_1.y = p_1.x == downL[0] ? downL[1] : downL[3];
		p_2.y = std::max(leftL[1], leftL[3]);
		p_2.x = p_2.y == leftL[1] ? leftL[0] : leftL[2];
		dis3 = PointLineUtils::dis_between_points(p_1, p_2);

		p_1.x = std::max(downL[0], downL[2]);
		p_1.y = p_1.x == downL[0] ? downL[1] : downL[3];
		p_2.y = std::max(rightL[1], rightL[3]);
		p_2.x = p_2.y == rightL[1] ? rightL[0] : rightL[2];
		dis4 = PointLineUtils::dis_between_points(p_1, p_2);

		float  closure = dis1 + dis2 + dis3 + dis4;
		float  shapeDiff = shape_diff(upL, downL, leftL, rightL);
		if (scoreS / (600 * 800)< 0.5)
			return -10000000;
		return  scoreS*0.0001 - 10 * closure - 10 * shapeDiff;
	}

	std::vector<cv::Vec4f> select_lines(std::vector<cv::Vec4f> &ups, std::vector<cv::Vec4f> &downs,
		std::vector<cv::Vec4f> &lefts, std::vector<cv::Vec4f> &rights, int imW, int imH)
	{
		const int searchN = 5;
		std::vector< std::vector<int> > pairs;
		for (int i = 0; i < searchN && i < ups.size(); i++)
		{
			for (int j = 0; j < searchN && j < downs.size(); j++)
			{
				for (int k = 0; k < searchN && k < lefts.size(); k++)
				{
					for (int m = 0; m <searchN && m < rights.size(); m++)
					{
						std::vector<int> pair;
						 //= { i, j, k, m };
						 pair.push_back(i);
						  pair.push_back(j);
						   pair.push_back(k);
						    pair.push_back(m);
						bool isPos = is_possible_card(ups[i], downs[j], lefts[k], rights[m], imW, imH);

						//std::cout << "isPos: " << isPos << endl;
						//PointLineUtils::draw_lines(showG, ups[i], downs[j], lefts[k], rights[m]);
						if (isPos)
						{

							pairs.push_back(pair);
						}
					}
				}
			}
		}

		int dstInx = 0;
		float maxScore = -FLT_MAX;
		int num = 0;
		for (int i = 0; i < pairs.size(); i++)
		{
			cv::Vec4f upL = ups[pairs[i][0]];
			cv::Vec4f downL = downs[pairs[i][1]];
			cv::Vec4f leftL = lefts[pairs[i][2]];
			cv::Vec4f rightL = rights[pairs[i][3]];
			float score = final_simi_score(upL, downL, leftL, rightL);
			if (maxScore < score)
			{
				maxScore = score;
				dstInx = i;
			}
			num++;
		}
		std::vector<cv::Vec4f> linesDst;
		if (num != 0)
		{
			linesDst.push_back(ups[pairs[dstInx][0]]); //上
			linesDst.push_back(downs[pairs[dstInx][1]]); // 下
			linesDst.push_back(lefts[pairs[dstInx][2]]); // 左
			linesDst.push_back(rights[pairs[dstInx][3]]); // 右
		}

		return linesDst;
	}

	int local_otsu_binarize(cv::Mat &srcGrey, cv::Mat &dst, int widthBlocks, int heightBlocks)
	{
		dst = srcGrey.clone();
		cv::Mat roi;
		cv::Mat roiDst;
		int widthStep = srcGrey.cols / widthBlocks;
		int heightStep = srcGrey.rows / heightBlocks;
		int residueW = srcGrey.cols%widthBlocks;
		int residueH = srcGrey.rows%heightBlocks;

		int avgT = 0;
		for (int i = 0; i<heightBlocks; i++)
		{
			int tmp = (i == heightBlocks - 1 ? 1 : 0)*residueH + heightStep;

			for (int j = 0; j<widthBlocks; j++)
			{
				cv::Rect r = cv::Rect(j*widthStep, i*heightStep,
					(j == widthBlocks - 1 ? 1 : 0)*residueW + widthStep, tmp);
				roi = srcGrey(r);
				roiDst = dst(r);
				int tmp1 = threshold(roi, roiDst, 0, 255, CV_THRESH_OTSU | CV_THRESH_BINARY);
				avgT += tmp1;
			}
		}
		avgT = avgT / (widthBlocks*heightBlocks);

		return avgT;
	}

	/*
	*  @brief:  draw rotate rect
	*  @src:  source image
	*  @rRect:  rotate rect
	*/
	cv::Mat draw_rotate_rect(cv::Mat &src, cv::RotatedRect &rRect)
	{
		cv::Mat draw = src.clone();
		cv::Point2f vertices[4];
		rRect.points(vertices);
		for (int i = 0; i < 4; i++)
			cv::line(draw, vertices[i], vertices[(i + 1) % 4], cv::Scalar(0, 0, 255), 3, 8);
		return draw;
	}
	/*********************************************************/


	void im_show(const char *win, cv::Mat &src, bool wait)
	{
		cv::namedWindow(win, 0);
		cv::imshow(win, src);
		if (wait)
			cv::waitKey(0);
	}


	/*
	* @brief 获取四个关键点和页码的定位区域
	* @param [in] srcGray 输入源灰度图像
	* @param [out] keyPoints4 四个关键点
	* @param [out] 页码定位区域（旋转矩形）
	* @return  -1 没有检测到关键点 0 检测正常
	*/
	int get_key_pts_and_page_code_roi(cv::Mat &srcGray,
		std::vector<cv::Point2f> &keyPoints4,
		cv::RotatedRect &pageCodeRoi)
	{
		try
		{
			float UNI_LSD_IMG_W = 850;
			float UNI_LSD_IMG_H = 640;
			cv::Mat graySmall;
			cv::resize(srcGray, graySmall, cv::Size(UNI_LSD_IMG_W,UNI_LSD_IMG_H), cv::INTER_NEAREST);
			std::vector<cv::Vec4f> linesRaw, linesDst0, linesDst1, linesDst2;
			cv::Ptr<cv::LineSegmentDetector> lsdDetectorPtr_ =
				cv::createLineSegmentDetector(cv::LSD_REFINE_NONE, 0.8, 0.6, 0.7, 24.5, 0, 0.7);

			lsdDetectorPtr_->detect(graySmall, linesRaw);

			for (size_t i = 0; i < linesRaw.size(); ++i)
			{
				cv::Vec4f& line = linesRaw[i];
				int detaX = abs(line[0] - line[2]);
				int detaY = abs(line[1] - line[3]);
				if (detaX + detaY > 100)
				{
					linesDst0.push_back(line);
				}
			}
			//PointLineUtils::draw_lines(graySmall, linesDst0, linesDst0);
			get_pair_lines(linesDst0, 8, 280, linesDst2);
			//PointLineUtils::merge_lines(linesDst1, linesDst2);
			//PointLineUtils::draw_lines(graySmall, linesDst2, linesDst2);
			std::vector<cv::Vec4f> ups, downs, lefts, rights;
			PointLineUtils::split_lines_by_position(graySmall.cols, graySmall.rows, linesDst2, true, ups, downs, lefts, rights);
			std::vector<cv::Vec4f> keyLines;
			keyLines = select_lines(ups, downs, lefts, rights, graySmall.cols, graySmall.rows);
			

			if (keyLines.size() == 4)
			{
				// 偏差修正
				float biasReviseW = 0.5*(float)srcGray.cols / UNI_LSD_IMG_W;
				float biasReviseH = 0.5*(float)srcGray.rows / UNI_LSD_IMG_H;

				cv::Point2f p = PointLineUtils::line_intersection(keyLines[0], keyLines[2]);
				p.x *= (float)srcGray.cols / UNI_LSD_IMG_W;
				p.x += biasReviseW;
				p.y *= (float)srcGray.rows / UNI_LSD_IMG_H;
				p.y += biasReviseH;
				keyPoints4.push_back(p);
				p = PointLineUtils::line_intersection(keyLines[0], keyLines[3]);
				p.x *= (float)srcGray.cols / UNI_LSD_IMG_W;
				p.x += biasReviseW;
				p.y *= (float)srcGray.rows / UNI_LSD_IMG_H;
				p.y += biasReviseH;
				keyPoints4.push_back(p);
				p = PointLineUtils::line_intersection(keyLines[1], keyLines[2]);
				p.x *= (float)srcGray.cols / UNI_LSD_IMG_W;
				p.x += biasReviseW;
				p.y *= (float)srcGray.rows / UNI_LSD_IMG_H;
				p.y += biasReviseH;
				keyPoints4.push_back(p);
				p = PointLineUtils::line_intersection(keyLines[1], keyLines[3]);
				p.x *= (float)srcGray.cols / UNI_LSD_IMG_W;
				p.x += biasReviseW;
				p.y *= (float)srcGray.rows / UNI_LSD_IMG_H;
				p.y += biasReviseH;
				keyPoints4.push_back(p);

				/*cv::Vec4f leftL(keyPoints4[0].x, keyPoints4[0].y, keyPoints4[2].x, keyPoints4[2].y);
				cv::Vec4f rightL(keyPoints4[1].x, keyPoints4[1].y, keyPoints4[3].x, keyPoints4[3].y);
				float ratio = PointLineUtils::line_length(rightL) / PointLineUtils::line_length(leftL);
				
				cv::Vec4f downL(keyPoints4[2].x, keyPoints4[2].y, keyPoints4[3].x, keyPoints4[3].y);
				float lineLen = PointLineUtils::line_length(downL);
				float bias = 300*(ratio - 1);
				cv::Point2f center((keyPoints4[2].x+ keyPoints4[3].x) / 2-bias,
					(keyPoints4[2].y+keyPoints4[3].y) / 2+ lineLen /24);
				float angle = 0;*/

			
				

				cv::Vec4f upL(keyPoints4[0].x, keyPoints4[0].y, keyPoints4[1].x, keyPoints4[1].y);
				cv::Vec4f downL(keyPoints4[2].x, keyPoints4[2].y, keyPoints4[3].x, keyPoints4[3].y);
				cv::Vec4f rightL(keyPoints4[1].x, keyPoints4[1].y, keyPoints4[3].x, keyPoints4[3].y);
				float ratio = PointLineUtils::line_length(downL) / PointLineUtils::line_length(upL);

				
				float lineLen = PointLineUtils::line_length(rightL);
				float bias = 200 * (ratio - 1);
				cv::Point2f center((keyPoints4[1].x + keyPoints4[3].x) / 2 + lineLen / 30,
					(keyPoints4[1].y + keyPoints4[3].y) / 2 - bias);
				float angle = 0; 
				pageCodeRoi = cv::RotatedRect(center,cv::Size(lineLen /14, lineLen / 7),angle);
				/*cv::Mat show = draw_rotate_rect(srcGray, pageCodeRoi);
				cv::namedWindow("s", 0);
				cv::imshow("s", show);
				cv::waitKey(0);
				cv::Mat show = draw_rotate_rect(srcGray, pageCodeRoi);
				im_show("codeRoi", show,true);*/
			}
			else
			{
				std::cout << ups.size() << std::endl;
				std::cout << downs.size() << std::endl;
				std::cout << lefts.size() << std::endl;
				std::cout << rights.size() << std::endl;

				return -1;
			}
			return 0;
		}
		catch (...)
		{
			return -1;
		}
	}
	

	const static int N_BINS = 8;
	const static int N_DIVS = 3;
	const static int VL_HOG_DIMS = N_DIVS*N_DIVS*N_BINS;
	const static int BIN_RANGE = (2 * CV_PI) / N_BINS;
	const static int R_W = 16;
	const static int R_H = 16;

	cv::Mat hog_fea(cv::Mat &srcBGR)
	{
		using namespace cv;
		Mat srcGrey;
		if (srcBGR.channels() == 3)
			cvtColor(srcBGR, srcGrey, CV_BGR2GRAY);
		else
			srcGrey = srcBGR;
		//cv::resize(srcGrey, srcGrey, cv::Size(240,240));
		Mat Hog;
		Hog = Mat::zeros(1, VL_HOG_DIMS, CV_32FC1);

		Mat Ix, Iy;

		//Find orientation gradients in x and y directions
		Sobel(srcGrey, Ix, CV_16S, 1, 0, 3);
		Sobel(srcGrey, Iy, CV_16S, 0, 1, 3);

		int cellx = srcGrey.cols / N_DIVS;
		int celly = srcGrey.rows / N_DIVS;

		int srcGrey_area = srcGrey.rows * srcGrey.cols;

		for (int m = 0; m < N_DIVS; m++)
		{
			for (int n = 0; n < N_DIVS; n++)
			{
				for (int i = 0; i < cellx; i++)
				{
					for (int j = 0; j < celly; j++)
					{

						float px, py, grad, norm_grad, angle, nth_bin;

						//px = Ix.at(m*cellx+i, n*celly+j);
						//py = Iy.at(m*cellx+i, n*celly+j);
						px = static_cast<float>(Ix.at<int16_t>((m*cellx) + i, (n*celly) + j));
						py = static_cast<float>(Iy.at<int16_t>((m*cellx) + i, (n*celly) + j));

						grad = static_cast<float>(std::sqrt(1.0*px*px + py*py));
						norm_grad = grad / srcGrey_area;

						//Orientation
						angle = std::atan2(py, px);

						//convert to 0 to 360 (0 to 2*pi)
						if (angle < 0)
							angle += 2 * CV_PI;

						//find appropriate bin for angle
						nth_bin = angle / BIN_RANGE;

						//add magnitude of the edges in the hog matrix
						Hog.at<float>(0, (m*N_DIVS + n)*N_BINS + static_cast<int>(angle)) += norm_grad;

					}
				}
			}

		}

		//Normalization
		for (int i = 0; i < N_DIVS*N_DIVS; i++)
		{
			float max = 0;
			int j;
			for (j = 0; j < N_BINS; j++)
			{
				if (Hog.at<float>(0, i*N_BINS + j) > max)
					max = Hog.at<float>(0, i*N_BINS + j);
			}
			for (j = 0; j < N_BINS; j++)
				Hog.at<float>(0, i*N_BINS + j) /= max;
		}
		//cv::normalize(Hog,Hog,0,1,cv::NORM_MINMAX);
		return Hog;
	}

	cv::Mat get_row_fea(cv::Mat &srcBGR)
	{
		cv::Mat reM;
		cv::resize(srcBGR, reM, cv::Size(R_W, R_H));
		cv::Mat hogF = hog_fea(reM);
		return hogF;
	}

	int recog_single_number(cv::Mat &srcGray, cv::Ptr<cv::ml::SVM>& svm)
	{
		cv::Mat fea = get_row_fea(srcGray);
		return  svm->predict(fea);
	}

#if _WIN32
	void train(std::vector<std::vector<std::string> > &filelist, const char *modelDstPath)
	{
		int samples = 0;
		for (auto i : filelist)
		{
			samples += i.size();
		}
		cv::Mat dataMat(samples, VL_HOG_DIMS, CV_32FC1);
		cv::Mat labelMat(samples, 1, CV_32SC1);
		std::cout << "Prepare data..\n";
		int n = 0;
		for (unsigned int i = 0; i<filelist.size(); i++)
		{
			for (unsigned int j = 0; j < filelist[i].size(); j++)
			{
				cv::Mat src = cv::imread(filelist[i][j]);
				assert(!src.data);
				cv::transpose(src, src);
				cv::flip(src, src, 0);

				cv::Mat fea = get_row_fea(src);
				assert(fea.cols != VL_HOG_DIMS);
				assert(fea.type() != dataMat.type());
				for (int kk = 0; kk < fea.cols; kk++)
					dataMat.at<float>(n, kk) = fea.at<float>(0, kk);
				labelMat.at<int>(n, 0) = i;
				n++;
			}

		}
		std::cout << "Prepare data down! Total sample:" <<n<<"\n";
		cv::Ptr<cv::ml::SVM> svm = cv::ml::SVM::create();
		svm->setType(cv::ml::SVM::C_SVC);
		svm->setKernel(cv::ml::SVM::LINEAR);
		svm->setTermCriteria(cv::TermCriteria(cv::TermCriteria::MAX_ITER, 100, 1e-6));
		std::cout << "Training....\n";
		svm->train(dataMat, cv::ml::ROW_SAMPLE, labelMat);
		std::cout << "Training  down!\n";
		svm->save(modelDstPath);
		int rightN = 0, totalN = 0;
		for (unsigned int i = 0; i<filelist.size(); i++)
		{
			for (unsigned int j = 0; j < filelist[i].size(); j++)
			{
				cv::Mat src = cv::imread(filelist[i][j]);
				assert(!src.data);
				cv::transpose(src, src);
				cv::flip(src, src, 0);
				cv::Mat fea = get_row_fea(src);
				assert(fea.cols != VL_HOG_DIMS);
				assert(fea.type() != CV_32FC1);
				int predict = svm->predict(fea);
				if (predict == i)
					rightN++;
				else
					std::cout << filelist[i][j] << std::endl;

				totalN++;
			}

		}
		std::cout << "Train Accuracy: " << rightN / (float)totalN << std::endl;
		
	}
	int test(std::vector<std::vector<std::string> > &filelist, const char *modelPath)
	{
		cv::Ptr<cv::ml::SVM> svm = cv::ml::SVM::create();

		svm = cv::ml::SVM::load(modelPath);

		int rightN = 0, totalN = 0;
		for (unsigned int i = 0; i<filelist.size(); i++)
		{
			for (unsigned int j = 0; j < filelist[i].size(); j++)
			{
				cv::Mat src = cv::imread(filelist[i][j]);
				assert(!src.data);
				cv::transpose(src, src);
				cv::flip(src, src, 0);
				cv::Mat fea = get_row_fea(src);
				assert(fea.cols != FEA_DIMS);
				assert(fea.type() != CV_32F);
				int predict = svm->predict(fea);
				if (predict == i)
					rightN++;
				else
					std::cout << filelist[i][j] << std::endl;

				totalN++;
			}

		}
		std::cout << "Total: "<<totalN<<" Test Accuracy: " << rightN / (float)totalN << std::endl;
		return 0;
	}
#endif
	template<typename Dtype>
	Dtype max_3(Dtype x1, Dtype x2, Dtype x3)
	{
		Dtype m;
		m = x1 > x2 ? x1 : x2;
		return m > x3 ? m : x3;
	}
	template<typename Dtype>
	Dtype min_3(Dtype x1, Dtype x2, Dtype x3)
	{
		Dtype m;
		m = x1 < x2 ? x1 : x2;
		return m < x3 ? m : x3;
	}
	void czur_book_ace_pro(cv::Mat &src, float C)
	{
		int winSizeW = src.cols / 10;
		int winSizeH = src.rows / 10;
		int nW = src.cols / winSizeW+1;
		int nH = src.rows / winSizeH+1;
#if WITH_OMP
#pragma omp parallel for num_threads(omp_get_max_threads())
#endif
		for (int i = 0; i <= nH; ++i)
		{
			for (int j = 0; j <= nW; ++j)
			{
				int r0 = i*winSizeH;
				int c0 = j*winSizeW;
                if (r0>src.rows-1)
                    continue;
                if(c0>src.cols-1)
                    continue;
				int r1 = r0 + winSizeH < src.rows ? r0 + winSizeH : src.rows;
				int c1 = c0 + winSizeW < src.cols ? c0 + winSizeW : src.cols;
				cv::Mat roi = src(cv::Rect(c0, r0, c1 - c0, r1 - r0));
				cv::Scalar m = cv::mean(roi);
				float avgGray = 0.30*m[2] + 0.59*m[1] + 0.11*m[0];
				for (int r = r0; r < r1; ++r)
				{
					for (int c = c0; c < c1; ++c)
					{
						float maxBGR = max_3(src.at<uchar>(r, 3 * c + 0),
							src.at<uchar>(r, 3 * c + 1),
							src.at<uchar>(r, 3 * c + 2));
						float minBGR = min_3(src.at<uchar>(r, 3 * c + 0),
							src.at<uchar>(r, 3 * c + 1),
							src.at<uchar>(r, 3 * c + 2));

						float s = maxBGR > 0 ? (1 - minBGR / maxBGR) : 0;
						float v = maxBGR / 255;

						double brightR = 245.0 / avgGray;
						if (s > 0.40*v&&v > 0.5)
							brightR = 1.0;

						float bb = (m[0] + (src.at<uchar>(r, 3 * c + 0) - m[0])*C)*brightR;
						float gg = (m[1] + (src.at<uchar>(r, 3 * c + 1) - m[1])*C)*brightR;
						float rr = (m[2] + (src.at<uchar>(r, 3 * c + 2) - m[2])*C)*brightR;
						bb = bb > 255 ? 255 : bb;
						bb = bb < 0 ? 0 : bb;
						gg = gg > 255 ? 255 : gg;
						gg = gg < 0 ? 0 : gg;
						rr = rr > 255 ? 255 : rr;
						rr = rr < 0 ? 0 : rr;
						src.at<uchar>(r, 3 * c + 0) = bb;
						src.at<uchar>(r, 3 * c + 1) = gg;
						src.at<uchar>(r, 3 * c + 2) = rr;

						maxBGR = max_3(bb, gg, rr);
						minBGR = min_3(bb, gg, rr);
						s = maxBGR > 0 ? (1 - minBGR / maxBGR) : 0;
						v = maxBGR / 255;
						if (s < 0.3*v&&v>0.8)
						{
							src.at<uchar>(r, 3 * c + 0) = 255;
							src.at<uchar>(r, 3 * c + 1) = 255;
							src.at<uchar>(r, 3 * c + 2) = 255;
						}

					}
				}
			}
		}
	}
	
}// namespace imflow